{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/jssm.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSSM\", \"fileTypes\": [\"jssm\", \"jssm_state\"], \"name\": \"jssm\", \"patterns\": [{ \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.mn\" } }, \"comment\": \"block comment\", \"end\": \"\\\\*/\", \"name\": \"comment.block.jssm\" }, { \"begin\": \"//\", \"comment\": \"block comment\", \"end\": \"$\", \"name\": \"comment.line.jssm\" }, { \"begin\": \"\\\\${\", \"captures\": { \"0\": { \"name\": \"entity.name.function\" } }, \"comment\": \"js outcalls\", \"end\": \"}\", \"name\": \"keyword.other\" }, { \"comment\": \"semver\", \"match\": \"(\\\\d*)(\\\\.)(\\\\d*)(\\\\.)(\\\\d*)\", \"name\": \"constant.numeric\" }, { \"comment\": \"jssm language tokens\", \"match\": \"graph_layout(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"machine_name(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"machine_version(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"jssm_version(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"transitions\", \"match\": \"<->\", \"name\": \"keyword.control.transition.jssmArrow.legal_legal\" }, { \"comment\": \"transitions\", \"match\": \"<-\", \"name\": \"keyword.control.transition.jssmArrow.legal_none\" }, { \"comment\": \"transitions\", \"match\": \"->\", \"name\": \"keyword.control.transition.jssmArrow.none_legal\" }, { \"comment\": \"transitions\", \"match\": \"<=>\", \"name\": \"keyword.control.transition.jssmArrow.main_main\" }, { \"comment\": \"transitions\", \"match\": \"=>\", \"name\": \"keyword.control.transition.jssmArrow.none_main\" }, { \"comment\": \"transitions\", \"match\": \"<=\", \"name\": \"keyword.control.transition.jssmArrow.main_none\" }, { \"comment\": \"transitions\", \"match\": \"<~>\", \"name\": \"keyword.control.transition.jssmArrow.forced_forced\" }, { \"comment\": \"transitions\", \"match\": \"~>\", \"name\": \"keyword.control.transition.jssmArrow.none_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~\", \"name\": \"keyword.control.transition.jssmArrow.forced_none\" }, { \"comment\": \"transitions\", \"match\": \"<-=>\", \"name\": \"keyword.control.transition.jssmArrow.legal_main\" }, { \"comment\": \"transitions\", \"match\": \"<=->\", \"name\": \"keyword.control.transition.jssmArrow.main_legal\" }, { \"comment\": \"transitions\", \"match\": \"<-~>\", \"name\": \"keyword.control.transition.jssmArrow.legal_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~->\", \"name\": \"keyword.control.transition.jssmArrow.forced_legal\" }, { \"comment\": \"transitions\", \"match\": \"<=~>\", \"name\": \"keyword.control.transition.jssmArrow.main_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~=>\", \"name\": \"keyword.control.transition.jssmArrow.forced_main\" }, { \"comment\": \"edge probability annotation\", \"match\": \"(\\\\d+)%\", \"name\": \"constant.numeric.jssmProbability\" }, { \"comment\": \"action annotation\", \"match\": \"\\\\'[^']*\\\\'\", \"name\": \"constant.character.jssmAction\" }, { \"comment\": \"jssm label annotation\", \"match\": '\\\\\"[^\"]*\\\\\"', \"name\": \"entity.name.tag.jssmLabel.doublequoted\" }, { \"comment\": \"jssm label annotation\", \"match\": \"([a-zA-Z0-9_.+&()#@!?,])\", \"name\": \"entity.name.tag.jssmLabel.atom\" }], \"scopeName\": \"source.jssm\", \"aliases\": [\"fsl\"] });\nvar jssm = [\n  lang\n];\n\nexport { jssm as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;QAAQ;KAAa;IAAE,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,WAAW;YAAiB,OAAO;YAAQ,QAAQ;QAAqB;QAAG;YAAE,SAAS;YAAM,WAAW;YAAiB,OAAO;YAAK,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,WAAW;YAAe,OAAO;YAAK,QAAQ;QAAgB;QAAG;YAAE,WAAW;YAAU,SAAS;YAAgC,QAAQ;QAAmB;QAAG;YAAE,WAAW;YAAwB,SAAS;YAAyB,QAAQ;QAAiC;QAAG;YAAE,WAAW;YAAwB,SAAS;YAAyB,QAAQ;QAAiC;QAAG;YAAE,WAAW;YAAwB,SAAS;YAA4B,QAAQ;QAAiC;QAAG;YAAE,WAAW;YAAwB,SAAS;YAAyB,QAAQ;QAAiC;QAAG;YAAE,WAAW;YAAe,SAAS;YAAO,QAAQ;QAAmD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAkD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAkD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAO,QAAQ;QAAiD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAiD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAiD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAO,QAAQ;QAAqD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAmD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAM,QAAQ;QAAmD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAkD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAkD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAoD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAoD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAmD;QAAG;YAAE,WAAW;YAAe,SAAS;YAAQ,QAAQ;QAAmD;QAAG;YAAE,WAAW;YAA+B,SAAS;YAAW,QAAQ;QAAmC;QAAG;YAAE,WAAW;YAAqB,SAAS;YAAe,QAAQ;QAAgC;QAAG;YAAE,WAAW;YAAyB,SAAS;YAAe,QAAQ;QAAyC;QAAG;YAAE,WAAW;YAAyB,SAAS;YAA4B,QAAQ;QAAiC;KAAE;IAAE,aAAa;IAAe,WAAW;QAAC;KAAM;AAAC;AACxkG,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}