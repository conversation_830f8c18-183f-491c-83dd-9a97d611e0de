{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/vitesse-dark.mjs"], "sourcesContent": ["var vitesseDark = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#4d9375\",\n    \"activityBar.background\": \"#121212\",\n    \"activityBar.border\": \"#191919\",\n    \"activityBar.foreground\": \"#dbd7caee\",\n    \"activityBar.inactiveForeground\": \"#dedcd550\",\n    \"activityBarBadge.background\": \"#bfbaaa\",\n    \"activityBarBadge.foreground\": \"#121212\",\n    \"badge.background\": \"#dedcd590\",\n    \"badge.foreground\": \"#121212\",\n    \"breadcrumb.activeSelectionForeground\": \"#eeeeee18\",\n    \"breadcrumb.background\": \"#181818\",\n    \"breadcrumb.focusForeground\": \"#dbd7caee\",\n    \"breadcrumb.foreground\": \"#959da5\",\n    \"breadcrumbPicker.background\": \"#121212\",\n    \"button.background\": \"#4d9375\",\n    \"button.foreground\": \"#121212\",\n    \"button.hoverBackground\": \"#4d9375\",\n    \"checkbox.background\": \"#181818\",\n    \"checkbox.border\": \"#2f363d\",\n    \"debugToolBar.background\": \"#121212\",\n    \"descriptionForeground\": \"#dedcd590\",\n    \"diffEditor.insertedTextBackground\": \"#4d937550\",\n    \"diffEditor.removedTextBackground\": \"#ab595950\",\n    \"dropdown.background\": \"#121212\",\n    \"dropdown.border\": \"#191919\",\n    \"dropdown.foreground\": \"#dbd7caee\",\n    \"dropdown.listBackground\": \"#181818\",\n    \"editor.background\": \"#121212\",\n    \"editor.findMatchBackground\": \"#e6cc7722\",\n    \"editor.findMatchHighlightBackground\": \"#e6cc7744\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#b808\",\n    \"editor.foldBackground\": \"#eeeeee10\",\n    \"editor.foreground\": \"#dbd7caee\",\n    \"editor.inactiveSelectionBackground\": \"#eeeeee10\",\n    \"editor.lineHighlightBackground\": \"#181818\",\n    \"editor.selectionBackground\": \"#eeeeee18\",\n    \"editor.selectionHighlightBackground\": \"#eeeeee10\",\n    \"editor.stackFrameHighlightBackground\": \"#a707\",\n    \"editor.wordHighlightBackground\": \"#1c6b4805\",\n    \"editor.wordHighlightStrongBackground\": \"#1c6b4810\",\n    \"editorBracketHighlight.foreground1\": \"#5eaab5\",\n    \"editorBracketHighlight.foreground2\": \"#4d9375\",\n    \"editorBracketHighlight.foreground3\": \"#d4976c\",\n    \"editorBracketHighlight.foreground4\": \"#d9739f\",\n    \"editorBracketHighlight.foreground5\": \"#e6cc77\",\n    \"editorBracketHighlight.foreground6\": \"#6394bf\",\n    \"editorBracketMatch.background\": \"#4d937520\",\n    \"editorError.foreground\": \"#cb7676\",\n    \"editorGroup.border\": \"#191919\",\n    \"editorGroupHeader.tabsBackground\": \"#121212\",\n    \"editorGroupHeader.tabsBorder\": \"#191919\",\n    \"editorGutter.addedBackground\": \"#4d9375\",\n    \"editorGutter.commentRangeForeground\": \"#dedcd550\",\n    \"editorGutter.deletedBackground\": \"#cb7676\",\n    \"editorGutter.foldingControlForeground\": \"#dedcd590\",\n    \"editorGutter.modifiedBackground\": \"#6394bf\",\n    \"editorHint.foreground\": \"#4d9375\",\n    \"editorIndentGuide.activeBackground\": \"#ffffff30\",\n    \"editorIndentGuide.background\": \"#ffffff15\",\n    \"editorInfo.foreground\": \"#6394bf\",\n    \"editorInlayHint.background\": \"#181818\",\n    \"editorInlayHint.foreground\": \"#666666\",\n    \"editorLineNumber.activeForeground\": \"#bfbaaa\",\n    \"editorLineNumber.foreground\": \"#dedcd550\",\n    \"editorOverviewRuler.border\": \"#111\",\n    \"editorStickyScroll.background\": \"#181818\",\n    \"editorStickyScrollHover.background\": \"#181818\",\n    \"editorWarning.foreground\": \"#d4976c\",\n    \"editorWhitespace.foreground\": \"#ffffff15\",\n    \"editorWidget.background\": \"#121212\",\n    \"errorForeground\": \"#cb7676\",\n    \"focusBorder\": \"#00000000\",\n    \"foreground\": \"#dbd7caee\",\n    \"gitDecoration.addedResourceForeground\": \"#4d9375\",\n    \"gitDecoration.conflictingResourceForeground\": \"#d4976c\",\n    \"gitDecoration.deletedResourceForeground\": \"#cb7676\",\n    \"gitDecoration.ignoredResourceForeground\": \"#dedcd550\",\n    \"gitDecoration.modifiedResourceForeground\": \"#6394bf\",\n    \"gitDecoration.submoduleResourceForeground\": \"#dedcd590\",\n    \"gitDecoration.untrackedResourceForeground\": \"#5eaab5\",\n    \"input.background\": \"#181818\",\n    \"input.border\": \"#191919\",\n    \"input.foreground\": \"#dbd7caee\",\n    \"input.placeholderForeground\": \"#dedcd590\",\n    \"inputOption.activeBackground\": \"#dedcd550\",\n    \"list.activeSelectionBackground\": \"#181818\",\n    \"list.activeSelectionForeground\": \"#dbd7caee\",\n    \"list.focusBackground\": \"#181818\",\n    \"list.highlightForeground\": \"#4d9375\",\n    \"list.hoverBackground\": \"#181818\",\n    \"list.hoverForeground\": \"#dbd7caee\",\n    \"list.inactiveFocusBackground\": \"#121212\",\n    \"list.inactiveSelectionBackground\": \"#181818\",\n    \"list.inactiveSelectionForeground\": \"#dbd7caee\",\n    \"menu.separatorBackground\": \"#191919\",\n    \"notificationCenterHeader.background\": \"#121212\",\n    \"notificationCenterHeader.foreground\": \"#959da5\",\n    \"notifications.background\": \"#121212\",\n    \"notifications.border\": \"#191919\",\n    \"notifications.foreground\": \"#dbd7caee\",\n    \"notificationsErrorIcon.foreground\": \"#cb7676\",\n    \"notificationsInfoIcon.foreground\": \"#6394bf\",\n    \"notificationsWarningIcon.foreground\": \"#d4976c\",\n    \"panel.background\": \"#121212\",\n    \"panel.border\": \"#191919\",\n    \"panelInput.border\": \"#2f363d\",\n    \"panelTitle.activeBorder\": \"#4d9375\",\n    \"panelTitle.activeForeground\": \"#dbd7caee\",\n    \"panelTitle.inactiveForeground\": \"#959da5\",\n    \"peekViewEditor.background\": \"#121212\",\n    \"peekViewEditor.matchHighlightBackground\": \"#ffd33d33\",\n    \"peekViewResult.background\": \"#121212\",\n    \"peekViewResult.matchHighlightBackground\": \"#ffd33d33\",\n    \"pickerGroup.border\": \"#191919\",\n    \"pickerGroup.foreground\": \"#dbd7caee\",\n    \"problemsErrorIcon.foreground\": \"#cb7676\",\n    \"problemsInfoIcon.foreground\": \"#6394bf\",\n    \"problemsWarningIcon.foreground\": \"#d4976c\",\n    \"progressBar.background\": \"#4d9375\",\n    \"quickInput.background\": \"#121212\",\n    \"quickInput.foreground\": \"#dbd7caee\",\n    \"quickInputList.focusBackground\": \"#181818\",\n    \"scrollbar.shadow\": \"#0000\",\n    \"scrollbarSlider.activeBackground\": \"#dedcd550\",\n    \"scrollbarSlider.background\": \"#dedcd510\",\n    \"scrollbarSlider.hoverBackground\": \"#dedcd550\",\n    \"settings.headerForeground\": \"#dbd7caee\",\n    \"settings.modifiedItemIndicator\": \"#4d9375\",\n    \"sideBar.background\": \"#121212\",\n    \"sideBar.border\": \"#191919\",\n    \"sideBar.foreground\": \"#bfbaaa\",\n    \"sideBarSectionHeader.background\": \"#121212\",\n    \"sideBarSectionHeader.border\": \"#191919\",\n    \"sideBarSectionHeader.foreground\": \"#dbd7caee\",\n    \"sideBarTitle.foreground\": \"#dbd7caee\",\n    \"statusBar.background\": \"#121212\",\n    \"statusBar.border\": \"#191919\",\n    \"statusBar.debuggingBackground\": \"#181818\",\n    \"statusBar.debuggingForeground\": \"#bfbaaa\",\n    \"statusBar.foreground\": \"#bfbaaa\",\n    \"statusBar.noFolderBackground\": \"#121212\",\n    \"statusBarItem.prominentBackground\": \"#181818\",\n    \"tab.activeBackground\": \"#121212\",\n    \"tab.activeBorder\": \"#191919\",\n    \"tab.activeBorderTop\": \"#dedcd590\",\n    \"tab.activeForeground\": \"#dbd7caee\",\n    \"tab.border\": \"#191919\",\n    \"tab.hoverBackground\": \"#181818\",\n    \"tab.inactiveBackground\": \"#121212\",\n    \"tab.inactiveForeground\": \"#959da5\",\n    \"tab.unfocusedActiveBorder\": \"#191919\",\n    \"tab.unfocusedActiveBorderTop\": \"#191919\",\n    \"tab.unfocusedHoverBackground\": \"#121212\",\n    \"terminal.ansiBlack\": \"#393a34\",\n    \"terminal.ansiBlue\": \"#6394bf\",\n    \"terminal.ansiBrightBlack\": \"#777777\",\n    \"terminal.ansiBrightBlue\": \"#6394bf\",\n    \"terminal.ansiBrightCyan\": \"#5eaab5\",\n    \"terminal.ansiBrightGreen\": \"#4d9375\",\n    \"terminal.ansiBrightMagenta\": \"#d9739f\",\n    \"terminal.ansiBrightRed\": \"#cb7676\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#e6cc77\",\n    \"terminal.ansiCyan\": \"#5eaab5\",\n    \"terminal.ansiGreen\": \"#4d9375\",\n    \"terminal.ansiMagenta\": \"#d9739f\",\n    \"terminal.ansiRed\": \"#cb7676\",\n    \"terminal.ansiWhite\": \"#dbd7ca\",\n    \"terminal.ansiYellow\": \"#e6cc77\",\n    \"terminal.foreground\": \"#dbd7caee\",\n    \"terminal.selectionBackground\": \"#eeeeee18\",\n    \"textBlockQuote.background\": \"#121212\",\n    \"textBlockQuote.border\": \"#191919\",\n    \"textCodeBlock.background\": \"#121212\",\n    \"textLink.activeForeground\": \"#4d9375\",\n    \"textLink.foreground\": \"#4d9375\",\n    \"textPreformat.foreground\": \"#d1d5da\",\n    \"textSeparator.foreground\": \"#586069\",\n    \"titleBar.activeBackground\": \"#121212\",\n    \"titleBar.activeForeground\": \"#bfbaaa\",\n    \"titleBar.border\": \"#181818\",\n    \"titleBar.inactiveBackground\": \"#121212\",\n    \"titleBar.inactiveForeground\": \"#959da5\",\n    \"tree.indentGuidesStroke\": \"#2f363d\",\n    \"welcomePage.buttonBackground\": \"#2f363d\",\n    \"welcomePage.buttonHoverBackground\": \"#444d56\"\n  },\n  \"displayName\": \"Vitesse Dark\",\n  \"name\": \"vitesse-dark\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"class\": \"#6872ab\",\n    \"interface\": \"#5d99a9\",\n    \"namespace\": \"#db889a\",\n    \"property\": \"#b8a965\",\n    \"type\": \"#5d99a9\"\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#758575dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"delimiter.bracket\",\n        \"delimiter\",\n        \"invalid.illegal.character-not-allowed-here.html\",\n        \"keyword.operator.rest\",\n        \"keyword.operator.spread\",\n        \"keyword.operator.type.annotation\",\n        \"keyword.operator.relational\",\n        \"keyword.operator.assignment\",\n        \"keyword.operator.type\",\n        \"meta.brace\",\n        \"meta.tag.block.any.html\",\n        \"meta.tag.inline.any.html\",\n        \"meta.tag.structure.input.void.html\",\n        \"meta.type.annotation\",\n        \"meta.embedded.block.github-actions-expression\",\n        \"storage.type.function.arrow\",\n        \"meta.objectliteral.ts\",\n        \"punctuation\",\n        \"punctuation.definition.string.begin.html.vue\",\n        \"punctuation.definition.string.end.html.vue\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#666666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.language\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c99076\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity\",\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#80a665\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#dbd7caee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"tag.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#80a665\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"storage.type.class.jsdoc\",\n        \"punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\",\n        \"support.type.builtin\",\n        \"constant.language.undefined\",\n        \"constant.language.null\",\n        \"constant.language.import-export-all.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cb7676\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.derivative\",\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#dbd7caee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\",\n        \"attribute.value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c98a7d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c98a7d77\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b8a96577\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#b8a965\"\n      }\n    },\n    {\n      \"scope\": [\n        \"property\",\n        \"meta.property-name\",\n        \"meta.object-literal.key\",\n        \"entity.name.tag.yaml\",\n        \"attribute.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b8a965\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"invalid.deprecated.entity.other.attribute-name.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bd976a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"identifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bd976a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.primitive\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DA994\"\n      }\n    },\n    {\n      \"scope\": \"namespace\",\n      \"settings\": {\n        \"foreground\": \"#db889a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"keyword.operator.assignment.compound\",\n        \"meta.var.expr.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cb7676\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#f97583\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#24292e\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#c98a7d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c4704f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c98a7d\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#e6cc77\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c99076\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.quantifier.regexp\",\n        \"constant.numeric\",\n        \"number\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4C9A91\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.unit\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cb7676\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.boolean\",\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#d4976c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#5d99a9\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#dbd7caee\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#dbd7caee\"\n      }\n    },\n    {\n      \"scope\": \"markup.raw\",\n      \"settings\": {\n        \"foreground\": \"#4d9375\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#86181d\",\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#144620\",\n        \"foreground\": \"#85e89d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#c24e00\",\n        \"foreground\": \"#ffab70\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#79b8ff\",\n        \"foreground\": \"#2f363d\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#b392f0\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#79b8ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#79b8ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#79b8ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d1d5da\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#fdaeb7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\",\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c98a7d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link.markdown\",\n        \"markup.underline.link.image.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#dedcd590\"\n      }\n    },\n    {\n      \"scope\": [\n        \"type.identifier\",\n        \"constant.other.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6872ab\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.html.vue\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#80a665\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.illegal.unrecognized-tag.html\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { vitesseDark as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc,OAAO,MAAM,CAAC;IAC9B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,yBAAyB;QACzB,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,iCAAiC;QACjC,sCAAsC;QACtC,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,4BAA4B;QAC5B,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,kCAAkC;QAClC,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,SAAS;QACT,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;IACV;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}