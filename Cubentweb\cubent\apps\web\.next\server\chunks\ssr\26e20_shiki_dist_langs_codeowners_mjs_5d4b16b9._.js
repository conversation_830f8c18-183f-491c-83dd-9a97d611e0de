module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/codeowners.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>codeowners)
});
const lang = Object.freeze({
    "displayName": "CODEOWNERS",
    "name": "codeowners",
    "patterns": [
        {
            "include": "#comment"
        },
        {
            "include": "#pattern"
        },
        {
            "include": "#owner"
        }
    ],
    "repository": {
        "comment": {
            "patterns": [
                {
                    "begin": "^\\s*#",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.codeowners"
                        }
                    },
                    "end": "$",
                    "name": "comment.line.codeowners"
                }
            ]
        },
        "owner": {
            "match": "\\S*@\\S+",
            "name": "storage.type.function.codeowners"
        },
        "pattern": {
            "match": "^\\s*(\\S+)",
            "name": "variable.other.codeowners"
        }
    },
    "scopeName": "text.codeowners"
});
var codeowners = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_codeowners_mjs_5d4b16b9._.js.map