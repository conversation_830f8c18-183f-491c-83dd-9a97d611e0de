{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/nord.mjs"], "sourcesContent": ["var nord = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#3b4252\",\n    \"activityBar.activeBorder\": \"#88c0d0\",\n    \"activityBar.background\": \"#2e3440\",\n    \"activityBar.dropBackground\": \"#3b4252\",\n    \"activityBar.foreground\": \"#d8dee9\",\n    \"activityBarBadge.background\": \"#88c0d0\",\n    \"activityBarBadge.foreground\": \"#2e3440\",\n    \"badge.background\": \"#88c0d0\",\n    \"badge.foreground\": \"#2e3440\",\n    \"button.background\": \"#88c0d0ee\",\n    \"button.foreground\": \"#2e3440\",\n    \"button.hoverBackground\": \"#88c0d0\",\n    \"button.secondaryBackground\": \"#434c5e\",\n    \"button.secondaryForeground\": \"#d8dee9\",\n    \"button.secondaryHoverBackground\": \"#4c566a\",\n    \"charts.blue\": \"#81a1c1\",\n    \"charts.foreground\": \"#d8dee9\",\n    \"charts.green\": \"#a3be8c\",\n    \"charts.lines\": \"#88c0d0\",\n    \"charts.orange\": \"#d08770\",\n    \"charts.purple\": \"#b48ead\",\n    \"charts.red\": \"#bf616a\",\n    \"charts.yellow\": \"#ebcb8b\",\n    \"debugConsole.errorForeground\": \"#bf616a\",\n    \"debugConsole.infoForeground\": \"#88c0d0\",\n    \"debugConsole.sourceForeground\": \"#616e88\",\n    \"debugConsole.warningForeground\": \"#ebcb8b\",\n    \"debugConsoleInputIcon.foreground\": \"#81a1c1\",\n    \"debugExceptionWidget.background\": \"#4c566a\",\n    \"debugExceptionWidget.border\": \"#2e3440\",\n    \"debugToolBar.background\": \"#3b4252\",\n    \"descriptionForeground\": \"#d8dee9e6\",\n    \"diffEditor.insertedTextBackground\": \"#81a1c133\",\n    \"diffEditor.removedTextBackground\": \"#bf616a4d\",\n    \"dropdown.background\": \"#3b4252\",\n    \"dropdown.border\": \"#3b4252\",\n    \"dropdown.foreground\": \"#d8dee9\",\n    \"editor.background\": \"#2e3440\",\n    \"editor.findMatchBackground\": \"#88c0d066\",\n    \"editor.findMatchHighlightBackground\": \"#88c0d033\",\n    \"editor.findRangeHighlightBackground\": \"#88c0d033\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#5e81ac\",\n    \"editor.foreground\": \"#d8dee9\",\n    \"editor.hoverHighlightBackground\": \"#3b4252\",\n    \"editor.inactiveSelectionBackground\": \"#434c5ecc\",\n    \"editor.inlineValuesBackground\": \"#4c566a\",\n    \"editor.inlineValuesForeground\": \"#eceff4\",\n    \"editor.lineHighlightBackground\": \"#3b4252\",\n    \"editor.lineHighlightBorder\": \"#3b4252\",\n    \"editor.rangeHighlightBackground\": \"#434c5e52\",\n    \"editor.selectionBackground\": \"#434c5ecc\",\n    \"editor.selectionHighlightBackground\": \"#434c5ecc\",\n    \"editor.stackFrameHighlightBackground\": \"#5e81ac\",\n    \"editor.wordHighlightBackground\": \"#81a1c166\",\n    \"editor.wordHighlightStrongBackground\": \"#81a1c199\",\n    \"editorActiveLineNumber.foreground\": \"#d8dee9cc\",\n    \"editorBracketHighlight.foreground1\": \"#8fbcbb\",\n    \"editorBracketHighlight.foreground2\": \"#88c0d0\",\n    \"editorBracketHighlight.foreground3\": \"#81a1c1\",\n    \"editorBracketHighlight.foreground4\": \"#5e81ac\",\n    \"editorBracketHighlight.foreground5\": \"#8fbcbb\",\n    \"editorBracketHighlight.foreground6\": \"#88c0d0\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#bf616a\",\n    \"editorBracketMatch.background\": \"#2e344000\",\n    \"editorBracketMatch.border\": \"#88c0d0\",\n    \"editorCodeLens.foreground\": \"#4c566a\",\n    \"editorCursor.foreground\": \"#d8dee9\",\n    \"editorError.border\": \"#bf616a00\",\n    \"editorError.foreground\": \"#bf616a\",\n    \"editorGroup.background\": \"#2e3440\",\n    \"editorGroup.border\": \"#3b425201\",\n    \"editorGroup.dropBackground\": \"#3b425299\",\n    \"editorGroupHeader.border\": \"#3b425200\",\n    \"editorGroupHeader.noTabsBackground\": \"#2e3440\",\n    \"editorGroupHeader.tabsBackground\": \"#2e3440\",\n    \"editorGroupHeader.tabsBorder\": \"#3b425200\",\n    \"editorGutter.addedBackground\": \"#a3be8c\",\n    \"editorGutter.background\": \"#2e3440\",\n    \"editorGutter.deletedBackground\": \"#bf616a\",\n    \"editorGutter.modifiedBackground\": \"#ebcb8b\",\n    \"editorHint.border\": \"#ebcb8b00\",\n    \"editorHint.foreground\": \"#ebcb8b\",\n    \"editorHoverWidget.background\": \"#3b4252\",\n    \"editorHoverWidget.border\": \"#3b4252\",\n    \"editorIndentGuide.activeBackground\": \"#4c566a\",\n    \"editorIndentGuide.background\": \"#434c5eb3\",\n    \"editorInlayHint.background\": \"#434c5e\",\n    \"editorInlayHint.foreground\": \"#d8dee9\",\n    \"editorLineNumber.activeForeground\": \"#d8dee9\",\n    \"editorLineNumber.foreground\": \"#4c566a\",\n    \"editorLink.activeForeground\": \"#88c0d0\",\n    \"editorMarkerNavigation.background\": \"#5e81acc0\",\n    \"editorMarkerNavigationError.background\": \"#bf616ac0\",\n    \"editorMarkerNavigationWarning.background\": \"#ebcb8bc0\",\n    \"editorOverviewRuler.addedForeground\": \"#a3be8c\",\n    \"editorOverviewRuler.border\": \"#3b4252\",\n    \"editorOverviewRuler.currentContentForeground\": \"#3b4252\",\n    \"editorOverviewRuler.deletedForeground\": \"#bf616a\",\n    \"editorOverviewRuler.errorForeground\": \"#bf616a\",\n    \"editorOverviewRuler.findMatchForeground\": \"#88c0d066\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#3b4252\",\n    \"editorOverviewRuler.infoForeground\": \"#81a1c1\",\n    \"editorOverviewRuler.modifiedForeground\": \"#ebcb8b\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#88c0d066\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#88c0d066\",\n    \"editorOverviewRuler.warningForeground\": \"#ebcb8b\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#88c0d066\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#88c0d066\",\n    \"editorRuler.foreground\": \"#434c5e\",\n    \"editorSuggestWidget.background\": \"#2e3440\",\n    \"editorSuggestWidget.border\": \"#3b4252\",\n    \"editorSuggestWidget.focusHighlightForeground\": \"#88c0d0\",\n    \"editorSuggestWidget.foreground\": \"#d8dee9\",\n    \"editorSuggestWidget.highlightForeground\": \"#88c0d0\",\n    \"editorSuggestWidget.selectedBackground\": \"#434c5e\",\n    \"editorSuggestWidget.selectedForeground\": \"#d8dee9\",\n    \"editorWarning.border\": \"#ebcb8b00\",\n    \"editorWarning.foreground\": \"#ebcb8b\",\n    \"editorWhitespace.foreground\": \"#4c566ab3\",\n    \"editorWidget.background\": \"#2e3440\",\n    \"editorWidget.border\": \"#3b4252\",\n    \"errorForeground\": \"#bf616a\",\n    \"extensionButton.prominentBackground\": \"#434c5e\",\n    \"extensionButton.prominentForeground\": \"#d8dee9\",\n    \"extensionButton.prominentHoverBackground\": \"#4c566a\",\n    \"focusBorder\": \"#3b4252\",\n    \"foreground\": \"#d8dee9\",\n    \"gitDecoration.conflictingResourceForeground\": \"#5e81ac\",\n    \"gitDecoration.deletedResourceForeground\": \"#bf616a\",\n    \"gitDecoration.ignoredResourceForeground\": \"#d8dee966\",\n    \"gitDecoration.modifiedResourceForeground\": \"#ebcb8b\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#bf616a\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#ebcb8b\",\n    \"gitDecoration.submoduleResourceForeground\": \"#8fbcbb\",\n    \"gitDecoration.untrackedResourceForeground\": \"#a3be8c\",\n    \"input.background\": \"#3b4252\",\n    \"input.border\": \"#3b4252\",\n    \"input.foreground\": \"#d8dee9\",\n    \"input.placeholderForeground\": \"#d8dee999\",\n    \"inputOption.activeBackground\": \"#5e81ac\",\n    \"inputOption.activeBorder\": \"#5e81ac\",\n    \"inputOption.activeForeground\": \"#eceff4\",\n    \"inputValidation.errorBackground\": \"#bf616a\",\n    \"inputValidation.errorBorder\": \"#bf616a\",\n    \"inputValidation.infoBackground\": \"#81a1c1\",\n    \"inputValidation.infoBorder\": \"#81a1c1\",\n    \"inputValidation.warningBackground\": \"#d08770\",\n    \"inputValidation.warningBorder\": \"#d08770\",\n    \"keybindingLabel.background\": \"#4c566a\",\n    \"keybindingLabel.border\": \"#4c566a\",\n    \"keybindingLabel.bottomBorder\": \"#4c566a\",\n    \"keybindingLabel.foreground\": \"#d8dee9\",\n    \"list.activeSelectionBackground\": \"#88c0d0\",\n    \"list.activeSelectionForeground\": \"#2e3440\",\n    \"list.dropBackground\": \"#88c0d099\",\n    \"list.errorForeground\": \"#bf616a\",\n    \"list.focusBackground\": \"#88c0d099\",\n    \"list.focusForeground\": \"#d8dee9\",\n    \"list.focusHighlightForeground\": \"#eceff4\",\n    \"list.highlightForeground\": \"#88c0d0\",\n    \"list.hoverBackground\": \"#3b4252\",\n    \"list.hoverForeground\": \"#eceff4\",\n    \"list.inactiveFocusBackground\": \"#434c5ecc\",\n    \"list.inactiveSelectionBackground\": \"#434c5e\",\n    \"list.inactiveSelectionForeground\": \"#d8dee9\",\n    \"list.warningForeground\": \"#ebcb8b\",\n    \"merge.border\": \"#3b425200\",\n    \"merge.currentContentBackground\": \"#81a1c14d\",\n    \"merge.currentHeaderBackground\": \"#81a1c166\",\n    \"merge.incomingContentBackground\": \"#8fbcbb4d\",\n    \"merge.incomingHeaderBackground\": \"#8fbcbb66\",\n    \"minimap.background\": \"#2e3440\",\n    \"minimap.errorHighlight\": \"#bf616acc\",\n    \"minimap.findMatchHighlight\": \"#88c0d0\",\n    \"minimap.selectionHighlight\": \"#88c0d0cc\",\n    \"minimap.warningHighlight\": \"#ebcb8bcc\",\n    \"minimapGutter.addedBackground\": \"#a3be8c\",\n    \"minimapGutter.deletedBackground\": \"#bf616a\",\n    \"minimapGutter.modifiedBackground\": \"#ebcb8b\",\n    \"minimapSlider.activeBackground\": \"#434c5eaa\",\n    \"minimapSlider.background\": \"#434c5e99\",\n    \"minimapSlider.hoverBackground\": \"#434c5eaa\",\n    \"notification.background\": \"#3b4252\",\n    \"notification.buttonBackground\": \"#434c5e\",\n    \"notification.buttonForeground\": \"#d8dee9\",\n    \"notification.buttonHoverBackground\": \"#4c566a\",\n    \"notification.errorBackground\": \"#bf616a\",\n    \"notification.errorForeground\": \"#2e3440\",\n    \"notification.foreground\": \"#d8dee9\",\n    \"notification.infoBackground\": \"#88c0d0\",\n    \"notification.infoForeground\": \"#2e3440\",\n    \"notification.warningBackground\": \"#ebcb8b\",\n    \"notification.warningForeground\": \"#2e3440\",\n    \"notificationCenter.border\": \"#3b425200\",\n    \"notificationCenterHeader.background\": \"#2e3440\",\n    \"notificationCenterHeader.foreground\": \"#88c0d0\",\n    \"notificationLink.foreground\": \"#88c0d0\",\n    \"notificationToast.border\": \"#3b425200\",\n    \"notifications.background\": \"#3b4252\",\n    \"notifications.border\": \"#2e3440\",\n    \"notifications.foreground\": \"#d8dee9\",\n    \"panel.background\": \"#2e3440\",\n    \"panel.border\": \"#3b4252\",\n    \"panelTitle.activeBorder\": \"#88c0d000\",\n    \"panelTitle.activeForeground\": \"#88c0d0\",\n    \"panelTitle.inactiveForeground\": \"#d8dee9\",\n    \"peekView.border\": \"#4c566a\",\n    \"peekViewEditor.background\": \"#2e3440\",\n    \"peekViewEditor.matchHighlightBackground\": \"#88c0d04d\",\n    \"peekViewEditorGutter.background\": \"#2e3440\",\n    \"peekViewResult.background\": \"#2e3440\",\n    \"peekViewResult.fileForeground\": \"#88c0d0\",\n    \"peekViewResult.lineForeground\": \"#d8dee966\",\n    \"peekViewResult.matchHighlightBackground\": \"#88c0d0cc\",\n    \"peekViewResult.selectionBackground\": \"#434c5e\",\n    \"peekViewResult.selectionForeground\": \"#d8dee9\",\n    \"peekViewTitle.background\": \"#3b4252\",\n    \"peekViewTitleDescription.foreground\": \"#d8dee9\",\n    \"peekViewTitleLabel.foreground\": \"#88c0d0\",\n    \"pickerGroup.border\": \"#3b4252\",\n    \"pickerGroup.foreground\": \"#88c0d0\",\n    \"progressBar.background\": \"#88c0d0\",\n    \"quickInputList.focusBackground\": \"#88c0d0\",\n    \"quickInputList.focusForeground\": \"#2e3440\",\n    \"sash.hoverBorder\": \"#88c0d0\",\n    \"scrollbar.shadow\": \"#00000066\",\n    \"scrollbarSlider.activeBackground\": \"#434c5eaa\",\n    \"scrollbarSlider.background\": \"#434c5e99\",\n    \"scrollbarSlider.hoverBackground\": \"#434c5eaa\",\n    \"selection.background\": \"#88c0d099\",\n    \"sideBar.background\": \"#2e3440\",\n    \"sideBar.border\": \"#3b4252\",\n    \"sideBar.foreground\": \"#d8dee9\",\n    \"sideBarSectionHeader.background\": \"#3b4252\",\n    \"sideBarSectionHeader.foreground\": \"#d8dee9\",\n    \"sideBarTitle.foreground\": \"#d8dee9\",\n    \"statusBar.background\": \"#3b4252\",\n    \"statusBar.border\": \"#3b425200\",\n    \"statusBar.debuggingBackground\": \"#5e81ac\",\n    \"statusBar.debuggingForeground\": \"#d8dee9\",\n    \"statusBar.foreground\": \"#d8dee9\",\n    \"statusBar.noFolderBackground\": \"#3b4252\",\n    \"statusBar.noFolderForeground\": \"#d8dee9\",\n    \"statusBarItem.activeBackground\": \"#4c566a\",\n    \"statusBarItem.errorBackground\": \"#3b4252\",\n    \"statusBarItem.errorForeground\": \"#bf616a\",\n    \"statusBarItem.hoverBackground\": \"#434c5e\",\n    \"statusBarItem.prominentBackground\": \"#3b4252\",\n    \"statusBarItem.prominentHoverBackground\": \"#434c5e\",\n    \"statusBarItem.warningBackground\": \"#ebcb8b\",\n    \"statusBarItem.warningForeground\": \"#2e3440\",\n    \"tab.activeBackground\": \"#3b4252\",\n    \"tab.activeBorder\": \"#88c0d000\",\n    \"tab.activeBorderTop\": \"#88c0d000\",\n    \"tab.activeForeground\": \"#d8dee9\",\n    \"tab.border\": \"#3b425200\",\n    \"tab.hoverBackground\": \"#3b4252cc\",\n    \"tab.hoverBorder\": \"#88c0d000\",\n    \"tab.inactiveBackground\": \"#2e3440\",\n    \"tab.inactiveForeground\": \"#d8dee966\",\n    \"tab.lastPinnedBorder\": \"#4c566a\",\n    \"tab.unfocusedActiveBorder\": \"#88c0d000\",\n    \"tab.unfocusedActiveBorderTop\": \"#88c0d000\",\n    \"tab.unfocusedActiveForeground\": \"#d8dee999\",\n    \"tab.unfocusedHoverBackground\": \"#3b4252b3\",\n    \"tab.unfocusedHoverBorder\": \"#88c0d000\",\n    \"tab.unfocusedInactiveForeground\": \"#d8dee966\",\n    \"terminal.ansiBlack\": \"#3b4252\",\n    \"terminal.ansiBlue\": \"#81a1c1\",\n    \"terminal.ansiBrightBlack\": \"#4c566a\",\n    \"terminal.ansiBrightBlue\": \"#81a1c1\",\n    \"terminal.ansiBrightCyan\": \"#8fbcbb\",\n    \"terminal.ansiBrightGreen\": \"#a3be8c\",\n    \"terminal.ansiBrightMagenta\": \"#b48ead\",\n    \"terminal.ansiBrightRed\": \"#bf616a\",\n    \"terminal.ansiBrightWhite\": \"#eceff4\",\n    \"terminal.ansiBrightYellow\": \"#ebcb8b\",\n    \"terminal.ansiCyan\": \"#88c0d0\",\n    \"terminal.ansiGreen\": \"#a3be8c\",\n    \"terminal.ansiMagenta\": \"#b48ead\",\n    \"terminal.ansiRed\": \"#bf616a\",\n    \"terminal.ansiWhite\": \"#e5e9f0\",\n    \"terminal.ansiYellow\": \"#ebcb8b\",\n    \"terminal.background\": \"#2e3440\",\n    \"terminal.foreground\": \"#d8dee9\",\n    \"terminal.tab.activeBorder\": \"#88c0d0\",\n    \"textBlockQuote.background\": \"#3b4252\",\n    \"textBlockQuote.border\": \"#81a1c1\",\n    \"textCodeBlock.background\": \"#4c566a\",\n    \"textLink.activeForeground\": \"#88c0d0\",\n    \"textLink.foreground\": \"#88c0d0\",\n    \"textPreformat.foreground\": \"#8fbcbb\",\n    \"textSeparator.foreground\": \"#eceff4\",\n    \"titleBar.activeBackground\": \"#2e3440\",\n    \"titleBar.activeForeground\": \"#d8dee9\",\n    \"titleBar.border\": \"#2e344000\",\n    \"titleBar.inactiveBackground\": \"#2e3440\",\n    \"titleBar.inactiveForeground\": \"#d8dee966\",\n    \"tree.indentGuidesStroke\": \"#616e88\",\n    \"walkThrough.embeddedEditorBackground\": \"#2e3440\",\n    \"welcomePage.buttonBackground\": \"#434c5e\",\n    \"welcomePage.buttonHoverBackground\": \"#4c566a\",\n    \"widget.shadow\": \"#00000066\"\n  },\n  \"displayName\": \"Nord\",\n  \"name\": \"nord\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#2e3440ff\",\n        \"foreground\": \"#d8dee9ff\"\n      }\n    },\n    {\n      \"scope\": \"emphasis\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"strong\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"foreground\": \"#616E88\"\n      }\n    },\n    {\n      \"scope\": \"constant.character\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"constant.language\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#B48EAD\"\n      }\n    },\n    {\n      \"scope\": \"constant.regexp\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.name.type.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"background\": \"#EBCB8B\",\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"background\": \"#BF616A\",\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.new\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#BF616A\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#A3BE8C\"\n      }\n    },\n    {\n      \"scope\": \"meta.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.method-parameters\",\n        \"punctuation.definition.function-parameters\",\n        \"punctuation.definition.parameters\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.comment\",\n        \"punctuation.end.definition.comment\",\n        \"punctuation.start.definition.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#616E88\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section\",\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded.begin\",\n        \"punctuation.section.embedded.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.terminator\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#A3BE8C\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"support.class\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"support.function.construct\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"support.type\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"support.type.exception\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#b48ead\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#bf616a\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#88c0d0\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#ebcb8b\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.pointer-access.c\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.c meta.preprocessor.include\",\n        \"source.c string.quoted.other.lt-gt.include\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.cpp keyword.control.directive.conditional\",\n        \"source.cpp punctuation.definition.directive\",\n        \"source.c keyword.control.directive.conditional\",\n        \"source.c punctuation.definition.directive\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"source.css constant.other.color.rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#B48EAD\"\n      }\n    },\n    {\n      \"scope\": \"source.css meta.property-value\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css keyword.control.at-rule.media\",\n        \"source.css keyword.control.at-rule.media punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"source.css punctuation.definition.keyword\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.css support.type.property-name\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"source.diff meta.diff.range.context\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.diff meta.diff.header.from-file\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.diff punctuation.definition.from-file\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.diff punctuation.definition.range\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.diff punctuation.definition.separator\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.elixir\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite.module.elixir\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.elixir\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant.elixir\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.go constant.other.placeholder.go\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"source.java comment.block.documentation.javadoc punctuation.definition.entity.html\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.java constant.other\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"source.java keyword.other.documentation\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java keyword.other.documentation.author.javadoc\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.java keyword.other.documentation.directive\",\n        \"source.java keyword.other.documentation.custom\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java keyword.other.documentation.see.javadoc\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java meta.method-call meta.method\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.java meta.tag.template.link.javadoc\",\n        \"source.java string.other.link.title.javadoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java meta.tag.template.value.javadoc\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"source.java punctuation.definition.keyword.javadoc\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.java punctuation.definition.tag.begin.javadoc\",\n        \"source.java punctuation.definition.tag.end.javadoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#616E88\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.modifier.import\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.modifier.package\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.type\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.type.annotation\",\n      \"settings\": {\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.type.generic\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.java storage.type.primitive\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js punctuation.decorator\",\n        \"source.js meta.decorator variable.other.readwrite\",\n        \"source.js meta.decorator entity.name.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"source.js meta.object-literal.key\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"source.js storage.type.class.jsdoc\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js string.quoted.template punctuation.quasi.element.begin\",\n        \"source.js string.quoted.template punctuation.quasi.element.end\",\n        \"source.js string.template punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.js string.quoted.template meta.method-call.with-arguments\",\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js string.template meta.template.expression support.variable.property\",\n        \"source.js string.template meta.template.expression variable.other.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"source.js support.type.primitive\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.js variable.other.object\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": \"source.js variable.other.readwrite.alias\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js meta.embedded.line meta.brace.square\",\n        \"source.js meta.embedded.line meta.brace.round\",\n        \"source.js string.quoted.template meta.brace.square\",\n        \"source.js string.quoted.template meta.brace.round\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": \"text.html.basic constant.character.entity.html\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": \"text.html.basic constant.other.inline-data\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"text.html.basic meta.tag.sgml.doctype\",\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"text.html.basic punctuation.definition.entity\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.properties entity.name.section.group-title.ini\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"source.properties punctuation.separator.key-value.ini\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown markup.fenced_code.block\",\n        \"text.html.markdown markup.fenced_code.block punctuation.definition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown markup.inline.raw\",\n        \"text.html.markdown markup.inline.raw punctuation.definition.raw\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown markup.underline.link\",\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown beginning.punctuation.definition.list\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown beginning.punctuation.definition.quote\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#616E88\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown constant.character.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.math.begin\",\n        \"text.html.markdown punctuation.definition.math.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown punctuation.definition.function.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown punctuation.math.operator.latex\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown punctuation.definition.heading\",\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.constant\",\n        \"text.html.markdown punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown constant.other.reference.link\",\n        \"text.html.markdown string.other.link.description\",\n        \"text.html.markdown string.other.link.title\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"source.perl punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.php meta.function-call\",\n        \"source.php meta.function-call.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.python entity.name.function.decorator\",\n        \"source.python meta.function.decorator support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.function-call.generic\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": \"source.python support.type\",\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.python variable.parameter.function.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.python meta.function.parameters variable.parameter.function.language.special.self\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.rust entity.name.type\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"source.rust meta.macro entity.name.function\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.rust meta.attribute\",\n        \"source.rust meta.attribute punctuation\",\n        \"source.rust meta.attribute keyword.operator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"source.rust entity.name.type.trait\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"source.rust punctuation.definition.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#EBCB8B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss punctuation.definition.interpolation.begin.bracket.curly\",\n        \"source.css.scss punctuation.definition.interpolation.end.bracket.curly\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81A1C1\"\n      }\n    },\n    {\n      \"scope\": \"source.css.scss variable.interpolation\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts punctuation.decorator\",\n        \"source.ts meta.decorator variable.other.readwrite\",\n        \"source.ts meta.decorator entity.name.function\",\n        \"source.tsx punctuation.decorator\",\n        \"source.tsx meta.decorator variable.other.readwrite\",\n        \"source.tsx meta.decorator entity.name.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts meta.object-literal.key\",\n        \"source.tsx meta.object-literal.key\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts meta.object-literal.key entity.name.function\",\n        \"source.tsx meta.object-literal.key entity.name.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#88C0D0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts support.class\",\n        \"source.ts support.type\",\n        \"source.ts entity.name.type\",\n        \"source.ts entity.name.class\",\n        \"source.tsx support.class\",\n        \"source.tsx support.type\",\n        \"source.tsx entity.name.type\",\n        \"source.tsx entity.name.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts support.constant.math\",\n        \"source.ts support.constant.dom\",\n        \"source.ts support.constant.json\",\n        \"source.tsx support.constant.math\",\n        \"source.tsx support.constant.dom\",\n        \"source.tsx support.constant.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts support.variable\",\n        \"source.tsx support.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D8DEE9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ts meta.embedded.line meta.brace.square\",\n        \"source.ts meta.embedded.line meta.brace.round\",\n        \"source.tsx meta.embedded.line meta.brace.square\",\n        \"source.tsx meta.embedded.line meta.brace.round\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ECEFF4\"\n      }\n    },\n    {\n      \"scope\": \"text.xml entity.name.tag.namespace\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    },\n    {\n      \"scope\": \"text.xml keyword.other.doctype\",\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": \"text.xml meta.tag.preprocessor entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#5E81AC\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.xml string.unquoted.cdata\",\n        \"text.xml string.unquoted.cdata punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#D08770\"\n      }\n    },\n    {\n      \"scope\": \"source.yaml entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#8FBCBB\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { nord as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,OAAO,OAAO,MAAM,CAAC;IACvB,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,+CAA+C;QAC/C,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,iCAAiC;QACjC,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,wCAAwC;QACxC,qCAAqC;QACrC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,4BAA4B;QAC5B,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,kCAAkC;QAClC,mCAAmC;QACnC,qBAAqB;QACrB,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,uCAAuC;QACvC,8BAA8B;QAC9B,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,gDAAgD;QAChD,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,0CAA0C;QAC1C,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,mBAAmB;QACnB,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,iCAAiC;QACjC,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,gBAAgB;QAChB,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,2BAA2B;QAC3B,iCAAiC;QACjC,iCAAiC;QACjC,sCAAsC;QACtC,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,kCAAkC;QAClC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,oBAAoB;QACpB,gBAAgB;QAChB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,kCAAkC;QAClC,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,0CAA0C;QAC1C,mCAAmC;QACnC,mCAAmC;QACnC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,6BAA6B;QAC7B,gCAAgC;QAChC,iCAAiC;QACjC,gCAAgC;QAChC,4BAA4B;QAC5B,mCAAmC;QACnC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,wCAAwC;QACxC,gCAAgC;QAChC,qCAAqC;QACrC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}