{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/plastic.mjs"], "sourcesContent": ["var plastic = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#1085FF\",\n    \"activityBar.background\": \"#21252B\",\n    \"activityBar.border\": \"#0D1117\",\n    \"activityBar.foreground\": \"#C6CCD7\",\n    \"activityBar.inactiveForeground\": \"#5F6672\",\n    \"activityBarBadge.background\": \"#E06C75\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"breadcrumb.focusForeground\": \"#C6CCD7\",\n    \"breadcrumb.foreground\": \"#5F6672\",\n    \"button.background\": \"#E06C75\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#E48189\",\n    \"button.secondaryBackground\": \"#0D1117\",\n    \"button.secondaryForeground\": \"#ffffff\",\n    \"checkbox.background\": \"#61AFEF\",\n    \"checkbox.foreground\": \"#ffffff\",\n    \"contrastBorder\": \"#0D1117\",\n    \"debugToolBar.background\": \"#181A1F\",\n    \"diffEditor.border\": \"#0D1117\",\n    \"diffEditor.diagonalFill\": \"#0D1117\",\n    \"diffEditor.insertedLineBackground\": \"#CBF6AC0D\",\n    \"diffEditor.insertedTextBackground\": \"#CBF6AC1A\",\n    \"diffEditor.removedLineBackground\": \"#FF9FA80D\",\n    \"diffEditor.removedTextBackground\": \"#FF9FA81A\",\n    \"dropdown.background\": \"#181A1F\",\n    \"dropdown.border\": \"#0D1117\",\n    \"editor.background\": \"#21252B\",\n    \"editor.findMatchBackground\": \"#00000000\",\n    \"editor.findMatchBorder\": \"#1085FF\",\n    \"editor.findMatchHighlightBackground\": \"#00000000\",\n    \"editor.findMatchHighlightBorder\": \"#C6CCD7\",\n    \"editor.foreground\": \"#A9B2C3\",\n    \"editor.lineHighlightBackground\": \"#A9B2C31A\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.linkedEditingBackground\": \"#0D1117\",\n    \"editor.rangeHighlightBorder\": \"#C6CCD7\",\n    \"editor.selectionBackground\": \"#A9B2C333\",\n    \"editor.selectionHighlightBackground\": \"#A9B2C31A\",\n    \"editor.selectionHighlightBorder\": \"#C6CCD7\",\n    \"editor.wordHighlightBackground\": \"#00000000\",\n    \"editor.wordHighlightBorder\": \"#1085FF\",\n    \"editor.wordHighlightStrongBackground\": \"#00000000\",\n    \"editor.wordHighlightStrongBorder\": \"#1085FF\",\n    \"editorBracketHighlight.foreground1\": \"#A9B2C3\",\n    \"editorBracketHighlight.foreground2\": \"#61AFEF\",\n    \"editorBracketHighlight.foreground3\": \"#E5C07B\",\n    \"editorBracketHighlight.foreground4\": \"#E06C75\",\n    \"editorBracketHighlight.foreground5\": \"#98C379\",\n    \"editorBracketHighlight.foreground6\": \"#B57EDC\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#D74E42\",\n    \"editorBracketMatch.background\": \"#00000000\",\n    \"editorBracketMatch.border\": \"#1085FF\",\n    \"editorCursor.foreground\": \"#A9B2C3\",\n    \"editorError.foreground\": \"#D74E42\",\n    \"editorGroup.border\": \"#0D1117\",\n    \"editorGroup.emptyBackground\": \"#181A1F\",\n    \"editorGroupHeader.tabsBackground\": \"#181A1F\",\n    \"editorGutter.addedBackground\": \"#98C379\",\n    \"editorGutter.deletedBackground\": \"#E06C75\",\n    \"editorGutter.modifiedBackground\": \"#D19A66\",\n    \"editorHoverWidget.background\": \"#181A1F\",\n    \"editorHoverWidget.border\": \"#1085FF\",\n    \"editorIndentGuide.activeBackground\": \"#A9B2C333\",\n    \"editorIndentGuide.background\": \"#0D1117\",\n    \"editorInfo.foreground\": \"#1085FF\",\n    \"editorInlayHint.background\": \"#00000000\",\n    \"editorInlayHint.foreground\": \"#5F6672\",\n    \"editorLightBulb.foreground\": \"#E9D16C\",\n    \"editorLightBulbAutoFix.foreground\": \"#1085FF\",\n    \"editorLineNumber.activeForeground\": \"#C6CCD7\",\n    \"editorLineNumber.foreground\": \"#5F6672\",\n    \"editorOverviewRuler.addedForeground\": \"#98C379\",\n    \"editorOverviewRuler.border\": \"#0D1117\",\n    \"editorOverviewRuler.deletedForeground\": \"#E06C75\",\n    \"editorOverviewRuler.errorForeground\": \"#D74E42\",\n    \"editorOverviewRuler.findMatchForeground\": \"#1085FF\",\n    \"editorOverviewRuler.infoForeground\": \"#1085FF\",\n    \"editorOverviewRuler.modifiedForeground\": \"#D19A66\",\n    \"editorOverviewRuler.warningForeground\": \"#E9D16C\",\n    \"editorRuler.foreground\": \"#0D1117\",\n    \"editorStickyScroll.background\": \"#181A1F\",\n    \"editorStickyScrollHover.background\": \"#21252B\",\n    \"editorSuggestWidget.background\": \"#181A1F\",\n    \"editorSuggestWidget.border\": \"#1085FF\",\n    \"editorSuggestWidget.selectedBackground\": \"#A9B2C31A\",\n    \"editorWarning.foreground\": \"#E9D16C\",\n    \"editorWhitespace.foreground\": \"#A9B2C31A\",\n    \"editorWidget.background\": \"#181A1F\",\n    \"errorForeground\": \"#D74E42\",\n    \"focusBorder\": \"#1085FF\",\n    \"gitDecoration.deletedResourceForeground\": \"#E06C75\",\n    \"gitDecoration.ignoredResourceForeground\": \"#5F6672\",\n    \"gitDecoration.modifiedResourceForeground\": \"#D19A66\",\n    \"gitDecoration.untrackedResourceForeground\": \"#98C379\",\n    \"input.background\": \"#0D1117\",\n    \"inputOption.activeBorder\": \"#1085FF\",\n    \"inputValidation.errorBackground\": \"#D74E42\",\n    \"inputValidation.errorBorder\": \"#D74E42\",\n    \"inputValidation.infoBackground\": \"#1085FF\",\n    \"inputValidation.infoBorder\": \"#1085FF\",\n    \"inputValidation.infoForeground\": \"#0D1117\",\n    \"inputValidation.warningBackground\": \"#E9D16C\",\n    \"inputValidation.warningBorder\": \"#E9D16C\",\n    \"inputValidation.warningForeground\": \"#0D1117\",\n    \"list.activeSelectionBackground\": \"#A9B2C333\",\n    \"list.activeSelectionForeground\": \"#ffffff\",\n    \"list.errorForeground\": \"#D74E42\",\n    \"list.focusBackground\": \"#A9B2C333\",\n    \"list.hoverBackground\": \"#A9B2C31A\",\n    \"list.inactiveFocusOutline\": \"#5F6672\",\n    \"list.inactiveSelectionBackground\": \"#A9B2C333\",\n    \"list.inactiveSelectionForeground\": \"#C6CCD7\",\n    \"list.warningForeground\": \"#E9D16C\",\n    \"minimap.findMatchHighlight\": \"#1085FF\",\n    \"minimap.selectionHighlight\": \"#C6CCD7\",\n    \"minimapGutter.addedBackground\": \"#98C379\",\n    \"minimapGutter.deletedBackground\": \"#E06C75\",\n    \"minimapGutter.modifiedBackground\": \"#D19A66\",\n    \"notificationCenter.border\": \"#0D1117\",\n    \"notificationCenterHeader.background\": \"#181A1F\",\n    \"notificationToast.border\": \"#0D1117\",\n    \"notifications.background\": \"#181A1F\",\n    \"notifications.border\": \"#0D1117\",\n    \"panel.background\": \"#181A1F\",\n    \"panel.border\": \"#0D1117\",\n    \"panelTitle.inactiveForeground\": \"#5F6672\",\n    \"peekView.border\": \"#1085FF\",\n    \"peekViewEditor.background\": \"#181A1F\",\n    \"peekViewEditor.matchHighlightBackground\": \"#A9B2C333\",\n    \"peekViewResult.background\": \"#181A1F\",\n    \"peekViewResult.matchHighlightBackground\": \"#A9B2C333\",\n    \"peekViewResult.selectionBackground\": \"#A9B2C31A\",\n    \"peekViewResult.selectionForeground\": \"#C6CCD7\",\n    \"peekViewTitle.background\": \"#181A1F\",\n    \"sash.hoverBorder\": \"#A9B2C333\",\n    \"scrollbar.shadow\": \"#00000000\",\n    \"scrollbarSlider.activeBackground\": \"#A9B2C333\",\n    \"scrollbarSlider.background\": \"#A9B2C31A\",\n    \"scrollbarSlider.hoverBackground\": \"#A9B2C333\",\n    \"sideBar.background\": \"#181A1F\",\n    \"sideBar.border\": \"#0D1117\",\n    \"sideBar.foreground\": \"#C6CCD7\",\n    \"sideBarSectionHeader.background\": \"#21252B\",\n    \"statusBar.background\": \"#21252B\",\n    \"statusBar.border\": \"#0D1117\",\n    \"statusBar.debuggingBackground\": \"#21252B\",\n    \"statusBar.debuggingBorder\": \"#56B6C2\",\n    \"statusBar.debuggingForeground\": \"#A9B2C3\",\n    \"statusBar.focusBorder\": \"#A9B2C3\",\n    \"statusBar.foreground\": \"#A9B2C3\",\n    \"statusBar.noFolderBackground\": \"#181A1F\",\n    \"statusBarItem.activeBackground\": \"#0D1117\",\n    \"statusBarItem.errorBackground\": \"#21252B\",\n    \"statusBarItem.errorForeground\": \"#D74E42\",\n    \"statusBarItem.focusBorder\": \"#A9B2C3\",\n    \"statusBarItem.hoverBackground\": \"#181A1F\",\n    \"statusBarItem.hoverForeground\": \"#A9B2C3\",\n    \"statusBarItem.remoteBackground\": \"#21252B\",\n    \"statusBarItem.remoteForeground\": \"#B57EDC\",\n    \"statusBarItem.warningBackground\": \"#21252B\",\n    \"statusBarItem.warningForeground\": \"#E9D16C\",\n    \"tab.activeBackground\": \"#21252B\",\n    \"tab.activeBorderTop\": \"#1085FF\",\n    \"tab.activeForeground\": \"#C6CCD7\",\n    \"tab.border\": \"#0D1117\",\n    \"tab.inactiveBackground\": \"#181A1F\",\n    \"tab.inactiveForeground\": \"#5F6672\",\n    \"tab.lastPinnedBorder\": \"#A9B2C333\",\n    \"terminal.ansiBlack\": \"#5F6672\",\n    \"terminal.ansiBlue\": \"#61AFEF\",\n    \"terminal.ansiBrightBlack\": \"#5F6672\",\n    \"terminal.ansiBrightBlue\": \"#61AFEF\",\n    \"terminal.ansiBrightCyan\": \"#56B6C2\",\n    \"terminal.ansiBrightGreen\": \"#98C379\",\n    \"terminal.ansiBrightMagenta\": \"#B57EDC\",\n    \"terminal.ansiBrightRed\": \"#E06C75\",\n    \"terminal.ansiBrightWhite\": \"#A9B2C3\",\n    \"terminal.ansiBrightYellow\": \"#E5C07B\",\n    \"terminal.ansiCyan\": \"#56B6C2\",\n    \"terminal.ansiGreen\": \"#98C379\",\n    \"terminal.ansiMagenta\": \"#B57EDC\",\n    \"terminal.ansiRed\": \"#E06C75\",\n    \"terminal.ansiWhite\": \"#A9B2C3\",\n    \"terminal.ansiYellow\": \"#E5C07B\",\n    \"terminal.foreground\": \"#A9B2C3\",\n    \"titleBar.activeBackground\": \"#21252B\",\n    \"titleBar.activeForeground\": \"#C6CCD7\",\n    \"titleBar.border\": \"#0D1117\",\n    \"titleBar.inactiveBackground\": \"#21252B\",\n    \"titleBar.inactiveForeground\": \"#5F6672\",\n    \"toolbar.hoverBackground\": \"#A9B2C333\",\n    \"widget.shadow\": \"#00000000\"\n  },\n  \"displayName\": \"Plastic\",\n  \"name\": \"plastic\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {},\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"source.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5F6672\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"support.function\",\n        \"meta.diff.range\",\n        \"punctuation.definition.range.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B57EDC\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"punctuation.definition.keyword\",\n        \"variable.language\",\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\",\n        \"punctuation.definition.from-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E06C75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56B6C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"support.class\",\n        \"entity.name.namespace\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61AFEF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inline.raw.string\",\n        \"string\",\n        \"markup.inserted\",\n        \"punctuation.definition.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.to-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98C379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.section\",\n        \"entity.name.tag\",\n        \"entity.name.type\",\n        \"support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E5C07B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name\",\n        \"support.variable\",\n        \"variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C6CCD7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other\",\n        \"punctuation.definition.entity\",\n        \"support.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D19A66\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.brace\",\n        \"punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#A9B2C3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"punctuation.definition.bold\",\n        \"entity.other.attribute-name.id\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"markup.italic\",\n        \"punctuation.definition.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { plastic as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,yBAAyB;QACzB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,uBAAuB;QACvB,uBAAuB;QACvB,kBAAkB;QAClB,2BAA2B;QAC3B,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,+BAA+B;QAC/B,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,uCAAuC;QACvC,8BAA8B;QAC9B,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,0CAA0C;QAC1C,yCAAyC;QACzC,0BAA0B;QAC1B,iCAAiC;QACjC,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,oBAAoB;QACpB,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,6BAA6B;QAC7B,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,6BAA6B;QAC7B,uCAAuC;QACvC,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,oBAAoB;QACpB,gBAAgB;QAChB,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,wBAAwB;QACxB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB,CAAC;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}