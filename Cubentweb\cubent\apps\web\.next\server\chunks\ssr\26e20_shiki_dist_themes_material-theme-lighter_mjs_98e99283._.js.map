{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/material-theme-lighter.mjs"], "sourcesContent": ["var materialThemeLighter = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#80CBC4\",\n    \"activityBar.background\": \"#FAFAFA\",\n    \"activityBar.border\": \"#FAFAFA60\",\n    \"activityBar.dropBackground\": \"#E5393580\",\n    \"activityBar.foreground\": \"#90A4AE\",\n    \"activityBarBadge.background\": \"#80CBC4\",\n    \"activityBarBadge.foreground\": \"#000000\",\n    \"badge.background\": \"#CCD7DA30\",\n    \"badge.foreground\": \"#90A4AE\",\n    \"breadcrumb.activeSelectionForeground\": \"#80CBC4\",\n    \"breadcrumb.background\": \"#FAFAFA\",\n    \"breadcrumb.focusForeground\": \"#90A4AE\",\n    \"breadcrumb.foreground\": \"#758a95\",\n    \"breadcrumbPicker.background\": \"#FAFAFA\",\n    \"button.background\": \"#80CBC440\",\n    \"button.foreground\": \"#ffffff\",\n    \"debugConsole.errorForeground\": \"#E53935\",\n    \"debugConsole.infoForeground\": \"#39ADB5\",\n    \"debugConsole.warningForeground\": \"#E2931D\",\n    \"debugToolBar.background\": \"#FAFAFA\",\n    \"diffEditor.insertedTextBackground\": \"#39ADB520\",\n    \"diffEditor.removedTextBackground\": \"#FF537020\",\n    \"dropdown.background\": \"#FAFAFA\",\n    \"dropdown.border\": \"#00000010\",\n    \"editor.background\": \"#FAFAFA\",\n    \"editor.findMatchBackground\": \"#00000020\",\n    \"editor.findMatchBorder\": \"#80CBC4\",\n    \"editor.findMatchHighlight\": \"#90A4AE\",\n    \"editor.findMatchHighlightBackground\": \"#00000010\",\n    \"editor.findMatchHighlightBorder\": \"#00000030\",\n    \"editor.findRangeHighlightBackground\": \"#E2931D30\",\n    \"editor.foreground\": \"#90A4AE\",\n    \"editor.lineHighlightBackground\": \"#CCD7DA50\",\n    \"editor.lineHighlightBorder\": \"#CCD7DA00\",\n    \"editor.rangeHighlightBackground\": \"#FFFFFF0d\",\n    \"editor.selectionBackground\": \"#80CBC440\",\n    \"editor.selectionHighlightBackground\": \"#27272720\",\n    \"editor.wordHighlightBackground\": \"#FF537030\",\n    \"editor.wordHighlightStrongBackground\": \"#91B85930\",\n    \"editorBracketMatch.background\": \"#FAFAFA\",\n    \"editorBracketMatch.border\": \"#27272750\",\n    \"editorCursor.foreground\": \"#272727\",\n    \"editorError.foreground\": \"#E5393570\",\n    \"editorGroup.border\": \"#00000020\",\n    \"editorGroup.dropBackground\": \"#E5393580\",\n    \"editorGroup.focusedEmptyBorder\": \"#E53935\",\n    \"editorGroupHeader.tabsBackground\": \"#FAFAFA\",\n    \"editorGutter.addedBackground\": \"#91B85960\",\n    \"editorGutter.deletedBackground\": \"#E5393560\",\n    \"editorGutter.modifiedBackground\": \"#6182B860\",\n    \"editorHoverWidget.background\": \"#FAFAFA\",\n    \"editorHoverWidget.border\": \"#00000010\",\n    \"editorIndentGuide.activeBackground\": \"#B0BEC5\",\n    \"editorIndentGuide.background\": \"#B0BEC570\",\n    \"editorInfo.foreground\": \"#6182B870\",\n    \"editorLineNumber.activeForeground\": \"#758a95\",\n    \"editorLineNumber.foreground\": \"#CFD8DC\",\n    \"editorLink.activeForeground\": \"#90A4AE\",\n    \"editorMarkerNavigation.background\": \"#90A4AE05\",\n    \"editorOverviewRuler.border\": \"#FAFAFA\",\n    \"editorOverviewRuler.errorForeground\": \"#E5393540\",\n    \"editorOverviewRuler.findMatchForeground\": \"#80CBC4\",\n    \"editorOverviewRuler.infoForeground\": \"#6182B840\",\n    \"editorOverviewRuler.warningForeground\": \"#E2931D40\",\n    \"editorRuler.foreground\": \"#B0BEC5\",\n    \"editorSuggestWidget.background\": \"#FAFAFA\",\n    \"editorSuggestWidget.border\": \"#00000010\",\n    \"editorSuggestWidget.foreground\": \"#90A4AE\",\n    \"editorSuggestWidget.highlightForeground\": \"#80CBC4\",\n    \"editorSuggestWidget.selectedBackground\": \"#CCD7DA50\",\n    \"editorWarning.foreground\": \"#E2931D70\",\n    \"editorWhitespace.foreground\": \"#90A4AE40\",\n    \"editorWidget.background\": \"#FAFAFA\",\n    \"editorWidget.border\": \"#80CBC4\",\n    \"editorWidget.resizeBorder\": \"#80CBC4\",\n    \"extensionBadge.remoteForeground\": \"#90A4AE\",\n    \"extensionButton.prominentBackground\": \"#91B85990\",\n    \"extensionButton.prominentForeground\": \"#90A4AE\",\n    \"extensionButton.prominentHoverBackground\": \"#91B859\",\n    \"focusBorder\": \"#FFFFFF00\",\n    \"foreground\": \"#90A4AE\",\n    \"gitDecoration.conflictingResourceForeground\": \"#E2931D90\",\n    \"gitDecoration.deletedResourceForeground\": \"#E5393590\",\n    \"gitDecoration.ignoredResourceForeground\": \"#758a9590\",\n    \"gitDecoration.modifiedResourceForeground\": \"#6182B890\",\n    \"gitDecoration.untrackedResourceForeground\": \"#91B85990\",\n    \"input.background\": \"#EEEEEE\",\n    \"input.border\": \"#00000010\",\n    \"input.foreground\": \"#90A4AE\",\n    \"input.placeholderForeground\": \"#90A4AE60\",\n    \"inputOption.activeBackground\": \"#90A4AE30\",\n    \"inputOption.activeBorder\": \"#90A4AE30\",\n    \"inputValidation.errorBorder\": \"#E53935\",\n    \"inputValidation.infoBorder\": \"#6182B8\",\n    \"inputValidation.warningBorder\": \"#E2931D\",\n    \"list.activeSelectionBackground\": \"#FAFAFA\",\n    \"list.activeSelectionForeground\": \"#80CBC4\",\n    \"list.dropBackground\": \"#E5393580\",\n    \"list.focusBackground\": \"#90A4AE20\",\n    \"list.focusForeground\": \"#90A4AE\",\n    \"list.highlightForeground\": \"#80CBC4\",\n    \"list.hoverBackground\": \"#FAFAFA\",\n    \"list.hoverForeground\": \"#B1C7D3\",\n    \"list.inactiveSelectionBackground\": \"#CCD7DA50\",\n    \"list.inactiveSelectionForeground\": \"#80CBC4\",\n    \"listFilterWidget.background\": \"#CCD7DA50\",\n    \"listFilterWidget.noMatchesOutline\": \"#CCD7DA50\",\n    \"listFilterWidget.outline\": \"#CCD7DA50\",\n    \"menu.background\": \"#FAFAFA\",\n    \"menu.foreground\": \"#90A4AE\",\n    \"menu.selectionBackground\": \"#CCD7DA50\",\n    \"menu.selectionBorder\": \"#CCD7DA50\",\n    \"menu.selectionForeground\": \"#80CBC4\",\n    \"menu.separatorBackground\": \"#90A4AE\",\n    \"menubar.selectionBackground\": \"#CCD7DA50\",\n    \"menubar.selectionBorder\": \"#CCD7DA50\",\n    \"menubar.selectionForeground\": \"#80CBC4\",\n    \"notebook.focusedCellBorder\": \"#80CBC4\",\n    \"notebook.inactiveFocusedCellBorder\": \"#80CBC450\",\n    \"notificationLink.foreground\": \"#80CBC4\",\n    \"notifications.background\": \"#FAFAFA\",\n    \"notifications.foreground\": \"#90A4AE\",\n    \"panel.background\": \"#FAFAFA\",\n    \"panel.border\": \"#FAFAFA60\",\n    \"panel.dropBackground\": \"#90A4AE\",\n    \"panelTitle.activeBorder\": \"#80CBC4\",\n    \"panelTitle.activeForeground\": \"#000000\",\n    \"panelTitle.inactiveForeground\": \"#90A4AE\",\n    \"peekView.border\": \"#00000020\",\n    \"peekViewEditor.background\": \"#EEEEEE\",\n    \"peekViewEditor.matchHighlightBackground\": \"#80CBC440\",\n    \"peekViewEditorGutter.background\": \"#EEEEEE\",\n    \"peekViewResult.background\": \"#EEEEEE\",\n    \"peekViewResult.matchHighlightBackground\": \"#80CBC440\",\n    \"peekViewResult.selectionBackground\": \"#758a9570\",\n    \"peekViewTitle.background\": \"#EEEEEE\",\n    \"peekViewTitleDescription.foreground\": \"#90A4AE60\",\n    \"pickerGroup.border\": \"#FFFFFF1a\",\n    \"pickerGroup.foreground\": \"#80CBC4\",\n    \"progressBar.background\": \"#80CBC4\",\n    \"quickInput.background\": \"#FAFAFA\",\n    \"quickInput.foreground\": \"#758a95\",\n    \"quickInput.list.focusBackground\": \"#90A4AE20\",\n    \"sash.hoverBorder\": \"#80CBC450\",\n    \"scrollbar.shadow\": \"#00000020\",\n    \"scrollbarSlider.activeBackground\": \"#80CBC4\",\n    \"scrollbarSlider.background\": \"#90A4AE20\",\n    \"scrollbarSlider.hoverBackground\": \"#90A4AE10\",\n    \"selection.background\": \"#CCD7DA80\",\n    \"settings.checkboxBackground\": \"#FAFAFA\",\n    \"settings.checkboxForeground\": \"#90A4AE\",\n    \"settings.dropdownBackground\": \"#FAFAFA\",\n    \"settings.dropdownForeground\": \"#90A4AE\",\n    \"settings.headerForeground\": \"#80CBC4\",\n    \"settings.modifiedItemIndicator\": \"#80CBC4\",\n    \"settings.numberInputBackground\": \"#FAFAFA\",\n    \"settings.numberInputForeground\": \"#90A4AE\",\n    \"settings.textInputBackground\": \"#FAFAFA\",\n    \"settings.textInputForeground\": \"#90A4AE\",\n    \"sideBar.background\": \"#FAFAFA\",\n    \"sideBar.border\": \"#FAFAFA60\",\n    \"sideBar.foreground\": \"#758a95\",\n    \"sideBarSectionHeader.background\": \"#FAFAFA\",\n    \"sideBarSectionHeader.border\": \"#FAFAFA60\",\n    \"sideBarTitle.foreground\": \"#90A4AE\",\n    \"statusBar.background\": \"#FAFAFA\",\n    \"statusBar.border\": \"#FAFAFA60\",\n    \"statusBar.debuggingBackground\": \"#9C3EDA\",\n    \"statusBar.debuggingForeground\": \"#FFFFFF\",\n    \"statusBar.foreground\": \"#7E939E\",\n    \"statusBar.noFolderBackground\": \"#FAFAFA\",\n    \"statusBarItem.activeBackground\": \"#E5393580\",\n    \"statusBarItem.hoverBackground\": \"#90A4AE20\",\n    \"statusBarItem.remoteBackground\": \"#80CBC4\",\n    \"statusBarItem.remoteForeground\": \"#000000\",\n    \"tab.activeBackground\": \"#FAFAFA\",\n    \"tab.activeBorder\": \"#80CBC4\",\n    \"tab.activeForeground\": \"#000000\",\n    \"tab.activeModifiedBorder\": \"#758a95\",\n    \"tab.border\": \"#FAFAFA\",\n    \"tab.inactiveBackground\": \"#FAFAFA\",\n    \"tab.inactiveForeground\": \"#758a95\",\n    \"tab.inactiveModifiedBorder\": \"#89221f\",\n    \"tab.unfocusedActiveBorder\": \"#90A4AE\",\n    \"tab.unfocusedActiveForeground\": \"#90A4AE\",\n    \"tab.unfocusedActiveModifiedBorder\": \"#b72d2a\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#89221f\",\n    \"terminal.ansiBlack\": \"#000000\",\n    \"terminal.ansiBlue\": \"#6182B8\",\n    \"terminal.ansiBrightBlack\": \"#90A4AE\",\n    \"terminal.ansiBrightBlue\": \"#6182B8\",\n    \"terminal.ansiBrightCyan\": \"#39ADB5\",\n    \"terminal.ansiBrightGreen\": \"#91B859\",\n    \"terminal.ansiBrightMagenta\": \"#9C3EDA\",\n    \"terminal.ansiBrightRed\": \"#E53935\",\n    \"terminal.ansiBrightWhite\": \"#FFFFFF\",\n    \"terminal.ansiBrightYellow\": \"#E2931D\",\n    \"terminal.ansiCyan\": \"#39ADB5\",\n    \"terminal.ansiGreen\": \"#91B859\",\n    \"terminal.ansiMagenta\": \"#9C3EDA\",\n    \"terminal.ansiRed\": \"#E53935\",\n    \"terminal.ansiWhite\": \"#FFFFFF\",\n    \"terminal.ansiYellow\": \"#E2931D\",\n    \"terminalCursor.background\": \"#000000\",\n    \"terminalCursor.foreground\": \"#E2931D\",\n    \"textLink.activeForeground\": \"#90A4AE\",\n    \"textLink.foreground\": \"#80CBC4\",\n    \"titleBar.activeBackground\": \"#FAFAFA\",\n    \"titleBar.activeForeground\": \"#90A4AE\",\n    \"titleBar.border\": \"#FAFAFA60\",\n    \"titleBar.inactiveBackground\": \"#FAFAFA\",\n    \"titleBar.inactiveForeground\": \"#758a95\",\n    \"tree.indentGuidesStroke\": \"#B0BEC5\",\n    \"widget.shadow\": \"#00000020\"\n  },\n  \"displayName\": \"Material Theme Lighter\",\n  \"name\": \"material-theme-lighter\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#FAFAFA\",\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#91B859\"\n      }\n    },\n    {\n      \"scope\": \"punctuation, constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape, text.html constant.character.entity.named\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#FF5370\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#F76D47\"\n      }\n    },\n    {\n      \"scope\": \"variable, variable.parameter, support.variable, variable.language, support.constant, meta.definition.variable entity.name.function, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other\",\n      \"settings\": {\n        \"foreground\": \"#F76D47\"\n      }\n    },\n    {\n      \"scope\": \"keyword, modifier, variable.language.this, support.type.object, constant.language\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, support.function\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": \"storage.type, storage.modifier, storage.control\",\n      \"settings\": {\n        \"foreground\": \"#9C3EDA\"\n      }\n    },\n    {\n      \"scope\": \"support.module, support.node\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"support.type, constant.other.key\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type, entity.other.inherited-class, entity.other\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"comment punctuation.definition.comment, string.quoted.docstring\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name, entity.name.type.class, support.type, support.class, meta.use\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property, meta.field.declaration entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.method entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"meta.function entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": \"template.expression.begin, template.expression.end, punctuation.definition.template-expression.begin, punctuation.definition.template-expression.end\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded, source.groovy.embedded, meta.template.expression\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.yaml\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"meta.object-literal.key, meta.object-literal.key string, support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.json\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#F76D47\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#8796B0\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag, punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#9C3EDA\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.html\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown meta.link.inline, meta.link.reference\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown beginning.punctuation.definition.list\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold markup.italic, markup.italic markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"italic bold\",\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"markup.fenced_code.block.markdown punctuation.definition.markdown\",\n      \"settings\": {\n        \"foreground\": \"#91B859\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#91B859\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.definition.ini\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.group-title.ini\",\n      \"settings\": {\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.class.identifier storage.type\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.identifier entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method-call meta.method, source.cs entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": \"source.cs storage.type\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.return-type\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"source.cs entity.name.type.namespace\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"meta.jsx.children, SXNested\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"support.class.component\",\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": \"source.cpp meta.block variable.other\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.member.access.python\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.function-call.python, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": \"meta.block\",\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.call\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": \"source.php support.other.namespace, source.php meta.use support.class\",\n      \"settings\": {\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"settings\": {\n        \"background\": \"#FAFAFA\",\n        \"foreground\": \"#90A4AE\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B859\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.format.placeholder.other.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F76D47\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#39ADB5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#90A4AE90\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5370\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9C3EDA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E2931D\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F76D47\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E53935\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#916b53\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6182B8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5370\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9C3EDA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B859\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { materialThemeLighter as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,uBAAuB,OAAO,MAAM,CAAC;IACvC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,6BAA6B;QAC7B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,kCAAkC;QAClC,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,8BAA8B;QAC9B,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,yCAAyC;QACzC,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,6BAA6B;QAC7B,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,+BAA+B;QAC/B,8BAA8B;QAC9B,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,8BAA8B;QAC9B,sCAAsC;QACtC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,oBAAoB;QACpB,gBAAgB;QAChB,wBAAwB;QACxB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,mCAAmC;QACnC,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,uCAAuC;QACvC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}