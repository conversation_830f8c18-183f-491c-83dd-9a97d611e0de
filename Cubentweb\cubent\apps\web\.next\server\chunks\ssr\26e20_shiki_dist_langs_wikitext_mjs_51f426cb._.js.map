{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/wikitext.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport css from './css.mjs';\nimport ini from './ini.mjs';\nimport java from './java.mjs';\nimport lua from './lua.mjs';\nimport make from './make.mjs';\nimport perl from './perl.mjs';\nimport r from './r.mjs';\nimport ruby from './ruby.mjs';\nimport php from './php.mjs';\nimport sql from './sql.mjs';\nimport vb from './vb.mjs';\nimport xml from './xml.mjs';\nimport xsl from './xsl.mjs';\nimport yaml from './yaml.mjs';\nimport bat from './bat.mjs';\nimport clojure from './clojure.mjs';\nimport coffee from './coffee.mjs';\nimport c from './c.mjs';\nimport cpp from './cpp.mjs';\nimport diff from './diff.mjs';\nimport docker from './docker.mjs';\nimport go from './go.mjs';\nimport groovy from './groovy.mjs';\nimport pug from './pug.mjs';\nimport javascript from './javascript.mjs';\nimport jsonc from './jsonc.mjs';\nimport less from './less.mjs';\nimport objective_c from './objective-c.mjs';\nimport swift from './swift.mjs';\nimport scss from './scss.mjs';\nimport raku from './raku.mjs';\nimport powershell from './powershell.mjs';\nimport python from './python.mjs';\nimport julia from './julia.mjs';\nimport rust from './rust.mjs';\nimport scala from './scala.mjs';\nimport shellscript from './shellscript.mjs';\nimport typescript from './typescript.mjs';\nimport csharp from './csharp.mjs';\nimport fsharp from './fsharp.mjs';\nimport dart from './dart.mjs';\nimport handlebars from './handlebars.mjs';\nimport markdown from './markdown.mjs';\nimport erlang from './erlang.mjs';\nimport elixir from './elixir.mjs';\nimport latex from './latex.mjs';\nimport bibtex from './bibtex.mjs';\nimport json from './json.mjs';\nimport './cpp-macro.mjs';\nimport './regexp.mjs';\nimport './glsl.mjs';\nimport './sass.mjs';\nimport './stylus.mjs';\nimport './tex.mjs';\nimport './gnuplot.mjs';\nimport './haskell.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Wikitext\", \"name\": \"wikitext\", \"patterns\": [{ \"include\": \"#wikitext\" }, { \"include\": \"text.html.basic\" }], \"repository\": { \"wikitext\": { \"patterns\": [{ \"include\": \"#signature\" }, { \"include\": \"#redirect\" }, { \"include\": \"#magic-words\" }, { \"include\": \"#argument\" }, { \"include\": \"#template\" }, { \"include\": \"#convert\" }, { \"include\": \"#list\" }, { \"include\": \"#table\" }, { \"include\": \"#font-style\" }, { \"include\": \"#internal-link\" }, { \"include\": \"#external-link\" }, { \"include\": \"#heading\" }, { \"include\": \"#break\" }, { \"include\": \"#wikixml\" }, { \"include\": \"#extension-comments\" }], \"repository\": { \"argument\": { \"begin\": \"({{{)\", \"end\": \"(}}})\", \"name\": \"variable.parameter.wikitext\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.wikitext\" }, \"2\": { \"name\": \"keyword.operator.wikitext\" } }, \"match\": \"(?:^|\\\\G)([^#:\\\\|\\\\[\\\\]{}\\\\|]*)(\\\\|)\" }, { \"include\": \"$self\" }] }, \"break\": { \"match\": \"^-{4,}\", \"name\": \"markup.changed.wikitext\" }, \"convert\": { \"begin\": \"(-\\\\{(?!\\\\{))([a-zA-Z](\\\\|))?\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.template.wikitext\" }, \"2\": { \"name\": \"entity.name.function.type.wikitext\" }, \"3\": { \"name\": \"keyword.operator.wikitext\" } }, \"end\": \"(\\\\}-)\", \"patterns\": [{ \"include\": \"$self\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.tag.language.wikitext\" }, \"2\": { \"name\": \"punctuation.separator.key-value.wikitext\" }, \"3\": { \"name\": \"string.unquoted.text.wikitext\", \"patterns\": [{ \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.terminator.rule.wikitext\" } }, \"match\": \"(?:([a-zA-Z\\\\-]*)(:))?(.*?)(?:(;)|(?=\\\\}-))\" }] }, \"extension-comments\": { \"begin\": \"(<%--)\\\\s*(\\\\[)([A-Z_]*)(\\\\])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.extension.wikitext\" }, \"2\": { \"name\": \"punctuation.definition.tag.extension.wikitext\" }, \"3\": { \"name\": \"storage.type.extension.wikitext\" }, \"4\": { \"name\": \"punctuation.definition.tag.extension.wikitext\" } }, \"end\": \"(\\\\[)([A-Z_]*)(\\\\])\\\\s*(--%>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.extension.wikitext\" }, \"2\": { \"name\": \"storage.type.extension.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.extension.wikitext\" }, \"4\": { \"name\": \"punctuation.definition.comment.extension.wikitext\" } }, \"name\": \"comment.block.documentation.special.extension.wikitext\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"meta.object.member.extension.wikitext\" }, \"1\": { \"name\": \"meta.object-literal.key.extension.wikitext\" }, \"2\": { \"name\": \"punctuation.separator.dictionary.key-value.extension.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.extension.wikitext\" }, \"4\": { \"name\": \"string.quoted.other.extension.wikitext\" }, \"5\": { \"name\": \"punctuation.definition.string.end.extension.wikitext\" } }, \"match\": \"(\\\\w*)\\\\s*(=)\\\\s*(#)(.*?)(#)\" }] }, \"external-link\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.link.external.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.url.wikitext\" }, \"3\": { \"name\": \"string.other.link.external.title.wikitext\", \"patterns\": [{ \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.link.external.wikitext\" } }, \"match\": \"(\\\\[)((?:(?:(?:http(?:s)?)|(?:ftp(?:s)?)):\\\\/\\\\/)[\\\\w.-]+(?:\\\\.[\\\\w\\\\.-]+)+[\\\\w\\\\-\\\\.~:\\\\/?#%@!$&'()\\\\*+,;=.]+)\\\\s*?([^\\\\]]*)(\\\\])\", \"name\": \"meta.link.external.wikitext\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.link.external.wikitext\" }, \"2\": { \"name\": \"invalid.illegal.bad-url.wikitext\" }, \"3\": { \"name\": \"string.other.link.external.title.wikitext\", \"patterns\": [{ \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.link.external.wikitext\" } }, \"match\": \"(\\\\[)([\\\\w.-]+(?:\\\\.[\\\\w\\\\.-]+)+[\\\\w\\\\-\\\\.~:\\\\/?#%@!$&'()\\\\*+,;=.]+)\\\\s*?([^\\\\]]*)(\\\\])\", \"name\": \"invalid.illegal.bad-link.wikitext\" }] }, \"font-style\": { \"patterns\": [{ \"include\": \"#bold\" }, { \"include\": \"#italic\" }], \"repository\": { \"bold\": { \"begin\": \"(''')\", \"end\": \"(''')|$\", \"name\": \"markup.bold.wikitext\", \"patterns\": [{ \"include\": \"#italic\" }, { \"include\": \"$self\" }] }, \"italic\": { \"begin\": \"('')\", \"end\": \"((?=[^'])|(?=''))''((?=[^'])|(?=''))|$\", \"name\": \"markup.italic.wikitext\", \"patterns\": [{ \"include\": \"#bold\" }, { \"include\": \"$self\" }] } } }, \"heading\": { \"captures\": { \"2\": { \"name\": \"string.quoted.other.heading.wikitext\", \"patterns\": [{ \"include\": \"$self\" }] } }, \"match\": \"^(={1,6})\\\\s*(.+?)\\\\s*(\\\\1)$\", \"name\": \"markup.heading.wikitext\" }, \"internal-link\": { \"TODO\": \"SINGLE LINE\", \"begin\": \"(\\\\[\\\\[)(([^#:\\\\|\\\\[\\\\]{}]*:)*)?([^\\\\|\\\\[\\\\]]*)?\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.link.internal.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.namespace.wikitext\" }, \"4\": { \"name\": \"entity.other.attribute-name.wikitext\" } }, \"end\": \"(\\\\]\\\\])\", \"name\": \"string.quoted.internal-link.wikitext\", \"patterns\": [{ \"include\": \"$self\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.wikitext\" }, \"5\": { \"name\": \"entity.other.attribute-name.localname.wikitext\" } }, \"match\": \"(\\\\|)|(?:\\\\s*)(?:([-\\\\w.]+)((:)))?([-\\\\w.:]+)\\\\s*(=)\" }] }, \"list\": { \"name\": \"markup.list.wikitext\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.list.begin.markdown.wikitext\" } }, \"match\": \"^([#*;:]+)\" }] }, \"magic-words\": { \"patterns\": [{ \"include\": \"#behavior-switches\" }, { \"include\": \"#outdated-behavior-switches\" }, { \"include\": \"#variables\" }], \"repository\": { \"behavior-switches\": { \"match\": \"(?i)(__)(NOTOC|FORCETOC|TOC|NOEDITSECTION|NEWSECTIONLINK|NOGALLERY|HIDDENCAT|EXPECTUNUSEDCATEGORY|NOCONTENTCONVERT|NOCC|NOTITLECONVERT|NOTC|INDEX|NOINDEX|STATICREDIRECT|NOGLOBAL|DISAMBIG)(__)\", \"name\": \"constant.language.behavior-switcher.wikitext\" }, \"outdated-behavior-switches\": { \"match\": \"(?i)(__)(START|END)(__)\", \"name\": \"invalid.deprecated.behavior-switcher.wikitext\" }, \"variables\": { \"patterns\": [{ \"match\": \"(?i)(\\\\{\\\\{)(CURRENTYEAR|CURRENTMONTH|CURRENTMONTH1|CURRENTMONTHNAME|CURRENTMONTHNAMEGEN|CURRENTMONTHABBREV|CURRENTDAY|CURRENTDAY2|CURRENTDOW|CURRENTDAYNAME|CURRENTTIME|CURRENTHOUR|CURRENTWEEK|CURRENTTIMESTAMP|LOCALYEAR|LOCALMONTH|LOCALMONTH1|LOCALMONTHNAME|LOCALMONTHNAMEGEN|LOCALMONTHABBREV|LOCALDAY|LOCALDAY2|LOCALDOW|LOCALDAYNAME|LOCALTIME|LOCALHOUR|LOCALWEEK|LOCALTIMESTAMP)(\\\\}\\\\})\", \"name\": \"constant.language.variables.time.wikitext\" }, { \"match\": \"(?i)(\\\\{\\\\{)(SITENAME|SERVER|SERVERNAME|DIRMARK|DIRECTIONMARK|SCRIPTPATH|STYLEPATH|CURRENTVERSION|CONTENTLANGUAGE|CONTENTLANG|PAGEID|PAGELANGUAGE|CASCADINGSOURCES|REVISIONID|REVISIONDAY|REVISIONDAY2|REVISIONMONTH|REVISIONMONTH1|REVISIONYEAR|REVISIONTIMESTAMP|REVISIONUSER|REVISIONSIZE)(\\\\}\\\\})\", \"name\": \"constant.language.variables.metadata.wikitext\" }, { \"match\": \"ISBN\\\\s+((9[\\\\-\\\\s]?7[\\\\-\\\\s]?[89][\\\\-\\\\s]?)?(\\\\d[\\\\-\\\\s]?){10})\", \"name\": \"constant.language.variables.isbn.wikitext\" }, { \"match\": \"RFC\\\\s+\\\\d+\", \"name\": \"constant.language.variables.rfc.wikitext\" }, { \"match\": \"PMID\\\\s+\\\\d+\", \"name\": \"constant.language.variables.pmid.wikitext\" }] } } }, \"redirect\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.redirect.wikitext\" }, \"2\": { \"name\": \"punctuation.definition.tag.link.internal.begin.wikitext\" }, \"3\": { \"name\": \"entity.name.tag.namespace.wikitext\" }, \"4\": null, \"5\": { \"name\": \"entity.other.attribute-name.wikitext\" }, \"6\": { \"name\": \"invalid.deprecated.ineffective.wikitext\" }, \"7\": { \"name\": \"punctuation.definition.tag.link.internal.end.wikitext\" } }, \"match\": \"(?i)(^\\\\s*?#REDIRECT)\\\\s*(\\\\[\\\\[)(([^#:\\\\|\\\\[\\\\]{}]*?:)*)?([^\\\\|\\\\[\\\\]]*)?(\\\\|[^\\\\[\\\\]]*?)?(\\\\]\\\\])\" }] }, \"signature\": { \"patterns\": [{ \"match\": \"~{3,5}\", \"name\": \"keyword.other.signature.wikitext\" }] }, \"table\": { \"patterns\": [{ \"begin\": \"^\\\\s*(\\\\{\\\\|)(.*)$\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.table.wikitext\" }, \"2\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }] } }, \"end\": \"^\\\\s*(\\\\|\\\\})\", \"name\": \"meta.tag.block.table.wikitext\", \"patterns\": [{ \"include\": \"$self\" }, { \"begin\": \"^\\\\s*(\\\\|-)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" } }, \"end\": \"$\", \"name\": \"meta.tag.block.table-row.wikitext\", \"patterns\": [{ \"include\": \"$self\" }, { \"include\": \"text.html.basic#attribute\" }, { \"match\": \"\\\\|.*\", \"name\": \"invalid.illegal.bad-table-context.wikitext\" }] }, { \"begin\": \"^\\\\s*(!)(([^\\\\[]*?)(\\\\|))?(.*?)(?=(!!)|$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": null, \"3\": { \"patterns\": [{ \"include\": \"$self\" }, { \"include\": \"text.html.basic#attribute\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.wikitext\" }, \"5\": { \"name\": \"markup.bold.style.wikitext\" } }, \"end\": \"$\", \"name\": \"meta.tag.block.th.heading\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.wikitext\" }, \"5\": { \"name\": \"markup.bold.style.wikitext\" } }, \"match\": \"(!!)(([^\\\\[]*?)(\\\\|))?(.*?)(?=(!!)|$)\", \"name\": \"meta.tag.block.th.inline.wikitext\" }, { \"include\": \"$self\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"string.unquoted.caption.wikitext\" } }, \"end\": \"$\", \"match\": \"^\\\\s*(\\\\|\\\\+)(.*?)$\", \"name\": \"meta.tag.block.caption.wikitext\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"^\\\\s*(\\\\|)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.wikitext\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"$self\" }, { \"match\": \"\\\\|\\\\|\", \"name\": \"keyword.operator.wikitext\" }] }] }] }, \"template\": { \"begin\": \"(\\\\{\\\\{)\\\\s*(([^#:\\\\|\\\\[\\\\]{}]*(:))*)\\\\s*((#[^#:\\\\|\\\\[\\\\]{}]+(:))*)([^#:\\\\|\\\\[\\\\]{}]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.template.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.local-name.wikitext\" }, \"4\": { \"name\": \"punctuation.separator.namespace.wikitext\" }, \"5\": { \"name\": \"entity.name.function.wikitext\" }, \"7\": { \"name\": \"punctuation.separator.namespace.wikitext\" }, \"8\": { \"name\": \"entity.name.tag.local-name.wikitext\" } }, \"end\": \"(\\\\}\\\\})\", \"patterns\": [{ \"include\": \"$self\" }, { \"match\": \"(\\\\|)\", \"name\": \"keyword.operator.wikitext\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.wikitext\" }, \"2\": { \"name\": \"punctuation.separator.namespace.wikitext\" }, \"3\": { \"name\": \"entity.other.attribute-name.local-name.wikitext\" }, \"4\": { \"name\": \"keyword.operator.equal.wikitext\" } }, \"match\": \"(?<=\\\\|)\\\\s*(?:([-\\\\w.]+)(:))?([-\\\\w\\\\s\\\\.:]+)\\\\s*(=)\" }] }, \"wikixml\": { \"patterns\": [{ \"include\": \"#wiki-self-closed-tags\" }, { \"include\": \"#normal-wiki-tags\" }, { \"include\": \"#nowiki\" }, { \"include\": \"#ref\" }, { \"include\": \"#jsonin\" }, { \"include\": \"#math\" }, { \"include\": \"#syntax-highlight\" }], \"repository\": { \"jsonin\": { \"begin\": \"(?i)(<)(graph|templatedata)(\\\\s+[^>]+)?\\\\s*(>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"contentName\": \"meta.embedded.block.json\", \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"include\": \"source.json\" }] }, \"math\": { \"begin\": \"(?i)(<)(math|chem|ce)(\\\\s+[^>]+)?\\\\s*(>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"contentName\": \"meta.embedded.block.latex\", \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"include\": \"text.html.markdown.math#math\" }] }, \"normal-wiki-tags\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"match\": \"(?i)(</?)(includeonly|onlyinclude|noinclude)(\\\\s+[^>]+)?\\\\s*(>)\", \"name\": \"meta.tag.metedata.normal.wikitext\" }, \"nowiki\": { \"begin\": \"(?i)(<)(nowiki)(\\\\s+[^>]+)?\\\\s*(>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.nowiki.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"contentName\": \"meta.embedded.block.plaintext\", \"end\": \"(?i)(</)(nowiki)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.nowiki.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } } }, \"ref\": { \"begin\": \"(?i)(<)(ref)(\\\\s+[^>]+)?\\\\s*(>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.ref.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"contentName\": \"meta.block.ref.wikitext\", \"end\": \"(?i)(</)(ref)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.ref.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, \"syntax-highlight\": { \"patterns\": [{ \"include\": \"#hl-css\" }, { \"include\": \"#hl-html\" }, { \"include\": \"#hl-ini\" }, { \"include\": \"#hl-java\" }, { \"include\": \"#hl-lua\" }, { \"include\": \"#hl-makefile\" }, { \"include\": \"#hl-perl\" }, { \"include\": \"#hl-r\" }, { \"include\": \"#hl-ruby\" }, { \"include\": \"#hl-php\" }, { \"include\": \"#hl-sql\" }, { \"include\": \"#hl-vb-net\" }, { \"include\": \"#hl-xml\" }, { \"include\": \"#hl-xslt\" }, { \"include\": \"#hl-yaml\" }, { \"include\": \"#hl-bat\" }, { \"include\": \"#hl-clojure\" }, { \"include\": \"#hl-coffee\" }, { \"include\": \"#hl-c\" }, { \"include\": \"#hl-cpp\" }, { \"include\": \"#hl-diff\" }, { \"include\": \"#hl-dockerfile\" }, { \"include\": \"#hl-go\" }, { \"include\": \"#hl-groovy\" }, { \"include\": \"#hl-pug\" }, { \"include\": \"#hl-js\" }, { \"include\": \"#hl-json\" }, { \"include\": \"#hl-less\" }, { \"include\": \"#hl-objc\" }, { \"include\": \"#hl-swift\" }, { \"include\": \"#hl-scss\" }, { \"include\": \"#hl-perl6\" }, { \"include\": \"#hl-powershell\" }, { \"include\": \"#hl-python\" }, { \"include\": \"#hl-julia\" }, { \"include\": \"#hl-rust\" }, { \"include\": \"#hl-scala\" }, { \"include\": \"#hl-shell\" }, { \"include\": \"#hl-ts\" }, { \"include\": \"#hl-csharp\" }, { \"include\": \"#hl-fsharp\" }, { \"include\": \"#hl-dart\" }, { \"include\": \"#hl-handlebars\" }, { \"include\": \"#hl-markdown\" }, { \"include\": \"#hl-erlang\" }, { \"include\": \"#hl-elixir\" }, { \"include\": \"#hl-latex\" }, { \"include\": \"#hl-bibtex\" }], \"repository\": { \"hl-bat\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(['\"]?)(?:batch|bat|dosbatch|winbatch)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.bat\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.batchfile\" }] }] }, \"hl-bibtex\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:bibtex|bib)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.bibtex\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.bibtex\" }] }] }, \"hl-c\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)c\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.c\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.c\" }] }] }, \"hl-clojure\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:clojure|clj)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.clojure\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.clojure\" }] }] }, \"hl-coffee\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:coffeescript|coffee-script|coffee)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.coffee\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.coffee\" }] }] }, \"hl-cpp\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:cpp|c\\\\+\\\\+)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.cpp\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.cpp\" }] }] }, \"hl-csharp\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:csharp|c#|cs)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.csharp\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.cs\" }] }] }, \"hl-css\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)css\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.css\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, \"hl-dart\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)dart\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.dart\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.dart\" }] }] }, \"hl-diff\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:diff|udiff)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.diff\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.diff\" }] }] }, \"hl-dockerfile\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:docker|dockerfile)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.dockerfile\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.dockerfile\" }] }] }, \"hl-elixir\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:elixir|ex|exs)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.elixir\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.elixir\" }] }] }, \"hl-erlang\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)erlang\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.erlang\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.erlang\" }] }] }, \"hl-fsharp\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:fsharp|f#)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.fsharp\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.fsharp\" }] }] }, \"hl-go\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:go|golang)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.go\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.go\" }] }] }, \"hl-groovy\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)groovy\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.groovy\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.groovy\" }] }] }, \"hl-handlebars\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)handlebars\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.handlebars\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.html.handlebars\" }] }] }, \"hl-html\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)html\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.html\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.html.basic\" }] }] }, \"hl-ini\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:ini|cfg|dosini)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.ini\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.ini\" }] }] }, \"hl-java\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)java\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.java\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.java\" }] }] }, \"hl-js\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:javascript|js)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.js\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"hl-json\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"json\"|'json'|\"json-object\"|'json-object'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.json\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.json.comments\" }] }] }, \"hl-julia\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"julia\"|'julia'|\"jl\"|'jl'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.julia\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.julia\" }] }] }, \"hl-latex\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:tex|latex)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.latex\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.tex.latex\" }] }] }, \"hl-less\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"less\"|'less'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.less\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.css.less\" }] }] }, \"hl-lua\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)lua\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.lua\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.lua\" }] }] }, \"hl-makefile\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:make|makefile|mf|bsdmake)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.makefile\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.makefile\" }] }] }, \"hl-markdown\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:markdown|md)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.markdown\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.html.markdown\" }] }] }, \"hl-objc\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"objective-c\"|'objective-c'|\"objectivec\"|'objectivec'|\"obj-c\"|'obj-c'|\"objc\"|'objc'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.objc\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.objc\" }] }] }, \"hl-perl\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:perl|ple)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.perl\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.perl\" }] }] }, \"hl-perl6\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"perl6\"|'perl6'|\"pl6\"|'pl6'|\"raku\"|'raku'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.perl6\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.perl.6\" }] }] }, \"hl-php\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:php|php3|php4|php5)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.php\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.php\" }] }] }, \"hl-powershell\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"powershell\"|'powershell'|\"pwsh\"|'pwsh'|\"posh\"|'posh'|\"ps1\"|'ps1'|\"psm1\"|'psm1'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.powershell\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.powershell\" }] }] }, \"hl-pug\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:pug|jade)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.pug\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.pug\" }] }] }, \"hl-python\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"python\"|'python'|\"py\"|'py'|\"sage\"|'sage'|\"python3\"|'python3'|\"py3\"|'py3'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.python\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.python\" }] }] }, \"hl-r\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:splus|s|r)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.r\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.r\" }] }] }, \"hl-ruby\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:ruby|rb|duby)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.ruby\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.ruby\" }] }] }, \"hl-rust\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"rust\"|'rust'|\"rs\"|'rs'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": null, \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.rust\" }] }] }, \"hl-scala\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"scala\"|'scala'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.scala\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.scala\" }] }] }, \"hl-scss\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"scss\"|'scss'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.scss\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.css.scss\" }] }] }, \"hl-shell\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"bash\"|'bash'|\"sh\"|'sh'|\"ksh\"|'ksh'|\"zsh\"|'zsh'|\"shell\"|'shell'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.shell\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.shell\" }] }] }, \"hl-sql\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)sql\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.sql\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.sql\" }] }] }, \"hl-swift\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"swift\"|'swift'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.swift\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.swift\" }] }] }, \"hl-ts\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:\"typescript\"|'typescript'|\"ts\"|'ts'))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.ts\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, \"hl-vb-net\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)(?:vb\\\\.net|vbnet|lobas|oobas|sobas)\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.vb-net\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.asp.vb.net\" }] }] }, \"hl-xml\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)xml\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.xml\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.xml\" }] }] }, \"hl-xslt\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)xslt\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.xslt\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"text.xml.xsl\" }] }] }, \"hl-yaml\": { \"begin\": `(?i)(<)(syntaxhighlight)((?:\\\\s+[^>]+)?(?:\\\\s+lang=(?:(['\"]?)yaml\\\\4))(?:\\\\s+[^>]+)?)\\\\s*(>)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.start.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"end\": \"(?i)(</)(syntaxhighlight)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.end.wikitext\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"patterns\": [{ \"begin\": \"(^|\\\\G)\", \"contentName\": \"meta.embedded.block.yaml\", \"end\": \"(?i)(?=</syntaxhighlight\\\\s*>)\", \"patterns\": [{ \"include\": \"source.yaml\" }] }] } } }, \"wiki-self-closed-tags\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.wikitext\" }, \"2\": { \"name\": \"entity.name.tag.wikitext\" }, \"3\": { \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }, { \"include\": \"$self\" }] }, \"4\": { \"name\": \"punctuation.definition.tag.end.wikitext\" } }, \"match\": \"(?i)(<)(templatestyles|ref|nowiki|onlyinclude|includeonly)(\\\\s+[^>]+)?\\\\s*(/>)\", \"name\": \"meta.tag.metedata.void.wikitext\" } } } } } }, \"scopeName\": \"source.wikitext\", \"embeddedLangs\": [\"html\", \"css\", \"ini\", \"java\", \"lua\", \"make\", \"perl\", \"r\", \"ruby\", \"php\", \"sql\", \"vb\", \"xml\", \"xsl\", \"yaml\", \"bat\", \"clojure\", \"coffee\", \"c\", \"cpp\", \"diff\", \"docker\", \"go\", \"groovy\", \"pug\", \"javascript\", \"jsonc\", \"less\", \"objective-c\", \"swift\", \"scss\", \"raku\", \"powershell\", \"python\", \"julia\", \"rust\", \"scala\", \"shellscript\", \"typescript\", \"csharp\", \"fsharp\", \"dart\", \"handlebars\", \"markdown\", \"erlang\", \"elixir\", \"latex\", \"bibtex\", \"json\"], \"aliases\": [\"mediawiki\", \"wiki\"] });\nvar wikitext = [\n  ...html,\n  ...css,\n  ...ini,\n  ...java,\n  ...lua,\n  ...make,\n  ...perl,\n  ...r,\n  ...ruby,\n  ...php,\n  ...sql,\n  ...vb,\n  ...xml,\n  ...xsl,\n  ...yaml,\n  ...bat,\n  ...clojure,\n  ...coffee,\n  ...c,\n  ...cpp,\n  ...diff,\n  ...docker,\n  ...go,\n  ...groovy,\n  ...pug,\n  ...javascript,\n  ...jsonc,\n  ...less,\n  ...objective_c,\n  ...swift,\n  ...scss,\n  ...raku,\n  ...powershell,\n  ...python,\n  ...julia,\n  ...rust,\n  ...scala,\n  ...shellscript,\n  ...typescript,\n  ...csharp,\n  ...fsharp,\n  ...dart,\n  ...handlebars,\n  ...markdown,\n  ...erlang,\n  ...elixir,\n  ...latex,\n  ...bibtex,\n  ...json,\n  lang\n];\n\nexport { wikitext as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,QAAQ;IAAY,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAkB;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;YAAE,cAAc;gBAAE,YAAY;oBAAE,SAAS;oBAAS,OAAO;oBAAS,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,SAAS;wBAAuC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,SAAS;oBAAE,SAAS;oBAAU,QAAQ;gBAA0B;gBAAG,WAAW;oBAAE,SAAS;oBAAiC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAU,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;oCAAiC,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,SAAS;wBAA8C;qBAAE;gBAAC;gBAAG,sBAAsB;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAiC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,QAAQ;oBAA0D,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAAgE;gCAAG,KAAK;oCAAE,QAAQ;gCAAyD;gCAAG,KAAK;oCAAE,QAAQ;gCAAyC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,SAAS;wBAA+B;qBAAE;gBAAC;gBAAG,iBAAiB;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoD;gCAAG,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,QAAQ;oCAA6C,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,SAAS;4BAAsI,QAAQ;wBAA8B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoD;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;oCAA6C,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,SAAS;4BAA2F,QAAQ;wBAAoC;qBAAE;gBAAC;gBAAG,cAAc;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;oBAAE,cAAc;wBAAE,QAAQ;4BAAE,SAAS;4BAAS,OAAO;4BAAW,QAAQ;4BAAwB,YAAY;gCAAC;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG,UAAU;4BAAE,SAAS;4BAAQ,OAAO;4BAA0C,QAAQ;4BAA0B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;oBAAE;gBAAE;gBAAG,WAAW;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;4BAAwC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAgC,QAAQ;gBAA0B;gBAAG,iBAAiB;oBAAE,QAAQ;oBAAe,SAAS;oBAAoD,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAiD;4BAAE;4BAAG,SAAS;wBAAuD;qBAAE;gBAAC;gBAAG,QAAQ;oBAAE,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsD;4BAAE;4BAAG,SAAS;wBAAa;qBAAE;gBAAC;gBAAG,eAAe;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAA8B;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;oBAAE,cAAc;wBAAE,qBAAqB;4BAAE,SAAS;4BAAmM,QAAQ;wBAA+C;wBAAG,8BAA8B;4BAAE,SAAS;4BAA2B,QAAQ;wBAAgD;wBAAG,aAAa;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAuY,QAAQ;gCAA4C;gCAAG;oCAAE,SAAS;oCAAyS,QAAQ;gCAAgD;gCAAG;oCAAE,SAAS;oCAAoE,QAAQ;gCAA4C;gCAAG;oCAAE,SAAS;oCAAe,QAAQ;gCAA2C;gCAAG;oCAAE,SAAS;oCAAgB,QAAQ;gCAA4C;6BAAE;wBAAC;oBAAE;gBAAE;gBAAG,YAAY;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0D;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;gCAAM,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;gCAAG,KAAK;oCAAE,QAAQ;gCAAwD;4BAAE;4BAAG,SAAS;wBAAsG;qBAAE;gBAAC;gBAAG,aAAa;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAU,QAAQ;wBAAmC;qBAAE;gBAAC;gBAAG,SAAS;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAiB,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;gCAAG;oCAAE,SAAS;oCAAmB,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA4C;oCAAE;oCAAG,OAAO;oCAAK,QAAQ;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;wCAAG;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,SAAS;4CAAS,QAAQ;wCAA6C;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAA6C,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;wCAAM,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;gDAAG;oDAAE,WAAW;gDAA4B;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAsC;wCAAG,KAAK;4CAAE,QAAQ;wCAA6B;oCAAE;oCAAG,OAAO;oCAAK,QAAQ;oCAA6B,YAAY;wCAAC;4CAAE,YAAY;gDAAE,KAAK;oDAAE,QAAQ;gDAA4C;gDAAG,KAAK;oDAAE,YAAY;wDAAC;4DAAE,WAAW;wDAA4B;wDAAG;4DAAE,WAAW;wDAAQ;qDAAE;gDAAC;gDAAG,KAAK;oDAAE,QAAQ;gDAAsC;gDAAG,KAAK;oDAAE,QAAQ;gDAA6B;4CAAE;4CAAG,SAAS;4CAAyC,QAAQ;wCAAoC;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,OAAO;oCAAK,SAAS;oCAAuB,QAAQ;oCAAmC,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAc,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,OAAO;oCAAK,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;wCAAG;4CAAE,SAAS;4CAAU,QAAQ;wCAA4B;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG,YAAY;oBAAE,SAAS;oBAA0F,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAA4B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAiD;gCAAG,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAAkD;gCAAG,KAAK;oCAAE,QAAQ;gCAAkC;4BAAE;4BAAG,SAAS;wBAAwD;qBAAE;gBAAC;gBAAG,WAAW;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;oBAAE,cAAc;wBAAE,UAAU;4BAAE,SAAS;4BAAkD,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAA4B,OAAO;4BAAwB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;wBAAG,QAAQ;4BAAE,SAAS;4BAA4C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAA6B,OAAO;4BAAwB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAA+B;6BAAE;wBAAC;wBAAG,oBAAoB;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,SAAS;4BAAmE,QAAQ;wBAAoC;wBAAG,UAAU;4BAAE,SAAS;4BAAsC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAiC,OAAO;4BAA2B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;wBAAE;wBAAG,OAAO;4BAAE,SAAS;4BAAmC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAA2B,OAAO;4BAAwB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG,oBAAoB;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAQ;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAQ;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAS;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAS;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAS;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAa;6BAAE;4BAAE,cAAc;gCAAE,UAAU;oCAAE,SAAS,CAAC,8HAA8H,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAmB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,sGAAsG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,QAAQ;oCAAE,SAAS,CAAC,yFAAyF,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAyB,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,cAAc;oCAAE,SAAS,CAAC,uGAAuG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA+B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAiB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,6HAA6H,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,uGAAuG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,wGAAwG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,4FAA4F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,sGAAsG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,iBAAiB;oCAAE,SAAS,CAAC,6GAA6G,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAkC,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAoB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,yGAAyG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,8FAA8F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,qGAAqG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,SAAS;oCAAE,SAAS,CAAC,qGAAqG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA0B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,8FAA8F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,iBAAiB;oCAAE,SAAS,CAAC,kGAAkG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAkC,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAuB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,4FAA4F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAkB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,0GAA0G,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,4FAA4F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,SAAS;oCAAE,SAAS,CAAC,yGAAyG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA0B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,uHAAuH,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAuB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,uGAAuG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAe;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,qGAAqG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAiB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAkB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,eAAe;oCAAE,SAAS,CAAC,oHAAoH,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAgC,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAkB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,eAAe;oCAAE,SAAS,CAAC,uGAAuG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAgC,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAqB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,iKAAiK,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,oGAAoG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,uHAAuH,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,8GAA8G,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,iBAAiB;oCAAE,SAAS,CAAC,6JAA6J,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAkC,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAoB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,oGAAoG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,uJAAuJ,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAgB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,QAAQ;oCAAE,SAAS,CAAC,qGAAqG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAyB,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,wGAAwG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,qGAAqG,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAAM,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,6FAA6F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAe;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAkB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,6IAA6I,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAe;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,YAAY;oCAAE,SAAS,CAAC,6FAA6F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA6B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAe;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,SAAS;oCAAE,SAAS,CAAC,iHAAiH,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA0B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,aAAa;oCAAE,SAAS,CAAC,4HAA4H,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA8B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAoB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,UAAU;oCAAE,SAAS,CAAC,2FAA2F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA2B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,4FAA4F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAe;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG,WAAW;oCAAE,SAAS,CAAC,4FAA4F,CAAC;oCAAE,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAA4B;gDAAG;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,OAAO;oCAAoC,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAA4C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2B;wCAAG,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,SAAS;4CAAW,eAAe;4CAA4B,OAAO;4CAAkC,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;4BAAE;wBAAE;wBAAG,yBAAyB;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,SAAS;4BAAkF,QAAQ;wBAAkC;oBAAE;gBAAE;YAAE;QAAE;IAAE;IAAG,aAAa;IAAmB,iBAAiB;QAAC;QAAQ;QAAO;QAAO;QAAQ;QAAO;QAAQ;QAAQ;QAAK;QAAQ;QAAO;QAAO;QAAM;QAAO;QAAO;QAAQ;QAAO;QAAW;QAAU;QAAK;QAAO;QAAQ;QAAU;QAAM;QAAU;QAAO;QAAc;QAAS;QAAQ;QAAe;QAAS;QAAQ;QAAQ;QAAc;QAAU;QAAS;QAAQ;QAAS;QAAe;QAAc;QAAU;QAAU;QAAQ;QAAc;QAAY;QAAU;QAAU;QAAS;QAAU;KAAO;IAAE,WAAW;QAAC;QAAa;KAAO;AAAC;AACz71D,IAAI,WAAW;OACV,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,iMAAA,CAAA,UAAG;OACH,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,kMAAA,CAAA,UAAI;OACJ,kMAAA,CAAA,UAAI;OACJ,+LAAA,CAAA,UAAC;OACD,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,iMAAA,CAAA,UAAG;OACH,gMAAA,CAAA,UAAE;OACF,iMAAA,CAAA,UAAG;OACH,iMAAA,CAAA,UAAG;OACH,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,qMAAA,CAAA,UAAO;OACP,oMAAA,CAAA,UAAM;OACN,+LAAA,CAAA,UAAC;OACD,iMAAA,CAAA,UAAG;OACH,kMAAA,CAAA,UAAI;OACJ,oMAAA,CAAA,UAAM;OACN,gMAAA,CAAA,UAAE;OACF,oMAAA,CAAA,UAAM;OACN,iMAAA,CAAA,UAAG;OACH,wMAAA,CAAA,UAAU;OACV,mMAAA,CAAA,UAAK;OACL,kMAAA,CAAA,UAAI;OACJ,4MAAA,CAAA,UAAW;OACX,mMAAA,CAAA,UAAK;OACL,kMAAA,CAAA,UAAI;OACJ,kMAAA,CAAA,UAAI;OACJ,wMAAA,CAAA,UAAU;OACV,oMAAA,CAAA,UAAM;OACN,mMAAA,CAAA,UAAK;OACL,kMAAA,CAAA,UAAI;OACJ,mMAAA,CAAA,UAAK;OACL,yMAAA,CAAA,UAAW;OACX,wMAAA,CAAA,UAAU;OACV,oMAAA,CAAA,UAAM;OACN,oMAAA,CAAA,UAAM;OACN,kMAAA,CAAA,UAAI;OACJ,wMAAA,CAAA,UAAU;OACV,sMAAA,CAAA,UAAQ;OACR,oMAAA,CAAA,UAAM;OACN,oMAAA,CAAA,UAAM;OACN,mMAAA,CAAA,UAAK;OACL,oMAAA,CAAA,UAAM;OACN,kMAAA,CAAA,UAAI;IACP;CACD", "ignoreList": [0], "debugId": null}}]}