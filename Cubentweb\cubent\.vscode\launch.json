{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "pnpm dev"}, {"name": "Next.js: debug client-side (app)", "type": "chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug client-side (web)", "type": "chrome", "request": "launch", "url": "http://localhost:3001"}, {"name": "Next.js: debug client-side (api)", "type": "chrome", "request": "launch", "url": "http://localhost:3002"}, {"name": "Next.js: debug client-side (email)", "type": "chrome", "request": "launch", "url": "http://localhost:3003"}, {"name": "Next.js: debug client-side (app)", "type": "chrome", "request": "launch", "url": "http://localhost:3004"}, {"name": "Next.js: debug client-side (studio)", "type": "chrome", "request": "launch", "url": "http://localhost:3005"}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"action": "debugWithEdge", "killOnServerStop": true, "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}"}}]}