---
title: Welcome to Cubent Coder
description: 'AI-Powered Autonomous Coding Agent for Visual Studio Code'
---

<img
  src="/images/Cubent.Dev.gif"
  alt="Cubent Coder Interface - AI-powered coding assistant in action"
/>

## What is Cubent Coder?

**Cubent Coder** is an AI-powered autonomous coding agent that lives in your VS Code editor. It can communicate in natural language, read and write files directly in your workspace, run terminal commands, automate browser actions, and integrate with any OpenAI-compatible or custom API/model.

Whether you're seeking a flexible coding partner, a system architect, or specialized roles like a QA engineer or product manager, Cubent Coder can help you build software more efficiently.

## Getting Started

Ready to transform your coding experience? Let's get you set up with Cubent Coder.

<CardGroup cols={2}>
  <Card
    title="Install Extension"
    icon="download"
    href="https://marketplace.visualstudio.com/items?itemName=cubent.cubent"
  >
    Download Cubent Coder from the VS Code Marketplace
  </Card>
  <Card
    title="Quick Setup"
    icon="rocket"
    href="/installation"
  >
    Connect your AI provider and start coding in minutes
  </Card>
</CardGroup>

## Key Features

Discover what makes <PERSON>ubent Coder the ultimate AI coding companion.

<CardGroup cols={2}>
  <Card
    title="Natural Language Coding"
    icon="comments"
    href="/features/chat-mode"
  >
    Generate, refactor, and debug code using simple natural language commands
  </Card>
  <Card
    title="Autonomous Agent"
    icon="robot"
    href="/features/agent-mode"
  >
    Let Cubent Coder work independently on complex tasks with minimal supervision
  </Card>
  <Card
    title="Custom Modes"
    icon="gear"
    href="/features/custom-modes"
  >
    Create specialized AI personas for security auditing, code review, and more
  </Card>
  <Card
    title="Smart Integration"
    icon="plug"
    href="/advanced/mcp-integration"
  >
    Extend capabilities with MCP (Model Context Protocol) and custom tools
  </Card>
</CardGroup>

## Community & Support

Join thousands of developers using Cubent Coder worldwide.

<CardGroup cols={3}>
  <Card
    title="Discord Community"
    icon="discord"
    href="https://discord.gg/cubent"
  >
    Get real-time help and connect with other developers
  </Card>
  <Card
    title="GitHub Repository"
    icon="github"
    href="https://github.com/LaxBloxBoy2/Cubent"
  >
    Contribute to the project and report issues
  </Card>
  <Card
    title="Reddit Community"
    icon="reddit"
    href="https://www.reddit.com/r/cubent/"
  >
    Share experiences and get tips from the community
  </Card>
</CardGroup>
