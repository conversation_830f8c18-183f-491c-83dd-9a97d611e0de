{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/json5.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON5\", \"fileTypes\": [\"json5\"], \"name\": \"json5\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json5\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json5\" } }, \"name\": \"meta.structure.array.json5\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json5\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json5\" }] }, \"comments\": { \"patterns\": [{ \"match\": \"/{2}.*\", \"name\": \"comment.single.json5\" }, { \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json5\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json5\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json5\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json5\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null|Infinity|NaN)\\\\b\", \"name\": \"constant.language.json5\" }, \"infinity\": { \"match\": \"(-)*\\\\b(?:Infinity|NaN)\\\\b\", \"name\": \"constant.language.json5\" }, \"key\": { \"name\": \"string.key.json5\", \"patterns\": [{ \"include\": \"#stringSingle\" }, { \"include\": \"#stringDouble\" }, { \"match\": \"[a-zA-Z0-9_-]\", \"name\": \"string.key.json5\" }] }, \"number\": { \"patterns\": [{ \"comment\": \"handles hexadecimal numbers\", \"match\": \"(0x)[0-9a-fA-f]*\", \"name\": \"constant.hex.numeric.json5\" }, { \"comment\": \"handles integer and decimal numbers\", \"match\": \"[+-.]?(?=[1-9]|0(?!\\\\d))\\\\d+(\\\\.\\\\d+)?([eE][+-]?\\\\d+)?\", \"name\": \"constant.dec.numeric.json5\" }] }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json5\" } }, \"comment\": \"a json5 object\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json5\" } }, \"name\": \"meta.structure.dictionary.json5\", \"patterns\": [{ \"include\": \"#comments\" }, { \"comment\": \"the json5 object key\", \"include\": \"#key\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json5\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json5\" } }, \"name\": \"meta.structure.dictionary.value.json5\", \"patterns\": [{ \"comment\": \"the json5 object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json5\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json5\" }] }, \"stringDouble\": { \"begin\": '[\"]', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json5\" } }, \"end\": '[\"]', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json5\" } }, \"name\": \"string.quoted.json5\", \"patterns\": [{ \"match\": '(?:\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4}))', \"name\": \"constant.character.escape.json5\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json5\" }] }, \"stringSingle\": { \"begin\": \"[']\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json5\" } }, \"end\": \"[']\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json5\" } }, \"name\": \"string.quoted.json5\", \"patterns\": [{ \"match\": '(?:\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4}))', \"name\": \"constant.character.escape.json5\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json5\" }] }, \"value\": { \"comment\": \"the 'value' diagram at http://json.org\", \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#infinity\" }, { \"include\": \"#number\" }, { \"include\": \"#stringSingle\" }, { \"include\": \"#stringDouble\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }] } }, \"scopeName\": \"source.json5\" });\nvar json5 = [\n  lang\n];\n\nexport { json5 as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;KAAQ;IAAE,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAsB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA0C,QAAQ;QAA0B;QAAG,YAAY;YAAE,SAAS;YAA8B,QAAQ;QAA0B;QAAG,OAAO;YAAE,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAmB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAA+B,SAAS;oBAAoB,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;oBAAuC,SAAS;oBAA0D,QAAQ;gBAA6B;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,WAAW;YAAkB,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;oBAAwB,WAAW;gBAAO;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;4BAA0B,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAAsD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAsD;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,SAAS;oBAA6C,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAmD;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,SAAS;oBAA6C,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAmD;aAAE;QAAC;QAAG,SAAS;YAAE,WAAW;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AAC3rH,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}