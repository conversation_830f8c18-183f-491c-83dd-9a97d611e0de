{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/diff.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Diff\", \"name\": \"diff\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.separator.diff\" } }, \"match\": \"^((\\\\*{15})|(={67})|(-{3}))$\\\\n?\", \"name\": \"meta.separator.diff\" }, { \"match\": \"^\\\\d+(,\\\\d+)*(a|d|c)\\\\d+(,\\\\d+)*$\\\\n?\", \"name\": \"meta.diff.range.normal\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.range.diff\" }, \"2\": { \"name\": \"meta.toc-list.line-number.diff\" }, \"3\": { \"name\": \"punctuation.definition.range.diff\" } }, \"match\": \"^(@@)\\\\s*(.+?)\\\\s*(@@)($\\\\n?)?\", \"name\": \"meta.diff.range.unified\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.range.diff\" }, \"4\": { \"name\": \"punctuation.definition.range.diff\" }, \"6\": { \"name\": \"punctuation.definition.range.diff\" }, \"7\": { \"name\": \"punctuation.definition.range.diff\" } }, \"match\": \"^(((-{3}) .+ (-{4}))|((\\\\*{3}) .+ (\\\\*{4})))$\\\\n?\", \"name\": \"meta.diff.range.context\" }, { \"match\": \"^diff --git a/.*$\\\\n?\", \"name\": \"meta.diff.header.git\" }, { \"match\": \"^diff (-|\\\\S+\\\\s+\\\\S+).*$\\\\n?\", \"name\": \"meta.diff.header.command\" }, { \"captures\": { \"4\": { \"name\": \"punctuation.definition.from-file.diff\" }, \"6\": { \"name\": \"punctuation.definition.from-file.diff\" }, \"7\": { \"name\": \"punctuation.definition.from-file.diff\" } }, \"match\": \"(^(((-{3}) .+)|((\\\\*{3}) .+))$\\\\n?|^(={4}) .+(?= - ))\", \"name\": \"meta.diff.header.from-file\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.definition.to-file.diff\" }, \"3\": { \"name\": \"punctuation.definition.to-file.diff\" }, \"4\": { \"name\": \"punctuation.definition.to-file.diff\" } }, \"match\": \"(^(\\\\+{3}) .+$\\\\n?| (-) .* (={4})$\\\\n?)\", \"name\": \"meta.diff.header.to-file\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.inserted.diff\" }, \"6\": { \"name\": \"punctuation.definition.inserted.diff\" } }, \"match\": \"^(((>)( .*)?)|((\\\\+).*))$\\\\n?\", \"name\": \"markup.inserted.diff\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.changed.diff\" } }, \"match\": \"^(!).*$\\\\n?\", \"name\": \"markup.changed.diff\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.deleted.diff\" }, \"6\": { \"name\": \"punctuation.definition.deleted.diff\" } }, \"match\": \"^(((<)( .*)?)|((-).*))$\\\\n?\", \"name\": \"markup.deleted.diff\" }, { \"begin\": \"^(#)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.diff\" } }, \"comment\": 'Git produces unified diffs with embedded comments\"', \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.diff\" }, { \"match\": \"^index [0-9a-f]{7,40}\\\\.\\\\.[0-9a-f]{7,40}.*$\\\\n?\", \"name\": \"meta.diff.index.git\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.key-value.diff\" }, \"2\": { \"name\": \"meta.toc-list.file-name.diff\" } }, \"match\": \"^Index(:) (.+)$\\\\n?\", \"name\": \"meta.diff.index\" }, { \"match\": \"^Only in .*: .*$\\\\n?\", \"name\": \"meta.diff.only-in\" }], \"scopeName\": \"source.diff\" });\nvar diff = [\n  lang\n];\n\nexport { diff as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAoC,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAyC,QAAQ;QAAyB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAkC,QAAQ;QAA0B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAqD,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAyB,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAiC,QAAQ;QAA2B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAyD,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2C,QAAQ;QAA2B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;YAAiC,QAAQ;QAAuB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAe,QAAQ;QAAsB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA+B,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,WAAW;YAAsD,OAAO;YAAO,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAoD,QAAQ;QAAsB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;YAAuB,QAAQ;QAAkB;QAAG;YAAE,SAAS;YAAwB,QAAQ;QAAoB;KAAE;IAAE,aAAa;AAAc;AACjtF,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}