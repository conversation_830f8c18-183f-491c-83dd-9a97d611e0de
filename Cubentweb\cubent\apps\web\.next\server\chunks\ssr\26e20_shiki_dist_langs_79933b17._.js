module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/shellscript.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>shellscript)
});
const lang = Object.freeze({
    "displayName": "Shell",
    "name": "shellscript",
    "patterns": [
        {
            "include": "#initial_context"
        }
    ],
    "repository": {
        "alias_statement": {
            "begin": "(?:(?:[ \\t]*+)(alias)(?:[ \\t]*+)((?:(?:((?<!\\w)-\\w+\\b)(?:[ \\t]*+))*))(?:(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(=)|(\\+=))|(-=))))",
            "beginCaptures": {
                "1": {
                    "name": "storage.type.alias.shell"
                },
                "2": {
                    "patterns": [
                        {
                            "match": "(?<!\\w)-\\w+\\b",
                            "name": "string.unquoted.argument.shell constant.other.option.shell"
                        }
                    ]
                },
                "3": {
                    "name": "string.unquoted.argument.shell constant.other.option.shell"
                },
                "4": {
                    "name": "variable.other.assignment.shell"
                },
                "5": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "6": {
                    "name": "variable.other.assignment.shell"
                },
                "7": {
                    "name": "constant.numeric.shell constant.numeric.integer.shell"
                },
                "8": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "9": {
                    "name": "keyword.operator.assignment.shell"
                },
                "10": {
                    "name": "keyword.operator.assignment.compound.shell"
                },
                "11": {
                    "name": "keyword.operator.assignment.compound.shell"
                }
            },
            "end": "(?:(?= |\\t|$)|(?:(?:(?:(;)|(&&))|(\\|\\|))|(&)))",
            "endCaptures": {
                "1": {
                    "name": "punctuation.terminator.statement.semicolon.shell"
                },
                "2": {
                    "name": "punctuation.separator.statement.and.shell"
                },
                "3": {
                    "name": "punctuation.separator.statement.or.shell"
                },
                "4": {
                    "name": "punctuation.separator.statement.background.shell"
                }
            },
            "name": "meta.expression.assignment.alias.shell",
            "patterns": [
                {
                    "include": "#normal_context"
                }
            ]
        },
        "argument": {
            "begin": "(?:[ \\t]++)(?!(?:&|\\||\\(|\\[|#|\\n|$|;))",
            "beginCaptures": {},
            "end": "(?= |\\t|;|\\||&|$|\\n|\\)|\\`)",
            "endCaptures": {},
            "name": "meta.argument.shell",
            "patterns": [
                {
                    "include": "#argument_context"
                },
                {
                    "include": "#line_continuation"
                }
            ]
        },
        "argument_context": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "string.unquoted.argument.shell",
                            "patterns": [
                                {
                                    "match": "\\*",
                                    "name": "variable.language.special.wildcard.shell"
                                },
                                {
                                    "include": "#variable"
                                },
                                {
                                    "include": "#numeric_literal"
                                },
                                {
                                    "captures": {
                                        "1": {
                                            "name": "constant.language.$1.shell"
                                        }
                                    },
                                    "match": "(?<!\\w)(\\b(?:true|false)\\b)(?!\\w)"
                                }
                            ]
                        }
                    },
                    "match": "(?:[ \\t]*+)((?:[^ \\t\\n>&;<>()$`\\\\\"'<\\|]+)(?!>))"
                },
                {
                    "include": "#normal_context"
                }
            ]
        },
        "arithmetic_double": {
            "patterns": [
                {
                    "begin": "\\(\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.arithmetic.double.shell"
                        }
                    },
                    "end": "\\)(?:\\s*)\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.arithmetic.double.shell"
                        }
                    },
                    "name": "meta.arithmetic.shell",
                    "patterns": [
                        {
                            "include": "#math"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                }
            ]
        },
        "arithmetic_no_dollar": {
            "patterns": [
                {
                    "begin": "\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.arithmetic.single.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.arithmetic.single.shell"
                        }
                    },
                    "name": "meta.arithmetic.shell",
                    "patterns": [
                        {
                            "include": "#math"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                }
            ]
        },
        "array_access_inline": {
            "captures": {
                "1": {
                    "name": "punctuation.section.array.shell"
                },
                "2": {
                    "patterns": [
                        {
                            "include": "#special_expansion"
                        },
                        {
                            "include": "#string"
                        },
                        {
                            "include": "#variable"
                        }
                    ]
                },
                "3": {
                    "name": "punctuation.section.array.shell"
                }
            },
            "match": "(?:(\\[)([^\\[\\]]+)(\\]))"
        },
        "array_value": {
            "begin": "(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(=)|(\\+=))|(-=))(?:[ \\t]*+)(\\()",
            "beginCaptures": {
                "1": {
                    "name": "variable.other.assignment.shell"
                },
                "2": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "3": {
                    "name": "variable.other.assignment.shell"
                },
                "4": {
                    "name": "constant.numeric.shell constant.numeric.integer.shell"
                },
                "5": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "6": {
                    "name": "keyword.operator.assignment.shell"
                },
                "7": {
                    "name": "keyword.operator.assignment.compound.shell"
                },
                "8": {
                    "name": "keyword.operator.assignment.compound.shell"
                },
                "9": {
                    "name": "punctuation.definition.array.shell"
                }
            },
            "end": "\\)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.array.shell"
                }
            },
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.assignment.array.shell entity.other.attribute-name.shell"
                        },
                        "2": {
                            "name": "keyword.operator.assignment.shell punctuation.definition.assignment.shell"
                        }
                    },
                    "match": "(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(=))"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.bracket.named-array.shell"
                        },
                        "2": {
                            "name": "string.unquoted.shell entity.other.attribute-name.bracket.shell"
                        },
                        "3": {
                            "name": "punctuation.definition.bracket.named-array.shell"
                        },
                        "4": {
                            "name": "punctuation.definition.assignment.shell"
                        }
                    },
                    "match": "(?:(\\[)(.+?)(\\])(=))"
                },
                {
                    "include": "#normal_context"
                },
                {
                    "include": "#simple_unquoted"
                }
            ]
        },
        "assignment_statement": {
            "patterns": [
                {
                    "include": "#array_value"
                },
                {
                    "include": "#modified_assignment_statement"
                },
                {
                    "include": "#normal_assignment_statement"
                }
            ]
        },
        "basic_command_name": {
            "captures": {
                "1": {
                    "name": "storage.modifier.$1.shell"
                },
                "2": {
                    "name": "entity.name.function.call.shell entity.name.command.shell",
                    "patterns": [
                        {
                            "match": "(?<!\\w)(?:continue|return|break)(?!\\w)",
                            "name": "keyword.control.$0.shell"
                        },
                        {
                            "match": "(?<!\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\.)(?!\\/))(?!\\w)(?!-)",
                            "name": "support.function.builtin.shell"
                        },
                        {
                            "include": "#variable"
                        }
                    ]
                }
            },
            "match": `(?:(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect	|nocorrect$|readonly |readonly	|readonly$|function |function	|function$|foreach |foreach	|foreach$|coproc |coproc	|coproc$|logout |logout	|logout$|export |export	|export$|select |select	|select$|repeat |repeat	|repeat$|pushd |pushd	|pushd$|until |until	|until$|while |while	|while$|local |local	|local$|case |case	|case$|done |done	|done$|elif |elif	|elif$|else |else	|else$|esac |esac	|esac$|popd |popd	|popd$|then |then	|then$|time |time	|time$|for |for	|for$|end |end	|end$|fi |fi	|fi$|do |do	|do$|in |in	|in$|if |if	|if$))(?:((?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$))|((?!"|'|\\\\\\n?$)(?:[^!'"<> \\t\\n\\r]+?)))(?:(?= |\\t)|(?:(?=;|\\||&|\\n|\\)|\\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\))))`,
            "name": "meta.statement.command.name.basic.shell"
        },
        "block_comment": {
            "begin": "(?:(?:\\s*+)(\\/\\*))",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.definition.comment.begin.shell"
                }
            },
            "end": "\\*\\/",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.comment.end.shell"
                }
            },
            "name": "comment.block.shell"
        },
        "boolean": {
            "match": "\\b(?:true|false)\\b",
            "name": "constant.language.$0.shell"
        },
        "case_statement": {
            "begin": "(?:(\\bcase\\b)(?:[ \\t]*+)(.+?)(?:[ \\t]*+)(\\bin\\b))",
            "beginCaptures": {
                "1": {
                    "name": "keyword.control.case.shell"
                },
                "2": {
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                "3": {
                    "name": "keyword.control.in.shell"
                }
            },
            "end": "\\besac\\b",
            "endCaptures": {
                "0": {
                    "name": "keyword.control.esac.shell"
                }
            },
            "name": "meta.case.shell",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "captures": {
                        "1": {
                            "name": "keyword.operator.pattern.case.default.shell"
                        }
                    },
                    "match": "(?:[ \\t]*+)(\\* *\\))"
                },
                {
                    "begin": "(?<!\\))(?!(?:[ \\t]*+)(?:esac\\b|$))",
                    "beginCaptures": {},
                    "end": "(?:(?=\\besac\\b)|(\\)))",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.operator.pattern.case.shell"
                        }
                    },
                    "name": "meta.case.entry.pattern.shell",
                    "patterns": [
                        {
                            "include": "#case_statement_context"
                        }
                    ]
                },
                {
                    "begin": "(?<=\\))",
                    "beginCaptures": {},
                    "end": "(?:(;;)|(?=\\besac\\b))",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.terminator.statement.case.shell"
                        }
                    },
                    "name": "meta.case.entry.body.shell",
                    "patterns": [
                        {
                            "include": "#typical_statements"
                        },
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "case_statement_context": {
            "patterns": [
                {
                    "match": "\\*",
                    "name": "variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"
                },
                {
                    "match": "\\+",
                    "name": "variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"
                },
                {
                    "match": "\\?",
                    "name": "variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"
                },
                {
                    "match": "@",
                    "name": "variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell"
                },
                {
                    "match": "\\|",
                    "name": "keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell"
                },
                {
                    "match": "\\\\.",
                    "name": "constant.character.escape.shell"
                },
                {
                    "match": "(?<=\\tin| in| |\\t|;;)\\(",
                    "name": "keyword.operator.pattern.case.shell"
                },
                {
                    "begin": "(?<=\\S)(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.group.shell punctuation.definition.regex.group.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell punctuation.definition.regex.group.shell"
                        }
                    },
                    "name": "meta.parenthese.shell",
                    "patterns": [
                        {
                            "include": "#case_statement_context"
                        }
                    ]
                },
                {
                    "begin": "\\[",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.character-class.shell"
                        }
                    },
                    "end": "\\]",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.character-class.shell"
                        }
                    },
                    "name": "string.regexp.character-class.shell",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character.escape.shell"
                        }
                    ]
                },
                {
                    "include": "#string"
                },
                {
                    "match": "[^) \\t\\n\\[?\\*\\|\\@]",
                    "name": "string.unquoted.pattern.shell string.regexp.unquoted.shell"
                }
            ]
        },
        "command_name_range": {
            "begin": "\\G",
            "beginCaptures": {},
            "end": "(?:(?= |\\t|;|\\||&|$|\\n|\\)|\\`)|(?=<))",
            "endCaptures": {},
            "name": "meta.statement.command.name.shell",
            "patterns": [
                {
                    "match": "(?<!\\w)(?:continue|return|break)(?!\\w)",
                    "name": "entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell"
                },
                {
                    "match": "(?<!\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\.)(?!\\/))(?!\\w)(?!-)",
                    "name": "entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell"
                },
                {
                    "include": "#variable"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.name.function.call.shell entity.name.command.shell"
                        }
                    },
                    "match": `(?:(?<!\\w)(?<=\\G|'|"|\\}|\\))([^ \\n\\t\\r"'=;&\\|\`){<>]+))`
                },
                {
                    "begin": `(?:(?:\\G|(?<! |\\t|;|\\||&|\\n|\\{|#))(?:(\\$?)((?:(")|(')))))`,
                    "beginCaptures": {
                        "1": {
                            "name": "meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell"
                        },
                        "2": {},
                        "3": {
                            "name": "meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell"
                        },
                        "4": {
                            "name": "meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell"
                        }
                    },
                    "end": "(?<!\\G)(?<=(?:\\2))",
                    "endCaptures": {},
                    "patterns": [
                        {
                            "include": "#continuation_of_single_quoted_command_name"
                        },
                        {
                            "include": "#continuation_of_double_quoted_command_name"
                        }
                    ]
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "include": "#simple_unquoted"
                }
            ]
        },
        "command_statement": {
            "begin": "(?:(?:[ \\t]*+)(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect	|nocorrect$|readonly |readonly	|readonly$|function |function	|function$|foreach |foreach	|foreach$|coproc |coproc	|coproc$|logout |logout	|logout$|export |export	|export$|select |select	|select$|repeat |repeat	|repeat$|pushd |pushd	|pushd$|until |until	|until$|while |while	|while$|local |local	|local$|case |case	|case$|done |done	|done$|elif |elif	|elif$|else |else	|else$|esac |esac	|esac$|popd |popd	|popd$|then |then	|then$|time |time	|time$|for |for	|for$|end |end	|end$|fi |fi	|fi$|do |do	|do$|in |in	|in$|if |if	|if$)(?!\\\\\\n?$)))",
            "beginCaptures": {},
            "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
            "endCaptures": {},
            "name": "meta.statement.command.shell",
            "patterns": [
                {
                    "include": "#command_name_range"
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "include": "#option"
                },
                {
                    "include": "#argument"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#heredoc"
                }
            ]
        },
        "comment": {
            "captures": {
                "1": {
                    "name": "comment.line.number-sign.shell meta.shebang.shell"
                },
                "2": {
                    "name": "punctuation.definition.comment.shebang.shell"
                },
                "3": {
                    "name": "comment.line.number-sign.shell"
                },
                "4": {
                    "name": "punctuation.definition.comment.shell"
                }
            },
            "match": "(?:(?:^|(?:[ \\t]++))(?:((?:(#!)(?:.*)))|((?:(#)(?:.*)))))"
        },
        "comments": {
            "patterns": [
                {
                    "include": "#block_comment"
                },
                {
                    "include": "#line_comment"
                }
            ]
        },
        "compound-command": {
            "patterns": [
                {
                    "begin": "\\[",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.logical-expression.shell"
                        }
                    },
                    "end": "\\]",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.logical-expression.shell"
                        }
                    },
                    "name": "meta.scope.logical-expression.shell",
                    "patterns": [
                        {
                            "include": "#logical-expression"
                        },
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "begin": "(?<=\\s|^){(?=\\s|$)",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell"
                        }
                    },
                    "end": "(?<=^|;)\\s*(})",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.definition.group.shell"
                        }
                    },
                    "name": "meta.scope.group.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "continuation_of_double_quoted_command_name": {
            "begin": '(?:\\G(?<="))',
            "beginCaptures": {},
            "contentName": "meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command",
            "end": '"',
            "endCaptures": {
                "0": {
                    "name": "string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell"
                }
            },
            "patterns": [
                {
                    "match": '\\\\[$\\n`"\\\\]',
                    "name": "constant.character.escape.shell"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                }
            ]
        },
        "continuation_of_single_quoted_command_name": {
            "begin": "(?:\\G(?<='))",
            "beginCaptures": {},
            "contentName": "meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command",
            "end": "'",
            "endCaptures": {
                "0": {
                    "name": "string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell"
                }
            }
        },
        "custom_command_names": {
            "patterns": []
        },
        "custom_commands": {
            "patterns": []
        },
        "double_quote_context": {
            "patterns": [
                {
                    "match": '\\\\[$`"\\\\\\n]',
                    "name": "constant.character.escape.shell"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                }
            ]
        },
        "double_quote_escape_char": {
            "match": '\\\\[$`"\\\\\\n]',
            "name": "constant.character.escape.shell"
        },
        "floating_keyword": {
            "patterns": [
                {
                    "match": "(?<=^|;|&| |\\t)(?:then|elif|else|done|end|do|if|fi)(?= |\\t|;|&|$)",
                    "name": "keyword.control.$0.shell"
                }
            ]
        },
        "for_statement": {
            "patterns": [
                {
                    "begin": "(?:(\\bfor\\b)(?:(?:[ \\t]*+)((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:[ \\t]*+)(\\bin\\b)))",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.for.shell"
                        },
                        "2": {
                            "name": "variable.other.for.shell"
                        },
                        "3": {
                            "name": "keyword.control.in.shell"
                        }
                    },
                    "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
                    "endCaptures": {},
                    "name": "meta.for.in.shell",
                    "patterns": [
                        {
                            "include": "#string"
                        },
                        {
                            "include": "#simple_unquoted"
                        },
                        {
                            "include": "#normal_context"
                        }
                    ]
                },
                {
                    "begin": "(\\bfor\\b)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.for.shell"
                        }
                    },
                    "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
                    "endCaptures": {},
                    "name": "meta.for.shell",
                    "patterns": [
                        {
                            "include": "#arithmetic_double"
                        },
                        {
                            "include": "#normal_context"
                        }
                    ]
                }
            ]
        },
        "function_definition": {
            "applyEndPatternLast": 1,
            "begin": `(?:[ \\t]*+)(?:(?:(\\bfunction\\b)(?:[ \\t]*+)([^ \\t\\n\\r()="']+)(?:(?:(\\()(?:[ \\t]*+)(\\)))?))|(?:([^ \\t\\n\\r()="']+)(?:[ \\t]*+)(\\()(?:[ \\t]*+)(\\))))`,
            "beginCaptures": {
                "1": {
                    "name": "storage.type.function.shell"
                },
                "2": {
                    "name": "entity.name.function.shell"
                },
                "3": {
                    "name": "punctuation.definition.arguments.shell"
                },
                "4": {
                    "name": "punctuation.definition.arguments.shell"
                },
                "5": {
                    "name": "entity.name.function.shell"
                },
                "6": {
                    "name": "punctuation.definition.arguments.shell"
                },
                "7": {
                    "name": "punctuation.definition.arguments.shell"
                }
            },
            "end": "(?<=\\}|\\))",
            "endCaptures": {},
            "name": "meta.function.shell",
            "patterns": [
                {
                    "match": "(?:\\G(?:\\t| |\\n))"
                },
                {
                    "begin": "\\{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"
                        }
                    },
                    "end": "\\}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"
                        }
                    },
                    "name": "meta.function.body.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "begin": "\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"
                        }
                    },
                    "name": "meta.function.body.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "include": "#initial_context"
                }
            ]
        },
        "heredoc": {
            "patterns": [
                {
                    "begin": `(?:((?<!<)(?:<<-))(?:[ \\t]*+)("|')(?:[ \\t]*+)([^"']+?)(?=\\s|;|&|<|"|')((?:\\2))(.*))`,
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.heredoc.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.string.heredoc.quote.shell"
                        },
                        "3": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        },
                        "4": {
                            "name": "punctuation.definition.string.heredoc.quote.shell"
                        },
                        "5": {
                            "patterns": [
                                {
                                    "include": "#redirect_fix"
                                },
                                {
                                    "include": "#typical_statements"
                                }
                            ]
                        }
                    },
                    "contentName": "string.quoted.heredoc.indent.$3",
                    "end": "(?:(?:^\\t*)(?:\\3)(?=\\s|;|&|$))",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.heredoc.$0.shell"
                        }
                    },
                    "patterns": []
                },
                {
                    "begin": `(?:((?<!<)(?:<<)(?!<))(?:[ \\t]*+)("|')(?:[ \\t]*+)([^"']+?)(?=\\s|;|&|<|"|')((?:\\2))(.*))`,
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.heredoc.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.string.heredoc.quote.shell"
                        },
                        "3": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        },
                        "4": {
                            "name": "punctuation.definition.string.heredoc.quote.shell"
                        },
                        "5": {
                            "patterns": [
                                {
                                    "include": "#redirect_fix"
                                },
                                {
                                    "include": "#typical_statements"
                                }
                            ]
                        }
                    },
                    "contentName": "string.quoted.heredoc.no-indent.$3",
                    "end": "(?:^(?:\\3)(?=\\s|;|&|$))",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        }
                    },
                    "patterns": []
                },
                {
                    "begin": `(?:((?<!<)(?:<<-))(?:[ \\t]*+)([^"' \\t]+)(?=\\s|;|&|<|"|')(.*))`,
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.heredoc.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        },
                        "3": {
                            "patterns": [
                                {
                                    "include": "#redirect_fix"
                                },
                                {
                                    "include": "#typical_statements"
                                }
                            ]
                        }
                    },
                    "contentName": "string.unquoted.heredoc.indent.$2",
                    "end": "(?:(?:^\\t*)(?:\\2)(?=\\s|;|&|$))",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#double_quote_escape_char"
                        },
                        {
                            "include": "#variable"
                        },
                        {
                            "include": "#interpolation"
                        }
                    ]
                },
                {
                    "begin": `(?:((?<!<)(?:<<)(?!<))(?:[ \\t]*+)([^"' \\t]+)(?=\\s|;|&|<|"|')(.*))`,
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.heredoc.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        },
                        "3": {
                            "patterns": [
                                {
                                    "include": "#redirect_fix"
                                },
                                {
                                    "include": "#typical_statements"
                                }
                            ]
                        }
                    },
                    "contentName": "string.unquoted.heredoc.no-indent.$2",
                    "end": "(?:^(?:\\2)(?=\\s|;|&|$))",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.heredoc.delimiter.shell"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#double_quote_escape_char"
                        },
                        {
                            "include": "#variable"
                        },
                        {
                            "include": "#interpolation"
                        }
                    ]
                }
            ]
        },
        "herestring": {
            "patterns": [
                {
                    "begin": "(<<<)\\s*(('))",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.herestring.shell"
                        },
                        "2": {
                            "name": "string.quoted.single.shell"
                        },
                        "3": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "contentName": "string.quoted.single.shell",
                    "end": "(')",
                    "endCaptures": {
                        "0": {
                            "name": "string.quoted.single.shell"
                        },
                        "1": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "meta.herestring.shell"
                },
                {
                    "begin": '(<<<)\\s*(("))',
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.herestring.shell"
                        },
                        "2": {
                            "name": "string.quoted.double.shell"
                        },
                        "3": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "contentName": "string.quoted.double.shell",
                    "end": '(")',
                    "endCaptures": {
                        "0": {
                            "name": "string.quoted.double.shell"
                        },
                        "1": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "meta.herestring.shell",
                    "patterns": [
                        {
                            "include": "#double_quote_context"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "name": "keyword.operator.herestring.shell"
                        },
                        "2": {
                            "name": "string.unquoted.herestring.shell",
                            "patterns": [
                                {
                                    "include": "#initial_context"
                                }
                            ]
                        }
                    },
                    "match": "(<<<)\\s*(([^\\s)\\\\]|\\\\.)+)",
                    "name": "meta.herestring.shell"
                }
            ]
        },
        "initial_context": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pipeline"
                },
                {
                    "include": "#normal_statement_seperator"
                },
                {
                    "include": "#logical_expression_double"
                },
                {
                    "include": "#logical_expression_single"
                },
                {
                    "include": "#assignment_statement"
                },
                {
                    "include": "#case_statement"
                },
                {
                    "include": "#for_statement"
                },
                {
                    "include": "#loop"
                },
                {
                    "include": "#function_definition"
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "include": "#arithmetic_double"
                },
                {
                    "include": "#misc_ranges"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                },
                {
                    "include": "#heredoc"
                },
                {
                    "include": "#herestring"
                },
                {
                    "include": "#redirection"
                },
                {
                    "include": "#pathname"
                },
                {
                    "include": "#floating_keyword"
                },
                {
                    "include": "#alias_statement"
                },
                {
                    "include": "#normal_statement"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#support"
                }
            ]
        },
        "inline_comment": {
            "captures": {
                "1": {
                    "name": "comment.block.shell punctuation.definition.comment.begin.shell"
                },
                "2": {
                    "name": "comment.block.shell"
                },
                "3": {
                    "patterns": [
                        {
                            "match": "\\*\\/",
                            "name": "comment.block.shell punctuation.definition.comment.end.shell"
                        },
                        {
                            "match": "\\*",
                            "name": "comment.block.shell"
                        }
                    ]
                }
            },
            "match": "(\\/\\*)((?:(?:[^\\*]|(?:(?:\\*++)[^\\/]))*+)((?:(?:\\*++)\\/)))"
        },
        "interpolation": {
            "patterns": [
                {
                    "include": "#arithmetic_dollar"
                },
                {
                    "include": "#subshell_dollar"
                },
                {
                    "begin": "`",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.evaluation.backticks.shell"
                        }
                    },
                    "end": "`",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.evaluation.backticks.shell"
                        }
                    },
                    "name": "string.interpolated.backtick.shell",
                    "patterns": [
                        {
                            "match": "\\\\[`\\\\$]",
                            "name": "constant.character.escape.shell"
                        },
                        {
                            "begin": "(?<=\\W)(?=#)(?!#{)",
                            "beginCaptures": {
                                "1": {
                                    "name": "punctuation.whitespace.comment.leading.shell"
                                }
                            },
                            "end": "(?!\\G)",
                            "patterns": [
                                {
                                    "begin": "#",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "punctuation.definition.comment.shell"
                                        }
                                    },
                                    "end": "(?=`)",
                                    "name": "comment.line.number-sign.shell"
                                }
                            ]
                        },
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "keyword": {
            "patterns": [
                {
                    "match": "(?<=^|;|&|\\s)(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=\\s|;|&|$)",
                    "name": "keyword.control.shell"
                },
                {
                    "match": "(?<=^|;|&|\\s)(?:export|declare|typeset|local|readonly)(?=\\s|;|&|$)",
                    "name": "storage.modifier.shell"
                }
            ]
        },
        "line_comment": {
            "begin": "(?:\\s*+)(\\/\\/)",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.definition.comment.shell"
                }
            },
            "end": "(?<=\\n)(?<!\\\\\\n)",
            "endCaptures": {},
            "name": "comment.line.double-slash.shell",
            "patterns": [
                {
                    "include": "#line_continuation_character"
                }
            ]
        },
        "line_continuation": {
            "match": "\\\\(?=\\n)",
            "name": "constant.character.escape.line-continuation.shell"
        },
        "logical-expression": {
            "patterns": [
                {
                    "include": "#arithmetic_no_dollar"
                },
                {
                    "comment": "do we want a special rule for ( expr )?",
                    "match": "=[=~]?|!=?|<|>|&&|\\|\\|",
                    "name": "keyword.operator.logical.shell"
                },
                {
                    "match": "(?<!\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])\\b",
                    "name": "keyword.operator.logical.shell"
                }
            ]
        },
        "logical_expression_context": {
            "patterns": [
                {
                    "include": "#regex_comparison"
                },
                {
                    "include": "#arithmetic_no_dollar"
                },
                {
                    "include": "#logical-expression"
                },
                {
                    "include": "#logical_expression_single"
                },
                {
                    "include": "#logical_expression_double"
                },
                {
                    "include": "#comment"
                },
                {
                    "include": "#boolean"
                },
                {
                    "include": "#redirect_number"
                },
                {
                    "include": "#numeric_literal"
                },
                {
                    "include": "#pipeline"
                },
                {
                    "include": "#normal_statement_seperator"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                },
                {
                    "include": "#heredoc"
                },
                {
                    "include": "#herestring"
                },
                {
                    "include": "#pathname"
                },
                {
                    "include": "#floating_keyword"
                },
                {
                    "include": "#support"
                }
            ]
        },
        "logical_expression_double": {
            "begin": "\\[\\[",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.logical-expression.shell"
                }
            },
            "end": "\\]\\]",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.logical-expression.shell"
                }
            },
            "name": "meta.scope.logical-expression.shell",
            "patterns": [
                {
                    "include": "#logical_expression_context"
                }
            ]
        },
        "logical_expression_single": {
            "begin": "\\[",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.logical-expression.shell"
                }
            },
            "end": "\\]",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.logical-expression.shell"
                }
            },
            "name": "meta.scope.logical-expression.shell",
            "patterns": [
                {
                    "include": "#logical_expression_context"
                }
            ]
        },
        "loop": {
            "patterns": [
                {
                    "begin": "(?<=^|;|&|\\s)(for)\\s+(.+?)\\s+(in)(?=\\s|;|&|$)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.shell"
                        },
                        "2": {
                            "name": "variable.other.loop.shell",
                            "patterns": [
                                {
                                    "include": "#string"
                                }
                            ]
                        },
                        "3": {
                            "name": "keyword.control.shell"
                        }
                    },
                    "end": "(?<=^|;|&|\\s)done(?=\\s|;|&|$|\\))",
                    "endCaptures": {
                        "0": {
                            "name": "keyword.control.shell"
                        }
                    },
                    "name": "meta.scope.for-in-loop.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "begin": "(?<=^|;|&|\\s)(while|until)(?=\\s|;|&|$)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.shell"
                        }
                    },
                    "end": "(?<=^|;|&|\\s)done(?=\\s|;|&|$|\\))",
                    "endCaptures": {
                        "0": {
                            "name": "keyword.control.shell"
                        }
                    },
                    "name": "meta.scope.while-loop.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "begin": "(?<=^|;|&|\\s)(select)\\s+((?:[^\\s\\\\]|\\\\.)+)(?=\\s|;|&|$)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.shell"
                        },
                        "2": {
                            "name": "variable.other.loop.shell"
                        }
                    },
                    "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$|\\))",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.shell"
                        }
                    },
                    "name": "meta.scope.select-block.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "begin": "(?<=^|;|&|\\s)if(?=\\s|;|&|$)",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control.if.shell"
                        }
                    },
                    "end": "(?<=^|;|&|\\s)fi(?=\\s|;|&|$)",
                    "endCaptures": {
                        "0": {
                            "name": "keyword.control.fi.shell"
                        }
                    },
                    "name": "meta.scope.if-block.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "math": {
            "patterns": [
                {
                    "include": "#variable"
                },
                {
                    "match": "\\+{1,2}|-{1,2}|!|~|\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\|{1,2}|&{1,2}|\\?|:|,|=|[*/%+\\-&^|]=|<<=|>>=",
                    "name": "keyword.operator.arithmetic.shell"
                },
                {
                    "match": "0[xX][0-9A-Fa-f]+",
                    "name": "constant.numeric.hex.shell"
                },
                {
                    "match": ";",
                    "name": "punctuation.separator.semicolon.range"
                },
                {
                    "match": "0\\d+",
                    "name": "constant.numeric.octal.shell"
                },
                {
                    "match": "\\d{1,2}#[0-9a-zA-Z@_]+",
                    "name": "constant.numeric.other.shell"
                },
                {
                    "match": "\\d+",
                    "name": "constant.numeric.integer.shell"
                },
                {
                    "match": "(?<!\\w)(?:[a-zA-Z_0-9]+)(?!\\w)",
                    "name": "variable.other.normal.shell"
                }
            ]
        },
        "math_operators": {
            "patterns": [
                {
                    "match": "\\+{1,2}|-{1,2}|!|~|\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\|{1,2}|&{1,2}|\\?|:|,|=|[*/%+\\-&^|]=|<<=|>>=",
                    "name": "keyword.operator.arithmetic.shell"
                },
                {
                    "match": "0[xX][0-9A-Fa-f]+",
                    "name": "constant.numeric.hex.shell"
                },
                {
                    "match": "0\\d+",
                    "name": "constant.numeric.octal.shell"
                },
                {
                    "match": "\\d{1,2}#[0-9a-zA-Z@_]+",
                    "name": "constant.numeric.other.shell"
                },
                {
                    "match": "\\d+",
                    "name": "constant.numeric.integer.shell"
                }
            ]
        },
        "misc_ranges": {
            "patterns": [
                {
                    "include": "#logical_expression_single"
                },
                {
                    "include": "#logical_expression_double"
                },
                {
                    "include": "#subshell_dollar"
                },
                {
                    "begin": "(?<![^ \\t])({)(?!\\w|\\$)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.group.shell"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.group.shell"
                        }
                    },
                    "name": "meta.scope.group.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "modified_assignment_statement": {
            "begin": "(?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$)",
            "beginCaptures": {
                "0": {
                    "name": "storage.modifier.$0.shell"
                }
            },
            "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
            "endCaptures": {},
            "name": "meta.statement.shell meta.expression.assignment.modified.shell",
            "patterns": [
                {
                    "match": "(?<!\\w)-\\w+\\b",
                    "name": "string.unquoted.argument.shell constant.other.option.shell"
                },
                {
                    "include": "#array_value"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.assignment.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.array.access.shell"
                        },
                        "3": {
                            "name": "variable.other.assignment.shell"
                        },
                        "4": {
                            "name": "constant.numeric.shell constant.numeric.integer.shell"
                        },
                        "5": {
                            "name": "punctuation.definition.array.access.shell"
                        },
                        "6": {
                            "name": "keyword.operator.assignment.shell"
                        },
                        "7": {
                            "name": "keyword.operator.assignment.compound.shell"
                        },
                        "8": {
                            "name": "keyword.operator.assignment.compound.shell"
                        },
                        "9": {
                            "name": "constant.numeric.shell constant.numeric.hex.shell"
                        },
                        "10": {
                            "name": "constant.numeric.shell constant.numeric.octal.shell"
                        },
                        "11": {
                            "name": "constant.numeric.shell constant.numeric.other.shell"
                        },
                        "12": {
                            "name": "constant.numeric.shell constant.numeric.decimal.shell"
                        },
                        "13": {
                            "name": "constant.numeric.shell constant.numeric.version.shell"
                        },
                        "14": {
                            "name": "constant.numeric.shell constant.numeric.integer.shell"
                        }
                    },
                    "match": "(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?)(?:(?:(?:(=)|(\\+=))|(-=))?)(?:(?:(?<==| |\\t|^|\\{|\\(|\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\d+))|(\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\d+(?:\\.\\d+)))|(-?\\d+(?:\\.\\d+)+))|(-?\\d+))(?= |\\t|$|\\}|\\)|;))?))"
                },
                {
                    "include": "#normal_context"
                }
            ]
        },
        "modifiers": {
            "match": "(?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$)",
            "name": "storage.modifier.$0.shell"
        },
        "normal_assignment_statement": {
            "begin": "(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(=)|(\\+=))|(-=))",
            "beginCaptures": {
                "1": {
                    "name": "variable.other.assignment.shell"
                },
                "2": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "3": {
                    "name": "variable.other.assignment.shell"
                },
                "4": {
                    "name": "constant.numeric.shell constant.numeric.integer.shell"
                },
                "5": {
                    "name": "punctuation.definition.array.access.shell"
                },
                "6": {
                    "name": "keyword.operator.assignment.shell"
                },
                "7": {
                    "name": "keyword.operator.assignment.compound.shell"
                },
                "8": {
                    "name": "keyword.operator.assignment.compound.shell"
                }
            },
            "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
            "endCaptures": {},
            "name": "meta.expression.assignment.shell",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#normal_assignment_statement"
                },
                {
                    "begin": "(?<= |\\t)(?! |\\t|\\w+=)",
                    "beginCaptures": {},
                    "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
                    "endCaptures": {},
                    "name": "meta.statement.command.env.shell",
                    "patterns": [
                        {
                            "include": "#command_name_range"
                        },
                        {
                            "include": "#line_continuation"
                        },
                        {
                            "include": "#option"
                        },
                        {
                            "include": "#argument"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                },
                {
                    "include": "#simple_unquoted"
                },
                {
                    "include": "#normal_context"
                }
            ]
        },
        "normal_context": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pipeline"
                },
                {
                    "include": "#normal_statement_seperator"
                },
                {
                    "include": "#misc_ranges"
                },
                {
                    "include": "#boolean"
                },
                {
                    "include": "#redirect_number"
                },
                {
                    "include": "#numeric_literal"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                },
                {
                    "include": "#heredoc"
                },
                {
                    "include": "#herestring"
                },
                {
                    "include": "#redirection"
                },
                {
                    "include": "#pathname"
                },
                {
                    "include": "#floating_keyword"
                },
                {
                    "include": "#support"
                },
                {
                    "include": "#parenthese"
                }
            ]
        },
        "normal_statement": {
            "begin": "(?:(?!^[ \\t]*+$)(?:(?<=^until | until |\\tuntil |^while | while |\\twhile |^elif | elif |\\telif |^else | else |\\telse |^then | then |\\tthen |^do | do |\\tdo |^if | if |\\tif )|(?<=(?:^|;|\\||&|!|\\(|\\{|\\`)))(?:[ \\t]*+)(?!nocorrect\\W|nocorrect\\$|function\\W|function\\$|foreach\\W|foreach\\$|repeat\\W|repeat\\$|logout\\W|logout\\$|coproc\\W|coproc\\$|select\\W|select\\$|while\\W|while\\$|pushd\\W|pushd\\$|until\\W|until\\$|case\\W|case\\$|done\\W|done\\$|elif\\W|elif\\$|else\\W|else\\$|esac\\W|esac\\$|popd\\W|popd\\$|then\\W|then\\$|time\\W|time\\$|for\\W|for\\$|end\\W|end\\$|fi\\W|fi\\$|do\\W|do\\$|in\\W|in\\$|if\\W|if\\$))",
            "beginCaptures": {},
            "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
            "endCaptures": {},
            "name": "meta.statement.shell",
            "patterns": [
                {
                    "include": "#typical_statements"
                }
            ]
        },
        "normal_statement_seperator": {
            "captures": {
                "1": {
                    "name": "punctuation.terminator.statement.semicolon.shell"
                },
                "2": {
                    "name": "punctuation.separator.statement.and.shell"
                },
                "3": {
                    "name": "punctuation.separator.statement.or.shell"
                },
                "4": {
                    "name": "punctuation.separator.statement.background.shell"
                }
            },
            "match": "(?:(?:(?:(;)|(&&))|(\\|\\|))|(&))"
        },
        "numeric_literal": {
            "captures": {
                "1": {
                    "name": "constant.numeric.shell constant.numeric.hex.shell"
                },
                "2": {
                    "name": "constant.numeric.shell constant.numeric.octal.shell"
                },
                "3": {
                    "name": "constant.numeric.shell constant.numeric.other.shell"
                },
                "4": {
                    "name": "constant.numeric.shell constant.numeric.decimal.shell"
                },
                "5": {
                    "name": "constant.numeric.shell constant.numeric.version.shell"
                },
                "6": {
                    "name": "constant.numeric.shell constant.numeric.integer.shell"
                }
            },
            "match": "(?<==| |\\t|^|\\{|\\(|\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\d+))|(\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\d+(?:\\.\\d+)))|(-?\\d+(?:\\.\\d+)+))|(-?\\d+))(?= |\\t|$|\\}|\\)|;)"
        },
        "option": {
            "begin": "(?:(?:[ \\t]++)(-)((?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))))",
            "beginCaptures": {
                "1": {
                    "name": "string.unquoted.argument.shell constant.other.option.dash.shell"
                },
                "2": {
                    "name": "string.unquoted.argument.shell constant.other.option.shell"
                }
            },
            "contentName": "string.unquoted.argument constant.other.option",
            "end": "(?:(?=[ \\t])|(?:(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)))",
            "endCaptures": {},
            "patterns": [
                {
                    "include": "#option_context"
                }
            ]
        },
        "option_context": {
            "patterns": [
                {
                    "include": "#misc_ranges"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#variable"
                },
                {
                    "include": "#interpolation"
                },
                {
                    "include": "#heredoc"
                },
                {
                    "include": "#herestring"
                },
                {
                    "include": "#redirection"
                },
                {
                    "include": "#pathname"
                },
                {
                    "include": "#floating_keyword"
                },
                {
                    "include": "#support"
                }
            ]
        },
        "parenthese": {
            "patterns": [
                {
                    "begin": "\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.parenthese.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.parenthese.shell"
                        }
                    },
                    "name": "meta.parenthese.group.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "pathname": {
            "patterns": [
                {
                    "match": "(?<=\\s|:|=|^)~",
                    "name": "keyword.operator.tilde.shell"
                },
                {
                    "match": "\\*|\\?",
                    "name": "keyword.operator.glob.shell"
                },
                {
                    "begin": "([?*+@!])(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.operator.extglob.shell"
                        },
                        "2": {
                            "name": "punctuation.definition.extglob.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.extglob.shell"
                        }
                    },
                    "name": "meta.structure.extglob.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "pipeline": {
            "patterns": [
                {
                    "match": "(?<=^|;|&|\\s)(time)(?=\\s|;|&|$)",
                    "name": "keyword.other.shell"
                },
                {
                    "match": "[|!]",
                    "name": "keyword.operator.pipe.shell"
                }
            ]
        },
        "redirect_fix": {
            "captures": {
                "1": {
                    "name": "keyword.operator.redirect.shell"
                },
                "2": {
                    "name": "string.unquoted.argument.shell"
                }
            },
            "match": "(?:(>>?)(?:[ \\t]*+)([^ \\t\\n>&;<>()$`\\\\\"'<\\|]+))"
        },
        "redirect_number": {
            "captures": {
                "1": {
                    "name": "keyword.operator.redirect.stdout.shell"
                },
                "2": {
                    "name": "keyword.operator.redirect.stderr.shell"
                },
                "3": {
                    "name": "keyword.operator.redirect.$3.shell"
                }
            },
            "match": "(?<=[ \\t])(?:(?:(1)|(2)|(\\d+))(?=>))"
        },
        "redirection": {
            "patterns": [
                {
                    "begin": "[><]\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "string.interpolated.process-substitution.shell",
                    "patterns": [
                        {
                            "include": "#initial_context"
                        }
                    ]
                },
                {
                    "match": "(?<![<>])(&>|\\d*>&\\d*|\\d*(>>|>|<)|\\d*<&|\\d*<>)(?![<>])",
                    "name": "keyword.operator.redirect.shell"
                }
            ]
        },
        "regex_comparison": {
            "match": "=~",
            "name": "keyword.operator.logical.regex.shell"
        },
        "regexp": {
            "patterns": [
                {
                    "match": "(?:.+)"
                }
            ]
        },
        "simple_options": {
            "captures": {
                "0": {
                    "patterns": [
                        {
                            "captures": {
                                "1": {
                                    "name": "string.unquoted.argument.shell constant.other.option.dash.shell"
                                },
                                "2": {
                                    "name": "string.unquoted.argument.shell constant.other.option.shell"
                                }
                            },
                            "match": "(?:[ \\t]++)(-)(\\w+)"
                        }
                    ]
                }
            },
            "match": "(?:(?:[ \\t]++)-(?:\\w+))*"
        },
        "simple_unquoted": {
            "match": "[^ \\t\\n>&;<>()$`\\\\\"'<\\|]",
            "name": "string.unquoted.shell"
        },
        "special_expansion": {
            "match": "!|:[-=?]?|\\*|@|##|#|%%|%|\\/",
            "name": "keyword.operator.expansion.shell"
        },
        "start_of_command": {
            "match": "(?:(?:[ \\t]*+)(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect	|nocorrect$|readonly |readonly	|readonly$|function |function	|function$|foreach |foreach	|foreach$|coproc |coproc	|coproc$|logout |logout	|logout$|export |export	|export$|select |select	|select$|repeat |repeat	|repeat$|pushd |pushd	|pushd$|until |until	|until$|while |while	|while$|local |local	|local$|case |case	|case$|done |done	|done$|elif |elif	|elif$|else |else	|else$|esac |esac	|esac$|popd |popd	|popd$|then |then	|then$|time |time	|time$|for |for	|for$|end |end	|end$|fi |fi	|fi$|do |do	|do$|in |in	|in$|if |if	|if$)(?!\\\\\\n?$)))"
        },
        "string": {
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character.escape.shell"
                },
                {
                    "begin": "'",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "end": "'",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "string.quoted.single.shell"
                },
                {
                    "begin": '\\$?"',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "end": '"',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "string.quoted.double.shell",
                    "patterns": [
                        {
                            "match": '\\\\[$\\n`"\\\\]',
                            "name": "constant.character.escape.shell"
                        },
                        {
                            "include": "#variable"
                        },
                        {
                            "include": "#interpolation"
                        }
                    ]
                },
                {
                    "begin": "\\$'",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.shell"
                        }
                    },
                    "end": "'",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.shell"
                        }
                    },
                    "name": "string.quoted.single.dollar.shell",
                    "patterns": [
                        {
                            "match": "\\\\(?:a|b|e|f|n|r|t|v|\\\\|')",
                            "name": "constant.character.escape.ansi-c.shell"
                        },
                        {
                            "match": '\\\\\\d{3}"',
                            "name": "constant.character.escape.octal.shell"
                        },
                        {
                            "match": '\\\\x[0-9a-fA-F]{2}"',
                            "name": "constant.character.escape.hex.shell"
                        },
                        {
                            "match": '\\\\c."',
                            "name": "constant.character.escape.control-char.shell"
                        }
                    ]
                }
            ]
        },
        "subshell_dollar": {
            "patterns": [
                {
                    "begin": "(?:\\$\\()",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.subshell.single.shell"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.subshell.single.shell"
                        }
                    },
                    "name": "meta.scope.subshell",
                    "patterns": [
                        {
                            "include": "#parenthese"
                        },
                        {
                            "include": "#initial_context"
                        }
                    ]
                }
            ]
        },
        "support": {
            "patterns": [
                {
                    "match": "(?<=^|;|&|\\s)(?::|\\.)(?=\\s|;|&|$)",
                    "name": "support.function.builtin.shell"
                }
            ]
        },
        "typical_statements": {
            "patterns": [
                {
                    "include": "#assignment_statement"
                },
                {
                    "include": "#case_statement"
                },
                {
                    "include": "#for_statement"
                },
                {
                    "include": "#while_statement"
                },
                {
                    "include": "#function_definition"
                },
                {
                    "include": "#command_statement"
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "include": "#arithmetic_double"
                },
                {
                    "include": "#normal_context"
                }
            ]
        },
        "variable": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell variable.parameter.positional.all.shell"
                        },
                        "2": {
                            "name": "variable.parameter.positional.all.shell"
                        }
                    },
                    "match": "(?:(\\$)(\\@(?!\\w)))"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell variable.parameter.positional.shell"
                        },
                        "2": {
                            "name": "variable.parameter.positional.shell"
                        }
                    },
                    "match": "(?:(\\$)(\\d(?!\\w)))"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell variable.language.special.shell"
                        },
                        "2": {
                            "name": "variable.language.special.shell"
                        }
                    },
                    "match": "(?:(\\$)([-*#?$!0_](?!\\w)))"
                },
                {
                    "begin": "(?:(\\$)(\\{)(?:[ \\t]*+)(?=\\d))",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell variable.parameter.positional.shell"
                        },
                        "2": {
                            "name": "punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell"
                        }
                    },
                    "contentName": "meta.parameter-expansion",
                    "end": "\\}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#special_expansion"
                        },
                        {
                            "include": "#array_access_inline"
                        },
                        {
                            "match": "\\d+",
                            "name": "variable.parameter.positional.shell"
                        },
                        {
                            "match": "(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w)",
                            "name": "variable.other.normal.shell"
                        },
                        {
                            "include": "#variable"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                },
                {
                    "begin": "(?:(\\$)(\\{))",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell"
                        },
                        "2": {
                            "name": "punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell"
                        }
                    },
                    "contentName": "meta.parameter-expansion",
                    "end": "\\}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#special_expansion"
                        },
                        {
                            "include": "#array_access_inline"
                        },
                        {
                            "match": "(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w)",
                            "name": "variable.other.normal.shell"
                        },
                        {
                            "include": "#variable"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.variable.shell variable.other.normal.shell"
                        },
                        "2": {
                            "name": "variable.other.normal.shell"
                        }
                    },
                    "match": "(?:(\\$)((?:\\w+)(?!\\w)))"
                }
            ]
        },
        "while_statement": {
            "patterns": [
                {
                    "begin": "(\\bwhile\\b)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.while.shell"
                        }
                    },
                    "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)",
                    "endCaptures": {},
                    "name": "meta.while.shell",
                    "patterns": [
                        {
                            "include": "#line_continuation"
                        },
                        {
                            "include": "#math_operators"
                        },
                        {
                            "include": "#option"
                        },
                        {
                            "include": "#simple_unquoted"
                        },
                        {
                            "include": "#normal_context"
                        },
                        {
                            "include": "#string"
                        }
                    ]
                }
            ]
        }
    },
    "scopeName": "source.shell",
    "aliases": [
        "bash",
        "sh",
        "shell",
        "zsh"
    ]
});
var shellscript = [
    lang
];
;
}}),
"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/json.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>json)
});
const lang = Object.freeze({
    "displayName": "JSON",
    "name": "json",
    "patterns": [
        {
            "include": "#value"
        }
    ],
    "repository": {
        "array": {
            "begin": "\\[",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.array.begin.json"
                }
            },
            "end": "\\]",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.array.end.json"
                }
            },
            "name": "meta.structure.array.json",
            "patterns": [
                {
                    "include": "#value"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.array.json"
                },
                {
                    "match": "[^\\s\\]]",
                    "name": "invalid.illegal.expected-array-separator.json"
                }
            ]
        },
        "comments": {
            "patterns": [
                {
                    "begin": "/\\*\\*(?!/)",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.json"
                        }
                    },
                    "end": "\\*/",
                    "name": "comment.block.documentation.json"
                },
                {
                    "begin": "/\\*",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.json"
                        }
                    },
                    "end": "\\*/",
                    "name": "comment.block.json"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.comment.json"
                        }
                    },
                    "match": "(//).*$\\n?",
                    "name": "comment.line.double-slash.js"
                }
            ]
        },
        "constant": {
            "match": "\\b(?:true|false|null)\\b",
            "name": "constant.language.json"
        },
        "number": {
            "match": "-?(?:0|[1-9]\\d*)(?:(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)?",
            "name": "constant.numeric.json"
        },
        "object": {
            "begin": "\\{",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.dictionary.begin.json"
                }
            },
            "end": "\\}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.dictionary.end.json"
                }
            },
            "name": "meta.structure.dictionary.json",
            "patterns": [
                {
                    "comment": "the JSON object key",
                    "include": "#objectkey"
                },
                {
                    "include": "#comments"
                },
                {
                    "begin": ":",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.separator.dictionary.key-value.json"
                        }
                    },
                    "end": "(,)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.separator.dictionary.pair.json"
                        }
                    },
                    "name": "meta.structure.dictionary.value.json",
                    "patterns": [
                        {
                            "comment": "the JSON object value",
                            "include": "#value"
                        },
                        {
                            "match": "[^\\s,]",
                            "name": "invalid.illegal.expected-dictionary-separator.json"
                        }
                    ]
                },
                {
                    "match": "[^\\s}]",
                    "name": "invalid.illegal.expected-dictionary-separator.json"
                }
            ]
        },
        "objectkey": {
            "begin": '"',
            "beginCaptures": {
                "0": {
                    "name": "punctuation.support.type.property-name.begin.json"
                }
            },
            "end": '"',
            "endCaptures": {
                "0": {
                    "name": "punctuation.support.type.property-name.end.json"
                }
            },
            "name": "string.json support.type.property-name.json",
            "patterns": [
                {
                    "include": "#stringcontent"
                }
            ]
        },
        "string": {
            "begin": '"',
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.string.begin.json"
                }
            },
            "end": '"',
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.string.end.json"
                }
            },
            "name": "string.quoted.double.json",
            "patterns": [
                {
                    "include": "#stringcontent"
                }
            ]
        },
        "stringcontent": {
            "patterns": [
                {
                    "match": '\\\\(?:["\\\\/bfnrt]|u[0-9a-fA-F]{4})',
                    "name": "constant.character.escape.json"
                },
                {
                    "match": "\\\\.",
                    "name": "invalid.illegal.unrecognized-string-escape.json"
                }
            ]
        },
        "value": {
            "patterns": [
                {
                    "include": "#constant"
                },
                {
                    "include": "#number"
                },
                {
                    "include": "#string"
                },
                {
                    "include": "#array"
                },
                {
                    "include": "#object"
                },
                {
                    "include": "#comments"
                }
            ]
        }
    },
    "scopeName": "source.json"
});
var json = [
    lang
];
;
}}),
"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/java.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>java)
});
const lang = Object.freeze({
    "displayName": "Java",
    "name": "java",
    "patterns": [
        {
            "begin": "\\b(package)\\b\\s*",
            "beginCaptures": {
                "1": {
                    "name": "keyword.other.package.java"
                }
            },
            "contentName": "storage.modifier.package.java",
            "end": "\\s*(;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.terminator.java"
                }
            },
            "name": "meta.package.java",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "match": "(?<=\\.)\\s*\\.|\\.(?=\\s*;)",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!_)_(?=\\s*(\\.|;))|\\b\\d+|-+",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "[A-Z]+",
                    "name": "invalid.deprecated.package_name_not_lowercase.java"
                },
                {
                    "match": "\\b(?<!\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\b",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.java"
                }
            ]
        },
        {
            "begin": "\\b(import)\\b\\s*\\b(static)?\\b\\s",
            "beginCaptures": {
                "1": {
                    "name": "keyword.other.import.java"
                },
                "2": {
                    "name": "storage.modifier.java"
                }
            },
            "contentName": "storage.modifier.import.java",
            "end": "\\s*(;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.terminator.java"
                }
            },
            "name": "meta.import.java",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "match": "(?<=\\.)\\s*\\.|\\.(?=\\s*;)",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!\\.)\\s*\\*",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!_)_(?=\\s*(\\.|;))|\\b\\d+|-+",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\b(?<!\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\b",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.java"
                },
                {
                    "match": "\\*",
                    "name": "variable.language.wildcard.java"
                }
            ]
        },
        {
            "include": "#comments-javadoc"
        },
        {
            "include": "#code"
        },
        {
            "include": "#module"
        }
    ],
    "repository": {
        "all-types": {
            "patterns": [
                {
                    "include": "#primitive-arrays"
                },
                {
                    "include": "#primitive-types"
                },
                {
                    "include": "#object-types"
                }
            ]
        },
        "annotations": {
            "patterns": [
                {
                    "begin": "((@)\\s*([^\\s(]+))(\\()",
                    "beginCaptures": {
                        "2": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "3": {
                            "name": "storage.type.annotation.java"
                        },
                        "4": {
                            "name": "punctuation.definition.annotation-arguments.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.annotation-arguments.end.bracket.round.java"
                        }
                    },
                    "name": "meta.declaration.annotation.java",
                    "patterns": [
                        {
                            "captures": {
                                "1": {
                                    "name": "constant.other.key.java"
                                },
                                "2": {
                                    "name": "keyword.operator.assignment.java"
                                }
                            },
                            "match": "(\\w*)\\s*(=)"
                        },
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "2": {
                            "name": "storage.modifier.java"
                        },
                        "3": {
                            "name": "storage.type.annotation.java"
                        },
                        "5": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "6": {
                            "name": "storage.type.annotation.java"
                        }
                    },
                    "match": "(@)(interface)\\s+(\\w*)|((@)\\s*(\\w+))",
                    "name": "meta.declaration.annotation.java"
                }
            ]
        },
        "anonymous-block-and-instance-initializer": {
            "begin": "{",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.block.begin.bracket.curly.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.block.end.bracket.curly.java"
                }
            },
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "anonymous-classes-and-new": {
            "begin": "\\bnew\\b",
            "beginCaptures": {
                "0": {
                    "name": "keyword.control.new.java"
                }
            },
            "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "include": "#function-call"
                },
                {
                    "include": "#all-types"
                },
                {
                    "begin": "(?<=\\))",
                    "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.inner-class.begin.bracket.curly.java"
                                }
                            },
                            "end": "}",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.inner-class.end.bracket.curly.java"
                                }
                            },
                            "name": "meta.inner-class.java",
                            "patterns": [
                                {
                                    "include": "#class-body"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "(?<=\\])",
                    "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.array-initializer.begin.bracket.curly.java"
                                }
                            },
                            "end": "}",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.array-initializer.end.bracket.curly.java"
                                }
                            },
                            "name": "meta.array-initializer.java",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "include": "#parens"
                }
            ]
        },
        "assertions": {
            "patterns": [
                {
                    "begin": "\\b(assert)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.assert.java"
                        }
                    },
                    "end": "$",
                    "name": "meta.declaration.assertion.java",
                    "patterns": [
                        {
                            "match": ":",
                            "name": "keyword.operator.assert.expression-separator.java"
                        },
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "class": {
            "begin": "(?=\\w?[\\w\\s-]*\\b(?:class|(?<!@)interface|enum)\\s+[\\w$]+)",
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.class.end.bracket.curly.java"
                }
            },
            "name": "meta.class.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.modifier.java"
                        },
                        "2": {
                            "name": "entity.name.type.class.java"
                        }
                    },
                    "match": "(class|(?<!@)interface|enum)\\s+([\\w$]+)",
                    "name": "meta.class.identifier.java"
                },
                {
                    "begin": "extends",
                    "beginCaptures": {
                        "0": {
                            "name": "storage.modifier.extends.java"
                        }
                    },
                    "end": "(?={|implements|permits)",
                    "name": "meta.definition.class.inherited.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "(implements)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?=\\s*extends|permits|\\{)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "(permits)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.permits.java"
                        }
                    },
                    "end": "(?=\\s*extends|implements|\\{)",
                    "name": "meta.definition.class.permits.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.class.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.class.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#class-body"
                        }
                    ]
                }
            ]
        },
        "class-body": {
            "patterns": [
                {
                    "include": "#comments-javadoc"
                },
                {
                    "include": "#comments"
                },
                {
                    "include": "#enums"
                },
                {
                    "include": "#class"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#static-initializer"
                },
                {
                    "include": "#class-fields-and-methods"
                },
                {
                    "include": "#annotations"
                },
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#member-variables"
                },
                {
                    "include": "#code"
                }
            ]
        },
        "class-fields-and-methods": {
            "patterns": [
                {
                    "begin": "(?==)",
                    "end": "(?=;)",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "include": "#methods"
                }
            ]
        },
        "code": {
            "patterns": [
                {
                    "include": "#annotations"
                },
                {
                    "include": "#comments"
                },
                {
                    "include": "#enums"
                },
                {
                    "include": "#class"
                },
                {
                    "include": "#record"
                },
                {
                    "include": "#anonymous-block-and-instance-initializer"
                },
                {
                    "include": "#try-catch-finally"
                },
                {
                    "include": "#assertions"
                },
                {
                    "include": "#parens"
                },
                {
                    "include": "#constants-and-special-vars"
                },
                {
                    "include": "#numbers"
                },
                {
                    "include": "#anonymous-classes-and-new"
                },
                {
                    "include": "#lambda-expression"
                },
                {
                    "include": "#keywords"
                },
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#method-call"
                },
                {
                    "include": "#function-call"
                },
                {
                    "include": "#variables"
                },
                {
                    "include": "#variables-local"
                },
                {
                    "include": "#objects"
                },
                {
                    "include": "#properties"
                },
                {
                    "include": "#strings"
                },
                {
                    "include": "#all-types"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.period.java"
                },
                {
                    "match": ";",
                    "name": "punctuation.terminator.java"
                }
            ]
        },
        "comments": {
            "patterns": [
                {
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "match": "/\\*\\*/",
                    "name": "comment.block.empty.java"
                },
                {
                    "include": "#comments-inline"
                }
            ]
        },
        "comments-inline": {
            "patterns": [
                {
                    "begin": "/\\*",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "end": "\\*/",
                    "name": "comment.block.java"
                },
                {
                    "begin": "(^[ \\t]+)?(?=//)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.java"
                        }
                    },
                    "end": "(?!\\G)",
                    "patterns": [
                        {
                            "begin": "//",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.comment.java"
                                }
                            },
                            "end": "\\n",
                            "name": "comment.line.double-slash.java"
                        }
                    ]
                }
            ]
        },
        "comments-javadoc": {
            "patterns": [
                {
                    "begin": "^\\s*(/\\*\\*)(?!/)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "end": "\\*/",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "name": "comment.block.javadoc.java",
                    "patterns": [
                        {
                            "match": "@(author|deprecated|return|see|serial|since|version)\\b",
                            "name": "keyword.other.documentation.javadoc.java"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "variable.parameter.java"
                                }
                            },
                            "match": "(@param)\\s+(\\S+)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "entity.name.type.class.java"
                                }
                            },
                            "match": "(@(?:exception|throws))\\s+(\\S+)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "entity.name.type.class.java"
                                },
                                "3": {
                                    "name": "variable.parameter.java"
                                }
                            },
                            "match": "{(@link)\\s+(\\S+)?#([\\w$]+\\s*\\([^()]*\\)).*?}"
                        }
                    ]
                }
            ]
        },
        "constants-and-special-vars": {
            "patterns": [
                {
                    "match": "\\b(true|false|null)\\b",
                    "name": "constant.language.java"
                },
                {
                    "match": "\\bthis\\b",
                    "name": "variable.language.this.java"
                },
                {
                    "match": "\\bsuper\\b",
                    "name": "variable.language.java"
                }
            ]
        },
        "enums": {
            "begin": "^\\s*([\\w\\s]*)(enum)\\s+(\\w+)",
            "beginCaptures": {
                "1": {
                    "patterns": [
                        {
                            "include": "#storage-modifiers"
                        }
                    ]
                },
                "2": {
                    "name": "storage.modifier.java"
                },
                "3": {
                    "name": "entity.name.type.enum.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.enum.end.bracket.curly.java"
                }
            },
            "name": "meta.enum.java",
            "patterns": [
                {
                    "begin": "\\b(extends)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.extends.java"
                        }
                    },
                    "end": "(?={|\\bimplements\\b)",
                    "name": "meta.definition.class.inherited.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "\\b(implements)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?={|\\bextends\\b)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.enum.begin.bracket.curly.java"
                        }
                    },
                    "end": "(?=})",
                    "patterns": [
                        {
                            "begin": "(?<={)",
                            "end": "(?=;|})",
                            "patterns": [
                                {
                                    "include": "#comments-javadoc"
                                },
                                {
                                    "include": "#comments"
                                },
                                {
                                    "begin": "\\b(\\w+)\\b",
                                    "beginCaptures": {
                                        "1": {
                                            "name": "constant.other.enum.java"
                                        }
                                    },
                                    "end": "(,)|(?=;|})",
                                    "endCaptures": {
                                        "1": {
                                            "name": "punctuation.separator.delimiter.java"
                                        }
                                    },
                                    "patterns": [
                                        {
                                            "include": "#comments-javadoc"
                                        },
                                        {
                                            "include": "#comments"
                                        },
                                        {
                                            "begin": "\\(",
                                            "beginCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.round.java"
                                                }
                                            },
                                            "end": "\\)",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.round.java"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "include": "#code"
                                                }
                                            ]
                                        },
                                        {
                                            "begin": "{",
                                            "beginCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.curly.java"
                                                }
                                            },
                                            "end": "}",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.curly.java"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "include": "#class-body"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "include": "#class-body"
                        }
                    ]
                }
            ]
        },
        "function-call": {
            "begin": "([A-Za-z_$][\\w$]*)\\s*(\\()",
            "beginCaptures": {
                "1": {
                    "name": "entity.name.function.java"
                },
                "2": {
                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                }
            },
            "end": "\\)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                }
            },
            "name": "meta.function-call.java",
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "generics": {
            "begin": "<",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.bracket.angle.java"
                }
            },
            "end": ">",
            "endCaptures": {
                "0": {
                    "name": "punctuation.bracket.angle.java"
                }
            },
            "patterns": [
                {
                    "match": "\\b(extends|super)\\b",
                    "name": "storage.modifier.$1.java"
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.type.java"
                        }
                    },
                    "match": "(?<!\\.)([a-zA-Z$_][a-zA-Z0-9$_]*)(?=\\s*<)"
                },
                {
                    "include": "#primitive-arrays"
                },
                {
                    "match": "[a-zA-Z$_][a-zA-Z0-9$_]*",
                    "name": "storage.type.generic.java"
                },
                {
                    "match": "\\?",
                    "name": "storage.type.generic.wildcard.java"
                },
                {
                    "match": "&",
                    "name": "punctuation.separator.types.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.period.java"
                },
                {
                    "include": "#parens"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "keywords": {
            "patterns": [
                {
                    "match": "\\bthrow\\b",
                    "name": "keyword.control.throw.java"
                },
                {
                    "match": "\\?|:",
                    "name": "keyword.control.ternary.java"
                },
                {
                    "match": "\\b(return|yield|break|case|continue|default|do|while|for|switch|if|else)\\b",
                    "name": "keyword.control.java"
                },
                {
                    "match": "\\b(instanceof)\\b",
                    "name": "keyword.operator.instanceof.java"
                },
                {
                    "match": "(<<|>>>?|~|\\^)",
                    "name": "keyword.operator.bitwise.java"
                },
                {
                    "match": "((&|\\^|\\||<<|>>>?)=)",
                    "name": "keyword.operator.assignment.bitwise.java"
                },
                {
                    "match": "(===?|!=|<=|>=|<>|<|>)",
                    "name": "keyword.operator.comparison.java"
                },
                {
                    "match": "([+*/%-]=)",
                    "name": "keyword.operator.assignment.arithmetic.java"
                },
                {
                    "match": "(=)",
                    "name": "keyword.operator.assignment.java"
                },
                {
                    "match": "(--|\\+\\+)",
                    "name": "keyword.operator.increment-decrement.java"
                },
                {
                    "match": "(-|\\+|\\*|\\/|%)",
                    "name": "keyword.operator.arithmetic.java"
                },
                {
                    "match": "(!|&&|\\|\\|)",
                    "name": "keyword.operator.logical.java"
                },
                {
                    "match": "(\\||&)",
                    "name": "keyword.operator.bitwise.java"
                },
                {
                    "match": "\\b(const|goto)\\b",
                    "name": "keyword.reserved.java"
                }
            ]
        },
        "lambda-expression": {
            "patterns": [
                {
                    "match": "->",
                    "name": "storage.type.function.arrow.java"
                }
            ]
        },
        "member-variables": {
            "begin": "(?=private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)",
            "end": "(?==|;)",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#variables"
                },
                {
                    "include": "#primitive-arrays"
                },
                {
                    "include": "#object-types"
                }
            ]
        },
        "method-call": {
            "begin": "(\\.)\\s*([A-Za-z_$][\\w$]*)\\s*(\\()",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.separator.period.java"
                },
                "2": {
                    "name": "entity.name.function.java"
                },
                "3": {
                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                }
            },
            "end": "\\)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                }
            },
            "name": "meta.method-call.java",
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "methods": {
            "begin": "(?!new)(?=[\\w<].*\\s+)(?=([^=/]|/(?!/))+\\()",
            "end": "(})|(?=;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.section.method.end.bracket.curly.java"
                }
            },
            "name": "meta.method.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "begin": "(\\w+)\\s*(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "entity.name.function.java"
                        },
                        "2": {
                            "name": "punctuation.definition.parameters.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.parameters.end.bracket.round.java"
                        }
                    },
                    "name": "meta.method.identifier.java",
                    "patterns": [
                        {
                            "include": "#parameters"
                        },
                        {
                            "include": "#parens"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#generics"
                },
                {
                    "begin": "(?=\\w.*\\s+\\w+\\s*\\()",
                    "end": "(?=\\s+\\w+\\s*\\()",
                    "name": "meta.method.return-type.java",
                    "patterns": [
                        {
                            "include": "#all-types"
                        },
                        {
                            "include": "#parens"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#throws"
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.method.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.method.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "module": {
            "begin": "((open)\\s)?(module)\\s+(\\w+)",
            "beginCaptures": {
                "1": {
                    "name": "storage.modifier.java"
                },
                "3": {
                    "name": "storage.modifier.java"
                },
                "4": {
                    "name": "entity.name.type.module.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.module.end.bracket.curly.java"
                }
            },
            "name": "meta.module.java",
            "patterns": [
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.module.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.module.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#comments-javadoc"
                        },
                        {
                            "match": "\\b(requires|transitive|exports|opens|to|uses|provides|with)\\b",
                            "name": "keyword.module.java"
                        }
                    ]
                }
            ]
        },
        "numbers": {
            "patterns": [
                {
                    "match": "\\b(?<!\\$)0(x|X)((?<!\\.)[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?[Ll]?(?!\\.)|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?\\.?|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)?\\.[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)[Pp][+-]?\\d([0-9_]*\\d)?[FfDd]?)\\b(?!\\$)",
                    "name": "constant.numeric.hex.java"
                },
                {
                    "match": "\\b(?<!\\$)0(b|B)[01]([01_]*[01])?[Ll]?\\b(?!\\$)",
                    "name": "constant.numeric.binary.java"
                },
                {
                    "match": "\\b(?<!\\$)0[0-7]([0-7_]*[0-7])?[Ll]?\\b(?!\\$)",
                    "name": "constant.numeric.octal.java"
                },
                {
                    "match": "(?<!\\$)(\\b\\d([0-9_]*\\d)?\\.\\B(?!\\.)|\\b\\d([0-9_]*\\d)?\\.([Ee][+-]?\\d([0-9_]*\\d)?)[FfDd]?\\b|\\b\\d([0-9_]*\\d)?\\.([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]\\b|\\b\\d([0-9_]*\\d)?\\.(\\d([0-9_]*\\d)?)([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]?\\b|(?<!\\.)\\B\\.\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]?\\b|\\b\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)[FfDd]?\\b|\\b\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]\\b|\\b(0|[1-9]([0-9_]*\\d)?)(?!\\.)[Ll]?\\b)(?!\\$)",
                    "name": "constant.numeric.decimal.java"
                }
            ]
        },
        "object-types": {
            "patterns": [
                {
                    "include": "#generics"
                },
                {
                    "begin": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*)([A-Z_]\\w*)\\s*(?=\\[)",
                    "beginCaptures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        },
                        "2": {
                            "name": "storage.type.object.array.java"
                        }
                    },
                    "end": "(?!\\s*\\[)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#parens"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        }
                    },
                    "match": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*[A-Z_]\\w*)\\s*(?=<)"
                },
                {
                    "captures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        }
                    },
                    "match": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*[A-Z_]\\w*)\\b((?=\\s*[A-Za-z$_\\n])|(?=\\s*\\.\\.\\.))"
                }
            ]
        },
        "object-types-inherited": {
            "patterns": [
                {
                    "include": "#generics"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        }
                    },
                    "match": "\\b(?:[A-Z]\\w*\\s*(\\.)\\s*)*[A-Z]\\w*\\b",
                    "name": "entity.other.inherited-class.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                }
            ]
        },
        "objects": {
            "match": "(?<![\\w$])[a-zA-Z_$][\\w$]*(?=\\s*\\.\\s*[\\w$]+)",
            "name": "variable.other.object.java"
        },
        "parameters": {
            "patterns": [
                {
                    "match": "\\bfinal\\b",
                    "name": "storage.modifier.java"
                },
                {
                    "include": "#annotations"
                },
                {
                    "include": "#all-types"
                },
                {
                    "include": "#strings"
                },
                {
                    "match": "\\w+",
                    "name": "variable.parameter.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.\\.\\.",
                    "name": "punctuation.definition.parameters.varargs.java"
                }
            ]
        },
        "parens": {
            "patterns": [
                {
                    "begin": "\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.round.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "\\[",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.square.java"
                        }
                    },
                    "end": "\\]",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.square.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.curly.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.curly.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "primitive-arrays": {
            "patterns": [
                {
                    "begin": "\\b(void|boolean|byte|char|short|int|float|long|double)\\b\\s*(?=\\[)",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.type.primitive.array.java"
                        }
                    },
                    "end": "(?!\\s*\\[)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#parens"
                        }
                    ]
                }
            ]
        },
        "primitive-types": {
            "match": "\\b(void|boolean|byte|char|short|int|float|long|double)\\b",
            "name": "storage.type.primitive.java"
        },
        "properties": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "keyword.control.new.java"
                        }
                    },
                    "match": "(\\.)\\s*(new)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "variable.other.object.property.java"
                        }
                    },
                    "match": "(\\.)\\s*([a-zA-Z_$][\\w$]*)(?=\\s*\\.\\s*[a-zA-Z_$][\\w$]*)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "variable.other.object.property.java"
                        }
                    },
                    "match": "(\\.)\\s*([a-zA-Z_$][\\w$]*)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "invalid.illegal.identifier.java"
                        }
                    },
                    "match": "(\\.)\\s*(\\d[\\w$]*)"
                }
            ]
        },
        "record": {
            "begin": "(?=\\w?[\\w\\s]*\\b(?:record)\\s+[\\w$]+)",
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.class.end.bracket.curly.java"
                }
            },
            "name": "meta.record.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                },
                {
                    "begin": "(record)\\s+([\\w$]+)(<[\\w$]+>)?(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.java"
                        },
                        "2": {
                            "name": "entity.name.type.record.java"
                        },
                        "3": {
                            "patterns": [
                                {
                                    "include": "#generics"
                                }
                            ]
                        },
                        "4": {
                            "name": "punctuation.definition.parameters.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.parameters.end.bracket.round.java"
                        }
                    },
                    "name": "meta.record.identifier.java",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "(implements)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?=\\s*\\{)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#record-body"
                }
            ]
        },
        "record-body": {
            "begin": "{",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.class.begin.bracket.curly.java"
                }
            },
            "end": "(?=})",
            "name": "meta.record.body.java",
            "patterns": [
                {
                    "include": "#record-constructor"
                },
                {
                    "include": "#class-body"
                }
            ]
        },
        "record-constructor": {
            "begin": "(?!new)(?=[\\w<].*\\s+)(?=([^(=/]|/(?!/))+(?={))",
            "end": "(})|(?=;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.section.method.end.bracket.curly.java"
                }
            },
            "name": "meta.method.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "begin": "(\\w+)",
                    "beginCaptures": {
                        "1": {
                            "name": "entity.name.function.java"
                        }
                    },
                    "end": "(?=\\s*{)",
                    "name": "meta.method.identifier.java",
                    "patterns": [
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#comments"
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.method.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.method.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "static-initializer": {
            "patterns": [
                {
                    "include": "#anonymous-block-and-instance-initializer"
                },
                {
                    "match": "static",
                    "name": "storage.modifier.java"
                }
            ]
        },
        "storage-modifiers": {
            "match": "\\b(public|private|protected|static|final|native|synchronized|abstract|threadsafe|transient|volatile|default|strictfp|sealed|non-sealed)\\b",
            "name": "storage.modifier.java"
        },
        "strings": {
            "patterns": [
                {
                    "begin": '"""',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": '"""',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.triple.java",
                    "patterns": [
                        {
                            "match": '(\\\\""")(?!")|(\\\\.)',
                            "name": "constant.character.escape.java"
                        }
                    ]
                },
                {
                    "begin": '"',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": '"',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.double.java",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character.escape.java"
                        }
                    ]
                },
                {
                    "begin": "'",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": "'",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.single.java",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character.escape.java"
                        }
                    ]
                }
            ]
        },
        "throws": {
            "begin": "throws",
            "beginCaptures": {
                "0": {
                    "name": "storage.modifier.java"
                }
            },
            "end": "(?={|;)",
            "name": "meta.throwables.java",
            "patterns": [
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "[a-zA-Z$_][\\.a-zA-Z0-9$_]*",
                    "name": "storage.type.java"
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "try-catch-finally": {
            "patterns": [
                {
                    "begin": "\\btry\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control.try.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.try.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.try.java",
                    "patterns": [
                        {
                            "begin": "\\(",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.resources.begin.bracket.round.java"
                                }
                            },
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.resources.end.bracket.round.java"
                                }
                            },
                            "name": "meta.try.resources.java",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.try.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "\\b(catch)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.catch.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.catch.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.catch.java",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "\\(",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                                }
                            },
                            "contentName": "meta.catch.parameters.java",
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#comments"
                                },
                                {
                                    "include": "#storage-modifiers"
                                },
                                {
                                    "begin": "[a-zA-Z$_][\\.a-zA-Z0-9$_]*",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "storage.type.java"
                                        }
                                    },
                                    "end": "(\\|)|(?=\\))",
                                    "endCaptures": {
                                        "1": {
                                            "name": "punctuation.catch.separator.java"
                                        }
                                    },
                                    "patterns": [
                                        {
                                            "include": "#comments"
                                        },
                                        {
                                            "captures": {
                                                "0": {
                                                    "name": "variable.parameter.java"
                                                }
                                            },
                                            "match": "\\w+"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.catch.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.catch.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "\\bfinally\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control.finally.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.finally.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.finally.java",
                    "patterns": [
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.finally.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.finally.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "variables": {
            "begin": "(?=\\b((void|boolean|byte|char|short|int|float|long|double)|(?>(\\w+\\.)*[A-Z_]+\\w*))\\b\\s*(<[\\w<>,\\.?\\s\\[\\]]*>)?\\s*((\\[\\])*)?\\s+[A-Za-z_$][\\w$]*([\\w\\[\\],$][\\w\\[\\],\\s]*)?\\s*(=|:|;))",
            "end": "(?==|:|;)",
            "name": "meta.definition.variable.java",
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.definition.java"
                        }
                    },
                    "match": "([A-Za-z$_][\\w$]*)(?=\\s*(\\[\\])*\\s*(;|:|=|,))"
                },
                {
                    "include": "#all-types"
                },
                {
                    "include": "#code"
                }
            ]
        },
        "variables-local": {
            "begin": "(?=\\b(var)\\b\\s+[A-Za-z_$][\\w$]*\\s*(=|:|;))",
            "end": "(?==|:|;)",
            "name": "meta.definition.variable.local.java",
            "patterns": [
                {
                    "match": "\\bvar\\b",
                    "name": "storage.type.local.java"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.definition.java"
                        }
                    },
                    "match": "([A-Za-z$_][\\w$]*)(?=\\s*(\\[\\])*\\s*(=|:|;))"
                },
                {
                    "include": "#code"
                }
            ]
        }
    },
    "scopeName": "source.java"
});
var java = [
    lang
];
;
}}),
"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/xml.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>xml)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$java$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/java.mjs [app-rsc] (ecmascript)");
;
const lang = Object.freeze({
    "displayName": "XML",
    "name": "xml",
    "patterns": [
        {
            "begin": "(<\\?)\\s*([-_a-zA-Z0-9]+)",
            "captures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "entity.name.tag.xml"
                }
            },
            "end": "(\\?>)",
            "name": "meta.tag.preprocessor.xml",
            "patterns": [
                {
                    "match": " ([a-zA-Z-]+)",
                    "name": "entity.other.attribute-name.xml"
                },
                {
                    "include": "#doublequotedString"
                },
                {
                    "include": "#singlequotedString"
                }
            ]
        },
        {
            "begin": "(<!)(DOCTYPE)\\s+([:a-zA-Z_][:a-zA-Z0-9_.-]*)",
            "captures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "keyword.other.doctype.xml"
                },
                "3": {
                    "name": "variable.language.documentroot.xml"
                }
            },
            "end": "\\s*(>)",
            "name": "meta.tag.sgml.doctype.xml",
            "patterns": [
                {
                    "include": "#internalSubset"
                }
            ]
        },
        {
            "include": "#comments"
        },
        {
            "begin": "(<)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(?=(\\s[^>]*)?></\\2>)",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "entity.name.tag.xml"
                },
                "3": {
                    "name": "entity.name.tag.namespace.xml"
                },
                "4": {
                    "name": "punctuation.separator.namespace.xml"
                },
                "5": {
                    "name": "entity.name.tag.localname.xml"
                }
            },
            "end": "(>)(</)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(>)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "punctuation.definition.tag.xml"
                },
                "3": {
                    "name": "entity.name.tag.xml"
                },
                "4": {
                    "name": "entity.name.tag.namespace.xml"
                },
                "5": {
                    "name": "punctuation.separator.namespace.xml"
                },
                "6": {
                    "name": "entity.name.tag.localname.xml"
                },
                "7": {
                    "name": "punctuation.definition.tag.xml"
                }
            },
            "name": "meta.tag.no-content.xml",
            "patterns": [
                {
                    "include": "#tagStuff"
                }
            ]
        },
        {
            "begin": "(</?)(?:([-\\w\\.]+)((:)))?([-\\w\\.:]+)",
            "captures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "entity.name.tag.namespace.xml"
                },
                "3": {
                    "name": "entity.name.tag.xml"
                },
                "4": {
                    "name": "punctuation.separator.namespace.xml"
                },
                "5": {
                    "name": "entity.name.tag.localname.xml"
                }
            },
            "end": "(/?>)",
            "name": "meta.tag.xml",
            "patterns": [
                {
                    "include": "#tagStuff"
                }
            ]
        },
        {
            "include": "#entity"
        },
        {
            "include": "#bare-ampersand"
        },
        {
            "begin": "<%@",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.embedded.begin.xml"
                }
            },
            "end": "%>",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.embedded.end.xml"
                }
            },
            "name": "source.java-props.embedded.xml",
            "patterns": [
                {
                    "match": "page|include|taglib",
                    "name": "keyword.other.page-props.xml"
                }
            ]
        },
        {
            "begin": "<%[!=]?(?!--)",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.embedded.begin.xml"
                }
            },
            "end": "(?!--)%>",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.embedded.end.xml"
                }
            },
            "name": "source.java.embedded.xml",
            "patterns": [
                {
                    "include": "source.java"
                }
            ]
        },
        {
            "begin": "<!\\[CDATA\\[",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.string.begin.xml"
                }
            },
            "end": "]]>",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.string.end.xml"
                }
            },
            "name": "string.unquoted.cdata.xml"
        }
    ],
    "repository": {
        "EntityDecl": {
            "begin": "(<!)(ENTITY)\\s+(%\\s+)?([:a-zA-Z_][:a-zA-Z0-9_.-]*)(\\s+(?:SYSTEM|PUBLIC)\\s+)?",
            "captures": {
                "1": {
                    "name": "punctuation.definition.tag.xml"
                },
                "2": {
                    "name": "keyword.other.entity.xml"
                },
                "3": {
                    "name": "punctuation.definition.entity.xml"
                },
                "4": {
                    "name": "variable.language.entity.xml"
                },
                "5": {
                    "name": "keyword.other.entitytype.xml"
                }
            },
            "end": "(>)",
            "patterns": [
                {
                    "include": "#doublequotedString"
                },
                {
                    "include": "#singlequotedString"
                }
            ]
        },
        "bare-ampersand": {
            "match": "&",
            "name": "invalid.illegal.bad-ampersand.xml"
        },
        "comments": {
            "patterns": [
                {
                    "begin": "<%--",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.xml"
                        },
                        "end": "--%>",
                        "name": "comment.block.xml"
                    }
                },
                {
                    "begin": "<!--",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.xml"
                        }
                    },
                    "end": "-->",
                    "name": "comment.block.xml",
                    "patterns": [
                        {
                            "begin": "--(?!>)",
                            "captures": {
                                "0": {
                                    "name": "invalid.illegal.bad-comments-or-CDATA.xml"
                                }
                            }
                        }
                    ]
                }
            ]
        },
        "doublequotedString": {
            "begin": '"',
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.string.begin.xml"
                }
            },
            "end": '"',
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.string.end.xml"
                }
            },
            "name": "string.quoted.double.xml",
            "patterns": [
                {
                    "include": "#entity"
                },
                {
                    "include": "#bare-ampersand"
                }
            ]
        },
        "entity": {
            "captures": {
                "1": {
                    "name": "punctuation.definition.constant.xml"
                },
                "3": {
                    "name": "punctuation.definition.constant.xml"
                }
            },
            "match": "(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#\\d+|#x[0-9a-fA-F]+)(;)",
            "name": "constant.character.entity.xml"
        },
        "internalSubset": {
            "begin": "(\\[)",
            "captures": {
                "1": {
                    "name": "punctuation.definition.constant.xml"
                }
            },
            "end": "(\\])",
            "name": "meta.internalsubset.xml",
            "patterns": [
                {
                    "include": "#EntityDecl"
                },
                {
                    "include": "#parameterEntity"
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "parameterEntity": {
            "captures": {
                "1": {
                    "name": "punctuation.definition.constant.xml"
                },
                "3": {
                    "name": "punctuation.definition.constant.xml"
                }
            },
            "match": "(%)([:a-zA-Z_][:a-zA-Z0-9_.-]*)(;)",
            "name": "constant.character.parameter-entity.xml"
        },
        "singlequotedString": {
            "begin": "'",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.string.begin.xml"
                }
            },
            "end": "'",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.string.end.xml"
                }
            },
            "name": "string.quoted.single.xml",
            "patterns": [
                {
                    "include": "#entity"
                },
                {
                    "include": "#bare-ampersand"
                }
            ]
        },
        "tagStuff": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name.namespace.xml"
                        },
                        "2": {
                            "name": "entity.other.attribute-name.xml"
                        },
                        "3": {
                            "name": "punctuation.separator.namespace.xml"
                        },
                        "4": {
                            "name": "entity.other.attribute-name.localname.xml"
                        }
                    },
                    "match": "(?:^|\\s+)(?:([-\\w.]+)((:)))?([-\\w.:]+)\\s*="
                },
                {
                    "include": "#doublequotedString"
                },
                {
                    "include": "#singlequotedString"
                }
            ]
        }
    },
    "scopeName": "text.xml",
    "embeddedLangs": [
        "java"
    ]
});
var xml = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$java$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    lang
];
;
}}),
"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/graphql.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>graphql)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$javascript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/javascript.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$typescript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/typescript.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$jsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/jsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$tsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/tsx.mjs [app-rsc] (ecmascript)");
;
;
;
;
const lang = Object.freeze({
    "displayName": "GraphQL",
    "fileTypes": [
        "graphql",
        "graphqls",
        "gql",
        "graphcool"
    ],
    "name": "graphql",
    "patterns": [
        {
            "include": "#graphql"
        }
    ],
    "repository": {
        "graphql": {
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-fragment-definition"
                },
                {
                    "include": "#graphql-directive-definition"
                },
                {
                    "include": "#graphql-type-interface"
                },
                {
                    "include": "#graphql-enum"
                },
                {
                    "include": "#graphql-scalar"
                },
                {
                    "include": "#graphql-union"
                },
                {
                    "include": "#graphql-schema"
                },
                {
                    "include": "#graphql-operation-def"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-ampersand": {
            "captures": {
                "1": {
                    "name": "keyword.operator.logical.graphql"
                }
            },
            "match": "\\s*(&)"
        },
        "graphql-arguments": {
            "begin": "\\s*(\\()",
            "beginCaptures": {
                "1": {
                    "name": "meta.brace.round.directive.graphql"
                }
            },
            "end": "\\s*(\\))",
            "endCaptures": {
                "1": {
                    "name": "meta.brace.round.directive.graphql"
                }
            },
            "name": "meta.arguments.graphql",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "begin": "\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\s*(:))",
                    "beginCaptures": {
                        "1": {
                            "name": "variable.parameter.graphql"
                        },
                        "2": {
                            "name": "punctuation.colon.graphql"
                        }
                    },
                    "end": "(?=\\s*(?:(?:([_A-Za-z][_0-9A-Za-z]*)\\s*(:))|\\)))|\\s*(,)",
                    "endCaptures": {
                        "3": {
                            "name": "punctuation.comma.graphql"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-directive"
                        },
                        {
                            "include": "#graphql-value"
                        },
                        {
                            "include": "#graphql-skip-newlines"
                        }
                    ]
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-boolean-value": {
            "captures": {
                "1": {
                    "name": "constant.language.boolean.graphql"
                }
            },
            "match": "\\s*\\b(true|false)\\b"
        },
        "graphql-colon": {
            "captures": {
                "1": {
                    "name": "punctuation.colon.graphql"
                }
            },
            "match": "\\s*(:)"
        },
        "graphql-comma": {
            "captures": {
                "1": {
                    "name": "punctuation.comma.graphql"
                }
            },
            "match": "\\s*(,)"
        },
        "graphql-comment": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.graphql"
                        }
                    },
                    "comment": "need to prefix comment space with a scope else Atom's reflow cmd doesn't work",
                    "match": "(\\s*)(#).*",
                    "name": "comment.line.graphql.js"
                },
                {
                    "begin": '(""")',
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.graphql"
                        }
                    },
                    "end": '(""")',
                    "name": "comment.line.graphql.js"
                },
                {
                    "begin": '(")',
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.graphql"
                        }
                    },
                    "end": '(")',
                    "name": "comment.line.graphql.js"
                }
            ]
        },
        "graphql-description-docstring": {
            "begin": '"""',
            "end": '"""',
            "name": "comment.block.graphql"
        },
        "graphql-description-singleline": {
            "match": '#(?=([^"]*"[^"]*")*[^"]*$).*$',
            "name": "comment.line.number-sign.graphql"
        },
        "graphql-directive": {
            "applyEndPatternLast": 1,
            "begin": "\\s*((@)\\s*([_A-Za-z][_0-9A-Za-z]*))",
            "beginCaptures": {
                "1": {
                    "name": "entity.name.function.directive.graphql"
                }
            },
            "end": "(?=.)",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-arguments"
                },
                {
                    "include": "#literal-quasi-embedded"
                },
                {
                    "include": "#graphql-skip-newlines"
                }
            ]
        },
        "graphql-directive-definition": {
            "applyEndPatternLast": 1,
            "begin": "\\s*(\\bdirective\\b)\\s*(@[_A-Za-z][_0-9A-Za-z]*)",
            "beginCaptures": {
                "1": {
                    "name": "keyword.directive.graphql"
                },
                "2": {
                    "name": "entity.name.function.directive.graphql"
                },
                "3": {
                    "name": "keyword.on.graphql"
                },
                "4": {
                    "name": "support.type.graphql"
                }
            },
            "end": "(?=.)",
            "patterns": [
                {
                    "include": "#graphql-variable-definitions"
                },
                {
                    "applyEndPatternLast": 1,
                    "begin": "\\s*(\\bon\\b)\\s*([_A-Za-z]*)",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.on.graphql"
                        },
                        "2": {
                            "name": "support.type.location.graphql"
                        }
                    },
                    "end": "(?=.)",
                    "patterns": [
                        {
                            "include": "#graphql-skip-newlines"
                        },
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#literal-quasi-embedded"
                        },
                        {
                            "captures": {
                                "2": {
                                    "name": "support.type.location.graphql"
                                }
                            },
                            "match": "\\s*(\\|)\\s*([_A-Za-z]*)"
                        }
                    ]
                },
                {
                    "include": "#graphql-skip-newlines"
                },
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-enum": {
            "begin": "\\s*+\\b(enum)\\b\\s*([_A-Za-z][_0-9A-Za-z]*)",
            "beginCaptures": {
                "1": {
                    "name": "keyword.enum.graphql"
                },
                "2": {
                    "name": "support.type.enum.graphql"
                }
            },
            "end": "(?<=})",
            "name": "meta.enum.graphql",
            "patterns": [
                {
                    "begin": "\\s*({)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.operation.graphql"
                        }
                    },
                    "end": "\\s*(})",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.operation.graphql"
                        }
                    },
                    "name": "meta.type.object.graphql",
                    "patterns": [
                        {
                            "include": "#graphql-object-type"
                        },
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-directive"
                        },
                        {
                            "include": "#graphql-enum-value"
                        },
                        {
                            "include": "#literal-quasi-embedded"
                        }
                    ]
                },
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-directive"
                }
            ]
        },
        "graphql-enum-value": {
            "match": "\\s*(?!=\\b(true|false|null)\\b)([_A-Za-z][_0-9A-Za-z]*)",
            "name": "constant.character.enum.graphql"
        },
        "graphql-field": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "string.unquoted.alias.graphql"
                        },
                        "2": {
                            "name": "punctuation.colon.graphql"
                        }
                    },
                    "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)\\s*(:)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.graphql"
                        }
                    },
                    "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)"
                },
                {
                    "include": "#graphql-arguments"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-selection-set"
                },
                {
                    "include": "#literal-quasi-embedded"
                },
                {
                    "include": "#graphql-skip-newlines"
                }
            ]
        },
        "graphql-float-value": {
            "captures": {
                "1": {
                    "name": "constant.numeric.float.graphql"
                }
            },
            "match": "\\s*(-?(0|[1-9]\\d*)(\\.\\d+)?((e|E)(\\+|-)?\\d+)?)"
        },
        "graphql-fragment-definition": {
            "begin": "\\s*(?:(\\bfragment\\b)\\s*([_A-Za-z][_0-9A-Za-z]*)?\\s*(?:(\\bon\\b)\\s*([_A-Za-z][_0-9A-Za-z]*)))",
            "captures": {
                "1": {
                    "name": "keyword.fragment.graphql"
                },
                "2": {
                    "name": "entity.name.fragment.graphql"
                },
                "3": {
                    "name": "keyword.on.graphql"
                },
                "4": {
                    "name": "support.type.graphql"
                }
            },
            "end": "(?<=})",
            "name": "meta.fragment.graphql",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-selection-set"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-skip-newlines"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-fragment-spread": {
            "applyEndPatternLast": 1,
            "begin": "\\s*(\\.\\.\\.)\\s*(?!\\bon\\b)([_A-Za-z][_0-9A-Za-z]*)",
            "captures": {
                "1": {
                    "name": "keyword.operator.spread.graphql"
                },
                "2": {
                    "name": "variable.fragment.graphql"
                }
            },
            "end": "(?=.)",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-selection-set"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#literal-quasi-embedded"
                },
                {
                    "include": "#graphql-skip-newlines"
                }
            ]
        },
        "graphql-ignore-spaces": {
            "match": "\\s*"
        },
        "graphql-inline-fragment": {
            "applyEndPatternLast": 1,
            "begin": "\\s*(\\.\\.\\.)\\s*(?:(\\bon\\b)\\s*([_A-Za-z][_0-9A-Za-z]*))?",
            "captures": {
                "1": {
                    "name": "keyword.operator.spread.graphql"
                },
                "2": {
                    "name": "keyword.on.graphql"
                },
                "3": {
                    "name": "support.type.graphql"
                }
            },
            "end": "(?=.)",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-selection-set"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-skip-newlines"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-input-types": {
            "patterns": [
                {
                    "include": "#graphql-scalar-type"
                },
                {
                    "captures": {
                        "1": {
                            "name": "support.type.graphql"
                        },
                        "2": {
                            "name": "keyword.operator.nulltype.graphql"
                        }
                    },
                    "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\s*(!))?"
                },
                {
                    "begin": "\\s*(\\[)",
                    "captures": {
                        "1": {
                            "name": "meta.brace.square.graphql"
                        },
                        "2": {
                            "name": "keyword.operator.nulltype.graphql"
                        }
                    },
                    "end": "\\s*(\\])(?:\\s*(!))?",
                    "name": "meta.type.list.graphql",
                    "patterns": [
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-input-types"
                        },
                        {
                            "include": "#graphql-comma"
                        },
                        {
                            "include": "#literal-quasi-embedded"
                        }
                    ]
                }
            ]
        },
        "graphql-list-value": {
            "patterns": [
                {
                    "begin": "\\s*+(\\[)",
                    "beginCaptures": {
                        "1": {
                            "name": "meta.brace.square.graphql"
                        }
                    },
                    "end": "\\s*(\\])",
                    "endCaptures": {
                        "1": {
                            "name": "meta.brace.square.graphql"
                        }
                    },
                    "name": "meta.listvalues.graphql",
                    "patterns": [
                        {
                            "include": "#graphql-value"
                        }
                    ]
                }
            ]
        },
        "graphql-name": {
            "captures": {
                "1": {
                    "name": "entity.name.function.graphql"
                }
            },
            "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)"
        },
        "graphql-null-value": {
            "captures": {
                "1": {
                    "name": "constant.language.null.graphql"
                }
            },
            "match": "\\s*\\b(null)\\b"
        },
        "graphql-object-field": {
            "captures": {
                "1": {
                    "name": "constant.object.key.graphql"
                },
                "2": {
                    "name": "string.unquoted.graphql"
                },
                "3": {
                    "name": "punctuation.graphql"
                }
            },
            "match": "\\s*(([_A-Za-z][_0-9A-Za-z]*))\\s*(:)"
        },
        "graphql-object-value": {
            "patterns": [
                {
                    "begin": "\\s*+({)",
                    "beginCaptures": {
                        "1": {
                            "name": "meta.brace.curly.graphql"
                        }
                    },
                    "end": "\\s*(})",
                    "endCaptures": {
                        "1": {
                            "name": "meta.brace.curly.graphql"
                        }
                    },
                    "name": "meta.objectvalues.graphql",
                    "patterns": [
                        {
                            "include": "#graphql-object-field"
                        },
                        {
                            "include": "#graphql-value"
                        }
                    ]
                }
            ]
        },
        "graphql-operation-def": {
            "patterns": [
                {
                    "include": "#graphql-query-mutation"
                },
                {
                    "include": "#graphql-name"
                },
                {
                    "include": "#graphql-variable-definitions"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-selection-set"
                }
            ]
        },
        "graphql-query-mutation": {
            "captures": {
                "1": {
                    "name": "keyword.operation.graphql"
                }
            },
            "match": "\\s*\\b(query|mutation)\\b"
        },
        "graphql-scalar": {
            "captures": {
                "1": {
                    "name": "keyword.scalar.graphql"
                },
                "2": {
                    "name": "entity.scalar.graphql"
                }
            },
            "match": "\\s*\\b(scalar)\\b\\s*([_A-Za-z][_0-9A-Za-z]*)"
        },
        "graphql-scalar-type": {
            "captures": {
                "1": {
                    "name": "support.type.builtin.graphql"
                },
                "2": {
                    "name": "keyword.operator.nulltype.graphql"
                }
            },
            "match": "\\s*\\b(Int|Float|String|Boolean|ID)\\b(?:\\s*(!))?"
        },
        "graphql-schema": {
            "begin": "\\s*\\b(schema)\\b",
            "beginCaptures": {
                "1": {
                    "name": "keyword.schema.graphql"
                }
            },
            "end": "(?<=})",
            "patterns": [
                {
                    "begin": "\\s*({)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.operation.graphql"
                        }
                    },
                    "end": "\\s*(})",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.operation.graphql"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\s*\\(|:)",
                            "beginCaptures": {
                                "1": {
                                    "name": "variable.arguments.graphql"
                                }
                            },
                            "end": "(?=\\s*(([_A-Za-z][_0-9A-Za-z]*)\\s*(\\(|:)|(})))|\\s*(,)",
                            "endCaptures": {
                                "5": {
                                    "name": "punctuation.comma.graphql"
                                }
                            },
                            "patterns": [
                                {
                                    "captures": {
                                        "1": {
                                            "name": "support.type.graphql"
                                        }
                                    },
                                    "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)"
                                },
                                {
                                    "include": "#graphql-comment"
                                },
                                {
                                    "include": "#graphql-description-docstring"
                                },
                                {
                                    "include": "#graphql-description-singleline"
                                },
                                {
                                    "include": "#graphql-colon"
                                },
                                {
                                    "include": "#graphql-skip-newlines"
                                }
                            ]
                        },
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-skip-newlines"
                        }
                    ]
                },
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-skip-newlines"
                }
            ]
        },
        "graphql-selection-set": {
            "begin": "\\s*({)",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.operation.graphql"
                }
            },
            "end": "\\s*(})",
            "endCaptures": {
                "1": {
                    "name": "punctuation.operation.graphql"
                }
            },
            "name": "meta.selectionset.graphql",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-field"
                },
                {
                    "include": "#graphql-fragment-spread"
                },
                {
                    "include": "#graphql-inline-fragment"
                },
                {
                    "include": "#graphql-comma"
                },
                {
                    "include": "#native-interpolation"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-skip-newlines": {
            "match": "\\s*\n"
        },
        "graphql-string-content": {
            "patterns": [
                {
                    "match": `\\\\[/'"\\\\nrtbf]`,
                    "name": "constant.character.escape.graphql"
                },
                {
                    "match": "\\\\u([0-9a-fA-F]{4})",
                    "name": "constant.character.escape.graphql"
                }
            ]
        },
        "graphql-string-value": {
            "begin": '\\s*+(("))',
            "beginCaptures": {
                "1": {
                    "name": "string.quoted.double.graphql"
                },
                "2": {
                    "name": "punctuation.definition.string.begin.graphql"
                }
            },
            "contentName": "string.quoted.double.graphql",
            "end": '\\s*+(?:(("))|(\n))',
            "endCaptures": {
                "1": {
                    "name": "string.quoted.double.graphql"
                },
                "2": {
                    "name": "punctuation.definition.string.end.graphql"
                },
                "3": {
                    "name": "invalid.illegal.newline.graphql"
                }
            },
            "patterns": [
                {
                    "include": "#graphql-string-content"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-type-definition": {
            "begin": "\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\s*\\(|:)",
            "beginCaptures": {
                "1": {
                    "name": "variable.graphql"
                }
            },
            "comment": "key (optionalArgs): Type",
            "end": "(?=\\s*(([_A-Za-z][_0-9A-Za-z]*)\\s*(\\(|:)|(})))|\\s*(,)",
            "endCaptures": {
                "5": {
                    "name": "punctuation.comma.graphql"
                }
            },
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-variable-definitions"
                },
                {
                    "include": "#graphql-type-object"
                },
                {
                    "include": "#graphql-colon"
                },
                {
                    "include": "#graphql-input-types"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-type-interface": {
            "applyEndPatternLast": 1,
            "begin": "\\s*\\b(?:(extends?)?\\b\\s*\\b(type)|(interface)|(input))\\b\\s*([_A-Za-z][_0-9A-Za-z]*)?",
            "captures": {
                "1": {
                    "name": "keyword.type.graphql"
                },
                "2": {
                    "name": "keyword.type.graphql"
                },
                "3": {
                    "name": "keyword.interface.graphql"
                },
                "4": {
                    "name": "keyword.input.graphql"
                },
                "5": {
                    "name": "support.type.graphql"
                }
            },
            "end": "(?=.)",
            "name": "meta.type.interface.graphql",
            "patterns": [
                {
                    "begin": "\\s*\\b(implements)\\b\\s*",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.implements.graphql"
                        }
                    },
                    "end": "\\s*(?={)",
                    "patterns": [
                        {
                            "captures": {
                                "1": {
                                    "name": "support.type.graphql"
                                }
                            },
                            "match": "\\s*([_A-Za-z][_0-9A-Za-z]*)"
                        },
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-directive"
                        },
                        {
                            "include": "#graphql-ampersand"
                        },
                        {
                            "include": "#graphql-comma"
                        }
                    ]
                },
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-type-object"
                },
                {
                    "include": "#literal-quasi-embedded"
                },
                {
                    "include": "#graphql-ignore-spaces"
                }
            ]
        },
        "graphql-type-object": {
            "begin": "\\s*({)",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.operation.graphql"
                }
            },
            "end": "\\s*(})",
            "endCaptures": {
                "1": {
                    "name": "punctuation.operation.graphql"
                }
            },
            "name": "meta.type.object.graphql",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-object-type"
                },
                {
                    "include": "#graphql-type-definition"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-union": {
            "applyEndPatternLast": 1,
            "begin": "\\s*\\b(union)\\b\\s*([_A-Za-z][_0-9A-Za-z]*)",
            "captures": {
                "1": {
                    "name": "keyword.union.graphql"
                },
                "2": {
                    "name": "support.type.graphql"
                }
            },
            "end": "(?=.)",
            "patterns": [
                {
                    "applyEndPatternLast": 1,
                    "begin": "\\s*(=)\\s*([_A-Za-z][_0-9A-Za-z]*)",
                    "captures": {
                        "1": {
                            "name": "punctuation.assignment.graphql"
                        },
                        "2": {
                            "name": "support.type.graphql"
                        }
                    },
                    "end": "(?=.)",
                    "patterns": [
                        {
                            "include": "#graphql-comment"
                        },
                        {
                            "include": "#graphql-description-docstring"
                        },
                        {
                            "include": "#graphql-description-singleline"
                        },
                        {
                            "include": "#graphql-skip-newlines"
                        },
                        {
                            "include": "#literal-quasi-embedded"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "punctuation.or.graphql"
                                },
                                "2": {
                                    "name": "support.type.graphql"
                                }
                            },
                            "match": "\\s*(\\|)\\s*([_A-Za-z][_0-9A-Za-z]*)"
                        }
                    ]
                },
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-skip-newlines"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-union-mark": {
            "captures": {
                "1": {
                    "name": "punctuation.union.graphql"
                }
            },
            "match": "\\s*(\\|)"
        },
        "graphql-value": {
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-variable-name"
                },
                {
                    "include": "#graphql-float-value"
                },
                {
                    "include": "#graphql-string-value"
                },
                {
                    "include": "#graphql-boolean-value"
                },
                {
                    "include": "#graphql-null-value"
                },
                {
                    "include": "#graphql-enum-value"
                },
                {
                    "include": "#graphql-list-value"
                },
                {
                    "include": "#graphql-object-value"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-variable-assignment": {
            "applyEndPatternLast": 1,
            "begin": "\\s(=)",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.assignment.graphql"
                }
            },
            "end": "(?=[\n,)])",
            "patterns": [
                {
                    "include": "#graphql-value"
                }
            ]
        },
        "graphql-variable-definition": {
            "begin": "\\s*(\\$?[_A-Za-z][_0-9A-Za-z]*)(?=\\s*\\(|:)",
            "beginCaptures": {
                "1": {
                    "name": "variable.parameter.graphql"
                }
            },
            "comment": "variable: type = value,.... which may be a list",
            "end": "(?=\\s*((\\$?[_A-Za-z][_0-9A-Za-z]*)\\s*(\\(|:)|(}|\\))))|\\s*(,)",
            "endCaptures": {
                "5": {
                    "name": "punctuation.comma.graphql"
                }
            },
            "name": "meta.variables.graphql",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-directive"
                },
                {
                    "include": "#graphql-colon"
                },
                {
                    "include": "#graphql-input-types"
                },
                {
                    "include": "#graphql-variable-assignment"
                },
                {
                    "include": "#literal-quasi-embedded"
                },
                {
                    "include": "#graphql-skip-newlines"
                }
            ]
        },
        "graphql-variable-definitions": {
            "begin": "\\s*(\\()",
            "captures": {
                "1": {
                    "name": "meta.brace.round.graphql"
                }
            },
            "end": "\\s*(\\))",
            "patterns": [
                {
                    "include": "#graphql-comment"
                },
                {
                    "include": "#graphql-description-docstring"
                },
                {
                    "include": "#graphql-description-singleline"
                },
                {
                    "include": "#graphql-variable-definition"
                },
                {
                    "include": "#literal-quasi-embedded"
                }
            ]
        },
        "graphql-variable-name": {
            "captures": {
                "1": {
                    "name": "variable.graphql"
                }
            },
            "match": "\\s*(\\$[_A-Za-z][_0-9A-Za-z]*)"
        },
        "native-interpolation": {
            "begin": "\\s*(\\${)",
            "beginCaptures": {
                "1": {
                    "name": "keyword.other.substitution.begin"
                }
            },
            "end": "(})",
            "endCaptures": {
                "1": {
                    "name": "keyword.other.substitution.end"
                }
            },
            "name": "native.interpolation",
            "patterns": [
                {
                    "include": "source.js"
                },
                {
                    "include": "source.ts"
                },
                {
                    "include": "source.js.jsx"
                },
                {
                    "include": "source.tsx"
                }
            ]
        }
    },
    "scopeName": "source.graphql",
    "embeddedLangs": [
        "javascript",
        "typescript",
        "jsx",
        "tsx"
    ],
    "aliases": [
        "gql"
    ]
});
var graphql = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$javascript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$typescript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$jsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$tsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    lang
];
;
}}),
"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/http.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>http)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$shellscript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/shellscript.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$json$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/json.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$xml$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/xml.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$graphql$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/graphql.mjs [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
const lang = Object.freeze({
    "displayName": "HTTP",
    "fileTypes": [
        "http",
        "rest"
    ],
    "name": "http",
    "patterns": [
        {
            "begin": "^\\s*(?=curl)",
            "end": "^\\s*(\\#{3,}.*?)?\\s*$",
            "endCaptures": {
                "0": {
                    "name": "comment.line.sharp.http"
                }
            },
            "name": "http.request.curl",
            "patterns": [
                {
                    "include": "source.shell"
                }
            ]
        },
        {
            "begin": "\\s*(?=(\\[|{[^{]))",
            "end": "^\\s*(\\#{3,}.*?)?\\s*$",
            "endCaptures": {
                "0": {
                    "name": "comment.line.sharp.http"
                }
            },
            "name": "http.request.body.json",
            "patterns": [
                {
                    "include": "source.json"
                }
            ]
        },
        {
            "begin": "^\\s*(?=<\\S)",
            "end": "^\\s*(\\#{3,}.*?)?\\s*$",
            "endCaptures": {
                "0": {
                    "name": "comment.line.sharp.http"
                }
            },
            "name": "http.request.body.xml",
            "patterns": [
                {
                    "include": "text.xml"
                }
            ]
        },
        {
            "begin": "\\s*(?=(query|mutation))",
            "end": "^\\s*(\\#{3,}.*?)?\\s*$",
            "endCaptures": {
                "0": {
                    "name": "comment.line.sharp.http"
                }
            },
            "name": "http.request.body.graphql",
            "patterns": [
                {
                    "include": "source.graphql"
                }
            ]
        },
        {
            "begin": "\\s*(?=(query|mutation))",
            "end": "^\\{\\s*$",
            "name": "http.request.body.graphql",
            "patterns": [
                {
                    "include": "source.graphql"
                }
            ]
        },
        {
            "include": "#metadata"
        },
        {
            "include": "#comments"
        },
        {
            "captures": {
                "1": {
                    "name": "keyword.other.http"
                },
                "2": {
                    "name": "variable.other.http"
                },
                "3": {
                    "name": "string.other.http"
                }
            },
            "match": "^\\s*(@)([^\\s=]+)\\s*=\\s*(.*?)\\s*$",
            "name": "http.filevariable"
        },
        {
            "captures": {
                "1": {
                    "name": "keyword.operator.http"
                },
                "2": {
                    "name": "variable.other.http"
                },
                "3": {
                    "name": "string.other.http"
                }
            },
            "match": "^\\s*(\\?|&)([^=\\s]+)=(.*)$",
            "name": "http.query"
        },
        {
            "captures": {
                "1": {
                    "name": "entity.name.tag.http"
                },
                "2": {
                    "name": "keyword.other.http"
                },
                "3": {
                    "name": "string.other.http"
                }
            },
            "match": "^([\\w\\-]+)\\s*(:)\\s*([^/].*?)\\s*$",
            "name": "http.headers"
        },
        {
            "include": "#request-line"
        },
        {
            "include": "#response-line"
        }
    ],
    "repository": {
        "comments": {
            "patterns": [
                {
                    "match": "^\\s*\\#{1,}.*$",
                    "name": "comment.line.sharp.http"
                },
                {
                    "match": "^\\s*\\/{2,}.*$",
                    "name": "comment.line.double-slash.http"
                }
            ]
        },
        "metadata": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        },
                        "3": {
                            "name": "entity.name.type.http"
                        }
                    },
                    "match": "^\\s*\\#{1,}\\s+(?:((@)name)\\s+([^\\s\\.]+))$",
                    "name": "comment.line.sharp.http"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        },
                        "3": {
                            "name": "entity.name.type.http"
                        }
                    },
                    "match": "^\\s*\\/{2,}\\s+(?:((@)name)\\s+([^\\s\\.]+))$",
                    "name": "comment.line.double-slash.http"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        }
                    },
                    "match": "^\\s*\\#{1,}\\s+((@)note)\\s*$",
                    "name": "comment.line.sharp.http"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        }
                    },
                    "match": "^\\s*\\/{2,}\\s+((@)note)\\s*$",
                    "name": "comment.line.double-slash.http"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        },
                        "3": {
                            "name": "variable.other.http"
                        },
                        "4": {
                            "name": "string.other.http"
                        }
                    },
                    "match": "^\\s*\\#{1,}\\s+(?:((@)prompt)\\s+([^\\s]+)(?:\\s+(.*))?\\s*)$",
                    "name": "comment.line.sharp.http"
                },
                {
                    "captures": {
                        "1": {
                            "name": "entity.other.attribute-name"
                        },
                        "2": {
                            "name": "punctuation.definition.block.tag.metadata"
                        },
                        "3": {
                            "name": "variable.other.http"
                        },
                        "4": {
                            "name": "string.other.http"
                        }
                    },
                    "match": "^\\s*\\/{2,}\\s+(?:((@)prompt)\\s+([^\\s]+)(?:\\s+(.*))?\\s*)$",
                    "name": "comment.line.double-slash.http"
                }
            ]
        },
        "protocol": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "keyword.other.http"
                        },
                        "2": {
                            "name": "constant.numeric.http"
                        }
                    },
                    "match": "(HTTP)/(\\d+.\\d+)",
                    "name": "http.version"
                }
            ]
        },
        "request-line": {
            "captures": {
                "1": {
                    "name": "keyword.control.http"
                },
                "2": {
                    "name": "const.language.http"
                },
                "3": {
                    "patterns": [
                        {
                            "include": "#protocol"
                        }
                    ]
                }
            },
            "match": "(?i)^(?:(get|post|put|delete|patch|head|options|connect|trace|lock|unlock|propfind|proppatch|copy|move|mkcol|mkcalendar|acl|search)\\s+)?\\s*(.+?)(?:\\s+(HTTP\\/\\S+))?$",
            "name": "http.requestline"
        },
        "response-line": {
            "captures": {
                "1": {
                    "patterns": [
                        {
                            "include": "#protocol"
                        }
                    ]
                },
                "2": {
                    "name": "constant.numeric.http"
                },
                "3": {
                    "name": "string.other.http"
                }
            },
            "match": "(?i)^\\s*(HTTP\\/\\S+)\\s([1-5]\\d\\d)\\s(.*)$",
            "name": "http.responseLine"
        }
    },
    "scopeName": "source.http",
    "embeddedLangs": [
        "shellscript",
        "json",
        "xml",
        "graphql"
    ]
});
var http = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$shellscript$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$json$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$xml$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$shiki$40$1$2e$17$2e$7$2f$node_modules$2f$shiki$2f$dist$2f$langs$2f$graphql$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"],
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_79933b17._.js.map