module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/matlab.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>matlab)
});
const lang = Object.freeze({
    "displayName": "MATLAB",
    "fileTypes": [
        "m"
    ],
    "name": "matlab",
    "patterns": [
        {
            "comment": "This and #all_after_command_dual are split out so #command_dual can be excluded in things like (), {}, []",
            "include": "#all_before_command_dual"
        },
        {
            "include": "#command_dual"
        },
        {
            "include": "#all_after_command_dual"
        }
    ],
    "repository": {
        "all_after_command_dual": {
            "patterns": [
                {
                    "include": "#string"
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "include": "#comments"
                },
                {
                    "include": "#conjugate_transpose"
                },
                {
                    "include": "#transpose"
                },
                {
                    "include": "#constants"
                },
                {
                    "include": "#variables"
                },
                {
                    "include": "#numbers"
                },
                {
                    "include": "#operators"
                }
            ]
        },
        "all_before_command_dual": {
            "patterns": [
                {
                    "include": "#classdef"
                },
                {
                    "include": "#function"
                },
                {
                    "include": "#blocks"
                },
                {
                    "include": "#control_statements"
                },
                {
                    "include": "#global_persistent"
                },
                {
                    "include": "#parens"
                },
                {
                    "include": "#square_brackets"
                },
                {
                    "include": "#indexing_curly_brackets"
                },
                {
                    "include": "#curly_brackets"
                }
            ]
        },
        "blocks": {
            "patterns": [
                {
                    "begin": "\\s*(?:^|[\\s,;])(for)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.for.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.for.matlab"
                        }
                    },
                    "name": "meta.for.matlab",
                    "patterns": [
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(if)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.if.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.if.matlab"
                        },
                        "2": {
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        }
                    },
                    "name": "meta.if.matlab",
                    "patterns": [
                        {
                            "captures": {
                                "2": {
                                    "name": "keyword.control.elseif.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "end": "^",
                            "match": "(\\s*)(?:^|[\\s,;])(elseif)\\b(.*)$\\n?",
                            "name": "meta.elseif.matlab"
                        },
                        {
                            "captures": {
                                "2": {
                                    "name": "keyword.control.else.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "end": "^",
                            "match": "(\\s*)(?:^|[\\s,;])(else)\\b(.*)?$\\n?",
                            "name": "meta.else.matlab"
                        },
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(parfor)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.for.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.for.matlab"
                        }
                    },
                    "name": "meta.parfor.matlab",
                    "patterns": [
                        {
                            "begin": "\\G(?!$)",
                            "end": "$\\n?",
                            "name": "meta.parfor-quantity.matlab",
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(spmd)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.spmd.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.spmd.matlab"
                        }
                    },
                    "name": "meta.spmd.matlab",
                    "patterns": [
                        {
                            "begin": "\\G(?!$)",
                            "end": "$\\n?",
                            "name": "meta.spmd-statement.matlab",
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(switch)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.switch.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.switch.matlab"
                        }
                    },
                    "name": "meta.switch.matlab",
                    "patterns": [
                        {
                            "captures": {
                                "2": {
                                    "name": "keyword.control.case.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "end": "^",
                            "match": "(\\s*)(?:^|[\\s,;])(case)\\b(.*)$\\n?",
                            "name": "meta.case.matlab"
                        },
                        {
                            "captures": {
                                "2": {
                                    "name": "keyword.control.otherwise.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "end": "^",
                            "match": "(\\s*)(?:^|[\\s,;])(otherwise)\\b(.*)?$\\n?",
                            "name": "meta.otherwise.matlab"
                        },
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(try)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.try.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.try.matlab"
                        }
                    },
                    "name": "meta.try.matlab",
                    "patterns": [
                        {
                            "captures": {
                                "2": {
                                    "name": "keyword.control.catch.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "end": "^",
                            "match": "(\\s*)(?:^|[\\s,;])(catch)\\b(.*)?$\\n?",
                            "name": "meta.catch.matlab"
                        },
                        {
                            "include": "$self"
                        }
                    ]
                },
                {
                    "begin": "\\s*(?:^|[\\s,;])(while)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.while.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.while.matlab"
                        }
                    },
                    "name": "meta.while.matlab",
                    "patterns": [
                        {
                            "include": "$self"
                        }
                    ]
                }
            ]
        },
        "braced_validator_list": {
            "begin": "\\s*({)\\s*",
            "beginCaptures": {
                "1": {
                    "name": "storage.type.matlab"
                }
            },
            "comment": "Validator functions. Treated as a recursive group to permit nested brackets, quotes, etc.",
            "end": "(})",
            "endCaptures": {
                "1": {
                    "name": "storage.type.matlab"
                }
            },
            "patterns": [
                {
                    "include": "#braced_validator_list"
                },
                {
                    "include": "#validator_strings"
                },
                {
                    "include": "#line_continuation"
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.type.matlab"
                        }
                    },
                    "match": `([^{}}'"\\.]+)`
                },
                {
                    "match": "\\.",
                    "name": "storage.type.matlab"
                }
            ]
        },
        "classdef": {
            "patterns": [
                {
                    "begin": "(^\\s*)(classdef)\\b\\s*(.*)",
                    "beginCaptures": {
                        "2": {
                            "name": "storage.type.class.matlab"
                        },
                        "3": {
                            "patterns": [
                                {
                                    "captures": {
                                        "1": {
                                            "patterns": [
                                                {
                                                    "match": "[a-zA-Z]\\w*",
                                                    "name": "variable.parameter.class.matlab"
                                                },
                                                {
                                                    "begin": "=\\s*",
                                                    "end": ",|(?=\\))",
                                                    "patterns": [
                                                        {
                                                            "match": "true|false",
                                                            "name": "constant.language.boolean.matlab"
                                                        },
                                                        {
                                                            "include": "#string"
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        "2": {
                                            "name": "meta.class-declaration.matlab"
                                        },
                                        "3": {
                                            "name": "entity.name.section.class.matlab"
                                        },
                                        "4": {
                                            "name": "keyword.operator.other.matlab"
                                        },
                                        "5": {
                                            "patterns": [
                                                {
                                                    "match": "[a-zA-Z]\\w*(\\.[a-zA-Z]\\w*)*",
                                                    "name": "entity.other.inherited-class.matlab"
                                                },
                                                {
                                                    "match": "&",
                                                    "name": "keyword.operator.other.matlab"
                                                }
                                            ]
                                        },
                                        "6": {
                                            "patterns": [
                                                {
                                                    "include": "$self"
                                                }
                                            ]
                                        }
                                    },
                                    "match": "(\\([^)]*\\))?\\s*(([a-zA-Z]\\w*)(?:\\s*(<)\\s*([^%]*))?)\\s*($|(?=(%|...)).*)"
                                }
                            ]
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.class.matlab"
                        }
                    },
                    "name": "meta.class.matlab",
                    "patterns": [
                        {
                            "begin": "(^\\s*)(properties)\\b([^%]*)\\s*(\\([^)]*\\))?\\s*($|(?=%))",
                            "beginCaptures": {
                                "2": {
                                    "name": "keyword.control.properties.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "match": "[a-zA-Z]\\w*",
                                            "name": "variable.parameter.properties.matlab"
                                        },
                                        {
                                            "begin": "=\\s*",
                                            "end": ",|(?=\\))",
                                            "patterns": [
                                                {
                                                    "match": "true|false",
                                                    "name": "constant.language.boolean.matlab"
                                                },
                                                {
                                                    "match": "public|protected|private",
                                                    "name": "constant.language.access.matlab"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            },
                            "end": "\\s*(?:^|[\\s,;])(end)\\b",
                            "endCaptures": {
                                "1": {
                                    "name": "keyword.control.end.properties.matlab"
                                }
                            },
                            "name": "meta.properties.matlab",
                            "patterns": [
                                {
                                    "include": "#validators"
                                },
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "begin": "(^\\s*)(methods)\\b([^%]*)\\s*(\\([^)]*\\))?\\s*($|(?=%))",
                            "beginCaptures": {
                                "2": {
                                    "name": "keyword.control.methods.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "match": "[a-zA-Z]\\w*",
                                            "name": "variable.parameter.methods.matlab"
                                        },
                                        {
                                            "begin": "=\\s*",
                                            "end": ",|(?=\\))",
                                            "patterns": [
                                                {
                                                    "match": "true|false",
                                                    "name": "constant.language.boolean.matlab"
                                                },
                                                {
                                                    "match": "public|protected|private",
                                                    "name": "constant.language.access.matlab"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            },
                            "end": "\\s*(?:^|[\\s,;])(end)\\b",
                            "endCaptures": {
                                "1": {
                                    "name": "keyword.control.end.methods.matlab"
                                }
                            },
                            "name": "meta.methods.matlab",
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "begin": "(^\\s*)(events)\\b([^%]*)\\s*(\\([^)]*\\))?\\s*($|(?=%))",
                            "beginCaptures": {
                                "2": {
                                    "name": "keyword.control.events.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "match": "[a-zA-Z]\\w*",
                                            "name": "variable.parameter.events.matlab"
                                        },
                                        {
                                            "begin": "=\\s*",
                                            "end": ",|(?=\\))",
                                            "patterns": [
                                                {
                                                    "match": "true|false",
                                                    "name": "constant.language.boolean.matlab"
                                                },
                                                {
                                                    "match": "public|protected|private",
                                                    "name": "constant.language.access.matlab"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            },
                            "end": "\\s*(?:^|[\\s,;])(end)\\b",
                            "endCaptures": {
                                "1": {
                                    "name": "keyword.control.end.events.matlab"
                                }
                            },
                            "name": "meta.events.matlab",
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "begin": "(^\\s*)(enumeration)\\b([^%]*)\\s*($|(?=%))",
                            "beginCaptures": {
                                "2": {
                                    "name": "keyword.control.enumeration.matlab"
                                }
                            },
                            "end": "\\s*(?:^|[\\s,;])(end)\\b",
                            "endCaptures": {
                                "1": {
                                    "name": "keyword.control.end.enumeration.matlab"
                                }
                            },
                            "name": "meta.enumeration.matlab",
                            "patterns": [
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "include": "$self"
                        }
                    ]
                }
            ]
        },
        "command_dual": {
            "captures": {
                "1": {
                    "name": "string.interpolated.matlab"
                },
                "2": {
                    "name": "variable.other.command.matlab"
                },
                "28": {
                    "name": "comment.line.percentage.matlab"
                }
            },
            "comment": "  1        2                                  3                                                    4                                                  5                                                    6                                                         7                                                                             8                 9                            10                   11                      12                         13                   14                                                               1516       17                                                             18                                                                                                                               19                      20                                     21                                    22                                      23                                         24                                         25                                                                         26            27                 28",
            "match": `^\\s*(([b-df-hk-moq-zA-HJ-MO-Z]\\w*|a|an|a([A-Za-mo-z0-9_]\\w*|n[A-Za-rt-z0-9_]\\w*|ns\\w+)|e|ep|e([A-Za-oq-z0-9_]\\w*|p[A-Za-rt-z0-9_]\\w*|ps\\w+)|in|i([A-Za-mo-z0-9_]\\w*|n[A-Za-eg-z0-9_]\\w*|nf\\w+)|I|In|I([A-Za-mo-z0-9_]\\w*|n[A-Za-eg-z0-9_]\\w*|nf\\w+)|j\\w+|N|Na|N([A-Zb-z0-9_]\\w*|a[A-MO-Za-z0-9_]\\w*|aN\\w+)|n|na|nar|narg|nargi|nargo|nargou|n([A-Zb-z0-9_]\\w*|a([A-Za-mo-qs-z0-9_]\\w*|n\\w+|r([A-Za-fh-z0-9_]\\w*|g([A-Za-hj-nq-z0-9_]\\w*|i([A-Za-mo-z0-9_]\\w*|n\\w+)|o([A-Za-tv-z0-9_]\\w*|u([A-Za-su-z]\\w*|t\\w+))))))|p|p[A-Za-hj-z0-9_]\\w*|pi\\w+)\\s+((([^\\s;,%()=.{&|~<>:+\\-*/\\\\@^'"]|(?=')|(?="))|(\\.\\^|\\.\\*|\\./|\\.\\\\|\\.'|\\.\\(|&&|==|\\|\\||&(?=[^&])|\\|(?=[^\\|])|~=|<=|>=|~(?!=)|<(?!=)|>(?!=)|:|\\+|-|\\*|/|\\\\|@|\\^)([^\\s]|\\s*(?=%)|\\s+$|\\s+(,|;|\\)|}|\\]|&|\\||<|>|=|:|\\*|/|\\\\|\\^|@|(\\.[^\\d.]|\\.\\.[^.])))|(\\.[^^*/\\\\'(\\sA-Za-z]))([^%]|'[^']*'|"[^"]*")*|(\\.(?=\\s)|\\.[A-Za-z]|(?={))([^(=\\'"%]|==|'[^']*'|"[^"]*"|\\(|\\([^)%]*\\)|\\[|\\[[^\\]%]*\\]|{|{[^}%]*})*(\\.\\.\\.[^%]*)?((?=%)|$)))(%.*)?$`
        },
        "comment_block": {
            "begin": "(^[\\s]*)%\\{[^\\n\\S]*+\\n",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.definition.comment.matlab"
                }
            },
            "end": "^[\\s]*%\\}[^\\n\\S]*+(?:\\n|$)",
            "name": "comment.block.percentage.matlab",
            "patterns": [
                {
                    "include": "#comment_block"
                },
                {
                    "match": "^[^\\n]*\\n"
                }
            ]
        },
        "comments": {
            "patterns": [
                {
                    "begin": "(^[ \\t]+)?(?=%%\\s)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.matlab"
                        }
                    },
                    "end": "(?!\\G)",
                    "patterns": [
                        {
                            "begin": "%%",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.comment.matlab"
                                }
                            },
                            "end": "\\n",
                            "name": "comment.line.double-percentage.matlab",
                            "patterns": [
                                {
                                    "begin": "\\G[^\\S\\n]*(?![\\n\\s])",
                                    "contentName": "meta.cell.matlab",
                                    "end": "(?=\\n)"
                                }
                            ]
                        }
                    ]
                },
                {
                    "include": "#comment_block"
                },
                {
                    "begin": "(^[ \\t]+)?(?=%)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.matlab"
                        }
                    },
                    "end": "(?!\\G)",
                    "patterns": [
                        {
                            "begin": "%",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.comment.matlab"
                                }
                            },
                            "end": "\\n",
                            "name": "comment.line.percentage.matlab"
                        }
                    ]
                }
            ]
        },
        "conjugate_transpose": {
            "match": "((?<=[^\\s])|(?<=\\])|(?<=\\))|(?<=\\}))'",
            "name": "keyword.operator.transpose.matlab"
        },
        "constants": {
            "comment": "MATLAB Constants",
            "match": "(?<!\\.)\\b(eps|false|Inf|inf|intmax|intmin|namelengthmax|NaN|nan|on|off|realmax|realmin|true|pi)\\b",
            "name": "constant.language.matlab"
        },
        "control_statements": {
            "captures": {
                "1": {
                    "name": "keyword.control.matlab"
                }
            },
            "match": "\\s*(?:^|[\\s,;])(break|continue|return)\\b",
            "name": "meta.control.matlab"
        },
        "curly_brackets": {
            "begin": "\\{",
            "comment": "We don't include $self here to avoid matching command syntax inside (), [], {}",
            "end": "\\}",
            "patterns": [
                {
                    "include": "#end_in_parens"
                },
                {
                    "include": "#all_before_command_dual"
                },
                {
                    "include": "#all_after_command_dual"
                },
                {
                    "include": "#end_in_parens"
                },
                {
                    "comment": "These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written",
                    "include": "#block_keywords"
                }
            ]
        },
        "end_in_parens": {
            "comment": "end as operator symbol",
            "match": "\\bend\\b",
            "name": "keyword.operator.symbols.matlab"
        },
        "function": {
            "patterns": [
                {
                    "begin": "(^\\s*)(function)\\s+(?:(?:(\\[)([^\\]]*)(\\])|([a-zA-Z]\\w*))\\s*=\\s*)?([a-zA-Z]\\w*(\\.[a-zA-Z]\\w*)*)\\s*",
                    "beginCaptures": {
                        "2": {
                            "name": "storage.type.function.matlab"
                        },
                        "3": {
                            "name": "punctuation.definition.arguments.begin.matlab"
                        },
                        "4": {
                            "patterns": [
                                {
                                    "match": "\\w+",
                                    "name": "variable.parameter.output.matlab"
                                }
                            ]
                        },
                        "5": {
                            "name": "punctuation.definition.arguments.end.matlab"
                        },
                        "6": {
                            "name": "variable.parameter.output.function.matlab"
                        },
                        "7": {
                            "name": "entity.name.function.matlab"
                        }
                    },
                    "end": "\\s*(?:^|[\\s,;])(end)\\b(\\s*\\n)?",
                    "endCaptures": {
                        "1": {
                            "name": "keyword.control.end.function.matlab"
                        }
                    },
                    "name": "meta.function.matlab",
                    "patterns": [
                        {
                            "begin": "\\G\\(",
                            "end": "\\)",
                            "name": "meta.arguments.function.matlab",
                            "patterns": [
                                {
                                    "include": "#line_continuation"
                                },
                                {
                                    "match": "\\w+",
                                    "name": "variable.parameter.input.matlab"
                                }
                            ]
                        },
                        {
                            "begin": "(^\\s*)(arguments)\\b([^%]*)\\s*(\\([^)]*\\))?\\s*($|(?=%))",
                            "beginCaptures": {
                                "2": {
                                    "name": "keyword.control.arguments.matlab"
                                },
                                "3": {
                                    "patterns": [
                                        {
                                            "match": "[a-zA-Z]\\w*",
                                            "name": "variable.parameter.arguments.matlab"
                                        }
                                    ]
                                }
                            },
                            "end": "\\s*(?:^|[\\s,;])(end)\\b",
                            "endCaptures": {
                                "1": {
                                    "name": "keyword.control.end.arguments.matlab"
                                }
                            },
                            "name": "meta.arguments.matlab",
                            "patterns": [
                                {
                                    "include": "#validators"
                                },
                                {
                                    "include": "$self"
                                }
                            ]
                        },
                        {
                            "include": "$self"
                        }
                    ]
                }
            ]
        },
        "global_persistent": {
            "captures": {
                "1": {
                    "name": "keyword.control.globalpersistent.matlab"
                }
            },
            "match": "^\\s*(global|persistent)\\b",
            "name": "meta.globalpersistent.matlab"
        },
        "indexing_curly_brackets": {
            "Comment": "Match identifier{idx, idx, } and stop at newline without ... This helps with partially written code like x{idx ",
            "begin": "([a-zA-Z][a-zA-Z0-9_\\.]*\\s*)\\{",
            "beginCaptures": {
                "1": {
                    "patterns": [
                        {
                            "include": "$self"
                        }
                    ]
                }
            },
            "comment": "We don't include $self here to avoid matching command syntax inside (), [], {}",
            "end": "(\\}|(?<!\\.\\.\\.).\\n)",
            "patterns": [
                {
                    "include": "#end_in_parens"
                },
                {
                    "include": "#all_before_command_dual"
                },
                {
                    "include": "#all_after_command_dual"
                },
                {
                    "include": "#end_in_parens"
                },
                {
                    "comment": "These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written",
                    "include": "#block_keywords"
                }
            ]
        },
        "line_continuation": {
            "captures": {
                "1": {
                    "name": "keyword.operator.symbols.matlab"
                },
                "2": {
                    "name": "comment.line.continuation.matlab"
                }
            },
            "comment": "Line continuations",
            "match": "(\\.\\.\\.)(.*)$",
            "name": "meta.linecontinuation.matlab"
        },
        "numbers": {
            "comment": "Valid numbers: 1, .1, 1.1, .1e1, 1.1e1, 1e1, 1i, 1j, 1e2j",
            "match": "(?<=[\\s\\-+\\*\\/\\\\=:\\[({,]|^)\\d*\\.?\\d+([eE][+-]?\\d)?([0-9&&[^\\.]])*(i|j)?\\b",
            "name": "constant.numeric.matlab"
        },
        "operators": {
            "comment": "Operator symbols",
            "match": "(?<=\\s)(==|~=|>|>=|<|<=|&|&&|:|\\||\\|\\||\\+|-|\\*|\\.\\*|/|\\./|\\\\|\\.\\\\|\\^|\\.\\^)(?=\\s)",
            "name": "keyword.operator.symbols.matlab"
        },
        "parens": {
            "begin": "\\(",
            "comment": "We don't include $self here to avoid matching command syntax inside (), [], {}",
            "end": "(\\)|(?<!\\.\\.\\.).\\n)",
            "patterns": [
                {
                    "include": "#end_in_parens"
                },
                {
                    "include": "#all_before_command_dual"
                },
                {
                    "include": "#all_after_command_dual"
                },
                {
                    "comment": "These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written",
                    "include": "#block_keywords"
                }
            ]
        },
        "square_brackets": {
            "begin": "\\[",
            "comment": "We don't include $self here to avoid matching command syntax inside (), [], {}",
            "end": "\\]",
            "patterns": [
                {
                    "include": "#all_before_command_dual"
                },
                {
                    "include": "#all_after_command_dual"
                },
                {
                    "comment": "These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written",
                    "include": "#block_keywords"
                }
            ]
        },
        "string": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "string.interpolated.matlab"
                        },
                        "2": {
                            "name": "punctuation.definition.string.begin.matlab"
                        }
                    },
                    "comment": "Shell command",
                    "match": "^\\s*((!).*$\\n?)"
                },
                {
                    "begin": "((?<=(\\[|\\(|\\{|=|\\s|;|:|,|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^))|^)'",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.matlab"
                        }
                    },
                    "comment": "Character vector literal (single-quoted)",
                    "end": "'(?=(\\[|\\(|\\{|\\]|\\)|\\}|=|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^|\\s|;|:|,))",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.matlab"
                        }
                    },
                    "name": "string.quoted.single.matlab",
                    "patterns": [
                        {
                            "match": "''",
                            "name": "constant.character.escape.matlab"
                        },
                        {
                            "match": "'(?=.)",
                            "name": "invalid.illegal.unescaped-quote.matlab"
                        },
                        {
                            "comment": "Operator symbols",
                            "match": "((\\%([+\\-0]?\\d{0,3}(\\.\\d{1,3})?)(c|d|e|E|f|g|G|s|((b|t)?(o|u|x|X))))|\\%\\%|\\\\(b|f|n|r|t|\\\\))",
                            "name": "constant.character.escape.matlab"
                        }
                    ]
                },
                {
                    "begin": '((?<=(\\[|\\(|\\{|=|\\s|;|:|,|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^))|^)"',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.matlab"
                        }
                    },
                    "comment": "String literal (double-quoted)",
                    "end": '"(?=(\\[|\\(|\\{|\\]|\\)|\\}|=|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^|\\||\\s|;|:|,))',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.matlab"
                        }
                    },
                    "name": "string.quoted.double.matlab",
                    "patterns": [
                        {
                            "match": '""',
                            "name": "constant.character.escape.matlab"
                        },
                        {
                            "match": '"(?=.)',
                            "name": "invalid.illegal.unescaped-quote.matlab"
                        }
                    ]
                }
            ]
        },
        "transpose": {
            "match": "\\.'",
            "name": "keyword.operator.transpose.matlab"
        },
        "validator_strings": {
            "comment": "Simplified string patterns nested inside validator functions which don't change scopes of matches.",
            "patterns": [
                {
                    "patterns": [
                        {
                            "begin": "((?<=(\\[|\\(|\\{|=|\\s|;|:|,|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^))|^)'",
                            "comment": "Character vector literal (single-quoted)",
                            "end": "'(?=(\\[|\\(|\\{|\\]|\\)|\\}|=|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^|\\s|;|:|,))",
                            "name": "storage.type.matlab",
                            "patterns": [
                                {
                                    "match": "''"
                                },
                                {
                                    "match": "'(?=.)"
                                },
                                {
                                    "match": "([^']+)"
                                }
                            ]
                        },
                        {
                            "begin": '((?<=(\\[|\\(|\\{|=|\\s|;|:|,|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^))|^)"',
                            "comment": "String literal (double-quoted)",
                            "end": '"(?=(\\[|\\(|\\{|\\]|\\)|\\}|=|~|<|>|&|\\||-|\\+|\\*|/|\\\\|\\.|\\^|\\||\\s|;|:|,))',
                            "name": "storage.type.matlab",
                            "patterns": [
                                {
                                    "match": '""'
                                },
                                {
                                    "match": '"(?=.)'
                                },
                                {
                                    "match": '[^"]+'
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "validators": {
            "begin": "\\s*[;]?\\s*([a-zA-Z][a-zA-Z0-9_\\.?]*)",
            "comment": "Property and argument validation. Match an identifier allowing . and ?.",
            "end": "([;\\n%=].*)",
            "endCaptures": {
                "1": {
                    "patterns": [
                        {
                            "captures": {
                                "1": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "comment": "Match comments",
                            "match": "([%].*)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "patterns": [
                                        {
                                            "include": "$self"
                                        }
                                    ]
                                }
                            },
                            "comment": "Handle things like arg = val; nextArg",
                            "match": "(=[^;]*)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "patterns": [
                                        {
                                            "include": "#validators"
                                        }
                                    ]
                                }
                            },
                            "comment": "End of property/argument patterns which start a new property/argument. Look for beginning of identifier after semicolon. Otherwise treat as regular code.",
                            "match": "([\\n;]\\s*[a-zA-Z].*)"
                        },
                        {
                            "include": "$self"
                        }
                    ]
                }
            },
            "patterns": [
                {
                    "include": "#line_continuation"
                },
                {
                    "comment": "Size declaration",
                    "match": "\\s*(\\([^)]*\\))",
                    "name": "storage.type.matlab"
                },
                {
                    "comment": "Type declaration",
                    "match": "([a-zA-Z][a-zA-Z0-9_\\.]*)",
                    "name": "storage.type.matlab"
                },
                {
                    "include": "#braced_validator_list"
                }
            ]
        },
        "variables": {
            "comment": "MATLAB variables",
            "match": "(?<!\\.)\\b(nargin|nargout|varargin|varargout)\\b",
            "name": "variable.other.function.matlab"
        }
    },
    "scopeName": "source.matlab"
});
var matlab = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_matlab_mjs_cfc54317._.js.map