(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[449],{2821:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportWebVitalsWithPath=void 0;let r=n(44344),i=n(4319),o=r.config.getWebVitalsEndpoint(),a=(0,i.throttle)(function(){let e=JSON.stringify(r.config.wrapWebVitalsObject(s)),t={"Content-Type":"application/json","User-Agent":"next-logtail/v"+r.Version};r.config.token&&(t.Authorization=`Bearer ${r.config.token}`);let n={body:e,method:"POST",keepalive:!0,headers:t};function i(){fetch(o,n).catch(console.error)}if(r.isBrowser&&r.isVercel&&navigator.sendBeacon)try{navigator.sendBeacon.bind(navigator)(o,e)}catch(e){i()}else i();s=[]},1e3),s=[];t.reportWebVitalsWithPath=function(e,t){s.push(Object.assign({route:t},e)),r.config.isEnvVarsSet()&&a()}},4319:function(e,t,n){"use strict";var r,i,o=n(37811),a=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{u(r.next(e))}catch(e){o(e)}}function s(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}u((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.throttle=t.requestToJSON=t.EndpointType=t.isNoPrettyPrint=void 0,t.isNoPrettyPrint="true"==o.env.BETTER_STACK_NO_PRETTY_PRINT,(r=i||(t.EndpointType=i={})).webVitals="web-vitals",r.logs="logs",t.requestToJSON=function(e){var t;return a(this,void 0,void 0,function*(){let n,r,i,o,a={};e.headers.forEach((e,t)=>{a[t]=e});let s={};if("cookies"in e)e.cookies.getAll().forEach(e=>{s[e.name]=e.value});else{let e=a.cookie;e&&(s=Object.fromEntries(e.split(";").map(e=>{let[t,n]=e.trim().split("=");return[t,n]})))}if("nextUrl"in e){let t=e.nextUrl;n={basePath:t.basePath,buildId:t.buildId,hash:t.hash,host:t.host,hostname:t.hostname,href:t.href,origin:t.origin,password:t.password,pathname:t.pathname,port:t.port,protocol:t.protocol,search:t.search,searchParams:Object.fromEntries(t.searchParams.entries()),username:t.username}}if(e.body)try{let n=e.clone();try{r=yield n.json(),null==(t=n.body)||t.getReader}catch(e){r=yield n.text()}}catch(e){console.warn("Could not parse request body:",e)}let u={mode:e.cache,credentials:e.credentials,redirect:e.redirect,referrerPolicy:e.referrerPolicy,integrity:e.integrity};return"ip"in e&&(i=e.ip),"geo"in e&&(o=e.geo),{method:e.method,url:e.url,headers:a,cookies:s,nextUrl:n,ip:i,geo:o,body:r,cache:u,mode:e.mode,destination:e.destination,referrer:e.referrer,keepalive:e.keepalive,signal:{aborted:e.signal.aborted,reason:e.signal.reason}}})},t.throttle=(e,t)=>{let n,r;return function(){let i=this,o=arguments;null==r&&(r=Date.now()),clearTimeout(n),n=setTimeout(()=>{Date.now()-r>=t&&(e.apply(i,o),r=Date.now())},Math.max(t-(Date.now()-r),0))}}},5234:(e,t,n)=>{e.exports=n(40089)},6350:e=>{"use strict";e.exports={version:"0.2.0"}},25632:e=>{!function(){"use strict";var t={};t.d=function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},void 0!==t&&(t.ab="//");var n={};t.r(n),t.d(n,{CLSThresholds:function(){return O},FCPThresholds:function(){return P},FIDThresholds:function(){return ee},INPThresholds:function(){return W},LCPThresholds:function(){return x},TTFBThresholds:function(){return K},onCLS:function(){return R},onFCP:function(){return L},onFID:function(){return et},onINP:function(){return G},onLCP:function(){return X},onTTFB:function(){return Y}});var r,i,o,a,s,u=-1,c=function(e){addEventListener("pageshow",function(t){t.persisted&&(u=t.timeStamp,e(t))},!0)},l=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},f=function(){var e=l();return e&&e.activationStart||0},d=function(e,t){var n=l(),r="navigate";return u>=0?r="back-forward-cache":n&&(document.prerendering||f()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:r}},p=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},v=function(e,t,n,r){var i,o;return function(a){var s;t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,s=t.value,t.rating=s>n[1]?"poor":s>n[0]?"needs-improvement":"good",e(t))}},h=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},m=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},E=function(e){var t=!1;return function(){t||(e(),t=!0)}},_=-1,T=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){"hidden"===document.visibilityState&&_>-1&&(_="visibilitychange"===e.type?e.timeStamp:0,b())},y=function(){addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},b=function(){removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},C=function(){return _<0&&(_=T(),y(),c(function(){setTimeout(function(){_=T(),y()},0)})),{get firstHiddenTime(){return _}}},I=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},P=[1800,3e3],L=function(e,t){t=t||{},I(function(){var n,r=C(),i=d("FCP"),o=p("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-f(),0),i.entries.push(e),n(!0)))})});o&&(n=v(e,i,P,t.reportAllChanges),c(function(r){n=v(e,i=d("FCP"),P,t.reportAllChanges),h(function(){i.value=performance.now()-r.timeStamp,n(!0)})}))})},O=[.1,.25],R=function(e,t){t=t||{},L(E(function(){var n,r=d("CLS",0),i=0,o=[],a=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}}),i>r.value&&(r.value=i,r.entries=o,n())},s=p("layout-shift",a);s&&(n=v(e,r,O,t.reportAllChanges),m(function(){a(s.takeRecords()),n(!0)}),c(function(){i=0,n=v(e,r=d("CLS",0),O,t.reportAllChanges),h(function(){return n()})}),setTimeout(n,0))}))},S=0,N=1/0,w=0,V=function(e){e.forEach(function(e){e.interactionId&&(N=Math.min(N,e.interactionId),S=(w=Math.max(w,e.interactionId))?(w-N)/7+1:0)})},U=function(){"interactionCount"in performance||r||(r=p("event",V,{type:"event",buffered:!0,durationThreshold:0}))},B=[],M=new Map,D=0,k=[],j=function(e){if(k.forEach(function(t){return t(e)}),e.interactionId||"first-input"===e.entryType){var t=B[B.length-1],n=M.get(e.interactionId);if(n||B.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};M.set(r.id,r),B.push(r)}B.sort(function(e,t){return t.latency-e.latency}),B.length>10&&B.splice(10).forEach(function(e){return M.delete(e.id)})}}},A=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=E(e),"hidden"===document.visibilityState?e():(n=t(e),m(e)),n},W=[200,500],G=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},I(function(){U();var n,i,o=d("INP"),a=function(e){A(function(){e.forEach(j);var t,n=(t=Math.min(B.length-1,Math.floor(((r?S:performance.interactionCount||0)-D)/50)),B[t]);n&&n.latency!==o.value&&(o.value=n.latency,o.entries=n.entries,i())})},s=p("event",a,{durationThreshold:null!=(n=t.durationThreshold)?n:40});i=v(e,o,W,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),m(function(){a(s.takeRecords()),i(!0)}),c(function(){D=0,B.length=0,M.clear(),i=v(e,o=d("INP"),W,t.reportAllChanges)}))}))},x=[2500,4e3],F={},X=function(e,t){t=t||{},I(function(){var n,r=C(),i=d("LCP"),o=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach(function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-f(),0),i.entries=[e],n())})},a=p("largest-contentful-paint",o);if(a){n=v(e,i,x,t.reportAllChanges);var s=E(function(){F[i.id]||(o(a.takeRecords()),a.disconnect(),F[i.id]=!0,n(!0))});["keydown","click"].forEach(function(e){addEventListener(e,function(){return A(s)},!0)}),m(s),c(function(r){n=v(e,i=d("LCP"),x,t.reportAllChanges),h(function(){i.value=performance.now()-r.timeStamp,F[i.id]=!0,n(!0)})})}})},K=[800,1800],H=function e(t){document.prerendering?I(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},Y=function(e,t){t=t||{};var n=d("TTFB"),r=v(e,n,K,t.reportAllChanges);H(function(){var i=l();i&&(n.value=Math.max(i.responseStart-f(),0),n.entries=[i],r(!0),c(function(){(r=v(e,n=d("TTFB",0),K,t.reportAllChanges))(!0)}))})},q={passive:!0,capture:!0},J=new Date,$=function(e,t){i||(i=t,o=e,a=new Date,Z(removeEventListener),z())},z=function(){if(o>=0&&o<a-J){var e={entryType:"first-input",name:i.type,target:i.target,cancelable:i.cancelable,startTime:i.timeStamp,processingStart:i.timeStamp+o};s.forEach(function(t){t(e)}),s=[]}},Q=function(e){if(e.cancelable){var t,n,r,i=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){$(i,e),r()},n=function(){r()},r=function(){removeEventListener("pointerup",t,q),removeEventListener("pointercancel",n,q)},addEventListener("pointerup",t,q),addEventListener("pointercancel",n,q)):$(i,e)}},Z=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,Q,q)})},ee=[100,300],et=function(e,t){t=t||{},I(function(){var n,r=C(),a=d("FID"),u=function(e){e.startTime<r.firstHiddenTime&&(a.value=e.processingStart-e.startTime,a.entries.push(e),n(!0))},l=function(e){e.forEach(u)},f=p("first-input",l);n=v(e,a,ee,t.reportAllChanges),f&&(m(E(function(){l(f.takeRecords()),f.disconnect()})),c(function(){n=v(e,a=d("FID"),ee,t.reportAllChanges),s=[],o=-1,i=null,Z(addEventListener),s.push(u),z()}))})};e.exports=n}()},35062:(e,t,n)=>{"use strict";n.r(t);var r=n(43914),i={};for(let e in r)"default"!==e&&(i[e]=()=>r[e]);n.d(t,i)},40089:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useReportWebVitals",{enumerable:!0,get:function(){return o}});let r=n(50628),i=n(25632);function o(e){(0,r.useEffect)(()=>{(0,i.onCLS)(e),(0,i.onFID)(e),(0,i.onLCP)(e),(0,i.onINP)(e),(0,i.onFCP)(e),(0,i.onTTFB)(e)},[e])}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41912:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useReportWebVitals=t.BetterStackWebVitals=void 0;let r=n(35062),i=n(5234),o=n(2821),a=n(50628);var s=n(43432);Object.defineProperty(t,"BetterStackWebVitals",{enumerable:!0,get:function(){return s.BetterStackWebVitals}}),t.useReportWebVitals=function(e){let t=(0,r.usePathname)(),n=(0,a.useRef)(e||t);"string"==typeof e&&e!==n.current?n.current=t:"string"==typeof e&&e===n.current&&(n.current=e);let s=(0,a.useCallback)(e=>(0,o.reportWebVitalsWithPath)(e,n.current),[]);(0,i.useReportWebVitals)(s)}},43432:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BetterStackWebVitals=void 0;let r=function(e){return e&&e.__esModule?e:{default:e}}(n(50628)),i=n(41912);t.BetterStackWebVitals=function(e){let{path:t}=e;return(0,i.useReportWebVitals)(t),r.default.createElement(r.default.Fragment,null)}},44344:function(e,t,n){"use strict";var r=n(37811),i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.config=t.isEdgeRuntime=t.isBrowser=t.isWebWorker=t.isNetlify=t.isVercel=t.isVercelIntegration=t.Version=void 0;let o=i(n(83707)),a=i(n(44357)),s=i(n(48531));t.Version=n(6350).version,t.isVercelIntegration=r.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL||r.env.BETTER_STACK_INGEST_ENDPOINT,t.isVercel=r.env.NEXT_PUBLIC_VERCEL||r.env.VERCEL,t.isNetlify="true"==r.env.NETLIFY,t.isWebWorker="undefined"!=typeof self&&void 0!==globalThis.WorkerGlobalScope&&self instanceof WorkerGlobalScope,t.isBrowser="undefined"!=typeof window||t.isWebWorker,t.isEdgeRuntime=!!globalThis.EdgeRuntime;let u=new o.default;t.config=u,t.isVercel?t.config=u=new a.default:t.isNetlify&&(t.config=u=new s.default)},44357:function(e,t,n){"use strict";var r=n(37811),i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let o=i(n(83707));class a extends o.default{constructor(){super(...arguments),this.shouldSendEdgeReport=!0,this.region=r.env.VERCEL_REGION||void 0,this.environment=r.env.VERCEL_ENV||"production"}wrapWebVitalsObject(e){return e.map(e=>({webVital:e,dt:new Date().getTime(),vercel:{environment:this.environment,source:"web-vital",deploymentId:r.env.VERCEL_DEPLOYMENT_ID,deploymentUrl:r.env.NEXT_PUBLIC_VERCEL_URL,project:r.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,git:{commit:r.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,repo:r.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,ref:r.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF}}}))}injectPlatformMetadata(e,t){e.vercel={environment:this.environment,region:this.region,source:t,deploymentId:r.env.VERCEL_DEPLOYMENT_ID,deploymentUrl:r.env.NEXT_PUBLIC_VERCEL_URL,project:r.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,git:{commit:r.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,repo:r.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,ref:r.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF}}}}t.default=a},48531:function(e,t,n){"use strict";var r=n(37811),i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let o=i(n(83707)),a=r.env.SITE_ID,s=r.env.BUILD_ID,u=r.env.CONTEXT,c=r.env.DEPLOYMENT_URL,l=r.env.DEPLOYMENT_ID;class f extends o.default{wrapWebVitalsObject(e){return e.map(e=>({webVital:e,dt:new Date().getTime(),netlify:{environment:this.environment,source:"web-vital",siteId:a,buildId:s,context:u,deploymentUrl:c,deploymentId:l}}))}injectPlatformMetadata(e,t){e.netlify={environment:this.environment,region:"edge"===t?r.env.DENO_REGION:r.env.AWS_REGION,source:t,siteId:a,buildId:s,context:u,deploymentUrl:c,deploymentId:"edge"===t?r.env.DENO_DEPLOYMENT_ID:l}}}t.default=f},83707:(e,t,n)=>{"use strict";var r=n(37811);Object.defineProperty(t,"__esModule",{value:!0});let i=n(4319),o=n(44344);class a{constructor(){this.proxyPath="/_betterstack",this.shouldSendEdgeReport=!1,this.token=r.env.NEXT_PUBLIC_BETTER_STACK_SOURCE_TOKEN||r.env.BETTER_STACK_SOURCE_TOKEN||r.env.NEXT_PUBLIC_LOGTAIL_SOURCE_TOKEN||r.env.LOGTAIL_SOURCE_TOKEN,this.environment="production",this.ingestingUrl=r.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL||r.env.BETTER_STACK_INGESTING_URL||r.env.NEXT_PUBLIC_LOGTAIL_URL||r.env.LOGTAIL_URL,this.region=r.env.REGION||void 0,this.customEndpoint=r.env.NEXT_PUBLIC_BETTER_STACK_CUSTOM_ENDPOINT}isEnvVarsSet(){return!!(this.ingestingUrl&&this.token)||!!this.customEndpoint}getIngestURL(e){return this.ingestingUrl||""}getLogsEndpoint(){return o.isBrowser&&this.customEndpoint?this.customEndpoint:o.isBrowser?`${this.proxyPath}/logs`:this.getIngestURL(i.EndpointType.logs)}getWebVitalsEndpoint(){return o.isBrowser&&this.customEndpoint?this.customEndpoint:o.isBrowser?`${this.proxyPath}/web-vitals`:this.getIngestURL(i.EndpointType.webVitals)}wrapWebVitalsObject(e){return e.map(e=>({webVital:e,dt:new Date().getTime(),platform:{environment:this.environment,source:"web-vital"},source:"web-vital"}))}injectPlatformMetadata(e,t){e.source=t,e.platform={environment:this.environment,region:this.region,source:t}}getHeaderOrDefault(e,t,n){return e.headers[t]?e.headers[t]:n}}t.default=a}}]);