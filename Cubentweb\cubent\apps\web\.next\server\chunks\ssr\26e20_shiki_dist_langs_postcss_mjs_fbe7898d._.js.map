{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/postcss.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PostCSS\", \"fileTypes\": [\"pcss\", \"postcss\"], \"foldingStartMarker\": \"/\\\\*|^#|^\\\\*|^\\\\b|^\\\\.\", \"foldingStopMarker\": \"\\\\*/|^\\\\s*$\", \"name\": \"postcss\", \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.postcss\", \"patterns\": [{ \"include\": \"#comment-tag\" }] }, { \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#placeholder-selector\" }, { \"include\": \"#variable\" }, { \"include\": \"#variable-root-css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#dotdotdot\" }, { \"begin\": \"@include\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.postcss\" } }, \"end\": \"(?=\\\\n|\\\\(|{|;)\", \"name\": \"support.function.name.postcss.library\" }, { \"begin\": \"@mixin|@function\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.postcss\" } }, \"end\": \"$\\\\n?|(?=\\\\(|{)\", \"name\": \"support.function.name.postcss.no-completions\", \"patterns\": [{ \"match\": \"[\\\\w-]+\", \"name\": \"entity.name.function\" }] }, { \"match\": \"(?<=@import)\\\\s[\\\\w/.*-]+\", \"name\": \"string.quoted.double.css.postcss\" }, { \"begin\": \"@\", \"end\": \"$\\\\n?|\\\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\\\s|,))|(?=;)\", \"name\": \"keyword.control.at-rule.css.postcss\" }, { \"begin\": \"#\", \"end\": \"$\\\\n?|(?=\\\\s|,|;|\\\\(|\\\\)|\\\\.|\\\\[|{|>)\", \"name\": \"entity.other.attribute-name.id.css.postcss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\.|(?<=&)(-|_)\", \"end\": \"$\\\\n?|(?=\\\\s|,|;|\\\\(|\\\\)|\\\\[|{|>)\", \"name\": \"entity.other.attribute-name.class.css.postcss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"name\": \"entity.other.attribute-selector.postcss\", \"patterns\": [{ \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"match\": \"\\\\^|\\\\$|\\\\*|~\", \"name\": \"keyword.other.regex.postcss\" }] }, { \"match\": \"(?<=\\\\]|\\\\)|not\\\\(|\\\\*|>|>\\\\s):[a-z:-]+|(::|:-)[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.postcss\" }, { \"begin\": \":\", \"end\": \"$\\\\n?|(?=;|\\\\s\\\\(|and\\\\(|{|}|\\\\),)\", \"name\": \"meta.property-list.css.postcss\", \"patterns\": [{ \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#variable\" }, { \"include\": \"#rgb-value\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"include\": \"#function-content-var\" }, { \"include\": \"#operator\" }, { \"include\": \"#parent-selector\" }, { \"include\": \"#property-value\" }] }, { \"include\": \"#rgb-value\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"begin\": \"(?<!-|\\\\()\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|x)\\\\b(?!-|\\\\)|:\\\\s)|&\", \"end\": \"(?=\\\\s|,|;|\\\\(|\\\\)|\\\\.|\\\\[|{|>|-|_)\", \"name\": \"entity.name.tag.css.postcss.symbol\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"include\": \"#operator\" }, { \"match\": \"[a-z-]+((?=:|#{))\", \"name\": \"support.type.property-name.css.postcss\" }, { \"include\": \"#reserved-words\" }, { \"include\": \"#property-value\" }], \"repository\": { \"comment-tag\": { \"begin\": \"{{\", \"end\": \"}}\", \"name\": \"comment.tags.postcss\", \"patterns\": [{ \"match\": \"[\\\\w-]+\", \"name\": \"comment.tag.postcss\" }] }, \"dotdotdot\": { \"match\": \"\\\\.{3}\", \"name\": \"variable.other\" }, \"double-quoted\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.css.postcss\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"double-slash\": { \"begin\": \"//\", \"end\": \"$\", \"name\": \"comment.line.postcss\", \"patterns\": [{ \"include\": \"#comment-tag\" }] }, \"flag\": { \"match\": \"!(important|default|optional|global)\", \"name\": \"keyword.other.important.css.postcss\" }, \"function\": { \"match\": \"(?<=[\\\\s|(|,|:])(?!url|format|attr)[\\\\w-][\\\\w-]*(?=\\\\()\", \"name\": \"support.function.name.postcss\" }, \"function-content\": { \"match\": \"(?<=url\\\\(|format\\\\(|attr\\\\().+?(?=\\\\))\", \"name\": \"string.quoted.double.css.postcss\" }, \"function-content-var\": { \"match\": \"(?<=var\\\\()[\\\\w-]+(?=\\\\))\", \"name\": \"variable.parameter.postcss\" }, \"interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.postcss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }] }, \"numeric\": { \"match\": \"(-|\\\\.)?\\\\d+(\\\\.\\\\d+)?\", \"name\": \"constant.numeric.css.postcss\" }, \"operator\": { \"match\": \"\\\\+|\\\\s-\\\\s|\\\\s-(?=\\\\$)|(?<=\\\\()-(?=\\\\$)|\\\\s-(?=\\\\()|\\\\*|/|%|=|!|<|>|~\", \"name\": \"keyword.operator.postcss\" }, \"parent-selector\": { \"match\": \"&\", \"name\": \"entity.name.tag.css.postcss\" }, \"placeholder-selector\": { \"begin\": \"(?<!\\\\d)%(?!\\\\d)\", \"end\": \"$\\\\n?|\\\\s|(?=;|{)\", \"name\": \"entity.other.attribute-name.placeholder-selector.postcss\" }, \"property-value\": { \"match\": \"[\\\\w-]+\", \"name\": \"meta.property-value.css.postcss, support.constant.property-value.css.postcss\" }, \"pseudo-class\": { \"match\": \":[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.postcss\" }, \"quoted-interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.postcss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }] }, \"reserved-words\": { \"match\": \"\\\\b(false|from|in|not|null|through|to|true)\\\\b\", \"name\": \"support.type.property-name.css.postcss\" }, \"rgb-value\": { \"match\": \"(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\\\\b\", \"name\": \"constant.other.color.rgb-value.css.postcss\" }, \"single-quoted\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.css.postcss\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"unit\": { \"match\": \"(?<=[\\\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|%)\", \"name\": \"keyword.other.unit.css.postcss\" }, \"variable\": { \"match\": \"\\\\$[\\\\w-]+\", \"name\": \"variable.parameter.postcss\" }, \"variable-root-css\": { \"match\": \"(?<!&)--[\\\\w-]+\", \"name\": \"variable.parameter.postcss\" } }, \"scopeName\": \"source.css.postcss\" });\nvar postcss = [\n  lang\n];\n\nexport { postcss as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAQ;KAAU;IAAE,sBAAsB;IAA0B,qBAAqB;IAAe,QAAQ;IAAW,YAAY;QAAC;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,SAAS;YAAY,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAmB,QAAQ;QAAwC;QAAG;YAAE,SAAS;YAAoB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAmB,QAAQ;YAAgD,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAuB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAK,OAAO;YAA+G,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAK,OAAO;YAAyC,QAAQ;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmB,OAAO;YAAqC,QAAQ;YAAiD,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA8B;aAAE;QAAC;QAAG;YAAE,SAAS;YAA2D,QAAQ;QAAuD;QAAG;YAAE,SAAS;YAAK,OAAO;YAAsC,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,SAAS;YAAqsB,OAAO;YAAuC,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAyC;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAkB;KAAE;IAAE,cAAc;QAAE,eAAe;YAAE,SAAS;YAAM,OAAO;YAAM,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAsB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAU,QAAQ;QAAiB;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAwC,QAAQ;QAAsC;QAAG,YAAY;YAAE,SAAS;YAA2D,QAAQ;QAAgC;QAAG,oBAAoB;YAAE,SAAS;YAA2C,QAAQ;QAAmC;QAAG,wBAAwB;YAAE,SAAS;YAA6B,QAAQ;QAA6B;QAAG,iBAAiB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAA0B,QAAQ;QAA+B;QAAG,YAAY;YAAE,SAAS;YAA0E,QAAQ;QAA2B;QAAG,mBAAmB;YAAE,SAAS;YAAK,QAAQ;QAA8B;QAAG,wBAAwB;YAAE,SAAS;YAAoB,OAAO;YAAqB,QAAQ;QAA2D;QAAG,kBAAkB;YAAE,SAAS;YAAW,QAAQ;QAA+E;QAAG,gBAAgB;YAAE,SAAS;YAAa,QAAQ;QAAuD;QAAG,wBAAwB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAkD,QAAQ;QAAyC;QAAG,aAAa;YAAE,SAAS;YAAyC,QAAQ;QAA6C;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAA8G,QAAQ;QAAiC;QAAG,YAAY;YAAE,SAAS;YAAc,QAAQ;QAA6B;QAAG,qBAAqB;YAAE,SAAS;YAAmB,QAAQ;QAA6B;IAAE;IAAG,aAAa;AAAqB;AAC/pN,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}