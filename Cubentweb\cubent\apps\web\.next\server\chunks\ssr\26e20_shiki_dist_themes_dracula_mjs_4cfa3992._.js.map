{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/dracula.mjs"], "sourcesContent": ["var dracula = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#BD93F910\",\n    \"activityBar.activeBorder\": \"#FF79C680\",\n    \"activityBar.background\": \"#343746\",\n    \"activityBar.foreground\": \"#F8F8F2\",\n    \"activityBar.inactiveForeground\": \"#6272A4\",\n    \"activityBarBadge.background\": \"#FF79C6\",\n    \"activityBarBadge.foreground\": \"#F8F8F2\",\n    \"badge.background\": \"#44475A\",\n    \"badge.foreground\": \"#F8F8F2\",\n    \"breadcrumb.activeSelectionForeground\": \"#F8F8F2\",\n    \"breadcrumb.background\": \"#282A36\",\n    \"breadcrumb.focusForeground\": \"#F8F8F2\",\n    \"breadcrumb.foreground\": \"#6272A4\",\n    \"breadcrumbPicker.background\": \"#191A21\",\n    \"button.background\": \"#44475A\",\n    \"button.foreground\": \"#F8F8F2\",\n    \"button.secondaryBackground\": \"#282A36\",\n    \"button.secondaryForeground\": \"#F8F8F2\",\n    \"button.secondaryHoverBackground\": \"#343746\",\n    \"debugToolBar.background\": \"#21222C\",\n    \"diffEditor.insertedTextBackground\": \"#50FA7B20\",\n    \"diffEditor.removedTextBackground\": \"#FF555550\",\n    \"dropdown.background\": \"#343746\",\n    \"dropdown.border\": \"#191A21\",\n    \"dropdown.foreground\": \"#F8F8F2\",\n    \"editor.background\": \"#282A36\",\n    \"editor.findMatchBackground\": \"#FFB86C80\",\n    \"editor.findMatchHighlightBackground\": \"#FFFFFF40\",\n    \"editor.findRangeHighlightBackground\": \"#44475A75\",\n    \"editor.foldBackground\": \"#21222C80\",\n    \"editor.foreground\": \"#F8F8F2\",\n    \"editor.hoverHighlightBackground\": \"#8BE9FD50\",\n    \"editor.lineHighlightBorder\": \"#44475A\",\n    \"editor.rangeHighlightBackground\": \"#BD93F915\",\n    \"editor.selectionBackground\": \"#44475A\",\n    \"editor.selectionHighlightBackground\": \"#424450\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#282A36\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#50FA7B\",\n    \"editor.snippetTabstopHighlightBackground\": \"#282A36\",\n    \"editor.snippetTabstopHighlightBorder\": \"#6272A4\",\n    \"editor.wordHighlightBackground\": \"#8BE9FD50\",\n    \"editor.wordHighlightStrongBackground\": \"#50FA7B50\",\n    \"editorBracketHighlight.foreground1\": \"#F8F8F2\",\n    \"editorBracketHighlight.foreground2\": \"#FF79C6\",\n    \"editorBracketHighlight.foreground3\": \"#8BE9FD\",\n    \"editorBracketHighlight.foreground4\": \"#50FA7B\",\n    \"editorBracketHighlight.foreground5\": \"#BD93F9\",\n    \"editorBracketHighlight.foreground6\": \"#FFB86C\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#FF5555\",\n    \"editorCodeLens.foreground\": \"#6272A4\",\n    \"editorError.foreground\": \"#FF5555\",\n    \"editorGroup.border\": \"#BD93F9\",\n    \"editorGroup.dropBackground\": \"#44475A70\",\n    \"editorGroupHeader.tabsBackground\": \"#191A21\",\n    \"editorGutter.addedBackground\": \"#50FA7B80\",\n    \"editorGutter.deletedBackground\": \"#FF555580\",\n    \"editorGutter.modifiedBackground\": \"#8BE9FD80\",\n    \"editorHoverWidget.background\": \"#282A36\",\n    \"editorHoverWidget.border\": \"#6272A4\",\n    \"editorIndentGuide.activeBackground\": \"#FFFFFF45\",\n    \"editorIndentGuide.background\": \"#FFFFFF1A\",\n    \"editorLineNumber.foreground\": \"#6272A4\",\n    \"editorLink.activeForeground\": \"#8BE9FD\",\n    \"editorMarkerNavigation.background\": \"#21222C\",\n    \"editorOverviewRuler.addedForeground\": \"#50FA7B80\",\n    \"editorOverviewRuler.border\": \"#191A21\",\n    \"editorOverviewRuler.currentContentForeground\": \"#50FA7B\",\n    \"editorOverviewRuler.deletedForeground\": \"#FF555580\",\n    \"editorOverviewRuler.errorForeground\": \"#FF555580\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#BD93F9\",\n    \"editorOverviewRuler.infoForeground\": \"#8BE9FD80\",\n    \"editorOverviewRuler.modifiedForeground\": \"#8BE9FD80\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#FFB86C\",\n    \"editorOverviewRuler.warningForeground\": \"#FFB86C80\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#8BE9FD\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#50FA7B\",\n    \"editorRuler.foreground\": \"#FFFFFF1A\",\n    \"editorSuggestWidget.background\": \"#21222C\",\n    \"editorSuggestWidget.foreground\": \"#F8F8F2\",\n    \"editorSuggestWidget.selectedBackground\": \"#44475A\",\n    \"editorWarning.foreground\": \"#8BE9FD\",\n    \"editorWhitespace.foreground\": \"#FFFFFF1A\",\n    \"editorWidget.background\": \"#21222C\",\n    \"errorForeground\": \"#FF5555\",\n    \"extensionButton.prominentBackground\": \"#50FA7B90\",\n    \"extensionButton.prominentForeground\": \"#F8F8F2\",\n    \"extensionButton.prominentHoverBackground\": \"#50FA7B60\",\n    \"focusBorder\": \"#6272A4\",\n    \"foreground\": \"#F8F8F2\",\n    \"gitDecoration.conflictingResourceForeground\": \"#FFB86C\",\n    \"gitDecoration.deletedResourceForeground\": \"#FF5555\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6272A4\",\n    \"gitDecoration.modifiedResourceForeground\": \"#8BE9FD\",\n    \"gitDecoration.untrackedResourceForeground\": \"#50FA7B\",\n    \"inlineChat.regionHighlight\": \"#343746\",\n    \"input.background\": \"#282A36\",\n    \"input.border\": \"#191A21\",\n    \"input.foreground\": \"#F8F8F2\",\n    \"input.placeholderForeground\": \"#6272A4\",\n    \"inputOption.activeBorder\": \"#BD93F9\",\n    \"inputValidation.errorBorder\": \"#FF5555\",\n    \"inputValidation.infoBorder\": \"#FF79C6\",\n    \"inputValidation.warningBorder\": \"#FFB86C\",\n    \"list.activeSelectionBackground\": \"#44475A\",\n    \"list.activeSelectionForeground\": \"#F8F8F2\",\n    \"list.dropBackground\": \"#44475A\",\n    \"list.errorForeground\": \"#FF5555\",\n    \"list.focusBackground\": \"#44475A75\",\n    \"list.highlightForeground\": \"#8BE9FD\",\n    \"list.hoverBackground\": \"#44475A75\",\n    \"list.inactiveSelectionBackground\": \"#44475A75\",\n    \"list.warningForeground\": \"#FFB86C\",\n    \"listFilterWidget.background\": \"#343746\",\n    \"listFilterWidget.noMatchesOutline\": \"#FF5555\",\n    \"listFilterWidget.outline\": \"#424450\",\n    \"merge.currentHeaderBackground\": \"#50FA7B90\",\n    \"merge.incomingHeaderBackground\": \"#BD93F990\",\n    \"panel.background\": \"#282A36\",\n    \"panel.border\": \"#BD93F9\",\n    \"panelTitle.activeBorder\": \"#FF79C6\",\n    \"panelTitle.activeForeground\": \"#F8F8F2\",\n    \"panelTitle.inactiveForeground\": \"#6272A4\",\n    \"peekView.border\": \"#44475A\",\n    \"peekViewEditor.background\": \"#282A36\",\n    \"peekViewEditor.matchHighlightBackground\": \"#F1FA8C80\",\n    \"peekViewResult.background\": \"#21222C\",\n    \"peekViewResult.fileForeground\": \"#F8F8F2\",\n    \"peekViewResult.lineForeground\": \"#F8F8F2\",\n    \"peekViewResult.matchHighlightBackground\": \"#F1FA8C80\",\n    \"peekViewResult.selectionBackground\": \"#44475A\",\n    \"peekViewResult.selectionForeground\": \"#F8F8F2\",\n    \"peekViewTitle.background\": \"#191A21\",\n    \"peekViewTitleDescription.foreground\": \"#6272A4\",\n    \"peekViewTitleLabel.foreground\": \"#F8F8F2\",\n    \"pickerGroup.border\": \"#BD93F9\",\n    \"pickerGroup.foreground\": \"#8BE9FD\",\n    \"progressBar.background\": \"#FF79C6\",\n    \"selection.background\": \"#BD93F9\",\n    \"settings.checkboxBackground\": \"#21222C\",\n    \"settings.checkboxBorder\": \"#191A21\",\n    \"settings.checkboxForeground\": \"#F8F8F2\",\n    \"settings.dropdownBackground\": \"#21222C\",\n    \"settings.dropdownBorder\": \"#191A21\",\n    \"settings.dropdownForeground\": \"#F8F8F2\",\n    \"settings.headerForeground\": \"#F8F8F2\",\n    \"settings.modifiedItemIndicator\": \"#FFB86C\",\n    \"settings.numberInputBackground\": \"#21222C\",\n    \"settings.numberInputBorder\": \"#191A21\",\n    \"settings.numberInputForeground\": \"#F8F8F2\",\n    \"settings.textInputBackground\": \"#21222C\",\n    \"settings.textInputBorder\": \"#191A21\",\n    \"settings.textInputForeground\": \"#F8F8F2\",\n    \"sideBar.background\": \"#21222C\",\n    \"sideBarSectionHeader.background\": \"#282A36\",\n    \"sideBarSectionHeader.border\": \"#191A21\",\n    \"sideBarTitle.foreground\": \"#F8F8F2\",\n    \"statusBar.background\": \"#191A21\",\n    \"statusBar.debuggingBackground\": \"#FF5555\",\n    \"statusBar.debuggingForeground\": \"#191A21\",\n    \"statusBar.foreground\": \"#F8F8F2\",\n    \"statusBar.noFolderBackground\": \"#191A21\",\n    \"statusBar.noFolderForeground\": \"#F8F8F2\",\n    \"statusBarItem.prominentBackground\": \"#FF5555\",\n    \"statusBarItem.prominentHoverBackground\": \"#FFB86C\",\n    \"statusBarItem.remoteBackground\": \"#BD93F9\",\n    \"statusBarItem.remoteForeground\": \"#282A36\",\n    \"tab.activeBackground\": \"#282A36\",\n    \"tab.activeBorderTop\": \"#FF79C680\",\n    \"tab.activeForeground\": \"#F8F8F2\",\n    \"tab.border\": \"#191A21\",\n    \"tab.inactiveBackground\": \"#21222C\",\n    \"tab.inactiveForeground\": \"#6272A4\",\n    \"terminal.ansiBlack\": \"#21222C\",\n    \"terminal.ansiBlue\": \"#BD93F9\",\n    \"terminal.ansiBrightBlack\": \"#6272A4\",\n    \"terminal.ansiBrightBlue\": \"#D6ACFF\",\n    \"terminal.ansiBrightCyan\": \"#A4FFFF\",\n    \"terminal.ansiBrightGreen\": \"#69FF94\",\n    \"terminal.ansiBrightMagenta\": \"#FF92DF\",\n    \"terminal.ansiBrightRed\": \"#FF6E6E\",\n    \"terminal.ansiBrightWhite\": \"#FFFFFF\",\n    \"terminal.ansiBrightYellow\": \"#FFFFA5\",\n    \"terminal.ansiCyan\": \"#8BE9FD\",\n    \"terminal.ansiGreen\": \"#50FA7B\",\n    \"terminal.ansiMagenta\": \"#FF79C6\",\n    \"terminal.ansiRed\": \"#FF5555\",\n    \"terminal.ansiWhite\": \"#F8F8F2\",\n    \"terminal.ansiYellow\": \"#F1FA8C\",\n    \"terminal.background\": \"#282A36\",\n    \"terminal.foreground\": \"#F8F8F2\",\n    \"titleBar.activeBackground\": \"#21222C\",\n    \"titleBar.activeForeground\": \"#F8F8F2\",\n    \"titleBar.inactiveBackground\": \"#191A21\",\n    \"titleBar.inactiveForeground\": \"#6272A4\",\n    \"walkThrough.embeddedEditorBackground\": \"#21222C\"\n  },\n  \"displayName\": \"Dracula Theme\",\n  \"name\": \"dracula\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"emphasis\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"strong\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6272A4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline italic\",\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline italic\",\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.filename\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.error\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\",\n        \"beginning.punctuation.definition.quote.markdown\",\n        \"punctuation.definition.link.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inline.raw\",\n        \"markup.raw.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link\",\n        \"markup.underline.link.image\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.link.reference.def.restructuredtext\",\n        \"punctuation.definition.directive.restructuredtext\",\n        \"string.other.link.description\",\n        \"string.other.link.title\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.directive.restructuredtext\",\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.separator.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6272A4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"fenced_code.block.language\",\n        \"markup.raw.inner.restructuredtext\",\n        \"markup.fenced_code.block.markdown punctuation.definition.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.constant.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading.markdown punctuation.definition.string.begin\",\n        \"markup.heading.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.paragraph.markdown punctuation.definition.string.begin\",\n        \"meta.paragraph.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.begin\",\n        \"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.class\",\n        \"entity.name.class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.expressions-and-types.swift\",\n        \"keyword.other.this\",\n        \"variable.language\",\n        \"variable.language punctuation.definition.variable.php\",\n        \"variable.other.readwrite.instance.ruby\",\n        \"variable.parameter.function.language.special\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"unused.comment\",\n        \"wildcard.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6272A4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment keyword.codetag.notation\",\n        \"comment.block.documentation keyword\",\n        \"comment.block.documentation storage.type.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation entity.name.type punctuation.definition.bracket\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"variable.other.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.escape\",\n        \"constant.character.string.escape\",\n        \"constant.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.parent-selector\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.object\",\n        \"meta.function-call.php\",\n        \"meta.function-call.static\",\n        \"meta.method-call.java meta.method\",\n        \"meta.method.groovy\",\n        \"support.function.any-method.lua\",\n        \"keyword.operator.function.infix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.variable.parameter\",\n        \"meta.at-rule.function variable\",\n        \"meta.at-rule.mixin variable\",\n        \"meta.function.arguments variable.other.php\",\n        \"meta.selectionset.graphql meta.arguments.graphql variable.arguments.graphql\",\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator variable.other.readwrite\",\n        \"meta.decorator variable.other.property\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator variable.other.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.new\",\n        \"keyword.operator.new\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selector\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic\",\n        \"support.variable\",\n        \"variable.other.predefined\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"support.type.property-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.symbol.hashkey punctuation.definition.constant.ruby\",\n        \"entity.other.attribute-name.placeholder punctuation\",\n        \"entity.other.attribute-name.pseudo-class punctuation\",\n        \"entity.other.attribute-name.pseudo-element punctuation\",\n        \"meta.group.double.toml\",\n        \"meta.group.toml\",\n        \"meta.object-binding-pattern-variable punctuation.destructuring\",\n        \"punctuation.colon.graphql\",\n        \"punctuation.definition.block.scalar.folded.yaml\",\n        \"punctuation.definition.block.scalar.literal.yaml\",\n        \"punctuation.definition.block.sequence.item.yaml\",\n        \"punctuation.definition.entity.other.inherited-class\",\n        \"punctuation.function.swift\",\n        \"punctuation.separator.dictionary.key-value\",\n        \"punctuation.separator.hash\",\n        \"punctuation.separator.inheritance\",\n        \"punctuation.separator.key-value\",\n        \"punctuation.separator.key-value.mapping.yaml\",\n        \"punctuation.separator.namespace\",\n        \"punctuation.separator.pointer-access\",\n        \"punctuation.separator.slice\",\n        \"string.unquoted.heredoc punctuation.definition.string\",\n        \"support.other.chomping-indicator.yaml\",\n        \"punctuation.separator.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.other.powershell\",\n        \"keyword.other.statement-separator.powershell\",\n        \"meta.brace.round\",\n        \"meta.function-call punctuation\",\n        \"punctuation.definition.arguments.begin\",\n        \"punctuation.definition.arguments.end\",\n        \"punctuation.definition.entity.begin\",\n        \"punctuation.definition.entity.end\",\n        \"punctuation.definition.tag.cs\",\n        \"punctuation.definition.type.begin\",\n        \"punctuation.definition.type.end\",\n        \"punctuation.section.scope.begin\",\n        \"punctuation.section.scope.end\",\n        \"punctuation.terminator.expression.php\",\n        \"storage.type.generic.java\",\n        \"string.template meta.brace\",\n        \"string.template punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.string-contents.quoted.double punctuation.definition.variable\",\n        \"punctuation.definition.interpolation.begin\",\n        \"punctuation.definition.interpolation.end\",\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\",\n        \"punctuation.section.embedded.begin\",\n        \"punctuation.section.embedded.coffee\",\n        \"punctuation.section.embedded.end\",\n        \"punctuation.section.embedded.end source.php\",\n        \"punctuation.section.embedded.end source.ruby\",\n        \"punctuation.definition.variable.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.target.makefile\",\n        \"entity.name.section.toml\",\n        \"entity.name.tag.yaml\",\n        \"variable.other.key.toml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.date\",\n        \"constant.other.timestamp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"meta.implementation storage.type.objc\",\n        \"meta.interface-or-protocol storage.type.objc\",\n        \"source.groovy storage.type.def\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type\",\n        \"keyword.primitive-datatypes.swift\",\n        \"keyword.type.cs\",\n        \"meta.protocol-list.objc\",\n        \"meta.return-type.objc\",\n        \"source.go storage.type\",\n        \"source.groovy storage.type\",\n        \"source.java storage.type\",\n        \"source.powershell entity.other.attribute-name\",\n        \"storage.class.std.rust\",\n        \"storage.type.attribute.swift\",\n        \"storage.type.c\",\n        \"storage.type.core.rust\",\n        \"storage.type.cs\",\n        \"storage.type.groovy\",\n        \"storage.type.objc\",\n        \"storage.type.php\",\n        \"storage.type.haskell\",\n        \"storage.type.ocaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.type-parameter\",\n        \"meta.indexer.mappedtype.declaration entity.name.type\",\n        \"meta.type.parameters entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"constant.other.character-class.set.regexp\",\n        \"constant.character.escape.backslash.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.capture.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF79C6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.assertion.regexp\",\n        \"keyword.operator.negation.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.assertion.look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50FA7B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin\",\n        \"punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#E9F284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.support.type.property-name.begin\",\n        \"punctuation.support.type.property-name.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FE\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted.docstring.multi\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.begin\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.end\",\n        \"string.quoted.docstring.multi.python constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6272A4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"constant.other.key.perl\",\n        \"support.variable.property\",\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.constant.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import variable.other.readwrite\",\n        \"meta.variable.assignment.destructured.object.coffee variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import variable.other.readwrite.alias\",\n        \"meta.export variable.other.readwrite.alias\",\n        \"meta.variable.assignment.destructured.object.coffee variable variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selectionset.graphql variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selectionset.graphql meta.arguments variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.fragment.graphql\",\n        \"variable.fragment.graphql\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8BE9FD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.symbol.hashkey.ruby\",\n        \"keyword.operator.dereference.java\",\n        \"keyword.operator.navigation.groovy\",\n        \"meta.scope.for-loop.shell punctuation.definition.string.begin\",\n        \"meta.scope.for-loop.shell punctuation.definition.string.end\",\n        \"meta.scope.for-loop.shell string\",\n        \"storage.modifier.import\",\n        \"punctuation.section.embedded.begin.tsx\",\n        \"punctuation.section.embedded.end.tsx\",\n        \"punctuation.section.embedded.begin.jsx\",\n        \"punctuation.section.embedded.end.jsx\",\n        \"punctuation.separator.list.comma.css\",\n        \"constant.language.empty-list.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#BD93F9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.scope.prerequisites.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute-selector.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F1FA8C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.attribute-selector.end.bracket.square.scss\",\n        \"punctuation.definition.attribute-selector.begin.bracket.square.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.preprocessor.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6272A4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.error\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#FF5555\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.warning\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#F1FA8C\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { dracula as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,iDAAiD;QACjD,6CAA6C;QAC7C,4CAA4C;QAC5C,wCAAwC;QACxC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,6BAA6B;QAC7B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,uCAAuC;QACvC,8BAA8B;QAC9B,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,0BAA0B;QAC1B,kCAAkC;QAClC,kCAAkC;QAClC,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,8BAA8B;QAC9B,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,+BAA+B;QAC/B,8BAA8B;QAC9B,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,oCAAoC;QACpC,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,iCAAiC;QACjC,kCAAkC;QAClC,oBAAoB;QACpB,gBAAgB;QAChB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,wCAAwC;IAC1C;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}