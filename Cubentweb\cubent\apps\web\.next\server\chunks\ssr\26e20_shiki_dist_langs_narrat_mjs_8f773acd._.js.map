{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/narrat.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Narrat Language\", \"name\": \"narrat\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expression\" }], \"repository\": { \"commands\": { \"patterns\": [{ \"match\": \"\\\\b(set|var)\\\\b\", \"name\": \"keyword.commands.variables.narrat\" }, { \"match\": \"\\\\b(talk|think)\\\\b\", \"name\": \"keyword.commands.text.narrat\" }, { \"match\": \"\\\\b(jump|run|wait|return|save|save_prompt)\", \"name\": \"keyword.commands.flow.narrat\" }, { \"match\": \"\\\\b(log|clear_dialog)\\\\b\", \"name\": \"keyword.commands.helpers.narrat\" }, { \"match\": \"\\\\b(set_screen|empty_layer|set_button)\", \"name\": \"keyword.commands.screens.narrat\" }, { \"match\": \"\\\\b(play|pause|stop)\\\\b\", \"name\": \"keyword.commands.audio.narrat\" }, { \"match\": \"\\\\b(notify|enable_notifications|disable_notifications)\\\\b\", \"name\": \"keyword.commands.notifications.narrat\" }, { \"match\": \"\\\\b(set_stat|get_stat_value|add_stat)\", \"name\": \"keyword.commands.stats.narrat\" }, { \"match\": \"\\\\b(neg|abs|random|random_float|random_from_args|min|max|clamp|floor|round|ceil|sqrt|^)\\\\b\", \"name\": \"keyword.commands.math.narrat\" }, { \"match\": \"\\\\b(concat|join)\\\\b\", \"name\": \"keyword.commands.string.narrat\" }, { \"match\": \"\\\\b(text_field)\\\\b\", \"name\": \"keyword.commands.text_field.narrat\" }, { \"match\": \"\\\\b(add_level|set_level|add_xp|roll|get_level|get_xp)\\\\b\", \"name\": \"keyword.commands.skills.narrat\" }, { \"match\": \"\\\\b(add_item|remove_item|enable_interaction|disable_interaction|has_item?|item_amount?)\", \"name\": \"keyword.commands.inventory.narrat\" }, { \"match\": \"\\\\b(start_quest|start_objective|complete_objective|complete_quest|quest_started?|objective_started?|quest_completed?|objective_completed?)\", \"name\": \"keyword.commands.quests.narrat\" }] }, \"comments\": { \"patterns\": [{ \"match\": \"\\\\/\\\\/.*$\", \"name\": \"comment.line.narrat\" }] }, \"expression\": { \"patterns\": [{ \"include\": \"#keywords\" }, { \"include\": \"#commands\" }, { \"include\": \"#operators\" }, { \"include\": \"#primitives\" }, { \"include\": \"#strings\" }, { \"include\": \"#paren-expression\" }] }, \"interpolation\": { \"patterns\": [{ \"match\": \"(\\\\w|\\\\.)+\", \"name\": \"variable.interpolation.narrat\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(if|else|choice)\\\\b\", \"name\": \"keyword.control.narrat\" }, { \"match\": \"\\\\$[\\\\w|\\\\.]+\\\\b\", \"name\": \"variable.value.narrat\" }, { \"match\": \"^\\\\w+(?=(\\\\s|\\\\w)*:)\", \"name\": \"entity.name.function.narrat\" }, { \"match\": \"^\\\\w+(?!(\\\\s|\\\\w)*:)\", \"name\": \"invalid.label.narrat\" }, { \"match\": \"(?<=\\\\w)[^^](\\\\b\\\\w+\\\\b)(?=(\\\\s|\\\\w)*:)\", \"name\": \"entity.other.attribute-name\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(&&|\\\\|\\\\||!=|==|>=|<=|<|>|!|\\\\?)\\\\s\", \"name\": \"keyword.operator.logic.narrat\" }, { \"match\": \"(\\\\+|-|\\\\*|\\\\/)\\\\s\", \"name\": \"keyword.operator.arithmetic.narrat\" }] }, \"paren-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.paren.open\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.paren.close\" } }, \"name\": \"expression.group\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"primitives\": { \"patterns\": [{ \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.narrat\" }, { \"match\": \"\\\\btrue\\\\b\", \"name\": \"constant.language.true.narrat\" }, { \"match\": \"\\\\bfalse\\\\b\", \"name\": \"constant.language.false.narrat\" }, { \"match\": \"\\\\bnull\\\\b\", \"name\": \"constant.language.null.narrat\" }, { \"match\": \"\\\\bundefined\\\\b\", \"name\": \"constant.language.undefined.narrat\" }] }, \"strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.narrat\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.narrat\" }, { \"begin\": \"%{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.template.open\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.template.close.narrat\" } }, \"name\": \"expression.template\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#interpolation\" }] }] } }, \"scopeName\": \"source.narrat\", \"aliases\": [\"nar\"] });\nvar narrat = [\n  lang\n];\n\nexport { narrat as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAmB,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAc;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA8C,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA0C,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA8F,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA4D,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA2F,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAA8I,QAAQ;gBAAiC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAAsB;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAAgC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA2C,QAAQ;gBAA8B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwC,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAqC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAqC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAiB,WAAW;QAAC;KAAM;AAAC;AAC1vH,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}