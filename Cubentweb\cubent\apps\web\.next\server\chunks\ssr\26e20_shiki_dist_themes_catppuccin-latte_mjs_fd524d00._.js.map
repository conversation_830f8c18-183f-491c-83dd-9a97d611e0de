{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/catppuccin-latte.mjs"], "sourcesContent": ["var catppuccinLatte = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#00000000\",\n    \"activityBar.activeBorder\": \"#00000000\",\n    \"activityBar.activeFocusBorder\": \"#00000000\",\n    \"activityBar.background\": \"#dce0e8\",\n    \"activityBar.border\": \"#00000000\",\n    \"activityBar.dropBorder\": \"#8839ef33\",\n    \"activityBar.foreground\": \"#8839ef\",\n    \"activityBar.inactiveForeground\": \"#9ca0b0\",\n    \"activityBarBadge.background\": \"#8839ef\",\n    \"activityBarBadge.foreground\": \"#dce0e8\",\n    \"activityBarTop.activeBorder\": \"#00000000\",\n    \"activityBarTop.dropBorder\": \"#8839ef33\",\n    \"activityBarTop.foreground\": \"#8839ef\",\n    \"activityBarTop.inactiveForeground\": \"#9ca0b0\",\n    \"badge.background\": \"#bcc0cc\",\n    \"badge.foreground\": \"#4c4f69\",\n    \"banner.background\": \"#bcc0cc\",\n    \"banner.foreground\": \"#4c4f69\",\n    \"banner.iconForeground\": \"#4c4f69\",\n    \"breadcrumb.activeSelectionForeground\": \"#8839ef\",\n    \"breadcrumb.background\": \"#eff1f5\",\n    \"breadcrumb.focusForeground\": \"#8839ef\",\n    \"breadcrumb.foreground\": \"#4c4f69cc\",\n    \"breadcrumbPicker.background\": \"#e6e9ef\",\n    \"button.background\": \"#8839ef\",\n    \"button.border\": \"#00000000\",\n    \"button.foreground\": \"#dce0e8\",\n    \"button.hoverBackground\": \"#9c5af2\",\n    \"button.secondaryBackground\": \"#acb0be\",\n    \"button.secondaryBorder\": \"#8839ef\",\n    \"button.secondaryForeground\": \"#4c4f69\",\n    \"button.secondaryHoverBackground\": \"#c0c3ce\",\n    \"button.separator\": \"#00000000\",\n    \"charts.blue\": \"#1e66f5\",\n    \"charts.foreground\": \"#4c4f69\",\n    \"charts.green\": \"#40a02b\",\n    \"charts.lines\": \"#5c5f77\",\n    \"charts.orange\": \"#fe640b\",\n    \"charts.purple\": \"#8839ef\",\n    \"charts.red\": \"#d20f39\",\n    \"charts.yellow\": \"#df8e1d\",\n    \"checkbox.background\": \"#bcc0cc\",\n    \"checkbox.border\": \"#00000000\",\n    \"checkbox.foreground\": \"#8839ef\",\n    \"commandCenter.activeBackground\": \"#acb0be33\",\n    \"commandCenter.activeBorder\": \"#8839ef\",\n    \"commandCenter.activeForeground\": \"#8839ef\",\n    \"commandCenter.background\": \"#e6e9ef\",\n    \"commandCenter.border\": \"#00000000\",\n    \"commandCenter.foreground\": \"#5c5f77\",\n    \"commandCenter.inactiveBorder\": \"#00000000\",\n    \"commandCenter.inactiveForeground\": \"#5c5f77\",\n    \"debugConsole.errorForeground\": \"#d20f39\",\n    \"debugConsole.infoForeground\": \"#1e66f5\",\n    \"debugConsole.sourceForeground\": \"#dc8a78\",\n    \"debugConsole.warningForeground\": \"#fe640b\",\n    \"debugConsoleInputIcon.foreground\": \"#4c4f69\",\n    \"debugExceptionWidget.background\": \"#dce0e8\",\n    \"debugExceptionWidget.border\": \"#8839ef\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#acb0be\",\n    \"debugIcon.breakpointDisabledForeground\": \"#d20f3999\",\n    \"debugIcon.breakpointForeground\": \"#d20f39\",\n    \"debugIcon.breakpointStackframeForeground\": \"#acb0be\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#bf607c\",\n    \"debugIcon.continueForeground\": \"#40a02b\",\n    \"debugIcon.disconnectForeground\": \"#acb0be\",\n    \"debugIcon.pauseForeground\": \"#1e66f5\",\n    \"debugIcon.restartForeground\": \"#179299\",\n    \"debugIcon.startForeground\": \"#40a02b\",\n    \"debugIcon.stepBackForeground\": \"#acb0be\",\n    \"debugIcon.stepIntoForeground\": \"#4c4f69\",\n    \"debugIcon.stepOutForeground\": \"#4c4f69\",\n    \"debugIcon.stepOverForeground\": \"#8839ef\",\n    \"debugIcon.stopForeground\": \"#d20f39\",\n    \"debugTokenExpression.boolean\": \"#8839ef\",\n    \"debugTokenExpression.error\": \"#d20f39\",\n    \"debugTokenExpression.number\": \"#fe640b\",\n    \"debugTokenExpression.string\": \"#40a02b\",\n    \"debugToolBar.background\": \"#dce0e8\",\n    \"debugToolBar.border\": \"#00000000\",\n    \"descriptionForeground\": \"#4c4f69\",\n    \"diffEditor.border\": \"#acb0be\",\n    \"diffEditor.diagonalFill\": \"#acb0be99\",\n    \"diffEditor.insertedLineBackground\": \"#40a02b26\",\n    \"diffEditor.insertedTextBackground\": \"#40a02b1a\",\n    \"diffEditor.removedLineBackground\": \"#d20f3926\",\n    \"diffEditor.removedTextBackground\": \"#d20f391a\",\n    \"diffEditorOverview.insertedForeground\": \"#40a02bcc\",\n    \"diffEditorOverview.removedForeground\": \"#d20f39cc\",\n    \"disabledForeground\": \"#6c6f85\",\n    \"dropdown.background\": \"#e6e9ef\",\n    \"dropdown.border\": \"#8839ef\",\n    \"dropdown.foreground\": \"#4c4f69\",\n    \"dropdown.listBackground\": \"#acb0be\",\n    \"editor.background\": \"#eff1f5\",\n    \"editor.findMatchBackground\": \"#e6adbd\",\n    \"editor.findMatchBorder\": \"#d20f3933\",\n    \"editor.findMatchHighlightBackground\": \"#a9daf0\",\n    \"editor.findMatchHighlightBorder\": \"#04a5e533\",\n    \"editor.findRangeHighlightBackground\": \"#a9daf0\",\n    \"editor.findRangeHighlightBorder\": \"#04a5e533\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#40a02b26\",\n    \"editor.foldBackground\": \"#04a5e540\",\n    \"editor.foreground\": \"#4c4f69\",\n    \"editor.hoverHighlightBackground\": \"#04a5e540\",\n    \"editor.lineHighlightBackground\": \"#4c4f6912\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#04a5e540\",\n    \"editor.rangeHighlightBorder\": \"#00000000\",\n    \"editor.selectionBackground\": \"#7c7f934d\",\n    \"editor.selectionHighlightBackground\": \"#7c7f9333\",\n    \"editor.selectionHighlightBorder\": \"#7c7f9333\",\n    \"editor.stackFrameHighlightBackground\": \"#df8e1d26\",\n    \"editor.wordHighlightBackground\": \"#7c7f9333\",\n    \"editorBracketHighlight.foreground1\": \"#d20f39\",\n    \"editorBracketHighlight.foreground2\": \"#fe640b\",\n    \"editorBracketHighlight.foreground3\": \"#df8e1d\",\n    \"editorBracketHighlight.foreground4\": \"#40a02b\",\n    \"editorBracketHighlight.foreground5\": \"#209fb5\",\n    \"editorBracketHighlight.foreground6\": \"#8839ef\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#e64553\",\n    \"editorBracketMatch.background\": \"#7c7f931a\",\n    \"editorBracketMatch.border\": \"#7c7f93\",\n    \"editorCodeLens.foreground\": \"#8c8fa1\",\n    \"editorCursor.background\": \"#eff1f5\",\n    \"editorCursor.foreground\": \"#dc8a78\",\n    \"editorError.background\": \"#00000000\",\n    \"editorError.border\": \"#00000000\",\n    \"editorError.foreground\": \"#d20f39\",\n    \"editorGroup.border\": \"#acb0be\",\n    \"editorGroup.dropBackground\": \"#8839ef33\",\n    \"editorGroup.emptyBackground\": \"#eff1f5\",\n    \"editorGroupHeader.tabsBackground\": \"#dce0e8\",\n    \"editorGutter.addedBackground\": \"#40a02b\",\n    \"editorGutter.background\": \"#eff1f5\",\n    \"editorGutter.commentGlyphForeground\": \"#8839ef\",\n    \"editorGutter.commentRangeForeground\": \"#ccd0da\",\n    \"editorGutter.deletedBackground\": \"#d20f39\",\n    \"editorGutter.foldingControlForeground\": \"#7c7f93\",\n    \"editorGutter.modifiedBackground\": \"#df8e1d\",\n    \"editorHoverWidget.background\": \"#e6e9ef\",\n    \"editorHoverWidget.border\": \"#acb0be\",\n    \"editorHoverWidget.foreground\": \"#4c4f69\",\n    \"editorIndentGuide.activeBackground\": \"#acb0be\",\n    \"editorIndentGuide.background\": \"#bcc0cc\",\n    \"editorInfo.background\": \"#00000000\",\n    \"editorInfo.border\": \"#00000000\",\n    \"editorInfo.foreground\": \"#1e66f5\",\n    \"editorInlayHint.background\": \"#e6e9efbf\",\n    \"editorInlayHint.foreground\": \"#acb0be\",\n    \"editorInlayHint.parameterBackground\": \"#e6e9efbf\",\n    \"editorInlayHint.parameterForeground\": \"#6c6f85\",\n    \"editorInlayHint.typeBackground\": \"#e6e9efbf\",\n    \"editorInlayHint.typeForeground\": \"#5c5f77\",\n    \"editorLightBulb.foreground\": \"#df8e1d\",\n    \"editorLineNumber.activeForeground\": \"#8839ef\",\n    \"editorLineNumber.foreground\": \"#8c8fa1\",\n    \"editorLink.activeForeground\": \"#8839ef\",\n    \"editorMarkerNavigation.background\": \"#e6e9ef\",\n    \"editorMarkerNavigationError.background\": \"#d20f39\",\n    \"editorMarkerNavigationInfo.background\": \"#1e66f5\",\n    \"editorMarkerNavigationWarning.background\": \"#fe640b\",\n    \"editorOverviewRuler.background\": \"#e6e9ef\",\n    \"editorOverviewRuler.border\": \"#4c4f6912\",\n    \"editorOverviewRuler.modifiedForeground\": \"#df8e1d\",\n    \"editorRuler.foreground\": \"#acb0be\",\n    \"editorStickyScrollHover.background\": \"#ccd0da\",\n    \"editorSuggestWidget.background\": \"#e6e9ef\",\n    \"editorSuggestWidget.border\": \"#acb0be\",\n    \"editorSuggestWidget.foreground\": \"#4c4f69\",\n    \"editorSuggestWidget.highlightForeground\": \"#8839ef\",\n    \"editorSuggestWidget.selectedBackground\": \"#ccd0da\",\n    \"editorWarning.background\": \"#00000000\",\n    \"editorWarning.border\": \"#00000000\",\n    \"editorWarning.foreground\": \"#fe640b\",\n    \"editorWhitespace.foreground\": \"#7c7f9366\",\n    \"editorWidget.background\": \"#e6e9ef\",\n    \"editorWidget.foreground\": \"#4c4f69\",\n    \"editorWidget.resizeBorder\": \"#acb0be\",\n    \"errorForeground\": \"#d20f39\",\n    \"errorLens.errorBackground\": \"#d20f3926\",\n    \"errorLens.errorBackgroundLight\": \"#d20f3926\",\n    \"errorLens.errorForeground\": \"#d20f39\",\n    \"errorLens.errorForegroundLight\": \"#d20f39\",\n    \"errorLens.errorMessageBackground\": \"#d20f3926\",\n    \"errorLens.hintBackground\": \"#40a02b26\",\n    \"errorLens.hintBackgroundLight\": \"#40a02b26\",\n    \"errorLens.hintForeground\": \"#40a02b\",\n    \"errorLens.hintForegroundLight\": \"#40a02b\",\n    \"errorLens.hintMessageBackground\": \"#40a02b26\",\n    \"errorLens.infoBackground\": \"#1e66f526\",\n    \"errorLens.infoBackgroundLight\": \"#1e66f526\",\n    \"errorLens.infoForeground\": \"#1e66f5\",\n    \"errorLens.infoForegroundLight\": \"#1e66f5\",\n    \"errorLens.infoMessageBackground\": \"#1e66f526\",\n    \"errorLens.statusBarErrorForeground\": \"#d20f39\",\n    \"errorLens.statusBarHintForeground\": \"#40a02b\",\n    \"errorLens.statusBarIconErrorForeground\": \"#d20f39\",\n    \"errorLens.statusBarIconWarningForeground\": \"#fe640b\",\n    \"errorLens.statusBarInfoForeground\": \"#1e66f5\",\n    \"errorLens.statusBarWarningForeground\": \"#fe640b\",\n    \"errorLens.warningBackground\": \"#fe640b26\",\n    \"errorLens.warningBackgroundLight\": \"#fe640b26\",\n    \"errorLens.warningForeground\": \"#fe640b\",\n    \"errorLens.warningForegroundLight\": \"#fe640b\",\n    \"errorLens.warningMessageBackground\": \"#fe640b26\",\n    \"extensionBadge.remoteBackground\": \"#1e66f5\",\n    \"extensionBadge.remoteForeground\": \"#dce0e8\",\n    \"extensionButton.prominentBackground\": \"#8839ef\",\n    \"extensionButton.prominentForeground\": \"#dce0e8\",\n    \"extensionButton.prominentHoverBackground\": \"#9c5af2\",\n    \"extensionButton.separator\": \"#eff1f5\",\n    \"extensionIcon.preReleaseForeground\": \"#acb0be\",\n    \"extensionIcon.sponsorForeground\": \"#ea76cb\",\n    \"extensionIcon.starForeground\": \"#df8e1d\",\n    \"extensionIcon.verifiedForeground\": \"#40a02b\",\n    \"focusBorder\": \"#8839ef\",\n    \"foreground\": \"#4c4f69\",\n    \"gitDecoration.addedResourceForeground\": \"#40a02b\",\n    \"gitDecoration.conflictingResourceForeground\": \"#8839ef\",\n    \"gitDecoration.deletedResourceForeground\": \"#d20f39\",\n    \"gitDecoration.ignoredResourceForeground\": \"#9ca0b0\",\n    \"gitDecoration.modifiedResourceForeground\": \"#df8e1d\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#d20f39\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#df8e1d\",\n    \"gitDecoration.submoduleResourceForeground\": \"#1e66f5\",\n    \"gitDecoration.untrackedResourceForeground\": \"#40a02b\",\n    \"gitlens.closedAutolinkedIssueIconColor\": \"#8839ef\",\n    \"gitlens.closedPullRequestIconColor\": \"#d20f39\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#40a02b\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#fe640b\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#df8e1d\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#fe640b\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#40a02b\",\n    \"gitlens.decorations.statusMergingOrRebasingConflictForegroundColor\": \"#e64553\",\n    \"gitlens.decorations.statusMergingOrRebasingForegroundColor\": \"#df8e1d\",\n    \"gitlens.decorations.workspaceCurrentForegroundColor\": \"#8839ef\",\n    \"gitlens.decorations.workspaceRepoMissingForegroundColor\": \"#6c6f85\",\n    \"gitlens.decorations.workspaceRepoOpenForegroundColor\": \"#8839ef\",\n    \"gitlens.decorations.worktreeHasUncommittedChangesForegroundColor\": \"#fe640b\",\n    \"gitlens.decorations.worktreeMissingForegroundColor\": \"#e64553\",\n    \"gitlens.graphChangesColumnAddedColor\": \"#40a02b\",\n    \"gitlens.graphChangesColumnDeletedColor\": \"#d20f39\",\n    \"gitlens.graphLane10Color\": \"#ea76cb\",\n    \"gitlens.graphLane1Color\": \"#8839ef\",\n    \"gitlens.graphLane2Color\": \"#df8e1d\",\n    \"gitlens.graphLane3Color\": \"#1e66f5\",\n    \"gitlens.graphLane4Color\": \"#dd7878\",\n    \"gitlens.graphLane5Color\": \"#40a02b\",\n    \"gitlens.graphLane6Color\": \"#7287fd\",\n    \"gitlens.graphLane7Color\": \"#dc8a78\",\n    \"gitlens.graphLane8Color\": \"#d20f39\",\n    \"gitlens.graphLane9Color\": \"#179299\",\n    \"gitlens.graphMinimapMarkerHeadColor\": \"#40a02b\",\n    \"gitlens.graphMinimapMarkerHighlightsColor\": \"#df8e1d\",\n    \"gitlens.graphMinimapMarkerLocalBranchesColor\": \"#1e66f5\",\n    \"gitlens.graphMinimapMarkerRemoteBranchesColor\": \"#0b57ef\",\n    \"gitlens.graphMinimapMarkerStashesColor\": \"#8839ef\",\n    \"gitlens.graphMinimapMarkerTagsColor\": \"#dd7878\",\n    \"gitlens.graphMinimapMarkerUpstreamColor\": \"#388c26\",\n    \"gitlens.graphScrollMarkerHeadColor\": \"#40a02b\",\n    \"gitlens.graphScrollMarkerHighlightsColor\": \"#df8e1d\",\n    \"gitlens.graphScrollMarkerLocalBranchesColor\": \"#1e66f5\",\n    \"gitlens.graphScrollMarkerRemoteBranchesColor\": \"#0b57ef\",\n    \"gitlens.graphScrollMarkerStashesColor\": \"#8839ef\",\n    \"gitlens.graphScrollMarkerTagsColor\": \"#dd7878\",\n    \"gitlens.graphScrollMarkerUpstreamColor\": \"#388c26\",\n    \"gitlens.gutterBackgroundColor\": \"#ccd0da4d\",\n    \"gitlens.gutterForegroundColor\": \"#4c4f69\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#8839ef\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#8839ef26\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#8839efcc\",\n    \"gitlens.mergedPullRequestIconColor\": \"#8839ef\",\n    \"gitlens.openAutolinkedIssueIconColor\": \"#40a02b\",\n    \"gitlens.openPullRequestIconColor\": \"#40a02b\",\n    \"gitlens.trailingLineBackgroundColor\": \"#00000000\",\n    \"gitlens.trailingLineForegroundColor\": \"#4c4f694d\",\n    \"gitlens.unpublishedChangesIconColor\": \"#40a02b\",\n    \"gitlens.unpublishedCommitIconColor\": \"#40a02b\",\n    \"gitlens.unpulledChangesIconColor\": \"#fe640b\",\n    \"icon.foreground\": \"#8839ef\",\n    \"input.background\": \"#ccd0da\",\n    \"input.border\": \"#00000000\",\n    \"input.foreground\": \"#4c4f69\",\n    \"input.placeholderForeground\": \"#4c4f6973\",\n    \"inputOption.activeBackground\": \"#acb0be\",\n    \"inputOption.activeBorder\": \"#8839ef\",\n    \"inputOption.activeForeground\": \"#4c4f69\",\n    \"inputValidation.errorBackground\": \"#d20f39\",\n    \"inputValidation.errorBorder\": \"#dce0e833\",\n    \"inputValidation.errorForeground\": \"#dce0e8\",\n    \"inputValidation.infoBackground\": \"#1e66f5\",\n    \"inputValidation.infoBorder\": \"#dce0e833\",\n    \"inputValidation.infoForeground\": \"#dce0e8\",\n    \"inputValidation.warningBackground\": \"#fe640b\",\n    \"inputValidation.warningBorder\": \"#dce0e833\",\n    \"inputValidation.warningForeground\": \"#dce0e8\",\n    \"issues.closed\": \"#8839ef\",\n    \"issues.newIssueDecoration\": \"#dc8a78\",\n    \"issues.open\": \"#40a02b\",\n    \"list.activeSelectionBackground\": \"#ccd0da\",\n    \"list.activeSelectionForeground\": \"#4c4f69\",\n    \"list.dropBackground\": \"#8839ef33\",\n    \"list.focusAndSelectionBackground\": \"#bcc0cc\",\n    \"list.focusBackground\": \"#ccd0da\",\n    \"list.focusForeground\": \"#4c4f69\",\n    \"list.focusOutline\": \"#00000000\",\n    \"list.highlightForeground\": \"#8839ef\",\n    \"list.hoverBackground\": \"#ccd0da80\",\n    \"list.hoverForeground\": \"#4c4f69\",\n    \"list.inactiveSelectionBackground\": \"#ccd0da\",\n    \"list.inactiveSelectionForeground\": \"#4c4f69\",\n    \"list.warningForeground\": \"#fe640b\",\n    \"listFilterWidget.background\": \"#bcc0cc\",\n    \"listFilterWidget.noMatchesOutline\": \"#d20f39\",\n    \"listFilterWidget.outline\": \"#00000000\",\n    \"menu.background\": \"#eff1f5\",\n    \"menu.border\": \"#eff1f580\",\n    \"menu.foreground\": \"#4c4f69\",\n    \"menu.selectionBackground\": \"#acb0be\",\n    \"menu.selectionBorder\": \"#00000000\",\n    \"menu.selectionForeground\": \"#4c4f69\",\n    \"menu.separatorBackground\": \"#acb0be\",\n    \"menubar.selectionBackground\": \"#bcc0cc\",\n    \"menubar.selectionForeground\": \"#4c4f69\",\n    \"merge.commonContentBackground\": \"#bcc0cc\",\n    \"merge.commonHeaderBackground\": \"#acb0be\",\n    \"merge.currentContentBackground\": \"#40a02b33\",\n    \"merge.currentHeaderBackground\": \"#40a02b66\",\n    \"merge.incomingContentBackground\": \"#1e66f533\",\n    \"merge.incomingHeaderBackground\": \"#1e66f566\",\n    \"minimap.background\": \"#e6e9ef80\",\n    \"minimap.errorHighlight\": \"#d20f39bf\",\n    \"minimap.findMatchHighlight\": \"#04a5e54d\",\n    \"minimap.selectionHighlight\": \"#acb0bebf\",\n    \"minimap.selectionOccurrenceHighlight\": \"#acb0bebf\",\n    \"minimap.warningHighlight\": \"#fe640bbf\",\n    \"minimapGutter.addedBackground\": \"#40a02bbf\",\n    \"minimapGutter.deletedBackground\": \"#d20f39bf\",\n    \"minimapGutter.modifiedBackground\": \"#df8e1dbf\",\n    \"minimapSlider.activeBackground\": \"#8839ef99\",\n    \"minimapSlider.background\": \"#8839ef33\",\n    \"minimapSlider.hoverBackground\": \"#8839ef66\",\n    \"notificationCenter.border\": \"#8839ef\",\n    \"notificationCenterHeader.background\": \"#e6e9ef\",\n    \"notificationCenterHeader.foreground\": \"#4c4f69\",\n    \"notificationLink.foreground\": \"#1e66f5\",\n    \"notificationToast.border\": \"#8839ef\",\n    \"notifications.background\": \"#e6e9ef\",\n    \"notifications.border\": \"#8839ef\",\n    \"notifications.foreground\": \"#4c4f69\",\n    \"notificationsErrorIcon.foreground\": \"#d20f39\",\n    \"notificationsInfoIcon.foreground\": \"#1e66f5\",\n    \"notificationsWarningIcon.foreground\": \"#fe640b\",\n    \"panel.background\": \"#eff1f5\",\n    \"panel.border\": \"#acb0be\",\n    \"panelSection.border\": \"#acb0be\",\n    \"panelSection.dropBackground\": \"#8839ef33\",\n    \"panelTitle.activeBorder\": \"#8839ef\",\n    \"panelTitle.activeForeground\": \"#4c4f69\",\n    \"panelTitle.inactiveForeground\": \"#6c6f85\",\n    \"peekView.border\": \"#8839ef\",\n    \"peekViewEditor.background\": \"#e6e9ef\",\n    \"peekViewEditor.matchHighlightBackground\": \"#04a5e54d\",\n    \"peekViewEditor.matchHighlightBorder\": \"#00000000\",\n    \"peekViewEditorGutter.background\": \"#e6e9ef\",\n    \"peekViewResult.background\": \"#e6e9ef\",\n    \"peekViewResult.fileForeground\": \"#4c4f69\",\n    \"peekViewResult.lineForeground\": \"#4c4f69\",\n    \"peekViewResult.matchHighlightBackground\": \"#04a5e54d\",\n    \"peekViewResult.selectionBackground\": \"#ccd0da\",\n    \"peekViewResult.selectionForeground\": \"#4c4f69\",\n    \"peekViewTitle.background\": \"#eff1f5\",\n    \"peekViewTitleDescription.foreground\": \"#5c5f77b3\",\n    \"peekViewTitleLabel.foreground\": \"#4c4f69\",\n    \"pickerGroup.border\": \"#8839ef\",\n    \"pickerGroup.foreground\": \"#8839ef\",\n    \"problemsErrorIcon.foreground\": \"#d20f39\",\n    \"problemsInfoIcon.foreground\": \"#1e66f5\",\n    \"problemsWarningIcon.foreground\": \"#fe640b\",\n    \"progressBar.background\": \"#8839ef\",\n    \"pullRequests.closed\": \"#d20f39\",\n    \"pullRequests.draft\": \"#7c7f93\",\n    \"pullRequests.merged\": \"#8839ef\",\n    \"pullRequests.notification\": \"#4c4f69\",\n    \"pullRequests.open\": \"#40a02b\",\n    \"sash.hoverBorder\": \"#8839ef\",\n    \"scrollbar.shadow\": \"#dce0e8\",\n    \"scrollbarSlider.activeBackground\": \"#ccd0da66\",\n    \"scrollbarSlider.background\": \"#acb0be80\",\n    \"scrollbarSlider.hoverBackground\": \"#9ca0b0\",\n    \"selection.background\": \"#8839ef66\",\n    \"settings.dropdownBackground\": \"#bcc0cc\",\n    \"settings.dropdownListBorder\": \"#00000000\",\n    \"settings.focusedRowBackground\": \"#acb0be33\",\n    \"settings.headerForeground\": \"#4c4f69\",\n    \"settings.modifiedItemIndicator\": \"#8839ef\",\n    \"settings.numberInputBackground\": \"#bcc0cc\",\n    \"settings.numberInputBorder\": \"#00000000\",\n    \"settings.textInputBackground\": \"#bcc0cc\",\n    \"settings.textInputBorder\": \"#00000000\",\n    \"sideBar.background\": \"#e6e9ef\",\n    \"sideBar.border\": \"#00000000\",\n    \"sideBar.dropBackground\": \"#8839ef33\",\n    \"sideBar.foreground\": \"#4c4f69\",\n    \"sideBarSectionHeader.background\": \"#e6e9ef\",\n    \"sideBarSectionHeader.foreground\": \"#4c4f69\",\n    \"sideBarTitle.foreground\": \"#8839ef\",\n    \"statusBar.background\": \"#dce0e8\",\n    \"statusBar.border\": \"#00000000\",\n    \"statusBar.debuggingBackground\": \"#fe640b\",\n    \"statusBar.debuggingBorder\": \"#00000000\",\n    \"statusBar.debuggingForeground\": \"#dce0e8\",\n    \"statusBar.foreground\": \"#4c4f69\",\n    \"statusBar.noFolderBackground\": \"#dce0e8\",\n    \"statusBar.noFolderBorder\": \"#00000000\",\n    \"statusBar.noFolderForeground\": \"#4c4f69\",\n    \"statusBarItem.activeBackground\": \"#acb0be66\",\n    \"statusBarItem.errorBackground\": \"#00000000\",\n    \"statusBarItem.errorForeground\": \"#d20f39\",\n    \"statusBarItem.hoverBackground\": \"#acb0be33\",\n    \"statusBarItem.prominentBackground\": \"#00000000\",\n    \"statusBarItem.prominentForeground\": \"#8839ef\",\n    \"statusBarItem.prominentHoverBackground\": \"#acb0be33\",\n    \"statusBarItem.remoteBackground\": \"#1e66f5\",\n    \"statusBarItem.remoteForeground\": \"#dce0e8\",\n    \"statusBarItem.warningBackground\": \"#00000000\",\n    \"statusBarItem.warningForeground\": \"#fe640b\",\n    \"symbolIcon.arrayForeground\": \"#fe640b\",\n    \"symbolIcon.booleanForeground\": \"#8839ef\",\n    \"symbolIcon.classForeground\": \"#df8e1d\",\n    \"symbolIcon.colorForeground\": \"#ea76cb\",\n    \"symbolIcon.constantForeground\": \"#fe640b\",\n    \"symbolIcon.constructorForeground\": \"#7287fd\",\n    \"symbolIcon.enumeratorForeground\": \"#df8e1d\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#df8e1d\",\n    \"symbolIcon.eventForeground\": \"#ea76cb\",\n    \"symbolIcon.fieldForeground\": \"#4c4f69\",\n    \"symbolIcon.fileForeground\": \"#8839ef\",\n    \"symbolIcon.folderForeground\": \"#8839ef\",\n    \"symbolIcon.functionForeground\": \"#1e66f5\",\n    \"symbolIcon.interfaceForeground\": \"#df8e1d\",\n    \"symbolIcon.keyForeground\": \"#179299\",\n    \"symbolIcon.keywordForeground\": \"#8839ef\",\n    \"symbolIcon.methodForeground\": \"#1e66f5\",\n    \"symbolIcon.moduleForeground\": \"#4c4f69\",\n    \"symbolIcon.namespaceForeground\": \"#df8e1d\",\n    \"symbolIcon.nullForeground\": \"#e64553\",\n    \"symbolIcon.numberForeground\": \"#fe640b\",\n    \"symbolIcon.objectForeground\": \"#df8e1d\",\n    \"symbolIcon.operatorForeground\": \"#179299\",\n    \"symbolIcon.packageForeground\": \"#dd7878\",\n    \"symbolIcon.propertyForeground\": \"#e64553\",\n    \"symbolIcon.referenceForeground\": \"#df8e1d\",\n    \"symbolIcon.snippetForeground\": \"#dd7878\",\n    \"symbolIcon.stringForeground\": \"#40a02b\",\n    \"symbolIcon.structForeground\": \"#179299\",\n    \"symbolIcon.textForeground\": \"#4c4f69\",\n    \"symbolIcon.typeParameterForeground\": \"#e64553\",\n    \"symbolIcon.unitForeground\": \"#4c4f69\",\n    \"symbolIcon.variableForeground\": \"#4c4f69\",\n    \"tab.activeBackground\": \"#eff1f5\",\n    \"tab.activeBorder\": \"#00000000\",\n    \"tab.activeBorderTop\": \"#8839ef\",\n    \"tab.activeForeground\": \"#8839ef\",\n    \"tab.activeModifiedBorder\": \"#df8e1d\",\n    \"tab.border\": \"#e6e9ef\",\n    \"tab.hoverBackground\": \"#ffffff\",\n    \"tab.hoverBorder\": \"#00000000\",\n    \"tab.hoverForeground\": \"#8839ef\",\n    \"tab.inactiveBackground\": \"#e6e9ef\",\n    \"tab.inactiveForeground\": \"#9ca0b0\",\n    \"tab.inactiveModifiedBorder\": \"#df8e1d4d\",\n    \"tab.lastPinnedBorder\": \"#8839ef\",\n    \"tab.unfocusedActiveBackground\": \"#e6e9ef\",\n    \"tab.unfocusedActiveBorder\": \"#00000000\",\n    \"tab.unfocusedActiveBorderTop\": \"#8839ef4d\",\n    \"tab.unfocusedInactiveBackground\": \"#d6dbe5\",\n    \"table.headerBackground\": \"#ccd0da\",\n    \"table.headerForeground\": \"#4c4f69\",\n    \"terminal.ansiBlack\": \"#bcc0cc\",\n    \"terminal.ansiBlue\": \"#1e66f5\",\n    \"terminal.ansiBrightBlack\": \"#6c6f85\",\n    \"terminal.ansiBrightBlue\": \"#1e66f5\",\n    \"terminal.ansiBrightCyan\": \"#04a5e5\",\n    \"terminal.ansiBrightGreen\": \"#40a02b\",\n    \"terminal.ansiBrightMagenta\": \"#ea76cb\",\n    \"terminal.ansiBrightRed\": \"#d20f39\",\n    \"terminal.ansiBrightWhite\": \"#5c5f77\",\n    \"terminal.ansiBrightYellow\": \"#df8e1d\",\n    \"terminal.ansiCyan\": \"#04a5e5\",\n    \"terminal.ansiGreen\": \"#40a02b\",\n    \"terminal.ansiMagenta\": \"#ea76cb\",\n    \"terminal.ansiRed\": \"#d20f39\",\n    \"terminal.ansiWhite\": \"#acb0be\",\n    \"terminal.ansiYellow\": \"#df8e1d\",\n    \"terminal.border\": \"#acb0be\",\n    \"terminal.dropBackground\": \"#8839ef33\",\n    \"terminal.foreground\": \"#4c4f69\",\n    \"terminal.inactiveSelectionBackground\": \"#acb0be80\",\n    \"terminal.selectionBackground\": \"#acb0be\",\n    \"terminal.tab.activeBorder\": \"#8839ef\",\n    \"terminalCommandDecoration.defaultBackground\": \"#acb0be\",\n    \"terminalCommandDecoration.errorBackground\": \"#d20f39\",\n    \"terminalCommandDecoration.successBackground\": \"#40a02b\",\n    \"terminalCursor.background\": \"#eff1f5\",\n    \"terminalCursor.foreground\": \"#dc8a78\",\n    \"textBlockQuote.background\": \"#e6e9ef\",\n    \"textBlockQuote.border\": \"#dce0e8\",\n    \"textCodeBlock.background\": \"#eff1f5\",\n    \"textLink.activeForeground\": \"#04a5e5\",\n    \"textLink.foreground\": \"#1e66f5\",\n    \"textPreformat.foreground\": \"#4c4f69\",\n    \"textSeparator.foreground\": \"#8839ef\",\n    \"titleBar.activeBackground\": \"#dce0e8\",\n    \"titleBar.activeForeground\": \"#4c4f69\",\n    \"titleBar.border\": \"#00000000\",\n    \"titleBar.inactiveBackground\": \"#dce0e8\",\n    \"titleBar.inactiveForeground\": \"#4c4f6980\",\n    \"tree.inactiveIndentGuidesStroke\": \"#bcc0cc\",\n    \"tree.indentGuidesStroke\": \"#7c7f93\",\n    \"walkThrough.embeddedEditorBackground\": \"#eff1f54d\",\n    \"welcomePage.progress.background\": \"#dce0e8\",\n    \"welcomePage.progress.foreground\": \"#8839ef\",\n    \"welcomePage.tileBackground\": \"#e6e9ef\",\n    \"widget.shadow\": \"#e6e9ef80\",\n    \"window.activeBorder\": \"#00000000\",\n    \"window.inactiveBorder\": \"#00000000\"\n  },\n  \"displayName\": \"Catppuccin Latte\",\n  \"name\": \"catppuccin-latte\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"boolean\": {\n      \"foreground\": \"#fe640b\"\n    },\n    \"builtinAttribute.attribute.library:rust\": {\n      \"foreground\": \"#1e66f5\"\n    },\n    \"class.builtin:python\": {\n      \"foreground\": \"#8839ef\"\n    },\n    \"class:python\": {\n      \"foreground\": \"#df8e1d\"\n    },\n    \"constant.builtin.readonly:nix\": {\n      \"foreground\": \"#8839ef\"\n    },\n    \"enumMember\": {\n      \"foreground\": \"#179299\"\n    },\n    \"function.decorator:python\": {\n      \"foreground\": \"#fe640b\"\n    },\n    \"generic.attribute:rust\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"heading\": {\n      \"foreground\": \"#d20f39\"\n    },\n    \"number\": {\n      \"foreground\": \"#fe640b\"\n    },\n    \"pol\": {\n      \"foreground\": \"#dd7878\"\n    },\n    \"property.readonly:javascript\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"property.readonly:javascriptreact\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"property.readonly:typescript\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"property.readonly:typescriptreact\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"selfKeyword\": {\n      \"foreground\": \"#d20f39\"\n    },\n    \"text.emph\": {\n      \"fontStyle\": \"italic\",\n      \"foreground\": \"#d20f39\"\n    },\n    \"text.math\": {\n      \"foreground\": \"#dd7878\"\n    },\n    \"text.strong\": {\n      \"fontStyle\": \"bold\",\n      \"foreground\": \"#d20f39\"\n    },\n    \"tomlArrayKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#1e66f5\"\n    },\n    \"tomlTableKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#1e66f5\"\n    },\n    \"type.defaultLibrary:go\": {\n      \"foreground\": \"#8839ef\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#e64553\"\n    },\n    \"variable.readonly.defaultLibrary:go\": {\n      \"foreground\": \"#8839ef\"\n    },\n    \"variable.readonly:javascript\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"variable.readonly:javascriptreact\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"variable.readonly:scala\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"variable.readonly:typescript\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"variable.readonly:typescriptreact\": {\n      \"foreground\": \"#4c4f69\"\n    },\n    \"variable.typeHint:python\": {\n      \"foreground\": \"#df8e1d\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"text\",\n        \"source\",\n        \"variable.other.readwrite\",\n        \"punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#7c7f93\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#9ca0b0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"variable.other.constant\",\n        \"entity.name.constant\",\n        \"constant.language.boolean\",\n        \"constant.language.false\",\n        \"constant.language.true\",\n        \"keyword.other.unit.user-defined\",\n        \"keyword.other.unit.suffix.floating-point\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"keyword.operator.word\",\n        \"keyword.operator.new\",\n        \"variable.language.super\",\n        \"support.type.primitive\",\n        \"storage.type\",\n        \"storage.modifier\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.documentation\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"punctuation.accessor\",\n        \"punctuation.definition.generic\",\n        \"meta.function.closure punctuation.section.parameters\",\n        \"punctuation.definition.tag\",\n        \"punctuation.separator.key-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.method\",\n        \"support.function\",\n        \"support.function.misc\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.other.inherited-class\",\n        \"support.class\",\n        \"meta.function-call.constructor\",\n        \"entity.name.struct\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.enum\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.enum variable.other.readwrite\",\n        \"variable.other.enummember\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"meta.property.object\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type\",\n        \"meta.type-alias\",\n        \"support.type\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation variable.function\",\n        \"meta.annotation variable.annotation.function\",\n        \"meta.annotation punctuation.definition.annotation\",\n        \"meta.decorator\",\n        \"punctuation.decorator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"meta.function.parameters\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"support.function.builtin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.documentation\",\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive\",\n        \"punctuation.definition.directive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.typeparameters\",\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.this\",\n        \"variable.language.this punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.template variable\",\n        \"string variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.new\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.specifier.extern.cpp\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.scope-resolution.template.call.cpp\",\n        \"entity.name.scope-resolution.parameter.cpp\",\n        \"entity.name.scope-resolution.cpp\",\n        \"entity.name.scope-resolution.function.definition.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.doxygen\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.reference.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"meta.interpolation.cs\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.documentation.cs\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css entity.other.attribute-name.class.css\",\n        \"entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.operator.css\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.other.attribute-name.pseudo-class\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"source.css constant.other.unicode-range\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"source.css variable.parameter.url\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.vendored.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-value variable\",\n        \"source.css meta.property-value variable.other.less\",\n        \"source.css meta.property-value variable.other.less punctuation.definition.variable.less\",\n        \"meta.definition.variable.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-list variable\",\n        \"meta.property-list variable.other.less\",\n        \"meta.property-list variable.other.less punctuation.definition.variable.less\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.percentage.css\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"source.css meta.attribute-selector\",\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.definition.ini\",\n        \"punctuation.support.type.property-name.json\",\n        \"support.type.property-name.json\",\n        \"punctuation.support.type.property-name.toml\",\n        \"support.type.property-name.toml\",\n        \"entity.name.tag.yaml\",\n        \"punctuation.support.type.property-name.yaml\",\n        \"support.type.property-name.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.json\",\n        \"constant.language.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.anchor.yaml\",\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name.table\",\n        \"entity.name.section.group-title.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.time.datetime.offset.toml\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.anchor.yaml\",\n        \"punctuation.definition.alias.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.document.begin.yaml\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.header.from-file\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.from-file.diff\",\n        \"punctuation.definition.to-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.go\",\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.parameters.go\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.go\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"variable.graphql\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#dd7878\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql\",\n      \"settings\": {\n        \"foreground\": \"#dd7878\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.doctype\",\n        \"meta.tag.sgml.doctype punctuation.definition.tag\",\n        \"meta.tag.metadata.doctype entity.name.tag\",\n        \"meta.tag.metadata.doctype punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html constant.character.entity\",\n        \"text.html constant.character.entity punctuation\",\n        \"constant.character.entity.xml\",\n        \"constant.character.entity.xml punctuation\",\n        \"constant.character.entity.js.jsx\",\n        \"constant.charactger.entity.js.jsx punctuation\",\n        \"constant.character.entity.tsx\",\n        \"constant.character.entity.tsx punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\",\n        \"support.class.component.jsx\",\n        \"support.class.component.tsx\",\n        \"support.class.component.vue\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.annotation\",\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.enum.java\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.javadoc.java keyword.other.documentation.javadoc.java\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"meta.export variable.other.readwrite.js\",\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.property.js\",\n        \"variable.other.property.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.jsdoc\",\n        \"comment.block.documentation variable.other\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.jsdoc\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console.js\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.node\",\n        \"support.type.object.module.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.implements\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.null.js\",\n        \"constant.language.null.ts\",\n        \"constant.language.undefined.js\",\n        \"constant.language.undefined.ts\",\n        \"support.type.builtin.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.generic\",\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.declaration.function.arrow.js\",\n        \"storage.type.function.arrow.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.decorator.ts\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.in.js\",\n        \"keyword.operator.expression.in.ts\",\n        \"keyword.operator.expression.infer.ts\",\n        \"keyword.operator.expression.instanceof.js\",\n        \"keyword.operator.expression.instanceof.ts\",\n        \"keyword.operator.expression.is\",\n        \"keyword.operator.expression.keyof.ts\",\n        \"keyword.operator.expression.of.js\",\n        \"keyword.operator.expression.of.ts\",\n        \"keyword.operator.expression.typeof.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.julia\",\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"text.tex keyword.control.preamble\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"text.tex support.function.be\",\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.general.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#dd7878\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-dash.documentation.lua storage.type.annotation.lua\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.double-dash.documentation.lua entity.name.variable.lua\",\n        \"comment.line.double-dash.documentation.lua variable.lua\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.1.markdown punctuation.definition.heading.markdown\",\n        \"heading.1.markdown\",\n        \"heading.1.quarto punctuation.definition.heading.quarto\",\n        \"heading.1.quarto\",\n        \"markup.heading.atx.1.mdx\",\n        \"markup.heading.atx.1.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.heading-0.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.2.markdown punctuation.definition.heading.markdown\",\n        \"heading.2.markdown\",\n        \"heading.2.quarto punctuation.definition.heading.quarto\",\n        \"heading.2.quarto\",\n        \"markup.heading.atx.2.mdx\",\n        \"markup.heading.atx.2.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.2.markdown\",\n        \"markup.heading.heading-1.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.3.markdown punctuation.definition.heading.markdown\",\n        \"heading.3.markdown\",\n        \"heading.3.quarto punctuation.definition.heading.quarto\",\n        \"heading.3.quarto\",\n        \"markup.heading.atx.3.mdx\",\n        \"markup.heading.atx.3.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-2.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.4.markdown punctuation.definition.heading.markdown\",\n        \"heading.4.markdown\",\n        \"heading.4.quarto punctuation.definition.heading.quarto\",\n        \"heading.4.quarto\",\n        \"markup.heading.atx.4.mdx\",\n        \"markup.heading.atx.4.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-3.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.5.markdown punctuation.definition.heading.markdown\",\n        \"heading.5.markdown\",\n        \"heading.5.quarto punctuation.definition.heading.quarto\",\n        \"heading.5.quarto\",\n        \"markup.heading.atx.5.mdx\",\n        \"markup.heading.atx.5.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-4.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.6.markdown punctuation.definition.heading.markdown\",\n        \"heading.6.markdown\",\n        \"heading.6.quarto punctuation.definition.heading.quarto\",\n        \"heading.6.quarto\",\n        \"markup.heading.atx.6.mdx\",\n        \"markup.heading.atx.6.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-5.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\",\n        \"foreground\": \"#6c6f85\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.link\",\n        \"markup.underline.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.link.title\",\n        \"text.html.quarto punctuation.definition.link.title\",\n        \"string.other.link.title.markdown\",\n        \"string.other.link.title.quarto\",\n        \"markup.link\",\n        \"punctuation.definition.constant.markdown\",\n        \"punctuation.definition.constant.quarto\",\n        \"constant.other.reference.link.markdown\",\n        \"constant.other.reference.link.quarto\",\n        \"markup.substitution.attribute-reference\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7287fd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.raw.markdown\",\n        \"punctuation.definition.raw.quarto\",\n        \"markup.inline.raw.string.markdown\",\n        \"markup.inline.raw.string.quarto\",\n        \"markup.raw.block.markdown\",\n        \"markup.raw.block.quarto\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block punctuation.definition\",\n        \"markup.raw support.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7c7f93\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\",\n        \"punctuation.definition.quote.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin.markdown\",\n        \"punctuation.definition.list.begin.quarto\",\n        \"markup.list.bullet\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.quarto\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.multipart.nix\",\n        \"entity.other.attribute-name.single.nix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#7287fd\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.path.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.attribute.builtin\",\n        \"meta.attribute.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.php punctuation.definition.variable.php\",\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"text.html.php support.function\",\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.phpdoc.php\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.magic.python\",\n        \"meta.function-call.arguments.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\",\n        \"variable.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow.python\",\n        \"keyword.operator.logical.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.python\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.token.decorator.python\",\n        \"meta.function.decorator.identifier.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.decorator.python\",\n        \"punctuation.definition.decorator.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.exception.python\",\n        \"support.function.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.indexed-name.python\",\n        \"meta.item-access.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.string.python\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.python\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.anchor.regexp\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.ts\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\",\n        \"keyword.other.back-reference.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#40a02b\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.range.regexp\",\n      \"settings\": {\n        \"foreground\": \"#dc8a78\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.numeric.regexp\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.no-capture.regexp\",\n        \"meta.assertion.look-ahead.regexp\",\n        \"meta.assertion.negative-look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation.rust\",\n        \"meta.annotation.rust punctuation\",\n        \"meta.attribute.rust\",\n        \"punctuation.definition.attribute.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute.rust string.quoted.double.rust\",\n        \"meta.attribute.rust string.quoted.single.char.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.macro.rules.rust\",\n        \"storage.type.module.rust\",\n        \"storage.modifier.rust\",\n        \"storage.type.struct.rust\",\n        \"storage.type.enum.rust\",\n        \"storage.type.trait.rust\",\n        \"storage.type.union.rust\",\n        \"storage.type.impl.rust\",\n        \"storage.type.rust\",\n        \"storage.type.function.rust\",\n        \"storage.type.type.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.numeric.rust\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"meta.generic.rust\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.impl.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.trait.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.union.rust\",\n      \"settings\": {\n        \"foreground\": \"#df8e1d\"\n      }\n    },\n    {\n      \"scope\": \"meta.enum.rust storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.macro.rust\",\n        \"meta.macro.rust support.function.rust\",\n        \"entity.name.function.macro.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.lifetime.rust\",\n        \"entity.name.type.lifetime\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.rust constant.other.placeholder.rust\",\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.return-type.rust meta.generic.rust storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust\",\n      \"settings\": {\n        \"foreground\": \"#1e66f5\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.brackets.angle.rust\",\n      \"settings\": {\n        \"foreground\": \"#04a5e5\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.caps.rust\",\n      \"settings\": {\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function.definition.rust variable.other.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e64553\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust variable.other.rust\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.self.rust\",\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.metavariable.name.rust\",\n        \"meta.macro.metavariable.rust keyword.operator.macro.dollar.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.shebang\",\n        \"comment.line.shebang punctuation.definition.comment\",\n        \"comment.line.shebang\",\n        \"punctuation.definition.comment.shebang.shell\",\n        \"meta.shebang.shell\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ea76cb\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.shebang constant.language\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\",\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d20f39\"\n      }\n    },\n    {\n      \"scope\": \"meta.string meta.interpolation.parameter.shell variable.other.readwrite\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fe640b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell punctuation.section.interpolation\",\n        \"punctuation.definition.evaluation.backticks.shell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#179299\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#8839ef\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell variable.other.normal.shell\",\n      \"settings\": {\n        \"foreground\": \"#4c4f69\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { catppuccinLatte as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,kBAAkB,OAAO,MAAM,CAAC;IAClC,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,qCAAqC;QACrC,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,8BAA8B;QAC9B,mCAAmC;QACnC,oBAAoB;QACpB,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,oCAAoC;QACpC,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,wCAAwC;QACxC,kCAAkC;QAClC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,0BAA0B;QAC1B,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,6BAA6B;QAC7B,kCAAkC;QAClC,6BAA6B;QAC7B,kCAAkC;QAClC,oCAAoC;QACpC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,sCAAsC;QACtC,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,qCAAqC;QACrC,wCAAwC;QACxC,+BAA+B;QAC/B,oCAAoC;QACpC,+BAA+B;QAC/B,oCAAoC;QACpC,sCAAsC;QACtC,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,6BAA6B;QAC7B,sCAAsC;QACtC,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,0CAA0C;QAC1C,sCAAsC;QACtC,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,uDAAuD;QACvD,2DAA2D;QAC3D,wDAAwD;QACxD,oEAAoE;QACpE,sDAAsD;QACtD,wCAAwC;QACxC,0CAA0C;QAC1C,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,uCAAuC;QACvC,6CAA6C;QAC7C,gDAAgD;QAChD,iDAAiD;QACjD,0CAA0C;QAC1C,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,4CAA4C;QAC5C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,sCAAsC;QACtC,0CAA0C;QAC1C,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,wCAAwC;QACxC,oCAAoC;QACpC,uCAAuC;QACvC,uCAAuC;QACvC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,6BAA6B;QAC7B,eAAe;QACf,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,oCAAoC;QACpC,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,wCAAwC;QACxC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,uBAAuB;QACvB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,uBAAuB;QACvB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,4BAA4B;QAC5B,sBAAsB;QACtB,kBAAkB;QAClB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,gCAAgC;QAChC,mCAAmC;QACnC,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,uBAAuB;QACvB,wCAAwC;QACxC,gCAAgC;QAChC,6BAA6B;QAC7B,+CAA+C;QAC/C,6CAA6C;QAC7C,+CAA+C;QAC/C,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wCAAwC;QACxC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,WAAW;YACT,cAAc;QAChB;QACA,2CAA2C;YACzC,cAAc;QAChB;QACA,wBAAwB;YACtB,cAAc;QAChB;QACA,gBAAgB;YACd,cAAc;QAChB;QACA,iCAAiC;YAC/B,cAAc;QAChB;QACA,cAAc;YACZ,cAAc;QAChB;QACA,6BAA6B;YAC3B,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,WAAW;YACT,cAAc;QAChB;QACA,UAAU;YACR,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,eAAe;YACb,cAAc;QAChB;QACA,aAAa;YACX,aAAa;YACb,cAAc;QAChB;QACA,aAAa;YACX,cAAc;QAChB;QACA,eAAe;YACb,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,uCAAuC;YACrC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,4BAA4B;YAC1B,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}