{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/nix.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"<PERSON>\", \"fileTypes\": [\"nix\"], \"name\": \"nix\", \"patterns\": [{ \"include\": \"#expression\" }], \"repository\": { \"attribute-bind\": { \"patterns\": [{ \"include\": \"#attribute-name\" }, { \"include\": \"#attribute-bind-from-equals\" }] }, \"attribute-bind-from-equals\": { \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.bind.nix\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.bind.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"attribute-inherit\": { \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.inherit.nix\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.inherit.nix\" } }, \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.arguments.nix\" } }, \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"begin\": \"\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.arguments.nix\" } }, \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-name-single\" }, { \"include\": \"#others\" }] }, { \"include\": \"#expression\" }] }, { \"begin\": \"(?=[a-zA-Z_])\", \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-name-single\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"attribute-name\": { \"patterns\": [{ \"match\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\", \"name\": \"entity.other.attribute-name.multipart.nix\" }, { \"match\": \"\\\\.\" }, { \"include\": \"#string-quoted\" }, { \"include\": \"#interpolation\" }] }, \"attribute-name-single\": { \"match\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\", \"name\": \"entity.other.attribute-name.single.nix\" }, \"attrset-contents\": { \"patterns\": [{ \"include\": \"#attribute-inherit\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-bind\" }, { \"include\": \"#others\" }] }, \"attrset-definition\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"patterns\": [{ \"include\": \"#attrset-contents\" }] }, { \"begin\": \"(?<=\\\\})\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"attrset-definition-brace-opened\": { \"patterns\": [{ \"begin\": \"(?<=\\\\})\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"(?=.?)\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"patterns\": [{ \"include\": \"#attrset-contents\" }] }] }, \"attrset-for-sure\": { \"patterns\": [{ \"begin\": \"(?=\\\\brec\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\brec\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#others\" }] }, { \"include\": \"#attrset-definition\" }, { \"include\": \"#others\" }] }, { \"begin\": \"(?=\\\\{\\\\s*(\\\\}|[^,?]*(=|;)))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition\" }, { \"include\": \"#others\" }] }] }, \"attrset-or-function\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset-or-function.nix\" } }, \"end\": \"(?=([\\\\])};]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": `(?=(\\\\s*\\\\}|\\\\\"|\\\\binherit\\\\b|\\\\$\\\\{|\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*(\\\\s*\\\\.|\\\\s*=[^=])))`, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"(?=(\\\\.\\\\.\\\\.|\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\\\\s*[,?]))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }, { \"include\": \"#bad-reserved\" }, { \"begin\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.maybe.nix\" } }, \"end\": \"(?=([\\\\])};]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(?=\\\\.)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"\\\\s*(\\\\,)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }, { \"begin\": \"(?==)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attribute-bind-from-equals\" }, { \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"(?=\\\\?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-parameter-default\" }, { \"begin\": \"\\\\,\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }] }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"bad-reserved\": { \"match\": \"(?<![\\\\w'-])(if|then|else|assert|with|let|in|rec|inherit)(?![\\\\w'-])\", \"name\": \"invalid.illegal.reserved.nix\" }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*([^*]|\\\\*[^\\\\/])*\", \"end\": \"\\\\*\\\\/\", \"name\": \"comment.block.nix\", \"patterns\": [{ \"include\": \"#comment-remark\" }] }, { \"begin\": \"\\\\#\", \"end\": \"$\", \"name\": \"comment.line.number-sign.nix\", \"patterns\": [{ \"include\": \"#comment-remark\" }] }] }, \"comment-remark\": { \"captures\": { \"1\": { \"name\": \"markup.bold.comment.nix\" } }, \"match\": \"(TODO|FIXME|BUG|!!!):?\" }, \"constants\": { \"patterns\": [{ \"begin\": \"\\\\b(builtins|true|false|null)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"constant.language.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"\\\\b(scopedImport|import|isNull|abort|throw|baseNameOf|dirOf|removeAttrs|map|toString|derivationStrict|derivation)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"support.function.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"\\\\b\\\\d+\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"constant.numeric.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"expression\": { \"patterns\": [{ \"include\": \"#parens-and-cont\" }, { \"include\": \"#list-and-cont\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#with-assert\" }, { \"include\": \"#function-for-sure\" }, { \"include\": \"#attrset-for-sure\" }, { \"include\": \"#attrset-or-function\" }, { \"include\": \"#let\" }, { \"include\": \"#if\" }, { \"include\": \"#operator-unary\" }, { \"include\": \"#constants\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#parameter-name-and-cont\" }, { \"include\": \"#others\" }] }, \"expression-cont\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#list\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#function-for-sure\" }, { \"include\": \"#attrset-for-sure\" }, { \"include\": \"#attrset-or-function\" }, { \"match\": \"(\\\\bor\\\\b|\\\\.|==|!=|!|<=|<|>=|>|&&|\\\\|\\\\||->|//|\\\\?|\\\\+\\\\+|-|\\\\*|/(?=([^*]|$))|\\\\+)\", \"name\": \"keyword.operator.nix\" }, { \"include\": \"#constants\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#parameter-name\" }, { \"include\": \"#others\" }] }, \"function-body\": { \"begin\": \"(@\\\\s*([a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*)\\\\s*)?(:)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"function-body-from-colon\": { \"begin\": \"(:)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.function.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"function-contents\": { \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#function-parameter\" }, { \"include\": \"#others\" }] }, \"function-definition\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-body-from-colon\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=:)\", \"patterns\": [{ \"begin\": \"(\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*)\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.4.nix\" } }, \"end\": \"(?=:)\", \"patterns\": [{ \"begin\": \"\\\\@\", \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-until-colon-no-arg\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-until-colon-with-arg\" }] }] }, { \"include\": \"#others\" }] }, \"function-definition-brace-opened\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-body-from-colon\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-close-brace-with-arg\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#function-contents\" }] }] }, { \"include\": \"#others\" }] }, \"function-for-sure\": { \"patterns\": [{ \"begin\": `(?=(\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\\\\s*[:@]|\\\\{[^}]*\\\\}\\\\s*:|\\\\{[^#}\"'/=]*[,?]))`, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition\" }] }] }, \"function-header-close-brace-no-arg\": { \"begin\": \"\\\\}\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.nix\" } }, \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#others\" }] }, \"function-header-close-brace-with-arg\": { \"begin\": \"\\\\}\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.nix\" } }, \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-terminal-arg\" }, { \"include\": \"#others\" }] }, \"function-header-open-brace\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.2.nix\" } }, \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#function-contents\" }] }, \"function-header-terminal-arg\": { \"begin\": \"(?=@)\", \"end\": \"(?=:)\", \"patterns\": [{ \"begin\": \"\\\\@\", \"end\": \"(?=:)\", \"patterns\": [{ \"begin\": \"(\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*)\", \"end\": \"(?=:)\", \"name\": \"variable.parameter.function.3.nix\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"function-header-until-colon-no-arg\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-open-brace\" }, { \"include\": \"#function-header-close-brace-no-arg\" }] }, \"function-header-until-colon-with-arg\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#function-header-open-brace\" }, { \"include\": \"#function-header-close-brace-with-arg\" }] }, \"function-parameter\": { \"patterns\": [{ \"begin\": \"(\\\\.\\\\.\\\\.)\", \"end\": \"(,|(?=\\\\}))\", \"name\": \"keyword.operator.nix\", \"patterns\": [{ \"include\": \"#others\" }] }, { \"begin\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.1.nix\" } }, \"end\": \"(,|(?=\\\\}))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#function-parameter-default\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#others\" }] }, \"function-parameter-default\": { \"begin\": \"\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=[,}])\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"if\": { \"begin\": \"(?=\\\\bif\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\bif\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\bth(?=en\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<=th)en\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\bel(?=se\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<=el)se\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"illegal\": { \"match\": \".\", \"name\": \"invalid.illegal\" }, \"interpolation\": { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.nix\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nix\" } }, \"name\": \"meta.embedded\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"let\": { \"begin\": \"(?=\\\\blet\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\blet\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(in|else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(?=\\\\{)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#attrset-contents\" }] }, { \"begin\": \"(^|(?<=\\\\}))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"include\": \"#others\" }] }, { \"include\": \"#attrset-contents\" }, { \"include\": \"#others\" }] }, { \"begin\": \"\\\\bin\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"list\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.nix\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"list-and-cont\": { \"begin\": \"(?=\\\\[)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#list\" }, { \"include\": \"#expression-cont\" }] }, \"operator-unary\": { \"match\": \"(!|-)\", \"name\": \"keyword.operator.unary.nix\" }, \"others\": { \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#illegal\" }] }, \"parameter-name\": { \"captures\": { \"0\": { \"name\": \"variable.parameter.name.nix\" } }, \"match\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\" }, \"parameter-name-and-cont\": { \"begin\": \"\\\\b[a-zA-Z_][a-zA-Z0-9_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.name.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, \"parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.expression.nix\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.expression.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"parens-and-cont\": { \"begin\": \"(?=\\\\()\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#expression-cont\" }] }, \"string\": { \"patterns\": [{ \"begin\": \"(?=\\\\'\\\\')\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\'\\\\'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.other.start.nix\" } }, \"end\": \"\\\\'\\\\'(?!\\\\$|\\\\'|\\\\\\\\.)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.other.end.nix\" } }, \"name\": \"string.quoted.other.nix\", \"patterns\": [{ \"match\": \"\\\\'\\\\'(\\\\$|\\\\'|\\\\\\\\.)\", \"name\": \"constant.character.escape.nix\" }, { \"include\": \"#interpolation\" }] }, { \"include\": \"#expression-cont\" }] }, { \"begin\": '(?=\\\\\")', \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#string-quoted\" }, { \"include\": \"#expression-cont\" }] }, { \"begin\": \"(~?[a-zA-Z0-9\\\\._\\\\-+]*(\\\\/[a-zA-Z0-9\\\\._\\\\-+]+)+)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.path.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"(<[a-zA-Z0-9\\\\._\\\\-+]+(\\\\/[a-zA-Z0-9\\\\._\\\\-+]+)*>)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.spath.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"([a-zA-Z][a-zA-Z0-9+\\\\-\\\\.]*:[a-zA-Z0-9\\\\%\\\\/?:\\\\@\\\\&=+$\\\\,\\\\-_\\\\.!\\\\~\\\\*\\\\']+)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.url.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"string-quoted\": { \"begin\": '\\\\\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.double.start.nix\" } }, \"end\": '\\\\\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.double.end.nix\" } }, \"name\": \"string.quoted.double.nix\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.nix\" }, { \"include\": \"#interpolation\" }] }, \"whitespace\": { \"match\": \"\\\\s+\" }, \"with-assert\": { \"begin\": \"(?<![\\\\w'-])(with|assert)(?![\\\\w'-])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\;\", \"patterns\": [{ \"include\": \"#expression\" }] } }, \"scopeName\": \"source.nix\" });\nvar nix = [\n  lang\n];\n\nexport { nix as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAc;KAAE;IAAE,cAAc;QAAE,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAyB;gCAAG;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;gBAAM;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAmC,QAAQ;QAAyC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAW,OAAO;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAAa,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoB;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAoC,YAAY;gBAAC;oBAAE,SAAS,CAAC,yFAAyF,CAAC;oBAAE,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2D,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAoC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAoC,YAAY;wBAAC;4BAAE,SAAS;4BAAW,OAAO;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAAmC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAa,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,OAAO;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAAoC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,OAAO;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAA8B;gCAAG;oCAAE,WAAW;gCAAmC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAW,OAAO;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAA8B;gCAAG;oCAAE,SAAS;oCAAO,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAuB;oCAAE;oCAAG,OAAO;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAoC;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAwE,QAAQ;QAA+B;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,OAAO;oBAAU,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;oBAAK,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;QAAyB;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAM;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAU,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,SAAS;oBAAuF,QAAQ;gBAAuB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAiD,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAU,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAS,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAS,YAAY;gCAAC;oCAAE,SAAS;oCAAO,OAAO;oCAAS,YAAY;wCAAC;4CAAE,WAAW;wCAAsC;wCAAG;4CAAE,WAAW;wCAAU;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAW,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAwC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,oCAAoC;YAAE,SAAS;YAAU,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAwC;wBAAG;4BAAE,SAAS;4BAAU,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,gFAAgF,CAAC;oBAAE,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,sCAAsC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,wCAAwC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAS,OAAO;YAAS,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAS,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,OAAO;4BAAS,QAAQ;wBAAoC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,sCAAsC;YAAE,SAAS;YAAW,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAsC;aAAE;QAAC;QAAG,wCAAwC;YAAE,SAAS;YAAW,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAwC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,OAAO;oBAAe,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA8B;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,MAAM;YAAE,SAAS;YAAgB,OAAO;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAkB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAkB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAK,QAAQ;QAAkB;QAAG,iBAAiB;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,OAAO;YAAE,SAAS;YAAiB,OAAO;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAwC,YAAY;wBAAC;4BAAE,SAAS;4BAAW,OAAO;4BAAqC,YAAY;gCAAC;oCAAE,SAAS;oCAAO,OAAO;oCAAO,YAAY;wCAAC;4CAAE,WAAW;wCAAoB;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAgB,OAAO;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAmB;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAW,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAS,QAAQ;QAA6B;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAAkC;QAAG,2BAA2B;YAAE,SAAS;YAAmC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAW,OAAO;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAAU,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,OAAO;4BAA2B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,SAAS;oCAAyB,QAAQ;gCAAgC;gCAAG;oCAAE,WAAW;gCAAiB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;QAAO;QAAG,eAAe;YAAE,SAAS;YAAwC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AAC10f,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}