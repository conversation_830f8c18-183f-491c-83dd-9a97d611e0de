{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/turtle.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"<PERSON>\", \"fileTypes\": [\"turtle\", \"ttl\", \"acl\"], \"name\": \"turtle\", \"patterns\": [{ \"include\": \"#rule-constraint\" }, { \"include\": \"#iriref\" }, { \"include\": \"#prefix\" }, { \"include\": \"#prefixed-name\" }, { \"include\": \"#comment\" }, { \"include\": \"#special-predicate\" }, { \"include\": \"#literals\" }, { \"include\": \"#language-tag\" }], \"repository\": { \"boolean\": { \"match\": \"\\\\b(?i:true|false)\\\\b\", \"name\": \"constant.language.sparql\" }, \"comment\": { \"match\": \"#.*$\", \"name\": \"comment.line.number-sign.turtle\" }, \"integer\": { \"match\": \"[+-]?(?:\\\\d+|\\\\d+\\\\.\\\\d*|\\\\.\\\\d+(?:[eE][+-]?\\\\d+)?)\", \"name\": \"constant.numeric.turtle\" }, \"iriref\": { \"match\": '<[^\\\\x20-\\\\x20<>\"{}|^`\\\\\\\\]*>', \"name\": \"entity.name.type.iriref.turtle\" }, \"language-tag\": { \"captures\": { \"1\": { \"name\": \"entity.name.class.turtle\" } }, \"match\": \"@(\\\\w+)\", \"name\": \"meta.string-literal-language-tag.turtle\" }, \"literals\": { \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#numeric\" }, { \"include\": \"#boolean\" }] }, \"numeric\": { \"patterns\": [{ \"include\": \"#integer\" }] }, \"prefix\": { \"match\": \"(?i:@?base|@?prefix)\\\\s\", \"name\": \"keyword.operator.turtle\" }, \"prefixed-name\": { \"captures\": { \"1\": { \"name\": \"storage.type.PNAME_NS.turtle\" }, \"2\": { \"name\": \"support.variable.PN_LOCAL.turtle\" } }, \"match\": \"(\\\\w*:)(\\\\w*)\", \"name\": \"constant.complex.turtle\" }, \"rule-constraint\": { \"begin\": '(rule:content) (\"\"\")', \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#prefixed-name\" }] }, \"2\": { \"name\": \"string.quoted.triple.turtle\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.triple.turtle\" } }, \"name\": \"meta.rule-constraint.turtle\", \"patterns\": [{ \"include\": \"source.srs\" }] }, \"single-dquote-string-literal\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.turtle\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.turtle\" } }, \"name\": \"string.quoted.double.turtle\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"single-squote-string-literal\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.turtle\" } }, \"end\": \"'\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.turtle\" }, \"2\": { \"name\": \"invalid.illegal.newline.turtle\" } }, \"name\": \"string.quoted.single.turtle\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"special-predicate\": { \"captures\": { \"1\": { \"name\": \"keyword.control.turtle\" } }, \"match\": \"\\\\s(a)\\\\s\", \"name\": \"meta.specialPredicate.turtle\" }, \"string\": { \"patterns\": [{ \"include\": \"#triple-squote-string-literal\" }, { \"include\": \"#triple-dquote-string-literal\" }, { \"include\": \"#single-squote-string-literal\" }, { \"include\": \"#single-dquote-string-literal\" }, { \"include\": \"#triple-tick-string-literal\" }] }, \"string-character-escape\": { \"match\": \"\\\\\\\\(x\\\\h{2}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\", \"name\": \"constant.character.escape.turtle\" }, \"triple-dquote-string-literal\": { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.turtle\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.turtle\" } }, \"name\": \"string.quoted.triple.turtle\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"triple-squote-string-literal\": { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.turtle\" } }, \"end\": \"'''\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.turtle\" } }, \"name\": \"string.quoted.triple.turtle\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"triple-tick-string-literal\": { \"begin\": \"```\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.turtle\" } }, \"end\": \"```\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.turtle\" } }, \"name\": \"string.quoted.triple.turtle\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] } }, \"scopeName\": \"source.turtle\" });\nvar turtle = [\n  lang\n];\n\nexport { turtle as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;QAAU;QAAO;KAAM;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAgB;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,SAAS;YAAyB,QAAQ;QAA2B;QAAG,WAAW;YAAE,SAAS;YAAQ,QAAQ;QAAkC;QAAG,WAAW;YAAE,SAAS;YAAuD,QAAQ;QAA0B;QAAG,UAAU;YAAE,SAAS;YAAiC,QAAQ;QAAiC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;YAAW,QAAQ;QAA0C;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA2B,QAAQ;QAA0B;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;YAAiB,QAAQ;QAA0B;QAAG,mBAAmB;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;YAAa,QAAQ;QAA+B;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAuE,QAAQ;QAAmC;QAAG,gCAAgC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;IAAE;IAAG,aAAa;AAAgB;AACr4H,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}