(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/packages/collaboration/hooks.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/collaboration/hooks.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$collaboration$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/collaboration/hooks.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/packages/design-system/components/ui/avatar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Avatar": (()=>Avatar),
    "AvatarFallback": (()=>AvatarFallback),
    "AvatarImage": (()=>AvatarImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$avatar$40$1$2e$1$2e$_2102b0ade537f1e2a172941acff2e7b1$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-avatar@1.1._2102b0ade537f1e2a172941acff2e7b1/node_modules/@radix-ui/react-avatar/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
function Avatar({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$avatar$40$1$2e$1$2e$_2102b0ade537f1e2a172941acff2e7b1$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "avatar",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative flex size-8 shrink-0 overflow-hidden rounded-full", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/packages/design-system/components/ui/avatar.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = Avatar;
function AvatarImage({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$avatar$40$1$2e$1$2e$_2102b0ade537f1e2a172941acff2e7b1$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Image"], {
        "data-slot": "avatar-image",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("aspect-square size-full", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/packages/design-system/components/ui/avatar.tsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
}
_c1 = AvatarImage;
function AvatarFallback({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$avatar$40$1$2e$1$2e$_2102b0ade537f1e2a172941acff2e7b1$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fallback"], {
        "data-slot": "avatar-fallback",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-muted flex size-full items-center justify-center rounded-full", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/packages/design-system/components/ui/avatar.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
_c2 = AvatarFallback;
;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Avatar");
__turbopack_context__.k.register(_c1, "AvatarImage");
__turbopack_context__.k.register(_c2, "AvatarFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AvatarStack": (()=>AvatarStack)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$collaboration$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/collaboration/hooks.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript) <export _useOthersSuspense as useOthers>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useSelfSuspense__as__useSelf$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript) <export _useSelfSuspense as useSelf>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/components/ui/tooltip.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const PresenceAvatar = ({ info })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
        delayDuration: 0,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                    className: "h-7 w-7 bg-secondary ring-1 ring-background",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                            src: info?.avatar,
                            alt: info?.name
                        }, void 0, false, {
                            fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                            lineNumber: 23,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                            className: "text-xs",
                            children: info?.name?.slice(0, 2)
                        }, void 0, false, {
                            fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                            lineNumber: 24,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                    lineNumber: 22,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                lineNumber: 21,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                collisionPadding: 4,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: info?.name ?? 'Unknown'
                }, void 0, false, {
                    fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                    lineNumber: 30,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                lineNumber: 29,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
        lineNumber: 20,
        columnNumber: 3
    }, this);
_c = PresenceAvatar;
const AvatarStack = ()=>{
    _s();
    const others = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__["useOthers"])();
    const self = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useSelfSuspense__as__useSelf$3e$__["useSelf"])();
    const hasMoreUsers = others.length > 3;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "-space-x-1 flex items-center px-4",
        children: [
            others.slice(0, 3).map(({ connectionId, info })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PresenceAvatar, {
                    info: info
                }, connectionId, false, {
                    fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                    lineNumber: 43,
                    columnNumber: 9
                }, this)),
            hasMoreUsers && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PresenceAvatar, {
                info: {
                    name: `+${others.length - 3}`,
                    color: 'var(--color-muted-foreground)'
                }
            }, void 0, false, {
                fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                lineNumber: 47,
                columnNumber: 9
            }, this),
            self && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PresenceAvatar, {
                info: self.info
            }, void 0, false, {
                fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
                lineNumber: 55,
                columnNumber: 16
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/apps/app/app/(authenticated)/components/avatar-stack.tsx",
        lineNumber: 41,
        columnNumber: 5
    }, this);
};
_s(AvatarStack, "IdZ+DFoIXkZVm4qsA+lDJ4Z+7sU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__["useOthers"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useSelfSuspense__as__useSelf$3e$__["useSelf"]
    ];
});
_c1 = AvatarStack;
var _c, _c1;
__turbopack_context__.k.register(_c, "PresenceAvatar");
__turbopack_context__.k.register(_c1, "AvatarStack");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/app/app/(authenticated)/components/cursors.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Cursors": (()=>Cursors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$collaboration$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/collaboration/hooks.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useMyPresence__as__useMyPresence$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript) <export _useMyPresence as useMyPresence>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript) <export _useOthersSuspense as useOthers>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const Cursor = ({ name, color, x, y })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "pointer-events-none absolute top-0 left-0 z-[999] select-none transition-transform duration-100",
        style: {
            transform: `translateX(${x}px) translateY(${y}px)`
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "absolute top-0 left-0",
                width: "24",
                height: "36",
                viewBox: "0 0 24 36",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("title", {
                        children: "Cursor"
                    }, void 0, false, {
                        fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
                        lineNumber: 31,
                        columnNumber: 7
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z",
                        fill: color
                    }, void 0, false, {
                        fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
                        lineNumber: 32,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
                lineNumber: 23,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-4 left-1.5 whitespace-nowrap rounded-full px-2 py-0.5 text-white text-xs",
                style: {
                    backgroundColor: color
                },
                children: name
            }, void 0, false, {
                fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
                lineNumber: 37,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
        lineNumber: 17,
        columnNumber: 3
    }, this);
_c = Cursor;
const Cursors = ()=>{
    _s();
    /**
   * useMyPresence returns the presence of the current user and a function to update it.
   * updateMyPresence is different than the setState function returned by the useState hook from React.
   * You don't need to pass the full presence object to update it.
   * See https://liveblocks.io/docs/api-reference/liveblocks-react#useMyPresence for more information
   */ const [_cursor, updateMyPresence] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useMyPresence__as__useMyPresence$3e$__["useMyPresence"])();
    /**
   * Return all the other users in the room and their presence (a cursor position in this case)
   */ const others = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__["useOthers"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Cursors.useEffect": ()=>{
            const onPointerMove = {
                "Cursors.useEffect.onPointerMove": (event)=>{
                    // Update the user cursor position on every pointer move
                    updateMyPresence({
                        cursor: {
                            x: Math.round(event.clientX),
                            y: Math.round(event.clientY)
                        }
                    });
                }
            }["Cursors.useEffect.onPointerMove"];
            const onPointerLeave = {
                "Cursors.useEffect.onPointerLeave": ()=>{
                    // When the pointer goes out, set cursor to null
                    updateMyPresence({
                        cursor: null
                    });
                }
            }["Cursors.useEffect.onPointerLeave"];
            document.body.addEventListener('pointermove', onPointerMove);
            document.body.addEventListener('pointerleave', onPointerLeave);
            return ({
                "Cursors.useEffect": ()=>{
                    document.body.removeEventListener('pointermove', onPointerMove);
                    document.body.removeEventListener('pointerleave', onPointerLeave);
                }
            })["Cursors.useEffect"];
        }
    }["Cursors.useEffect"], [
        updateMyPresence
    ]);
    return others.map(({ connectionId, presence, info })=>{
        if (!presence.cursor) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Cursor, {
            // connectionId is an integer that is incremented at every new connections
            // Assigning a color with a modulo makes sure that a specific user has the same colors on every clients
            color: info.color,
            x: presence.cursor.x,
            y: presence.cursor.y,
            name: info?.name
        }, `cursor-${connectionId}`, false, {
            fileName: "[project]/apps/app/app/(authenticated)/components/cursors.tsx",
            lineNumber: 95,
            columnNumber: 7
        }, this);
    });
};
_s(Cursors, "eMAOYJU/6YfrnYs7AcMA1wVzVAk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useMyPresence__as__useMyPresence$3e$__["useMyPresence"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$useOthersSuspense__as__useOthers$3e$__["useOthers"]
    ];
});
_c1 = Cursors;
var _c, _c1;
__turbopack_context__.k.register(_c, "Cursor");
__turbopack_context__.k.register(_c1, "Cursors");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/app/app/actions/users/data:7793c2 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7ff338b8348af8de407d6c556f940c1261d4d2958c":"getUsers"},"apps/app/app/actions/users/get.ts",""] */ __turbopack_context__.s({
    "getUsers": (()=>getUsers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var getUsers = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7ff338b8348af8de407d6c556f940c1261d4d2958c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getUsers"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/app/app/actions/users/data:ca42c4 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f3c2a1f04377f514613be66698ae6e672570b4ae7":"searchUsers"},"apps/app/app/actions/users/search.ts",""] */ __turbopack_context__.s({
    "searchUsers": (()=>searchUsers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var searchUsers = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f3c2a1f04377f514613be66698ae6e672570b4ae7", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "searchUsers"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vc2VhcmNoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc2VydmVyJztcblxuaW1wb3J0IHtcbiAgdHlwZSBPcmdhbml6YXRpb25NZW1iZXJzaGlwLFxuICBhdXRoLFxuICBjbGVya0NsaWVudCxcbn0gZnJvbSAnQHJlcG8vYXV0aC9zZXJ2ZXInO1xuaW1wb3J0IEZ1c2UgZnJvbSAnZnVzZS5qcyc7XG5cbmNvbnN0IGdldE5hbWUgPSAodXNlcjogT3JnYW5pemF0aW9uTWVtYmVyc2hpcCk6IHN0cmluZyB8IHVuZGVmaW5lZCA9PiB7XG4gIGxldCBuYW1lID0gdXNlci5wdWJsaWNVc2VyRGF0YT8uZmlyc3ROYW1lO1xuXG4gIGlmIChuYW1lICYmIHVzZXIucHVibGljVXNlckRhdGE/Lmxhc3ROYW1lKSB7XG4gICAgbmFtZSA9IGAke25hbWV9ICR7dXNlci5wdWJsaWNVc2VyRGF0YS5sYXN0TmFtZX1gO1xuICB9IGVsc2UgaWYgKCFuYW1lKSB7XG4gICAgbmFtZSA9IHVzZXIucHVibGljVXNlckRhdGE/LmlkZW50aWZpZXI7XG4gIH1cblxuICByZXR1cm4gbmFtZTtcbn07XG5cbmV4cG9ydCBjb25zdCBzZWFyY2hVc2VycyA9IGFzeW5jIChcbiAgcXVlcnk6IHN0cmluZ1xuKTogUHJvbWlzZTxcbiAgfCB7XG4gICAgICBkYXRhOiBzdHJpbmdbXTtcbiAgICB9XG4gIHwge1xuICAgICAgZXJyb3I6IHVua25vd247XG4gICAgfVxuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBvcmdJZCB9ID0gYXdhaXQgYXV0aCgpO1xuXG4gICAgaWYgKCFvcmdJZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdOb3QgbG9nZ2VkIGluJyk7XG4gICAgfVxuXG4gICAgY29uc3QgY2xlcmsgPSBhd2FpdCBjbGVya0NsaWVudCgpO1xuXG4gICAgY29uc3QgbWVtYmVycyA9IGF3YWl0IGNsZXJrLm9yZ2FuaXphdGlvbnMuZ2V0T3JnYW5pemF0aW9uTWVtYmVyc2hpcExpc3Qoe1xuICAgICAgb3JnYW5pemF0aW9uSWQ6IG9yZ0lkLFxuICAgICAgbGltaXQ6IDEwMCxcbiAgICB9KTtcblxuICAgIGNvbnN0IHVzZXJzID0gbWVtYmVycy5kYXRhLm1hcCgodXNlcikgPT4gKHtcbiAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgbmFtZTogZ2V0TmFtZSh1c2VyKSA/PyB1c2VyLnB1YmxpY1VzZXJEYXRhPy5pZGVudGlmaWVyLFxuICAgICAgaW1hZ2VVcmw6IHVzZXIucHVibGljVXNlckRhdGE/LmltYWdlVXJsLFxuICAgIH0pKTtcblxuICAgIGNvbnN0IGZ1c2UgPSBuZXcgRnVzZSh1c2Vycywge1xuICAgICAga2V5czogWyduYW1lJ10sXG4gICAgICBtaW5NYXRjaENoYXJMZW5ndGg6IDEsXG4gICAgICB0aHJlc2hvbGQ6IDAuMyxcbiAgICB9KTtcblxuICAgIGNvbnN0IHJlc3VsdHMgPSBmdXNlLnNlYXJjaChxdWVyeSk7XG4gICAgY29uc3QgZGF0YSA9IHJlc3VsdHMubWFwKChyZXN1bHQpID0+IHJlc3VsdC5pdGVtLmlkKTtcblxuICAgIHJldHVybiB7IGRhdGEgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4geyBlcnJvciB9O1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJ5U0FxQmEifQ==
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/collaboration/room.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Room": (()=>Room)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$S44E4FBQ$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-S44E4FBQ.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$RoomProvider__as__RoomProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@liveblocks+react@2.24.2_react@19.1.0/node_modules/@liveblocks/react/dist/chunk-N6OQQVYV.js [app-client] (ecmascript) <export _RoomProvider as RoomProvider>");
'use client';
;
;
const Room = ({ id, children, authEndpoint, fallback, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LiveblocksProvider"], {
        authEndpoint: authEndpoint,
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$N6OQQVYV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$5f$RoomProvider__as__RoomProvider$3e$__["RoomProvider"], {
            id: id,
            initialPresence: {
                cursor: null
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$liveblocks$2b$react$40$2$2e$24$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$liveblocks$2f$react$2f$dist$2f$chunk$2d$S44E4FBQ$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ClientSideSuspense"], {
                fallback: fallback,
                children: children
            }, void 0, false, {
                fileName: "[project]/packages/collaboration/room.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/packages/collaboration/room.tsx",
            lineNumber: 33,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/packages/collaboration/room.tsx",
        lineNumber: 32,
        columnNumber: 3
    }, this);
_c = Room;
var _c;
__turbopack_context__.k.register(_c, "Room");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/app/app/(authenticated)/components/collaboration-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollaborationProvider": (()=>CollaborationProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$actions$2f$users$2f$data$3a$7793c2__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/apps/app/app/actions/users/data:7793c2 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$actions$2f$users$2f$data$3a$ca42c4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/apps/app/app/actions/users/data:ca42c4 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$collaboration$2f$room$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/collaboration/room.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
const CollaborationProvider = ({ orgId, children })=>{
    const resolveUsers = async ({ userIds })=>{
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$actions$2f$users$2f$data$3a$7793c2__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getUsers"])(userIds);
        if ('error' in response) {
            throw new Error('Problem resolving users');
        }
        return response.data;
    };
    const resolveMentionSuggestions = async ({ text })=>{
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$actions$2f$users$2f$data$3a$ca42c4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["searchUsers"])(text);
        if ('error' in response) {
            throw new Error('Problem resolving mention suggestions');
        }
        return response.data;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$collaboration$2f$room$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Room"], {
        id: `${orgId}:presence`,
        authEndpoint: "/api/collaboration/auth",
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "px-3 text-muted-foreground text-xs",
            children: "Loading..."
        }, void 0, false, {
            fileName: "[project]/apps/app/app/(authenticated)/components/collaboration-provider.tsx",
            lineNumber: 40,
            columnNumber: 9
        }, void 0),
        resolveUsers: resolveUsers,
        resolveMentionSuggestions: resolveMentionSuggestions,
        children: children
    }, void 0, false, {
        fileName: "[project]/apps/app/app/(authenticated)/components/collaboration-provider.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_c = CollaborationProvider;
var _c;
__turbopack_context__.k.register(_c, "CollaborationProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_046808c5._.js.map