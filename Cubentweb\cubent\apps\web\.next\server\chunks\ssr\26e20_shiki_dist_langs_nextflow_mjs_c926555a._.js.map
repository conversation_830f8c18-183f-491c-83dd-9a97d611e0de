{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/nextflow.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Nextflow\", \"name\": \"nextflow\", \"patterns\": [{ \"include\": \"#nfl-rules\" }], \"repository\": { \"implicit-variables\": { \"patterns\": [{ \"match\": \"(?<=[^\\\\.\\\\w]|^)(params|nextflow|workflow|launchDir|moduleDir|projectDir|workDir)\\\\b\", \"name\": \"variable.language.nextflow\" }] }, \"include-statement\": { \"patterns\": [{ \"match\": \"^\\\\b(include)\\\\b\", \"name\": \"keyword.nextflow\" }, { \"match\": \"\\\\b(from)\\\\b\", \"name\": \"keyword.nextflow\" }] }, \"nfl-rules\": { \"patterns\": [{ \"include\": \"#process-def\" }, { \"include\": \"#workflow-def\" }, { \"include\": \"#code-block\" }, { \"include\": \"#include-statement\" }, { \"include\": \"#implicit-variables\" }, { \"begin\": \"(\\\\w*\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"source.nextflow-groovy\" }] } }, \"comment\": \"method call and parens\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#nfl-rules\" }] }, { \"begin\": \"{\", \"comment\": \"braces\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#nfl-rules\" }] }, { \"include\": \"source.nextflow-groovy\" }] }, \"process-body\": { \"patterns\": [{ \"match\": \"(?:accelerator|afterScript|beforeScript|cache|cpus|conda|container|containerOptions|clusterOptions|debug|disk|echo|errorStrategy|executor|ext|label|machineType|maxErrors|maxForks|maxRetries|memory|module|penv|pod|publishDir|queue|resourceLabels|scratch|stageInMode|stageOutMode|storeDir|tag|time)\\\\b\", \"name\": \"entity.name.function.nextflow\" }, { \"match\": \"(?:input|output|when|script|shell|exec):\", \"name\": \"constant.block.nextflow\" }, { \"match\": \"\\\\b(tuple|set|path|file|val|stdout)(\\\\(|\\\\s)\", \"name\": \"entity.name.function.nextflow\" }, { \"include\": \"#implicit-variables\" }, { \"begin\": \"(\\\\w*\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"source.nextflow-groovy\" }] } }, \"comment\": \"method call and parens\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#process-body\" }] }, { \"begin\": \"{\", \"comment\": \"braces\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#process-body\" }] }, { \"include\": \"source.nextflow-groovy#comments\" }, { \"include\": \"source.nextflow-groovy#support-functions\" }, { \"include\": \"source.nextflow-groovy#keyword\" }, { \"include\": \"source.nextflow-groovy#values\" }, { \"include\": \"source.nextflow-groovy#anonymous-classes-and-new\" }, { \"include\": \"source.nextflow-groovy#types\" }, { \"include\": \"source.nextflow-groovy#parens\" }, { \"include\": \"source.nextflow-groovy#closures\" }, { \"include\": \"source.nextflow-groovy#braces\" }] }, \"process-def\": { \"begin\": `^\\\\s*(process)\\\\s+(\\\\w+|\"[^\"]+\"|'[^']+')\\\\s*{`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.nextflow\" }, \"2\": { \"name\": \"function.nextflow\" } }, \"end\": \"}\", \"name\": \"process.nextflow\", \"patterns\": [{ \"include\": \"#process-body\" }] }, \"workflow-body\": { \"patterns\": [{ \"include\": \"#implicit-variables\" }, { \"match\": \"(?:take|main|emit):\", \"name\": \"constant.block.nextflow\" }, { \"match\": \"(?<=[\\\\s\\\\.])(branch|buffer|close|collate|collect|collectFile|combine|concat|count|countBy|cross|distinct|dump|filter|first|flatMap|flatten|groupTuple|ifEmpty|join|last|map|max|merge|min|mix|multiMap|randomSample|reduce|set|splitCsv|splitFasta|splitFastq|splitText|sum|take|tap|toInteger|toList|toSortedList|transpose|unique|until|view)(?=[{(\\\\s])\", \"name\": \"entity.name.function.nextflow\" }, { \"captures\": { \"1\": { \"name\": \"keyword.nextflow\" }, \"2\": { \"name\": \"entity.name.function.nextflow\" } }, \"comment\": \"Channel factory single line\", \"match\": \"\\\\b((?:C|c)hannel\\\\.)((fromList|fromPath|fromFilePairs|fromSRA|from|of|empty|value|watchPath)\\\\W)?\" }, { \"begin\": \"\\\\b((?:C|c)hannel)\\\\s*$\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.nextflow\" } }, \"comment\": \"Channel factory multi-line\", \"end\": \"(^\\\\s*)(?:(\\\\.)(fromList|fromPath|fromFilePairs|fromSRA|from|of|empty|value|watchPath)\\\\W)?\", \"endCaptures\": { \"1\": { \"name\": \"keyword.nextflow\" }, \"2\": { \"name\": \"keyword.nextflow\" }, \"3\": { \"name\": \"entity.name.function.nextflow\" } } }, { \"begin\": \"(\\\\w*\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"source.nextflow-groovy\" }] } }, \"comment\": \"method call and parens\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#workflow-body\" }] }, { \"begin\": \"{\", \"comment\": \"braces\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#workflow-body\" }] }, { \"include\": \"source.nextflow-groovy#comments\" }, { \"include\": \"source.nextflow-groovy#support-functions\" }, { \"include\": \"source.nextflow-groovy#keyword\" }, { \"include\": \"source.nextflow-groovy#values\" }, { \"include\": \"source.nextflow-groovy#anonymous-classes-and-new\" }, { \"include\": \"source.nextflow-groovy#types\" }, { \"include\": \"source.nextflow-groovy#parens\" }, { \"include\": \"source.nextflow-groovy#closures\" }, { \"include\": \"source.nextflow-groovy#braces\" }] }, \"workflow-def\": { \"begin\": `^\\\\s*(workflow)(?:\\\\s+(\\\\w+|\"[^\"]+\"|'[^']+'))?\\\\s*{`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.nextflow\" }, \"2\": { \"name\": \"constant.nextflow\" } }, \"end\": \"}\", \"name\": \"workflow.nextflow\", \"patterns\": [{ \"include\": \"#workflow-body\" }] } }, \"scopeName\": \"source.nextflow\", \"aliases\": [\"nf\"] });\nvar nextflow = [\n  lang\n];\n\nexport { nextflow as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,QAAQ;IAAY,YAAY;QAAC;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwF,QAAQ;gBAA6B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAmB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;oBAAE;oBAAG,WAAW;oBAA0B,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAU,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+S,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAgD,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;oBAAE;oBAAG,WAAW;oBAA0B,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAU,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA2C;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAmD;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS,CAAC,6CAA6C,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA+V,QAAQ;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAA+B,SAAS;gBAAqG;gBAAG;oBAAE,SAAS;oBAA2B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,WAAW;oBAA8B,OAAO;oBAA+F,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;gBAAE;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;oBAAE;oBAAG,WAAW;oBAA0B,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAU,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA2C;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAmD;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS,CAAC,mDAAmD,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAmB,WAAW;QAAC;KAAK;AAAC;AACr1J,IAAI,WAAW;IACb;CACD", "ignoreList": [0], "debugId": null}}]}