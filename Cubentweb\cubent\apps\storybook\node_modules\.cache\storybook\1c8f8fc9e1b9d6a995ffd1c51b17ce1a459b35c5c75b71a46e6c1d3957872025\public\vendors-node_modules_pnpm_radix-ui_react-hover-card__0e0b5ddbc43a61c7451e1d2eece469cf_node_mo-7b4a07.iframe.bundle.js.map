{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-hover-card__0e0b5ddbc43a61c7451e1d2eece469cf_node_mo-7b4a07.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-hover-card@_0e0b5ddbc43a61c7451e1d2eece469cf/node_modules/@radix-ui/react-hover-card/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/hover-card.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { jsx } from \"react/jsx-runtime\";\nvar originalBodyUserSelect;\nvar HOVERCARD_NAME = \"HoverCard\";\nvar [createHoverCardContext, createHoverCardScope] = createContextScope(HOVERCARD_NAME, [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [HoverCardProvider, useHoverCardContext] = createHoverCardContext(HOVERCARD_NAME);\nvar HoverCard = (props) => {\n  const {\n    __scopeHoverCard,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    openDelay = 700,\n    closeDelay = 300\n  } = props;\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const openTimerRef = React.useRef(0);\n  const closeTimerRef = React.useRef(0);\n  const hasSelectionRef = React.useRef(false);\n  const isPointerDownOnContentRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: HOVERCARD_NAME\n  });\n  const handleOpen = React.useCallback(() => {\n    clearTimeout(closeTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => setOpen(true), openDelay);\n  }, [openDelay, setOpen]);\n  const handleClose = React.useCallback(() => {\n    clearTimeout(openTimerRef.current);\n    if (!hasSelectionRef.current && !isPointerDownOnContentRef.current) {\n      closeTimerRef.current = window.setTimeout(() => setOpen(false), closeDelay);\n    }\n  }, [closeDelay, setOpen]);\n  const handleDismiss = React.useCallback(() => setOpen(false), [setOpen]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(openTimerRef.current);\n      clearTimeout(closeTimerRef.current);\n    };\n  }, []);\n  return /* @__PURE__ */ jsx(\n    HoverCardProvider,\n    {\n      scope: __scopeHoverCard,\n      open,\n      onOpenChange: setOpen,\n      onOpen: handleOpen,\n      onClose: handleClose,\n      onDismiss: handleDismiss,\n      hasSelectionRef,\n      isPointerDownOnContentRef,\n      children: /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children })\n    }\n  );\n};\nHoverCard.displayName = HOVERCARD_NAME;\nvar TRIGGER_NAME = \"HoverCardTrigger\";\nvar HoverCardTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeHoverCard, ...triggerProps } = props;\n    const context = useHoverCardContext(TRIGGER_NAME, __scopeHoverCard);\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.a,\n      {\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        ...triggerProps,\n        ref: forwardedRef,\n        onPointerEnter: composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen)),\n        onPointerLeave: composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose)),\n        onFocus: composeEventHandlers(props.onFocus, context.onOpen),\n        onBlur: composeEventHandlers(props.onBlur, context.onClose),\n        onTouchStart: composeEventHandlers(props.onTouchStart, (event) => event.preventDefault())\n      }\n    ) });\n  }\n);\nHoverCardTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"HoverCardPortal\";\nvar [PortalProvider, usePortalContext] = createHoverCardContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar HoverCardPortal = (props) => {\n  const { __scopeHoverCard, forceMount, children, container } = props;\n  const context = useHoverCardContext(PORTAL_NAME, __scopeHoverCard);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeHoverCard, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nHoverCardPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"HoverCardContent\";\nvar HoverCardContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeHoverCard);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useHoverCardContext(CONTENT_NAME, props.__scopeHoverCard);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(\n      HoverCardContentImpl,\n      {\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        ...contentProps,\n        onPointerEnter: composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen)),\n        onPointerLeave: composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose)),\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nHoverCardContent.displayName = CONTENT_NAME;\nvar HoverCardContentImpl = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeHoverCard,\n    onEscapeKeyDown,\n    onPointerDownOutside,\n    onFocusOutside,\n    onInteractOutside,\n    ...contentProps\n  } = props;\n  const context = useHoverCardContext(CONTENT_NAME, __scopeHoverCard);\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [containSelection, setContainSelection] = React.useState(false);\n  React.useEffect(() => {\n    if (containSelection) {\n      const body = document.body;\n      originalBodyUserSelect = body.style.userSelect || body.style.webkitUserSelect;\n      body.style.userSelect = \"none\";\n      body.style.webkitUserSelect = \"none\";\n      return () => {\n        body.style.userSelect = originalBodyUserSelect;\n        body.style.webkitUserSelect = originalBodyUserSelect;\n      };\n    }\n  }, [containSelection]);\n  React.useEffect(() => {\n    if (ref.current) {\n      const handlePointerUp = () => {\n        setContainSelection(false);\n        context.isPointerDownOnContentRef.current = false;\n        setTimeout(() => {\n          const hasSelection = document.getSelection()?.toString() !== \"\";\n          if (hasSelection) context.hasSelectionRef.current = true;\n        });\n      };\n      document.addEventListener(\"pointerup\", handlePointerUp);\n      return () => {\n        document.removeEventListener(\"pointerup\", handlePointerUp);\n        context.hasSelectionRef.current = false;\n        context.isPointerDownOnContentRef.current = false;\n      };\n    }\n  }, [context.isPointerDownOnContentRef, context.hasSelectionRef]);\n  React.useEffect(() => {\n    if (ref.current) {\n      const tabbables = getTabbableNodes(ref.current);\n      tabbables.forEach((tabbable) => tabbable.setAttribute(\"tabindex\", \"-1\"));\n    }\n  });\n  return /* @__PURE__ */ jsx(\n    DismissableLayer,\n    {\n      asChild: true,\n      disableOutsidePointerEvents: false,\n      onInteractOutside,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside: composeEventHandlers(onFocusOutside, (event) => {\n        event.preventDefault();\n      }),\n      onDismiss: context.onDismiss,\n      children: /* @__PURE__ */ jsx(\n        PopperPrimitive.Content,\n        {\n          ...popperScope,\n          ...contentProps,\n          onPointerDown: composeEventHandlers(contentProps.onPointerDown, (event) => {\n            if (event.currentTarget.contains(event.target)) {\n              setContainSelection(true);\n            }\n            context.hasSelectionRef.current = false;\n            context.isPointerDownOnContentRef.current = true;\n          }),\n          ref: composedRefs,\n          style: {\n            ...contentProps.style,\n            userSelect: containSelection ? \"text\" : void 0,\n            // Safari requires prefix\n            WebkitUserSelect: containSelection ? \"text\" : void 0,\n            // re-namespace exposed content custom properties\n            ...{\n              \"--radix-hover-card-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n              \"--radix-hover-card-content-available-width\": \"var(--radix-popper-available-width)\",\n              \"--radix-hover-card-content-available-height\": \"var(--radix-popper-available-height)\",\n              \"--radix-hover-card-trigger-width\": \"var(--radix-popper-anchor-width)\",\n              \"--radix-hover-card-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n          }\n        }\n      )\n    }\n  );\n});\nvar ARROW_NAME = \"HoverCardArrow\";\nvar HoverCardArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeHoverCard, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nHoverCardArrow.displayName = ARROW_NAME;\nfunction excludeTouch(eventHandler) {\n  return (event) => event.pointerType === \"touch\" ? void 0 : eventHandler();\n}\nfunction getTabbableNodes(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nvar Root2 = HoverCard;\nvar Trigger = HoverCardTrigger;\nvar Portal = HoverCardPortal;\nvar Content2 = HoverCardContent;\nvar Arrow2 = HoverCardArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  HoverCard,\n  HoverCardArrow,\n  HoverCardContent,\n  HoverCardPortal,\n  HoverCardTrigger,\n  Portal,\n  Root2 as Root,\n  Trigger,\n  createHoverCardScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}