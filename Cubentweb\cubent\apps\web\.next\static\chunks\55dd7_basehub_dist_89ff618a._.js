(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/chunk-VU7JBGRB.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/react/code-block/client/context.ts
__turbopack_context__.s({
    "CodeBlockContext": (()=>CodeBlockContext),
    "useCodeBlockContext": (()=>useCodeBlockContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var CodeBlockContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(void 0);
var useCodeBlockContext = ()=>{
    const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(CodeBlockContext);
    if (ctx === void 0) {
        throw new Error("Context not found. Make sure to render CodeBlock on top this hook call.");
    }
    return ctx;
};
;
}}),
"[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/client-BDEKBT54.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>client_default)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$basehub$40$8$2e$2$2e$7_$40$babel$2b$runtim_3aebfa8e064bb601162c04844d38dd02$2f$node_modules$2f$basehub$2f$dist$2f$chunk$2d$VU7JBGRB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/chunk-VU7JBGRB.js [app-client] (ecmascript)");
// src/react/code-block/client.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
"use client";
;
;
;
;
var CodeBlockClientController = ({ children, snippets, storeSnippetSelection, groupId })=>{
    "use client";
    const isSingleSnippet = snippets.length === 1;
    const [activeSnippet, setActiveSnippet] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(snippets[0]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CodeBlockClientController.useEffect": ()=>{
            const snippets2 = document.querySelectorAll(`[data-snippet-group-id="${groupId}"]`);
            snippets2.forEach({
                "CodeBlockClientController.useEffect": (div)=>{
                    if (div.getAttribute("data-snippet-id") === (activeSnippet == null ? void 0 : activeSnippet.id)) {
                        div.style.display = "block";
                        div.setAttribute("data-active", "true");
                    } else {
                        div.style.display = "none";
                        div.setAttribute("data-active", "false");
                    }
                }
            }["CodeBlockClientController.useEffect"]);
        }
    }["CodeBlockClientController.useEffect"], [
        activeSnippet,
        groupId
    ]);
    const localStorageKey = !isSingleSnippet && storeSnippetSelection ? `__bshb-active-snippet-for-${snippets.map((s)=>s.label || s.id).sort((a, b)=>a.localeCompare(b)).join("-")}` : null;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CodeBlockClientController.useEffect": ()=>{
            var _a;
            if (!localStorageKey) return;
            try {
                const activeSnippetFromLS = (_a = window.localStorage) == null ? void 0 : _a.getItem(localStorageKey);
                if (activeSnippetFromLS) {
                    const snippet = snippets.find({
                        "CodeBlockClientController.useEffect.snippet": (s)=>s.label === activeSnippetFromLS || s.id === activeSnippetFromLS
                    }["CodeBlockClientController.useEffect.snippet"]);
                    if (snippet) setActiveSnippet(snippet);
                }
            } catch (e) {}
            function handleSnippetChange(event) {
                if (event.detail.key !== localStorageKey) return;
                const newActiveSnippet = snippets.find({
                    "CodeBlockClientController.useEffect.handleSnippetChange.newActiveSnippet": (s)=>s.label === event.detail.snippet.label || s.id === event.detail.snippet.id
                }["CodeBlockClientController.useEffect.handleSnippetChange.newActiveSnippet"]);
                if (newActiveSnippet) {
                    setActiveSnippet(newActiveSnippet);
                }
            }
            window.addEventListener("__bshb-snippet-change", handleSnippetChange);
            return ({
                "CodeBlockClientController.useEffect": ()=>{
                    window.removeEventListener("__bshb-snippet-change", handleSnippetChange);
                }
            })["CodeBlockClientController.useEffect"];
        }
    }["CodeBlockClientController.useEffect"], [
        localStorageKey,
        snippets
    ]);
    const selectSnippet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CodeBlockClientController.useCallback[selectSnippet]": (snippet)=>{
            var _a;
            setActiveSnippet(snippet);
            if (!localStorageKey) return;
            try {
                (_a = window.localStorage) == null ? void 0 : _a.setItem(localStorageKey, snippet.label || snippet.id);
            } catch (e) {}
            const event = new CustomEvent("__bshb-snippet-change", {
                detail: {
                    key: localStorageKey,
                    snippet
                }
            });
            window.dispatchEvent(event);
        }
    }["CodeBlockClientController.useCallback[selectSnippet]"], [
        localStorageKey
    ]);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$basehub$40$8$2e$2$2e$7_$40$babel$2b$runtim_3aebfa8e064bb601162c04844d38dd02$2f$node_modules$2f$basehub$2f$dist$2f$chunk$2d$VU7JBGRB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CodeBlockContext"].Provider, {
        value: {
            snippets,
            activeSnippet,
            selectSnippet,
            groupId
        },
        children
    });
};
var client_default = CodeBlockClientController;
;
}}),
"[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/chunk-BG6MEPCE.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "__async": (()=>__async),
    "__objRest": (()=>__objRest),
    "__spreadProps": (()=>__spreadProps),
    "__spreadValues": (()=>__spreadValues)
});
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __spreadValues = (a, b)=>{
    for(var prop in b || (b = {}))if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)){
        if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    }
    return a;
};
var __spreadProps = (a, b)=>__defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude)=>{
    var target = {};
    for(var prop in source)if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];
    if (source != null && __getOwnPropSymbols) for (var prop of __getOwnPropSymbols(source)){
        if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop)) target[prop] = source[prop];
    }
    return target;
};
var __async = (__this, __arguments, generator)=>{
    return new Promise((resolve, reject)=>{
        var fulfilled = (value)=>{
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        };
        var rejected = (value)=>{
            try {
                step(generator.throw(value));
            } catch (e) {
                reject(e);
            }
        };
        var step = (x)=>x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
        step((generator = generator.apply(__this, __arguments)).next());
    });
};
;
}}),
"[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/next-image.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseHubImage": (()=>BaseHubImage),
    "basehubImageLoader": (()=>basehubImageLoader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$basehub$40$8$2e$2$2e$7_$40$babel$2b$runtim_3aebfa8e064bb601162c04844d38dd02$2f$node_modules$2f$basehub$2f$dist$2f$chunk$2d$BG6MEPCE$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/chunk-BG6MEPCE.js [app-client] (ecmascript)");
// src/next/image/primitive.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
"use client";
;
;
;
;
var r2URL_deprecated = `https://basehub.earth`;
var assetsURL = `https://assets.basehub.com`;
var basehubImageLoader = ({ src, width, quality })=>{
    let url;
    try {
        url = new URL(src);
    } catch (error) {
        throw new Error(`Invalid BaseHub Image URL: ${src}

Expected origin to be one of:
- ${r2URL_deprecated} (deprecated)
- ${assetsURL}
`);
    }
    const params = [
        `width=${width}`,
        `quality=${quality || 90}`
    ];
    if (url.href.includes(r2URL_deprecated)) {
        if (url.pathname.startsWith("/cdn-cgi/image/")) {
            const [_empty, _cdnThing, _imageThing, currentParams = "", ...rest] = url.pathname.split("/");
            const filteredParams = currentParams.split(",").filter((param)=>{
                return !param.startsWith("width=") && !param.startsWith("quality=") && !param.startsWith("w=") && !param.startsWith("q=") && // also strip height because next.js doesn't need it
                !param.startsWith("h=") && !param.startsWith("height=");
            });
            let newParams = [
                ...filteredParams,
                ...params
            ].join(",");
            if (newParams.includes("format=") === false) {
                newParams += ",format=auto";
            }
            url.pathname = `/cdn-cgi/image/${newParams}/${rest.join("/")}`;
        } else {
            params.push("format=auto");
            url.pathname = `/cdn-cgi/image/${params.join(",")}${url.pathname}`;
        }
    } else if (url.href.includes(assetsURL)) {
        params.forEach((param)=>{
            const [key, value] = param.split("=");
            if (!key || !value) return;
            url.searchParams.set(key, value);
        });
        if (url.searchParams.has("format") === false) {
            url.searchParams.set("format", "auto");
        }
        url.searchParams.delete("height");
        url.searchParams.delete("h");
    }
    const imageURL = new URL(assetsURL);
    if (url.href.includes(r2URL_deprecated)) {
        if (url.pathname.startsWith("/cdn-cgi/image/")) {
            const [_empty, _cdnThing, _imageThing, currentParams = "", ...rest] = url.pathname.split("/");
            imageURL.pathname = rest.join("/");
            imageURL.search = currentParams.split(",").join("&");
        } else {
            imageURL.pathname = url.pathname;
            imageURL.search = url.search;
        }
    } else if (url.href.includes(assetsURL)) {
        imageURL.pathname = url.pathname;
        imageURL.search = url.search;
    } else {
        return src;
    }
    return imageURL.toString();
};
var BaseHubImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    var _a, _b, _c;
    const unoptimized = (_c = (_b = props.unoptimized) != null ? _b : (_a = props.src.toString().split("?")[0]) == null ? void 0 : _a.endsWith(".svg")) != null ? _c : void 0;
    return(// eslint-disable-next-line jsx-a11y/alt-text
    /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$basehub$40$8$2e$2$2e$7_$40$babel$2b$runtim_3aebfa8e064bb601162c04844d38dd02$2f$node_modules$2f$basehub$2f$dist$2f$chunk$2d$BG6MEPCE$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__spreadProps"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$basehub$40$8$2e$2$2e$7_$40$babel$2b$runtim_3aebfa8e064bb601162c04844d38dd02$2f$node_modules$2f$basehub$2f$dist$2f$chunk$2d$BG6MEPCE$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__spreadValues"])({}, props), {
        placeholder: props.placeholder,
        loader: basehubImageLoader,
        unoptimized,
        ref
    })));
});
;
}}),
}]);

//# sourceMappingURL=55dd7_basehub_dist_89ff618a._.js.map