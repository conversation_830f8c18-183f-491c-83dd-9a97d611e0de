{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/desktop.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Desktop\", \"name\": \"desktop\", \"patterns\": [{ \"include\": \"#layout\" }, { \"include\": \"#keywords\" }, { \"include\": \"#values\" }, { \"include\": \"#inCommands\" }, { \"include\": \"#inCategories\" }], \"repository\": { \"inCategories\": { \"patterns\": [{ \"match\": \"(?<=^Categories.*)AudioVideo|(?<=^Categories.*)Audio|(?<=^Categories.*)Video|(?<=^Categories.*)Development|(?<=^Categories.*)Education|(?<=^Categories.*)Game|(?<=^Categories.*)Graphics|(?<=^Categories.*)Network|(?<=^Categories.*)Office|(?<=^Categories.*)Science|(?<=^Categories.*)Settings|(?<=^Categories.*)System|(?<=^Categories.*)Utility\", \"name\": \"markup.bold\" }] }, \"inCommands\": { \"patterns\": [{ \"match\": \"(?<=^Exec.*\\\\s)-+\\\\S+\", \"name\": \"variable.parameter\" }, { \"match\": \"(?<=^Exec.*)\\\\s\\\\%[fFuUick]\\\\s\", \"name\": \"variable.language\" }, { \"match\": '\".*\"', \"name\": \"string\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"^Type\\\\b|^Version\\\\b|^Name\\\\b|^GenericName\\\\b|^NoDisplay\\\\b|^Comment\\\\b|^Icon\\\\b|^Hidden\\\\b|^OnlyShowIn\\\\b|^NotShowIn\\\\b|^DBusActivatable\\\\b|^TryExec\\\\b|^Exec\\\\b|^Path\\\\b|^Terminal\\\\b|^Actions\\\\b|^MimeType\\\\b|^Categories\\\\b|^Implements\\\\b|^Keywords\\\\b|^StartupNotify\\\\b|^StartupWMClass\\\\b|^URL\\\\b|^PrefersNonDefaultGPU\\\\b|^Encoding\\\\b\", \"name\": \"keyword\" }, { \"match\": \"^X-[A-z 0-9 -]*\", \"name\": \"keyword.other\" }, { \"match\": \"(?<!^)\\\\[.+\\\\]\", \"name\": \"constant.language\" }, { \"match\": \"^GtkTheme\\\\b|^MetacityTheme\\\\b|^IconTheme\\\\b|^CursorTheme\\\\b|^ButtonLayout\\\\b|^ApplicationFont\\\\b\", \"name\": \"keyword\" }] }, \"layout\": { \"patterns\": [{ \"begin\": \"^\\\\[Desktop\", \"end\": \"\\\\]\", \"name\": \"markup.heading\" }, { \"begin\": \"^\\\\[X-\\\\w*\", \"end\": \"\\\\]\", \"name\": \"markup.heading\" }, { \"match\": \"^\\\\s*#.*\", \"name\": \"comment\" }, { \"match\": \";\", \"name\": \"strong\" }] }, \"values\": { \"patterns\": [{ \"match\": \"(?<=^\\\\S+)=\", \"name\": \"keyword.operator\" }, { \"match\": \"\\\\btrue\\\\b|\\\\bfalse\\\\b\", \"name\": \"variable.other\" }, { \"match\": \"(?<=^Version.*)\\\\d+(\\\\.{0,1}\\\\d*)\", \"name\": \"variable.other\" }] } }, \"scopeName\": \"source.desktop\" });\nvar desktop = [\n  lang\n];\n\nexport { desktop as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAgB;KAAE;IAAE,cAAc;QAAE,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuV,QAAQ;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAkC,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAS;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkV,QAAQ;gBAAU;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAgB;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAAqG,QAAQ;gBAAU;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,OAAO;oBAAO,QAAQ;gBAAiB;gBAAG;oBAAE,SAAS;oBAAc,OAAO;oBAAO,QAAQ;gBAAiB;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAU;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAS;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAiB;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAiB;aAAE;QAAC;IAAE;IAAG,aAAa;AAAiB;AACx+D,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}