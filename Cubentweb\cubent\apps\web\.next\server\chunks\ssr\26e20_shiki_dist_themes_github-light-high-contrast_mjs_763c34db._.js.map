{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/github-light-high-contrast.mjs"], "sourcesContent": ["var githubLightHighContrast = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#ef5b48\",\n    \"activityBar.background\": \"#ffffff\",\n    \"activityBar.border\": \"#20252c\",\n    \"activityBar.foreground\": \"#0e1116\",\n    \"activityBar.inactiveForeground\": \"#0e1116\",\n    \"activityBarBadge.background\": \"#0349b4\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"badge.background\": \"#0349b4\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumb.activeSelectionForeground\": \"#0e1116\",\n    \"breadcrumb.focusForeground\": \"#0e1116\",\n    \"breadcrumb.foreground\": \"#0e1116\",\n    \"breadcrumbPicker.background\": \"#ffffff\",\n    \"button.background\": \"#055d20\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#024c1a\",\n    \"button.secondaryBackground\": \"#acb6c0\",\n    \"button.secondaryForeground\": \"#0e1116\",\n    \"button.secondaryHoverBackground\": \"#ced5dc\",\n    \"checkbox.background\": \"#e7ecf0\",\n    \"checkbox.border\": \"#20252c\",\n    \"debugConsole.errorForeground\": \"#a0111f\",\n    \"debugConsole.infoForeground\": \"#4b535d\",\n    \"debugConsole.sourceForeground\": \"#744500\",\n    \"debugConsole.warningForeground\": \"#603700\",\n    \"debugConsoleInputIcon.foreground\": \"#512598\",\n    \"debugIcon.breakpointForeground\": \"#a0111f\",\n    \"debugTokenExpression.boolean\": \"#024c1a\",\n    \"debugTokenExpression.error\": \"#86061d\",\n    \"debugTokenExpression.name\": \"#023b95\",\n    \"debugTokenExpression.number\": \"#024c1a\",\n    \"debugTokenExpression.string\": \"#032563\",\n    \"debugTokenExpression.value\": \"#032563\",\n    \"debugToolBar.background\": \"#ffffff\",\n    \"descriptionForeground\": \"#0e1116\",\n    \"diffEditor.insertedLineBackground\": \"#82e5964d\",\n    \"diffEditor.insertedTextBackground\": \"#43c66380\",\n    \"diffEditor.removedLineBackground\": \"#ffc1bc4d\",\n    \"diffEditor.removedTextBackground\": \"#ee5a5d66\",\n    \"dropdown.background\": \"#ffffff\",\n    \"dropdown.border\": \"#20252c\",\n    \"dropdown.foreground\": \"#0e1116\",\n    \"dropdown.listBackground\": \"#ffffff\",\n    \"editor.background\": \"#ffffff\",\n    \"editor.findMatchBackground\": \"#744500\",\n    \"editor.findMatchHighlightBackground\": \"#f0ce5380\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#26a148\",\n    \"editor.foldBackground\": \"#66707b1a\",\n    \"editor.foreground\": \"#0e1116\",\n    \"editor.inactiveSelectionBackground\": \"#66707b\",\n    \"editor.lineHighlightBackground\": \"#e7ecf0\",\n    \"editor.linkedEditingBackground\": \"#0349b412\",\n    \"editor.selectionBackground\": \"#0e1116\",\n    \"editor.selectionForeground\": \"#ffffff\",\n    \"editor.selectionHighlightBackground\": \"#26a14840\",\n    \"editor.stackFrameHighlightBackground\": \"#b58407\",\n    \"editor.wordHighlightBackground\": \"#e7ecf080\",\n    \"editor.wordHighlightBorder\": \"#acb6c099\",\n    \"editor.wordHighlightStrongBackground\": \"#acb6c04d\",\n    \"editor.wordHighlightStrongBorder\": \"#acb6c099\",\n    \"editorBracketHighlight.foreground1\": \"#0349b4\",\n    \"editorBracketHighlight.foreground2\": \"#055d20\",\n    \"editorBracketHighlight.foreground3\": \"#744500\",\n    \"editorBracketHighlight.foreground4\": \"#a0111f\",\n    \"editorBracketHighlight.foreground5\": \"#971368\",\n    \"editorBracketHighlight.foreground6\": \"#622cbc\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#0e1116\",\n    \"editorBracketMatch.background\": \"#26a14840\",\n    \"editorBracketMatch.border\": \"#26a14899\",\n    \"editorCursor.foreground\": \"#0349b4\",\n    \"editorGroup.border\": \"#20252c\",\n    \"editorGroupHeader.tabsBackground\": \"#ffffff\",\n    \"editorGroupHeader.tabsBorder\": \"#20252c\",\n    \"editorGutter.addedBackground\": \"#26a148\",\n    \"editorGutter.deletedBackground\": \"#ee5a5d\",\n    \"editorGutter.modifiedBackground\": \"#b58407\",\n    \"editorIndentGuide.activeBackground\": \"#0e11163d\",\n    \"editorIndentGuide.background\": \"#0e11161f\",\n    \"editorInlayHint.background\": \"#acb6c033\",\n    \"editorInlayHint.foreground\": \"#0e1116\",\n    \"editorInlayHint.paramBackground\": \"#acb6c033\",\n    \"editorInlayHint.paramForeground\": \"#0e1116\",\n    \"editorInlayHint.typeBackground\": \"#acb6c033\",\n    \"editorInlayHint.typeForeground\": \"#0e1116\",\n    \"editorLineNumber.activeForeground\": \"#0e1116\",\n    \"editorLineNumber.foreground\": \"#88929d\",\n    \"editorOverviewRuler.border\": \"#ffffff\",\n    \"editorWhitespace.foreground\": \"#acb6c0\",\n    \"editorWidget.background\": \"#ffffff\",\n    \"errorForeground\": \"#a0111f\",\n    \"focusBorder\": \"#0349b4\",\n    \"foreground\": \"#0e1116\",\n    \"gitDecoration.addedResourceForeground\": \"#055d20\",\n    \"gitDecoration.conflictingResourceForeground\": \"#873800\",\n    \"gitDecoration.deletedResourceForeground\": \"#a0111f\",\n    \"gitDecoration.ignoredResourceForeground\": \"#66707b\",\n    \"gitDecoration.modifiedResourceForeground\": \"#744500\",\n    \"gitDecoration.submoduleResourceForeground\": \"#0e1116\",\n    \"gitDecoration.untrackedResourceForeground\": \"#055d20\",\n    \"icon.foreground\": \"#0e1116\",\n    \"input.background\": \"#ffffff\",\n    \"input.border\": \"#20252c\",\n    \"input.foreground\": \"#0e1116\",\n    \"input.placeholderForeground\": \"#66707b\",\n    \"keybindingLabel.foreground\": \"#0e1116\",\n    \"list.activeSelectionBackground\": \"#acb6c033\",\n    \"list.activeSelectionForeground\": \"#0e1116\",\n    \"list.focusBackground\": \"#dff7ff\",\n    \"list.focusForeground\": \"#0e1116\",\n    \"list.highlightForeground\": \"#0349b4\",\n    \"list.hoverBackground\": \"#e7ecf0\",\n    \"list.hoverForeground\": \"#0e1116\",\n    \"list.inactiveFocusBackground\": \"#dff7ff\",\n    \"list.inactiveSelectionBackground\": \"#acb6c033\",\n    \"list.inactiveSelectionForeground\": \"#0e1116\",\n    \"minimapSlider.activeBackground\": \"#88929d47\",\n    \"minimapSlider.background\": \"#88929d33\",\n    \"minimapSlider.hoverBackground\": \"#88929d3d\",\n    \"notificationCenterHeader.background\": \"#e7ecf0\",\n    \"notificationCenterHeader.foreground\": \"#0e1116\",\n    \"notifications.background\": \"#ffffff\",\n    \"notifications.border\": \"#20252c\",\n    \"notifications.foreground\": \"#0e1116\",\n    \"notificationsErrorIcon.foreground\": \"#a0111f\",\n    \"notificationsInfoIcon.foreground\": \"#0349b4\",\n    \"notificationsWarningIcon.foreground\": \"#744500\",\n    \"panel.background\": \"#ffffff\",\n    \"panel.border\": \"#20252c\",\n    \"panelInput.border\": \"#20252c\",\n    \"panelTitle.activeBorder\": \"#ef5b48\",\n    \"panelTitle.activeForeground\": \"#0e1116\",\n    \"panelTitle.inactiveForeground\": \"#0e1116\",\n    \"pickerGroup.border\": \"#20252c\",\n    \"pickerGroup.foreground\": \"#0e1116\",\n    \"progressBar.background\": \"#0349b4\",\n    \"quickInput.background\": \"#ffffff\",\n    \"quickInput.foreground\": \"#0e1116\",\n    \"scrollbar.shadow\": \"#66707b33\",\n    \"scrollbarSlider.activeBackground\": \"#88929d47\",\n    \"scrollbarSlider.background\": \"#88929d33\",\n    \"scrollbarSlider.hoverBackground\": \"#88929d3d\",\n    \"settings.headerForeground\": \"#0e1116\",\n    \"settings.modifiedItemIndicator\": \"#b58407\",\n    \"sideBar.background\": \"#ffffff\",\n    \"sideBar.border\": \"#20252c\",\n    \"sideBar.foreground\": \"#0e1116\",\n    \"sideBarSectionHeader.background\": \"#ffffff\",\n    \"sideBarSectionHeader.border\": \"#20252c\",\n    \"sideBarSectionHeader.foreground\": \"#0e1116\",\n    \"sideBarTitle.foreground\": \"#0e1116\",\n    \"statusBar.background\": \"#ffffff\",\n    \"statusBar.border\": \"#20252c\",\n    \"statusBar.debuggingBackground\": \"#a0111f\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.focusBorder\": \"#0349b480\",\n    \"statusBar.foreground\": \"#0e1116\",\n    \"statusBar.noFolderBackground\": \"#ffffff\",\n    \"statusBarItem.activeBackground\": \"#0e11161f\",\n    \"statusBarItem.focusBorder\": \"#0349b4\",\n    \"statusBarItem.hoverBackground\": \"#0e111614\",\n    \"statusBarItem.prominentBackground\": \"#acb6c033\",\n    \"statusBarItem.remoteBackground\": \"#e7ecf0\",\n    \"statusBarItem.remoteForeground\": \"#0e1116\",\n    \"symbolIcon.arrayForeground\": \"#702c00\",\n    \"symbolIcon.booleanForeground\": \"#023b95\",\n    \"symbolIcon.classForeground\": \"#702c00\",\n    \"symbolIcon.colorForeground\": \"#032563\",\n    \"symbolIcon.constantForeground\": \"#024c1a\",\n    \"symbolIcon.constructorForeground\": \"#341763\",\n    \"symbolIcon.enumeratorForeground\": \"#702c00\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#023b95\",\n    \"symbolIcon.eventForeground\": \"#4b535d\",\n    \"symbolIcon.fieldForeground\": \"#702c00\",\n    \"symbolIcon.fileForeground\": \"#603700\",\n    \"symbolIcon.folderForeground\": \"#603700\",\n    \"symbolIcon.functionForeground\": \"#512598\",\n    \"symbolIcon.interfaceForeground\": \"#702c00\",\n    \"symbolIcon.keyForeground\": \"#023b95\",\n    \"symbolIcon.keywordForeground\": \"#86061d\",\n    \"symbolIcon.methodForeground\": \"#512598\",\n    \"symbolIcon.moduleForeground\": \"#86061d\",\n    \"symbolIcon.namespaceForeground\": \"#86061d\",\n    \"symbolIcon.nullForeground\": \"#023b95\",\n    \"symbolIcon.numberForeground\": \"#024c1a\",\n    \"symbolIcon.objectForeground\": \"#702c00\",\n    \"symbolIcon.operatorForeground\": \"#032563\",\n    \"symbolIcon.packageForeground\": \"#702c00\",\n    \"symbolIcon.propertyForeground\": \"#702c00\",\n    \"symbolIcon.referenceForeground\": \"#023b95\",\n    \"symbolIcon.snippetForeground\": \"#023b95\",\n    \"symbolIcon.stringForeground\": \"#032563\",\n    \"symbolIcon.structForeground\": \"#702c00\",\n    \"symbolIcon.textForeground\": \"#032563\",\n    \"symbolIcon.typeParameterForeground\": \"#032563\",\n    \"symbolIcon.unitForeground\": \"#023b95\",\n    \"symbolIcon.variableForeground\": \"#702c00\",\n    \"tab.activeBackground\": \"#ffffff\",\n    \"tab.activeBorder\": \"#ffffff\",\n    \"tab.activeBorderTop\": \"#ef5b48\",\n    \"tab.activeForeground\": \"#0e1116\",\n    \"tab.border\": \"#20252c\",\n    \"tab.hoverBackground\": \"#ffffff\",\n    \"tab.inactiveBackground\": \"#ffffff\",\n    \"tab.inactiveForeground\": \"#0e1116\",\n    \"tab.unfocusedActiveBorder\": \"#ffffff\",\n    \"tab.unfocusedActiveBorderTop\": \"#20252c\",\n    \"tab.unfocusedHoverBackground\": \"#e7ecf0\",\n    \"terminal.ansiBlack\": \"#0e1116\",\n    \"terminal.ansiBlue\": \"#0349b4\",\n    \"terminal.ansiBrightBlack\": \"#4b535d\",\n    \"terminal.ansiBrightBlue\": \"#1168e3\",\n    \"terminal.ansiBrightCyan\": \"#3192aa\",\n    \"terminal.ansiBrightGreen\": \"#055d20\",\n    \"terminal.ansiBrightMagenta\": \"#844ae7\",\n    \"terminal.ansiBrightRed\": \"#86061d\",\n    \"terminal.ansiBrightWhite\": \"#88929d\",\n    \"terminal.ansiBrightYellow\": \"#4e2c00\",\n    \"terminal.ansiCyan\": \"#1b7c83\",\n    \"terminal.ansiGreen\": \"#024c1a\",\n    \"terminal.ansiMagenta\": \"#622cbc\",\n    \"terminal.ansiRed\": \"#a0111f\",\n    \"terminal.ansiWhite\": \"#66707b\",\n    \"terminal.ansiYellow\": \"#3f2200\",\n    \"terminal.foreground\": \"#0e1116\",\n    \"textBlockQuote.background\": \"#ffffff\",\n    \"textBlockQuote.border\": \"#20252c\",\n    \"textCodeBlock.background\": \"#acb6c033\",\n    \"textLink.activeForeground\": \"#0349b4\",\n    \"textLink.foreground\": \"#0349b4\",\n    \"textPreformat.foreground\": \"#0e1116\",\n    \"textSeparator.foreground\": \"#88929d\",\n    \"titleBar.activeBackground\": \"#ffffff\",\n    \"titleBar.activeForeground\": \"#0e1116\",\n    \"titleBar.border\": \"#20252c\",\n    \"titleBar.inactiveBackground\": \"#ffffff\",\n    \"titleBar.inactiveForeground\": \"#0e1116\",\n    \"tree.indentGuidesStroke\": \"#88929d\",\n    \"welcomePage.buttonBackground\": \"#e7ecf0\",\n    \"welcomePage.buttonHoverBackground\": \"#ced5dc\"\n  },\n  \"displayName\": \"GitHub Light High Contrast\",\n  \"name\": \"github-light-high-contrast\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#66707b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a0111f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.other.constant\",\n        \"variable.other.enummember\",\n        \"variable.language\",\n        \"entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"meta.export.default\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#702c00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function\",\n        \"meta.jsx.children\",\n        \"meta.block\",\n        \"meta.tag.attributes\",\n        \"entity.name.constant\",\n        \"meta.object.member\",\n        \"meta.embedded.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0e1116\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#622cbc\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#024c1a\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#a0111f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a0111f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0e1116\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#032563\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#702c00\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#0e1116\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#a0111f\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#032563\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#032563\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#024c1a\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#024c1a\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#702c00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#024c1a\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#0e1116\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#0e1116\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strikethrough\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#fff0ee\",\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a0111f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#d2fedb\",\n        \"foreground\": \"#024c1a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#ffc67b\",\n        \"foreground\": \"#702c00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#023b95\",\n        \"foreground\": \"#e7ecf0\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#622cbc\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#023b95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4b535d\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#6e011a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#032563\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { githubLightHighContrast as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,0BAA0B,OAAO,MAAM,CAAC;IAC1C,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,kCAAkC;QAClC,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,sCAAsC;QACtC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}