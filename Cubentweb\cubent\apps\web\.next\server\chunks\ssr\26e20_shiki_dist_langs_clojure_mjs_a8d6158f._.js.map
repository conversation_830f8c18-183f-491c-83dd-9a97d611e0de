{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/clojure.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Clojure\", \"name\": \"clojure\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#shebang-comment\" }, { \"include\": \"#quoted-sexp\" }, { \"include\": \"#sexp\" }, { \"include\": \"#keyfn\" }, { \"include\": \"#string\" }, { \"include\": \"#vector\" }, { \"include\": \"#set\" }, { \"include\": \"#map\" }, { \"include\": \"#regexp\" }, { \"include\": \"#var\" }, { \"include\": \"#constants\" }, { \"include\": \"#dynamic-variables\" }, { \"include\": \"#metadata\" }, { \"include\": \"#namespace-symbol\" }, { \"include\": \"#symbol\" }], \"repository\": { \"comment\": { \"begin\": \"(?<!\\\\\\\\);\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.clojure\" } }, \"end\": \"$\", \"name\": \"comment.line.semicolon.clojure\" }, \"constants\": { \"patterns\": [{ \"match\": \"(nil)(?=(\\\\s|\\\\)|\\\\]|\\\\}))\", \"name\": \"constant.language.nil.clojure\" }, { \"match\": \"(true|false)\", \"name\": \"constant.language.boolean.clojure\" }, { \"match\": \"(##(?:Inf|-Inf|NaN))\", \"name\": \"constant.numeric.symbol.clojure\" }, { \"match\": \"([-+]?\\\\d+/\\\\d+)\", \"name\": \"constant.numeric.ratio.clojure\" }, { \"match\": \"([-+]?(?:(?:3[0-6])|(?:[12]\\\\d)|[2-9])[rR][0-9A-Za-z]+N?)\", \"name\": \"constant.numeric.arbitrary-radix.clojure\" }, { \"match\": \"([-+]?0[xX][0-9a-fA-F]+N?)\", \"name\": \"constant.numeric.hexadecimal.clojure\" }, { \"match\": \"([-+]?0[0-7]+N?)\", \"name\": \"constant.numeric.octal.clojure\" }, { \"match\": \"([-+]?\\\\d+(?:(\\\\.|(?=[eEM]))\\\\d*([eE][-+]?\\\\d+)?)M?)\", \"name\": \"constant.numeric.double.clojure\" }, { \"match\": \"([-+]?\\\\d+N?)\", \"name\": \"constant.numeric.long.clojure\" }, { \"include\": \"#keyword\" }] }, \"dynamic-variables\": { \"match\": \"\\\\*[\\\\w\\\\.\\\\-_:+=><!?\\\\d]+\\\\*\", \"name\": \"meta.symbol.dynamic.clojure\" }, \"keyfn\": { \"patterns\": [{ \"match\": \"(?<=(\\\\s|\\\\(|\\\\[|\\\\{))(if(-[-\\\\p{Ll}?]*)?|when(-[-\\\\p{Ll}]*)?|for(-[-\\\\p{Ll}]*)?|cond|do|let(-[-\\\\p{Ll}?]*)?|binding|loop|recur|fn|throw[\\\\p{Ll}\\\\-]*|try|catch|finally|([\\\\p{Ll}]*case))(?=(\\\\s|\\\\)|\\\\]|\\\\}))\", \"name\": \"storage.control.clojure\" }, { \"match\": \"(?<=(\\\\s|\\\\(|\\\\[|\\\\{))(declare-?|(in-)?ns|import|use|require|load|compile|(def[\\\\p{Ll}\\\\-]*))(?=(\\\\s|\\\\)|\\\\]|\\\\}))\", \"name\": \"keyword.control.clojure\" }] }, \"keyword\": { \"match\": \"(?<=(\\\\s|\\\\(|\\\\[|\\\\{)):[\\\\w#\\\\.\\\\-_:+=><\\\\/!?\\\\*]+(?=(\\\\s|\\\\)|\\\\]|\\\\}|\\\\,))\", \"name\": \"constant.keyword.clojure\" }, \"map\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.map.begin.clojure\" } }, \"end\": \"(\\\\}(?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.map.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.map.end.clojure\" } }, \"name\": \"meta.map.clojure\", \"patterns\": [{ \"include\": \"$self\" }] }, \"metadata\": { \"patterns\": [{ \"begin\": \"(\\\\^\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.metadata.map.begin.clojure\" } }, \"end\": \"(\\\\}(?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.metadata.map.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.metadata.map.end.clojure\" } }, \"name\": \"meta.metadata.map.clojure\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"(\\\\^)\", \"end\": \"(\\\\s)\", \"name\": \"meta.metadata.simple.clojure\", \"patterns\": [{ \"include\": \"#keyword\" }, { \"include\": \"$self\" }] }] }, \"namespace-symbol\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"meta.symbol.namespace.clojure\" } }, \"match\": \"([\\\\p{L}\\\\.\\\\-_+=><!?\\\\*][\\\\w\\\\.\\\\-_:+=><!?\\\\*\\\\d]*)/\" }] }, \"quoted-sexp\": { \"begin\": \"(['``]\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.clojure\" } }, \"end\": \"(\\\\))$|(\\\\)(?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.expression.end.trailing.clojure\" }, \"3\": { \"name\": \"punctuation.section.expression.end.clojure\" } }, \"name\": \"meta.quoted-expression.clojure\", \"patterns\": [{ \"include\": \"$self\" }] }, \"regexp\": { \"begin\": '#\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.regexp.begin.clojure\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regexp.end.clojure\" } }, \"name\": \"string.regexp.clojure\", \"patterns\": [{ \"include\": \"#regexp_escaped_char\" }] }, \"regexp_escaped_char\": { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.clojure\" }, \"set\": { \"begin\": \"(\\\\#\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.set.begin.clojure\" } }, \"end\": \"(\\\\}(?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.set.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.set.end.clojure\" } }, \"name\": \"meta.set.clojure\", \"patterns\": [{ \"include\": \"$self\" }] }, \"sexp\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.clojure\" } }, \"end\": \"(\\\\))$|(\\\\)(?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.expression.end.trailing.clojure\" }, \"3\": { \"name\": \"punctuation.section.expression.end.clojure\" } }, \"name\": \"meta.expression.clojure\", \"patterns\": [{ \"begin\": \"(?<=\\\\()(ns|declare|def[\\\\w\\\\d._:+=><!?*-]*|[\\\\w._:+=><!?*-][\\\\w\\\\d._:+=><!?*-]*/def[\\\\w\\\\d._:+=><!?*-]*)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.clojure\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.definition.global.clojure\", \"patterns\": [{ \"include\": \"#metadata\" }, { \"include\": \"#dynamic-variables\" }, { \"match\": \"([\\\\p{L}\\\\.\\\\-_+=><!?\\\\*][\\\\w\\\\.\\\\-_:+=><!?\\\\*\\\\d]*)\", \"name\": \"entity.global.clojure\" }, { \"include\": \"$self\" }] }, { \"include\": \"#keyfn\" }, { \"include\": \"#constants\" }, { \"include\": \"#vector\" }, { \"include\": \"#map\" }, { \"include\": \"#set\" }, { \"include\": \"#sexp\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.clojure\" } }, \"match\": \"(?<=\\\\()(.+?)(?=\\\\s|\\\\))\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"include\": \"$self\" }] }, \"shebang-comment\": { \"begin\": \"^(#!)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.shebang.clojure\" } }, \"end\": \"$\", \"name\": \"comment.line.shebang.clojure\" }, \"string\": { \"begin\": '(?<!\\\\\\\\)(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.clojure\" } }, \"end\": '(\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.clojure\" } }, \"name\": \"string.quoted.double.clojure\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.clojure\" }] }, \"symbol\": { \"patterns\": [{ \"match\": \"([\\\\p{L}\\\\.\\\\-_+=><!?\\\\*][\\\\w\\\\.\\\\-_:+=><!?\\\\*\\\\d]*)\", \"name\": \"meta.symbol.clojure\" }] }, \"var\": { \"match\": \"(?<=(\\\\s|\\\\(|\\\\[|\\\\{)\\\\#)'[\\\\w\\\\.\\\\-_:+=><\\\\/!?\\\\*]+(?=(\\\\s|\\\\)|\\\\]|\\\\}))\", \"name\": \"meta.var.clojure\" }, \"vector\": { \"begin\": \"(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.vector.begin.clojure\" } }, \"end\": \"(\\\\](?=[}\\\\])\\\\s]*(?:;|$)))|(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.vector.end.trailing.clojure\" }, \"2\": { \"name\": \"punctuation.section.vector.end.clojure\" } }, \"name\": \"meta.vector.clojure\", \"patterns\": [{ \"include\": \"$self\" }] } }, \"scopeName\": \"source.clojure\", \"aliases\": [\"clj\"] });\nvar clojure = [\n  lang\n];\n\nexport { clojure as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAO;QAAG;YAAE,WAAW;QAAO;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAO;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAU;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAK,QAAQ;QAAiC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAwD,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAiC,QAAQ;QAA8B;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkN,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAsH,QAAQ;gBAA0B;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAA+E,QAAQ;QAA2B;QAAG,OAAO;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAqC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,OAAO;oBAAS,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAwD;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAA4C,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAS,QAAQ;QAAoC;QAAG,OAAO;YAAE,SAAS;YAAY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAqC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAA4C,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,SAAS;oBAAiH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,SAAS;4BAAwD,QAAQ;wBAAwB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,QAAQ;QAA+B;QAAG,UAAU;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAoC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwD,QAAQ;gBAAsB;aAAE;QAAC;QAAG,OAAO;YAAE,SAAS;YAA6E,QAAQ;QAAmB;QAAG,UAAU;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAqC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;IAAE;IAAG,aAAa;IAAkB,WAAW;QAAC;KAAM;AAAC;AAC11N,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}