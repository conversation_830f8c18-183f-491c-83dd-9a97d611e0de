"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2503],{11227:(e,t,r)=>{r.d(t,{D:()=>g,M2:()=>v,MR:()=>m});var i={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},n=new Set(["first_factor","second_factor","multi_factor"]),o=new Set(["strict_mfa","strict","moderate","lax"]),s=e=>"number"==typeof e&&e>0,l=e=>n.has(e),a=e=>o.has(e),u=e=>e.startsWith("org:")?e:`org:${e}`,d=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:n}=t;return(e.role||e.permission)&&r&&i&&n?e.permission?n.includes(u(e.permission)):e.role?i===u(e.role):null:null},c=(e,t)=>{let{org:r,user:i}=p(e),[n,o]=t.split(":"),s=o||n;return"org"===n?r.includes(s):"user"===n?i.includes(s):[...r,...i].includes(s)},h=(e,t)=>{let{features:r,plans:i}=t;return e.feature&&r?c(r,e.feature):e.plan&&i?c(i,e.plan):null},p=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},g=e=>{if(!e)return!1;let t="string"==typeof e&&a(e),r="object"==typeof e&&l(e.level)&&s(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?i[e]:e).bind(null,e)},f=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=g(e.reverification);if(!r)return null;let{level:i,afterMinutes:n}=r(),[o,s]=t,l=-1!==o?n>o:null,a=-1!==s?n>s:null;switch(i){case"first_factor":return l;case"second_factor":return -1!==s?a:l;case"multi_factor":return -1===s?l:l&&a}},m=e=>t=>{if(!e.userId)return!1;let r=h(t,e),i=d(t,e),n=f(t,e);return[r||i,n].some(e=>null===e)?[r||i,n].some(e=>!0===e):[r||i,n].every(e=>!0===e)},v=({authObject:{sessionId:e,sessionStatus:t,userId:r,actor:i,orgId:n,orgRole:o,orgSlug:s,signOut:l,getToken:a,has:u,sessionClaims:d},options:{treatPendingAsSignedOut:c=!0}})=>void 0===e&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:l,getToken:a}:null===e&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:c&&"pending"===t?{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:e&&d&&r&&n&&o?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:i||null,orgId:n,orgRole:o,orgSlug:s||null,has:u,signOut:l,getToken:a}:e&&d&&r&&!n?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:i||null,orgId:null,orgRole:null,orgSlug:null,has:u,signOut:l,getToken:a}:void 0},12503:(e,t,r)=>{r.d(t,{B$:()=>c.B$,wF:()=>c.wF,lT:()=>c.lT,z0:()=>c.z0,A0:()=>c.A0,lJ:()=>eL,ul:()=>ek,PQ:()=>eS,oE:()=>e_,nC:()=>ev,NC:()=>ey,nm:()=>ew,EH:()=>c.EH,rm:()=>c.rm,m2:()=>c.m2,W5:()=>c.W5,mO:()=>c.mO,eG:()=>c.eG,Ls:()=>eo,hZ:()=>eC,M_:()=>eE,ct:()=>eO,Hx:()=>es,Ny:()=>ej,iB:()=>c.iB,Bl:()=>c.Bl,uF:()=>eg,Fv:()=>eu,cP:()=>eP,As:()=>c.As,ho:()=>c.ho,ui:()=>c.ui,Z5:()=>c.Z5,D_:()=>c.D_,Wp:()=>c.Wp,wV:()=>c.dy,g7:()=>c.g7,go:()=>c.go,yC:()=>c.yC,Jd:()=>c.Jd});var i,n,o,s,l,a,u,d,c=r(39625),h=e=>{throw TypeError(e)},p=(e,t,r)=>t.has(e)||h("Cannot "+r),g=(e,t,r)=>(p(e,t,"read from private field"),r?r.call(e):t.get(e)),f=(e,t,r)=>t.has(e)?h("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),m=(e,t,r,i)=>(p(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),v=(e,t,r)=>(p(e,t,"access private method"),r),k=r(76578),b=r(66598),y=r(50628),_=r(6341),S=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r};r(22651);var P=r(50287),w=r(63848),C=(e,t,r)=>!e&&r?j(r):O(t),j=e=>{let t=e.userId,r=e.user,i=e.sessionId,n=e.sessionStatus,o=e.sessionClaims,s=e.session,l=e.organization,a=e.orgId,u=e.orgRole,d=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:i,session:s,sessionStatus:n,sessionClaims:o,organization:l,orgId:a,orgRole:u,orgPermissions:d,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},O=e=>{let t=e.user?e.user.id:e.user,r=e.user,i=e.session?e.session.id:e.session,n=e.session,o=e.session?.status,s=e.session?e.session.lastActiveToken?.jwt?.claims:null,l=e.session?e.session.factorVerificationAge:null,a=n?.actor,u=e.organization,d=e.organization?e.organization.id:e.organization,c=u?.slug,h=u?r?.organizationMemberships?.find(e=>e.organization.id===d):u,p=h?h.permissions:h;return{userId:t,user:r,sessionId:i,session:n,sessionStatus:o,sessionClaims:s,organization:u,orgId:d,orgRole:h?h.role:h,orgSlug:c,orgPermissions:p,actor:a,factorVerificationAge:l}},E=r(17267),U=(e,t,r,i,n)=>{let{notify:o}=n||{},s=e.get(r);s||(s=[],e.set(r,s)),s.push(i),o&&t.has(r)&&i(t.get(r))},M=(e,t,r)=>(e.get(t)||[]).map(e=>e(r)),z=(e,t,r)=>{let i=e.get(t);i&&(r?i.splice(i.indexOf(r)>>>0,1):e.set(t,[]))},I=()=>{let e=new Map,t=new Map,r=new Map;return{on:(...r)=>U(e,t,...r),prioritizedOn:(...e)=>U(r,t,...e),emit:(i,n)=>{t.set(i,n),M(r,i,n),M(e,i,n)},off:(...t)=>z(e,...t),prioritizedOff:(...e)=>z(r,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},L={Status:"status"},W=()=>I();"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var R=e=>t=>{try{return y.Children.only(e)}catch{return c.sb.throw((0,c.Wq)(t))}},T=(e,t)=>(e||(e=t),"string"==typeof e&&(e=y.createElement("button",null,e)),e),A=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},D=new Map,x=e=>{let t=Array(e.length).fill(null),[r,i]=(0,y.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>i(r=>r.map((r,i)=>i===t?e:r)),unmount:()=>i(e=>e.map((e,r)=>r===t?null:e)),portal:()=>y.createElement(y.Fragment,null,r[t]?(0,_.createPortal)(e.component,r[t]):null)}))},N=(e,t)=>!!e&&y.isValidElement(e)&&(null==e?void 0:e.type)===t,F=(e,t)=>$({children:e,reorderItemsLabels:["account","security"],LinkComponent:ea,PageComponent:el,MenuItemsComponent:ec,componentName:"UserProfile"},t),B=(e,t)=>$({children:e,reorderItemsLabels:["general","members"],LinkComponent:em,PageComponent:ef,componentName:"OrganizationProfile"},t),V=e=>{let t=[],r=[em,ef,ec,el,ea];return y.Children.forEach(e,e=>{r.some(t=>N(e,t))||t.push(e)}),t},$=(e,t)=>{let{children:r,LinkComponent:i,PageComponent:n,MenuItemsComponent:o,reorderItemsLabels:s,componentName:l}=e,{allowForAnyChildren:a=!1}=t||{},u=[];y.Children.forEach(r,e=>{if(!N(e,n)&&!N(e,i)&&!N(e,o)){e&&!a&&(0,b.s2)((0,c.n)(l));return}let{props:t}=e,{children:r,label:d,url:h,labelIcon:p}=t;if(N(e,n))if(K(t,s))u.push({label:d});else{if(!J(t))return void(0,b.s2)((0,c.sR)(l));u.push({label:d,labelIcon:p,children:r,url:h})}if(N(e,i))if(!q(t))return void(0,b.s2)((0,c.D)(l));else u.push({label:d,labelIcon:p,url:h})});let d=[],h=[],p=[];u.forEach((e,t)=>{if(J(e)){d.push({component:e.children,id:t}),h.push({component:e.labelIcon,id:t});return}q(e)&&p.push({component:e.labelIcon,id:t})});let g=x(d),f=x(h),m=x(p),v=[],k=[];return u.forEach((e,t)=>{if(K(e,s))return void v.push({label:e.label});if(J(e)){let{portal:r,mount:i,unmount:n}=g.find(e=>e.id===t),{portal:o,mount:s,unmount:l}=f.find(e=>e.id===t);v.push({label:e.label,url:e.url,mount:i,unmount:n,mountIcon:s,unmountIcon:l}),k.push(r),k.push(o);return}if(q(e)){let{portal:r,mount:i,unmount:n}=m.find(e=>e.id===t);v.push({label:e.label,url:e.url,mountIcon:i,unmountIcon:n}),k.push(r);return}}),{customPages:v,customPagesPortals:k}},K=(e,t)=>{let{children:r,label:i,url:n,labelIcon:o}=e;return!r&&!n&&!o&&t.some(e=>e===i)},J=e=>{let{children:t,label:r,url:i,labelIcon:n}=e;return!!t&&!!i&&!!n&&!!r},q=e=>{let{children:t,label:r,url:i,labelIcon:n}=e;return!t&&!!i&&!!n&&!!r},G=e=>Q({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:ec,MenuActionComponent:eh,MenuLinkComponent:ep,UserProfileLinkComponent:ea,UserProfilePageComponent:el}),Q=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:i,UserProfileLinkComponent:n,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let l=[],a=[],u=[];y.Children.forEach(e,e=>{if(!N(e,t)&&!N(e,n)&&!N(e,o)){e&&(0,b.s2)(c.P6);return}if(N(e,n)||N(e,o))return;let{props:a}=e;y.Children.forEach(a.children,e=>{if(!N(e,r)&&!N(e,i)){e&&(0,b.s2)(c.wm);return}let{props:t}=e,{label:n,labelIcon:o,href:a,onClick:u,open:d}=t;if(N(e,r))if(Z(t,s))l.push({label:n});else{if(!H(t))return void(0,b.s2)(c.Wv);let e={label:n,labelIcon:o};if(void 0!==u)l.push({...e,onClick:u});else{if(void 0===d)return void(0,b.s2)("Custom menu item must have either onClick or open property");l.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(N(e,i))if(!Y(t))return void(0,b.s2)(c.ld);else l.push({label:n,labelIcon:o,href:a})})});let d=[],h=[];l.forEach((e,t)=>{H(e)&&d.push({component:e.labelIcon,id:t}),Y(e)&&h.push({component:e.labelIcon,id:t})});let p=x(d),g=x(h);return l.forEach((e,t)=>{if(Z(e,s)&&a.push({label:e.label}),H(e)){let{portal:r,mount:i,unmount:n}=p.find(e=>e.id===t),o={label:e.label,mountIcon:i,unmountIcon:n};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),a.push(o),u.push(r)}if(Y(e)){let{portal:r,mount:i,unmount:n}=g.find(e=>e.id===t);a.push({label:e.label,href:e.href,mountIcon:i,unmountIcon:n}),u.push(r)}}),{customMenuItems:a,customMenuItemsPortals:u}},Z=(e,t)=>{let{children:r,label:i,onClick:n,labelIcon:o}=e;return!r&&!n&&!o&&t.some(e=>e===i)},H=e=>{let{label:t,labelIcon:r,onClick:i,open:n}=e;return!!r&&!!t&&("function"==typeof i||"string"==typeof n)},Y=e=>{let{label:t,href:r,labelIcon:i}=e;return!!r&&!!i&&!!t};function X(e){let t=(0,y.useRef)(),[r,i]=(0,y.useState)("rendering");return(0,y.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:i=0}=e;return new Promise((e,n)=>{if(!t)return void n(Error("No root element provided"));let o=t;if(r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)return void e();let s=new MutationObserver(i=>{for(let n of i)if("childList"===n.type&&(!o&&r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),i>0&&setTimeout(()=>{s.disconnect(),n(Error("Timeout waiting for element children"))},i)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{i("rendered")}).catch(()=>{i("error")}))},[e]),r}var ee=e=>"mount"in e,et=e=>"open"in e,er=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),ei=class extends y.PureComponent{constructor(){super(...arguments),this.rootRef=y.createRef()}componentDidUpdate(e){var t,r,i,n;if(!ee(e)||!ee(this.props))return;let o=S(e.props,"customPages","customMenuItems","children"),s=S(this.props.props,"customPages","customMenuItems","children"),l=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),a=(null==(i=o.customMenuItems)?void 0:i.length)!==(null==(n=s.customMenuItems)?void 0:n.length),u=er(e.props.customMenuItems),d=er(this.props.props.customMenuItems);(!(0,P.MZ)(o,s)||!(0,P.MZ)(u,d)||l||a)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(ee(this.props)&&this.props.mount(this.rootRef.current,this.props.props),et(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(ee(this.props)&&this.props.unmount(this.rootRef.current),et(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return y.createElement(y.Fragment,null,!e&&y.createElement("div",{...t}),this.props.children)}},en=e=>{var t,r;return y.createElement(y.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,y.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,y.createElement)(e,{key:t})))},eo=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),es=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0});function el({children:e}){return(0,b.s2)(c.$n),y.createElement(y.Fragment,null,e)}function ea({children:e}){return(0,b.s2)(c._I),y.createElement(y.Fragment,null,e)}var eu=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=F(i.children);return y.createElement(y.Fragment,null,n&&r,y.createElement(ei,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...i,customPages:s},rootProps:o},y.createElement(en,{customPagesPortals:l})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:el,Link:ea}),ed=(0,y.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function ec({children:e}){return(0,b.s2)(c.UX),y.createElement(y.Fragment,null,e)}function eh({children:e}){return(0,b.s2)(c.aU),y.createElement(y.Fragment,null,e)}function ep({children:e}){return(0,b.s2)(c.Uw),y.createElement(y.Fragment,null,e)}var eg=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=F(i.children,{allowForAnyChildren:!!i.__experimental_asProvider}),a=Object.assign(i.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:d}=G(i.children),c=V(i.children),h={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...i,userProfileProps:a,customMenuItems:u}};return y.createElement(ed.Provider,{value:h},n&&r,e.loaded&&y.createElement(ei,{component:t,...h,hideRootHtmlElement:!!i.__experimental_asProvider,rootProps:o},i.__experimental_asProvider?c:null,y.createElement(en,{customPagesPortals:l,customMenuItemsPortals:d})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:el,UserProfileLink:ea,MenuItems:ec,Action:eh,Link:ep,__experimental_Outlet:function(e){let t=(0,y.useContext)(ed),r={...t,props:{...t.props,...e}};return y.createElement(ei,{...r})}});function ef({children:e}){return(0,b.s2)(c.vb),y.createElement(y.Fragment,null,e)}function em({children:e}){return(0,b.s2)(c.kf),y.createElement(y.Fragment,null,e)}var ev=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=B(i.children);return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...i,customPages:s},rootProps:o},y.createElement(en,{customPagesPortals:l})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:ef,Link:em}),ek=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0}),eb=(0,y.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),ey=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=B(i.children,{allowForAnyChildren:!!i.__experimental_asProvider}),a=Object.assign(i.organizationProfileProps||{},{customPages:s}),u=V(i.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...i,organizationProfileProps:a},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),y.createElement(eb.Provider,{value:d},y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{...d,hideRootHtmlElement:!!i.__experimental_asProvider},i.__experimental_asProvider?u:null,y.createElement(en,{customPagesPortals:l}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:ef,OrganizationProfileLink:em,__experimental_Outlet:function(e){let t=(0,y.useContext)(eb),r={...t,props:{...t.props,...e}};return y.createElement(ei,{...r})}}),e_=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0}),eS=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0}),eP=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0}),ew=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===X(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return y.createElement(y.Fragment,null,n&&r,e.loaded&&y.createElement(ei,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"PricingTable",renderWhileLoading:!0}),eC=(0,c.Q)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:i,forceRedirectUrl:n,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:l,initialValues:a,withSignUp:u,oauthFlow:d,...c}=r,h=R(t=T(t,"Sign in"))("SignInButton"),p=()=>{let t={forceRedirectUrl:n,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:i,signUpForceRedirectUrl:s,initialValues:a,withSignUp:u,oauthFlow:d};return"modal"===l?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:n})},g=async e=>(h&&"object"==typeof h&&"props"in h&&await A(h.props.onClick)(e),p()),f={...c,onClick:g};return y.cloneElement(h,f)},{component:"SignInButton",renderWhileLoading:!0}),ej=(0,c.Q)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:i,forceRedirectUrl:n,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:l,unsafeMetadata:a,initialValues:u,oauthFlow:d,...c}=r,h=R(t=T(t,"Sign up"))("SignUpButton"),p=()=>{let t={fallbackRedirectUrl:i,forceRedirectUrl:n,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,unsafeMetadata:a,initialValues:u,oauthFlow:d};return"modal"===l?e.openSignUp({...t,appearance:r.appearance}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:i,signUpForceRedirectUrl:n})},g=async e=>(h&&"object"==typeof h&&"props"in h&&await A(h.props.onClick)(e),p()),f={...c,onClick:g};return y.cloneElement(h,f)},{component:"SignUpButton",renderWhileLoading:!0}),eO=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:i="/",signOutOptions:n,...o}=r,s=R(t=T(t,"Sign out"))("SignOutButton"),l=()=>e.signOut({redirectUrl:i,...n}),a=async e=>(await A(s.props.onClick)(e),l()),u={...o,onClick:a};return y.cloneElement(s,u)},{component:"SignOutButton",renderWhileLoading:!0}),eE=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:i,...n}=r,o=R(t=T(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:i||void 0})}()},l=async e=>(await A(o.props.onClick)(e),s()),a={...n,onClick:l};return y.cloneElement(o,a)},{component:"SignInWithMetamask",renderWhileLoading:!0});void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var eU={name:"@clerk/clerk-react",version:"5.31.6",environment:"production"},eM=class e{constructor(e){f(this,u),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],f(this,i,"loading"),f(this,n),f(this,o),f(this,s),f(this,l,W()),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.on=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.on)return this.clerkjs.on(...e);g(this,l).on(...e)},this.off=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.off)return this.clerkjs.off(...e);g(this,l).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=e=>{var t;if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null==(t=g(this,l).internal.retrieveListeners("status"))||t.forEach(e=>{this.on("status",e,{notify:!0})}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenCheckout&&e.__internal_openCheckout(this.preopenCheckout),null!==this.preopenPlanDetails&&e.__internal_openPlanDetails(this.preopenPlanDetails),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.mountPricingTable(r,t)}),void 0===this.clerkjs.status&&g(this,l).emit(L.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await v(this,u,d).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__experimental_navigateToTask=async e=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&this.loaded?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let i=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?null==(r=i())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",i)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await v(this,u,d).call(this)).authenticateWithGoogleOneTap(e),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};m(this,s,r),m(this,o,null==e?void 0:e.proxyUrl),m(this,n,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=(0,E.M)()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=eU),g(this,l).emit(L.Status,"loading"),g(this,l).prioritizedOn(L.Status,e=>m(this,i,e)),g(this,s)&&this.loadClerkJS()}get publishableKey(){return g(this,s)}get loaded(){var e;return(null==(e=this.clerkjs)?void 0:e.loaded)||!1}get status(){var e;return this.clerkjs?(null==(e=this.clerkjs)?void 0:e.status)||(this.clerkjs.loaded?"ready":"loading"):g(this,i)}static getOrCreateInstance(t){return(0,E.M)()&&g(this,a)&&(!t.Clerk||g(this,a).Clerk===t.Clerk)&&g(this,a).publishableKey===t.publishableKey||m(this,a,new e(t)),g(this,a)}static clearInstance(){m(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?(0,b.VK)(g(this,n),new URL(window.location.href),""):"function"==typeof g(this,n)?c.sb.throw(c.Vo):g(this,n)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?(0,b.VK)(g(this,o),new URL(window.location.href),""):"function"==typeof g(this,o)?c.sb.throw(c.Vo):g(this,o)||""}__internal_getOption(e){var t,r;return(null==(t=this.clerkjs)?void 0:t.__internal_getOption)?null==(r=this.clerkjs)?void 0:r.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?(0,b.VK)(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&c.sb.throw(c.Vo)}async loadClerkJS(){var e,t;if("browser"===this.mode&&!this.loaded){"undefined"!=typeof window&&(window.__clerk_publishable_key=g(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(g(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk).loaded||(this.beforeLoad(e),await e.load(this.options)),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await (0,k._R)({...this.options,publishableKey:g(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){g(this,l).emit(L.Status,"error"),console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get billing(){var e;return null==(e=this.clerkjs)?void 0:e.billing}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function ez(e){let{isomorphicClerkOptions:t,initialState:r,children:i}=e,{isomorphicClerk:n,clerkStatus:o}=eI(t),[s,l]=y.useState({client:n.client,session:n.session,user:n.user,organization:n.organization});y.useEffect(()=>n.addListener(e=>l({...e})),[]);let a=C(n.loaded,s,r),u=y.useMemo(()=>({value:n}),[o]),d=y.useMemo(()=>({value:s.client}),[s.client]),{sessionId:h,sessionStatus:p,sessionClaims:g,session:f,userId:m,user:v,orgId:k,actor:b,organization:_,orgRole:S,orgSlug:w,orgPermissions:j,factorVerificationAge:O}=a,E=y.useMemo(()=>({value:{sessionId:h,sessionStatus:p,sessionClaims:g,userId:m,actor:b,orgId:k,orgRole:S,orgSlug:w,orgPermissions:j,factorVerificationAge:O}}),[h,p,m,b,k,S,w,O,null==g?void 0:g.__raw]),U=y.useMemo(()=>({value:f}),[h,f]),M=y.useMemo(()=>({value:v}),[m,v]),z=y.useMemo(()=>({value:{organization:_}}),[k,_]);return y.createElement(c.SW.Provider,{value:u},y.createElement(P.pc.Provider,{value:d},y.createElement(P.IC.Provider,{value:U},y.createElement(P.TS,{...z.value},y.createElement(c.cy.Provider,{value:E},y.createElement(P.Rs.Provider,{value:M},i))))))}i=new WeakMap,n=new WeakMap,o=new WeakMap,s=new WeakMap,l=new WeakMap,a=new WeakMap,u=new WeakSet,d=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},f(eM,a);var eI=e=>{let t=y.useMemo(()=>eM.getOrCreateInstance(e),[]),[r,i]=y.useState(t.status);return y.useEffect(()=>{t.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),y.useEffect(()=>{t.__unstable__updateProps({options:e})},[e.localization]),y.useEffect(()=>(t.on("status",i),()=>t.off("status",i)),[t]),y.useEffect(()=>()=>{eM.clearInstance()},[]),{isomorphicClerk:t,clerkStatus:r}},eL=function(e,t,r){let i=e.displayName||e.name||t||"Component",n=i=>(!function(e,t,r=1){y.useEffect(()=>{let i=D.get(e)||0;return i==r?c.sb.throw(t):(D.set(e,i+1),()=>{D.set(e,(D.get(e)||1)-1)})},[])}(t,r),y.createElement(e,{...i}));return n.displayName=`withMaxAllowedInstancesGuard(${i})`,n}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:i,...n}=e,{publishableKey:o="",Clerk:s}=n;return s||i||(o?o&&!(0,w.rA)(o)&&c.sb.throwInvalidPublishableKeyError({key:o}):c.sb.throwMissingPublishableKeyError()),y.createElement(ez,{initialState:t,isomorphicClerkOptions:n},r)},"ClerkProvider",c.yN);eL.displayName="ClerkProvider",(0,c.wV)({packageName:"@clerk/clerk-react"}),(0,k.kX)("@clerk/clerk-react")},13319:(e,t,r)=>{r.d(t,{_r:()=>i._r});var i=r(60376);r(22651)},17267:(e,t,r)=>{function i(){return"undefined"!=typeof window}r.d(t,{M:()=>i}),RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i"),r(22651)},22651:(e,t,r)=>{r.d(t,{OV:()=>p,S7:()=>h,VA:()=>a,ie:()=>d,jq:()=>g});var i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,l=e=>{throw TypeError(e)},a=(e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,l)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of o(t))s.call(e,a)||a===r||i(e,a,{get:()=>t[a],enumerable:!(l=n(t,a))||l.enumerable});return e},d=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),c=(e,t,r)=>t.has(e)||l("Cannot "+r),h=(e,t,r)=>(c(e,t,"read from private field"),r?r.call(e):t.get(e)),p=(e,t,r,i)=>(c(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),g=(e,t,r)=>(c(e,t,"access private method"),r)},24466:(e,t,r)=>{r.d(t,{zz:()=>n});var i=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let n=e(r.toString());n!==r&&(i[n]=i[r],delete i[r]),"object"==typeof i[n]&&(i[n]=t(i[n]))}return i};return t};function n(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}i(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),i(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},39625:(e,t,r)=>{r.d(t,{cy:()=>p,B$:()=>en,wF:()=>H,lT:()=>Z,z0:()=>G,A0:()=>Q,SW:()=>f,EH:()=>Y,rm:()=>ei,m2:()=>er,W5:()=>X,mO:()=>ee,eG:()=>et,iB:()=>J,Bl:()=>q,D:()=>j,wm:()=>M,sR:()=>C,n:()=>w,sb:()=>c,s7:()=>E,Wq:()=>k,yN:()=>v,kd:()=>O,kf:()=>P,vb:()=>S,wV:()=>h,Vo:()=>b,As:()=>N,ho:()=>l.ho,hP:()=>F,ui:()=>B,Z5:()=>l.Z5,D_:()=>l.D_,Wp:()=>l.Wp,dy:()=>l.wV,g7:()=>l.g7,go:()=>V,yC:()=>$,Jd:()=>l.Jd,P6:()=>U,aU:()=>I,ld:()=>W,Wv:()=>R,UX:()=>z,Uw:()=>L,_I:()=>_,$n:()=>y,Q:()=>K});var i=r(13319),n=r(11227);r(22651);var o=r(46456),s=r(50628),l=r(50287),a=r(88167),u=new Set,d=(e,t,r)=>{let i=(0,a.MC)()||(0,a.Fj)(),n=r??e;u.has(n)||i||(u.add(n),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},c=(0,i._r)({packageName:"@clerk/clerk-react"});function h(e){c.setMessages(e).setPackageName(e)}var[p,g]=(0,l.e3)("AuthContext"),f=l.ED,m=l.hQ,v="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",k=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,b="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",y="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",_="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",S="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",P="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",w=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,C=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,j=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,O=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,E=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,U="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",M="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",z="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",I="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",L="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",W="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",R="Missing props. <UserButton.Action /> component requires the following props: label.",T=e=>{(0,l.Kz)(()=>{c.throwMissingClerkProviderError({source:e})})},A=e=>new Promise(t=>{let r=i=>{["ready","degraded"].includes(i)&&(t(),e.off("status",r))};e.on("status",r,{notify:!0})}),D=e=>async t=>(await A(e),e.session)?e.session.getToken(t):null,x=e=>async(...t)=>(await A(e),e.signOut(...t)),N=(e={})=>{var t,r;T("useAuth");let{treatPendingAsSignedOut:i,...n}=null!=e?e:{},l=g();void 0===l.sessionId&&void 0===l.userId&&(l=null!=n?n:{});let a=m(),u=(0,s.useCallback)(D(a),[a]),d=(0,s.useCallback)(x(a),[a]);return null==(t=a.telemetry)||t.record((0,o.FJ)("useAuth",{treatPendingAsSignedOut:i})),F({...l,getToken:u,signOut:d},{treatPendingAsSignedOut:null!=i?i:null==(r=a.__internal_getOption)?void 0:r.call(a,"treatPendingAsSignedOut")})};function F(e,{treatPendingAsSignedOut:t=!0}={}){let{userId:r,orgId:i,orgRole:o,has:l,signOut:a,getToken:u,orgPermissions:d,factorVerificationAge:h,sessionClaims:p}=null!=e?e:{},g=(0,s.useCallback)(e=>l?l(e):(0,n.MR)({userId:r,orgId:i,orgRole:o,orgPermissions:d,factorVerificationAge:h,features:(null==p?void 0:p.fea)||"",plans:(null==p?void 0:p.pla)||""})(e),[l,r,i,o,d,h]),f=(0,n.M2)({authObject:{...e,getToken:u,signOut:a,has:g},options:{treatPendingAsSignedOut:t}});return f||c.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")}function B(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=s.useMemo(()=>e.createEmailLinkFlow(),[e]);return s.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}var V=()=>{var e;T("useSignIn");let t=m(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignIn")),r)?{isLoaded:!0,signIn:r.signIn,setActive:t.setActive}:{isLoaded:!1,signIn:void 0,setActive:void 0}},$=()=>{var e;T("useSignUp");let t=m(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignUp")),r)?{isLoaded:!0,signUp:r.signUp,setActive:t.setActive}:{isLoaded:!1,signUp:void 0,setActive:void 0}},K=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let i="string"==typeof t?void 0:t,n=t=>{T(r||"withClerk");let n=m();return n.loaded||(null==i?void 0:i.renderWhileLoading)?s.createElement(e,{...t,component:r,clerk:n}):null};return n.displayName=`withClerk(${r})`,n},J=({children:e,treatPendingAsSignedOut:t})=>{T("SignedIn");let{userId:r}=N({treatPendingAsSignedOut:t});return r?e:null},q=({children:e,treatPendingAsSignedOut:t})=>{T("SignedOut");let{userId:r}=N({treatPendingAsSignedOut:t});return null===r?e:null},G=({children:e})=>(T("ClerkLoaded"),m().loaded)?e:null,Q=({children:e})=>(T("ClerkLoading"),"loading"!==m().status)?null:e,Z=({children:e})=>(T("ClerkFailed"),"error"!==m().status)?null:e,H=({children:e})=>(T("ClerkDegraded"),"degraded"!==m().status)?null:e,Y=({children:e,fallback:t,treatPendingAsSignedOut:r,...i})=>{T("Protect");let{isLoaded:n,has:o,userId:s}=N({treatPendingAsSignedOut:r});if(!n)return null;let l=null!=t?t:null;return s?"function"==typeof i.condition?i.condition(o)?e:l:i.role||i.permission||i.feature||i.plan?o(i)?e:l:e:l},X=K(({clerk:e,...t})=>{let{client:r,session:i}=e,n=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return s.useEffect(()=>{null===i&&n?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),ee=K(({clerk:e,...t})=>(s.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),et=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),er=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),ei=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),en=K(({clerk:e,...t})=>(s.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},46456:(e,t,r)=>{r.d(t,{FJ:()=>i.FJ,YF:()=>i.YF});var i=r(72655);r(24466),r(22651)},50287:(e,t,r)=>{let i,n;r.d(t,{ED:()=>eK,pc:()=>eQ,TS:()=>e1,IC:()=>eH,Rs:()=>eq,e3:()=>eV,MZ:()=>ts,Kz:()=>e2,ho:()=>tr,hQ:()=>eJ,WD:()=>eZ,Z5:()=>e4,D_:()=>te,Wp:()=>ta,wV:()=>ti,g7:()=>tn,Jd:()=>to});var o={};r.r(o),r.d(o,{SWRConfig:()=>ej,default:()=>eO,mutate:()=>et,preload:()=>ed,unstable_serialize:()=>eP,useSWRConfig:()=>eu});var s=r(72655),l=(...e)=>{},a=()=>{let e=l,t=l;return{promise:new Promise((r,i)=>{e=r,t=i}),resolve:e,reject:t}};r(24466);var u=r(60376),d="reverification-error",c=e=>({clerk_error:{type:"forbidden",reason:d,metadata:{reverification:e}}}),h=e=>e&&"object"==typeof e&&"clerk_error"in e&&e.clerk_error?.type==="forbidden"&&e.clerk_error?.reason===d,p=r(11227),g=r(22651),f=r(50628),m=r(71885),v=Object.prototype.hasOwnProperty;let k=new WeakMap,b=()=>{},y=b(),_=Object,S=e=>e===y,P=e=>"function"==typeof e,w=(e,t)=>({...e,...t}),C=e=>P(e.then),j={},O={},E="undefined",U=typeof window!=E,M=typeof document!=E,z=U&&"Deno"in window,I=()=>U&&typeof window.requestAnimationFrame!=E,L=(e,t)=>{let r=k.get(e);return[()=>!S(t)&&e.get(t)||j,i=>{if(!S(t)){let n=e.get(t);t in O||(O[t]=n),r[5](t,w(n,i),n||j)}},r[6],()=>!S(t)&&t in O?O[t]:!S(t)&&e.get(t)||j]},W=!0,[R,T]=U&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[b,b],A={initFocus:e=>(M&&document.addEventListener("visibilitychange",e),R("focus",e),()=>{M&&document.removeEventListener("visibilitychange",e),T("focus",e)}),initReconnect:e=>{let t=()=>{W=!0,e()},r=()=>{W=!1};return R("online",t),R("offline",r),()=>{T("online",t),T("offline",r)}}},D=!f.useId,x=!U||z,N=e=>I()?window.requestAnimationFrame(e):setTimeout(e,1),F=x?f.useEffect:f.useLayoutEffect,B="undefined"!=typeof navigator&&navigator.connection,V=!x&&B&&(["slow-2g","2g"].includes(B.effectiveType)||B.saveData),$=new WeakMap,K=(e,t)=>_.prototype.toString.call(e)==="[object ".concat(t,"]"),J=0,q=e=>{let t,r,i=typeof e,n=K(e,"Date"),o=K(e,"RegExp"),s=K(e,"Object");if(_(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=$.get(e))return t;if(t=++J+"~",$.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=q(e[r])+",";$.set(e,t)}if(s){t="#";let i=_.keys(e).sort();for(;!S(r=i.pop());)S(e[r])||(t+=r+":"+q(e[r])+",");$.set(e,t)}}return t},G=e=>{if(P(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?q(e):"",t]},Q=0,Z=()=>++Q;async function H(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[i,n,o,s]=t,l=w({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),a=l.populateCache,u=l.rollbackOnError,d=l.optimisticData,c=e=>"function"==typeof u?u(e):!1!==u,h=l.throwOnError;if(P(n)){let e=[];for(let t of i.keys())!/^\$(inf|sub)\$/.test(t)&&n(i.get(t)._k)&&e.push(t);return Promise.all(e.map(p))}return p(n);async function p(e){let r,[n]=G(e);if(!n)return;let[s,u]=L(i,n),[p,g,f,m]=k.get(i),v=()=>{let t=p[n];return(P(l.revalidate)?l.revalidate(s().data,e):!1!==l.revalidate)&&(delete f[n],delete m[n],t&&t[0])?t[0](2).then(()=>s().data):s().data};if(t.length<3)return v();let b=o,_=Z();g[n]=[_,0];let w=!S(d),j=s(),O=j.data,E=j._c,U=S(E)?O:E;if(w&&u({data:d=P(d)?d(U,O):d,_c:U}),P(b))try{b=b(U)}catch(e){r=e}if(b&&C(b)){if(b=await b.catch(e=>{r=e}),_!==g[n][0]){if(r)throw r;return b}r&&w&&c(r)&&(a=!0,u({data:U,_c:y}))}if(a&&!r&&(P(a)?u({data:a(b,U),error:y,_c:y}):u({data:b,error:y,_c:y})),g[n][1]=Z(),Promise.resolve(v()).then(()=>{u({_c:y})}),r){if(h)throw r;return}return b}}let Y=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},X=(e,t)=>{if(!k.has(e)){let r=w(A,t),i=Object.create(null),n=H.bind(y,e),o=b,s=Object.create(null),l=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},a=(t,r,i)=>{e.set(t,r);let n=s[t];if(n)for(let e of n)e(r,i)},u=()=>{if(!k.has(e)&&(k.set(e,[i,Object.create(null),Object.create(null),Object.create(null),n,a,l]),!x)){let t=r.initFocus(setTimeout.bind(y,Y.bind(y,i,0))),n=r.initReconnect(setTimeout.bind(y,Y.bind(y,i,1)));o=()=>{t&&t(),n&&n(),k.delete(e)}}};return u(),[e,n,u,o]}return[e,k.get(e)[4]]},[ee,et]=X(new Map),er=w({onLoadingSlow:b,onSuccess:b,onError:b,onErrorRetry:(e,t,r,i,n)=>{let o=r.errorRetryCount,s=n.retryCount,l=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(S(o)||!(s>o))&&setTimeout(i,l,n)},onDiscarded:b,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var i,n;if(t===r)return!0;if(t&&r&&(i=t.constructor)===r.constructor){if(i===Date)return t.getTime()===r.getTime();if(i===RegExp)return t.toString()===r.toString();if(i===Array){if((n=t.length)===r.length)for(;n--&&e(t[n],r[n]););return -1===n}if(!i||"object"==typeof t){for(i in n=0,t)if(v.call(t,i)&&++n&&!v.call(r,i)||!(i in r)||!e(t[i],r[i]))return!1;return Object.keys(r).length===n}}return t!=t&&r!=r},isPaused:()=>!1,cache:ee,mutate:et,fallback:{}},{isOnline:()=>W,isVisible:()=>{let e=M&&document.visibilityState;return S(e)||"hidden"!==e}}),ei=(e,t)=>{let r=w(e,t);if(t){let{use:i,fallback:n}=e,{use:o,fallback:s}=t;i&&o&&(r.use=i.concat(o)),n&&s&&(r.fallback=w(n,s))}return r},en=(0,f.createContext)({}),eo="$inf$",es=U&&window.__SWR_DEVTOOLS_USE__,el=es?window.__SWR_DEVTOOLS_USE__:[],ea=e=>P(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],eu=()=>w(er,(0,f.useContext)(en)),ed=(e,t)=>{let[r,i]=G(e),[,,,n]=k.get(ee);if(n[r])return n[r];let o=t(i);return n[r]=o,o},ec=el.concat(e=>(t,r,i)=>{let n=r&&((...e)=>{let[i]=G(t),[,,,n]=k.get(ee);if(i.startsWith(eo))return r(...e);let o=n[i];return S(o)?r(...e):(delete n[i],o)});return e(t,n,i)}),eh=(e,t,r)=>{let i=t[e]||(t[e]=[]);return i.push(r),()=>{let e=i.indexOf(r);e>=0&&(i[e]=i[i.length-1],i.pop())}};es&&(window.__SWR_DEVTOOLS_REACT__=f);let ep=()=>{},eg=ep(),ef=Object,em=e=>e===eg,ev=e=>"function"==typeof e,ek=new WeakMap,eb=(e,t)=>ef.prototype.toString.call(e)===`[object ${t}]`,ey=0,e_=e=>{let t,r,i=typeof e,n=eb(e,"Date"),o=eb(e,"RegExp"),s=eb(e,"Object");if(ef(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=ek.get(e))return t;if(t=++ey+"~",ek.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=e_(e[r])+",";ek.set(e,t)}if(s){t="#";let i=ef.keys(e).sort();for(;!em(r=i.pop());)em(e[r])||(t+=r+":"+e_(e[r])+",");ek.set(e,t)}}return t},eS=e=>{if(ev(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?e_(e):"",t]},eP=e=>eS(e)[0],ew=f.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),eC={dedupe:!0},ej=_.defineProperty(e=>{let{value:t}=e,r=(0,f.useContext)(en),i=P(t),n=(0,f.useMemo)(()=>i?t(r):t,[i,r,t]),o=(0,f.useMemo)(()=>i?n:ei(r,n),[i,r,n]),s=n&&n.provider,l=(0,f.useRef)(y);s&&!l.current&&(l.current=X(s(o.cache||ee),n));let a=l.current;return a&&(o.cache=a[0],o.mutate=a[1]),F(()=>{if(a)return a[2]&&a[2](),a[3]},[]),(0,f.createElement)(en.Provider,w(e,{value:o}))},"defaultValue",{value:er}),eO=(i=(e,t,r)=>{let{cache:i,compare:n,suspense:o,fallbackData:s,revalidateOnMount:l,revalidateIfStale:a,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:c,keepPreviousData:h}=r,[p,g,v,b]=k.get(i),[_,j]=G(e),O=(0,f.useRef)(!1),E=(0,f.useRef)(!1),U=(0,f.useRef)(_),M=(0,f.useRef)(t),z=(0,f.useRef)(r),I=()=>z.current,W=()=>I().isVisible()&&I().isOnline(),[R,T,A,B]=L(i,_),V=(0,f.useRef)({}).current,$=S(s)?S(r.fallback)?y:r.fallback[_]:s,K=(e,t)=>{for(let r in V)if("data"===r){if(!n(e[r],t[r])&&(!S(e[r])||!n(ei,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},J=(0,f.useMemo)(()=>{let e=!!_&&!!t&&(S(l)?!I().isPaused()&&!o&&!1!==a:l),r=t=>{let r=w(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},i=R(),n=B(),s=r(i),u=i===n?s:r(n),d=s;return[()=>{let e=r(R());return K(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>u]},[i,_]),q=(0,m.useSyncExternalStore)((0,f.useCallback)(e=>A(_,(t,r)=>{K(r,t)||e()}),[i,_]),J[0],J[1]),Q=!O.current,Y=p[_]&&p[_].length>0,X=q.data,ee=S(X)?$&&C($)?ew($):$:X,et=q.error,er=(0,f.useRef)(ee),ei=h?S(X)?S(er.current)?ee:er.current:X:ee,en=(!Y||!!S(et))&&(Q&&!S(l)?l:!I().isPaused()&&(o?!S(ee)&&a:S(ee)||a)),eo=!!(_&&t&&Q&&en),es=S(q.isValidating)?eo:q.isValidating,el=S(q.isLoading)?eo:q.isLoading,ea=(0,f.useCallback)(async e=>{let t,i,o=M.current;if(!_||!o||E.current||I().isPaused())return!1;let s=!0,l=e||{},a=!v[_]||!l.dedupe,u=()=>D?!E.current&&_===U.current&&O.current:_===U.current,d={isValidating:!1,isLoading:!1},c=()=>{T(d)},h=()=>{let e=v[_];e&&e[1]===i&&delete v[_]},f={isValidating:!0};S(R().data)&&(f.isLoading=!0);try{if(a&&(T(f),r.loadingTimeout&&S(R().data)&&setTimeout(()=>{s&&u()&&I().onLoadingSlow(_,r)},r.loadingTimeout),v[_]=[o(j),Z()]),[t,i]=v[_],t=await t,a&&setTimeout(h,r.dedupingInterval),!v[_]||v[_][1]!==i)return a&&u()&&I().onDiscarded(_),!1;d.error=y;let e=g[_];if(!S(e)&&(i<=e[0]||i<=e[1]||0===e[1]))return c(),a&&u()&&I().onDiscarded(_),!1;let l=R().data;d.data=n(l,t)?l:t,a&&u()&&I().onSuccess(t,_,r)}catch(r){h();let e=I(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,a&&u()&&(e.onError(r,_,e),(!0===t||P(t)&&t(r))&&(!I().revalidateOnFocus||!I().revalidateOnReconnect||W())&&e.onErrorRetry(r,_,e,e=>{let t=p[_];t&&t[0]&&t[0](3,e)},{retryCount:(l.retryCount||0)+1,dedupe:!0})))}return s=!1,c(),!0},[_,i]),eu=(0,f.useCallback)((...e)=>H(i,U.current,...e),[]);if(F(()=>{M.current=t,z.current=r,S(X)||(er.current=X)}),F(()=>{if(!_)return;let e=ea.bind(y,eC),t=0;I().revalidateOnFocus&&(t=Date.now()+I().focusThrottleInterval);let r=eh(_,p,(r,i={})=>{if(0==r){let r=Date.now();I().revalidateOnFocus&&r>t&&W()&&(t=r+I().focusThrottleInterval,e())}else if(1==r)I().revalidateOnReconnect&&W()&&e();else if(2==r)return ea();else if(3==r)return ea(i)});return E.current=!1,U.current=_,O.current=!0,T({_k:j}),en&&(S(ee)||x?e():N(e)),()=>{E.current=!0,r()}},[_]),F(()=>{let e;function t(){let t=P(u)?u(R().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!R().error&&(d||I().isVisible())&&(c||I().isOnline())?ea(eC).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,d,c,_]),(0,f.useDebugValue)(ei),o&&S(ee)&&_){if(!D&&x)throw Error("Fallback data is required when using Suspense in SSR.");M.current=t,z.current=r,E.current=!1;let e=b[_];if(S(e)||ew(eu(e)),S(et)){let e=ea(eC);S(ei)||(e.status="fulfilled",e.value=!0),ew(e)}else throw et}return{mutate:eu,get data(){return V.data=!0,ei},get error(){return V.error=!0,et},get isValidating(){return V.isValidating=!0,es},get isLoading(){return V.isLoading=!0,el}}},function(...e){let t=eu(),[r,n,o]=ea(e),s=ei(t,o),l=i,{use:a}=s,u=(a||[]).concat(ec);for(let e=u.length;e--;)l=u[e](l);return l(r,n||s.fetcher||null,s)}),eE=()=>{},eU=eE(),eM=Object,ez=e=>e===eU,eI=e=>"function"==typeof e,eL=new WeakMap,eW=(e,t)=>eM.prototype.toString.call(e)===`[object ${t}]`,eR=0,eT=e=>{let t,r,i=typeof e,n=eW(e,"Date"),o=eW(e,"RegExp"),s=eW(e,"Object");if(eM(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=eL.get(e))return t;if(t=++eR+"~",eL.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eT(e[r])+",";eL.set(e,t)}if(s){t="#";let i=eM.keys(e).sort();for(;!ez(r=i.pop());)ez(e[r])||(t+=r+":"+eT(e[r])+",");eL.set(e,t)}}return t},eA=e=>{if(eI(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eT(e):"",t]},eD=e=>eA(e?e(0,null):null)[0],ex=Promise.resolve(),eN=(n=e=>(t,r,i)=>{let n,o=(0,f.useRef)(!1),{cache:s,initialSize:l=1,revalidateAll:a=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:h=!1}=i,[,,,p]=k.get(ee);try{(n=eD(t))&&(n=eo+n)}catch(e){}let[g,v,b]=L(s,n),_=(0,f.useCallback)(()=>S(g()._l)?l:g()._l,[s,n,l]);(0,m.useSyncExternalStore)((0,f.useCallback)(e=>n?b(n,()=>{e()}):()=>{},[s,n]),_,_);let w=(0,f.useCallback)(()=>{let e=g()._l;return S(e)?l:e},[n,l]),C=(0,f.useRef)(w());F(()=>{if(!o.current){o.current=!0;return}n&&v({_l:u?C.current:w()})},[n,s]);let j=c&&!o.current,O=e(n,async e=>{let n=g()._i,o=g()._r;v({_r:y});let l=[],u=w(),[c]=L(s,e),f=c().data,m=[],k=null;for(let e=0;e<u;++e){let[u,c]=G(t(e,h?null:k));if(!u)break;let[g,v]=L(s,u),b=g().data,y=a||n||S(b)||d&&!e&&!S(f)||j||f&&!S(f[e])&&!i.compare(f[e],b);if(r&&("function"==typeof o?o(b,c):y)){let t=async()=>{if(u in p){let e=p[u];delete p[u],b=await e}else b=await r(c);v({data:b,_k:c}),l[e]=b};h?m.push(t):await t()}else l[e]=b;h||(k=b)}return h&&await Promise.all(m.map(e=>e())),v({_i:y}),l},i),E=(0,f.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},i=!1!==r.revalidate;return n?(i&&(S(e)?v({_i:!0,_r:r.revalidate}):v({_i:!1,_r:r.revalidate})),arguments.length?O.mutate(e,{...r,revalidate:i}):O.mutate()):ex},[n,s]),U=(0,f.useCallback)(e=>{let r;if(!n)return ex;let[,i]=L(s,n);if(P(e)?r=e(w()):"number"==typeof e&&(r=e),"number"!=typeof r)return ex;i({_l:r}),C.current=r;let o=[],[l]=L(s,n),a=null;for(let e=0;e<r;++e){let[r]=G(t(e,a)),[i]=L(s,r),n=r?i().data:y;if(S(n))return E(l().data);o.push(n),a=n}return E(o)},[n,s,E,w]);return{size:w(),setSize:U,mutate:E,get data(){return O.data},get error(){return O.error},get isValidating(){return O.isValidating},get isLoading(){return O.isLoading}}},(...e)=>{let[t,r,i]=ea(e),o=(i.use||[]).concat(n);return eO(t,r,{...i,use:o})});var eF=r(61372);function eB(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var eV=(e,t)=>{let{assertCtxFn:r=eB}=t||{},i=f.createContext(void 0);return i.displayName=e,[i,()=>{let t=f.useContext(i);return r(t,`${e} not found`),t.value},()=>{let e=f.useContext(i);return e?e.value:{}}]},e$={};(0,g.VA)(e$,{useSWR:()=>eO,useSWRInfinite:()=>eN}),(0,g.ie)(e$,o);var[eK,eJ]=eV("ClerkInstanceContext"),[eq,eG]=eV("UserContext"),[eQ,eZ]=eV("ClientContext"),[eH,eY]=eV("SessionContext"),[eX,e0]=(f.createContext({}),eV("OrganizationContext")),e1=({children:e,organization:t,swrConfig:r})=>f.createElement(e$.SWRConfig,{value:r},f.createElement(eX.Provider,{value:{value:{organization:t}}},e));function e2(e){if(!f.useContext(eK)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function e6(e,t){let r=new Set(Object.keys(t)),i={};for(let t of Object.keys(e))r.has(t)||(i[t]=e[t]);return i}var e5=(e,t)=>{let r="boolean"==typeof e&&e,i=(0,f.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),n=(0,f.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),o={};for(let i of Object.keys(t))o[i]=r?t[i]:e?.[i]??t[i];return{...o,initialPage:i.current,pageSize:n.current}},e7={dedupingInterval:6e4,focusThrottleInterval:12e4},e3=(e,t,r,i)=>{let[n,o]=(0,f.useState)(e.initialPage??1),s=(0,f.useRef)(e.initialPage??1),l=(0,f.useRef)(e.pageSize??10),a=r.enabled??!0,u=r.infinite??!1,d=r.keepPreviousData??!1,c={...i,...e,initialPage:n,pageSize:l.current},{data:h,isValidating:p,isLoading:g,error:m,mutate:v}=eO(!u&&t&&a?c:null,e=>{let r=e6(e,i);return t?.(r)},{keepPreviousData:d,...e7}),{data:k,isLoading:b,isValidating:y,error:_,size:S,setSize:P,mutate:w}=eN(t=>u&&a?{...e,...i,initialPage:s.current+t,pageSize:l.current}:null,e=>{let r=e6(e,i);return t?.(r)},e7),C=(0,f.useMemo)(()=>u?S:n,[u,S,n]),j=(0,f.useCallback)(e=>u?void P(e):o(e),[P]),O=(0,f.useMemo)(()=>u?k?.map(e=>e?.data).flat()??[]:h?.data??[],[u,h,k]),E=(0,f.useMemo)(()=>u?k?.[k?.length-1]?.total_count||0:h?.total_count??0,[u,h,k]),U=u?b:g,M=u?y:p,z=(u?_:m)??null,I=(0,f.useCallback)(()=>{j(e=>Math.max(0,e+1))},[j]),L=(0,f.useCallback)(()=>{j(e=>Math.max(0,e-1))},[j]),W=(s.current-1)*l.current,R=Math.ceil((E-W)/l.current),T=E-W*l.current>C*l.current,A=(C-1)*l.current>W*l.current,D=u?e=>w(e,{revalidate:!1}):e=>v(e,{revalidate:!1});return{data:O,count:E,error:z,isLoading:U,isFetching:M,isError:!!z,page:C,pageCount:R,fetchPage:j,fetchNext:I,fetchPrevious:L,hasNextPage:T,hasPreviousPage:A,revalidate:u?()=>w():()=>v(),setData:D}},e8={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function e4(e){var t,r;let{domains:i,membershipRequests:n,memberships:o,invitations:l,subscriptions:a}=e||{};e2("useOrganization");let{organization:u}=e0(),d=eY(),c=e5(i,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),h=e5(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),p=e5(o,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1,query:void 0}),g=e5(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),f=e5(a,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),m=eJ();m.telemetry?.record((0,s.FJ)("useOrganization"));let v=void 0===i?void 0:{initialPage:c.initialPage,pageSize:c.pageSize,enrollmentMode:c.enrollmentMode},k=void 0===n?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,status:h.status},b=void 0===o?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,role:p.role,query:p.query},y=void 0===l?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,status:g.status},_=void 0===a?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,orgId:u?.id},S=e3({...v},u?.getDomains,{keepPreviousData:c.keepPreviousData,infinite:c.infinite,enabled:!!v},{type:"domains",organizationId:u?.id}),P=e3({...k},u?.getMembershipRequests,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!k},{type:"membershipRequests",organizationId:u?.id}),w=e3(b||{},u?.getMemberships,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!b},{type:"members",organizationId:u?.id}),C=e3({...y},u?.getInvitations,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!y},{type:"invitations",organizationId:u?.id}),j=e3({..._},u?.getSubscriptions,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!_},{type:"subscriptions",organizationId:u?.id});return void 0===u?{isLoaded:!1,organization:void 0,membership:void 0,domains:e8,membershipRequests:e8,memberships:e8,invitations:e8,subscriptions:e8}:null===u?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null,subscriptions:null}:!m.loaded&&u?{isLoaded:!0,organization:u,membership:void 0,domains:e8,membershipRequests:e8,memberships:e8,invitations:e8,subscriptions:e8}:{isLoaded:m.loaded,organization:u,membership:(t=d.user.organizationMemberships,r=u.id,t.find(e=>e.organization.id===r)),domains:S,membershipRequests:P,memberships:w,invitations:C,subscriptions:j}}var e9={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function te(e){let{userMemberships:t,userInvitations:r,userSuggestions:i}=e||{};e2("useOrganizationList");let n=e5(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),o=e5(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=e5(i,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),a=eJ(),u=eG();a.telemetry?.record((0,s.FJ)("useOrganizationList"));let d=void 0===t?void 0:{initialPage:n.initialPage,pageSize:n.pageSize},c=void 0===r?void 0:{initialPage:o.initialPage,pageSize:o.pageSize,status:o.status},h=void 0===i?void 0:{initialPage:l.initialPage,pageSize:l.pageSize,status:l.status},p=!!(a.loaded&&u),g=e3(d||{},u?.getOrganizationMemberships,{keepPreviousData:n.keepPreviousData,infinite:n.infinite,enabled:!!d},{type:"userMemberships",userId:u?.id}),f=e3({...c},u?.getOrganizationInvitations,{keepPreviousData:o.keepPreviousData,infinite:o.infinite,enabled:!!c},{type:"userInvitations",userId:u?.id}),m=e3({...h},u?.getOrganizationSuggestions,{keepPreviousData:l.keepPreviousData,infinite:l.infinite,enabled:!!h},{type:"userSuggestions",userId:u?.id});return p?{isLoaded:p,setActive:a.setActive,createOrganization:a.createOrganization,userMemberships:g,userInvitations:f,userSuggestions:m}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:e9,userInvitations:e9,userSuggestions:e9}}var tt="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tr=()=>(e2("useClerk"),eJ()),ti=(e={})=>{e2("useSession");let t=eY(),r=tr();if(void 0===t)return{isLoaded:!1,isSignedIn:void 0,session:void 0};let i=t?.status==="pending"&&(e.treatPendingAsSignedOut??r.__internal_getOption("treatPendingAsSignedOut"));return null===t||i?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:t}},tn=()=>{e2("useSessionList");let e=eJ(),t=eZ();return t?{isLoaded:!0,sessions:t.sessions,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setActive:void 0}};function to(){e2("useUser");let e=eG();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var ts=eF.j;async function tl(e){try{let t=await e;if(t instanceof Response)return t.json();return t}catch(e){if((0,u.$R)(e)&&e.errors.find(({code:e})=>"session_reverification_required"===e))return c();throw e}}var ta=(e,t)=>{let{__internal_openReverification:r,telemetry:i}=tr(),n=(0,f.useRef)(e),o=(0,f.useRef)(t),l=(0,f.useMemo)(()=>(function(e){return function(t){return async(...r)=>{let i=await tl(t(...r));if(h(i)){let n=a(),o=(0,p.D)(i.clerk_error.metadata?.reverification),l=o?o().level:void 0,d=()=>{n.reject(new u.cR("User cancelled attempted verification",{code:"reverification_cancelled"}))},c=()=>{n.resolve(!0)};void 0===e.onNeedsReverification?e.openUIComponent?.({level:l,afterVerification:c,afterVerificationCancelled:d}):(e.telemetry?.record((0,s.FJ)("UserVerificationCustomUI")),e.onNeedsReverification({cancel:d,complete:c,level:l})),await n.promise,i=await tl(t(...r))}return i}}})({openUIComponent:r,telemetry:i,...o.current})(n.current),[r,n.current,o.current]);return tt(()=>{n.current=e,o.current=t}),l}},60376:(e,t,r)=>{function i(e){return"clerkError"in e}r.d(t,{$R:()=>i,_r:()=>s,cR:()=>n});var n=class e extends Error{constructor(t,{code:r}){let i="\uD83D\uDD12 Clerk:",n=RegExp(i.replace(" ","\\s*"),"i"),o=t.replace(n,""),s=`${i} ${o.trim()}

(code="${r}")
`;super(s),this.toString=()=>`[${this.name}]
Message:${this.message}`,Object.setPrototypeOf(this,e.prototype),this.code=r,this.message=s,this.clerkRuntimeError=!0,this.name="ClerkRuntimeError"}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function s({packageName:e,customMessages:t}){let r=e,i={...o,...t};function n(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(n(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(n(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(n(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(n(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(n(i.MissingClerkProvider,e))},throw(e){throw Error(n(e))}}}},63848:(e,t,r)=>{r.d(t,{RZ:()=>a,rA:()=>l,q5:()=>s});var i=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,n=r(70301),o="pk_live_";function s(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(o)?"production":"development",n=i(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function l(e=""){try{let t=e.startsWith(o)||e.startsWith("pk_test_"),r=i(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function a(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=n.gE.some(e=>r.endsWith(e)),e.set(r,i)),i}}}},66598:(e,t,r)=>{r.d(t,{VK:()=>o,b_:()=>i.b_,Fj:()=>i.Fj,s2:()=>n});var i=r(88167),n=e=>{(0,i.b_)()&&console.error(`Clerk: ${e}`)};function o(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}r(22651)},70301:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>a,Vc:()=>l,gE:()=>n,iM:()=>i,mG:()=>o,ub:()=>s});var i=[".lcl.dev",".lclstage.dev",".lclclerk.com"],n=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],l="https://api.lclclerk.com",a="https://api.clerkstage.dev",u="https://api.clerk.com"},71885:(e,t,r)=>{e.exports=r(99512)},72655:(e,t,r)=>{r.d(t,{FJ:()=>b,YF:()=>y}),r(24466);var i,n,o,s,l,a,u,d,c,h,p,g,f,m,v,k=r(22651);r(37811),i=new WeakMap,n=new WeakMap,o=new WeakSet,s=function(e){let{sk:t,pk:r,payload:i,...n}=e,o={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>o[e]))},l=function(){let e=localStorage.getItem((0,k.S7)(this,i));return e?JSON.parse(e):{}},a=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,k.S7)(this,i)),!1}};u=new WeakMap,d=new WeakMap,c=new WeakMap,h=new WeakMap,p=new WeakMap,g=new WeakSet,f=function(e,t){let r=Math.random();return!!(r<=(0,k.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,k.S7)(this,d).isEventThrottled(e)},m=function(){fetch(new URL("/v1/event",(0,k.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.OV)(this,h,[])}).catch(()=>void 0)},v=function(){let e={name:(0,k.S7)(this,c).sdk,version:(0,k.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e};function b(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}function y(e){return{event:"FRAMEWORK_METADATA",eventSamplingRate:.1,payload:e}}},76578:(e,t,r)=>{r.d(t,{T5:()=>S,nO:()=>_,_R:()=>y,kX:()=>b});var i=(e,t="5.67.3")=>{if(e)return e;let r=n(t);return r?"snapshot"===r?"5.67.3":r:o(t)},n=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],o=e=>e.trim().replace(/^v/,"").split(".")[0];function s(e){return e.startsWith("/")}var l=/\/$|\/\?|\/#/,a={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},u=async e=>new Promise(t=>setTimeout(t,e)),d=(e,t)=>t?e*(1+Math.random()):e,c=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=d(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await u(r()),t++}},h=async(e,t={})=>{let r=0,{shouldRetry:i,initialDelay:n,maxDelayBetweenRetries:o,factor:s,retryImmediately:l,jitter:h}={...a,...t},p=c({initialDelay:n,maxDelayBetweenRetries:o,factor:s,jitter:h});for(;;)try{return await e()}catch(e){if(!i(e,++r))throw e;l&&1===r?await u(d(100,h)):await p()}};async function p(e="",t){let{async:r,defer:i,beforeLoad:n,crossOrigin:o,nonce:s}=t||{};return h(()=>new Promise((t,l)=>{e||l(Error("loadScript cannot be called without a src")),document&&document.body||l("loadScript cannot be called when document does not exist");let a=document.createElement("script");o&&a.setAttribute("crossorigin",o),a.async=r||!1,a.defer=i||!1,a.addEventListener("load",()=>{a.remove(),t(a)}),a.addEventListener("error",()=>{a.remove(),l()}),a.src=e,a.nonce=s,n?.(a),document.body.appendChild(a)}),{shouldRetry:(e,t)=>t<=5})}var g=r(60376),f=r(63848),m="Clerk: Failed to load Clerk",{isDevOrStagingUrl:v}=(0,f.RZ)(),k=(0,g._r)({packageName:"@clerk/shared"});function b(e){k.setPackageName({packageName:e})}var y=async e=>{let t=document.querySelector("script[data-clerk-js-script]");return t?new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(m)})}):e?.publishableKey?p(_(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:P(e)}).catch(()=>{throw Error(m)}):void k.throwMissingPublishableKeyError()},_=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:n,proxyUrl:o,domain:l,publishableKey:a}=e;if(t)return t;let u="";u=o&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||s(e)}(o)?(function(e){return e?s(e)?new URL(e,window.location.origin).toString():e:""})(o).replace(/http(s)?:\/\//,""):l&&!v((0,f.q5)(a)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(l):(0,f.q5)(a)?.frontendApi||"";let d=r?`${r.replace(/\.+$/,"")}.`:"",c=i(n);return`https://${u}/npm/@clerk/clerk-js@${c}/dist/clerk.${d}browser.js`},S=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},P=e=>t=>{let r=S(e);for(let e in r)t.setAttribute(e,r[e])};r(22651)},88167:(e,t,r)=>{r.d(t,{Fj:()=>o,MC:()=>n,b_:()=>i});var i=()=>!1,n=()=>!1,o=()=>{try{return!0}catch{}return!1}},99512:(e,t,r)=>{var i=r(50628),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useState,s=i.useEffect,l=i.useLayoutEffect,a=i.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),i=o({inst:{value:r,getSnapshot:t}}),n=i[0].inst,d=i[1];return l(function(){n.value=r,n.getSnapshot=t,u(n)&&d({inst:n})},[e,r,t]),s(function(){return u(n)&&d({inst:n}),e(function(){u(n)&&d({inst:n})})},[e]),a(r),r};t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:d}}]);