{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/github-light-default.mjs"], "sourcesContent": ["var githubLightDefault = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#fd8c73\",\n    \"activityBar.background\": \"#ffffff\",\n    \"activityBar.border\": \"#d0d7de\",\n    \"activityBar.foreground\": \"#1f2328\",\n    \"activityBar.inactiveForeground\": \"#656d76\",\n    \"activityBarBadge.background\": \"#0969da\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"badge.background\": \"#0969da\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumb.activeSelectionForeground\": \"#656d76\",\n    \"breadcrumb.focusForeground\": \"#1f2328\",\n    \"breadcrumb.foreground\": \"#656d76\",\n    \"breadcrumbPicker.background\": \"#ffffff\",\n    \"button.background\": \"#1f883d\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#1a7f37\",\n    \"button.secondaryBackground\": \"#ebecf0\",\n    \"button.secondaryForeground\": \"#24292f\",\n    \"button.secondaryHoverBackground\": \"#f3f4f6\",\n    \"checkbox.background\": \"#f6f8fa\",\n    \"checkbox.border\": \"#d0d7de\",\n    \"debugConsole.errorForeground\": \"#cf222e\",\n    \"debugConsole.infoForeground\": \"#57606a\",\n    \"debugConsole.sourceForeground\": \"#9a6700\",\n    \"debugConsole.warningForeground\": \"#7d4e00\",\n    \"debugConsoleInputIcon.foreground\": \"#6639ba\",\n    \"debugIcon.breakpointForeground\": \"#cf222e\",\n    \"debugTokenExpression.boolean\": \"#116329\",\n    \"debugTokenExpression.error\": \"#a40e26\",\n    \"debugTokenExpression.name\": \"#0550ae\",\n    \"debugTokenExpression.number\": \"#116329\",\n    \"debugTokenExpression.string\": \"#0a3069\",\n    \"debugTokenExpression.value\": \"#0a3069\",\n    \"debugToolBar.background\": \"#ffffff\",\n    \"descriptionForeground\": \"#656d76\",\n    \"diffEditor.insertedLineBackground\": \"#aceebb4d\",\n    \"diffEditor.insertedTextBackground\": \"#6fdd8b80\",\n    \"diffEditor.removedLineBackground\": \"#ffcecb4d\",\n    \"diffEditor.removedTextBackground\": \"#ff818266\",\n    \"dropdown.background\": \"#ffffff\",\n    \"dropdown.border\": \"#d0d7de\",\n    \"dropdown.foreground\": \"#1f2328\",\n    \"dropdown.listBackground\": \"#ffffff\",\n    \"editor.background\": \"#ffffff\",\n    \"editor.findMatchBackground\": \"#bf8700\",\n    \"editor.findMatchHighlightBackground\": \"#fae17d80\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#4ac26b66\",\n    \"editor.foldBackground\": \"#6e77811a\",\n    \"editor.foreground\": \"#1f2328\",\n    \"editor.lineHighlightBackground\": \"#eaeef280\",\n    \"editor.linkedEditingBackground\": \"#0969da12\",\n    \"editor.selectionHighlightBackground\": \"#4ac26b40\",\n    \"editor.stackFrameHighlightBackground\": \"#d4a72c66\",\n    \"editor.wordHighlightBackground\": \"#eaeef280\",\n    \"editor.wordHighlightBorder\": \"#afb8c199\",\n    \"editor.wordHighlightStrongBackground\": \"#afb8c14d\",\n    \"editor.wordHighlightStrongBorder\": \"#afb8c199\",\n    \"editorBracketHighlight.foreground1\": \"#0969da\",\n    \"editorBracketHighlight.foreground2\": \"#1a7f37\",\n    \"editorBracketHighlight.foreground3\": \"#9a6700\",\n    \"editorBracketHighlight.foreground4\": \"#cf222e\",\n    \"editorBracketHighlight.foreground5\": \"#bf3989\",\n    \"editorBracketHighlight.foreground6\": \"#8250df\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#656d76\",\n    \"editorBracketMatch.background\": \"#4ac26b40\",\n    \"editorBracketMatch.border\": \"#4ac26b99\",\n    \"editorCursor.foreground\": \"#0969da\",\n    \"editorGroup.border\": \"#d0d7de\",\n    \"editorGroupHeader.tabsBackground\": \"#f6f8fa\",\n    \"editorGroupHeader.tabsBorder\": \"#d0d7de\",\n    \"editorGutter.addedBackground\": \"#4ac26b66\",\n    \"editorGutter.deletedBackground\": \"#ff818266\",\n    \"editorGutter.modifiedBackground\": \"#d4a72c66\",\n    \"editorIndentGuide.activeBackground\": \"#1f23283d\",\n    \"editorIndentGuide.background\": \"#1f23281f\",\n    \"editorInlayHint.background\": \"#afb8c133\",\n    \"editorInlayHint.foreground\": \"#656d76\",\n    \"editorInlayHint.paramBackground\": \"#afb8c133\",\n    \"editorInlayHint.paramForeground\": \"#656d76\",\n    \"editorInlayHint.typeBackground\": \"#afb8c133\",\n    \"editorInlayHint.typeForeground\": \"#656d76\",\n    \"editorLineNumber.activeForeground\": \"#1f2328\",\n    \"editorLineNumber.foreground\": \"#8c959f\",\n    \"editorOverviewRuler.border\": \"#ffffff\",\n    \"editorWhitespace.foreground\": \"#afb8c1\",\n    \"editorWidget.background\": \"#ffffff\",\n    \"errorForeground\": \"#cf222e\",\n    \"focusBorder\": \"#0969da\",\n    \"foreground\": \"#1f2328\",\n    \"gitDecoration.addedResourceForeground\": \"#1a7f37\",\n    \"gitDecoration.conflictingResourceForeground\": \"#bc4c00\",\n    \"gitDecoration.deletedResourceForeground\": \"#cf222e\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6e7781\",\n    \"gitDecoration.modifiedResourceForeground\": \"#9a6700\",\n    \"gitDecoration.submoduleResourceForeground\": \"#656d76\",\n    \"gitDecoration.untrackedResourceForeground\": \"#1a7f37\",\n    \"icon.foreground\": \"#656d76\",\n    \"input.background\": \"#ffffff\",\n    \"input.border\": \"#d0d7de\",\n    \"input.foreground\": \"#1f2328\",\n    \"input.placeholderForeground\": \"#6e7781\",\n    \"keybindingLabel.foreground\": \"#1f2328\",\n    \"list.activeSelectionBackground\": \"#afb8c133\",\n    \"list.activeSelectionForeground\": \"#1f2328\",\n    \"list.focusBackground\": \"#ddf4ff\",\n    \"list.focusForeground\": \"#1f2328\",\n    \"list.highlightForeground\": \"#0969da\",\n    \"list.hoverBackground\": \"#eaeef280\",\n    \"list.hoverForeground\": \"#1f2328\",\n    \"list.inactiveFocusBackground\": \"#ddf4ff\",\n    \"list.inactiveSelectionBackground\": \"#afb8c133\",\n    \"list.inactiveSelectionForeground\": \"#1f2328\",\n    \"minimapSlider.activeBackground\": \"#8c959f47\",\n    \"minimapSlider.background\": \"#8c959f33\",\n    \"minimapSlider.hoverBackground\": \"#8c959f3d\",\n    \"notificationCenterHeader.background\": \"#f6f8fa\",\n    \"notificationCenterHeader.foreground\": \"#656d76\",\n    \"notifications.background\": \"#ffffff\",\n    \"notifications.border\": \"#d0d7de\",\n    \"notifications.foreground\": \"#1f2328\",\n    \"notificationsErrorIcon.foreground\": \"#cf222e\",\n    \"notificationsInfoIcon.foreground\": \"#0969da\",\n    \"notificationsWarningIcon.foreground\": \"#9a6700\",\n    \"panel.background\": \"#f6f8fa\",\n    \"panel.border\": \"#d0d7de\",\n    \"panelInput.border\": \"#d0d7de\",\n    \"panelTitle.activeBorder\": \"#fd8c73\",\n    \"panelTitle.activeForeground\": \"#1f2328\",\n    \"panelTitle.inactiveForeground\": \"#656d76\",\n    \"pickerGroup.border\": \"#d0d7de\",\n    \"pickerGroup.foreground\": \"#656d76\",\n    \"progressBar.background\": \"#0969da\",\n    \"quickInput.background\": \"#ffffff\",\n    \"quickInput.foreground\": \"#1f2328\",\n    \"scrollbar.shadow\": \"#6e778133\",\n    \"scrollbarSlider.activeBackground\": \"#8c959f47\",\n    \"scrollbarSlider.background\": \"#8c959f33\",\n    \"scrollbarSlider.hoverBackground\": \"#8c959f3d\",\n    \"settings.headerForeground\": \"#1f2328\",\n    \"settings.modifiedItemIndicator\": \"#d4a72c66\",\n    \"sideBar.background\": \"#f6f8fa\",\n    \"sideBar.border\": \"#d0d7de\",\n    \"sideBar.foreground\": \"#1f2328\",\n    \"sideBarSectionHeader.background\": \"#f6f8fa\",\n    \"sideBarSectionHeader.border\": \"#d0d7de\",\n    \"sideBarSectionHeader.foreground\": \"#1f2328\",\n    \"sideBarTitle.foreground\": \"#1f2328\",\n    \"statusBar.background\": \"#ffffff\",\n    \"statusBar.border\": \"#d0d7de\",\n    \"statusBar.debuggingBackground\": \"#cf222e\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.focusBorder\": \"#0969da80\",\n    \"statusBar.foreground\": \"#656d76\",\n    \"statusBar.noFolderBackground\": \"#ffffff\",\n    \"statusBarItem.activeBackground\": \"#1f23281f\",\n    \"statusBarItem.focusBorder\": \"#0969da\",\n    \"statusBarItem.hoverBackground\": \"#1f232814\",\n    \"statusBarItem.prominentBackground\": \"#afb8c133\",\n    \"statusBarItem.remoteBackground\": \"#eaeef2\",\n    \"statusBarItem.remoteForeground\": \"#1f2328\",\n    \"symbolIcon.arrayForeground\": \"#953800\",\n    \"symbolIcon.booleanForeground\": \"#0550ae\",\n    \"symbolIcon.classForeground\": \"#953800\",\n    \"symbolIcon.colorForeground\": \"#0a3069\",\n    \"symbolIcon.constantForeground\": \"#116329\",\n    \"symbolIcon.constructorForeground\": \"#3e1f79\",\n    \"symbolIcon.enumeratorForeground\": \"#953800\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#0550ae\",\n    \"symbolIcon.eventForeground\": \"#57606a\",\n    \"symbolIcon.fieldForeground\": \"#953800\",\n    \"symbolIcon.fileForeground\": \"#7d4e00\",\n    \"symbolIcon.folderForeground\": \"#7d4e00\",\n    \"symbolIcon.functionForeground\": \"#6639ba\",\n    \"symbolIcon.interfaceForeground\": \"#953800\",\n    \"symbolIcon.keyForeground\": \"#0550ae\",\n    \"symbolIcon.keywordForeground\": \"#a40e26\",\n    \"symbolIcon.methodForeground\": \"#6639ba\",\n    \"symbolIcon.moduleForeground\": \"#a40e26\",\n    \"symbolIcon.namespaceForeground\": \"#a40e26\",\n    \"symbolIcon.nullForeground\": \"#0550ae\",\n    \"symbolIcon.numberForeground\": \"#116329\",\n    \"symbolIcon.objectForeground\": \"#953800\",\n    \"symbolIcon.operatorForeground\": \"#0a3069\",\n    \"symbolIcon.packageForeground\": \"#953800\",\n    \"symbolIcon.propertyForeground\": \"#953800\",\n    \"symbolIcon.referenceForeground\": \"#0550ae\",\n    \"symbolIcon.snippetForeground\": \"#0550ae\",\n    \"symbolIcon.stringForeground\": \"#0a3069\",\n    \"symbolIcon.structForeground\": \"#953800\",\n    \"symbolIcon.textForeground\": \"#0a3069\",\n    \"symbolIcon.typeParameterForeground\": \"#0a3069\",\n    \"symbolIcon.unitForeground\": \"#0550ae\",\n    \"symbolIcon.variableForeground\": \"#953800\",\n    \"tab.activeBackground\": \"#ffffff\",\n    \"tab.activeBorder\": \"#ffffff\",\n    \"tab.activeBorderTop\": \"#fd8c73\",\n    \"tab.activeForeground\": \"#1f2328\",\n    \"tab.border\": \"#d0d7de\",\n    \"tab.hoverBackground\": \"#ffffff\",\n    \"tab.inactiveBackground\": \"#f6f8fa\",\n    \"tab.inactiveForeground\": \"#656d76\",\n    \"tab.unfocusedActiveBorder\": \"#ffffff\",\n    \"tab.unfocusedActiveBorderTop\": \"#d0d7de\",\n    \"tab.unfocusedHoverBackground\": \"#eaeef280\",\n    \"terminal.ansiBlack\": \"#24292f\",\n    \"terminal.ansiBlue\": \"#0969da\",\n    \"terminal.ansiBrightBlack\": \"#57606a\",\n    \"terminal.ansiBrightBlue\": \"#218bff\",\n    \"terminal.ansiBrightCyan\": \"#3192aa\",\n    \"terminal.ansiBrightGreen\": \"#1a7f37\",\n    \"terminal.ansiBrightMagenta\": \"#a475f9\",\n    \"terminal.ansiBrightRed\": \"#a40e26\",\n    \"terminal.ansiBrightWhite\": \"#8c959f\",\n    \"terminal.ansiBrightYellow\": \"#633c01\",\n    \"terminal.ansiCyan\": \"#1b7c83\",\n    \"terminal.ansiGreen\": \"#116329\",\n    \"terminal.ansiMagenta\": \"#8250df\",\n    \"terminal.ansiRed\": \"#cf222e\",\n    \"terminal.ansiWhite\": \"#6e7781\",\n    \"terminal.ansiYellow\": \"#4d2d00\",\n    \"terminal.foreground\": \"#1f2328\",\n    \"textBlockQuote.background\": \"#f6f8fa\",\n    \"textBlockQuote.border\": \"#d0d7de\",\n    \"textCodeBlock.background\": \"#afb8c133\",\n    \"textLink.activeForeground\": \"#0969da\",\n    \"textLink.foreground\": \"#0969da\",\n    \"textPreformat.foreground\": \"#656d76\",\n    \"textSeparator.foreground\": \"#d8dee4\",\n    \"titleBar.activeBackground\": \"#ffffff\",\n    \"titleBar.activeForeground\": \"#656d76\",\n    \"titleBar.border\": \"#d0d7de\",\n    \"titleBar.inactiveBackground\": \"#f6f8fa\",\n    \"titleBar.inactiveForeground\": \"#656d76\",\n    \"tree.indentGuidesStroke\": \"#d8dee4\",\n    \"welcomePage.buttonBackground\": \"#f6f8fa\",\n    \"welcomePage.buttonHoverBackground\": \"#f3f4f6\"\n  },\n  \"displayName\": \"GitHub Light Default\",\n  \"name\": \"github-light-default\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6e7781\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cf222e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.other.constant\",\n        \"variable.other.enummember\",\n        \"variable.language\",\n        \"entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"meta.export.default\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#953800\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function\",\n        \"meta.jsx.children\",\n        \"meta.block\",\n        \"meta.tag.attributes\",\n        \"entity.name.constant\",\n        \"meta.object.member\",\n        \"meta.embedded.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1f2328\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#8250df\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#116329\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#cf222e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cf222e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1f2328\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0a3069\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#953800\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#1f2328\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#cf222e\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#f6f8fa\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0a3069\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0a3069\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#116329\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#116329\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#953800\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#116329\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#1f2328\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#1f2328\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strikethrough\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#ffebe9\",\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cf222e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#dafbe1\",\n        \"foreground\": \"#116329\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#ffd8b5\",\n        \"foreground\": \"#953800\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#0550ae\",\n        \"foreground\": \"#eaeef2\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#8250df\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#0550ae\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#57606a\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#82071e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0a3069\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { githubLightDefault as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,qBAAqB,OAAO,MAAM,CAAC;IACrC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,kCAAkC;QAClC,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,kCAAkC;QAClC,kCAAkC;QAClC,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}