#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/cli/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/cli/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../react-email/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../react-email/dist/cli/index.mjs" "$@"
fi
