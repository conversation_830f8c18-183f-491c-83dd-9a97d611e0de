{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/everforest-light.mjs"], "sourcesContent": ["var everforestLight = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#93b259d0\",\n    \"activityBar.activeFocusBorder\": \"#93b259\",\n    \"activityBar.background\": \"#fdf6e3\",\n    \"activityBar.border\": \"#fdf6e3\",\n    \"activityBar.dropBackground\": \"#fdf6e3\",\n    \"activityBar.foreground\": \"#5c6a72\",\n    \"activityBar.inactiveForeground\": \"#939f91\",\n    \"activityBarBadge.background\": \"#93b259\",\n    \"activityBarBadge.foreground\": \"#fdf6e3\",\n    \"badge.background\": \"#93b259\",\n    \"badge.foreground\": \"#fdf6e3\",\n    \"breadcrumb.activeSelectionForeground\": \"#5c6a72\",\n    \"breadcrumb.focusForeground\": \"#5c6a72\",\n    \"breadcrumb.foreground\": \"#939f91\",\n    \"button.background\": \"#93b259\",\n    \"button.foreground\": \"#fdf6e3\",\n    \"button.hoverBackground\": \"#93b259d0\",\n    \"button.secondaryBackground\": \"#efebd4\",\n    \"button.secondaryForeground\": \"#5c6a72\",\n    \"button.secondaryHoverBackground\": \"#e6e2cc\",\n    \"charts.blue\": \"#3a94c5\",\n    \"charts.foreground\": \"#5c6a72\",\n    \"charts.green\": \"#8da101\",\n    \"charts.orange\": \"#f57d26\",\n    \"charts.purple\": \"#df69ba\",\n    \"charts.red\": \"#f85552\",\n    \"charts.yellow\": \"#dfa000\",\n    \"checkbox.background\": \"#fdf6e3\",\n    \"checkbox.border\": \"#e0dcc7\",\n    \"checkbox.foreground\": \"#f57d26\",\n    \"debugConsole.errorForeground\": \"#f85552\",\n    \"debugConsole.infoForeground\": \"#8da101\",\n    \"debugConsole.sourceForeground\": \"#df69ba\",\n    \"debugConsole.warningForeground\": \"#dfa000\",\n    \"debugConsoleInputIcon.foreground\": \"#35a77c\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#3a94c5\",\n    \"debugIcon.breakpointDisabledForeground\": \"#f1706f\",\n    \"debugIcon.breakpointForeground\": \"#f85552\",\n    \"debugIcon.breakpointStackframeForeground\": \"#f85552\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#879686\",\n    \"debugIcon.continueForeground\": \"#3a94c5\",\n    \"debugIcon.disconnectForeground\": \"#df69ba\",\n    \"debugIcon.pauseForeground\": \"#dfa000\",\n    \"debugIcon.restartForeground\": \"#35a77c\",\n    \"debugIcon.startForeground\": \"#35a77c\",\n    \"debugIcon.stepBackForeground\": \"#3a94c5\",\n    \"debugIcon.stepIntoForeground\": \"#3a94c5\",\n    \"debugIcon.stepOutForeground\": \"#3a94c5\",\n    \"debugIcon.stepOverForeground\": \"#3a94c5\",\n    \"debugIcon.stopForeground\": \"#f85552\",\n    \"debugTokenExpression.boolean\": \"#df69ba\",\n    \"debugTokenExpression.error\": \"#f85552\",\n    \"debugTokenExpression.name\": \"#3a94c5\",\n    \"debugTokenExpression.number\": \"#df69ba\",\n    \"debugTokenExpression.string\": \"#dfa000\",\n    \"debugTokenExpression.value\": \"#8da101\",\n    \"debugToolBar.background\": \"#fdf6e3\",\n    \"descriptionForeground\": \"#939f91\",\n    \"diffEditor.diagonalFill\": \"#e0dcc7\",\n    \"diffEditor.insertedTextBackground\": \"#6ec39830\",\n    \"diffEditor.removedTextBackground\": \"#f1706f30\",\n    \"dropdown.background\": \"#fdf6e3\",\n    \"dropdown.border\": \"#e0dcc7\",\n    \"dropdown.foreground\": \"#879686\",\n    \"editor.background\": \"#fdf6e3\",\n    \"editor.findMatchBackground\": \"#f3945940\",\n    \"editor.findMatchHighlightBackground\": \"#a4bb4a40\",\n    \"editor.findRangeHighlightBackground\": \"#e6e2cc50\",\n    \"editor.foldBackground\": \"#e0dcc780\",\n    \"editor.foreground\": \"#5c6a72\",\n    \"editor.hoverHighlightBackground\": \"#e6e2cc90\",\n    \"editor.inactiveSelectionBackground\": \"#e6e2cc50\",\n    \"editor.lineHighlightBackground\": \"#efebd470\",\n    \"editor.lineHighlightBorder\": \"#e0dcc700\",\n    \"editor.rangeHighlightBackground\": \"#efebd480\",\n    \"editor.selectionBackground\": \"#e6e2cca0\",\n    \"editor.selectionHighlightBackground\": \"#e6e2cc50\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#a4bb4a40\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#fdf6e3\",\n    \"editor.snippetTabstopHighlightBackground\": \"#efebd4\",\n    \"editor.symbolHighlightBackground\": \"#6cb3c640\",\n    \"editor.wordHighlightBackground\": \"#e6e2cc48\",\n    \"editor.wordHighlightStrongBackground\": \"#e6e2cc90\",\n    \"editorBracketHighlight.foreground1\": \"#f85552\",\n    \"editorBracketHighlight.foreground2\": \"#dfa000\",\n    \"editorBracketHighlight.foreground3\": \"#8da101\",\n    \"editorBracketHighlight.foreground4\": \"#3a94c5\",\n    \"editorBracketHighlight.foreground5\": \"#f57d26\",\n    \"editorBracketHighlight.foreground6\": \"#df69ba\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#939f91\",\n    \"editorBracketMatch.background\": \"#e0dcc7\",\n    \"editorBracketMatch.border\": \"#fdf6e300\",\n    \"editorCodeLens.foreground\": \"#a4ad9ea0\",\n    \"editorCursor.foreground\": \"#5c6a72\",\n    \"editorError.background\": \"#f1706f00\",\n    \"editorError.foreground\": \"#f1706f\",\n    \"editorGhostText.background\": \"#fdf6e300\",\n    \"editorGhostText.foreground\": \"#a4ad9ea0\",\n    \"editorGroup.border\": \"#efebd4\",\n    \"editorGroup.dropBackground\": \"#e0dcc760\",\n    \"editorGroupHeader.noTabsBackground\": \"#fdf6e3\",\n    \"editorGroupHeader.tabsBackground\": \"#fdf6e3\",\n    \"editorGutter.addedBackground\": \"#a4bb4aa0\",\n    \"editorGutter.background\": \"#fdf6e300\",\n    \"editorGutter.commentRangeForeground\": \"#a4ad9e\",\n    \"editorGutter.deletedBackground\": \"#f1706fa0\",\n    \"editorGutter.modifiedBackground\": \"#6cb3c6a0\",\n    \"editorHint.foreground\": \"#e092be\",\n    \"editorHoverWidget.background\": \"#f4f0d9\",\n    \"editorHoverWidget.border\": \"#e6e2cc\",\n    \"editorIndentGuide.activeBackground\": \"#87968650\",\n    \"editorIndentGuide.background\": \"#87968620\",\n    \"editorInfo.background\": \"#6cb3c600\",\n    \"editorInfo.foreground\": \"#6cb3c6\",\n    \"editorInlayHint.background\": \"#fdf6e300\",\n    \"editorInlayHint.foreground\": \"#a4ad9ea0\",\n    \"editorInlayHint.parameterBackground\": \"#fdf6e300\",\n    \"editorInlayHint.parameterForeground\": \"#a4ad9ea0\",\n    \"editorInlayHint.typeBackground\": \"#fdf6e300\",\n    \"editorInlayHint.typeForeground\": \"#a4ad9ea0\",\n    \"editorLightBulb.foreground\": \"#dfa000\",\n    \"editorLightBulbAutoFix.foreground\": \"#35a77c\",\n    \"editorLineNumber.activeForeground\": \"#879686e0\",\n    \"editorLineNumber.foreground\": \"#a4ad9ea0\",\n    \"editorLink.activeForeground\": \"#8da101\",\n    \"editorMarkerNavigation.background\": \"#f4f0d9\",\n    \"editorMarkerNavigationError.background\": \"#f1706f80\",\n    \"editorMarkerNavigationInfo.background\": \"#6cb3c680\",\n    \"editorMarkerNavigationWarning.background\": \"#e4b64980\",\n    \"editorOverviewRuler.addedForeground\": \"#a4bb4aa0\",\n    \"editorOverviewRuler.border\": \"#fdf6e300\",\n    \"editorOverviewRuler.commonContentForeground\": \"#939f91\",\n    \"editorOverviewRuler.currentContentForeground\": \"#6cb3c6\",\n    \"editorOverviewRuler.deletedForeground\": \"#f1706fa0\",\n    \"editorOverviewRuler.errorForeground\": \"#f85552\",\n    \"editorOverviewRuler.findMatchForeground\": \"#6ec398\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#6ec398\",\n    \"editorOverviewRuler.infoForeground\": \"#df69ba\",\n    \"editorOverviewRuler.modifiedForeground\": \"#6cb3c6a0\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#6ec398\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#6ec398\",\n    \"editorOverviewRuler.warningForeground\": \"#dfa000\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#e0dcc7\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#e0dcc7\",\n    \"editorRuler.foreground\": \"#e6e2cca0\",\n    \"editorSuggestWidget.background\": \"#efebd4\",\n    \"editorSuggestWidget.border\": \"#efebd4\",\n    \"editorSuggestWidget.foreground\": \"#5c6a72\",\n    \"editorSuggestWidget.highlightForeground\": \"#8da101\",\n    \"editorSuggestWidget.selectedBackground\": \"#e6e2cc\",\n    \"editorUnnecessaryCode.border\": \"#fdf6e3\",\n    \"editorUnnecessaryCode.opacity\": \"#00000080\",\n    \"editorWarning.background\": \"#e4b64900\",\n    \"editorWarning.foreground\": \"#e4b649\",\n    \"editorWhitespace.foreground\": \"#e6e2cc\",\n    \"editorWidget.background\": \"#fdf6e3\",\n    \"editorWidget.border\": \"#e0dcc7\",\n    \"editorWidget.foreground\": \"#5c6a72\",\n    \"errorForeground\": \"#f85552\",\n    \"extensionBadge.remoteBackground\": \"#93b259\",\n    \"extensionBadge.remoteForeground\": \"#fdf6e3\",\n    \"extensionButton.prominentBackground\": \"#93b259\",\n    \"extensionButton.prominentForeground\": \"#fdf6e3\",\n    \"extensionButton.prominentHoverBackground\": \"#93b259d0\",\n    \"extensionIcon.preReleaseForeground\": \"#f57d26\",\n    \"extensionIcon.starForeground\": \"#35a77c\",\n    \"extensionIcon.verifiedForeground\": \"#8da101\",\n    \"focusBorder\": \"#fdf6e300\",\n    \"foreground\": \"#879686\",\n    \"gitDecoration.addedResourceForeground\": \"#8da101a0\",\n    \"gitDecoration.conflictingResourceForeground\": \"#df69baa0\",\n    \"gitDecoration.deletedResourceForeground\": \"#f85552a0\",\n    \"gitDecoration.ignoredResourceForeground\": \"#e0dcc7\",\n    \"gitDecoration.modifiedResourceForeground\": \"#3a94c5a0\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#35a77ca0\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#35a77ca0\",\n    \"gitDecoration.submoduleResourceForeground\": \"#f57d26a0\",\n    \"gitDecoration.untrackedResourceForeground\": \"#dfa000a0\",\n    \"gitlens.closedPullRequestIconColor\": \"#f85552\",\n    \"gitlens.decorations.addedForegroundColor\": \"#8da101\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#35a77c\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#f57d26\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#dfa000\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#f85552\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#3a94c5\",\n    \"gitlens.decorations.branchUpToDateForegroundColor\": \"#5c6a72\",\n    \"gitlens.decorations.copiedForegroundColor\": \"#df69ba\",\n    \"gitlens.decorations.deletedForegroundColor\": \"#f85552\",\n    \"gitlens.decorations.ignoredForegroundColor\": \"#879686\",\n    \"gitlens.decorations.modifiedForegroundColor\": \"#3a94c5\",\n    \"gitlens.decorations.renamedForegroundColor\": \"#df69ba\",\n    \"gitlens.decorations.untrackedForegroundColor\": \"#dfa000\",\n    \"gitlens.gutterBackgroundColor\": \"#fdf6e3\",\n    \"gitlens.gutterForegroundColor\": \"#5c6a72\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#3a94c5\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#f4f0d9\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#93b259\",\n    \"gitlens.mergedPullRequestIconColor\": \"#df69ba\",\n    \"gitlens.openPullRequestIconColor\": \"#35a77c\",\n    \"gitlens.trailingLineForegroundColor\": \"#939f91\",\n    \"gitlens.unpublishedCommitIconColor\": \"#dfa000\",\n    \"gitlens.unpulledChangesIconColor\": \"#f57d26\",\n    \"gitlens.unpushlishedChangesIconColor\": \"#3a94c5\",\n    \"icon.foreground\": \"#35a77c\",\n    \"imagePreview.border\": \"#fdf6e3\",\n    \"input.background\": \"#fdf6e300\",\n    \"input.border\": \"#e0dcc7\",\n    \"input.foreground\": \"#5c6a72\",\n    \"input.placeholderForeground\": \"#a4ad9e\",\n    \"inputOption.activeBorder\": \"#35a77c\",\n    \"inputValidation.errorBackground\": \"#f1706f\",\n    \"inputValidation.errorBorder\": \"#f85552\",\n    \"inputValidation.errorForeground\": \"#5c6a72\",\n    \"inputValidation.infoBackground\": \"#6cb3c6\",\n    \"inputValidation.infoBorder\": \"#3a94c5\",\n    \"inputValidation.infoForeground\": \"#5c6a72\",\n    \"inputValidation.warningBackground\": \"#e4b649\",\n    \"inputValidation.warningBorder\": \"#dfa000\",\n    \"inputValidation.warningForeground\": \"#5c6a72\",\n    \"issues.closed\": \"#f85552\",\n    \"issues.open\": \"#35a77c\",\n    \"keybindingLabel.background\": \"#fdf6e300\",\n    \"keybindingLabel.border\": \"#f4f0d9\",\n    \"keybindingLabel.bottomBorder\": \"#efebd4\",\n    \"keybindingLabel.foreground\": \"#5c6a72\",\n    \"keybindingTable.headerBackground\": \"#efebd4\",\n    \"keybindingTable.rowsBackground\": \"#f4f0d9\",\n    \"list.activeSelectionBackground\": \"#e6e2cc80\",\n    \"list.activeSelectionForeground\": \"#5c6a72\",\n    \"list.dropBackground\": \"#f4f0d980\",\n    \"list.errorForeground\": \"#f85552\",\n    \"list.focusBackground\": \"#e6e2cc80\",\n    \"list.focusForeground\": \"#5c6a72\",\n    \"list.highlightForeground\": \"#8da101\",\n    \"list.hoverBackground\": \"#fdf6e300\",\n    \"list.hoverForeground\": \"#5c6a72\",\n    \"list.inactiveFocusBackground\": \"#e6e2cc60\",\n    \"list.inactiveSelectionBackground\": \"#e6e2cc80\",\n    \"list.inactiveSelectionForeground\": \"#879686\",\n    \"list.invalidItemForeground\": \"#f1706f\",\n    \"list.warningForeground\": \"#dfa000\",\n    \"menu.background\": \"#fdf6e3\",\n    \"menu.foreground\": \"#879686\",\n    \"menu.selectionBackground\": \"#f4f0d9\",\n    \"menu.selectionForeground\": \"#5c6a72\",\n    \"menubar.selectionBackground\": \"#fdf6e3\",\n    \"menubar.selectionBorder\": \"#fdf6e3\",\n    \"merge.border\": \"#fdf6e300\",\n    \"merge.currentContentBackground\": \"#6cb3c640\",\n    \"merge.currentHeaderBackground\": \"#6cb3c680\",\n    \"merge.incomingContentBackground\": \"#6ec39840\",\n    \"merge.incomingHeaderBackground\": \"#6ec39880\",\n    \"minimap.errorHighlight\": \"#f1706f80\",\n    \"minimap.findMatchHighlight\": \"#6ec39860\",\n    \"minimap.selectionHighlight\": \"#e0dcc7f0\",\n    \"minimap.warningHighlight\": \"#e4b64980\",\n    \"minimapGutter.addedBackground\": \"#a4bb4aa0\",\n    \"minimapGutter.deletedBackground\": \"#f1706fa0\",\n    \"minimapGutter.modifiedBackground\": \"#6cb3c6a0\",\n    \"notebook.cellBorderColor\": \"#e0dcc7\",\n    \"notebook.cellHoverBackground\": \"#fdf6e3\",\n    \"notebook.cellStatusBarItemHoverBackground\": \"#f4f0d9\",\n    \"notebook.cellToolbarSeparator\": \"#e0dcc7\",\n    \"notebook.focusedCellBackground\": \"#fdf6e3\",\n    \"notebook.focusedCellBorder\": \"#e0dcc7\",\n    \"notebook.focusedEditorBorder\": \"#e0dcc7\",\n    \"notebook.focusedRowBorder\": \"#e0dcc7\",\n    \"notebook.inactiveFocusedCellBorder\": \"#e0dcc7\",\n    \"notebook.outputContainerBackgroundColor\": \"#f4f0d9\",\n    \"notebook.selectedCellBorder\": \"#e0dcc7\",\n    \"notebookStatusErrorIcon.foreground\": \"#f85552\",\n    \"notebookStatusRunningIcon.foreground\": \"#3a94c5\",\n    \"notebookStatusSuccessIcon.foreground\": \"#8da101\",\n    \"notificationCenterHeader.background\": \"#efebd4\",\n    \"notificationCenterHeader.foreground\": \"#5c6a72\",\n    \"notificationLink.foreground\": \"#8da101\",\n    \"notifications.background\": \"#fdf6e3\",\n    \"notifications.foreground\": \"#5c6a72\",\n    \"notificationsErrorIcon.foreground\": \"#f85552\",\n    \"notificationsInfoIcon.foreground\": \"#3a94c5\",\n    \"notificationsWarningIcon.foreground\": \"#dfa000\",\n    \"panel.background\": \"#fdf6e3\",\n    \"panel.border\": \"#fdf6e3\",\n    \"panelInput.border\": \"#e0dcc7\",\n    \"panelSection.border\": \"#efebd4\",\n    \"panelSectionHeader.background\": \"#fdf6e3\",\n    \"panelTitle.activeBorder\": \"#93b259d0\",\n    \"panelTitle.activeForeground\": \"#5c6a72\",\n    \"panelTitle.inactiveForeground\": \"#939f91\",\n    \"peekView.border\": \"#e6e2cc\",\n    \"peekViewEditor.background\": \"#f4f0d9\",\n    \"peekViewEditor.matchHighlightBackground\": \"#e4b64950\",\n    \"peekViewEditorGutter.background\": \"#f4f0d9\",\n    \"peekViewResult.background\": \"#f4f0d9\",\n    \"peekViewResult.fileForeground\": \"#5c6a72\",\n    \"peekViewResult.lineForeground\": \"#879686\",\n    \"peekViewResult.matchHighlightBackground\": \"#e4b64950\",\n    \"peekViewResult.selectionBackground\": \"#6ec39850\",\n    \"peekViewResult.selectionForeground\": \"#5c6a72\",\n    \"peekViewTitle.background\": \"#e6e2cc\",\n    \"peekViewTitleDescription.foreground\": \"#5c6a72\",\n    \"peekViewTitleLabel.foreground\": \"#8da101\",\n    \"pickerGroup.border\": \"#93b2591a\",\n    \"pickerGroup.foreground\": \"#5c6a72\",\n    \"ports.iconRunningProcessForeground\": \"#f57d26\",\n    \"problemsErrorIcon.foreground\": \"#f85552\",\n    \"problemsInfoIcon.foreground\": \"#3a94c5\",\n    \"problemsWarningIcon.foreground\": \"#dfa000\",\n    \"progressBar.background\": \"#93b259\",\n    \"quickInputTitle.background\": \"#f4f0d9\",\n    \"rust_analyzer.inlayHints.background\": \"#fdf6e300\",\n    \"rust_analyzer.inlayHints.foreground\": \"#a4ad9ea0\",\n    \"rust_analyzer.syntaxTreeBorder\": \"#f85552\",\n    \"sash.hoverBorder\": \"#e6e2cc\",\n    \"scrollbar.shadow\": \"#3c474d20\",\n    \"scrollbarSlider.activeBackground\": \"#879686\",\n    \"scrollbarSlider.background\": \"#e0dcc780\",\n    \"scrollbarSlider.hoverBackground\": \"#e0dcc7\",\n    \"selection.background\": \"#e6e2ccc0\",\n    \"settings.checkboxBackground\": \"#fdf6e3\",\n    \"settings.checkboxBorder\": \"#e0dcc7\",\n    \"settings.checkboxForeground\": \"#f57d26\",\n    \"settings.dropdownBackground\": \"#fdf6e3\",\n    \"settings.dropdownBorder\": \"#e0dcc7\",\n    \"settings.dropdownForeground\": \"#35a77c\",\n    \"settings.focusedRowBackground\": \"#f4f0d9\",\n    \"settings.headerForeground\": \"#879686\",\n    \"settings.modifiedItemIndicator\": \"#a4ad9e\",\n    \"settings.numberInputBackground\": \"#fdf6e3\",\n    \"settings.numberInputBorder\": \"#e0dcc7\",\n    \"settings.numberInputForeground\": \"#df69ba\",\n    \"settings.rowHoverBackground\": \"#f4f0d9\",\n    \"settings.textInputBackground\": \"#fdf6e3\",\n    \"settings.textInputBorder\": \"#e0dcc7\",\n    \"settings.textInputForeground\": \"#3a94c5\",\n    \"sideBar.background\": \"#fdf6e3\",\n    \"sideBar.foreground\": \"#939f91\",\n    \"sideBarSectionHeader.background\": \"#fdf6e300\",\n    \"sideBarSectionHeader.foreground\": \"#879686\",\n    \"sideBarTitle.foreground\": \"#879686\",\n    \"statusBar.background\": \"#fdf6e3\",\n    \"statusBar.border\": \"#fdf6e3\",\n    \"statusBar.debuggingBackground\": \"#fdf6e3\",\n    \"statusBar.debuggingForeground\": \"#f57d26\",\n    \"statusBar.foreground\": \"#879686\",\n    \"statusBar.noFolderBackground\": \"#fdf6e3\",\n    \"statusBar.noFolderBorder\": \"#fdf6e3\",\n    \"statusBar.noFolderForeground\": \"#879686\",\n    \"statusBarItem.activeBackground\": \"#e6e2cc70\",\n    \"statusBarItem.errorBackground\": \"#fdf6e3\",\n    \"statusBarItem.errorForeground\": \"#f85552\",\n    \"statusBarItem.hoverBackground\": \"#e6e2cca0\",\n    \"statusBarItem.prominentBackground\": \"#fdf6e3\",\n    \"statusBarItem.prominentForeground\": \"#5c6a72\",\n    \"statusBarItem.prominentHoverBackground\": \"#e6e2cca0\",\n    \"statusBarItem.remoteBackground\": \"#fdf6e3\",\n    \"statusBarItem.remoteForeground\": \"#879686\",\n    \"statusBarItem.warningBackground\": \"#fdf6e3\",\n    \"statusBarItem.warningForeground\": \"#dfa000\",\n    \"symbolIcon.arrayForeground\": \"#3a94c5\",\n    \"symbolIcon.booleanForeground\": \"#df69ba\",\n    \"symbolIcon.classForeground\": \"#dfa000\",\n    \"symbolIcon.colorForeground\": \"#5c6a72\",\n    \"symbolIcon.constantForeground\": \"#35a77c\",\n    \"symbolIcon.constructorForeground\": \"#df69ba\",\n    \"symbolIcon.enumeratorForeground\": \"#df69ba\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#35a77c\",\n    \"symbolIcon.eventForeground\": \"#dfa000\",\n    \"symbolIcon.fieldForeground\": \"#5c6a72\",\n    \"symbolIcon.fileForeground\": \"#5c6a72\",\n    \"symbolIcon.folderForeground\": \"#5c6a72\",\n    \"symbolIcon.functionForeground\": \"#8da101\",\n    \"symbolIcon.interfaceForeground\": \"#dfa000\",\n    \"symbolIcon.keyForeground\": \"#8da101\",\n    \"symbolIcon.keywordForeground\": \"#f85552\",\n    \"symbolIcon.methodForeground\": \"#8da101\",\n    \"symbolIcon.moduleForeground\": \"#df69ba\",\n    \"symbolIcon.namespaceForeground\": \"#df69ba\",\n    \"symbolIcon.nullForeground\": \"#35a77c\",\n    \"symbolIcon.numberForeground\": \"#df69ba\",\n    \"symbolIcon.objectForeground\": \"#df69ba\",\n    \"symbolIcon.operatorForeground\": \"#f57d26\",\n    \"symbolIcon.packageForeground\": \"#df69ba\",\n    \"symbolIcon.propertyForeground\": \"#35a77c\",\n    \"symbolIcon.referenceForeground\": \"#3a94c5\",\n    \"symbolIcon.snippetForeground\": \"#5c6a72\",\n    \"symbolIcon.stringForeground\": \"#8da101\",\n    \"symbolIcon.structForeground\": \"#dfa000\",\n    \"symbolIcon.textForeground\": \"#5c6a72\",\n    \"symbolIcon.typeParameterForeground\": \"#35a77c\",\n    \"symbolIcon.unitForeground\": \"#5c6a72\",\n    \"symbolIcon.variableForeground\": \"#3a94c5\",\n    \"tab.activeBackground\": \"#fdf6e3\",\n    \"tab.activeBorder\": \"#93b259d0\",\n    \"tab.activeForeground\": \"#5c6a72\",\n    \"tab.border\": \"#fdf6e3\",\n    \"tab.hoverBackground\": \"#fdf6e3\",\n    \"tab.hoverForeground\": \"#5c6a72\",\n    \"tab.inactiveBackground\": \"#fdf6e3\",\n    \"tab.inactiveForeground\": \"#a4ad9e\",\n    \"tab.lastPinnedBorder\": \"#93b259d0\",\n    \"tab.unfocusedActiveBorder\": \"#939f91\",\n    \"tab.unfocusedActiveForeground\": \"#879686\",\n    \"tab.unfocusedHoverForeground\": \"#5c6a72\",\n    \"tab.unfocusedInactiveForeground\": \"#a4ad9e\",\n    \"terminal.ansiBlack\": \"#5c6a72\",\n    \"terminal.ansiBlue\": \"#3a94c5\",\n    \"terminal.ansiBrightBlack\": \"#5c6a72\",\n    \"terminal.ansiBrightBlue\": \"#3a94c5\",\n    \"terminal.ansiBrightCyan\": \"#35a77c\",\n    \"terminal.ansiBrightGreen\": \"#8da101\",\n    \"terminal.ansiBrightMagenta\": \"#df69ba\",\n    \"terminal.ansiBrightRed\": \"#f85552\",\n    \"terminal.ansiBrightWhite\": \"#f4f0d9\",\n    \"terminal.ansiBrightYellow\": \"#dfa000\",\n    \"terminal.ansiCyan\": \"#35a77c\",\n    \"terminal.ansiGreen\": \"#8da101\",\n    \"terminal.ansiMagenta\": \"#df69ba\",\n    \"terminal.ansiRed\": \"#f85552\",\n    \"terminal.ansiWhite\": \"#939f91\",\n    \"terminal.ansiYellow\": \"#dfa000\",\n    \"terminal.foreground\": \"#5c6a72\",\n    \"terminalCursor.foreground\": \"#5c6a72\",\n    \"testing.iconErrored\": \"#f85552\",\n    \"testing.iconFailed\": \"#f85552\",\n    \"testing.iconPassed\": \"#35a77c\",\n    \"testing.iconQueued\": \"#3a94c5\",\n    \"testing.iconSkipped\": \"#df69ba\",\n    \"testing.iconUnset\": \"#dfa000\",\n    \"testing.runAction\": \"#35a77c\",\n    \"textBlockQuote.background\": \"#f4f0d9\",\n    \"textBlockQuote.border\": \"#e6e2cc\",\n    \"textCodeBlock.background\": \"#f4f0d9\",\n    \"textLink.activeForeground\": \"#8da101c0\",\n    \"textLink.foreground\": \"#8da101\",\n    \"textPreformat.foreground\": \"#dfa000\",\n    \"titleBar.activeBackground\": \"#fdf6e3\",\n    \"titleBar.activeForeground\": \"#879686\",\n    \"titleBar.border\": \"#fdf6e3\",\n    \"titleBar.inactiveBackground\": \"#fdf6e3\",\n    \"titleBar.inactiveForeground\": \"#a4ad9e\",\n    \"toolbar.hoverBackground\": \"#f4f0d9\",\n    \"tree.indentGuidesStroke\": \"#a4ad9e\",\n    \"walkThrough.embeddedEditorBackground\": \"#f4f0d9\",\n    \"welcomePage.buttonBackground\": \"#f4f0d9\",\n    \"welcomePage.buttonHoverBackground\": \"#f4f0d9a0\",\n    \"welcomePage.progress.foreground\": \"#8da101\",\n    \"welcomePage.tileHoverBackground\": \"#f4f0d9\",\n    \"widget.shadow\": \"#3c474d20\"\n  },\n  \"displayName\": \"Everforest Light\",\n  \"name\": \"everforest-light\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"class:python\": \"#35a77c\",\n    \"class:typescript\": \"#35a77c\",\n    \"class:typescriptreact\": \"#35a77c\",\n    \"enum:typescript\": \"#df69ba\",\n    \"enum:typescriptreact\": \"#df69ba\",\n    \"enumMember:typescript\": \"#3a94c5\",\n    \"enumMember:typescriptreact\": \"#3a94c5\",\n    \"interface:typescript\": \"#35a77c\",\n    \"interface:typescriptreact\": \"#35a77c\",\n    \"intrinsic:python\": \"#df69ba\",\n    \"macro:rust\": \"#35a77c\",\n    \"memberOperatorOverload\": \"#f57d26\",\n    \"module:python\": \"#3a94c5\",\n    \"namespace:rust\": \"#df69ba\",\n    \"namespace:typescript\": \"#df69ba\",\n    \"namespace:typescriptreact\": \"#df69ba\",\n    \"operatorOverload\": \"#f57d26\",\n    \"property.defaultLibrary:javascript\": \"#df69ba\",\n    \"property.defaultLibrary:javascriptreact\": \"#df69ba\",\n    \"property.defaultLibrary:typescript\": \"#df69ba\",\n    \"property.defaultLibrary:typescriptreact\": \"#df69ba\",\n    \"selfKeyword:rust\": \"#df69ba\",\n    \"variable.defaultLibrary:javascript\": \"#df69ba\",\n    \"variable.defaultLibrary:javascriptreact\": \"#df69ba\",\n    \"variable.defaultLibrary:typescript\": \"#df69ba\",\n    \"variable.defaultLibrary:typescriptreact\": \"#df69ba\"\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": \"keyword, storage.type.function, storage.type.class, storage.type.enum, storage.type.interface, storage.type.property, keyword.operator.new, keyword.operator.expression, keyword.operator.new, keyword.operator.delete, storage.type.extends\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.debugger\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage, modifier, keyword.var, entity.name.tag, keyword.control.case, keyword.control.switch\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"string, punctuation.definition.string.end, punctuation.definition.string.begin, punctuation.definition.string.template.begin, punctuation.definition.string.template.end\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape, punctuation.quasi.element, punctuation.definition.template-expression, punctuation.section.embedded, storage.type.format, constant.other.placeholder, constant.other.placeholder, variable.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, support.function, meta.function, meta.function-call, meta.definition.method\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule, keyword.control.import, keyword.control.export, storage.type.namespace, punctuation.decorator, keyword.control.directive, keyword.preprocessor, punctuation.definition.preprocessor, punctuation.definition.directive, keyword.other.import, keyword.other.package, entity.name.type.namespace, entity.name.scope-resolution, keyword.other.using, keyword.package, keyword.import, keyword.map\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.annotation\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.label, constant.other.label\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"support.module, support.node, support.other.module, support.type.object.module, entity.name.type.module, entity.name.type.class.module, keyword.control.module\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type, support.type, entity.name.type, keyword.type\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.class, support.class, entity.name.class, entity.other.inherited-class, storage.class\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.this, variable.language.self, variable.language.super, keyword.other.this, variable.language.special, constant.language.null, constant.language.undefined, constant.language.nan\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"constant.language, support.constant\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"variable, support.variable, meta.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property, support.variable.property, variable.other.property, variable.other.object.property, variable.other.enummember, variable.other.member, meta.object-literal.key\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation, meta.brace, meta.delimiter, meta.bracket\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"heading.1.markdown, markup.heading.setext.1.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"heading.2.markdown, markup.heading.setext.2.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"heading.3.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"heading.4.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"heading.5.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"heading.6.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown, constant.other.reference.link.markdown, string.other.link.description.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link.image.markdown, markup.underline.link.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin.markdown, punctuation.definition.string.end.markdown, punctuation.definition.italic.markdown, punctuation.definition.quote.begin.markdown, punctuation.definition.metadata.markdown, punctuation.separator.key-value.markdown, punctuation.definition.constant.markdown\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown, punctuation.definition.constant.begin.markdown, punctuation.definition.constant.end.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold markup.italic, markup.italic markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"italic bold\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.markdown, punctuation.definition.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"markup.fenced_code.block.markdown, markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.restructuredtext\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.field.restructuredtext, punctuation.separator.key-value.restructuredtext, punctuation.definition.directive.restructuredtext, punctuation.definition.constant.restructuredtext, punctuation.definition.italic.restructuredtext, punctuation.definition.table.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.restructuredtext\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.restructuredtext, punctuation.definition.link.restructuredtext, punctuation.definition.raw.restructuredtext, punctuation.section.raw.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.footnote.link.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"support.directive.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.directive.restructuredtext, markup.raw.restructuredtext, markup.raw.inner.restructuredtext, string.other.link.title.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.function.latex, punctuation.definition.function.tex, punctuation.definition.keyword.latex, constant.character.newline.tex, punctuation.definition.keyword.tex\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"support.function.be.latex\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"support.function.section.latex, keyword.control.table.cell.latex, keyword.control.table.newline.latex\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"support.class.latex, variable.parameter.latex, variable.parameter.function.latex, variable.parameter.definition.label.latex, constant.other.reference.label.latex\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.preamble.latex\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.namespace.xml\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.html, entity.name.tag.xml, entity.name.tag.localname.xml\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.html, entity.other.attribute-name.xml, entity.other.attribute-name.localname.xml\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.html, string.quoted.single.html, punctuation.definition.string.begin.html, punctuation.definition.string.end.html, punctuation.separator.key-value.html, punctuation.definition.string.begin.xml, punctuation.definition.string.end.xml, string.quoted.double.xml, string.quoted.single.xml, punctuation.definition.tag.begin.html, punctuation.definition.tag.end.html, punctuation.definition.tag.xml, meta.tag.xml, meta.tag.preprocessor.xml, meta.tag.other.html, meta.tag.block.any.html, meta.tag.inline.any.html\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.documentroot.xml, meta.tag.sgml.doctype.xml\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.proto\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.proto.syntax, string.quoted.single.proto.syntax, string.quoted.double.proto, string.quoted.single.proto\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.proto, entity.name.class.message.proto\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.css, punctuation.separator.key-value.css, punctuation.terminator.rule.css, punctuation.separator.list.comma.css\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.css\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.pseudo-class.css, entity.other.attribute-name.pseudo-element.css\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.css, string.quoted.double.css, support.constant.property-value.css, meta.property-value.css, punctuation.definition.string.begin.css, punctuation.definition.string.end.css, constant.numeric.css, support.constant.font-name.css, variable.parameter.keyframe-list.css\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"support.type.vendored.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.css, entity.other.keyframe-offset.css, punctuation.definition.keyword.css, keyword.control.at-rule.keyframes.css, meta.selector.css\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.scss, punctuation.separator.key-value.scss, punctuation.terminator.rule.scss, punctuation.separator.list.comma.scss\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule.keyframes.scss\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.interpolation.begin.bracket.curly.scss, punctuation.definition.interpolation.end.bracket.curly.scss\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin.scss, punctuation.definition.string.end.scss, string.quoted.double.scss, string.quoted.single.scss, constant.character.css.sass, meta.property-value.scss\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule.include.scss, keyword.control.at-rule.use.scss, keyword.control.at-rule.mixin.scss, keyword.control.at-rule.extend.scss, keyword.control.at-rule.import.scss\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.stylus\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.stylus\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.js\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.accessor.js, punctuation.separator.key-value.js, punctuation.separator.label.js, keyword.operator.accessor.js\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.block.tag.jsdoc\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.js, storage.type.function.arrow.js\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"JSXNested\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.jsx, entity.other.attribute-name.jsx, punctuation.definition.tag.begin.js.jsx, punctuation.definition.tag.end.js.jsx, entity.other.attribute-name.js.jsx\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.ts\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.annotation.ts, punctuation.accessor.ts, punctuation.separator.key-value.ts\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.directive.ts, entity.other.attribute-name.directive.ts\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.ts, entity.name.type.interface.ts, entity.other.inherited-class.ts, entity.name.type.alias.ts, entity.name.type.class.ts, entity.name.type.enum.ts\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.ts, storage.type.function.arrow.ts, storage.type.type.ts\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.ts\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.ts, keyword.control.export.ts, storage.type.namespace.ts\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.tsx\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.annotation.tsx, punctuation.accessor.tsx, punctuation.separator.key-value.tsx\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.directive.tsx, entity.other.attribute-name.directive.tsx, punctuation.definition.tag.begin.tsx, punctuation.definition.tag.end.tsx, entity.other.attribute-name.tsx\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.tsx, entity.name.type.interface.tsx, entity.other.inherited-class.tsx, entity.name.type.alias.tsx, entity.name.type.class.tsx, entity.name.type.enum.tsx\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.tsx\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.tsx, keyword.control.export.tsx, storage.type.namespace.tsx\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.tsx, storage.type.function.arrow.tsx, storage.type.type.tsx, support.class.component.tsx\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.coffee\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"meta.type-signature.purescript\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.double-colon.purescript, keyword.other.arrow.purescript, keyword.other.big-arrow.purescript\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.purescript\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.purescript, string.quoted.double.purescript, punctuation.definition.string.begin.purescript, punctuation.definition.string.end.purescript, string.quoted.triple.purescript, entity.name.type.purescript\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"support.other.module.purescript\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.dot.dart\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.primitive.dart\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"support.class.dart\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.dart, string.interpolated.single.dart, string.interpolated.double.dart\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.dart\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.dart, storage.type.annotation.dart\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.pug\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.pug\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.tag.pug\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.pug, storage.type.import.include.pug\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.c, storage.modifier.array.bracket.square.c, meta.function.definition.parameters.c\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.dot-access.c, constant.character.escape.line-continuation.c\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.include.c, punctuation.definition.directive.c, keyword.control.directive.pragma.c, keyword.control.directive.line.c, keyword.control.directive.define.c, keyword.control.directive.conditional.c, keyword.control.directive.diagnostic.error.c, keyword.control.directive.undef.c, keyword.control.directive.conditional.ifdef.c, keyword.control.directive.endif.c, keyword.control.directive.conditional.ifndef.c, keyword.control.directive.conditional.if.c, keyword.control.directive.else.c\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.pointer-access.c\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.c\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.cpp, storage.modifier.array.bracket.square.cpp, meta.function.definition.parameters.cpp, meta.body.function.definition.cpp\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.dot-access.cpp, constant.character.escape.line-continuation.cpp\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.include.cpp, punctuation.definition.directive.cpp, keyword.control.directive.pragma.cpp, keyword.control.directive.line.cpp, keyword.control.directive.define.cpp, keyword.control.directive.conditional.cpp, keyword.control.directive.diagnostic.error.cpp, keyword.control.directive.undef.cpp, keyword.control.directive.conditional.ifdef.cpp, keyword.control.directive.endif.cpp, keyword.control.directive.conditional.ifndef.cpp, keyword.control.directive.conditional.if.cpp, keyword.control.directive.else.cpp, storage.type.namespace.definition.cpp, keyword.other.using.directive.cpp, storage.type.struct.cpp\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.pointer-access.cpp, punctuation.section.angle-brackets.begin.template.call.cpp, punctuation.section.angle-brackets.end.template.call.cpp\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.cpp\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.using.cs\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.cs, constant.character.escape.cs, punctuation.definition.interpolation.begin.cs, punctuation.definition.interpolation.end.cs\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.cs, string.quoted.single.cs, punctuation.definition.string.begin.cs, punctuation.definition.string.end.cs\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.object.property.cs\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.namespace.cs\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"keyword.symbol.fsharp, constant.language.unit.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.format.specifier.fsharp, entity.name.type.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.fsharp, string.quoted.single.fsharp, punctuation.definition.string.begin.fsharp, punctuation.definition.string.end.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"support.function.attribute.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.java, punctuation.separator.period.java\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.java, keyword.other.package.java\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.arrow.java, keyword.control.ternary.java\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.property.java\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.wildcard.java, storage.modifier.import.java, storage.type.annotation.java, punctuation.definition.annotation.java, storage.modifier.package.java, entity.name.type.module.java\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.kotlin, storage.type.annotation.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.scala\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.scala\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.import.scala\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.scala, string.quoted.single.scala, punctuation.definition.string.begin.scala, punctuation.definition.string.end.scala, string.quoted.double.interpolated.scala, string.quoted.single.interpolated.scala, string.quoted.triple.scala\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class, entity.other.inherited-class.scala\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"keyword.declaration.stable.scala, keyword.other.arrow.scala\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.scala\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.navigation.groovy, meta.method.body.java, meta.definition.method.groovy, meta.definition.method.signature.java\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.groovy\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.groovy, keyword.other.package.groovy, keyword.other.import.static.groovy\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.def.groovy\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.interpolated.groovy, meta.method.groovy\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.groovy, storage.modifier.package.groovy\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.annotation.groovy\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.go\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.go\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.import.go, keyword.package.go\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.mod.rust\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.path.rust, keyword.operator.member-access.rust\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.core.rust\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"meta.attribute.rust, variable.language.rust, storage.type.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.swift, support.function.any-method.swift\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.swift\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.class.php\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.trait.php\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php, support.other.namespace.php\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.modifier.access.control.public.cpp, storage.type.modifier.access.control.private.cpp\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.include.php, storage.type.php\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.arguments.python\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.decorator.python, punctuation.separator.period.python\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.python, keyword.control.import.from.python\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.lua\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.lua\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.method.with-arguments.ruby\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.method.ruby\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.pseudo-method.ruby, storage.type.variable.ruby\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.special-method.ruby\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.module.ruby, punctuation.definition.constant.ruby\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.character-class.ruby,string.regexp.interpolated.ruby,punctuation.definition.character-class.ruby,string.regexp.group.ruby, punctuation.section.regexp.ruby, punctuation.definition.group.ruby\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant.ruby\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.arrow.haskell, keyword.other.big-arrow.haskell, keyword.other.double-colon.haskell\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.haskell, string.quoted.double.haskell, string.quoted.single.haskell, punctuation.definition.string.begin.haskell, punctuation.definition.string.end.haskell\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.haskell\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace, meta.preprocessor.haskell\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.julia, keyword.control.export.julia\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.storage.modifier.julia\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.period.elm\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.elm\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.r\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.r, variable.function.r\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.r\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.namespace.r\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.module-function.erlang, punctuation.section.directive.begin.erlang\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.erlang, keyword.control.directive.define.erlang\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.class.module.erlang\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.erlang, string.quoted.single.erlang, punctuation.definition.string.begin.erlang, punctuation.definition.string.end.erlang\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.export.erlang, keyword.control.directive.module.erlang, keyword.control.directive.import.erlang, keyword.control.directive.behaviour.erlang\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite.module.elixir, punctuation.definition.variable.elixir\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.elixir\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.module.elixir\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.value-signature.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.variant.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.sub.perl, storage.type.declare.routine.perl\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.lisp\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function-type.lisp\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"keyword.constant.lisp\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.lisp\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword.clojure, support.variable.clojure, meta.definition.variable.clojure\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.global.clojure\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.clojure\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"meta.scope.if-block.shell, meta.scope.group.shell\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.shell, entity.name.function.shell\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell, string.quoted.single.shell, punctuation.definition.string.begin.shell, punctuation.definition.string.end.shell, string.unquoted.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.heredoc-token.shell, variable.other.normal.shell, punctuation.definition.variable.shell, variable.other.special.shell, variable.other.positional.shell, variable.other.bracket.shell\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.fish\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"support.function.unix.fish\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.normal.fish, punctuation.definition.variable.fish, variable.other.fixed.fish, variable.other.special.fish\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.fish, punctuation.definition.string.end.fish, punctuation.definition.string.begin.fish, string.quoted.single.fish\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape.single.fish\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.variable.powershell\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.powershell, support.function.attribute.powershell, support.function.powershell\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.powershell, string.quoted.double.powershell, punctuation.definition.string.begin.powershell, punctuation.definition.string.end.powershell, string.quoted.double.heredoc.powershell\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.powershell\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#5c6a72\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.graphql\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.fragment.graphql\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.target.makefile\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.makefile\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"meta.scope.prerequisites.makefile\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"string.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"storage.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.map.viml\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.map.viml\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.map.viml, constant.character.map.key.viml\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.map.special.viml\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.tmux, constant.numeric.tmux\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.package-manager.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.flag.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.dockerfile, string.quoted.single.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.base-image.dockerfile, entity.name.image.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.separator.diff\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff, punctuation.definition.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range.context, punctuation.definition.range.diff\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header.from-file\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff, punctuation.definition.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff, punctuation.definition.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.from-file.diff\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.group-title.ini, punctuation.definition.entity.ini\",\n      \"settings\": {\n        \"foreground\": \"#f85552\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value.ini\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.ini, string.quoted.single.ini, punctuation.definition.string.begin.ini, punctuation.definition.string.end.ini\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.definition.ini\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"support.function.aggregate.sql\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.sql, punctuation.definition.string.end.sql, punctuation.definition.string.begin.sql, string.quoted.double.sql\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"support.type.graphql\",\n      \"settings\": {\n        \"foreground\": \"#dfa000\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.graphql\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.support.type.property-name.begin.json, punctuation.support.type.property-name.end.json, punctuation.separator.dictionary.key-value.json, punctuation.definition.string.begin.json, punctuation.definition.string.end.json, punctuation.separator.dictionary.pair.json, punctuation.separator.array.json\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.json\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value.mapping.yaml\",\n      \"settings\": {\n        \"foreground\": \"#939f91\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.plain.out.yaml, string.quoted.single.yaml, string.quoted.double.yaml, punctuation.definition.string.begin.yaml, punctuation.definition.string.end.yaml, string.unquoted.plain.in.yaml, string.unquoted.block.yaml\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.anchor.yaml, punctuation.definition.block.sequence.item.yaml\",\n      \"settings\": {\n        \"foreground\": \"#35a77c\"\n      }\n    },\n    {\n      \"scope\": \"keyword.key.toml\",\n      \"settings\": {\n        \"foreground\": \"#f57d26\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.basic.line.toml, string.quoted.single.literal.line.toml, punctuation.definition.keyValuePair.toml\",\n      \"settings\": {\n        \"foreground\": \"#8da101\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.boolean.toml\",\n      \"settings\": {\n        \"foreground\": \"#3a94c5\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.table.toml, punctuation.definition.table.toml, entity.other.attribute-name.table.array.toml, punctuation.definition.table.array.toml\",\n      \"settings\": {\n        \"foreground\": \"#df69ba\"\n      }\n    },\n    {\n      \"scope\": \"comment, string.comment, punctuation.definition.comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#939f91\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { everforestLight as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,kBAAkB,OAAO,MAAM,CAAC;IAClC,UAAU;QACR,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,iDAAiD;QACjD,6CAA6C;QAC7C,4CAA4C;QAC5C,oCAAoC;QACpC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,mCAAmC;QACnC,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,8BAA8B;QAC9B,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,gCAAgC;QAChC,iCAAiC;QACjC,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,mBAAmB;QACnB,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,sCAAsC;QACtC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,sCAAsC;QACtC,4CAA4C;QAC5C,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,qDAAqD;QACrD,6CAA6C;QAC7C,8CAA8C;QAC9C,8CAA8C;QAC9C,+CAA+C;QAC/C,8CAA8C;QAC9C,gDAAgD;QAChD,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,oCAAoC;QACpC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,wCAAwC;QACxC,mBAAmB;QACnB,uBAAuB;QACvB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,eAAe;QACf,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,0BAA0B;QAC1B,mBAAmB;QACnB,mBAAmB;QACnB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,gBAAgB;QAChB,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,4BAA4B;QAC5B,gCAAgC;QAChC,6CAA6C;QAC7C,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,6BAA6B;QAC7B,sCAAsC;QACtC,2CAA2C;QAC3C,+BAA+B;QAC/B,sCAAsC;QACtC,wCAAwC;QACxC,wCAAwC;QACxC,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,uBAAuB;QACvB,iCAAiC;QACjC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sBAAsB;QACtB,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,6BAA6B;QAC7B,iCAAiC;QACjC,gCAAgC;QAChC,mCAAmC;QACnC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,uBAAuB;QACvB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,qBAAqB;QACrB,qBAAqB;QACrB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,wCAAwC;QACxC,gCAAgC;QAChC,qCAAqC;QACrC,mCAAmC;QACnC,mCAAmC;QACnC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,gBAAgB;QAChB,oBAAoB;QACpB,yBAAyB;QACzB,mBAAmB;QACnB,wBAAwB;QACxB,yBAAyB;QACzB,8BAA8B;QAC9B,wBAAwB;QACxB,6BAA6B;QAC7B,oBAAoB;QACpB,cAAc;QACd,0BAA0B;QAC1B,iBAAiB;QACjB,kBAAkB;QAClB,wBAAwB;QACxB,6BAA6B;QAC7B,oBAAoB;QACpB,sCAAsC;QACtC,2CAA2C;QAC3C,sCAAsC;QACtC,2CAA2C;QAC3C,oBAAoB;QACpB,sCAAsC;QACtC,2CAA2C;QAC3C,sCAAsC;QACtC,2CAA2C;IAC7C;IACA,eAAe;QACb;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}