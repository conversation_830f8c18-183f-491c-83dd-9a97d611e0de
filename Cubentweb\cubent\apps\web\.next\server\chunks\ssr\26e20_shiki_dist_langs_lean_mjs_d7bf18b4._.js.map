{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/lean.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Lean 4\", \"fileTypes\": [], \"name\": \"lean\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(Prop|Type|Sort)\\\\b\", \"name\": \"storage.type.lean4\" }, { \"match\": \"\\\\battribute\\\\b\\\\s*\\\\[[^\\\\]]*\\\\]\", \"name\": \"storage.modifier.lean4\" }, { \"match\": \"@\\\\[[^\\\\]]*\\\\]\", \"name\": \"storage.modifier.lean4\" }, { \"match\": \"\\\\b(?<!\\\\.)(global|local|scoped|partial|unsafe|private|protected|noncomputable)(?!\\\\.)\\\\b\", \"name\": \"storage.modifier.lean4\" }, { \"match\": \"\\\\b(sorry|admit|stop)\\\\b\", \"name\": \"invalid.illegal.lean4\" }, { \"match\": \"#(print|eval|reduce|check|check_failure)\\\\b\", \"name\": \"keyword.other.lean4\" }, { \"match\": \"\\\\bderiving\\\\s+instance\\\\b\", \"name\": \"keyword.other.command.lean4\" }, { \"begin\": \"\\\\b(?<!\\\\.)(inductive|coinductive|structure|theorem|axiom|abbrev|lemma|def|instance|class|constant)\\\\b\\\\s+(\\\\{[^}]*\\\\})?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.definitioncommand.lean4\" } }, \"end\": \"(?=\\\\bwith\\\\b|\\\\bextends\\\\b|\\\\bwhere\\\\b|[:\\\\|(\\\\[{\\u2983<>])\", \"name\": \"meta.definitioncommand.lean4\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#definitionName\" }, { \"match\": \",\" }] }, { \"match\": \"\\\\b(?<!\\\\.)(theorem|show|have|from|suffices|nomatch|def|class|structure|instance|set_option|initialize|builtin_initialize|example|inductive|coinductive|axiom|constant|universe|universes|variable|variables|import|open|export|theory|prelude|renaming|hiding|exposing|do|by|let|extends|mutual|mut|where|rec|syntax|macro_rules|macro|deriving|fun|section|namespace|end|infix|infixl|infixr|postfix|prefix|notation|abbrev|if|then|else|calc|match|with|for|in|unless|try|catch|finally|return|continue|break)(?!\\\\.)\\\\b\", \"name\": \"keyword.other.lean4\" }, { \"begin\": \"\\xAB\", \"contentName\": \"entity.name.lean4\", \"end\": \"\\xBB\" }, { \"begin\": '(s!)\"', \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.lean4\" } }, \"end\": '\"', \"name\": \"string.interpolated.lean4\", \"patterns\": [{ \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.lean4\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.lean4\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": `\\\\\\\\[\\\\\\\\\"ntr']`, \"name\": \"constant.character.escape.lean4\" }, { \"match\": \"\\\\\\\\x[0-9A-Fa-f][0-9A-Fa-f]\", \"name\": \"constant.character.escape.lean4\" }, { \"match\": \"\\\\\\\\u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]\", \"name\": \"constant.character.escape.lean4\" }] }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.lean4\", \"patterns\": [{ \"match\": `\\\\\\\\[\\\\\\\\\"ntr']`, \"name\": \"constant.character.escape.lean4\" }, { \"match\": \"\\\\\\\\x[0-9A-Fa-f][0-9A-Fa-f]\", \"name\": \"constant.character.escape.lean4\" }, { \"match\": \"\\\\\\\\u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]\", \"name\": \"constant.character.escape.lean4\" }] }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.lean4\" }, { \"match\": \"'[^\\\\\\\\']'\", \"name\": \"string.quoted.single.lean4\" }, { \"captures\": { \"1\": { \"name\": \"constant.character.escape.lean4\" } }, \"match\": \"'(\\\\\\\\(x[0-9A-Fa-f][0-9A-Fa-f]|u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]|.))'\", \"name\": \"string.quoted.single.lean4\" }, { \"match\": \"`+[^\\\\[(]\\\\S+\", \"name\": \"entity.name.lean4\" }, { \"match\": \"\\\\b(\\\\d+|0([xX][0-9a-fA-F]+)|[-]?(0|[1-9]\\\\d*)(\\\\.\\\\d+)?([eE][+-]?\\\\d+)?)\\\\b\", \"name\": \"constant.numeric.lean4\" }], \"repository\": { \"blockComment\": { \"begin\": \"/-\", \"end\": \"-/\", \"name\": \"comment.block.lean4\", \"patterns\": [{ \"include\": \"source.lean4.markdown\" }, { \"include\": \"#blockComment\" }] }, \"comments\": { \"patterns\": [{ \"include\": \"#dashComment\" }, { \"include\": \"#docComment\" }, { \"include\": \"#stringBlock\" }, { \"include\": \"#modDocComment\" }, { \"include\": \"#blockComment\" }] }, \"dashComment\": { \"begin\": \"--\", \"end\": \"$\", \"name\": \"comment.line.double-dash.lean4\", \"patterns\": [{ \"include\": \"source.lean4.markdown\" }] }, \"definitionName\": { \"patterns\": [{ \"match\": \"\\\\b[^:\\xAB\\xBB(){}\\\\s=\\u2192\\u03BB\\u2200?][^:\\xAB\\xBB(){}\\\\s]*\", \"name\": \"entity.name.function.lean4\" }, { \"begin\": \"\\xAB\", \"contentName\": \"entity.name.function.lean4\", \"end\": \"\\xBB\" }] }, \"docComment\": { \"begin\": \"/--\", \"end\": \"-/\", \"name\": \"comment.block.documentation.lean4\", \"patterns\": [{ \"include\": \"source.lean4.markdown\" }, { \"include\": \"#blockComment\" }] }, \"modDocComment\": { \"begin\": \"/-!\", \"end\": \"-/\", \"name\": \"comment.block.documentation.lean4\", \"patterns\": [{ \"include\": \"source.lean4.markdown\" }, { \"include\": \"#blockComment\" }] } }, \"scopeName\": \"source.lean4\", \"aliases\": [\"lean4\"] });\nvar lean = [\n  lang\n];\n\nexport { lean as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa,EAAE;IAAE,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAA0B,QAAQ;QAAqB;QAAG;YAAE,SAAS;YAAoC,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAAkB,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAA6F,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAA4B,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAA+C,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAA8B,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAA4H,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAgE,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;gBAAI;aAAE;QAAC;QAAG;YAAE,SAAS;YAA+f,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAQ,eAAe;YAAqB,OAAO;QAAO;QAAG;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAK,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,eAAe,CAAC;oBAAE,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAqD,QAAQ;gBAAkC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,SAAS,CAAC,eAAe,CAAC;oBAAE,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAqD,QAAQ;gBAAkC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAc,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;YAAqF,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAAiB,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAAgF,QAAQ;QAAyB;KAAE;IAAE,cAAc;QAAE,gBAAgB;YAAE,SAAS;YAAM,OAAO;YAAM,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkE,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAQ,eAAe;oBAA8B,OAAO;gBAAO;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,OAAO;YAAM,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,OAAO;YAAM,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,WAAW;QAAC;KAAQ;AAAC;AAC30I,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}