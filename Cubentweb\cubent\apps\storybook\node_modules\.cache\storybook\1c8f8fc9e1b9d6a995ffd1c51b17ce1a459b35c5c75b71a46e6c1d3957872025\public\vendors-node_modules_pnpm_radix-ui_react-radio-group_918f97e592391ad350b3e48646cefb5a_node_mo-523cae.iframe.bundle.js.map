{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-radio-group_918f97e592391ad350b3e48646cefb5a_node_mo-523cae.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;;;;;;;;;;;;;;;;;AC9SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-radio-group_918f97e592391ad350b3e48646cefb5a/node_modules/@radix-ui/react-radio-group/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-previou_791288859aab8756064fe392350c2e0c/node_modules/@radix-ui/react-use-previous/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/radio-group.tsx\nimport * as React2 from \"react\";\nimport { composeEventHandlers as composeEventHandlers2 } from \"@radix-ui/primitive\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { Primitive as Primitive2 } from \"@radix-ui/react-primitive\";\nimport * as RovingFocusGroup from \"@radix-ui/react-roving-focus\";\nimport { createRovingFocusGroupScope } from \"@radix-ui/react-roving-focus\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\n\n// src/radio.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar RADIO_NAME = \"Radio\";\nvar [createRadioContext, createRadioScope] = createContextScope(RADIO_NAME);\nvar [RadioProvider, useRadioContext] = createRadioContext(RADIO_NAME);\nvar Radio = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRadio,\n      name,\n      checked = false,\n      required,\n      disabled,\n      value = \"on\",\n      onCheck,\n      form,\n      ...radioProps\n    } = props;\n    const [button, setButton] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    return /* @__PURE__ */ jsxs(RadioProvider, { scope: __scopeRadio, checked, disabled, children: [\n      /* @__PURE__ */ jsx(\n        Primitive.button,\n        {\n          type: \"button\",\n          role: \"radio\",\n          \"aria-checked\": checked,\n          \"data-state\": getState(checked),\n          \"data-disabled\": disabled ? \"\" : void 0,\n          disabled,\n          value,\n          ...radioProps,\n          ref: composedRefs,\n          onClick: composeEventHandlers(props.onClick, (event) => {\n            if (!checked) onCheck?.();\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })\n        }\n      ),\n      isFormControl && /* @__PURE__ */ jsx(\n        RadioBubbleInput,\n        {\n          control: button,\n          bubbles: !hasConsumerStoppedPropagationRef.current,\n          name,\n          value,\n          checked,\n          required,\n          disabled,\n          form,\n          style: { transform: \"translateX(-100%)\" }\n        }\n      )\n    ] });\n  }\n);\nRadio.displayName = RADIO_NAME;\nvar INDICATOR_NAME = \"RadioIndicator\";\nvar RadioIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.checked, children: /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...indicatorProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nRadioIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar RadioBubbleInput = React.forwardRef(\n  ({\n    __scopeRadio,\n    control,\n    checked,\n    bubbles = true,\n    ...props\n  }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        \"checked\"\n      );\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event(\"click\", { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n    return /* @__PURE__ */ jsx(\n      Primitive.input,\n      {\n        type: \"radio\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          ...controlSize,\n          position: \"absolute\",\n          pointerEvents: \"none\",\n          opacity: 0,\n          margin: 0\n        }\n      }\n    );\n  }\n);\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n  return checked ? \"checked\" : \"unchecked\";\n}\n\n// src/radio-group.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ARROW_KEYS = [\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"];\nvar RADIO_GROUP_NAME = \"RadioGroup\";\nvar [createRadioGroupContext, createRadioGroupScope] = createContextScope2(RADIO_GROUP_NAME, [\n  createRovingFocusGroupScope,\n  createRadioScope\n]);\nvar useRovingFocusGroupScope = createRovingFocusGroupScope();\nvar useRadioScope = createRadioScope();\nvar [RadioGroupProvider, useRadioGroupContext] = createRadioGroupContext(RADIO_GROUP_NAME);\nvar RadioGroup = React2.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRadioGroup,\n      name,\n      defaultValue,\n      value: valueProp,\n      required = false,\n      disabled = false,\n      orientation,\n      dir,\n      loop = true,\n      onValueChange,\n      ...groupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? null,\n      onChange: onValueChange,\n      caller: RADIO_GROUP_NAME\n    });\n    return /* @__PURE__ */ jsx2(\n      RadioGroupProvider,\n      {\n        scope: __scopeRadioGroup,\n        name,\n        required,\n        disabled,\n        value,\n        onValueChange: setValue,\n        children: /* @__PURE__ */ jsx2(\n          RovingFocusGroup.Root,\n          {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            orientation,\n            dir: direction,\n            loop,\n            children: /* @__PURE__ */ jsx2(\n              Primitive2.div,\n              {\n                role: \"radiogroup\",\n                \"aria-required\": required,\n                \"aria-orientation\": orientation,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                dir: direction,\n                ...groupProps,\n                ref: forwardedRef\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nRadioGroup.displayName = RADIO_GROUP_NAME;\nvar ITEM_NAME = \"RadioGroupItem\";\nvar RadioGroupItem = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = React2.useRef(null);\n    const composedRefs = useComposedRefs2(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = React2.useRef(false);\n    React2.useEffect(() => {\n      const handleKeyDown = (event) => {\n        if (ARROW_KEYS.includes(event.key)) {\n          isArrowKeyPressedRef.current = true;\n        }\n      };\n      const handleKeyUp = () => isArrowKeyPressedRef.current = false;\n      document.addEventListener(\"keydown\", handleKeyDown);\n      document.addEventListener(\"keyup\", handleKeyUp);\n      return () => {\n        document.removeEventListener(\"keydown\", handleKeyDown);\n        document.removeEventListener(\"keyup\", handleKeyUp);\n      };\n    }, []);\n    return /* @__PURE__ */ jsx2(\n      RovingFocusGroup.Item,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !isDisabled,\n        active: checked,\n        children: /* @__PURE__ */ jsx2(\n          Radio,\n          {\n            disabled: isDisabled,\n            required: context.required,\n            checked,\n            ...radioScope,\n            ...itemProps,\n            name: context.name,\n            ref: composedRefs,\n            onCheck: () => context.onValueChange(itemProps.value),\n            onKeyDown: composeEventHandlers2((event) => {\n              if (event.key === \"Enter\") event.preventDefault();\n            }),\n            onFocus: composeEventHandlers2(itemProps.onFocus, () => {\n              if (isArrowKeyPressedRef.current) ref.current?.click();\n            })\n          }\n        )\n      }\n    );\n  }\n);\nRadioGroupItem.displayName = ITEM_NAME;\nvar INDICATOR_NAME2 = \"RadioGroupIndicator\";\nvar RadioGroupIndicator = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return /* @__PURE__ */ jsx2(RadioIndicator, { ...radioScope, ...indicatorProps, ref: forwardedRef });\n  }\n);\nRadioGroupIndicator.displayName = INDICATOR_NAME2;\nvar Root2 = RadioGroup;\nvar Item2 = RadioGroupItem;\nvar Indicator = RadioGroupIndicator;\nexport {\n  Indicator,\n  Item2 as Item,\n  RadioGroup,\n  RadioGroupIndicator,\n  RadioGroupItem,\n  Root2 as Root,\n  createRadioGroupScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}