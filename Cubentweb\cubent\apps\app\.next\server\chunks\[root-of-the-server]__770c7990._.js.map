{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/api/auth/set-cross-domain-token/route.ts"], "sourcesContent": ["import { auth, currentUser } from '@clerk/nextjs/server';\nimport { NextResponse } from 'next/server';\nimport { cookies } from 'next/headers';\n\nexport async function POST() {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });\n    }\n\n    const user = await currentUser();\n    \n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 });\n    }\n\n    // Create a simple token with user data\n    const tokenData = {\n      id: user.id,\n      fullName: user.fullName,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      emailAddresses: user.emailAddresses.map(email => ({\n        emailAddress: email.emailAddress\n      })),\n      imageUrl: user.imageUrl,\n      timestamp: Date.now()\n    };\n\n    // Encode the token (in production, you'd want to encrypt/sign this)\n    const token = Buffer.from(JSON.stringify(tokenData)).toString('base64');\n\n    // Set cookie with domain .cubent.dev so it's shared across subdomains\n    const cookieStore = await cookies();\n    cookieStore.set('cubent_auth_token', token, {\n      domain: '.cubent.dev',\n      httpOnly: false, // Allow JavaScript access\n      secure: true, // HTTPS only\n      sameSite: 'lax',\n      maxAge: 60 * 60 * 24 * 30, // 30 days to match JWT token lifetime\n      path: '/'\n    });\n\n    return NextResponse.json({ success: true, token });\n  } catch (error) {\n    console.error('Error setting cross-domain token:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,MAAM,OAAO,MAAM,CAAA,GAAA,sSAAA,CAAA,cAAW,AAAD;QAE7B,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,uCAAuC;QACvC,MAAM,YAAY;YAChB,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,gBAAgB,KAAK,cAAc,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAChD,cAAc,MAAM,YAAY;gBAClC,CAAC;YACD,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,GAAG;QACrB;QAEA,oEAAoE;QACpE,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,YAAY,QAAQ,CAAC;QAE9D,sEAAsE;QACtE,MAAM,cAAc,MAAM,CAAA,GAAA,gPAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,qBAAqB,OAAO;YAC1C,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;YACvB,MAAM;QACR;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM;QAAM;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}