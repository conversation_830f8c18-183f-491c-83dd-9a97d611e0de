module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/gherkin.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>gherkin)
});
const lang = Object.freeze({
    "displayName": "Gherkin",
    "fileTypes": [
        "feature"
    ],
    "firstLineMatch": "\uAE30\uB2A5|\u6A5F\u80FD|\u529F\u80FD|\u30D5\u30A3\u30FC\u30C1\u30E3|\u062E\u0627\u0635\u064A\u0629|\u05EA\u05DB\u05D5\u05E0\u05D4|\u0424\u0443\u043D\u043A\u0446\u0456\u043E\u043D\u0430\u043B|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B\u043D\u043E\u0441\u0442|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B|\u041E\u0441\u043E\u0431\u0438\u043D\u0430|\u0424\u0443\u043D\u043A\u0446\u0438\u044F|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E\u0441\u0442\u044C|\u0421\u0432\u043E\u0439\u0441\u0442\u0432\u043E|\u041C\u043E\u0433\u0443\u045B\u043D\u043E\u0441\u0442|\xD6zellik|W\u0142a\u015Bciwo\u015B\u0107|T\xEDnh n\u0103ng|Savyb\u0117|Po\u017Eiadavka|Po\u017Eadavek|Osobina|Ominaisuus|Omadus|OH HAI|Mogu\u0107nost|Mogucnost|Jellemz\u0151|F\u012B\u010Da|Funzionalit\xE0|Funktionalit\xE4t|Funkcionalnost|Funkcionalit\u0101te|Func\u021Bionalitate|Functionaliteit|Functionalitate|Funcionalitat|Funcionalidade|Fonctionnalit\xE9|Fitur|Ability|Business Need|Feature|Egenskap|Egenskab|Crikey|Caracter\xEDstica|Arwedd(.*)",
    "foldingStartMarker": "^\\s*\\b(\uC608|\uC2DC\uB098\uB9AC\uC624 \uAC1C\uC694|\uC2DC\uB098\uB9AC\uC624|\uBC30\uACBD|\u80CC\u666F|\u5834\u666F\u5927\u7DB1|\u5834\u666F|\u573A\u666F\u5927\u7EB2|\u573A\u666F|\u5287\u672C\u5927\u7DB1|\u5287\u672C|\u4F8B\u5B50|\u4F8B|\u30C6\u30F3\u30D7\u30EC|\u30B7\u30CA\u30EA\u30AA\u30C6\u30F3\u30D7\u30EC\u30FC\u30C8|\u30B7\u30CA\u30EA\u30AA\u30C6\u30F3\u30D7\u30EC|\u30B7\u30CA\u30EA\u30AA\u30A2\u30A6\u30C8\u30E9\u30A4\u30F3|\u30B7\u30CA\u30EA\u30AA|\u30B5\u30F3\u30D7\u30EB|\u0633\u064A\u0646\u0627\u0631\u064A\u0648 \u0645\u062E\u0637\u0637|\u0633\u064A\u0646\u0627\u0631\u064A\u0648|\u0627\u0645\u062B\u0644\u0629|\u0627\u0644\u062E\u0644\u0641\u064A\u0629|\u05EA\u05E8\u05D7\u05D9\u05E9|\u05EA\u05D1\u05E0\u05D9\u05EA \u05EA\u05E8\u05D7\u05D9\u05E9|\u05E8\u05E7\u05E2|\u05D3\u05D5\u05D2\u05DE\u05D0\u05D5\u05EA|\u0422\u0430\u0440\u0438\u0445|\u0421\u0446\u0435\u043D\u0430\u0440\u0456\u0439|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0458\u0438|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u043E|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0439 \u0441\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430\u0441\u0438|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0439|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0456\u044E|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u0458\u0430|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u044F|\u0421\u043A\u0438\u0446\u0430|\u0420\u0430\u043C\u043A\u0430 \u043D\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u0439|\u041F\u0440\u0438\u043C\u0435\u0440\u0438|\u041F\u0440\u0438\u043C\u0435\u0440|\u041F\u0440\u0438\u043A\u043B\u0430\u0434\u0438|\u041F\u0440\u0435\u0434\u044B\u0441\u0442\u043E\u0440\u0438\u044F|\u041F\u0440\u0435\u0434\u0438\u0441\u0442\u043E\u0440\u0438\u044F|\u041F\u043E\u0437\u0430\u0434\u0438\u043D\u0430|\u041F\u0435\u0440\u0435\u0434\u0443\u043C\u043E\u0432\u0430|\u041E\u0441\u043D\u043E\u0432\u0430|\u041C\u0438\u0441\u043E\u043B\u043B\u0430\u0440|\u041A\u043E\u043D\u0446\u0435\u043F\u0442|\u041A\u043E\u043D\u0442\u0435\u043A\u0441\u0442|\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u044F|\xD6rnekler|Za\u0142o\u017Cenia|Wharrimean is|Voorbeelden|Variantai|T\xECnh hu\u1ED1ng|The thing of it is|Tausta|Taust|Tapausaihio|Tapaus|Tapaukset|Szenariogrundriss|Szenario|Szablon scenariusza|Stsenaarium|Struktura scenarija|Skica|Skenario konsep|Skenario|Situ\u0101cija|Senaryo tasla\u011F\u0131|Senaryo|Sc\xE9n\xE1\u0159|Sc\xE9nario|Schema dello scenario|Scen\u0101rijs p\u0113c parauga|Scen\u0101rijs|Scen\xE1r|Scenariusz|Scenariul de \u015Fablon|Scenariul de sablon|Scenariu|Scenarios|Scenario Outline|Scenario Amlinellol|Scenario|Example|Scenarijus|Scenariji|Scenarijaus \u0161ablonas|Scenarijai|Scenarij|Scenarie|Rerefons|Raamstsenaarium|P\u0159\xEDklady|P\xE9ld\xE1k|Pr\xEDklady|Przyk\u0142ady|Primjeri|Primeri|Primer|Pozad\xED|Pozadina|Pozadie|Plan du sc\xE9nario|Plan du Sc\xE9nario|Piem\u0113ri|Pavyzd\u017Eiai|Paraugs|Osnova sc\xE9n\xE1\u0159e|Osnova|N\xE1\u010Drt Sc\xE9n\xE1\u0159e|N\xE1\u010Drt Scen\xE1ru|Mate|MISHUN SRSLY|MISHUN|K\u1ECBch b\u1EA3n|Kontext|Konteksts|Kontekstas|Kontekst|Koncept|Khung t\xECnh hu\u1ED1ng|Khung k\u1ECBch b\u1EA3n|Juhtumid|H\xE1tt\xE9r|Grundlage|Ge\xE7mi\u015F|Forgat\xF3k\xF6nyv v\xE1zlat|Forgat\xF3k\xF6nyv|Exemplos|Exemples|Exemplele|Exempel|Examples|Esquema do Cen\xE1rio|Esquema do Cenario|Esquema del escenario|Esquema de l'escenari|Esempi|Escenario|Escenari|Enghreifftiau|Eksempler|Ejemplos|EXAMPLZ|D\u1EEF li\u1EC7u|Dis is what went down|Dasar|Contoh|Contexto|Contexte|Contesto|Condi\u0163ii|Conditii|Cobber|Cen\xE1rio|Cenario|Cefndir|B\u1ED1i c\u1EA3nh|Blokes|Beispiele|Bakgrunn|Bakgrund|Baggrund|Background|B4|Antecedents|Antecedentes|All y'all|Achtergrond|Abstrakt Scenario|Abstract Scenario|Rule|Regla|R\xE8gle|Regel|Regra)",
    "foldingStopMarker": "^\\s*$",
    "name": "gherkin",
    "patterns": [
        {
            "include": "#feature_element_keyword"
        },
        {
            "include": "#feature_keyword"
        },
        {
            "include": "#step_keyword"
        },
        {
            "include": "#strings_triple_quote"
        },
        {
            "include": "#strings_single_quote"
        },
        {
            "include": "#strings_double_quote"
        },
        {
            "include": "#comments"
        },
        {
            "include": "#tags"
        },
        {
            "include": "#scenario_outline_variable"
        },
        {
            "include": "#table"
        }
    ],
    "repository": {
        "comments": {
            "captures": {
                "0": {
                    "name": "comment.line.number-sign"
                }
            },
            "match": "^\\s*(#.*)"
        },
        "feature_element_keyword": {
            "captures": {
                "1": {
                    "name": "keyword.language.gherkin.feature.scenario"
                },
                "2": {
                    "name": "string.language.gherkin.scenario.title.title"
                }
            },
            "match": "^\\s*(\uC608|\uC2DC\uB098\uB9AC\uC624 \uAC1C\uC694|\uC2DC\uB098\uB9AC\uC624|\uBC30\uACBD|\u80CC\u666F|\u5834\u666F\u5927\u7DB1|\u5834\u666F|\u573A\u666F\u5927\u7EB2|\u573A\u666F|\u5287\u672C\u5927\u7DB1|\u5287\u672C|\u4F8B\u5B50|\u4F8B|\u30C6\u30F3\u30D7\u30EC|\u30B7\u30CA\u30EA\u30AA\u30C6\u30F3\u30D7\u30EC\u30FC\u30C8|\u30B7\u30CA\u30EA\u30AA\u30C6\u30F3\u30D7\u30EC|\u30B7\u30CA\u30EA\u30AA\u30A2\u30A6\u30C8\u30E9\u30A4\u30F3|\u30B7\u30CA\u30EA\u30AA|\u30B5\u30F3\u30D7\u30EB|\u0633\u064A\u0646\u0627\u0631\u064A\u0648 \u0645\u062E\u0637\u0637|\u0633\u064A\u0646\u0627\u0631\u064A\u0648|\u0627\u0645\u062B\u0644\u0629|\u0627\u0644\u062E\u0644\u0641\u064A\u0629|\u05EA\u05E8\u05D7\u05D9\u05E9|\u05EA\u05D1\u05E0\u05D9\u05EA \u05EA\u05E8\u05D7\u05D9\u05E9|\u05E8\u05E7\u05E2|\u05D3\u05D5\u05D2\u05DE\u05D0\u05D5\u05EA|\u0422\u0430\u0440\u0438\u0445|\u0421\u0446\u0435\u043D\u0430\u0440\u0456\u0439|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0458\u0438|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u043E|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0439 \u0441\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430\u0441\u0438|\u0421\u0446\u0435\u043D\u0430\u0440\u0438\u0439|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0456\u044E|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u0458\u0430|\u0421\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u044F|\u0421\u043A\u0438\u0446\u0430|\u0420\u0430\u043C\u043A\u0430 \u043D\u0430 \u0441\u0446\u0435\u043D\u0430\u0440\u0438\u0439|\u041F\u0440\u0438\u043C\u0435\u0440\u0438|\u041F\u0440\u0438\u043C\u0435\u0440|\u041F\u0440\u0438\u043A\u043B\u0430\u0434\u0438|\u041F\u0440\u0435\u0434\u044B\u0441\u0442\u043E\u0440\u0438\u044F|\u041F\u0440\u0435\u0434\u0438\u0441\u0442\u043E\u0440\u0438\u044F|\u041F\u043E\u0437\u0430\u0434\u0438\u043D\u0430|\u041F\u0435\u0440\u0435\u0434\u0443\u043C\u043E\u0432\u0430|\u041E\u0441\u043D\u043E\u0432\u0430|\u041C\u0438\u0441\u043E\u043B\u043B\u0430\u0440|\u041A\u043E\u043D\u0446\u0435\u043F\u0442|\u041A\u043E\u043D\u0442\u0435\u043A\u0441\u0442|\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u044F|\xD6rnekler|Za\u0142o\u017Cenia|Wharrimean is|Voorbeelden|Variantai|T\xECnh hu\u1ED1ng|The thing of it is|Tausta|Taust|Tapausaihio|Tapaus|Tapaukset|Szenariogrundriss|Szenario|Szablon scenariusza|Stsenaarium|Struktura scenarija|Skica|Skenario konsep|Skenario|Situ\u0101cija|Senaryo tasla\u011F\u0131|Senaryo|Sc\xE9n\xE1\u0159|Sc\xE9nario|Schema dello scenario|Scen\u0101rijs p\u0113c parauga|Scen\u0101rijs|Scen\xE1r|Scenariusz|Scenariul de \u015Fablon|Scenariul de sablon|Scenariu|Scenarios|Scenario Outline|Scenario Amlinellol|Scenario|Example|Scenarijus|Scenariji|Scenarijaus \u0161ablonas|Scenarijai|Scenarij|Scenarie|Rerefons|Raamstsenaarium|P\u0159\xEDklady|P\xE9ld\xE1k|Pr\xEDklady|Przyk\u0142ady|Primjeri|Primeri|Primer|Pozad\xED|Pozadina|Pozadie|Plan du sc\xE9nario|Plan du Sc\xE9nario|Piem\u0113ri|Pavyzd\u017Eiai|Paraugs|Osnova sc\xE9n\xE1\u0159e|Osnova|N\xE1\u010Drt Sc\xE9n\xE1\u0159e|N\xE1\u010Drt Scen\xE1ru|Mate|MISHUN SRSLY|MISHUN|K\u1ECBch b\u1EA3n|Kontext|Konteksts|Kontekstas|Kontekst|Koncept|Khung t\xECnh hu\u1ED1ng|Khung k\u1ECBch b\u1EA3n|Juhtumid|H\xE1tt\xE9r|Grundlage|Ge\xE7mi\u015F|Forgat\xF3k\xF6nyv v\xE1zlat|Forgat\xF3k\xF6nyv|Exemplos|Exemples|Exemplele|Exempel|Examples|Esquema do Cen\xE1rio|Esquema do Cenario|Esquema del escenario|Esquema de l'escenari|Esempi|Escenario|Escenari|Enghreifftiau|Eksempler|Ejemplos|EXAMPLZ|D\u1EEF li\u1EC7u|Dis is what went down|Dasar|Contoh|Contexto|Contexte|Contesto|Condi\u0163ii|Conditii|Cobber|Cen\xE1rio|Cenario|Cefndir|B\u1ED1i c\u1EA3nh|Blokes|Beispiele|Bakgrunn|Bakgrund|Baggrund|Background|B4|Antecedents|Antecedentes|All y'all|Achtergrond|Abstrakt Scenario|Abstract Scenario|Rule|Regla|R\xE8gle|Regel|Regra):(.*)"
        },
        "feature_keyword": {
            "captures": {
                "1": {
                    "name": "keyword.language.gherkin.feature"
                },
                "2": {
                    "name": "string.language.gherkin.feature.title"
                }
            },
            "match": "^\\s*(\uAE30\uB2A5|\u6A5F\u80FD|\u529F\u80FD|\u30D5\u30A3\u30FC\u30C1\u30E3|\u062E\u0627\u0635\u064A\u0629|\u05EA\u05DB\u05D5\u05E0\u05D4|\u0424\u0443\u043D\u043A\u0446\u0456\u043E\u043D\u0430\u043B|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B\u043D\u043E\u0441\u0442|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B|\u041E\u0441\u043E\u0431\u0438\u043D\u0430|\u0424\u0443\u043D\u043A\u0446\u0438\u044F|\u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E\u0441\u0442\u044C|\u0421\u0432\u043E\u0439\u0441\u0442\u0432\u043E|\u041C\u043E\u0433\u0443\u045B\u043D\u043E\u0441\u0442|\xD6zellik|W\u0142a\u015Bciwo\u015B\u0107|T\xEDnh n\u0103ng|Savyb\u0117|Po\u017Eiadavka|Po\u017Eadavek|Osobina|Ominaisuus|Omadus|OH HAI|Mogu\u0107nost|Mogucnost|Jellemz\u0151|F\u012B\u010Da|Funzionalit\xE0|Funktionalit\xE4t|Funkcionalnost|Funkcionalit\u0101te|Func\u021Bionalitate|Functionaliteit|Functionalitate|Funcionalitat|Funcionalidade|Fonctionnalit\xE9|Fitur|Ability|Business Need|Feature|Ability|Egenskap|Egenskab|Crikey|Caracter\xEDstica|Arwedd):(.*)\\b"
        },
        "scenario_outline_variable": {
            "match": "<[a-zA-Z0-9 _-]*>",
            "name": "variable.other"
        },
        "step_keyword": {
            "captures": {
                "1": {
                    "name": "keyword.language.gherkin.feature.step"
                }
            },
            "match": "^\\s*(En |\u0648 |Y |E |\u0535\u057E |Ya |Too right |V\u0259 |H\u0259m |A |\u0418 |\u800C\u4E14 |\u5E76\u4E14 |\u540C\u65F6 |\u4E26\u4E14 |\u540C\u6642 |Ak |Epi |A tak\xE9 |Og |\u{1F602} |And |Kaj |Ja |Et que |Et qu' |Et |\u10D3\u10D0 |Und |\u039A\u03B1\u03B9 |\u0A85\u0AA8\u0AC7 |\u05D5\u05D2\u05DD |\u0914\u0930 |\u0924\u0925\u093E |\xC9s |Dan |Agus |\u304B\u3064 |Lan |\u0CAE\u0CA4\u0CCD\u0CA4\u0CC1 |'ej |latlh |\uADF8\uB9AC\uACE0 |AN |Un |Ir |an |a |\u041C\u04E9\u043D |\u0422\u044D\u0433\u044D\u044D\u0434 |Ond |7 |\u0A05\u0A24\u0A47 |Aye |Oraz |Si |\u0218i |\u015Ei |\u041A \u0442\u043E\u043C\u0443 \u0436\u0435 |\u0422\u0430\u043A\u0436\u0435 |An |A tie\u017E |A taktie\u017E |A z\xE1rove\u0148 |In |Ter |Och |\u0BAE\u0BC7\u0BB2\u0BC1\u0BAE\u0BCD |\u0BAE\u0BB1\u0BCD\u0BB1\u0BC1\u0BAE\u0BCD |\u04BA\u04D9\u043C |\u0412\u04D9 |\u0C2E\u0C30\u0C3F\u0C2F\u0C41 |\u0E41\u0E25\u0E30 |Ve |\u0406 |\u0410 \u0442\u0430\u043A\u043E\u0436 |\u0422\u0430 |\u0627\u0648\u0631 |\u0412\u0430 |V\xE0 |Maar |\u0644\u0643\u0646 |Pero |\u0532\u0561\u0575\u0581 |Peru |Yeah nah |Amma |Ancaq |Ali |\u041D\u043E |Per\xF2 |\u4F46\u662F |Men |Ale |\u{1F614} |But |Sed |Kuid |Mutta |Mais que |Mais qu' |Mais |\u10DB\u10D0\u10D2\xAD\u10E0\u10D0\u10DB |Aber |\u0391\u03BB\u03BB\u03AC |\u0AAA\u0AA3 |\u05D0\u05D1\u05DC |\u092A\u0930 |\u092A\u0930\u0928\u094D\u0924\u0941 |\u0915\u093F\u0928\u094D\u0924\u0941 |De |En |Tapi |Ach |Ma |\u3057\u304B\u3057 |\u4F46\u3057 |\u305F\u3060\u3057 |Nanging |Ananging |\u0C86\u0CA6\u0CB0\u0CC6 |'ach |'a |\uD558\uC9C0\uB9CC |\uB2E8 |BUT |Bet |awer |m\xE4 |No |Tetapi |\u0413\u044D\u0445\u0434\u044D\u044D |\u0425\u0430\u0440\u0438\u043D |Ac |\u0A2A\u0A30 |\u0627\u0645\u0627 |Avast! |Mas |Dar |\u0410 |\u0418\u043D\u0430\u0447\u0435 |Buh |\u0410\u043B\u0438 |Toda |Ampak |Vendar |\u0B86\u0BA9\u0BBE\u0BB2\u0BCD |\u041B\u04D9\u043A\u0438\u043D |\u04D8\u043C\u043C\u0430 |\u0C15\u0C3E\u0C28\u0C3F |\u0E41\u0E15\u0E48 |Fakat |Ama |\u0410\u043B\u0435 |\u0644\u06CC\u06A9\u0646 |\u041B\u0435\u043A\u0438\u043D |\u0411\u0438\u0440\u043E\u043A |\u0410\u043C\u043C\u043E |Nh\u01B0ng |Ond |Dan |\u0627\u0630\u0627\u064B |\u062B\u0645 |Alavez |Allora |Antonces |\u0531\u057A\u0561 |Ent\xF3s |But at the end of the day I reckon |O halda |Zatim |\u0422\u043E |Aleshores |Cal |\u90A3\u4E48 |\u90A3\u9EBC |L\xE8 sa a |Le sa a |Onda |Pak |S\xE5 |\u{1F64F} |Then |Do |Siis |Niin |Alors |Ent\xF3n |Logo |\u10DB\u10D0\u10E8\u10D8\u10DC |Dann |\u03A4\u03CC\u03C4\u03B5 |\u0AAA\u0A9B\u0AC0 |\u05D0\u05D6 |\u05D0\u05D6\u05D9 |\u0924\u092C |\u0924\u0926\u093E |Akkor |\xDE\xE1 |Maka |Ansin |\u306A\u3089\u3070 |Njuk |Banjur |\u0CA8\u0C82\u0CA4\u0CB0 |vaj |\uADF8\uB7EC\uBA74 |DEN |Tad |Tada |dann |\u0422\u043E\u0433\u0430\u0448 |Togash |Kemudian |\u0422\u044D\u0433\u044D\u0445\u044D\u0434 |\u04AE\u04AF\u043D\u0438\u0439 \u0434\u0430\u0440\u0430\u0430 |Tha |\xDEa |\xD0a |Tha the |\xDEa \xFEe |\xD0a \xF0e |\u0A24\u0A26 |\u0622\u0646\u06AF\u0627\u0647 |Let go and haul |Wtedy |Ent\xE3o |Entao |Atunci |\u0417\u0430\u0442\u0435\u043C |\u0422\u043E\u0433\u0434\u0430 |Dun |Den youse gotta |\u041E\u043D\u0434\u0430 |Tak |Potom |Nato |Potem |Takrat |Entonces |\u0B85\u0BAA\u0BCD\u0BAA\u0BC6\u0BBE\u0BB4\u0BC1\u0BA4\u0BC1 |\u041D\u04D9\u0442\u0438\u0497\u04D9\u0434\u04D9 |\u0C05\u0C2A\u0C4D\u0C2A\u0C41\u0C21\u0C41 |\u0E14\u0E31\u0E07\u0E19\u0E31\u0E49\u0E19 |O zaman |\u0422\u043E\u0434\u0456 |\u067E\u06BE\u0631 |\u062A\u0628 |\u0423\u043D\u0434\u0430 |Th\xEC |Yna |Wanneer |\u0645\u062A\u0649 |\u0639\u0646\u062F\u0645\u0627 |Cuan |\u0535\u0569\u0565 |\u0535\u0580\u0562 |Cuando |It's just unbelievable |\u018Fg\u0259r |N\u0259 vaxt ki |Kada |\u041A\u043E\u0433\u0430\u0442\u043E |Quan |\u5F53 |\u7576 |L\xE8 |Le |Kad |Kdy\u017E |N\xE5r |Als |\u{1F3AC} |When |Se |Kui |Kun |Quand |Lorsque |Lorsqu' |Cando |\u10E0\u10DD\u10D3\u10D4\u10E1\u10D0\u10EA |Wenn |\u038C\u03C4\u03B1\u03BD |\u0A95\u0ACD\u0AAF\u0ABE\u0AB0\u0AC7 |\u05DB\u05D0\u05E9\u05E8 |\u091C\u092C |\u0915\u0926\u093E |Majd |Ha |Amikor |\xDEegar |Ketika |Nuair a |Nuair nach |Nuair ba |Nuair n\xE1r |Quando |\u3082\u3057 |Manawa |Menawa |\u0CB8\u0CCD\u0CA5\u0CBF\u0CA4\u0CBF\u0CAF\u0CA8\u0CCD\u0CA8\u0CC1 |qaSDI' |\uB9CC\uC77C |\uB9CC\uC57D |WEN |Ja |Kai |wann |\u041A\u043E\u0433\u0430 |Koga |Apabila |\u0425\u044D\u0440\u044D\u0432 |Tha |\xDEa |\xD0a |\u0A1C\u0A26\u0A4B\u0A02 |\u0647\u0646\u06AF\u0627\u0645\u06CC |Blimey! |Je\u017Celi |Je\u015Bli |Gdy |Kiedy |Cand |C\xE2nd |\u041A\u043E\u0433\u0434\u0430 |\u0415\u0441\u043B\u0438 |Wun |Youse know like when |\u041A\u0430\u0434\u0430 |\u041A\u0430\u0434 |Ke\u010F |Ak |Ko |Ce |\u010Ce |Kadar |N\xE4r |\u0B8E\u0BAA\u0BCD\u0BAA\u0BC7\u0BBE\u0BA4\u0BC1 |\u04D8\u0433\u04D9\u0440 |\u0C08 \u0C2A\u0C30\u0C3F\u0C38\u0C4D\u0C25\u0C3F\u0C24\u0C3F\u0C32\u0C4B |\u0E40\u0E21\u0E37\u0E48\u0E2D |E\u011Fer ki |\u042F\u043A\u0449\u043E |\u041A\u043E\u043B\u0438 |\u062C\u0628 |\u0410\u0433\u0430\u0440 |Khi |Pryd |Gegewe |\u0628\u0641\u0631\u0636 |Dau |Dada |Daus |Dadas |\u0534\u056B\u0581\u0578\u0582\u0584 |D\xE1u |Daos |Daes |Y'know |Tutaq ki |Verilir |Dato |\u0414\u0430\u0434\u0435\u043D\u043E |Donat |Donada |At\xE8s |Atesa |\u5047\u5982 |\u5047\u8BBE |\u5047\u5B9A |\u5047\u8A2D |Sipoze |Sipoze ke |Sipoze Ke |Zadan |Zadani |Zadano |Pokud |Za p\u0159edpokladu |Givet |Gegeven |Stel |\u{1F610} |Given |Donita\u0135o |Komence |Eeldades |Oletetaan |Soit |Etant donn\xE9 que |Etant donn\xE9 qu' |Etant donn\xE9 |Etant donn\xE9e |Etant donn\xE9s |Etant donn\xE9es |\xC9tant donn\xE9 que |\xC9tant donn\xE9 qu' |\xC9tant donn\xE9 |\xC9tant donn\xE9e |\xC9tant donn\xE9s |\xC9tant donn\xE9es |Dado |Dados |\u10DB\u10DD\u10EA\u10D4\u10DB\u10E3\u10DA\u10D8 |Angenommen |Gegeben sei |Gegeben seien |\u0394\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03BF\u03C5 |\u0A86\u0AAA\u0AC7\u0AB2 \u0A9B\u0AC7 |\u05D1\u05D4\u05D9\u05E0\u05EA\u05DF |\u0905\u0917\u0930 |\u092F\u0926\u093F |\u091A\u0942\u0902\u0915\u093F |Amennyiben |Adott |Ef |Dengan |Cuir i gc\xE1s go |Cuir i gc\xE1s nach |Cuir i gc\xE1s gur |Cuir i gc\xE1s n\xE1r |Data |Dati |Date |\u524D\u63D0 |Nalika |Nalikaning |\u0CA8\u0CBF\u0CD5\u0CA1\u0CBF\u0CA6 |ghu' noblu' |DaH ghu' bejlu' |\uC870\uAC74 |\uBA3C\uC800 |I CAN HAZ |Kad |Duota |ugeholl |\u0414\u0430\u0434\u0435\u043D\u0430 |Dadeno |Dadena |Diberi |Bagi |\u04E8\u0433\u04E9\u0433\u0434\u0441\u04E9\u043D \u043D\u044C |\u0410\u043D\u0445 |Gitt |Thurh |\xDEurh |\xD0urh |\u0A1C\u0A47\u0A15\u0A30 |\u0A1C\u0A3F\u0A35\u0A47\u0A02 \u0A15\u0A3F |\u0628\u0627 \u0641\u0631\u0636 |Gangway! |Zak\u0142adaj\u0105c |Maj\u0105c |Zak\u0142adaj\u0105c, \u017Ce |Date fiind |Dat fiind |Dat\u0103 fiind |Dati fiind |Da\u021Bi fiind |Da\u0163i fiind |\u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C |\u0414\u0430\u043D\u043E |\u041F\u0443\u0441\u0442\u044C |Givun |Youse know when youse got |\u0417\u0430 \u0434\u0430\u0442\u043E |\u0417\u0430 \u0434\u0430\u0442\u0435 |\u0417\u0430 \u0434\u0430\u0442\u0438 |Za dato |Za date |Za dati |Pokia\u013E |Za predpokladu |Dano |Podano |Zaradi |Privzeto |\u0B95\u0BC6\u0BBE\u0B9F\u0BC1\u0B95\u0BCD\u0B95\u0BAA\u0BCD\u0BAA\u0B9F\u0BCD\u0B9F |\u04D8\u0439\u0442\u0438\u043A |\u0C1A\u0C46\u0C2A\u0C4D\u0C2A\u0C2C\u0C21\u0C3F\u0C28\u0C26\u0C3F |\u0E01\u0E33\u0E2B\u0E19\u0E14\u0E43\u0E2B\u0E49 |Diyelim ki |\u041F\u0440\u0438\u043F\u0443\u0441\u0442\u0438\u043C\u043E |\u041F\u0440\u0438\u043F\u0443\u0441\u0442\u0438\u043C\u043E, \u0449\u043E |\u041D\u0435\u0445\u0430\u0439 |\u0627\u06AF\u0631 |\u0628\u0627\u0644\u0641\u0631\u0636 |\u0641\u0631\u0636 \u06A9\u06CC\u0627 |\u0410\u0433\u0430\u0440 |Bi\u1EBFt |Cho |Anrhegedig a |\\* )"
        },
        "strings_double_quote": {
            "begin": `(?<![a-zA-Z0-9'])"`,
            "end": `"(?![a-zA-Z0-9'])`,
            "name": "string.quoted.double",
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character.escape.untitled"
                }
            ]
        },
        "strings_single_quote": {
            "begin": `(?<![a-zA-Z0-9"])'`,
            "end": `'(?![a-zA-Z0-9"])`,
            "name": "string.quoted.single",
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character.escape"
                }
            ]
        },
        "strings_triple_quote": {
            "begin": '""".*',
            "end": '"""',
            "name": "string.quoted.single"
        },
        "table": {
            "begin": "^\\s*\\|",
            "end": "\\|\\s*$",
            "name": "keyword.control.cucumber.table",
            "patterns": [
                {
                    "match": "\\w",
                    "name": "source"
                }
            ]
        },
        "tags": {
            "captures": {
                "0": {
                    "name": "entity.name.type.class.tsx"
                }
            },
            "match": "(@[^@\\r\\n\\t ]+)"
        }
    },
    "scopeName": "text.gherkin.feature"
});
var gherkin = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_gherkin_mjs_6676dcb9._.js.map