{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/sql.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"SQL\", \"name\": \"sql\", \"patterns\": [{ \"match\": \"((?<!@)@)\\\\b(\\\\w+)\\\\b\", \"name\": \"text.variable\" }, { \"match\": \"(\\\\[)[^\\\\]]*(\\\\])\", \"name\": \"text.bracketed\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.sql\" }, \"5\": { \"name\": \"entity.name.function.sql\" } }, \"match\": \"(?i:^\\\\s*(create(?:\\\\s+or\\\\s+replace)?)\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)(['\\\"`]?)(\\\\w+)\\\\4\", \"name\": \"meta.create.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.sql\" } }, \"match\": \"(?i:^\\\\s*(drop)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\", \"name\": \"meta.drop.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.table.sql\" }, \"3\": { \"name\": \"entity.name.function.sql\" }, \"4\": { \"name\": \"keyword.other.cascade.sql\" } }, \"match\": \"(?i:\\\\s*(drop)\\\\s+(table)\\\\s+(\\\\w+)(\\\\s+cascade)?\\\\b)\", \"name\": \"meta.drop.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.table.sql\" } }, \"match\": \"(?i:^\\\\s*(alter)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)\", \"name\": \"meta.alter.sql\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.sql\" }, \"2\": { \"name\": \"storage.type.sql\" }, \"3\": { \"name\": \"constant.numeric.sql\" }, \"4\": { \"name\": \"storage.type.sql\" }, \"5\": { \"name\": \"constant.numeric.sql\" }, \"6\": { \"name\": \"storage.type.sql\" }, \"7\": { \"name\": \"constant.numeric.sql\" }, \"8\": { \"name\": \"constant.numeric.sql\" }, \"9\": { \"name\": \"storage.type.sql\" }, \"10\": { \"name\": \"constant.numeric.sql\" }, \"11\": { \"name\": \"storage.type.sql\" }, \"12\": { \"name\": \"storage.type.sql\" }, \"13\": { \"name\": \"storage.type.sql\" }, \"14\": { \"name\": \"constant.numeric.sql\" }, \"15\": { \"name\": \"storage.type.sql\" } }, \"match\": \"(?i)\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\b|\\\\b(bit\\\\svarying|character\\\\s(?:varying)?|tinyint|var\\\\schar|float|interval)\\\\((\\\\d+)\\\\)|\\\\b(char|number|varchar\\\\d?)\\\\b(?:\\\\((\\\\d+)\\\\))?|\\\\b(numeric|decimal)\\\\b(?:\\\\((\\\\d+),(\\\\d+)\\\\))?|\\\\b(times?)\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\swith(?:out)?\\\\stime\\\\szone\\\\b)?|\\\\b(timestamp)(?:(s|tz))?\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\s(with|without)\\\\stime\\\\szone\\\\b)?\" }, { \"match\": \"(?i:\\\\b((?:primary|foreign)\\\\s+key|references|on\\\\sdelete(\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\b)\", \"name\": \"storage.modifier.sql\" }, { \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.sql\" }, { \"match\": \"(?i:\\\\b(select(\\\\s+(all|distinct))?|insert\\\\s+(ignore\\\\s+)?into|update|delete|from|set|where|group\\\\s+by|or|like|and|union(\\\\s+all)?|having|order\\\\s+by|limit|cross\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\s+outer)?)\\\\s+join|natural(\\\\s+(inner|(left|right|full)(\\\\s+outer)?))?\\\\s+join)\\\\b)\", \"name\": \"keyword.other.DML.sql\" }, { \"match\": \"(?i:\\\\b(on|off|((is\\\\s+)?not\\\\s+)?null)\\\\b)\", \"name\": \"keyword.other.DDL.create.II.sql\" }, { \"match\": \"(?i:\\\\bvalues\\\\b)\", \"name\": \"keyword.other.DML.II.sql\" }, { \"match\": \"(?i:\\\\b(begin(\\\\s+work)?|start\\\\s+transaction|commit(\\\\s+work)?|rollback(\\\\s+work)?)\\\\b)\", \"name\": \"keyword.other.LUW.sql\" }, { \"match\": \"(?i:\\\\b(grant(\\\\swith\\\\sgrant\\\\soption)?|revoke)\\\\b)\", \"name\": \"keyword.other.authorization.sql\" }, { \"match\": \"(?i:\\\\bin\\\\b)\", \"name\": \"keyword.other.data-integrity.sql\" }, { \"match\": \"(?i:^\\\\s*(comment\\\\s+on\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\s+.*?\\\\s+(is)\\\\s+)\", \"name\": \"keyword.other.object-comments.sql\" }, { \"match\": \"(?i)\\\\bAS\\\\b\", \"name\": \"keyword.other.alias.sql\" }, { \"match\": \"(?i)\\\\b(DESC|ASC)\\\\b\", \"name\": \"keyword.other.order.sql\" }, { \"match\": \"\\\\*\", \"name\": \"keyword.operator.star.sql\" }, { \"match\": \"[!<>]?=|<>|<|>\", \"name\": \"keyword.operator.comparison.sql\" }, { \"match\": \"-|\\\\+|/\", \"name\": \"keyword.operator.math.sql\" }, { \"match\": \"\\\\|\\\\|\", \"name\": \"keyword.operator.concatenator.sql\" }, { \"captures\": { \"1\": { \"name\": \"support.function.aggregate.sql\" } }, \"match\": \"(?i)\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.analytic.sql\" } }, \"match\": \"(?i)\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.bitmanipulation.sql\" } }, \"match\": \"(?i)\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.conversion.sql\" } }, \"match\": \"(?i)\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.collation.sql\" } }, \"match\": \"(?i)\\\\b(collationproperty|tertiary_weights)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.cryptographic.sql\" } }, \"match\": \"(?i)\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.cursor.sql\" } }, \"match\": \"(?i)\\\\b(cursor_status)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.datetime.sql\" } }, \"match\": \"(?i)\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.datatype.sql\" } }, \"match\": \"(?i)\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.expression.sql\" } }, \"match\": \"(?i)\\\\b(coalesce|nullif)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.globalvar.sql\" } }, \"match\": \"(?<!@)@@(?i)\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.json.sql\" } }, \"match\": \"(?i)\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.logical.sql\" } }, \"match\": \"(?i)\\\\b(choose|iif|greatest|least)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.mathematical.sql\" } }, \"match\": \"(?i)\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.metadata.sql\" } }, \"match\": \"(?i)\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.ranking.sql\" } }, \"match\": \"(?i)\\\\b(rank|dense_rank|ntile|row_number)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.rowset.sql\" } }, \"match\": \"(?i)\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.security.sql\" } }, \"match\": \"(?i)\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.string.sql\" } }, \"match\": \"(?i)\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.system.sql\" } }, \"match\": \"(?i)\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.textimage.sql\" } }, \"match\": \"(?i)\\\\b(patindex|textptr|textvalid)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"constant.other.database-name.sql\" }, \"2\": { \"name\": \"constant.other.table-name.sql\" } }, \"match\": \"(\\\\w+?)\\\\.(\\\\w+)\" }, { \"include\": \"#strings\" }, { \"include\": \"#regexps\" }, { \"match\": \"\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\s+or\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\b\", \"name\": \"keyword.other.sql\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.scope.begin.sql\" }, \"2\": { \"name\": \"punctuation.section.scope.end.sql\" } }, \"comment\": \"Allow for special \\u21A9 behavior\", \"match\": \"(\\\\()(\\\\))\", \"name\": \"meta.block.sql\" }], \"repository\": { \"comment-block\": { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.sql\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block\", \"patterns\": [{ \"include\": \"#comment-block\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=--)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.sql\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.sql\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.sql\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.sql\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [] }, { \"include\": \"#comment-block\" }] }, \"regexps\": { \"patterns\": [{ \"begin\": \"/(?=\\\\S.*/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.regexp.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"match\": \"\\\\\\\\/\", \"name\": \"constant.character.escape.slash.sql\" }] }, { \"begin\": \"%r\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"comment\": \"We should probably handle nested bracket pairs!?! -- Allan\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.regexp.modr.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }] }, \"string_escape\": { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.sql\" }, \"string_interpolation\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"3\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"match\": \"(#\\\\{)([^}]*)(\\\\})\", \"name\": \"string.interpolated.sql\" }, \"strings\": { \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"3\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": \"(N)?(')[^']*(')\", \"name\": \"string.quoted.single.sql\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.single.sql\", \"patterns\": [{ \"include\": \"#string_escape\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": \"(`)[^`\\\\\\\\]*(`)\", \"name\": \"string.quoted.other.backtick.sql\" }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.other.backtick.sql\", \"patterns\": [{ \"include\": \"#string_escape\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": '(\")[^\"#]*(\")', \"name\": \"string.quoted.double.sql\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.double.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }, { \"begin\": \"%\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.other.quoted.brackets.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }] } }, \"scopeName\": \"source.sql\" });\nvar sql = [\n  lang\n];\n\nexport { sql as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAyB,QAAQ;QAAgB;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAiB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;YAA4O,QAAQ;QAAkB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAiL,QAAQ;QAAgB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAAyD,QAAQ;QAAgB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;YAAmM,QAAQ;QAAiB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAuB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAuB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,SAAS;QAA4gB;QAAG;YAAE,SAAS;YAAyH,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAc,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAiT,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAA+C,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAA4F,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAwD,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAiB,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAsK,QAAQ;QAAoC;QAAG;YAAE,SAAS;YAAgB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAwB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAO,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAkB,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAW,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAU,QAAQ;QAAoC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAuL;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA4G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;QAAsE;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAAuE;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAwD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;QAAuY;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAAmC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA+U;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAkG;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAAqC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAwW;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAA2G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA+C;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAA8I;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAwiB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAAsD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAA+G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA0S;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAAkQ;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAA4Z;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAgD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAmB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,SAAS;YAAquT,QAAQ;QAAoB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,WAAW;YAAqC,SAAS;YAAc,QAAQ;QAAiB;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAQ,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAA+B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,YAAY,EAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAA8D,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAS,QAAQ;QAAgC;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAsB,QAAQ;QAA0B;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAmB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAmB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAgB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AAChwvB,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/sas.mjs"], "sourcesContent": ["import sql from './sql.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"SAS\", \"fileTypes\": [\"sas\"], \"foldingStartMarker\": \"(?i:(proc|data|%macro).*;$)\", \"foldingStopMarker\": \"(?i:(run|quit|%mend)\\\\s?);\", \"name\": \"sas\", \"patterns\": [{ \"include\": \"#starComment\" }, { \"include\": \"#blockComment\" }, { \"include\": \"#macro\" }, { \"include\": \"#constant\" }, { \"include\": \"#quote\" }, { \"include\": \"#operator\" }, { \"begin\": \"\\\\b(?i:(data))\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.sas\" } }, \"comment\": \"Begins a DATA step and provides names for any output SAS data sets, views, or programs.\", \"end\": \"(;)\", \"patterns\": [{ \"include\": \"#blockComment\" }, { \"include\": \"#dataSet\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.sas\" }, \"2\": { \"name\": \"keyword.other.sas\" } }, \"match\": \"(?i:(?:(stack|pgm|view|source)\\\\s?=\\\\s?)|(debug|nesting|nolist))\" }] }, { \"begin\": \"\\\\b(?i:(set|update|modify|merge))\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.sas\" }, \"2\": { \"name\": \"entity.name.class.sas\" }, \"3\": { \"name\": \"entity.name.class.sas\" } }, \"comment\": \"DATA set File-Handling Statements for DATA step\", \"end\": \"(;)\", \"patterns\": [{ \"include\": \"#blockComment\" }, { \"include\": \"#dataSet\" }] }, { \"match\": \"(?i:\\\\b(if|while|until|for|do|end|then|else|run|quit|cancel|options)\\\\b)\", \"name\": \"keyword.control.sas\" }, { \"captures\": { \"1\": { \"name\": \"support.class.sas\" }, \"3\": { \"name\": \"entity.name.function.sas\" } }, \"match\": \"(?i:(%(bquote|do|else|end|eval|global|goto|if|inc|include|index|input|length|let|list|local|lowcase|macro|mend|nrbquote|nrquote|nrstr|put|qscan|qsysfunc|quote|run|scan|str|substr|syscall|sysevalf|sysexec|sysfunc|sysrc|then|to|unquote|upcase|until|while|window)\\\\b))\\\\s*(\\\\w*)\", \"name\": \"keyword.other.sas\" }, { \"begin\": \"(?i:\\\\b(proc\\\\s*(sql))\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.sas\" }, \"2\": { \"name\": \"support.class.sas\" } }, \"comment\": \"Looks like for this to work there must be a *name* as well as the patterns/include bit.\", \"end\": \"(?i:\\\\b(quit)\\\\s*;)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.sas\" } }, \"name\": \"meta.sql.sas\", \"patterns\": [{ \"include\": \"#starComment\" }, { \"include\": \"#blockComment\" }, { \"include\": \"source.sql\" }] }, { \"match\": \"(?i:\\\\b(by|label|format)\\\\b)\", \"name\": \"keyword.datastep.sas\" }, { \"captures\": { \"1\": { \"name\": \"support.function.sas\" }, \"2\": { \"name\": \"support.class.sas\" } }, \"match\": \"(?i:\\\\b(proc (\\\\w+))\\\\b)\", \"name\": \"meta.function-call.sas\" }, { \"match\": \"(?i:\\\\b(_n_|_error_)\\\\b)\", \"name\": \"variable.language.sas\" }, { \"captures\": { \"1\": { \"name\": \"support.class.sas\" } }, \"match\": \"\\\\b(?i:(_all_|_character_|_cmd_|_freq_|_i_|_infile_|_last_|_msg_|_null_|_numeric_|_temporary_|_type_|abort|abs|addr|adjrsq|airy|alpha|alter|altlog|altprint|and|arcos|array|arsin|as|atan|attrc|attrib|attrn|authserver|autoexec|awscontrol|awsdef|awsmenu|awsmenumerge|awstitle|backward|band|base|betainv|between|blocksize|blshift|bnot|bor|brshift|bufno|bufsize|bxor|by|byerr|byline|byte|calculated|call|cards|cards4|case|catcache|cbufno|cdf|ceil|center|cexist|change|chisq|cinv|class|cleanup|close|cnonct|cntllev|coalesce|codegen|col|collate|collin|column|comamid|comaux1|comaux2|comdef|compbl|compound|compress|config|continue|convert|cos|cosh|cpuid|create|cross|crosstab|css|curobs|cv|daccdb|daccdbsl|daccsl|daccsyd|dacctab|dairy|datalines|datalines4|date|datejul|datepart|datetime|day|dbcslang|dbcstype|dclose|ddm|delete|delimiter|depdb|depdbsl|depsl|depsyd|deptab|dequote|descending|descript|design=|device|dflang|dhms|dif|digamma|dim|dinfo|display|distinct|dkricond|dkrocond|dlm|dnum|do|dopen|doptname|doptnum|dread|drop|dropnote|dsname|dsnferr|echo|else|emaildlg|emailid|emailpw|emailserver|emailsys|encrypt|end|endsas|engine|eof|eov|erf|erfc|error|errorcheck|errors|exist|exp|fappend|fclose|fcol|fdelete|feedback|fetch|fetchobs|fexist|fget|file|fileclose|fileexist|filefmt|filename|fileref|filevar|finfo|finv|fipname|fipnamel|fipstate|first|firstobs|floor|fmterr|fmtsearch|fnonct|fnote|font|fontalias|footnote[1-9]?|fopen|foptname|foptnum|force|formatted|formchar|formdelim|formdlim|forward|fpoint|fpos|fput|fread|frewind|frlen|from|fsep|full|fullstimer|fuzz|fwrite|gaminv|gamma|getoption|getvarc|getvarn|go|goto|group|gwindow|hbar|hbound|helpenv|helploc|hms|honorappearance|hosthelp|hostprint|hour|hpct|html|hvar|ibessel|ibr|id|if|index|indexc|indexw|infile|informat|initcmd|initstmt|inner|input|inputc|inputn|inr|insert|int|intck|intnx|into|intrr|invaliddata|irr|is|jbessel|join|juldate|keep|kentb|kurtosis|label|lag|last|lbound|leave|left|length|levels|lgamma|lib|libname|library|libref|line|linesize|link|list|log|log10|log2|logpdf|logpmf|logsdf|lostcard|lowcase|lrecl|ls|macro|macrogen|maps|mautosource|max|maxdec|maxr|mdy|mean|measures|median|memtype|merge|merror|min|minute|missing|missover|mlogic|mod|mode|model|modify|month|mopen|mort|mprint|mrecall|msglevel|msymtabmax|mvarsize|myy|n|nest|netpv|new|news|nmiss|no|nobatch|nobs|nocaps|nocardimage|nocenter|nocharcode|nocmdmac|nocol|nocum|nodate|nodbcs|nodetails|nodmr|nodms|nodmsbatch|nodup|nodupkey|noduplicates|noechoauto|noequals|noerrorabend|noexitwindows|nofullstimer|noicon|noimplmac|noint|nolist|noloadlist|nomiss|nomlogic|nomprint|nomrecall|nomsgcase|nomstored|nomultenvappl|nonotes|nonumber|noobs|noovp|nopad|nopercent|noprint|noprintinit|normal|norow|norsasuser|nosetinit|nosource|nosource2|nosplash|nosymbolgen|note|notes|notitle|notitles|notsorted|noverbose|noxsync|noxwait|npv|null|number|numkeys|nummousekeys|nway|obs|ods|on|open|option|order|ordinal|otherwise|out|outer|outp=|output|over|ovp|p(1|5|10|25|50|75|90|95|99)|pad|pad2|page|pageno|pagesize|paired|parm|parmcards|path|pathdll|pathname|pdf|peek|peekc|pfkey|pmf|point|poisson|poke|position|printer|probbeta|probbnml|probchi|probf|probgam|probhypr|probit|probnegb|probnorm|probsig|probt|procleave|project|prt|propcase|prxmatch|prxparse|prxchange|prxposn|ps|put|putc|putn|pw|pwreq|qtr|quote|r|ranbin|rancau|ranexp|rangam|range|ranks|rannor|ranpoi|rantbl|rantri|ranuni|read|recfm|register|regr|remote|remove|rename|repeat|replace|resolve|retain|return|reuse|reverse|rewind|right|round|rsquare|rtf|rtrace|rtraceloc|s|s2|samploc|sasautos|sascontrol|sasfrscr|sashelp|sasmsg|sasmstore|sasscript|sasuser|saving|scan|sdf|second|select|selection|separated|seq|serror|set|setcomm|setot|sign|simple|sin|sinh|siteinfo|skewness|skip|sle|sls|sortedby|sortpgm|sortseq|sortsize|soundex|source2|spedis|splashlocation|split|spool|sqrt|start|std|stderr|stdin|stfips|stimer|stname|stnamel|stop|stopover|strip|subgroup|subpopn|substr|sum|sumwgt|symbol|symbolgen|symget|symput|sysget|sysin|sysleave|sysmsg|sysparm|sysprint|sysprintfont|sysprod|sysrc|system|t|table|tables|tan|tanh|tapeclose|tbufsize|terminal|test|then|time|timepart|tinv|title[1-9]?|tnonct|to|today|tol|tooldef|totper|transformout|translate|trantab|tranwrd|trigamma|trim|trimn|trunc|truncover|type|unformatted|uniform|union|until|upcase|update|user|usericon|uss|validate|value|var|varfmt|varinfmt|varlabel|varlen|varname|varnum|varray|varrayx|vartype|verify|vformat|vformatd|vformatdx|vformatn|vformatnx|vformatw|vformatwx|vformatx|vinarray|vinarrayx|vinformat|vinformatd|vinformatdx|vinformatn|vinformatnx|vinformatw|vinformatwx|vinformatx|vlabel|vlabelx|vlength|vlengthx|vname|vnamex|vnferr|vtype|vtypex|weekday|weight|when|where|while|wincharset|window|work|workinit|workterm|write|wsum|wsumx|x|xsync|xwait|year|yearcutoff|yes|yyq|zipfips|zipname|zipnamel|zipstate))\\\\b\", \"name\": \"support.function.sas\" }], \"repository\": { \"blockComment\": { \"patterns\": [{ \"begin\": \"\\\\/\\\\*\", \"end\": \"\\\\*\\\\/\", \"name\": \"comment.block.slashstar.sas\" }] }, \"constant\": { \"patterns\": [{ \"comment\": \"numeric constant\", \"match\": \"(?<![&}])\\\\b\\\\d*\\\\.?\\\\d+([eEdD][-+]?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.sas\" }, { \"comment\": \"single quote numeric-type constant\", \"match\": \"(')([^']+)(')(dt|[dt])\", \"name\": \"constant.numeric.quote.single.sas\" }, { \"comment\": \"double quote numeric-type constant\", \"match\": '(\")([^\"]+)(\")(dt|[dt])', \"name\": \"constant.numeric.quote.double.sas\" }] }, \"dataSet\": { \"patterns\": [{ \"begin\": \"((\\\\w+)\\\\.)?(\\\\w+)\\\\s?\\\\(\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.class.libref.sas\" }, \"3\": { \"name\": \"entity.name.class.dsname.sas\" } }, \"comment\": \"data set with options\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#dataSetOptions\" }, { \"include\": \"#blockComment\" }, { \"include\": \"#macro\" }, { \"include\": \"#constant\" }, { \"include\": \"#quote\" }, { \"include\": \"#operator\" }] }, { \"captures\": { \"2\": { \"name\": \"entity.name.class.libref.sas\" }, \"3\": { \"name\": \"entity.name.class.dsname.sas\" } }, \"comment\": \"data set without options\", \"match\": \"\\\\b((\\\\w+)\\\\.)?(\\\\w+)\\\\b\" }] }, \"dataSetOptions\": { \"patterns\": [{ \"match\": \"(?<=\\\\s|\\\\(|\\\\))(?i:ALTER|BUFNO|BUFSIZE|CNTLLEV|COMPRESS|DLDMGACTION|ENCRYPT|ENCRYPTKEY|EXTENDOBSCOUNTER|GENMAX|GENNUM|INDEX|LABEL|OBSBUF|OUTREP|PW|PWREQ|READ|REPEMPTY|REPLACE|REUSE|ROLE|SORTEDBY|SPILL|TOBSNO|TYPE|WRITE|FILECLOSE|FIRSTOBS|IN|OBS|POINTOBS|WHERE|WHEREUP|IDXNAME|IDXWHERE|DROP|KEEP|RENAME)\\\\s?=\", \"name\": \"keyword.other.sas\" }] }, \"macro\": { \"patterns\": [{ \"match\": \"(&+(?i:[a-z_]([a-z0-9_]+)?)(\\\\.+)?)\\\\b\", \"name\": \"variable.other.macro.sas\" }] }, \"operator\": { \"patterns\": [{ \"match\": \"([+\\\\-\\\\*\\\\^\\\\/])\", \"name\": \"keyword.operator.arithmetic.sas\" }, { \"match\": \"\\\\b(?i:(eq|ne|gt|lt|ge|le|in|not|&|and|or|min|max))\\\\b\", \"name\": \"keyword.operator.comparison.sas\" }, { \"match\": \"([\\xAC<>^~]?=(:)?|>|<|\\\\||!|\\xA6|\\xAC|^|~|<>|><|\\\\|\\\\|)\", \"name\": \"keyword.operator.sas\" }] }, \"quote\": { \"patterns\": [{ \"begin\": \"(?<!%)(')\", \"comment\": \"single quoted string block\", \"end\": \"(')([bx])?\", \"name\": \"string.quoted.single.sas\" }, { \"begin\": '(\")', \"comment\": \"double quoted string block\", \"end\": '(\")([bx])?', \"name\": \"string.quoted.double.sas\" }] }, \"starComment\": { \"patterns\": [{ \"include\": \"#blockcomment\" }, { \"begin\": \"(?<=;)[\\\\s%]*\\\\*\", \"end\": \";\", \"name\": \"comment.line.inline.star.sas\" }, { \"begin\": \"^[\\\\s%]*\\\\*\", \"end\": \";\", \"name\": \"comment.line.start.sas\" }] } }, \"scopeName\": \"source.sas\", \"embeddedLangs\": [\"sql\"] });\nvar sas = [\n  ...sql,\n  lang\n];\n\nexport { sas as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAM;IAAE,sBAAsB;IAA+B,qBAAqB;IAA8B,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,WAAW;YAA2F,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;gBAAmE;aAAE;QAAC;QAAG;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,WAAW;YAAmD,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4E,QAAQ;QAAsB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;YAAuR,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAA8B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,WAAW;YAA2F,OAAO;YAAuB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,QAAQ;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG;YAAE,SAAS;YAAgC,QAAQ;QAAuB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAA4B,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAA4B,QAAQ;QAAwB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAqvJ,QAAQ;QAAuB;KAAE;IAAE,cAAc;QAAE,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,OAAO;oBAAU,QAAQ;gBAA8B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAoB,SAAS;oBAAiD,QAAQ;gBAAuB;gBAAG;oBAAE,WAAW;oBAAsC,SAAS;oBAA0B,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;oBAAsC,SAAS;oBAA0B,QAAQ;gBAAoC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,WAAW;oBAAyB,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,WAAW;oBAA4B,SAAS;gBAA2B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwT,QAAQ;gBAAoB;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0C,QAAQ;gBAA2B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA0D,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA2D,QAAQ;gBAAuB;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,WAAW;oBAA8B,OAAO;oBAAc,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAO,WAAW;oBAA8B,OAAO;oBAAc,QAAQ;gBAA2B;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAoB,OAAO;oBAAK,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAe,OAAO;oBAAK,QAAQ;gBAAyB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAc,iBAAiB;QAAC;KAAM;AAAC;AACvxT,IAAI,MAAM;OACL,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}