{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/toml.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"TOML\", \"fileTypes\": [\"toml\"], \"name\": \"toml\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#groups\" }, { \"include\": \"#key_pair\" }, { \"include\": \"#invalid\" }], \"repository\": { \"comments\": { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.toml\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.toml\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.toml\" }] }, \"groups\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.begin.toml\" }, \"2\": { \"patterns\": [{ \"match\": \"[^\\\\s.]+\", \"name\": \"entity.name.section.toml\" }] }, \"3\": { \"name\": \"punctuation.definition.section.begin.toml\" } }, \"match\": \"^\\\\s*(\\\\[)([^\\\\[\\\\]]*)(\\\\])\", \"name\": \"meta.group.toml\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.begin.toml\" }, \"2\": { \"patterns\": [{ \"match\": \"[^\\\\s.]+\", \"name\": \"entity.name.section.toml\" }] }, \"3\": { \"name\": \"punctuation.definition.section.begin.toml\" } }, \"match\": \"^\\\\s*(\\\\[\\\\[)([^\\\\[\\\\]]*)(\\\\]\\\\])\", \"name\": \"meta.group.double.toml\" }] }, \"invalid\": { \"match\": \"\\\\S+(\\\\s*(?=\\\\S))?\", \"name\": \"invalid.illegal.not-allowed-here.toml\" }, \"key_pair\": { \"patterns\": [{ \"begin\": \"([A-Za-z0-9_-]+)\\\\s*(=)\\\\s*\", \"captures\": { \"1\": { \"name\": \"variable.other.key.toml\" }, \"2\": { \"name\": \"punctuation.separator.key-value.toml\" } }, \"end\": \"(?<=\\\\S)(?<!=)|$\", \"patterns\": [{ \"include\": \"#primatives\" }] }, { \"begin\": '((\")(.*?)(\"))\\\\s*(=)\\\\s*', \"captures\": { \"1\": { \"name\": \"variable.other.key.toml\" }, \"2\": { \"name\": \"punctuation.definition.variable.begin.toml\" }, \"3\": { \"patterns\": [{ \"match\": '\\\\\\\\([btnfr\"\\\\\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape.toml\" }, { \"match\": '\\\\\\\\[^btnfr\"\\\\\\\\]', \"name\": \"invalid.illegal.escape.toml\" }, { \"match\": '\"', \"name\": \"invalid.illegal.not-allowed-here.toml\" }] }, \"4\": { \"name\": \"punctuation.definition.variable.end.toml\" }, \"5\": { \"name\": \"punctuation.separator.key-value.toml\" } }, \"end\": \"(?<=\\\\S)(?<!=)|$\", \"patterns\": [{ \"include\": \"#primatives\" }] }, { \"begin\": \"((')([^']*)('))\\\\s*(=)\\\\s*\", \"captures\": { \"1\": { \"name\": \"variable.other.key.toml\" }, \"2\": { \"name\": \"punctuation.definition.variable.begin.toml\" }, \"4\": { \"name\": \"punctuation.definition.variable.end.toml\" }, \"5\": { \"name\": \"punctuation.separator.key-value.toml\" } }, \"end\": \"(?<=\\\\S)(?<!=)|$\", \"patterns\": [{ \"include\": \"#primatives\" }] }, { \"begin\": `(((?:[A-Za-z0-9_-]+|\"(?:[^\"\\\\\\\\]|\\\\\\\\.)*\"|'[^']*')(?:\\\\s*\\\\.\\\\s*|(?=\\\\s*=))){2,})\\\\s*(=)\\\\s*`, \"captures\": { \"1\": { \"name\": \"variable.other.key.toml\", \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"punctuation.separator.variable.toml\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.begin.toml\" }, \"2\": { \"patterns\": [{ \"match\": '\\\\\\\\([btnfr\"\\\\\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape.toml\" }, { \"match\": '\\\\\\\\[^btnfr\"\\\\\\\\]', \"name\": \"invalid.illegal.escape.toml\" }] }, \"3\": { \"name\": \"punctuation.definition.variable.end.toml\" } }, \"match\": '(\")((?:[^\"\\\\\\\\]|\\\\\\\\.)*)(\")' }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.begin.toml\" }, \"2\": { \"name\": \"punctuation.definition.variable.end.toml\" } }, \"match\": \"(')[^']*(')\" }] }, \"3\": { \"name\": \"punctuation.separator.key-value.toml\" } }, \"comment\": \"Dotted key\", \"end\": \"(?<=\\\\S)(?<!=)|$\", \"patterns\": [{ \"include\": \"#primatives\" }] }] }, \"primatives\": { \"patterns\": [{ \"begin\": '\\\\G\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.toml\" } }, \"end\": '\"{3,5}', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.toml\" } }, \"name\": \"string.quoted.triple.double.toml\", \"patterns\": [{ \"match\": '\\\\\\\\([btnfr\"\\\\\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape.toml\" }, { \"match\": '\\\\\\\\[^btnfr\"\\\\\\\\\\\\n]', \"name\": \"invalid.illegal.escape.toml\" }] }, { \"begin\": '\\\\G\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.toml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.toml\" } }, \"name\": \"string.quoted.double.toml\", \"patterns\": [{ \"match\": '\\\\\\\\([btnfr\"\\\\\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape.toml\" }, { \"match\": '\\\\\\\\[^btnfr\"\\\\\\\\]', \"name\": \"invalid.illegal.escape.toml\" }] }, { \"begin\": \"\\\\G'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.toml\" } }, \"end\": \"'{3,5}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.toml\" } }, \"name\": \"string.quoted.triple.single.toml\" }, { \"begin\": \"\\\\G'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.toml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.toml\" } }, \"name\": \"string.quoted.single.toml\" }, { \"match\": \"\\\\G\\\\d{4}-(0[1-9]|1[012])-(?!00|3[2-9])[0-3]\\\\d([Tt ](?!2[5-9])[0-2]\\\\d:[0-5]\\\\d:(?!6[1-9])[0-6]\\\\d(\\\\.\\\\d+)?(Z|[+-](?!2[5-9])[0-2]\\\\d:[0-5]\\\\d)?)?\", \"name\": \"constant.other.date.toml\" }, { \"match\": \"\\\\G(?!2[5-9])[0-2]\\\\d:[0-5]\\\\d:(?!6[1-9])[0-6]\\\\d(\\\\.\\\\d+)?\", \"name\": \"constant.other.time.toml\" }, { \"match\": \"\\\\G(true|false)\", \"name\": \"constant.language.boolean.toml\" }, { \"match\": \"\\\\G0x\\\\h(\\\\h|_\\\\h)*\", \"name\": \"constant.numeric.hex.toml\" }, { \"match\": \"\\\\G0o[0-7]([0-7]|_[0-7])*\", \"name\": \"constant.numeric.octal.toml\" }, { \"match\": \"\\\\G0b[01]([01]|_[01])*\", \"name\": \"constant.numeric.binary.toml\" }, { \"match\": \"\\\\G[+-]?(inf|nan)\", \"name\": \"constant.numeric.toml\" }, { \"match\": \"\\\\G([+-]?(0|([1-9]((\\\\d|_\\\\d)+)?)))(?=[.eE])(\\\\.(\\\\d((\\\\d|_\\\\d)+)?))?([eE]([+-]?\\\\d((\\\\d|_\\\\d)+)?))?\", \"name\": \"constant.numeric.float.toml\" }, { \"match\": \"\\\\G([+-]?(0|([1-9]((\\\\d|_\\\\d)+)?)))\", \"name\": \"constant.numeric.integer.toml\" }, { \"begin\": \"\\\\G\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.toml\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.toml\" } }, \"name\": \"meta.array.toml\", \"patterns\": [{ \"begin\": `(?=[\"'']|[+-]?\\\\d|[+-]?(inf|nan)|true|false|\\\\[|\\\\{)`, \"end\": \",|(?=])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.array.toml\" } }, \"patterns\": [{ \"include\": \"#primatives\" }, { \"include\": \"#comments\" }, { \"include\": \"#invalid\" }] }, { \"include\": \"#comments\" }, { \"include\": \"#invalid\" }] }, { \"begin\": \"\\\\G\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.inline-table.begin.toml\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.inline-table.end.toml\" } }, \"name\": \"meta.inline-table.toml\", \"patterns\": [{ \"begin\": \"(?=\\\\S)\", \"end\": \",|(?=})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.inline-table.toml\" } }, \"patterns\": [{ \"include\": \"#key_pair\" }] }, { \"include\": \"#comments\" }] }] } }, \"scopeName\": \"source.toml\" });\nvar toml = [\n  lang\n];\n\nexport { toml as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;KAAO;IAAE,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAAgC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAY,QAAQ;gCAA2B;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAA+B,QAAQ;gBAAkB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAY,QAAQ;gCAA2B;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAAqC,QAAQ;gBAAyB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAsB,QAAQ;QAAwC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAsD,QAAQ;gCAAiC;gCAAG;oCAAE,SAAS;oCAAqB,QAAQ;gCAA8B;gCAAG;oCAAE,SAAS;oCAAK,QAAQ;gCAAwC;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,4FAA4F,CAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,SAAS;oCAAO,QAAQ;gCAAsC;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6C;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,SAAS;oDAAsD,QAAQ;gDAAiC;gDAAG;oDAAE,SAAS;oDAAqB,QAAQ;gDAA8B;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAA2C;oCAAE;oCAAG,SAAS;gCAA8B;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6C;wCAAG,KAAK;4CAAE,QAAQ;wCAA2C;oCAAE;oCAAG,SAAS;gCAAc;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,WAAW;oBAAc,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,SAAS;4BAAsD,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAwB,QAAQ;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAsD,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAqB,QAAQ;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAuJ,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAA+D,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAwG,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,SAAS,CAAC,oDAAoD,CAAC;4BAAE,OAAO;4BAAW,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAAW,OAAO;4BAAW,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AACtpN,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}