{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/rel.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Rel\", \"name\": \"rel\", \"patterns\": [{ \"include\": \"#strings\" }, { \"include\": \"#comment\" }, { \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#deprecated-temporary\" }, { \"include\": \"#operators\" }, { \"include\": \"#symbols\" }, { \"include\": \"#keywords\" }, { \"include\": \"#otherkeywords\" }, { \"include\": \"#types\" }, { \"include\": \"#constants\" }], \"repository\": { \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"name\": \"comment.block.documentation.rel\", \"patterns\": [{ \"include\": \"#docblock\" }] }, { \"begin\": \"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.rel\" }, \"2\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"3\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"name\": \"comment.block.rel\" }, { \"begin\": 'doc\"\"\"', \"end\": '\"\"\"', \"name\": \"comment.block.documentation.rel\" }, { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.rel\" }, \"2\": { \"name\": \"comment.line.double-slash.rel\" }, \"3\": { \"name\": \"punctuation.definition.comment.rel\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"contentName\": \"comment.line.double-slash.rel\", \"end\": \"(?=$)\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"(\\\\b(true|false)\\\\b)\", \"name\": \"constant.language.rel\" }] }, \"deprecated-temporary\": { \"patterns\": [{ \"match\": \"@inspect\", \"name\": \"keyword.other.rel\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(\\\\b(def|entity|bound|include|ic|forall|exists|\\u2200|\\u2203|return|module|^end)\\\\b)|(((<)?\\\\|(>)?)|\\u2200|\\u2203)\", \"name\": \"keyword.control.rel\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(\\\\b(if|then|else|and|or|not|eq|neq|lt|lt_eq|gt|gt_eq)\\\\b)|(\\\\+|-|\\\\*|\\\\/|\\xF7|\\\\^|\\\\%|=|!=|\\u2260|<|<=|\\u2264|>|>=|\\u2265|\\\\&)|\\\\s+(end)\", \"name\": \"keyword.other.rel\" }] }, \"otherkeywords\": { \"patterns\": [{ \"match\": \"\\\\s*(@inline)\\\\s*|\\\\s*(@auto_number)\\\\s*|\\\\s*(function)\\\\s|(\\\\b(implies|select|from|\\u2208|where|for|in)\\\\b)|(((<)?\\\\|(>)?)|\\u2208)\", \"name\": \"keyword.other.rel\" }] }, \"single-line-comment-consuming-line-ending\": { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.rel\" }, \"2\": { \"name\": \"comment.line.double-slash.rel\" }, \"3\": { \"name\": \"punctuation.definition.comment.rel\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"contentName\": \"comment.line.double-slash.rel\", \"end\": \"(?=^)\" }, \"strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.rel\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.rel\" }] }, \"symbols\": { \"patterns\": [{ \"match\": \"(:[\\\\[_$A-Za-z](\\\\]|[_$0-9A-Za-z]*))\", \"name\": \"variable.parameter.rel\" }] }, \"types\": { \"patterns\": [{ \"match\": \"(\\\\b(Symbol|Char|Bool|Rational|FixedDecimal|Float16|Float32|Float64|Int8|Int16|Int32|Int64|Int128|UInt8|UInt16|UInt32|UInt64|UInt128|Date|DateTime|Day|Week|Month|Year|Nanosecond|Microsecond|Millisecond|Second|Minute|Hour|FilePos|HashValue|AutoNumberValue)\\\\b)\", \"name\": \"entity.name.type.rel\" }] } }, \"scopeName\": \"source.rel\" });\nvar rel = [\n  lang\n];\n\nexport { rel as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAA6C;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAO,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAoD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,eAAe;oBAAiC,OAAO;gBAAQ;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,QAAQ;gBAAoB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsH,QAAQ;gBAAsB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6I,QAAQ;gBAAoB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuI,QAAQ;gBAAoB;aAAE;QAAC;QAAG,6CAA6C;YAAE,SAAS;YAAoD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,eAAe;YAAiC,OAAO;QAAQ;QAAG,WAAW;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwC,QAAQ;gBAAyB;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuQ,QAAQ;gBAAuB;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AACz+G,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}