{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-menubar_1_1_e943c26f42656451529ab5989f075360_node_mo-80ae9f.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAqCA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-menubar@1.1_e943c26f42656451529ab5989f075360/node_modules/@radix-ui/react-menubar/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/menubar.tsx\nimport * as React from \"react\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as MenuPrimitive from \"@radix-ui/react-menu\";\nimport { createMenuScope } from \"@radix-ui/react-menu\";\nimport * as RovingFocusGroup from \"@radix-ui/react-roving-focus\";\nimport { createRovingFocusGroupScope } from \"@radix-ui/react-roving-focus\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { jsx } from \"react/jsx-runtime\";\nvar MENUBAR_NAME = \"Menubar\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(MENUBAR_NAME);\nvar [createMenubarContext, createMenubarScope] = createContextScope(MENUBAR_NAME, [\n  createCollectionScope,\n  createRovingFocusGroupScope\n]);\nvar useMenuScope = createMenuScope();\nvar useRovingFocusGroupScope = createRovingFocusGroupScope();\nvar [MenubarContextProvider, useMenubarContext] = createMenubarContext(MENUBAR_NAME);\nvar Menubar = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeMenubar,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      loop = true,\n      dir,\n      ...menubarProps\n    } = props;\n    const direction = useDirection(dir);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? \"\",\n      caller: MENUBAR_NAME\n    });\n    const [currentTabStopId, setCurrentTabStopId] = React.useState(null);\n    return /* @__PURE__ */ jsx(\n      MenubarContextProvider,\n      {\n        scope: __scopeMenubar,\n        value,\n        onMenuOpen: React.useCallback(\n          (value2) => {\n            setValue(value2);\n            setCurrentTabStopId(value2);\n          },\n          [setValue]\n        ),\n        onMenuClose: React.useCallback(() => setValue(\"\"), [setValue]),\n        onMenuToggle: React.useCallback(\n          (value2) => {\n            setValue((prevValue) => prevValue ? \"\" : value2);\n            setCurrentTabStopId(value2);\n          },\n          [setValue]\n        ),\n        dir: direction,\n        loop,\n        children: /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeMenubar, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeMenubar, children: /* @__PURE__ */ jsx(\n          RovingFocusGroup.Root,\n          {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            orientation: \"horizontal\",\n            loop,\n            dir: direction,\n            currentTabStopId,\n            onCurrentTabStopIdChange: setCurrentTabStopId,\n            children: /* @__PURE__ */ jsx(Primitive.div, { role: \"menubar\", ...menubarProps, ref: forwardedRef })\n          }\n        ) }) })\n      }\n    );\n  }\n);\nMenubar.displayName = MENUBAR_NAME;\nvar MENU_NAME = \"MenubarMenu\";\nvar [MenubarMenuProvider, useMenubarMenuContext] = createMenubarContext(MENU_NAME);\nvar MenubarMenu = (props) => {\n  const { __scopeMenubar, value: valueProp, ...menuProps } = props;\n  const autoValue = useId();\n  const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n  const context = useMenubarContext(MENU_NAME, __scopeMenubar);\n  const menuScope = useMenuScope(__scopeMenubar);\n  const triggerRef = React.useRef(null);\n  const wasKeyboardTriggerOpenRef = React.useRef(false);\n  const open = context.value === value;\n  React.useEffect(() => {\n    if (!open) wasKeyboardTriggerOpenRef.current = false;\n  }, [open]);\n  return /* @__PURE__ */ jsx(\n    MenubarMenuProvider,\n    {\n      scope: __scopeMenubar,\n      value,\n      triggerId: useId(),\n      triggerRef,\n      contentId: useId(),\n      wasKeyboardTriggerOpenRef,\n      children: /* @__PURE__ */ jsx(\n        MenuPrimitive.Root,\n        {\n          ...menuScope,\n          open,\n          onOpenChange: (open2) => {\n            if (!open2) context.onMenuClose();\n          },\n          modal: false,\n          dir: context.dir,\n          ...menuProps\n        }\n      )\n    }\n  );\n};\nMenubarMenu.displayName = MENU_NAME;\nvar TRIGGER_NAME = \"MenubarTrigger\";\nvar MenubarTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, disabled = false, ...triggerProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(TRIGGER_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(TRIGGER_NAME, __scopeMenubar);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, menuContext.triggerRef);\n    const [isFocused, setIsFocused] = React.useState(false);\n    const open = context.value === menuContext.value;\n    return /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeMenubar, value: menuContext.value, disabled, children: /* @__PURE__ */ jsx(\n      RovingFocusGroup.Item,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        tabStopId: menuContext.value,\n        children: /* @__PURE__ */ jsx(MenuPrimitive.Anchor, { asChild: true, ...menuScope, children: /* @__PURE__ */ jsx(\n          Primitive.button,\n          {\n            type: \"button\",\n            role: \"menuitem\",\n            id: menuContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": open,\n            \"aria-controls\": open ? menuContext.contentId : void 0,\n            \"data-highlighted\": isFocused ? \"\" : void 0,\n            \"data-state\": open ? \"open\" : \"closed\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n              if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                context.onMenuOpen(menuContext.value);\n                if (!open) event.preventDefault();\n              }\n            }),\n            onPointerEnter: composeEventHandlers(props.onPointerEnter, () => {\n              const menubarOpen = Boolean(context.value);\n              if (menubarOpen && !open) {\n                context.onMenuOpen(menuContext.value);\n                ref.current?.focus();\n              }\n            }),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if (disabled) return;\n              if ([\"Enter\", \" \"].includes(event.key)) context.onMenuToggle(menuContext.value);\n              if (event.key === \"ArrowDown\") context.onMenuOpen(menuContext.value);\n              if ([\"Enter\", \" \", \"ArrowDown\"].includes(event.key)) {\n                menuContext.wasKeyboardTriggerOpenRef.current = true;\n                event.preventDefault();\n              }\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => setIsFocused(true)),\n            onBlur: composeEventHandlers(props.onBlur, () => setIsFocused(false))\n          }\n        ) })\n      }\n    ) });\n  }\n);\nMenubarTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"MenubarPortal\";\nvar MenubarPortal = (props) => {\n  const { __scopeMenubar, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Portal, { ...menuScope, ...portalProps });\n};\nMenubarPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenubarContent\";\nvar MenubarContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, align = \"start\", ...contentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(CONTENT_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(CONTENT_NAME, __scopeMenubar);\n    const getItems = useCollection(__scopeMenubar);\n    const hasInteractedOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.Content,\n      {\n        id: menuContext.contentId,\n        \"aria-labelledby\": menuContext.triggerId,\n        \"data-radix-menubar-content\": \"\",\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        align,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          const menubarOpen = Boolean(context.value);\n          if (!menubarOpen && !hasInteractedOutsideRef.current) {\n            menuContext.triggerRef.current?.focus();\n          }\n          hasInteractedOutsideRef.current = false;\n          event.preventDefault();\n        }),\n        onFocusOutside: composeEventHandlers(props.onFocusOutside, (event) => {\n          const target = event.target;\n          const isMenubarTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          if (isMenubarTrigger) event.preventDefault();\n        }),\n        onInteractOutside: composeEventHandlers(props.onInteractOutside, () => {\n          hasInteractedOutsideRef.current = true;\n        }),\n        onEntryFocus: (event) => {\n          if (!menuContext.wasKeyboardTriggerOpenRef.current) event.preventDefault();\n        },\n        onKeyDown: composeEventHandlers(\n          props.onKeyDown,\n          (event) => {\n            if ([\"ArrowRight\", \"ArrowLeft\"].includes(event.key)) {\n              const target = event.target;\n              const targetIsSubTrigger = target.hasAttribute(\"data-radix-menubar-subtrigger\");\n              const isKeyDownInsideSubMenu = target.closest(\"[data-radix-menubar-content]\") !== event.currentTarget;\n              const prevMenuKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n              const isPrevKey = prevMenuKey === event.key;\n              const isNextKey = !isPrevKey;\n              if (isNextKey && targetIsSubTrigger) return;\n              if (isKeyDownInsideSubMenu && isPrevKey) return;\n              const items = getItems().filter((item) => !item.disabled);\n              let candidateValues = items.map((item) => item.value);\n              if (isPrevKey) candidateValues.reverse();\n              const currentIndex = candidateValues.indexOf(menuContext.value);\n              candidateValues = context.loop ? wrapArray(candidateValues, currentIndex + 1) : candidateValues.slice(currentIndex + 1);\n              const [nextValue] = candidateValues;\n              if (nextValue) context.onMenuOpen(nextValue);\n            }\n          },\n          { checkForDefaultPrevented: false }\n        ),\n        style: {\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            \"--radix-menubar-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n            \"--radix-menubar-content-available-width\": \"var(--radix-popper-available-width)\",\n            \"--radix-menubar-content-available-height\": \"var(--radix-popper-available-height)\",\n            \"--radix-menubar-trigger-width\": \"var(--radix-popper-anchor-width)\",\n            \"--radix-menubar-trigger-height\": \"var(--radix-popper-anchor-height)\"\n          }\n        }\n      }\n    );\n  }\n);\nMenubarContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenubarGroup\";\nvar MenubarGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Group, { ...menuScope, ...groupProps, ref: forwardedRef });\n  }\n);\nMenubarGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenubarLabel\";\nvar MenubarLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Label, { ...menuScope, ...labelProps, ref: forwardedRef });\n  }\n);\nMenubarLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenubarItem\";\nvar MenubarItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Item, { ...menuScope, ...itemProps, ref: forwardedRef });\n  }\n);\nMenubarItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"MenubarCheckboxItem\";\nvar MenubarCheckboxItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.CheckboxItem, { ...menuScope, ...checkboxItemProps, ref: forwardedRef });\n  }\n);\nMenubarCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenubarRadioGroup\";\nvar MenubarRadioGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.RadioGroup, { ...menuScope, ...radioGroupProps, ref: forwardedRef });\n  }\n);\nMenubarRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenubarRadioItem\";\nvar MenubarRadioItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.RadioItem, { ...menuScope, ...radioItemProps, ref: forwardedRef });\n  }\n);\nMenubarRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"MenubarItemIndicator\";\nvar MenubarItemIndicator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeMenubar, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return /* @__PURE__ */ jsx(MenuPrimitive.ItemIndicator, { ...menuScope, ...itemIndicatorProps, ref: forwardedRef });\n});\nMenubarItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenubarSeparator\";\nvar MenubarSeparator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Separator, { ...menuScope, ...separatorProps, ref: forwardedRef });\n  }\n);\nMenubarSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenubarArrow\";\nvar MenubarArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Arrow, { ...menuScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nMenubarArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenubarSub\";\nvar MenubarSub = (props) => {\n  const { __scopeMenubar, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SUB_NAME\n  });\n  return /* @__PURE__ */ jsx(MenuPrimitive.Sub, { ...menuScope, open, onOpenChange: setOpen, children });\n};\nMenubarSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenubarSubTrigger\";\nvar MenubarSubTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.SubTrigger,\n      {\n        \"data-radix-menubar-subtrigger\": \"\",\n        ...menuScope,\n        ...subTriggerProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nMenubarSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenubarSubContent\";\nvar MenubarSubContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenubar, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.SubContent,\n      {\n        ...menuScope,\n        \"data-radix-menubar-content\": \"\",\n        ...subContentProps,\n        ref: forwardedRef,\n        style: {\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            \"--radix-menubar-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n            \"--radix-menubar-content-available-width\": \"var(--radix-popper-available-width)\",\n            \"--radix-menubar-content-available-height\": \"var(--radix-popper-available-height)\",\n            \"--radix-menubar-trigger-width\": \"var(--radix-popper-anchor-width)\",\n            \"--radix-menubar-trigger-height\": \"var(--radix-popper-anchor-height)\"\n          }\n        }\n      }\n    );\n  }\n);\nMenubarSubContent.displayName = SUB_CONTENT_NAME;\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root3 = Menubar;\nvar Menu = MenubarMenu;\nvar Trigger = MenubarTrigger;\nvar Portal2 = MenubarPortal;\nvar Content2 = MenubarContent;\nvar Group2 = MenubarGroup;\nvar Label2 = MenubarLabel;\nvar Item3 = MenubarItem;\nvar CheckboxItem2 = MenubarCheckboxItem;\nvar RadioGroup2 = MenubarRadioGroup;\nvar RadioItem2 = MenubarRadioItem;\nvar ItemIndicator2 = MenubarItemIndicator;\nvar Separator2 = MenubarSeparator;\nvar Arrow2 = MenubarArrow;\nvar Sub2 = MenubarSub;\nvar SubTrigger2 = MenubarSubTrigger;\nvar SubContent2 = MenubarSubContent;\nexport {\n  Arrow2 as Arrow,\n  CheckboxItem2 as CheckboxItem,\n  Content2 as Content,\n  Group2 as Group,\n  Item3 as Item,\n  ItemIndicator2 as ItemIndicator,\n  Label2 as Label,\n  Menu,\n  Menubar,\n  MenubarArrow,\n  MenubarCheckboxItem,\n  MenubarContent,\n  MenubarGroup,\n  MenubarItem,\n  MenubarItemIndicator,\n  MenubarLabel,\n  MenubarMenu,\n  MenubarPortal,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarSeparator,\n  MenubarSub,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarTrigger,\n  Portal2 as Portal,\n  RadioGroup2 as RadioGroup,\n  RadioItem2 as RadioItem,\n  Root3 as Root,\n  Separator2 as Separator,\n  Sub2 as Sub,\n  SubContent2 as SubContent,\n  SubTrigger2 as SubTrigger,\n  Trigger,\n  createMenubarScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}