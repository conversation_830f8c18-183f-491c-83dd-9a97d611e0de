{"extension.displayName": "cubent Code (पहले cubent Cline)", "extension.description": "आपके एडिटर में एआई एजेंट्स की पूरी डेवलपमेंट टीम।", "command.newTask.title": "नया कार्य", "command.explainCode.title": "कोड समझाएं", "command.fixCode.title": "कोड ठीक करें", "command.improveCode.title": "कोड सुधारें", "command.addToContext.title": "संदर्भ में जोड़ें", "command.openInNewTab.title": "नए टैब में खोलें", "command.focusInput.title": "इनपुट फ़ील्ड पर फोकस करें", "command.setCustomStoragePath.title": "कस्टम स्टोरेज पाथ सेट करें", "command.terminal.addToContext.title": "टर्मिनल सामग्री को संदर्भ में जोड़ें", "command.terminal.fixCommand.title": "यह कमांड ठीक करें", "command.terminal.explainCommand.title": "यह कमांड समझाएं", "command.acceptInput.title": "इनपुट/सुझाव स्वीकारें", "views.activitybar.title": "cubent Code", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent Code", "command.mcpServers.title": "एमसीपी सर्वर", "command.prompts.title": "मोड्स", "command.history.title": "इतिहास", "command.openInEditor.title": "एडिटर में खोलें", "command.settings.title": "सेटिंग्स", "command.documentation.title": "दस्तावेज़ीकरण", "configuration.title": "cubent Code", "commands.allowedCommands.description": "वे कमांड जो स्वचालित रूप से निष्पादित की जा सकती हैं जब 'हमेशा निष्पादन संचालन को स्वीकृत करें' सक्रिय हो", "settings.vsCodeLmModelSelector.description": "VSCode भाषा मॉडल API के लिए सेटिंग्स", "settings.vsCodeLmModelSelector.vendor.description": "भाषा मॉडल का विक्रेता (उदा. copilot)", "settings.vsCodeLmModelSelector.family.description": "भाषा मॉडल का परिवार (उदा. gpt-4)", "settings.customStoragePath.description": "कस्टम स्टोरेज पाथ। डिफ़ॉल्ट स्थान का उपयोग करने के लिए खाली छोड़ें। पूर्ण पथ का समर्थन करता है (उदा. 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "cubent coder Cloud सक्षम करें।"}