{"extension.displayName": "Cubent.Dev", "extension.description": "在你的编辑器中提供完整的 AI 代理开发团队。", "command.newTask.title": "新建任务", "command.explainCode.title": "解释代码", "command.fixCode.title": "修复代码", "command.improveCode.title": "改进代码", "command.addToContext.title": "添加到上下文", "command.openInNewTab.title": "在新标签页中打开", "command.focusInput.title": "聚焦输入框", "command.setCustomStoragePath.title": "设置自定义存储路径", "command.terminal.addToContext.title": "将终端内容添加到上下文", "command.terminal.fixCommand.title": "修复此命令", "command.terminal.explainCommand.title": "解释此命令", "command.acceptInput.title": "接受输入/建议", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "Send to Cubent", "views.terminalMenu.label": "Send to Cubent", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP 服务器", "command.prompts.title": "模式", "command.history.title": "历史记录", "command.openInEditor.title": "在编辑器中打开", "command.settings.title": "设置", "command.documentation.title": "文档", "configuration.title": "cubent coder", "commands.allowedCommands.description": "当启用'始终批准执行操作'时可以自动执行的命令", "settings.vsCodeLmModelSelector.description": "VSCode 语言模型 API 的设置", "settings.vsCodeLmModelSelector.vendor.description": "语言模型的供应商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "语言模型的系列（例如：gpt-4）", "settings.customStoragePath.description": "自定义存储路径。留空以使用默认位置。支持绝对路径（例如：'D:\\cubentCoderStorage'）", "settings.cubentCoderCloudEnabled.description": "启用 cubent coder Cloud。"}