{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/dotenv.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"dotEnv\", \"name\": \"dotenv\", \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#line-comment\" }] } }, \"comment\": \"Full Line Comment\", \"match\": \"^\\\\s?(#.*$)\\\\n\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#key\" }] }, \"2\": { \"name\": \"keyword.operator.assignment.dotenv\" }, \"3\": { \"name\": \"property.value.dotenv\", \"patterns\": [{ \"include\": \"#line-comment\" }, { \"include\": \"#double-quoted-string\" }, { \"include\": \"#single-quoted-string\" }, { \"include\": \"#interpolation\" }] } }, \"comment\": \"ENV entry\", \"match\": \"^\\\\s?(.*?)\\\\s?(=)(.*)$\" }], \"repository\": { \"double-quoted-string\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#escape-characters\" }] } }, \"comment\": \"Double Quoted String\", \"match\": '\"(.*)\"', \"name\": \"string.quoted.double.dotenv\" }, \"escape-characters\": { \"comment\": \"Escape characters\", \"match\": `\\\\\\\\[nrtfb\"'\\\\\\\\]|\\\\\\\\u[0123456789ABCDEF]{4}`, \"name\": \"constant.character.escape.dotenv\" }, \"interpolation\": { \"captures\": { \"1\": { \"name\": \"keyword.interpolation.begin.dotenv\" }, \"2\": { \"name\": \"variable.interpolation.dotenv\" }, \"3\": { \"name\": \"keyword.interpolation.end.dotenv\" } }, \"comment\": \"Interpolation (variable substitution)\", \"match\": \"(\\\\$\\\\{)(.*)(\\\\})\" }, \"key\": { \"captures\": { \"1\": { \"name\": \"keyword.key.export.dotenv\" }, \"2\": { \"name\": \"variable.key.dotenv\", \"patterns\": [{ \"include\": \"#variable\" }] } }, \"comment\": \"Key\", \"match\": \"(export\\\\s)?(.*)\" }, \"line-comment\": { \"comment\": \"Comment\", \"match\": \"#.*$\", \"name\": \"comment.line.dotenv\" }, \"single-quoted-string\": { \"comment\": \"Single Quoted String\", \"match\": \"'(.*)'\", \"name\": \"string.quoted.single.dotenv\" }, \"variable\": { \"comment\": \"env variable\", \"match\": \"[a-zA-Z_]+\\\\w*\" } }, \"scopeName\": \"source.dotenv\" });\nvar dotenv = [\n  lang\n];\n\nexport { dotenv as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,QAAQ;IAAU,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAqB,SAAS;QAAiB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAO;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAa,SAAS;QAAyB;KAAE;IAAE,cAAc;QAAE,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAwB,SAAS;YAAU,QAAQ;QAA8B;QAAG,qBAAqB;YAAE,WAAW;YAAqB,SAAS,CAAC,4CAA4C,CAAC;YAAE,QAAQ;QAAmC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,WAAW;YAAyC,SAAS;QAAoB;QAAG,OAAO;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAO,SAAS;QAAmB;QAAG,gBAAgB;YAAE,WAAW;YAAW,SAAS;YAAQ,QAAQ;QAAsB;QAAG,wBAAwB;YAAE,WAAW;YAAwB,SAAS;YAAU,QAAQ;QAA8B;QAAG,YAAY;YAAE,WAAW;YAAgB,SAAS;QAAiB;IAAE;IAAG,aAAa;AAAgB;AACpwD,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}