{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/berry.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"<PERSON>\", \"name\": \"berry\", \"patterns\": [{ \"include\": \"#controls\" }, { \"include\": \"#strings\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#function\" }, { \"include\": \"#member\" }, { \"include\": \"#identifier\" }, { \"include\": \"#number\" }, { \"include\": \"#operator\" }], \"repository\": { \"comment-block\": { \"begin\": \"\\\\#-\", \"end\": \"-#\", \"name\": \"comment.berry\", \"patterns\": [{}] }, \"comments\": { \"begin\": \"\\\\#\", \"end\": \"\\\\n\", \"name\": \"comment.line.berry\", \"patterns\": [{}] }, \"controls\": { \"patterns\": [{ \"match\": \"\\\\b(if|elif|else|for|while|do|end|break|continue|return|try|except|raise)\\\\b\", \"name\": \"keyword.control.berry\" }] }, \"function\": { \"patterns\": [{ \"match\": \"\\\\b([a-zA-Z_]\\\\w*(?=\\\\s*\\\\())\", \"name\": \"entity.name.function.berry\" }] }, \"identifier\": { \"patterns\": [{ \"match\": \"\\\\b[_A-Za-z]\\\\w+\\\\b\", \"name\": \"identifier.berry\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(var|static|def|class|true|false|nil|self|super|import|as|_class)\\\\b\", \"name\": \"keyword.berry\" }] }, \"member\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"entity.other.attribute-name.berry\" } }, \"match\": \"\\\\.([a-zA-Z_]\\\\w*)\" }] }, \"number\": { \"patterns\": [{ \"match\": \"0x[a-fA-F0-9]+|\\\\d+|(\\\\d+\\\\.?|\\\\.\\\\d)\\\\d*([eE][+-]?\\\\d+)?\", \"name\": \"constant.numeric.berry\" }] }, \"operator\": { \"patterns\": [{ \"match\": \"\\\\(|\\\\)|\\\\[|\\\\]|\\\\.|-|!|~|\\\\*|/|%|\\\\+|&|\\\\^|\\\\||<|>|=|:\", \"name\": \"keyword.operator.berry\" }] }, \"strings\": { \"patterns\": [{ \"begin\": `(\"|')`, \"end\": \"\\\\1\", \"name\": \"string.quoted.double.berry\", \"patterns\": [{ \"match\": `(\\\\\\\\x[\\\\h]{2})|(\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\)|(\\\\\\\\\")|(\\\\\\\\')|(\\\\\\\\a)|(\\\\\\\\b)|(\\\\\\\\f)|(\\\\\\\\n)|(\\\\\\\\r)|(\\\\\\\\t)|(\\\\\\\\v)`, \"name\": \"constant.character.escape.berry\" }] }, { \"begin\": `f(\"|')`, \"end\": \"\\\\1\", \"name\": \"string.quoted.other.berry\", \"patterns\": [{ \"match\": `(\\\\\\\\x[\\\\h]{2})|(\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\)|(\\\\\\\\\")|(\\\\\\\\')|(\\\\\\\\a)|(\\\\\\\\b)|(\\\\\\\\f)|(\\\\\\\\n)|(\\\\\\\\r)|(\\\\\\\\t)|(\\\\\\\\v)`, \"name\": \"constant.character.escape.berry\" }, { \"match\": \"\\\\{\\\\{[^}]*\\\\}\\\\}\", \"name\": \"string.quoted.other.berry\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"name\": \"keyword.other.unit.berry\", \"patterns\": [{ \"include\": \"#keywords\" }, { \"include\": \"#numbers\" }, { \"include\": \"#identifier\" }, { \"include\": \"#operator\" }, { \"include\": \"#member\" }, { \"include\": \"#function\" }] }] }] } }, \"scopeName\": \"source.berry\", \"aliases\": [\"be\"] });\nvar berry = [\n  lang\n];\n\nexport { berry as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAQ,OAAO;YAAM,QAAQ;YAAiB,YAAY;gBAAC,CAAC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAAsB,YAAY;gBAAC,CAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgF,QAAQ;gBAAwB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiC,QAAQ;gBAA6B;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAAmB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2E,QAAQ;gBAAgB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAqB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,QAAQ;gBAAyB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2D,QAAQ;gBAAyB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,KAAK,CAAC;oBAAE,OAAO;oBAAO,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS,CAAC,iHAAiH,CAAC;4BAAE,QAAQ;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,MAAM,CAAC;oBAAE,OAAO;oBAAO,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS,CAAC,iHAAiH,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAqB,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAO,OAAO;4BAAO,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,WAAW;QAAC;KAAK;AAAC;AACt2E,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}