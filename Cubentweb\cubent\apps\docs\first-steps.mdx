---
title: Your First Steps
description: 'Learn the basics of Cubent Coder with hands-on examples'
---

## Welcome to Cubent Coder!

Now that you have Cubent Coder installed, let's walk through your first interactions and learn the core concepts that will make you productive immediately.

## Understanding the Interface

### Sidebar Panel

The Cubent Coder panel appears in your VS Code sidebar with these key elements:

- **Chat Interface**: Where you communicate with the AI
- **Mode Selector**: Switch between Chat, Plan, Agent, and Custom modes
- **Task History**: Review previous conversations and tasks
- **Settings**: Quick access to configuration options

### Status Indicators

Look for these visual cues:
- 🟢 **Green**: AI is ready and connected
- 🟡 **Yellow**: Processing your request
- 🔴 **Red**: Connection or configuration issue

## Your First Conversation

Let's start with a simple interaction:

### Step 1: Open the Chat

1. Click the Cubent Coder icon in the sidebar
2. You'll see the chat interface
3. Type your first message in the input box

### Step 2: Try Basic Commands

Start with these simple examples:

```
Hello! Can you help me understand this codebase?
```

```
What programming languages do you see in this project?
```

```
Can you explain what this function does?
```

<Note>
Select some code in your editor before asking about it - Cubent Coder will automatically include the selected code in its context.
</Note>

## Code Generation Basics

### Simple Code Generation

Try generating some code:

```
Create a Python function that calculates the factorial of a number
```

```
Write a React component for a simple todo list
```

```
Generate a SQL query to find users who registered in the last 30 days
```

### Code Explanation

Select any code in your editor and ask:

```
Explain this code to me
```

```
What could be improved in this function?
```

```
Are there any potential bugs here?
```

## File Operations

Cubent Coder can work with files in your workspace:

### Reading Files

```
What's in the package.json file?
```

```
Show me the main entry point of this application
```

### Creating Files

```
Create a new README.md file for this project
```

```
Generate a .gitignore file for a Node.js project
```

### Modifying Files

```
Add error handling to the main.js file
```

```
Update the CSS to make the header responsive
```

## Understanding Modes

### Chat Mode (Default)

- Best for: Questions, explanations, code reviews
- Behavior: Conversational, informative
- Use when: You want to understand or discuss code

### Plan Mode

- Best for: Project planning, architecture decisions
- Behavior: Strategic, high-level thinking
- Use when: You need technical leadership guidance

### Agent Mode

- Best for: Complex tasks, multi-step operations
- Behavior: Action-oriented, autonomous
- Use when: You want Cubent Coder to implement solutions

## Best Practices

### Be Specific

Instead of: "Fix this code"
Try: "This function is throwing a null pointer exception when the input array is empty. Can you add proper error handling?"

### Provide Context

- Select relevant code before asking questions
- Mention the programming language and framework
- Explain what you're trying to achieve

### Use Progressive Refinement

Start with a general request, then refine:

1. "Create a user authentication system"
2. "Add password validation with specific requirements"
3. "Include email verification functionality"

## Common First Tasks

Here are some typical tasks new users try:

### Code Review

```
Review this function for potential improvements and security issues
```

### Documentation

```
Generate documentation for this API endpoint
```

### Testing

```
Create unit tests for this class
```

### Refactoring

```
Refactor this code to follow better design patterns
```

## Getting Help

If you're stuck or need clarification:

### In-App Help

```
How do I switch to Agent mode?
```

```
What's the difference between Chat and Plan mode?
```

### Community Resources

- **Discord**: [Join our community](https://discord.gg/cubent) for real-time help
- **Documentation**: Browse these docs for detailed guides
- **GitHub**: [Report issues](https://github.com/LaxBloxBoy2/Cubent/issues) or request features

## Next Steps

Ready to dive deeper? Explore these advanced features:

<CardGroup cols={2}>
  <Card
    title="Master Agent Mode"
    icon="robot"
    href="/features/agent-mode"
  >
    Let Cubent Coder work autonomously on complex tasks
  </Card>
  <Card
    title="Create Custom Modes"
    icon="gear"
    href="/features/custom-modes"
  >
    Build specialized AI personas for your workflow
  </Card>
  <Card
    title="Terminal Integration"
    icon="terminal"
    href="/features/terminal-integration"
  >
    Execute commands and manage your development environment
  </Card>
  <Card
    title="Advanced Automation"
    icon="magic"
    href="/advanced/automation"
  >
    Set up complex workflows and automation
  </Card>
</CardGroup>

<Tip>
Remember: Cubent Coder learns from your interactions. The more you use it, the better it becomes at understanding your coding style and preferences!
</Tip>
