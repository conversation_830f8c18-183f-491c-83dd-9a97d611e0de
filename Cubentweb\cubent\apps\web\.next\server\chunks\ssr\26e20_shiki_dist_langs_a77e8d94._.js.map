{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/diff.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Diff\", \"name\": \"diff\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.separator.diff\" } }, \"match\": \"^((\\\\*{15})|(={67})|(-{3}))$\\\\n?\", \"name\": \"meta.separator.diff\" }, { \"match\": \"^\\\\d+(,\\\\d+)*(a|d|c)\\\\d+(,\\\\d+)*$\\\\n?\", \"name\": \"meta.diff.range.normal\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.range.diff\" }, \"2\": { \"name\": \"meta.toc-list.line-number.diff\" }, \"3\": { \"name\": \"punctuation.definition.range.diff\" } }, \"match\": \"^(@@)\\\\s*(.+?)\\\\s*(@@)($\\\\n?)?\", \"name\": \"meta.diff.range.unified\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.range.diff\" }, \"4\": { \"name\": \"punctuation.definition.range.diff\" }, \"6\": { \"name\": \"punctuation.definition.range.diff\" }, \"7\": { \"name\": \"punctuation.definition.range.diff\" } }, \"match\": \"^(((-{3}) .+ (-{4}))|((\\\\*{3}) .+ (\\\\*{4})))$\\\\n?\", \"name\": \"meta.diff.range.context\" }, { \"match\": \"^diff --git a/.*$\\\\n?\", \"name\": \"meta.diff.header.git\" }, { \"match\": \"^diff (-|\\\\S+\\\\s+\\\\S+).*$\\\\n?\", \"name\": \"meta.diff.header.command\" }, { \"captures\": { \"4\": { \"name\": \"punctuation.definition.from-file.diff\" }, \"6\": { \"name\": \"punctuation.definition.from-file.diff\" }, \"7\": { \"name\": \"punctuation.definition.from-file.diff\" } }, \"match\": \"(^(((-{3}) .+)|((\\\\*{3}) .+))$\\\\n?|^(={4}) .+(?= - ))\", \"name\": \"meta.diff.header.from-file\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.definition.to-file.diff\" }, \"3\": { \"name\": \"punctuation.definition.to-file.diff\" }, \"4\": { \"name\": \"punctuation.definition.to-file.diff\" } }, \"match\": \"(^(\\\\+{3}) .+$\\\\n?| (-) .* (={4})$\\\\n?)\", \"name\": \"meta.diff.header.to-file\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.inserted.diff\" }, \"6\": { \"name\": \"punctuation.definition.inserted.diff\" } }, \"match\": \"^(((>)( .*)?)|((\\\\+).*))$\\\\n?\", \"name\": \"markup.inserted.diff\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.changed.diff\" } }, \"match\": \"^(!).*$\\\\n?\", \"name\": \"markup.changed.diff\" }, { \"captures\": { \"3\": { \"name\": \"punctuation.definition.deleted.diff\" }, \"6\": { \"name\": \"punctuation.definition.deleted.diff\" } }, \"match\": \"^(((<)( .*)?)|((-).*))$\\\\n?\", \"name\": \"markup.deleted.diff\" }, { \"begin\": \"^(#)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.diff\" } }, \"comment\": 'Git produces unified diffs with embedded comments\"', \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.diff\" }, { \"match\": \"^index [0-9a-f]{7,40}\\\\.\\\\.[0-9a-f]{7,40}.*$\\\\n?\", \"name\": \"meta.diff.index.git\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.key-value.diff\" }, \"2\": { \"name\": \"meta.toc-list.file-name.diff\" } }, \"match\": \"^Index(:) (.+)$\\\\n?\", \"name\": \"meta.diff.index\" }, { \"match\": \"^Only in .*: .*$\\\\n?\", \"name\": \"meta.diff.only-in\" }], \"scopeName\": \"source.diff\" });\nvar diff = [\n  lang\n];\n\nexport { diff as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAoC,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAyC,QAAQ;QAAyB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAkC,QAAQ;QAA0B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAqD,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAyB,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAiC,QAAQ;QAA2B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAyD,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2C,QAAQ;QAA2B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;YAAiC,QAAQ;QAAuB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAe,QAAQ;QAAsB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA+B,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,WAAW;YAAsD,OAAO;YAAO,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAoD,QAAQ;QAAsB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;YAAuB,QAAQ;QAAkB;QAAG;YAAE,SAAS;YAAwB,QAAQ;QAAoB;KAAE;IAAE,aAAa;AAAc;AACjtF,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/git-commit.mjs"], "sourcesContent": ["import diff from './diff.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Git Commit Message\", \"name\": \"git-commit\", \"patterns\": [{ \"begin\": \"(?=^diff --git)\", \"comment\": \"diff presented at the end of the commit message when using commit -v.\", \"contentName\": \"source.diff\", \"end\": \"\\\\z\", \"name\": \"meta.embedded.diff.git-commit\", \"patterns\": [{ \"include\": \"source.diff\" }] }, { \"begin\": \"^(?!#)\", \"comment\": \"User supplied message\", \"end\": \"^(?=#)\", \"name\": \"meta.scope.message.git-commit\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.deprecated.line-too-long.git-commit\" }, \"2\": { \"name\": \"invalid.illegal.line-too-long.git-commit\" } }, \"comment\": \"Mark > 50 lines as deprecated, > 72 as illegal\", \"match\": \"\\\\G.{0,50}(.{0,22}(.*))$\", \"name\": \"meta.scope.subject.git-commit\" }] }, { \"begin\": \"^(?=#)\", \"comment\": \"Git supplied metadata in a number of lines starting with #\", \"contentName\": \"comment.line.number-sign.git-commit\", \"end\": \"^(?!#)\", \"name\": \"meta.scope.metadata.git-commit\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"markup.changed.git-commit\" } }, \"match\": \"^#\\\\t((modified|renamed):.*)$\" }, { \"captures\": { \"1\": { \"name\": \"markup.inserted.git-commit\" } }, \"match\": \"^#\\\\t(new file:.*)$\" }, { \"captures\": { \"1\": { \"name\": \"markup.deleted.git-commit\" } }, \"match\": \"^#\\\\t(deleted.*)$\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.file-type.git-commit\" }, \"2\": { \"name\": \"string.unquoted.filename.git-commit\" } }, \"comment\": \"Fallback for non-English git commit template\", \"match\": \"^#\\\\t([^:]+): *(.*)$\" }] }], \"scopeName\": \"text.git-commit\", \"embeddedLangs\": [\"diff\"] });\nvar gitCommit = [\n  ...diff,\n  lang\n];\n\nexport { gitCommit as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAsB,QAAQ;IAAc,YAAY;QAAC;YAAE,SAAS;YAAmB,WAAW;YAAyE,eAAe;YAAe,OAAO;YAAO,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG;YAAE,SAAS;YAAU,WAAW;YAAyB,OAAO;YAAU,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,WAAW;oBAAkD,SAAS;oBAA4B,QAAQ;gBAAgC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAU,WAAW;YAA8D,eAAe;YAAuC,OAAO;YAAU,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAAsB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAoB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,WAAW;oBAAgD,SAAS;gBAAuB;aAAE;QAAC;KAAE;IAAE,aAAa;IAAmB,iBAAiB;QAAC;KAAO;AAAC;AACjiD,IAAI,YAAY;OACX,kMAAA,CAAA,UAAI;IACP;CACD", "ignoreList": [0], "debugId": null}}]}