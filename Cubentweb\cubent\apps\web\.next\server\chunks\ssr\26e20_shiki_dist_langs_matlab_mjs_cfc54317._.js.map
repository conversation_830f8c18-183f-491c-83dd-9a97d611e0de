{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/matlab.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"MATLAB\", \"fileTypes\": [\"m\"], \"name\": \"matlab\", \"patterns\": [{ \"comment\": \"This and #all_after_command_dual are split out so #command_dual can be excluded in things like (), {}, []\", \"include\": \"#all_before_command_dual\" }, { \"include\": \"#command_dual\" }, { \"include\": \"#all_after_command_dual\" }], \"repository\": { \"all_after_command_dual\": { \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#comments\" }, { \"include\": \"#conjugate_transpose\" }, { \"include\": \"#transpose\" }, { \"include\": \"#constants\" }, { \"include\": \"#variables\" }, { \"include\": \"#numbers\" }, { \"include\": \"#operators\" }] }, \"all_before_command_dual\": { \"patterns\": [{ \"include\": \"#classdef\" }, { \"include\": \"#function\" }, { \"include\": \"#blocks\" }, { \"include\": \"#control_statements\" }, { \"include\": \"#global_persistent\" }, { \"include\": \"#parens\" }, { \"include\": \"#square_brackets\" }, { \"include\": \"#indexing_curly_brackets\" }, { \"include\": \"#curly_brackets\" }] }, \"blocks\": { \"patterns\": [{ \"begin\": \"\\\\s*(?:^|[\\\\s,;])(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.for.matlab\" } }, \"name\": \"meta.for.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(if)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.if.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.if.matlab\" }, \"2\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"name\": \"meta.if.matlab\", \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"keyword.control.elseif.matlab\" }, \"3\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"end\": \"^\", \"match\": \"(\\\\s*)(?:^|[\\\\s,;])(elseif)\\\\b(.*)$\\\\n?\", \"name\": \"meta.elseif.matlab\" }, { \"captures\": { \"2\": { \"name\": \"keyword.control.else.matlab\" }, \"3\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"end\": \"^\", \"match\": \"(\\\\s*)(?:^|[\\\\s,;])(else)\\\\b(.*)?$\\\\n?\", \"name\": \"meta.else.matlab\" }, { \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(parfor)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.for.matlab\" } }, \"name\": \"meta.parfor.matlab\", \"patterns\": [{ \"begin\": \"\\\\G(?!$)\", \"end\": \"$\\\\n?\", \"name\": \"meta.parfor-quantity.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(spmd)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.spmd.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.spmd.matlab\" } }, \"name\": \"meta.spmd.matlab\", \"patterns\": [{ \"begin\": \"\\\\G(?!$)\", \"end\": \"$\\\\n?\", \"name\": \"meta.spmd-statement.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(switch)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.switch.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.switch.matlab\" } }, \"name\": \"meta.switch.matlab\", \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"keyword.control.case.matlab\" }, \"3\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"end\": \"^\", \"match\": \"(\\\\s*)(?:^|[\\\\s,;])(case)\\\\b(.*)$\\\\n?\", \"name\": \"meta.case.matlab\" }, { \"captures\": { \"2\": { \"name\": \"keyword.control.otherwise.matlab\" }, \"3\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"end\": \"^\", \"match\": \"(\\\\s*)(?:^|[\\\\s,;])(otherwise)\\\\b(.*)?$\\\\n?\", \"name\": \"meta.otherwise.matlab\" }, { \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(try)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.try.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.try.matlab\" } }, \"name\": \"meta.try.matlab\", \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"keyword.control.catch.matlab\" }, \"3\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"end\": \"^\", \"match\": \"(\\\\s*)(?:^|[\\\\s,;])(catch)\\\\b(.*)?$\\\\n?\", \"name\": \"meta.catch.matlab\" }, { \"include\": \"$self\" }] }, { \"begin\": \"\\\\s*(?:^|[\\\\s,;])(while)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.while.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.while.matlab\" } }, \"name\": \"meta.while.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"braced_validator_list\": { \"begin\": \"\\\\s*({)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.matlab\" } }, \"comment\": \"Validator functions. Treated as a recursive group to permit nested brackets, quotes, etc.\", \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"storage.type.matlab\" } }, \"patterns\": [{ \"include\": \"#braced_validator_list\" }, { \"include\": \"#validator_strings\" }, { \"include\": \"#line_continuation\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.matlab\" } }, \"match\": `([^{}}'\"\\\\.]+)` }, { \"match\": \"\\\\.\", \"name\": \"storage.type.matlab\" }] }, \"classdef\": { \"patterns\": [{ \"begin\": \"(^\\\\s*)(classdef)\\\\b\\\\s*(.*)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.class.matlab\" }, \"3\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"variable.parameter.class.matlab\" }, { \"begin\": \"=\\\\s*\", \"end\": \",|(?=\\\\))\", \"patterns\": [{ \"match\": \"true|false\", \"name\": \"constant.language.boolean.matlab\" }, { \"include\": \"#string\" }] }] }, \"2\": { \"name\": \"meta.class-declaration.matlab\" }, \"3\": { \"name\": \"entity.name.section.class.matlab\" }, \"4\": { \"name\": \"keyword.operator.other.matlab\" }, \"5\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*(\\\\.[a-zA-Z]\\\\w*)*\", \"name\": \"entity.other.inherited-class.matlab\" }, { \"match\": \"&\", \"name\": \"keyword.operator.other.matlab\" }] }, \"6\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"match\": \"(\\\\([^)]*\\\\))?\\\\s*(([a-zA-Z]\\\\w*)(?:\\\\s*(<)\\\\s*([^%]*))?)\\\\s*($|(?=(%|...)).*)\" }] } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.class.matlab\" } }, \"name\": \"meta.class.matlab\", \"patterns\": [{ \"begin\": \"(^\\\\s*)(properties)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.properties.matlab\" }, \"3\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"variable.parameter.properties.matlab\" }, { \"begin\": \"=\\\\s*\", \"end\": \",|(?=\\\\))\", \"patterns\": [{ \"match\": \"true|false\", \"name\": \"constant.language.boolean.matlab\" }, { \"match\": \"public|protected|private\", \"name\": \"constant.language.access.matlab\" }] }] } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.properties.matlab\" } }, \"name\": \"meta.properties.matlab\", \"patterns\": [{ \"include\": \"#validators\" }, { \"include\": \"$self\" }] }, { \"begin\": \"(^\\\\s*)(methods)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.methods.matlab\" }, \"3\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"variable.parameter.methods.matlab\" }, { \"begin\": \"=\\\\s*\", \"end\": \",|(?=\\\\))\", \"patterns\": [{ \"match\": \"true|false\", \"name\": \"constant.language.boolean.matlab\" }, { \"match\": \"public|protected|private\", \"name\": \"constant.language.access.matlab\" }] }] } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.methods.matlab\" } }, \"name\": \"meta.methods.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"(^\\\\s*)(events)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.events.matlab\" }, \"3\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"variable.parameter.events.matlab\" }, { \"begin\": \"=\\\\s*\", \"end\": \",|(?=\\\\))\", \"patterns\": [{ \"match\": \"true|false\", \"name\": \"constant.language.boolean.matlab\" }, { \"match\": \"public|protected|private\", \"name\": \"constant.language.access.matlab\" }] }] } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.events.matlab\" } }, \"name\": \"meta.events.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"(^\\\\s*)(enumeration)\\\\b([^%]*)\\\\s*($|(?=%))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.enumeration.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.enumeration.matlab\" } }, \"name\": \"meta.enumeration.matlab\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"include\": \"$self\" }] }] }, \"command_dual\": { \"captures\": { \"1\": { \"name\": \"string.interpolated.matlab\" }, \"2\": { \"name\": \"variable.other.command.matlab\" }, \"28\": { \"name\": \"comment.line.percentage.matlab\" } }, \"comment\": \"  1        2                                  3                                                    4                                                  5                                                    6                                                         7                                                                             8                 9                            10                   11                      12                         13                   14                                                               1516       17                                                             18                                                                                                                               19                      20                                     21                                    22                                      23                                         24                                         25                                                                         26            27                 28\", \"match\": `^\\\\s*(([b-df-hk-moq-zA-HJ-MO-Z]\\\\w*|a|an|a([A-Za-mo-z0-9_]\\\\w*|n[A-Za-rt-z0-9_]\\\\w*|ns\\\\w+)|e|ep|e([A-Za-oq-z0-9_]\\\\w*|p[A-Za-rt-z0-9_]\\\\w*|ps\\\\w+)|in|i([A-Za-mo-z0-9_]\\\\w*|n[A-Za-eg-z0-9_]\\\\w*|nf\\\\w+)|I|In|I([A-Za-mo-z0-9_]\\\\w*|n[A-Za-eg-z0-9_]\\\\w*|nf\\\\w+)|j\\\\w+|N|Na|N([A-Zb-z0-9_]\\\\w*|a[A-MO-Za-z0-9_]\\\\w*|aN\\\\w+)|n|na|nar|narg|nargi|nargo|nargou|n([A-Zb-z0-9_]\\\\w*|a([A-Za-mo-qs-z0-9_]\\\\w*|n\\\\w+|r([A-Za-fh-z0-9_]\\\\w*|g([A-Za-hj-nq-z0-9_]\\\\w*|i([A-Za-mo-z0-9_]\\\\w*|n\\\\w+)|o([A-Za-tv-z0-9_]\\\\w*|u([A-Za-su-z]\\\\w*|t\\\\w+))))))|p|p[A-Za-hj-z0-9_]\\\\w*|pi\\\\w+)\\\\s+((([^\\\\s;,%()=.{&|~<>:+\\\\-*/\\\\\\\\@^'\"]|(?=')|(?=\"))|(\\\\.\\\\^|\\\\.\\\\*|\\\\./|\\\\.\\\\\\\\|\\\\.'|\\\\.\\\\(|&&|==|\\\\|\\\\||&(?=[^&])|\\\\|(?=[^\\\\|])|~=|<=|>=|~(?!=)|<(?!=)|>(?!=)|:|\\\\+|-|\\\\*|/|\\\\\\\\|@|\\\\^)([^\\\\s]|\\\\s*(?=%)|\\\\s+$|\\\\s+(,|;|\\\\)|}|\\\\]|&|\\\\||<|>|=|:|\\\\*|/|\\\\\\\\|\\\\^|@|(\\\\.[^\\\\d.]|\\\\.\\\\.[^.])))|(\\\\.[^^*/\\\\\\\\'(\\\\sA-Za-z]))([^%]|'[^']*'|\"[^\"]*\")*|(\\\\.(?=\\\\s)|\\\\.[A-Za-z]|(?={))([^(=\\\\'\"%]|==|'[^']*'|\"[^\"]*\"|\\\\(|\\\\([^)%]*\\\\)|\\\\[|\\\\[[^\\\\]%]*\\\\]|{|{[^}%]*})*(\\\\.\\\\.\\\\.[^%]*)?((?=%)|$)))(%.*)?$` }, \"comment_block\": { \"begin\": \"(^[\\\\s]*)%\\\\{[^\\\\n\\\\S]*+\\\\n\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.matlab\" } }, \"end\": \"^[\\\\s]*%\\\\}[^\\\\n\\\\S]*+(?:\\\\n|$)\", \"name\": \"comment.block.percentage.matlab\", \"patterns\": [{ \"include\": \"#comment_block\" }, { \"match\": \"^[^\\\\n]*\\\\n\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=%%\\\\s)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.matlab\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"%%\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.matlab\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-percentage.matlab\", \"patterns\": [{ \"begin\": \"\\\\G[^\\\\S\\\\n]*(?![\\\\n\\\\s])\", \"contentName\": \"meta.cell.matlab\", \"end\": \"(?=\\\\n)\" }] }] }, { \"include\": \"#comment_block\" }, { \"begin\": \"(^[ \\\\t]+)?(?=%)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.matlab\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"%\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.matlab\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.percentage.matlab\" }] }] }, \"conjugate_transpose\": { \"match\": \"((?<=[^\\\\s])|(?<=\\\\])|(?<=\\\\))|(?<=\\\\}))'\", \"name\": \"keyword.operator.transpose.matlab\" }, \"constants\": { \"comment\": \"MATLAB Constants\", \"match\": \"(?<!\\\\.)\\\\b(eps|false|Inf|inf|intmax|intmin|namelengthmax|NaN|nan|on|off|realmax|realmin|true|pi)\\\\b\", \"name\": \"constant.language.matlab\" }, \"control_statements\": { \"captures\": { \"1\": { \"name\": \"keyword.control.matlab\" } }, \"match\": \"\\\\s*(?:^|[\\\\s,;])(break|continue|return)\\\\b\", \"name\": \"meta.control.matlab\" }, \"curly_brackets\": { \"begin\": \"\\\\{\", \"comment\": \"We don't include $self here to avoid matching command syntax inside (), [], {}\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#end_in_parens\" }, { \"include\": \"#all_before_command_dual\" }, { \"include\": \"#all_after_command_dual\" }, { \"include\": \"#end_in_parens\" }, { \"comment\": \"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\", \"include\": \"#block_keywords\" }] }, \"end_in_parens\": { \"comment\": \"end as operator symbol\", \"match\": \"\\\\bend\\\\b\", \"name\": \"keyword.operator.symbols.matlab\" }, \"function\": { \"patterns\": [{ \"begin\": \"(^\\\\s*)(function)\\\\s+(?:(?:(\\\\[)([^\\\\]]*)(\\\\])|([a-zA-Z]\\\\w*))\\\\s*=\\\\s*)?([a-zA-Z]\\\\w*(\\\\.[a-zA-Z]\\\\w*)*)\\\\s*\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.function.matlab\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.matlab\" }, \"4\": { \"patterns\": [{ \"match\": \"\\\\w+\", \"name\": \"variable.parameter.output.matlab\" }] }, \"5\": { \"name\": \"punctuation.definition.arguments.end.matlab\" }, \"6\": { \"name\": \"variable.parameter.output.function.matlab\" }, \"7\": { \"name\": \"entity.name.function.matlab\" } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b(\\\\s*\\\\n)?\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.function.matlab\" } }, \"name\": \"meta.function.matlab\", \"patterns\": [{ \"begin\": \"\\\\G\\\\(\", \"end\": \"\\\\)\", \"name\": \"meta.arguments.function.matlab\", \"patterns\": [{ \"include\": \"#line_continuation\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.input.matlab\" }] }, { \"begin\": \"(^\\\\s*)(arguments)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.arguments.matlab\" }, \"3\": { \"patterns\": [{ \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"variable.parameter.arguments.matlab\" }] } }, \"end\": \"\\\\s*(?:^|[\\\\s,;])(end)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.end.arguments.matlab\" } }, \"name\": \"meta.arguments.matlab\", \"patterns\": [{ \"include\": \"#validators\" }, { \"include\": \"$self\" }] }, { \"include\": \"$self\" }] }] }, \"global_persistent\": { \"captures\": { \"1\": { \"name\": \"keyword.control.globalpersistent.matlab\" } }, \"match\": \"^\\\\s*(global|persistent)\\\\b\", \"name\": \"meta.globalpersistent.matlab\" }, \"indexing_curly_brackets\": { \"Comment\": \"Match identifier{idx, idx, } and stop at newline without ... This helps with partially written code like x{idx \", \"begin\": \"([a-zA-Z][a-zA-Z0-9_\\\\.]*\\\\s*)\\\\{\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"comment\": \"We don't include $self here to avoid matching command syntax inside (), [], {}\", \"end\": \"(\\\\}|(?<!\\\\.\\\\.\\\\.).\\\\n)\", \"patterns\": [{ \"include\": \"#end_in_parens\" }, { \"include\": \"#all_before_command_dual\" }, { \"include\": \"#all_after_command_dual\" }, { \"include\": \"#end_in_parens\" }, { \"comment\": \"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\", \"include\": \"#block_keywords\" }] }, \"line_continuation\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.symbols.matlab\" }, \"2\": { \"name\": \"comment.line.continuation.matlab\" } }, \"comment\": \"Line continuations\", \"match\": \"(\\\\.\\\\.\\\\.)(.*)$\", \"name\": \"meta.linecontinuation.matlab\" }, \"numbers\": { \"comment\": \"Valid numbers: 1, .1, 1.1, .1e1, 1.1e1, 1e1, 1i, 1j, 1e2j\", \"match\": \"(?<=[\\\\s\\\\-+\\\\*\\\\/\\\\\\\\=:\\\\[({,]|^)\\\\d*\\\\.?\\\\d+([eE][+-]?\\\\d)?([0-9&&[^\\\\.]])*(i|j)?\\\\b\", \"name\": \"constant.numeric.matlab\" }, \"operators\": { \"comment\": \"Operator symbols\", \"match\": \"(?<=\\\\s)(==|~=|>|>=|<|<=|&|&&|:|\\\\||\\\\|\\\\||\\\\+|-|\\\\*|\\\\.\\\\*|/|\\\\./|\\\\\\\\|\\\\.\\\\\\\\|\\\\^|\\\\.\\\\^)(?=\\\\s)\", \"name\": \"keyword.operator.symbols.matlab\" }, \"parens\": { \"begin\": \"\\\\(\", \"comment\": \"We don't include $self here to avoid matching command syntax inside (), [], {}\", \"end\": \"(\\\\)|(?<!\\\\.\\\\.\\\\.).\\\\n)\", \"patterns\": [{ \"include\": \"#end_in_parens\" }, { \"include\": \"#all_before_command_dual\" }, { \"include\": \"#all_after_command_dual\" }, { \"comment\": \"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\", \"include\": \"#block_keywords\" }] }, \"square_brackets\": { \"begin\": \"\\\\[\", \"comment\": \"We don't include $self here to avoid matching command syntax inside (), [], {}\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#all_before_command_dual\" }, { \"include\": \"#all_after_command_dual\" }, { \"comment\": \"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\", \"include\": \"#block_keywords\" }] }, \"string\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.interpolated.matlab\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.matlab\" } }, \"comment\": \"Shell command\", \"match\": \"^\\\\s*((!).*$\\\\n?)\" }, { \"begin\": \"((?<=(\\\\[|\\\\(|\\\\{|=|\\\\s|;|:|,|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^))|^)'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.matlab\" } }, \"comment\": \"Character vector literal (single-quoted)\", \"end\": \"'(?=(\\\\[|\\\\(|\\\\{|\\\\]|\\\\)|\\\\}|=|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^|\\\\s|;|:|,))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.matlab\" } }, \"name\": \"string.quoted.single.matlab\", \"patterns\": [{ \"match\": \"''\", \"name\": \"constant.character.escape.matlab\" }, { \"match\": \"'(?=.)\", \"name\": \"invalid.illegal.unescaped-quote.matlab\" }, { \"comment\": \"Operator symbols\", \"match\": \"((\\\\%([+\\\\-0]?\\\\d{0,3}(\\\\.\\\\d{1,3})?)(c|d|e|E|f|g|G|s|((b|t)?(o|u|x|X))))|\\\\%\\\\%|\\\\\\\\(b|f|n|r|t|\\\\\\\\))\", \"name\": \"constant.character.escape.matlab\" }] }, { \"begin\": '((?<=(\\\\[|\\\\(|\\\\{|=|\\\\s|;|:|,|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^))|^)\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.matlab\" } }, \"comment\": \"String literal (double-quoted)\", \"end\": '\"(?=(\\\\[|\\\\(|\\\\{|\\\\]|\\\\)|\\\\}|=|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^|\\\\||\\\\s|;|:|,))', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.matlab\" } }, \"name\": \"string.quoted.double.matlab\", \"patterns\": [{ \"match\": '\"\"', \"name\": \"constant.character.escape.matlab\" }, { \"match\": '\"(?=.)', \"name\": \"invalid.illegal.unescaped-quote.matlab\" }] }] }, \"transpose\": { \"match\": \"\\\\.'\", \"name\": \"keyword.operator.transpose.matlab\" }, \"validator_strings\": { \"comment\": \"Simplified string patterns nested inside validator functions which don't change scopes of matches.\", \"patterns\": [{ \"patterns\": [{ \"begin\": \"((?<=(\\\\[|\\\\(|\\\\{|=|\\\\s|;|:|,|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^))|^)'\", \"comment\": \"Character vector literal (single-quoted)\", \"end\": \"'(?=(\\\\[|\\\\(|\\\\{|\\\\]|\\\\)|\\\\}|=|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^|\\\\s|;|:|,))\", \"name\": \"storage.type.matlab\", \"patterns\": [{ \"match\": \"''\" }, { \"match\": \"'(?=.)\" }, { \"match\": \"([^']+)\" }] }, { \"begin\": '((?<=(\\\\[|\\\\(|\\\\{|=|\\\\s|;|:|,|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^))|^)\"', \"comment\": \"String literal (double-quoted)\", \"end\": '\"(?=(\\\\[|\\\\(|\\\\{|\\\\]|\\\\)|\\\\}|=|~|<|>|&|\\\\||-|\\\\+|\\\\*|/|\\\\\\\\|\\\\.|\\\\^|\\\\||\\\\s|;|:|,))', \"name\": \"storage.type.matlab\", \"patterns\": [{ \"match\": '\"\"' }, { \"match\": '\"(?=.)' }, { \"match\": '[^\"]+' }] }] }] }, \"validators\": { \"begin\": \"\\\\s*[;]?\\\\s*([a-zA-Z][a-zA-Z0-9_\\\\.?]*)\", \"comment\": \"Property and argument validation. Match an identifier allowing . and ?.\", \"end\": \"([;\\\\n%=].*)\", \"endCaptures\": { \"1\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"comment\": \"Match comments\", \"match\": \"([%].*)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"comment\": \"Handle things like arg = val; nextArg\", \"match\": \"(=[^;]*)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#validators\" }] } }, \"comment\": \"End of property/argument patterns which start a new property/argument. Look for beginning of identifier after semicolon. Otherwise treat as regular code.\", \"match\": \"([\\\\n;]\\\\s*[a-zA-Z].*)\" }, { \"include\": \"$self\" }] } }, \"patterns\": [{ \"include\": \"#line_continuation\" }, { \"comment\": \"Size declaration\", \"match\": \"\\\\s*(\\\\([^)]*\\\\))\", \"name\": \"storage.type.matlab\" }, { \"comment\": \"Type declaration\", \"match\": \"([a-zA-Z][a-zA-Z0-9_\\\\.]*)\", \"name\": \"storage.type.matlab\" }, { \"include\": \"#braced_validator_list\" }] }, \"variables\": { \"comment\": \"MATLAB variables\", \"match\": \"(?<!\\\\.)\\\\b(nargin|nargout|varargin|varargout)\\\\b\", \"name\": \"variable.other.function.matlab\" } }, \"scopeName\": \"source.matlab\" });\nvar matlab = [\n  lang\n];\n\nexport { matlab as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;KAAI;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;YAA6G,WAAW;QAA2B;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAA0B;KAAE;IAAE,cAAc;QAAE,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;oBAAE;oBAAG,QAAQ;oBAAkB,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAK,SAAS;4BAA2C,QAAQ;wBAAqB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAK,SAAS;4BAA0C,QAAQ;wBAAmB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,SAAS;4BAAY,OAAO;4BAAS,QAAQ;4BAA+B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,SAAS;4BAAY,OAAO;4BAAS,QAAQ;4BAA8B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAK,SAAS;4BAAyC,QAAQ;wBAAmB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAK,SAAS;4BAA+C,QAAQ;wBAAwB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAAK,SAAS;4BAA2C,QAAQ;wBAAoB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,WAAW;YAA6F,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS,CAAC,cAAc,CAAC;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAsB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,YAAY;gDAAC;oDAAE,SAAS;oDAAgB,QAAQ;gDAAkC;gDAAG;oDAAE,SAAS;oDAAS,OAAO;oDAAa,YAAY;wDAAC;4DAAE,SAAS;4DAAc,QAAQ;wDAAmC;wDAAG;4DAAE,WAAW;wDAAU;qDAAE;gDAAC;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgC;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgC;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,SAAS;oDAAkC,QAAQ;gDAAsC;gDAAG;oDAAE,SAAS;oDAAK,QAAQ;gDAAgC;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;gCAAiF;6BAAE;wBAAC;oBAAE;oBAAG,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,SAAS;4BAAgE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAgB,QAAQ;wCAAuC;wCAAG;4CAAE,SAAS;4CAAS,OAAO;4CAAa,YAAY;gDAAC;oDAAE,SAAS;oDAAc,QAAQ;gDAAmC;gDAAG;oDAAE,SAAS;oDAA4B,QAAQ;gDAAkC;6CAAE;wCAAC;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAA6B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA0B,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA6D,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAgB,QAAQ;wCAAoC;wCAAG;4CAAE,SAAS;4CAAS,OAAO;4CAAa,YAAY;gDAAC;oDAAE,SAAS;oDAAc,QAAQ;gDAAmC;gDAAG;oDAAE,SAAS;oDAA4B,QAAQ;gDAAkC;6CAAE;wCAAC;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAA6B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,QAAQ;4BAAuB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA4D,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAgB,QAAQ;wCAAmC;wCAAG;4CAAE,SAAS;4CAAS,OAAO;4CAAa,YAAY;gDAAC;oDAAE,SAAS;oDAAc,QAAQ;gDAAmC;gDAAG;oDAAE,SAAS;oDAA4B,QAAQ;gDAAkC;6CAAE;wCAAC;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAA6B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA+C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAA6B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,MAAM;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,WAAW;YAAgkC,SAAS,CAAC,ghCAAghC,CAAC;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA+B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAmC,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;gBAAc;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;4BAAyC,YAAY;gCAAC;oCAAE,SAAS;oCAA6B,eAAe;oCAAoB,OAAO;gCAAU;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAA6C,QAAQ;QAAoC;QAAG,aAAa;YAAE,WAAW;YAAoB,SAAS;YAAwG,QAAQ;QAA2B;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;YAA+C,QAAQ;QAAsB;QAAG,kBAAkB;YAAE,SAAS;YAAO,WAAW;YAAkF,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;oBAA4K,WAAW;gBAAkB;aAAE;QAAC;QAAG,iBAAiB;YAAE,WAAW;YAA0B,SAAS;YAAa,QAAQ;QAAkC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAQ,QAAQ;gCAAmC;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAuC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS;4BAAU,OAAO;4BAAO,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,SAAS;oCAAQ,QAAQ;gCAAkC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA+D,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAgB,QAAQ;wCAAsC;qCAAE;gCAAC;4BAAE;4BAAG,OAAO;4BAA6B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;YAA+B,QAAQ;QAA+B;QAAG,2BAA2B;YAAE,WAAW;YAAmH,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAkF,OAAO;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;oBAA4K,WAAW;gBAAkB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,WAAW;YAAsB,SAAS;YAAoB,QAAQ;QAA+B;QAAG,WAAW;YAAE,WAAW;YAA6D,SAAS;YAA0F,QAAQ;QAA0B;QAAG,aAAa;YAAE,WAAW;YAAoB,SAAS;YAAsG,QAAQ;QAAkC;QAAG,UAAU;YAAE,SAAS;YAAO,WAAW;YAAkF,OAAO;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;oBAA4K,WAAW;gBAAkB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,WAAW;YAAkF,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;oBAA4K,WAAW;gBAAkB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAAiB,SAAS;gBAAoB;gBAAG;oBAAE,SAAS;oBAA4E,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAA4C,OAAO;oBAAmF,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAAM,QAAQ;wBAAmC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAyC;wBAAG;4BAAE,WAAW;4BAAoB,SAAS;4BAA0G,QAAQ;wBAAmC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4E,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAAkC,OAAO;oBAAuF,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAAM,QAAQ;wBAAmC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAyC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAQ,QAAQ;QAAoC;QAAG,qBAAqB;YAAE,WAAW;YAAsG,YAAY;gBAAC;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAA4E,WAAW;4BAA4C,OAAO;4BAAmF,QAAQ;4BAAuB,YAAY;gCAAC;oCAAE,SAAS;gCAAK;gCAAG;oCAAE,SAAS;gCAAS;gCAAG;oCAAE,SAAS;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA4E,WAAW;4BAAkC,OAAO;4BAAuF,QAAQ;4BAAuB,YAAY;gCAAC;oCAAE,SAAS;gCAAK;gCAAG;oCAAE,SAAS;gCAAS;gCAAG;oCAAE,SAAS;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA2C,WAAW;YAA2E,OAAO;YAAgB,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAAkB,SAAS;wBAAU;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAAyC,SAAS;wBAAW;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAc;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAA6J,SAAS;wBAAyB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAAqB,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAA8B,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,aAAa;YAAE,WAAW;YAAoB,SAAS;YAAqD,QAAQ;QAAiC;IAAE;IAAG,aAAa;AAAgB;AAC/noB,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}