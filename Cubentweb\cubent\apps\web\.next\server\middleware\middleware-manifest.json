{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_65232333._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_5738550d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|images|ingest|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "67b7c01565c53cb20f21f83b874e1bc1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c599309066945724ab3fe11a3a80a80e707c044d7c8aab8f2640bfbde3d16bfc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "10c2b3fa522d03d22dbbf3e3c9a10379e5aa51857eea0e4f8492662c89481ae6"}}}, "instrumentation": null, "functions": {}}