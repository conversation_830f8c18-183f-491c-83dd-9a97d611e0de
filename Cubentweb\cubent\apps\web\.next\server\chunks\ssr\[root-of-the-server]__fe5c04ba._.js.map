{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx"], "sourcesContent": ["export const AnimatedTitle = () => {\n  return (\n    <h1 className=\"max-w-4xl text-center font-regular text-5xl tracking-tighter md:text-6xl relative z-10\">\n      Built to optimize. Trained to ship.\n    </h1>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,gBAAgB;IAC3B,qBACE,6VAAC;QAAG,WAAU;kBAAyF;;;;;;AAI3G", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/hero.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { blog } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, MoveRight, PhoneCall } from 'lucide-react';\nimport Link from 'next/link';\nimport { AnimatedTitle } from './animated-title';\n\ntype HeroProps = {\n  dictionary: Dictionary;\n};\n\n// Hero component for the homepage\nexport const Hero = async ({ dictionary }: HeroProps) => (\n  <div className=\"w-full relative overflow-hidden -mt-20 pt-20\">\n\n\n\n\n\n\n\n    {/* Grid background extending behind header */}\n    <div\n      className=\"absolute inset-0 -top-20 opacity-20\"\n      style={{\n        backgroundImage: `\n          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '40px 40px'\n      }}\n    />\n    {/* Evenly spaced dashed grid lines that frame content without conflicting */}\n    <div className=\"absolute inset-0 -top-20 pointer-events-none z-10\">\n      {/* Left vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          left: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)',\n          maskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)',\n          WebkitMaskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)'\n        }}\n      />\n\n      {/* Right vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          right: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)',\n          maskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)',\n          WebkitMaskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)'\n        }}\n      />\n\n      {/* Top horizontal dashed line - above content */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '20px',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Middle horizontal line - between announcement and headline */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '20%',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Bottom horizontal line - below buttons */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          bottom: '20px',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n    </div>\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"flex flex-col items-center justify-center gap-8 pt-6 pb-8 lg:pt-12 lg:pb-12\">\n        <div>\n          <Feed queries={[blog.latestPostQuery]}>\n            {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n            {async ([data]: [any]) => {\n              'use server';\n\n              return (\n                <Button variant=\"secondary\" size=\"sm\" className=\"gap-4 bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/20 hover:from-orange-500/20 hover:to-orange-600/20 hover:border-orange-500/30 text-orange-400 hover:text-orange-300\" asChild>\n                  <Link href={`/blog/${data.blog.posts.item?._slug}`}>\n                    {dictionary.web.home.hero.announcement}{' '}\n                    <MoveRight className=\"h-4 w-4 text-orange-400\" />\n                  </Link>\n                </Button>\n              );\n            }}\n          </Feed>\n        </div>\n        <div className=\"flex flex-col gap-6 relative\">\n          <AnimatedTitle />\n          <p className=\"max-w-3xl mx-auto text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl relative z-10\">\n            Meet Cubent.Dev, your AI coding partner inside the editor. Generate code, solve bugs, document faster, and build smarter with simple language.\n          </p>\n        </div>\n        <div className=\"flex flex-col sm:flex-row gap-4 mt-8\">\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n              </svg>\n              VS Code\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://plugins.jetbrains.com/plugin/cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\" fill=\"currentColor\"/>\n                <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" fill=\"white\"/>\n                <text x=\"3\" y=\"8\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">JET</text>\n                <text x=\"3\" y=\"15\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">BRAINS</text>\n                <rect x=\"3\" y=\"18\" width=\"10\" height=\"1.5\" fill=\"black\"/>\n              </svg>\n              JetBrains\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AACA;;;;;;;;;;MA0Fa,wEAAO,CAAC,KAAY;;IAGnB,qBACE,6VAAC,2JAAA,CAAA,SAAM;QAAC,SAAQ;QAAY,MAAK;QAAK,WAAU;QAAmM,OAAO;kBACxP,cAAA,6VAAC,2QAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO;;;gBACR;8BACxC,6VAAC,oSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B;AA9FL,MAAM,OAAO,OAAO,EAAE,UAAU,EAAa,iBAClD,6VAAC;QAAI,WAAU;;0BASb,6VAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;QAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAGF,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM;4BACN,YAAY;4BACZ,WAAW;4BACX,iBAAiB;wBACnB;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,YAAY;4BACZ,WAAW;4BACX,iBAAiB;wBACnB;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;;;;;;;0BAIJ,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;sCACC,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,eAAe;iCAAC;0CAElC,8VAAA;;;;;;;;;;;sCAcL,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,gLAAA,CAAA,gBAAa;;;;;8CACd,6VAAC;oCAAE,WAAU;8CAAsH;;;;;;;;;;;;sCAIrI,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,2JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoE,QAAO;wCAAS,KAAI;wCAAsB,WAAU;;0DACjI,6VAAC;gDAAI,WAAU;gDAAwB,SAAQ;gDAAY,MAAK;0DAC9D,cAAA,6VAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;0DAEN,6VAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,6VAAC,2JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAA8C,QAAO;wCAAS,KAAI;wCAAsB,WAAU;;0DAC3G,6VAAC;gDAAI,WAAU;gDAAwB,SAAQ;gDAAY,MAAK;;kEAC9D,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;;;;;;kEAC9C,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;;;;;;kEAC9C,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,UAAS;wDAAI,YAAW;wDAAO,MAAK;wDAAQ,YAAW;kEAAoB;;;;;;kEAC7F,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAK,UAAS;wDAAI,YAAW;wDAAO,MAAK;wDAAQ,YAAW;kEAAoB;;;;;;kEAC9F,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAK,OAAM;wDAAK,QAAO;wDAAM,MAAK;;;;;;;;;;;;4CAC5C;0DAEN,6VAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'Cubent';\nconst author: Metadata['authors'] = {\n  name: '<PERSON><PERSON><PERSON>',\n  url: 'https://cubent.dev/',\n};\nconst publisher = 'Cubent';\nconst twitterHandle = '@cubent';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx <module evaluation>\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uFACA", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx <module evaluation>\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sFACA", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oFACA", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gEACA", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/model-providers.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ModelProviders = () => {\n  return (\n    <div className=\"w-full py-20 lg:py-32\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex flex-col items-center justify-center gap-12\">\n          {/* Header */}\n          <div className=\"flex flex-col gap-4 text-center max-w-4xl mx-auto\">\n            <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n              First-class support for every major model provider\n            </h2>\n            <p className=\"max-w-2xl mx-auto text-lg text-muted-foreground leading-relaxed tracking-tight\">\n              Connect with the AI models you trust. <PERSON><PERSON><PERSON> works seamlessly with all leading providers.\n            </p>\n          </div>\n\n          {/* Model Provider Logos */}\n          <div className=\"w-full max-w-6xl mx-auto\">\n            <div className=\"flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16\">\n\n              {/* OpenAI */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png\"\n                    alt=\"OpenAI\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenAI</span>\n              </div>\n\n              {/* Anthropic */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png\"\n                    alt=\"Anthropic\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Anthropic</span>\n              </div>\n\n              {/* Google */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/gemini-color.png\"\n                    alt=\"Google Gemini\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Google</span>\n              </div>\n\n              {/* Cohere */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/cohere-color.png\"\n                    alt=\"Cohere\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Cohere</span>\n              </div>\n\n              {/* Mistral */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral-color.png\"\n                    alt=\"Mistral\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Mistral</span>\n              </div>\n\n              {/* OpenRouter */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openrouter.png\"\n                    alt=\"OpenRouter\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenRouter</span>\n              </div>\n\n            </div>\n          </div>\n\n\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAAiB;IAC5B,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6VAAC;gCAAE,WAAU;0CAAiF;;;;;;;;;;;;kCAMhG,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CAGb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E", "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx <module evaluation>\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/page.tsx"], "sourcesContent": ["import { showBetaFeature } from '@repo/feature-flags';\nimport { getDictionary } from '@repo/internationalization';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport { Community } from './components/community';\nimport { CTA } from './components/cta';\nimport { Download } from './components/download';\nimport { FAQ } from './components/faq';\n\nimport { Hero } from './components/hero';\nimport { Mockup } from './components/mockup';\nimport { ModelProviders } from './components/model-providers';\nimport { Stats } from './components/stats';\nimport { Testimonials } from './components/testimonials';\nimport { TrustedBy } from './components/trusted-by';\n\ntype HomeProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: HomeProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return createMetadata(dictionary.web.home.meta);\n};\n\nconst Home = async ({ params }: HomeProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n  const betaFeature = await showBetaFeature();\n\n  return (\n    <>\n      {betaFeature && (\n        <div className=\"w-full bg-black py-2 text-center text-white\">\n          Beta feature now available\n        </div>\n      )}\n      <Hero dictionary={dictionary} />\n      <TrustedBy dictionary={dictionary} />\n      <Mockup />\n      <ModelProviders />\n      <Community dictionary={dictionary} />\n      <Download />\n    </>\n  );\n};\n\nexport default Home;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AAGA;;;;;;;;;;;AAQO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;AAChD;AAEA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAa;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,qBACE;;YACG,6BACC,6VAAC;gBAAI,WAAU;0BAA8C;;;;;;0BAI/D,6VAAC,mKAAA,CAAA,OAAI;gBAAC,YAAY;;;;;;0BAClB,6VAAC,4KAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,qKAAA,CAAA,SAAM;;;;;0BACP,6VAAC,iLAAA,CAAA,iBAAc;;;;;0BACf,6VAAC,wKAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,uKAAA,CAAA,WAAQ;;;;;;;AAGf;uCAEe", "debugId": null}}]}