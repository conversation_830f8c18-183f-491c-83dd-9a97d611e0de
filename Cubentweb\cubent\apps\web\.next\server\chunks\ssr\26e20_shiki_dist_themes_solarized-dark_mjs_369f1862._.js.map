{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/solarized-dark.mjs"], "sourcesContent": ["var solarizedDark = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#003847\",\n    \"badge.background\": \"#047aa6\",\n    \"button.background\": \"#2AA19899\",\n    \"debugExceptionWidget.background\": \"#00212B\",\n    \"debugExceptionWidget.border\": \"#AB395B\",\n    \"debugToolBar.background\": \"#00212B\",\n    \"dropdown.background\": \"#00212B\",\n    \"dropdown.border\": \"#2AA19899\",\n    \"editor.background\": \"#002B36\",\n    \"editor.foreground\": \"#839496\",\n    \"editor.lineHighlightBackground\": \"#073642\",\n    \"editor.selectionBackground\": \"#274642\",\n    \"editor.selectionHighlightBackground\": \"#005A6FAA\",\n    \"editor.wordHighlightBackground\": \"#004454AA\",\n    \"editor.wordHighlightStrongBackground\": \"#005A6FAA\",\n    \"editorBracketHighlight.foreground1\": \"#cdcdcdff\",\n    \"editorBracketHighlight.foreground2\": \"#b58900ff\",\n    \"editorBracketHighlight.foreground3\": \"#d33682ff\",\n    \"editorCursor.foreground\": \"#D30102\",\n    \"editorGroup.border\": \"#00212B\",\n    \"editorGroup.dropBackground\": \"#2AA19844\",\n    \"editorGroupHeader.tabsBackground\": \"#004052\",\n    \"editorHoverWidget.background\": \"#004052\",\n    \"editorIndentGuide.activeBackground\": \"#C3E1E180\",\n    \"editorIndentGuide.background\": \"#93A1A180\",\n    \"editorLineNumber.activeForeground\": \"#949494\",\n    \"editorMarkerNavigationError.background\": \"#AB395B\",\n    \"editorMarkerNavigationWarning.background\": \"#5B7E7A\",\n    \"editorWhitespace.foreground\": \"#93A1A180\",\n    \"editorWidget.background\": \"#00212B\",\n    \"errorForeground\": \"#ffeaea\",\n    \"focusBorder\": \"#2AA19899\",\n    \"input.background\": \"#003847\",\n    \"input.foreground\": \"#93A1A1\",\n    \"input.placeholderForeground\": \"#93A1A1AA\",\n    \"inputOption.activeBorder\": \"#2AA19899\",\n    \"inputValidation.errorBackground\": \"#571b26\",\n    \"inputValidation.errorBorder\": \"#a92049\",\n    \"inputValidation.infoBackground\": \"#052730\",\n    \"inputValidation.infoBorder\": \"#363b5f\",\n    \"inputValidation.warningBackground\": \"#5d5938\",\n    \"inputValidation.warningBorder\": \"#9d8a5e\",\n    \"list.activeSelectionBackground\": \"#005A6F\",\n    \"list.dropBackground\": \"#00445488\",\n    \"list.highlightForeground\": \"#1ebcc5\",\n    \"list.hoverBackground\": \"#004454AA\",\n    \"list.inactiveSelectionBackground\": \"#00445488\",\n    \"minimap.selectionHighlight\": \"#274642\",\n    \"panel.border\": \"#2b2b4a\",\n    \"peekView.border\": \"#2b2b4a\",\n    \"peekViewEditor.background\": \"#10192c\",\n    \"peekViewEditor.matchHighlightBackground\": \"#7744AA40\",\n    \"peekViewResult.background\": \"#00212B\",\n    \"peekViewTitle.background\": \"#00212B\",\n    \"pickerGroup.border\": \"#2AA19899\",\n    \"pickerGroup.foreground\": \"#2AA19899\",\n    \"ports.iconRunningProcessForeground\": \"#369432\",\n    \"progressBar.background\": \"#047aa6\",\n    \"quickInputList.focusBackground\": \"#005A6F\",\n    \"selection.background\": \"#2AA19899\",\n    \"sideBar.background\": \"#00212B\",\n    \"sideBarTitle.foreground\": \"#93A1A1\",\n    \"statusBar.background\": \"#00212B\",\n    \"statusBar.debuggingBackground\": \"#00212B\",\n    \"statusBar.foreground\": \"#93A1A1\",\n    \"statusBar.noFolderBackground\": \"#00212B\",\n    \"statusBarItem.prominentBackground\": \"#003847\",\n    \"statusBarItem.prominentHoverBackground\": \"#003847\",\n    \"statusBarItem.remoteBackground\": \"#2AA19899\",\n    \"tab.activeBackground\": \"#002B37\",\n    \"tab.activeForeground\": \"#d6dbdb\",\n    \"tab.border\": \"#003847\",\n    \"tab.inactiveBackground\": \"#004052\",\n    \"tab.inactiveForeground\": \"#93A1A1\",\n    \"tab.lastPinnedBorder\": \"#2AA19844\",\n    \"terminal.ansiBlack\": \"#073642\",\n    \"terminal.ansiBlue\": \"#268bd2\",\n    \"terminal.ansiBrightBlack\": \"#002b36\",\n    \"terminal.ansiBrightBlue\": \"#839496\",\n    \"terminal.ansiBrightCyan\": \"#93a1a1\",\n    \"terminal.ansiBrightGreen\": \"#586e75\",\n    \"terminal.ansiBrightMagenta\": \"#6c71c4\",\n    \"terminal.ansiBrightRed\": \"#cb4b16\",\n    \"terminal.ansiBrightWhite\": \"#fdf6e3\",\n    \"terminal.ansiBrightYellow\": \"#657b83\",\n    \"terminal.ansiCyan\": \"#2aa198\",\n    \"terminal.ansiGreen\": \"#859900\",\n    \"terminal.ansiMagenta\": \"#d33682\",\n    \"terminal.ansiRed\": \"#dc322f\",\n    \"terminal.ansiWhite\": \"#eee8d5\",\n    \"terminal.ansiYellow\": \"#b58900\",\n    \"titleBar.activeBackground\": \"#002C39\"\n  },\n  \"displayName\": \"Solarized Dark\",\n  \"name\": \"solarized-dark\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#839496\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded\",\n        \"source.groovy.embedded\",\n        \"string meta.image.inline.markdown\",\n        \"variable.legacy.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#839496\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#586E75\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#2AA198\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#D33682\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language\",\n        \"variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#93A1A1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.name.type\",\n        \"entity.name.namespace\",\n        \"entity.name.scope-resolution\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded.begin\",\n        \"punctuation.section.embedded.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"meta.preprocessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B58900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.construct\",\n        \"keyword.other.new\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character\",\n        \"constant.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"foreground\": \"#6C71C4\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter\",\n      \"settings\": {}\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#586E75\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#93A1A1\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.continuation\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\",\n        \"support.variable\"\n      ],\n      \"settings\": {}\n    },\n    {\n      \"scope\": [\n        \"support.type\",\n        \"support.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"support.type.exception\",\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"support.other.variable\",\n      \"settings\": {}\n    },\n    {\n      \"scope\": \"invalid\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"markup.list\",\n      \"settings\": {\n        \"foreground\": \"#B58900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D33682\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#2AA198\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.setext\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#268BD2\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { solarizedDark as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,gBAAgB,OAAO,MAAM,CAAC;IAChC,UAAU;QACR,0BAA0B;QAC1B,oBAAoB;QACpB,qBAAqB;QACrB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,2BAA2B;QAC3B,sBAAsB;QACtB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,oBAAoB;QACpB,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,kCAAkC;QAClC,uBAAuB;QACvB,4BAA4B;QAC5B,wBAAwB;QACxB,oCAAoC;QACpC,8BAA8B;QAC9B,gBAAgB;QAChB,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,0BAA0B;QAC1B,kCAAkC;QAClC,wBAAwB;QACxB,sBAAsB;QACtB,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;IAC/B;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY,CAAC;QACf;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY,CAAC;QACf;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY,CAAC;QACf;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}