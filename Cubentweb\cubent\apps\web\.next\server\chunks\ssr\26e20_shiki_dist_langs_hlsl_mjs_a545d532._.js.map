{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/hlsl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"HLSL\", \"name\": \"hlsl\", \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.line.block.hlsl\" }, { \"begin\": \"//\", \"end\": \"$\", \"name\": \"comment.line.double-slash.hlsl\" }, { \"match\": \"\\\\b\\\\d+\\\\.\\\\d*(F|f)?\\\\b\", \"name\": \"constant.numeric.decimal.hlsl\" }, { \"match\": \"(\\\\.(\\\\d+)(F|f)?)\\\\b\", \"name\": \"constant.numeric.decimal.hlsl\" }, { \"match\": \"\\\\b(\\\\d+(F|f)?)\\\\b\", \"name\": \"constant.numeric.decimal.hlsl\" }, { \"match\": \"\\\\b(0(x|X)[0-9a-fA-F]+)\\\\b\", \"name\": \"constant.numeric.hex.hlsl\" }, { \"match\": \"\\\\b(false|true)\\\\b\", \"name\": \"constant.language.hlsl\" }, { \"match\": \"^\\\\s*#\\\\s*(define|elif|else|endif|ifdef|ifndef|if|undef|include|line|error|pragma)\", \"name\": \"keyword.preprocessor.hlsl\" }, { \"match\": \"\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\b\", \"name\": \"keyword.control.hlsl\" }, { \"match\": \"\\\\b(compile)\\\\b\", \"name\": \"keyword.control.fx.hlsl\" }, { \"match\": \"\\\\b(typedef)\\\\b\", \"name\": \"keyword.typealias.hlsl\" }, { \"match\": \"\\\\b(bool([1-4](x[1-4])?)?|double([1-4](x[1-4])?)?|dword|float([1-4](x[1-4])?)?|half([1-4](x[1-4])?)?|int([1-4](x[1-4])?)?|matrix|min10float([1-4](x[1-4])?)?|min12int([1-4](x[1-4])?)?|min16float([1-4](x[1-4])?)?|min16int([1-4](x[1-4])?)?|min16uint([1-4](x[1-4])?)?|unsigned|uint([1-4](x[1-4])?)?|vector|void)\\\\b\", \"name\": \"storage.type.basic.hlsl\" }, { \"match\": \"\\\\b([a-zA-Z_]\\\\w*)(?=[\\\\s]*\\\\()\", \"name\": \"support.function.hlsl\" }, { \"match\": \"(?<=:\\\\s|:)(?i:BINORMAL\\\\d*|BLENDINDICES\\\\d*|BLENDWEIGHT\\\\d*|COLOR\\\\d*|NORMAL\\\\d*|POSITIONT|POSITION|PSIZE\\\\d*|TANGENT\\\\d*|TEXCOORD\\\\d*|FOG|TESSFACTOR\\\\d*|VFACE|VPOS|DEPTH\\\\d*)\\\\b\", \"name\": \"support.variable.semantic.hlsl\" }, { \"match\": \"(?<=:\\\\s|:)(?i:SV_ClipDistance\\\\d*|SV_CullDistance\\\\d*|SV_Coverage|SV_Depth|SV_DepthGreaterEqual\\\\d*|SV_DepthLessEqual\\\\d*|SV_InstanceID|SV_IsFrontFace|SV_Position|SV_RenderTargetArrayIndex|SV_SampleIndex|SV_StencilRef|SV_Target[0-7]?|SV_VertexID|SV_ViewportArrayIndex)\\\\b\", \"name\": \"support.variable.semantic.sm4.hlsl\" }, { \"match\": \"(?<=:\\\\s|:)(?i:SV_DispatchThreadID|SV_DomainLocation|SV_GroupID|SV_GroupIndex|SV_GroupThreadID|SV_GSInstanceID|SV_InsideTessFactor|SV_OutputControlPointID|SV_TessFactor)\\\\b\", \"name\": \"support.variable.semantic.sm5.hlsl\" }, { \"match\": \"(?<=:\\\\s|:)(?i:SV_InnerCoverage|SV_StencilRef)\\\\b\", \"name\": \"support.variable.semantic.sm5_1.hlsl\" }, { \"match\": \"\\\\b(column_major|const|export|extern|globallycoherent|groupshared|inline|inout|in|out|precise|row_major|shared|static|uniform|volatile)\\\\b\", \"name\": \"storage.modifier.hlsl\" }, { \"match\": \"\\\\b(snorm|unorm)\\\\b\", \"name\": \"storage.modifier.float.hlsl\" }, { \"match\": \"\\\\b(packoffset|register)\\\\b\", \"name\": \"storage.modifier.postfix.hlsl\" }, { \"match\": \"\\\\b(centroid|linear|nointerpolation|noperspective|sample)\\\\b\", \"name\": \"storage.modifier.interpolation.hlsl\" }, { \"match\": \"\\\\b(lineadj|line|point|triangle|triangleadj)\\\\b\", \"name\": \"storage.modifier.geometryshader.hlsl\" }, { \"match\": \"\\\\b(string)\\\\b\", \"name\": \"support.type.other.hlsl\" }, { \"match\": \"\\\\b(AppendStructuredBuffer|Buffer|ByteAddressBuffer|ConstantBuffer|ConsumeStructuredBuffer|InputPatch|OutputPatch)\\\\b\", \"name\": \"support.type.object.hlsl\" }, { \"match\": \"\\\\b(RasterizerOrderedBuffer|RasterizerOrderedByteAddressBuffer|RasterizerOrderedStructuredBuffer|RasterizerOrderedTexture1D|RasterizerOrderedTexture1DArray|RasterizerOrderedTexture2D|RasterizerOrderedTexture2DArray|RasterizerOrderedTexture3D)\\\\b\", \"name\": \"support.type.object.rasterizerordered.hlsl\" }, { \"match\": \"\\\\b(RWBuffer|RWByteAddressBuffer|RWStructuredBuffer|RWTexture1D|RWTexture1DArray|RWTexture2D|RWTexture2DArray|RWTexture3D)\\\\b\", \"name\": \"support.type.object.rw.hlsl\" }, { \"match\": \"\\\\b(LineStream|PointStream|TriangleStream)\\\\b\", \"name\": \"support.type.object.geometryshader.hlsl\" }, { \"match\": \"\\\\b(sampler|sampler1D|sampler2D|sampler3D|samplerCUBE|sampler_state)\\\\b\", \"name\": \"support.type.sampler.legacy.hlsl\" }, { \"match\": \"\\\\b(SamplerState|SamplerComparisonState)\\\\b\", \"name\": \"support.type.sampler.hlsl\" }, { \"match\": \"\\\\b(texture2D|textureCUBE)\\\\b\", \"name\": \"support.type.texture.legacy.hlsl\" }, { \"match\": \"\\\\b(Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture2DMS|Texture2DMSArray|Texture3D|TextureCube|TextureCubeArray)\\\\b\", \"name\": \"support.type.texture.hlsl\" }, { \"match\": \"\\\\b(cbuffer|class|interface|namespace|struct|tbuffer)\\\\b\", \"name\": \"storage.type.structured.hlsl\" }, { \"match\": \"\\\\b(FALSE|TRUE|NULL)\\\\b\", \"name\": \"support.constant.property-value.fx.hlsl\" }, { \"match\": \"\\\\b(BlendState|DepthStencilState|RasterizerState)\\\\b\", \"name\": \"support.type.fx.hlsl\" }, { \"match\": \"\\\\b(technique|Technique|technique10|technique11|pass)\\\\b\", \"name\": \"storage.type.fx.technique.hlsl\" }, { \"match\": \"\\\\b(AlphaToCoverageEnable|BlendEnable|SrcBlend|DestBlend|BlendOp|SrcBlendAlpha|DestBlendAlpha|BlendOpAlpha|RenderTargetWriteMask)\\\\b\", \"name\": \"meta.object-literal.key.fx.blendstate.hlsl\" }, { \"match\": \"\\\\b(DepthEnable|DepthWriteMask|DepthFunc|StencilEnable|StencilReadMask|StencilWriteMask|FrontFaceStencilFail|FrontFaceStencilZFail|FrontFaceStencilPass|FrontFaceStencilFunc|BackFaceStencilFail|BackFaceStencilZFail|BackFaceStencilPass|BackFaceStencilFunc)\\\\b\", \"name\": \"meta.object-literal.key.fx.depthstencilstate.hlsl\" }, { \"match\": \"\\\\b(FillMode|CullMode|FrontCounterClockwise|DepthBias|DepthBiasClamp|SlopeScaleDepthBias|ZClipEnable|ScissorEnable|MultiSampleEnable|AntiAliasedLineEnable)\\\\b\", \"name\": \"meta.object-literal.key.fx.rasterizerstate.hlsl\" }, { \"match\": \"\\\\b(Filter|AddressU|AddressV|AddressW|MipLODBias|MaxAnisotropy|ComparisonFunc|BorderColor|MinLOD|MaxLOD)\\\\b\", \"name\": \"meta.object-literal.key.fx.samplerstate.hlsl\" }, { \"match\": \"\\\\b(?i:ZERO|ONE|SRC_COLOR|INV_SRC_COLOR|SRC_ALPHA|INV_SRC_ALPHA|DEST_ALPHA|INV_DEST_ALPHA|DEST_COLOR|INV_DEST_COLOR|SRC_ALPHA_SAT|BLEND_FACTOR|INV_BLEND_FACTOR|SRC1_COLOR|INV_SRC1_COLOR|SRC1_ALPHA|INV_SRC1_ALPHA)\\\\b\", \"name\": \"support.constant.property-value.fx.blend.hlsl\" }, { \"match\": \"\\\\b(?i:ADD|SUBTRACT|REV_SUBTRACT|MIN|MAX)\\\\b\", \"name\": \"support.constant.property-value.fx.blendop.hlsl\" }, { \"match\": \"\\\\b(?i:ALL)\\\\b\", \"name\": \"support.constant.property-value.fx.depthwritemask.hlsl\" }, { \"match\": \"\\\\b(?i:NEVER|LESS|EQUAL|LESS_EQUAL|GREATER|NOT_EQUAL|GREATER_EQUAL|ALWAYS)\\\\b\", \"name\": \"support.constant.property-value.fx.comparisonfunc.hlsl\" }, { \"match\": \"\\\\b(?i:KEEP|REPLACE|INCR_SAT|DECR_SAT|INVERT|INCR|DECR)\\\\b\", \"name\": \"support.constant.property-value.fx.stencilop.hlsl\" }, { \"match\": \"\\\\b(?i:WIREFRAME|SOLID)\\\\b\", \"name\": \"support.constant.property-value.fx.fillmode.hlsl\" }, { \"match\": \"\\\\b(?i:NONE|FRONT|BACK)\\\\b\", \"name\": \"support.constant.property-value.fx.cullmode.hlsl\" }, { \"match\": \"\\\\b(?i:MIN_MAG_MIP_POINT|MIN_MAG_POINT_MIP_LINEAR|MIN_POINT_MAG_LINEAR_MIP_POINT|MIN_POINT_MAG_MIP_LINEAR|MIN_LINEAR_MAG_MIP_POINT|MIN_LINEAR_MAG_POINT_MIP_LINEAR|MIN_MAG_LINEAR_MIP_POINT|MIN_MAG_MIP_LINEAR|ANISOTROPIC|COMPARISON_MIN_MAG_MIP_POINT|COMPARISON_MIN_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_POINT_MAG_MIP_LINEAR|COMPARISON_MIN_LINEAR_MAG_MIP_POINT|COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_MAG_MIP_LINEAR|COMPARISON_ANISOTROPIC|TEXT_1BIT)\\\\b\", \"name\": \"support.constant.property-value.fx.filter.hlsl\" }, { \"match\": \"\\\\b(?i:WRAP|MIRROR|CLAMP|BORDER|MIRROR_ONCE)\\\\b\", \"name\": \"support.constant.property-value.fx.textureaddressmode.hlsl\" }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.hlsl\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.hlsl\" }] }], \"scopeName\": \"source.hlsl\" });\nvar hlsl = [\n  lang\n];\n\nexport { hlsl as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAA2B,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAwB,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAA8B,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAAsF,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAkF,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAmB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAmB,QAAQ;QAAyB;QAAG;YAAE,SAAS;YAA0T,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAmC,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAuL,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAoR,QAAQ;QAAqC;QAAG;YAAE,SAAS;YAAgL,QAAQ;QAAqC;QAAG;YAAE,SAAS;YAAqD,QAAQ;QAAuC;QAAG;YAAE,SAAS;YAA8I,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAuB,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAA+B,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAgE,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAmD,QAAQ;QAAuC;QAAG;YAAE,SAAS;YAAkB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAyH,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAyP,QAAQ;QAA6C;QAAG;YAAE,SAAS;YAAiI,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAAiD,QAAQ;QAA0C;QAAG;YAAE,SAAS;YAA2E,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAA+C,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAiI,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAA4D,QAAQ;QAA+B;QAAG;YAAE,SAAS;YAA2B,QAAQ;QAA0C;QAAG;YAAE,SAAS;YAAwD,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAA4D,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAwI,QAAQ;QAA6C;QAAG;YAAE,SAAS;YAAqQ,QAAQ;QAAoD;QAAG;YAAE,SAAS;YAAkK,QAAQ;QAAkD;QAAG;YAAE,SAAS;YAA+G,QAAQ;QAA+C;QAAG;YAAE,SAAS;YAA2N,QAAQ;QAAgD;QAAG;YAAE,SAAS;YAAgD,QAAQ;QAAkD;QAAG;YAAE,SAAS;YAAkB,QAAQ;QAAyD;QAAG;YAAE,SAAS;YAAiF,QAAQ;QAAyD;QAAG;YAAE,SAAS;YAA8D,QAAQ;QAAoD;QAAG;YAAE,SAAS;YAA8B,QAAQ;QAAmD;QAAG;YAAE,SAAS;YAA8B,QAAQ;QAAmD;QAAG;YAAE,SAAS;YAAmiB,QAAQ;QAAiD;QAAG;YAAE,SAAS;YAAmD,QAAQ;QAA6D;QAAG;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAiC;aAAE;QAAC;KAAE;IAAE,aAAa;AAAc;AACn7O,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}