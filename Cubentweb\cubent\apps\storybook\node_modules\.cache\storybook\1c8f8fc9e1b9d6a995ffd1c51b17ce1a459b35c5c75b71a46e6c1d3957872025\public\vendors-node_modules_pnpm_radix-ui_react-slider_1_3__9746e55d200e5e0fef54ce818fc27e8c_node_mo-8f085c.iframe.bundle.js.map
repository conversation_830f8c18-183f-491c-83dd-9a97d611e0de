{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-slider_1_3__9746e55d200e5e0fef54ce818fc27e8c_node_mo-8f085c.iframe.bundle.js", "mappings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rjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-slider@1.3._9746e55d200e5e0fef54ce818fc27e8c/node_modules/@radix-ui/react-slider/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-previou_791288859aab8756064fe392350c2e0c/node_modules/@radix-ui/react-use-previous/dist/index.mjs"], "sourcesContent": ["// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/slider.tsx\nimport * as React from \"react\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_KEYS = [\"PageUp\", \"PageDown\"];\nvar ARROW_KEYS = [\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"];\nvar BACK_KEYS = {\n  \"from-left\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowLeft\"],\n  \"from-right\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowRight\"],\n  \"from-bottom\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowLeft\"],\n  \"from-top\": [\"Home\", \"PageDown\", \"ArrowUp\", \"ArrowLeft\"]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = createContextScope(SLIDER_NAME, [\n  createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      name,\n      min = 0,\n      max = 100,\n      step = 1,\n      orientation = \"horizontal\",\n      disabled = false,\n      minStepsBetweenThumbs = 0,\n      defaultValue = [min],\n      value,\n      onValueChange = () => {\n      },\n      onValueCommit = () => {\n      },\n      inverted = false,\n      form,\n      ...sliderProps\n    } = props;\n    const thumbRefs = React.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = React.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = useControllableState({\n      prop: value,\n      defaultProp: defaultValue,\n      onChange: (value2) => {\n        const thumbs = [...thumbRefs.current];\n        thumbs[valueIndexToChangeRef.current]?.focus();\n        onValueChange(value2);\n      }\n    });\n    const valuesBeforeSlideStartRef = React.useRef(values);\n    function handleSlideStart(value2) {\n      const closestIndex = getClosestValueIndex(values, value2);\n      updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n      updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n      const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n      const nextValue = values[valueIndexToChangeRef.current];\n      const hasChanged = nextValue !== prevValue;\n      if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = { commit: false }) {\n      const decimalCount = getDecimalCount(step);\n      const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n      const nextValue = clamp(snapToStep, [min, max]);\n      setValues((prevValues = []) => {\n        const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n        if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n          valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n          const hasChanged = String(nextValues) !== String(prevValues);\n          if (hasChanged && commit) onValueCommit(nextValues);\n          return hasChanged ? nextValues : prevValues;\n        } else {\n          return prevValues;\n        }\n      });\n    }\n    return /* @__PURE__ */ jsx(\n      SliderProvider,\n      {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(\n          SliderOrientation,\n          {\n            \"aria-disabled\": disabled,\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...sliderProps,\n            ref: forwardedRef,\n            onPointerDown: composeEventHandlers(sliderProps.onPointerDown, () => {\n              if (!disabled) valuesBeforeSlideStartRef.current = values;\n            }),\n            min,\n            max,\n            inverted,\n            onSlideStart: disabled ? void 0 : handleSlideStart,\n            onSlideMove: disabled ? void 0 : handleSlideMove,\n            onSlideEnd: disabled ? void 0 : handleSlideEnd,\n            onHomeKeyDown: () => !disabled && updateValues(min, 0, { commit: true }),\n            onEndKeyDown: () => !disabled && updateValues(max, values.length - 1, { commit: true }),\n            onStepKeyDown: ({ event, direction: stepDirection }) => {\n              if (!disabled) {\n                const isPageKey = PAGE_KEYS.includes(event.key);\n                const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                const multiplier = isSkipKey ? 10 : 1;\n                const atIndex = valueIndexToChangeRef.current;\n                const value2 = values[atIndex];\n                const stepInDirection = step * multiplier * stepDirection;\n                updateValues(value2 + stepInDirection, atIndex, { commit: true });\n              }\n            }\n          }\n        ) }) })\n      }\n    );\n  }\n);\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n  startEdge: \"left\",\n  endEdge: \"right\",\n  size: \"width\",\n  direction: 1\n});\nvar SliderHorizontal = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      min,\n      max,\n      dir,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const [slider, setSlider] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setSlider(node));\n    const rectRef = React.useRef(void 0);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n      const rect = rectRef.current || slider.getBoundingClientRect();\n      const input = [0, rect.width];\n      const output = isSlidingFromLeft ? [min, max] : [max, min];\n      const value = linearScale(input, output);\n      rectRef.current = rect;\n      return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ jsx(\n      SliderOrientationProvider,\n      {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ jsx(\n          SliderImpl,\n          {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n              ...sliderProps.style,\n              [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event) => {\n              const value = getValueFromPointer(event.clientX);\n              onSlideStart?.(value);\n            },\n            onSlideMove: (event) => {\n              const value = getValueFromPointer(event.clientX);\n              onSlideMove?.(value);\n            },\n            onSlideEnd: () => {\n              rectRef.current = void 0;\n              onSlideEnd?.();\n            },\n            onStepKeyDown: (event) => {\n              const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n              const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n              onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n            }\n          }\n        )\n      }\n    );\n  }\n);\nvar SliderVertical = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      min,\n      max,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const sliderRef = React.useRef(null);\n    const ref = useComposedRefs(forwardedRef, sliderRef);\n    const rectRef = React.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n      const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n      const input = [0, rect.height];\n      const output = isSlidingFromBottom ? [max, min] : [min, max];\n      const value = linearScale(input, output);\n      rectRef.current = rect;\n      return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ jsx(\n      SliderOrientationProvider,\n      {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ jsx(\n          SliderImpl,\n          {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n              ...sliderProps.style,\n              [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event) => {\n              const value = getValueFromPointer(event.clientY);\n              onSlideStart?.(value);\n            },\n            onSlideMove: (event) => {\n              const value = getValueFromPointer(event.clientY);\n              onSlideMove?.(value);\n            },\n            onSlideEnd: () => {\n              rectRef.current = void 0;\n              onSlideEnd?.();\n            },\n            onStepKeyDown: (event) => {\n              const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n              const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n              onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n            }\n          }\n        )\n      }\n    );\n  }\n);\nvar SliderImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSlider,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onHomeKeyDown,\n      onEndKeyDown,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          if (event.key === \"Home\") {\n            onHomeKeyDown(event);\n            event.preventDefault();\n          } else if (event.key === \"End\") {\n            onEndKeyDown(event);\n            event.preventDefault();\n          } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n            onStepKeyDown(event);\n            event.preventDefault();\n          }\n        }),\n        onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n          const target = event.target;\n          target.setPointerCapture(event.pointerId);\n          event.preventDefault();\n          if (context.thumbs.has(target)) {\n            target.focus();\n          } else {\n            onSlideStart(event);\n          }\n        }),\n        onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n            onSlideEnd(event);\n          }\n        })\n      }\n    );\n  }\n);\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map(\n      (value) => convertValueToPercentage(value, context.min, context.max)\n    );\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          [orientation.startEdge]: offsetStart + \"%\",\n          [orientation.endEdge]: offsetEnd + \"%\"\n        }\n      }\n    );\n  }\n);\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = React.forwardRef(\n  (props, forwardedRef) => {\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const index = React.useMemo(\n      () => thumb ? getItems().findIndex((item) => item.ref.current === thumb) : -1,\n      [getItems, thumb]\n    );\n    return /* @__PURE__ */ jsx(SliderThumbImpl, { ...props, ref: composedRefs, index });\n  }\n);\nvar SliderThumbImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = useSize(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    React.useEffect(() => {\n      if (thumb) {\n        context.thumbs.add(thumb);\n        return () => {\n          context.thumbs.delete(thumb);\n        };\n      }\n    }, [thumb, context.thumbs]);\n    return /* @__PURE__ */ jsxs(\n      \"span\",\n      {\n        style: {\n          transform: \"var(--radix-slider-thumb-transform)\",\n          position: \"absolute\",\n          [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n          /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(\n            Primitive.span,\n            {\n              role: \"slider\",\n              \"aria-label\": props[\"aria-label\"] || label,\n              \"aria-valuemin\": context.min,\n              \"aria-valuenow\": value,\n              \"aria-valuemax\": context.max,\n              \"aria-orientation\": context.orientation,\n              \"data-orientation\": context.orientation,\n              \"data-disabled\": context.disabled ? \"\" : void 0,\n              tabIndex: context.disabled ? void 0 : 0,\n              ...thumbProps,\n              ref: composedRefs,\n              style: value === void 0 ? { display: \"none\" } : props.style,\n              onFocus: composeEventHandlers(props.onFocus, () => {\n                context.valueIndexToChangeRef.current = index;\n              })\n            }\n          ) }),\n          isFormControl && /* @__PURE__ */ jsx(\n            SliderBubbleInput,\n            {\n              name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n              form: context.form,\n              value\n            },\n            index\n          )\n        ]\n      }\n    );\n  }\n);\nSliderThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar SliderBubbleInput = React.forwardRef(\n  ({ __scopeSlider, value, ...props }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevValue = usePrevious(value);\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"input\", { bubbles: true });\n        setValue.call(input, value);\n        input.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ jsx(\n      Primitive.input,\n      {\n        style: { display: \"none\" },\n        ...props,\n        ref: composedRefs,\n        defaultValue: value\n      }\n    );\n  }\n);\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n  const nextValues = [...prevValues];\n  nextValues[atIndex] = nextValue;\n  return nextValues.sort((a, b) => a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n  const maxSteps = max - min;\n  const percentPerStep = 100 / maxSteps;\n  const percentage = percentPerStep * (value - min);\n  return clamp(percentage, [0, 100]);\n}\nfunction getLabel(index, totalValues) {\n  if (totalValues > 2) {\n    return `Value ${index + 1} of ${totalValues}`;\n  } else if (totalValues === 2) {\n    return [\"Minimum\", \"Maximum\"][index];\n  } else {\n    return void 0;\n  }\n}\nfunction getClosestValueIndex(values, nextValue) {\n  if (values.length === 1) return 0;\n  const distances = values.map((value) => Math.abs(value - nextValue));\n  const closestDistance = Math.min(...distances);\n  return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n  const halfWidth = width / 2;\n  const halfPercent = 50;\n  const offset = linearScale([0, halfPercent], [0, halfWidth]);\n  return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n  return values.slice(0, -1).map((value, index) => values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n  if (minStepsBetweenValues > 0) {\n    const stepsBetweenValues = getStepsBetweenValues(values);\n    const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n    return actualMinStepsBetweenValues >= minStepsBetweenValues;\n  }\n  return true;\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction getDecimalCount(value) {\n  return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n  const rounder = Math.pow(10, decimalCount);\n  return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\nexport {\n  Range,\n  Root,\n  Slider,\n  SliderRange,\n  SliderThumb,\n  SliderTrack,\n  Thumb,\n  Track,\n  createSliderScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}