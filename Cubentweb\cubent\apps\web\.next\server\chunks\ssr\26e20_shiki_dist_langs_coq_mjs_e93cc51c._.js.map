{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/coq.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Coq\", \"fileTypes\": [\"v\"], \"name\": \"coq\", \"patterns\": [{ \"comment\": \"Vernacular import keywords\", \"match\": \"\\\\b(From|Require|Import|Export|Local|Global|Include)\\\\b\", \"name\": \"keyword.control.import.coq\" }, { \"comment\": \"Vernacular scope keywords\", \"match\": \"\\\\b((Open|Close|Delimit|Undelimit|Bind)\\\\s+Scope)\\\\b\", \"name\": \"keyword.control.import.coq\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"2\": { \"name\": \"entity.name.function.theorem.coq\" } }, \"comment\": \"Theorem declarations\", \"match\": \"(Theorem|Lemma|Remark|Fact|Corollary|Property|Proposition|Goal)\\\\s+((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"2\": { \"name\": \"keyword.source.coq\" }, \"3\": { \"name\": \"entity.name.assumption.coq\" } }, \"comment\": \"Assumptions\", \"match\": \"\\\\b(Parameters?|Axioms?|Conjectures?|Variables?|Hypothesis|Hypotheses)(\\\\s+Inline)?\\\\b\\\\s*\\\\(?\\\\s*((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"3\": { \"name\": \"entity.name.assumption.coq\" } }, \"comment\": \"Context\", \"match\": \"\\\\b(Context)\\\\b\\\\s*`?\\\\s*(\\\\(|\\\\{)?\\\\s*((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"2\": { \"name\": \"keyword.source.coq\" }, \"3\": { \"name\": \"entity.name.function.coq\" } }, \"comment\": \"Definitions\", \"match\": \"(\\\\b(?:Program|Local)\\\\s+)?\\\\b(Definition|Fixpoint|CoFixpoint|Function|Example|Let(?:\\\\s+Fixpoint|\\\\s+CoFixpoint)?|Instance|Equations|Equations?)\\\\s+((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" } }, \"comment\": \"Obligations\", \"match\": \"\\\\b((Show\\\\s+)?Obligation\\\\s+Tactic|Obligations\\\\s+of|Obligation|Next\\\\s+Obligation(\\\\s+of)?|Solve\\\\s+Obligations(\\\\s+of)?|Solve\\\\s+All\\\\s+Obligations|Admit\\\\s+Obligations(\\\\s+of)?|Instance)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"3\": { \"name\": \"entity.name.type.coq\" } }, \"comment\": \"Type declarations\", \"match\": \"(CoInductive|Inductive|Variant|Record|Structure|Class)\\\\s+(>\\\\s*)?((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.source.coq\" }, \"2\": { \"name\": \"entity.name.function.ltac\" } }, \"comment\": \"Ltac declarations\", \"match\": \"(Ltac)\\\\s+((\\\\p{L}|[_\\\\u00A0])(\\\\p{L}|[0-9_\\\\u00A0'])*)\" }, { \"comment\": \"Vernacular keywords\", \"match\": \"\\\\b(Hint|Constructors|Resolve|Rewrite|Ltac|Implicit(\\\\s+Types)?|Set|Unset|Remove\\\\s+Printing|Arguments|Tactic\\\\s+Notation|Notation|Infix|Reserved\\\\s+Notation|Section|Module\\\\s+Type|Module|End|Check|Print|Eval|Search|Universe|Coercions?|Generalizable\\\\s+All|Generalizable\\\\s+Variable?|Existing\\\\s+Instance|Existing\\\\s+Class|Canonical|About|Locate|Collection|Typeclasses\\\\s+(Opaque|Transparent))\\\\b\", \"name\": \"keyword.source.coq\" }, { \"comment\": \"Proof keywords\", \"match\": \"\\\\b(Proof|Qed|Defined|Save|Abort(\\\\s+All)?|Undo(\\\\s+To)?|Restart|Focus|Unfocus|Unfocused|Show\\\\s+Proof|Show\\\\s+Existentials|Show|Unshelve)\\\\b\", \"name\": \"keyword.source.coq\" }, { \"comment\": \"Vernacular Debug keywords\", \"match\": \"\\\\b(Quit|Drop|Time|Redirect|Timeout|Fail)\\\\b\", \"name\": \"keyword.debug.coq\" }, { \"comment\": \"Admits are bad\", \"match\": \"\\\\b(admit|Admitted)\\\\b\", \"name\": \"invalid.illegal.admit.coq\" }, { \"comment\": \"Operators\", \"match\": \":|\\\\||=|<|>|\\\\*|\\\\+|-|\\\\{|\\\\}|\\u2260|\\u2228|\\u2227|\\u2194|\\xAC|\\u2192|\\u2264|\\u2265\", \"name\": \"keyword.operator.coq\" }, { \"comment\": \"Type keywords\", \"match\": \"\\\\b(forall|exists|Type|Set|Prop|nat|bool|option|list|unit|sum|prod|comparison|Empty_set)\\\\b|\\u2200|\\u2203\", \"name\": \"support.type.coq\" }, { \"comment\": \"Ltac keywords\", \"match\": \"\\\\b(try|repeat|rew|progress|fresh|solve|now|first|tryif|at|once|do|only)\\\\b\", \"name\": \"keyword.control.ltac\" }, { \"comment\": \"Common Ltac connectors\", \"match\": \"\\\\b(into|with|eqn|by|move|as|using)\\\\b\", \"name\": \"keyword.control.ltac\" }, { \"comment\": \"Gallina keywords\", \"match\": \"\\\\b(match|lazymatch|multimatch|fun|with|return|end|let|in|if|then|else|fix|for|where|and)\\\\b|\\u03BB\", \"name\": \"keyword.control.gallina\" }, { \"comment\": \"Ltac builtins\", \"match\": \"\\\\b(intro|intros|revert|induction|destruct|auto|eauto|tauto|eassumption|apply|eapply|assumption|constructor|econstructor|reflexivity|inversion|injection|assert|split|esplit|omega|fold|unfold|specialize|rewrite|erewrite|change|symmetry|refine|simpl|intuition|firstorder|generalize|idtac|exist|exists|eexists|elim|eelim|rename|subst|congruence|trivial|left|right|set|pose|discriminate|clear|clearbody|contradict|contradiction|exact|dependent|remember|case|easy|unshelve|pattern|transitivity|etransitivity|f_equal|exfalso|replace|abstract|cycle|swap|revgoals|shelve|unshelve)\\\\b\", \"name\": \"support.function.builtin.ltac\" }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\(\\\\*(?!#)\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment.block.coq\", \"patterns\": [{ \"include\": \"#block_comment\" }, { \"include\": \"#block_double_quoted_string\" }] }, { \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]+)|(\\\\d+(\\\\.\\\\d+)?))\\\\b\", \"name\": \"constant.numeric.gallina\" }, { \"comment\": \"Gallina builtin constructors\", \"match\": \"\\\\b(True|False|tt|false|true|Some|None|nil|cons|pair|inl|inr|O|S|Eq|Lt|Gt|id|ex|all|unique)\\\\b\", \"name\": \"constant.language.constructor.gallina\" }, { \"match\": \"\\\\b_\\\\b\", \"name\": \"constant.language.wildcard.coq\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.coq\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.coq\" } }, \"name\": \"string.quoted.double.coq\" }], \"repository\": { \"block_comment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\(\\\\*(?!#)\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment.block.coq\", \"patterns\": [{ \"include\": \"#block_comment\" }, { \"include\": \"#block_double_quoted_string\" }] }, \"block_double_quoted_string\": { \"applyEndPatternLast\": 1, \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.coq\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.coq\" } }, \"name\": \"string.quoted.double.coq\" } }, \"scopeName\": \"source.coq\" });\nvar coq = [\n  lang\n];\n\nexport { coq as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAI;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;YAA8B,SAAS;YAA2D,QAAQ;QAA6B;QAAG;YAAE,WAAW;YAA6B,SAAS;YAAwD,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,WAAW;YAAwB,SAAS;QAAmH;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAe,SAAS;QAAkJ;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAW,SAAS;QAAuF;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,WAAW;YAAe,SAAS;QAAqM;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,WAAW;YAAe,SAAS;QAAoM;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,WAAW;YAAqB,SAAS;QAAkH;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAqB,SAAS;QAA0D;QAAG;YAAE,WAAW;YAAuB,SAAS;YAAgZ,QAAQ;QAAqB;QAAG;YAAE,WAAW;YAAkB,SAAS;YAAiJ,QAAQ;QAAqB;QAAG;YAAE,WAAW;YAA6B,SAAS;YAAgD,QAAQ;QAAoB;QAAG;YAAE,WAAW;YAAkB,SAAS;YAA0B,QAAQ;QAA4B;QAAG;YAAE,WAAW;YAAa,SAAS;YAAuF,QAAQ;QAAuB;QAAG;YAAE,WAAW;YAAiB,SAAS;YAA6G,QAAQ;QAAmB;QAAG;YAAE,WAAW;YAAiB,SAAS;YAA+E,QAAQ;QAAuB;QAAG;YAAE,WAAW;YAA0B,SAAS;YAA0C,QAAQ;QAAuB;QAAG;YAAE,WAAW;YAAoB,SAAS;YAAuG,QAAQ;QAA0B;QAAG;YAAE,WAAW;YAAiB,SAAS;YAAmkB,QAAQ;QAAgC;QAAG;YAAE,uBAAuB;YAAG,SAAS;YAAe,OAAO;YAAU,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiD,QAAQ;QAA2B;QAAG;YAAE,WAAW;YAAgC,SAAS;YAAkG,QAAQ;QAAwC;QAAG;YAAE,SAAS;YAAW,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA2B;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,uBAAuB;YAAG,SAAS;YAAe,OAAO;YAAU,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,8BAA8B;YAAE,uBAAuB;YAAG,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA2B;IAAE;IAAG,aAAa;AAAa;AACl6L,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}