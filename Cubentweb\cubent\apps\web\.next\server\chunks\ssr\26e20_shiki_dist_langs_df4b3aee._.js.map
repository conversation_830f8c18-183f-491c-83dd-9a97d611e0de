{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/regexp.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"RegExp\", \"fileTypes\": [\"re\"], \"name\": \"regexp\", \"patterns\": [{ \"include\": \"#regexp-expression\" }], \"repository\": { \"codetags\": { \"captures\": { \"1\": { \"name\": \"keyword.codetag.notation.python\" } }, \"match\": \"(?:\\\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\\\b)\" }, \"fregexp-base-expression\": { \"patterns\": [{ \"include\": \"#fregexp-quantifier\" }, { \"include\": \"#fstring-formatting-braces\" }, { \"match\": \"\\\\{.*?\\\\}\" }, { \"include\": \"#regexp-base-common\" }] }, \"fregexp-quantifier\": { \"match\": \"\\\\{\\\\{(\\\\d+|\\\\d+,(\\\\d+)?|,\\\\d+)\\\\}\\\\}\", \"name\": \"keyword.operator.quantifier.regexp\" }, \"fstring-formatting-braces\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" }, \"2\": { \"name\": \"invalid.illegal.brace.python\" }, \"3\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"comment\": \"empty braces are illegal\", \"match\": \"({)(\\\\s*?)(})\" }, { \"match\": \"({{|}})\", \"name\": \"constant.character.escape.python\" }] }, \"regexp-backreference\": { \"captures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.backreference.regexp\" }, \"3\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp\" } }, \"match\": \"(\\\\()(\\\\?P=\\\\w+(?:\\\\s+[0-9A-Za-z]+)?)(\\\\))\", \"name\": \"meta.backreference.named.regexp\" }, \"regexp-backreference-number\": { \"captures\": { \"1\": { \"name\": \"entity.name.tag.backreference.regexp\" } }, \"match\": \"(\\\\\\\\[1-9]\\\\d?)\", \"name\": \"meta.backreference.regexp\" }, \"regexp-base-common\": { \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"support.other.match.any.regexp\" }, { \"match\": \"\\\\^\", \"name\": \"support.other.match.begin.regexp\" }, { \"match\": \"\\\\$\", \"name\": \"support.other.match.end.regexp\" }, { \"match\": \"[+*?]\\\\??\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.disjunction.regexp\" }, { \"include\": \"#regexp-escape-sequence\" }] }, \"regexp-base-expression\": { \"patterns\": [{ \"include\": \"#regexp-quantifier\" }, { \"include\": \"#regexp-base-common\" }] }, \"regexp-character-set\": { \"patterns\": [{ \"match\": \"\\\\[\\\\^?\\\\](?!.*?\\\\])\" }, { \"begin\": \"(\\\\[)(\\\\^)?(\\\\])?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.character.set.begin.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" }, \"3\": { \"name\": \"constant.character.set.regexp\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.character.set.end.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.character.set.regexp\", \"patterns\": [{ \"include\": \"#regexp-charecter-set-escapes\" }, { \"match\": \"[^\\\\n]\", \"name\": \"constant.character.set.regexp\" }] }] }, \"regexp-charecter-set-escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[abfnrtv\\\\\\\\]\", \"name\": \"constant.character.escape.regexp\" }, { \"include\": \"#regexp-escape-special\" }, { \"match\": \"\\\\\\\\([0-7]{1,3})\", \"name\": \"constant.character.escape.regexp\" }, { \"include\": \"#regexp-escape-character\" }, { \"include\": \"#regexp-escape-unicode\" }, { \"include\": \"#regexp-escape-catchall\" }] }, \"regexp-comments\": { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.comment.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.comment.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"comment.regexp\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"regexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[0-9A-Za-z]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-escape-catchall\": { \"match\": \"\\\\\\\\(.|\\\\n)\", \"name\": \"constant.character.escape.regexp\" }, \"regexp-escape-character\": { \"match\": \"\\\\\\\\(x[0-9A-Fa-f]{2}|0[0-7]{1,2}|[0-7]{3})\", \"name\": \"constant.character.escape.regexp\" }, \"regexp-escape-sequence\": { \"patterns\": [{ \"include\": \"#regexp-escape-special\" }, { \"include\": \"#regexp-escape-character\" }, { \"include\": \"#regexp-escape-unicode\" }, { \"include\": \"#regexp-backreference-number\" }, { \"include\": \"#regexp-escape-catchall\" }] }, \"regexp-escape-special\": { \"match\": \"\\\\\\\\([AbBdDsSwWZ])\", \"name\": \"support.other.escape.special.regexp\" }, \"regexp-escape-unicode\": { \"match\": \"\\\\\\\\(u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\", \"name\": \"constant.character.unicode.regexp\" }, \"regexp-expression\": { \"patterns\": [{ \"include\": \"#regexp-base-expression\" }, { \"include\": \"#regexp-character-set\" }, { \"include\": \"#regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#regexp-lookahead\" }, { \"include\": \"#regexp-lookahead-negative\" }, { \"include\": \"#regexp-lookbehind\" }, { \"include\": \"#regexp-lookbehind-negative\" }, { \"include\": \"#regexp-conditional\" }, { \"include\": \"#regexp-parentheses-non-capturing\" }, { \"include\": \"#regexp-parentheses\" }] }, \"regexp-flags\": { \"match\": \"\\\\(\\\\?[aiLmsux]+\\\\)\", \"name\": \"storage.modifier.flag.regexp\" }, \"regexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-named-group\": { \"begin\": \"(\\\\()(\\\\?P<\\\\w+(?:\\\\s+[0-9A-Za-z]+)?>)\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#regexp-expression\" }] }, \"regexp-quantifier\": { \"match\": \"\\\\{(\\\\d+|\\\\d+,(\\\\d+)?|,\\\\d+)\\\\}\", \"name\": \"keyword.operator.quantifier.regexp\" } }, \"scopeName\": \"source.regexp.python\", \"aliases\": [\"regex\"] });\nvar regexp = [\n  lang\n];\n\nexport { regexp as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;KAAK;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAqB;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAA2C;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,SAAS;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAyC,QAAQ;QAAqC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,WAAW;oBAA4B,SAAS;gBAAgB;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAmC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4F;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0F;YAAE;YAAG,SAAS;YAA8C,QAAQ;QAAkC;QAAG,+BAA+B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;YAAmB,QAAQ;QAA4B;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAsC;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;gBAAuB;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmE;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiE;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAgC;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAkD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8F;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAe,QAAQ;QAAmC;QAAG,2BAA2B;YAAE,SAAS;YAA8C,QAAQ;QAAmC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAsB,QAAQ;QAAsC;QAAG,yBAAyB;YAAE,SAAS;YAAyC,QAAQ;QAAoC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAuB,QAAQ;QAA+B;QAAG,oBAAoB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiF;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0F;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmF;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4F;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8E;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4E;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwE;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsE;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oCAAoC;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsF;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoF;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAmC,QAAQ;QAAqC;IAAE;IAAG,aAAa;IAAwB,WAAW;QAAC;KAAQ;AAAC;AACz3Q,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/glsl.mjs"], "sourcesContent": ["import c from './c.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"GLSL\", \"fileTypes\": [\"vs\", \"fs\", \"gs\", \"vsh\", \"fsh\", \"gsh\", \"vshader\", \"fshader\", \"gshader\", \"vert\", \"frag\", \"geom\", \"f.glsl\", \"v.glsl\", \"g.glsl\"], \"foldingStartMarker\": \"/\\\\*\\\\*|\\\\{\\\\s*$\", \"foldingStopMarker\": \"\\\\*\\\\*/|^\\\\s*\\\\}\", \"name\": \"glsl\", \"patterns\": [{ \"match\": \"\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\b\", \"name\": \"keyword.control.glsl\" }, { \"match\": \"\\\\b(void|bool|int|uint|float|vec2|vec3|vec4|bvec2|bvec3|bvec4|ivec2|ivec2|ivec3|uvec2|uvec2|uvec3|mat2|mat3|mat4|mat2x2|mat2x3|mat2x4|mat3x2|mat3x3|mat3x4|mat4x2|mat4x3|mat4x4|sampler[1|2|3]D|samplerCube|sampler2DRect|sampler[1|2]DShadow|sampler2DRectShadow|sampler[1|2]DArray|sampler[1|2]DArrayShadow|samplerBuffer|sampler2DMS|sampler2DMSArray|struct|isampler[1|2|3]D|isamplerCube|isampler2DRect|isampler[1|2]DArray|isamplerBuffer|isampler2DMS|isampler2DMSArray|usampler[1|2|3]D|usamplerCube|usampler2DRect|usampler[1|2]DArray|usamplerBuffer|usampler2DMS|usampler2DMSArray)\\\\b\", \"name\": \"storage.type.glsl\" }, { \"match\": \"\\\\b(attribute|centroid|const|flat|in|inout|invariant|noperspective|out|smooth|uniform|varying)\\\\b\", \"name\": \"storage.modifier.glsl\" }, { \"match\": \"\\\\b(gl_BackColor|gl_BackLightModelProduct|gl_BackLightProduct|gl_BackMaterial|gl_BackSecondaryColor|gl_ClipDistance|gl_ClipPlane|gl_ClipVertex|gl_Color|gl_DepthRange|gl_DepthRangeParameters|gl_EyePlaneQ|gl_EyePlaneR|gl_EyePlaneS|gl_EyePlaneT|gl_Fog|gl_FogCoord|gl_FogFragCoord|gl_FogParameters|gl_FragColor|gl_FragCoord|gl_FragDat|gl_FragDept|gl_FrontColor|gl_FrontFacing|gl_FrontLightModelProduct|gl_FrontLightProduct|gl_FrontMaterial|gl_FrontSecondaryColor|gl_InstanceID|gl_Layer|gl_LightModel|gl_LightModelParameters|gl_LightModelProducts|gl_LightProducts|gl_LightSource|gl_LightSourceParameters|gl_MaterialParameters|gl_ModelViewMatrix|gl_ModelViewMatrixInverse|gl_ModelViewMatrixInverseTranspose|gl_ModelViewMatrixTranspose|gl_ModelViewProjectionMatrix|gl_ModelViewProjectionMatrixInverse|gl_ModelViewProjectionMatrixInverseTranspose|gl_ModelViewProjectionMatrixTranspose|gl_MultiTexCoord[0-7]|gl_Normal|gl_NormalMatrix|gl_NormalScale|gl_ObjectPlaneQ|gl_ObjectPlaneR|gl_ObjectPlaneS|gl_ObjectPlaneT|gl_Point|gl_PointCoord|gl_PointParameters|gl_PointSize|gl_Position|gl_PrimitiveIDIn|gl_ProjectionMatrix|gl_ProjectionMatrixInverse|gl_ProjectionMatrixInverseTranspose|gl_ProjectionMatrixTranspose|gl_SecondaryColor|gl_TexCoord|gl_TextureEnvColor|gl_TextureMatrix|gl_TextureMatrixInverse|gl_TextureMatrixInverseTranspose|gl_TextureMatrixTranspose|gl_Vertex|gl_VertexIDh)\\\\b\", \"name\": \"support.variable.glsl\" }, { \"match\": \"\\\\b(gl_MaxClipPlanes|gl_MaxCombinedTextureImageUnits|gl_MaxDrawBuffers|gl_MaxFragmentUniformComponents|gl_MaxLights|gl_MaxTextureCoords|gl_MaxTextureImageUnits|gl_MaxTextureUnits|gl_MaxVaryingFloats|gl_MaxVertexAttribs|gl_MaxVertexTextureImageUnits|gl_MaxVertexUniformComponents)\\\\b\", \"name\": \"support.constant.glsl\" }, { \"match\": \"\\\\b(abs|acos|all|any|asin|atan|ceil|clamp|cos|cross|degrees|dFdx|dFdy|distance|dot|equal|exp|exp2|faceforward|floor|fract|ftransform|fwidth|greaterThan|greaterThanEqual|inversesqrt|length|lessThan|lessThanEqual|log|log2|matrixCompMult|max|min|mix|mod|noise[1-4]|normalize|not|notEqual|outerProduct|pow|radians|reflect|refract|shadow1D|shadow1DLod|shadow1DProj|shadow1DProjLod|shadow2D|shadow2DLod|shadow2DProj|shadow2DProjLod|sign|sin|smoothstep|sqrt|step|tan|texture1D|texture1DLod|texture1DProj|texture1DProjLod|texture2D|texture2DLod|texture2DProj|texture2DProjLod|texture3D|texture3DLod|texture3DProj|texture3DProjLod|textureCube|textureCubeLod|transpose)\\\\b\", \"name\": \"support.function.glsl\" }, { \"match\": \"\\\\b(asm|double|enum|extern|goto|inline|long|short|sizeof|static|typedef|union|unsigned|volatile)\\\\b\", \"name\": \"invalid.illegal.glsl\" }, { \"include\": \"source.c\" }], \"scopeName\": \"source.glsl\", \"embeddedLangs\": [\"c\"] });\nvar glsl = [\n  ...c,\n  lang\n];\n\nexport { glsl as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;QAAM;QAAM;QAAM;QAAO;QAAO;QAAO;QAAW;QAAW;QAAW;QAAQ;QAAQ;QAAQ;QAAU;QAAU;KAAS;IAAE,sBAAsB;IAAoB,qBAAqB;IAAoB,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAkF,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAqkB,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAAqG,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAm2C,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAA8R,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAA0pB,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAuG,QAAQ;QAAuB;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,aAAa;IAAe,iBAAiB;QAAC;KAAI;AAAC;AAC1zH,IAAI,OAAO;OACN,+LAAA,CAAA,UAAC;IACJ;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/sql.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"SQL\", \"name\": \"sql\", \"patterns\": [{ \"match\": \"((?<!@)@)\\\\b(\\\\w+)\\\\b\", \"name\": \"text.variable\" }, { \"match\": \"(\\\\[)[^\\\\]]*(\\\\])\", \"name\": \"text.bracketed\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.sql\" }, \"5\": { \"name\": \"entity.name.function.sql\" } }, \"match\": \"(?i:^\\\\s*(create(?:\\\\s+or\\\\s+replace)?)\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)(['\\\"`]?)(\\\\w+)\\\\4\", \"name\": \"meta.create.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.sql\" } }, \"match\": \"(?i:^\\\\s*(drop)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\", \"name\": \"meta.drop.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.table.sql\" }, \"3\": { \"name\": \"entity.name.function.sql\" }, \"4\": { \"name\": \"keyword.other.cascade.sql\" } }, \"match\": \"(?i:\\\\s*(drop)\\\\s+(table)\\\\s+(\\\\w+)(\\\\s+cascade)?\\\\b)\", \"name\": \"meta.drop.sql\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.create.sql\" }, \"2\": { \"name\": \"keyword.other.table.sql\" } }, \"match\": \"(?i:^\\\\s*(alter)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)\", \"name\": \"meta.alter.sql\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.sql\" }, \"2\": { \"name\": \"storage.type.sql\" }, \"3\": { \"name\": \"constant.numeric.sql\" }, \"4\": { \"name\": \"storage.type.sql\" }, \"5\": { \"name\": \"constant.numeric.sql\" }, \"6\": { \"name\": \"storage.type.sql\" }, \"7\": { \"name\": \"constant.numeric.sql\" }, \"8\": { \"name\": \"constant.numeric.sql\" }, \"9\": { \"name\": \"storage.type.sql\" }, \"10\": { \"name\": \"constant.numeric.sql\" }, \"11\": { \"name\": \"storage.type.sql\" }, \"12\": { \"name\": \"storage.type.sql\" }, \"13\": { \"name\": \"storage.type.sql\" }, \"14\": { \"name\": \"constant.numeric.sql\" }, \"15\": { \"name\": \"storage.type.sql\" } }, \"match\": \"(?i)\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\b|\\\\b(bit\\\\svarying|character\\\\s(?:varying)?|tinyint|var\\\\schar|float|interval)\\\\((\\\\d+)\\\\)|\\\\b(char|number|varchar\\\\d?)\\\\b(?:\\\\((\\\\d+)\\\\))?|\\\\b(numeric|decimal)\\\\b(?:\\\\((\\\\d+),(\\\\d+)\\\\))?|\\\\b(times?)\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\swith(?:out)?\\\\stime\\\\szone\\\\b)?|\\\\b(timestamp)(?:(s|tz))?\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\s(with|without)\\\\stime\\\\szone\\\\b)?\" }, { \"match\": \"(?i:\\\\b((?:primary|foreign)\\\\s+key|references|on\\\\sdelete(\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\b)\", \"name\": \"storage.modifier.sql\" }, { \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.sql\" }, { \"match\": \"(?i:\\\\b(select(\\\\s+(all|distinct))?|insert\\\\s+(ignore\\\\s+)?into|update|delete|from|set|where|group\\\\s+by|or|like|and|union(\\\\s+all)?|having|order\\\\s+by|limit|cross\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\s+outer)?)\\\\s+join|natural(\\\\s+(inner|(left|right|full)(\\\\s+outer)?))?\\\\s+join)\\\\b)\", \"name\": \"keyword.other.DML.sql\" }, { \"match\": \"(?i:\\\\b(on|off|((is\\\\s+)?not\\\\s+)?null)\\\\b)\", \"name\": \"keyword.other.DDL.create.II.sql\" }, { \"match\": \"(?i:\\\\bvalues\\\\b)\", \"name\": \"keyword.other.DML.II.sql\" }, { \"match\": \"(?i:\\\\b(begin(\\\\s+work)?|start\\\\s+transaction|commit(\\\\s+work)?|rollback(\\\\s+work)?)\\\\b)\", \"name\": \"keyword.other.LUW.sql\" }, { \"match\": \"(?i:\\\\b(grant(\\\\swith\\\\sgrant\\\\soption)?|revoke)\\\\b)\", \"name\": \"keyword.other.authorization.sql\" }, { \"match\": \"(?i:\\\\bin\\\\b)\", \"name\": \"keyword.other.data-integrity.sql\" }, { \"match\": \"(?i:^\\\\s*(comment\\\\s+on\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\s+.*?\\\\s+(is)\\\\s+)\", \"name\": \"keyword.other.object-comments.sql\" }, { \"match\": \"(?i)\\\\bAS\\\\b\", \"name\": \"keyword.other.alias.sql\" }, { \"match\": \"(?i)\\\\b(DESC|ASC)\\\\b\", \"name\": \"keyword.other.order.sql\" }, { \"match\": \"\\\\*\", \"name\": \"keyword.operator.star.sql\" }, { \"match\": \"[!<>]?=|<>|<|>\", \"name\": \"keyword.operator.comparison.sql\" }, { \"match\": \"-|\\\\+|/\", \"name\": \"keyword.operator.math.sql\" }, { \"match\": \"\\\\|\\\\|\", \"name\": \"keyword.operator.concatenator.sql\" }, { \"captures\": { \"1\": { \"name\": \"support.function.aggregate.sql\" } }, \"match\": \"(?i)\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.analytic.sql\" } }, \"match\": \"(?i)\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.bitmanipulation.sql\" } }, \"match\": \"(?i)\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.conversion.sql\" } }, \"match\": \"(?i)\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.collation.sql\" } }, \"match\": \"(?i)\\\\b(collationproperty|tertiary_weights)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.cryptographic.sql\" } }, \"match\": \"(?i)\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.cursor.sql\" } }, \"match\": \"(?i)\\\\b(cursor_status)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.datetime.sql\" } }, \"match\": \"(?i)\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.datatype.sql\" } }, \"match\": \"(?i)\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.expression.sql\" } }, \"match\": \"(?i)\\\\b(coalesce|nullif)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.globalvar.sql\" } }, \"match\": \"(?<!@)@@(?i)\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.json.sql\" } }, \"match\": \"(?i)\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.logical.sql\" } }, \"match\": \"(?i)\\\\b(choose|iif|greatest|least)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.mathematical.sql\" } }, \"match\": \"(?i)\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.metadata.sql\" } }, \"match\": \"(?i)\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.ranking.sql\" } }, \"match\": \"(?i)\\\\b(rank|dense_rank|ntile|row_number)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.rowset.sql\" } }, \"match\": \"(?i)\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.security.sql\" } }, \"match\": \"(?i)\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.string.sql\" } }, \"match\": \"(?i)\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.system.sql\" } }, \"match\": \"(?i)\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"support.function.textimage.sql\" } }, \"match\": \"(?i)\\\\b(patindex|textptr|textvalid)\\\\b\\\\s*\\\\(\" }, { \"captures\": { \"1\": { \"name\": \"constant.other.database-name.sql\" }, \"2\": { \"name\": \"constant.other.table-name.sql\" } }, \"match\": \"(\\\\w+?)\\\\.(\\\\w+)\" }, { \"include\": \"#strings\" }, { \"include\": \"#regexps\" }, { \"match\": \"\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\s+or\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\b\", \"name\": \"keyword.other.sql\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.scope.begin.sql\" }, \"2\": { \"name\": \"punctuation.section.scope.end.sql\" } }, \"comment\": \"Allow for special \\u21A9 behavior\", \"match\": \"(\\\\()(\\\\))\", \"name\": \"meta.block.sql\" }], \"repository\": { \"comment-block\": { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.sql\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block\", \"patterns\": [{ \"include\": \"#comment-block\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=--)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.sql\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.sql\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.sql\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.sql\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [] }, { \"include\": \"#comment-block\" }] }, \"regexps\": { \"patterns\": [{ \"begin\": \"/(?=\\\\S.*/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.regexp.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"match\": \"\\\\\\\\/\", \"name\": \"constant.character.escape.slash.sql\" }] }, { \"begin\": \"%r\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"comment\": \"We should probably handle nested bracket pairs!?! -- Allan\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.regexp.modr.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }] }, \"string_escape\": { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.sql\" }, \"string_interpolation\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"3\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"match\": \"(#\\\\{)([^}]*)(\\\\})\", \"name\": \"string.interpolated.sql\" }, \"strings\": { \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"3\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": \"(N)?(')[^']*(')\", \"name\": \"string.quoted.single.sql\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.single.sql\", \"patterns\": [{ \"include\": \"#string_escape\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": \"(`)[^`\\\\\\\\]*(`)\", \"name\": \"string.quoted.other.backtick.sql\" }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.other.backtick.sql\", \"patterns\": [{ \"include\": \"#string_escape\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.sql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"comment\": \"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\", \"match\": '(\")[^\"#]*(\")', \"name\": \"string.quoted.double.sql\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.quoted.double.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }, { \"begin\": \"%\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.sql\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.sql\" } }, \"name\": \"string.other.quoted.brackets.sql\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }] } }, \"scopeName\": \"source.sql\" });\nvar sql = [\n  lang\n];\n\nexport { sql as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAyB,QAAQ;QAAgB;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAiB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;YAA4O,QAAQ;QAAkB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAiL,QAAQ;QAAgB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAAyD,QAAQ;QAAgB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;YAAmM,QAAQ;QAAiB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAuB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;gBAAG,MAAM;oBAAE,QAAQ;gBAAuB;gBAAG,MAAM;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,SAAS;QAA4gB;QAAG;YAAE,SAAS;YAAyH,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAc,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAiT,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAA+C,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAA4F,QAAQ;QAAwB;QAAG;YAAE,SAAS;YAAwD,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAiB,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAsK,QAAQ;QAAoC;QAAG;YAAE,SAAS;YAAgB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAwB,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAO,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAkB,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAW,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAAU,QAAQ;QAAoC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAuL;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA4G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;QAAsE;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAAuE;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAwD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;QAAuY;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAAmC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA+U;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAkG;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAAqC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAwW;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAA2G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA+C;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAA8I;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAwiB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAAsD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAA+G;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA0S;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAAkQ;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;QAA4Z;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAgD;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAmB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,SAAS;YAAquT,QAAQ;QAAoB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,WAAW;YAAqC,SAAS;YAAc,QAAQ;QAAiB;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAQ,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAA+B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,YAAY,EAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAA8D,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAS,QAAQ;QAAgC;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAsB,QAAQ;QAA0B;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAmB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAmB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,WAAW;oBAAyI,SAAS;oBAAgB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AAChwvB,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/r.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"R\", \"name\": \"r\", \"patterns\": [{ \"include\": \"#roxygen\" }, { \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#keywords\" }, { \"include\": \"#storage-type\" }, { \"include\": \"#strings\" }, { \"include\": \"#brackets\" }, { \"include\": \"#function-declarations\" }, { \"include\": \"#lambda-functions\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#function-calls\" }, { \"include\": \"#general-variables\" }], \"repository\": { \"brackets\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.r\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.r\" } }, \"patterns\": [{ \"include\": \"source.r\" }] }, { \"begin\": \"\\\\[(?!\\\\[)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.single.begin.r\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.single.end.r\" } }, \"patterns\": [{ \"include\": \"source.r\" }] }, { \"begin\": \"\\\\[\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.double.begin.r\" } }, \"contentName\": \"meta.item-access.arguments.r\", \"end\": \"\\\\]\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.double.end.r\" } }, \"patterns\": [{ \"include\": \"source.r\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.begin.r\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.r\" } }, \"patterns\": [{ \"include\": \"source.r\" }] }] }, \"builtin-functions\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(abbreviate|abs|acos|acosh|activeBindingFunction|addNA|addTaskCallback|agrep|agrepl|alist|all|all\\\\.equal|all\\\\.equal\\\\.character|all\\\\.equal\\\\.default|all\\\\.equal\\\\.environment|all\\\\.equal\\\\.envRefClass|all\\\\.equal\\\\.factor|all\\\\.equal\\\\.formula|all\\\\.equal\\\\.function|all\\\\.equal\\\\.language|all\\\\.equal\\\\.list|all\\\\.equal\\\\.numeric|all\\\\.equal\\\\.POSIXt|all\\\\.equal\\\\.raw|all\\\\.names|allowInterrupts|all\\\\.vars|any|anyDuplicated|anyDuplicated\\\\.array|anyDuplicated\\\\.data\\\\.frame|anyDuplicated\\\\.default|anyDuplicated\\\\.matrix|anyNA|anyNA\\\\.data\\\\.frame|anyNA\\\\.numeric_version|anyNA\\\\.POSIXlt|aperm|aperm\\\\.default|aperm\\\\.table|append|apply|Arg|args|array|arrayInd|as\\\\.array|as\\\\.array\\\\.default|as\\\\.call|as\\\\.character|as\\\\.character\\\\.condition|as\\\\.character\\\\.Date|as\\\\.character\\\\.default|as\\\\.character\\\\.error|as\\\\.character\\\\.factor|as\\\\.character\\\\.hexmode|as\\\\.character\\\\.numeric_version|as\\\\.character\\\\.octmode|as\\\\.character\\\\.POSIXt|as\\\\.character\\\\.srcref|as\\\\.complex|as\\\\.data\\\\.frame|as\\\\.data\\\\.frame\\\\.array|as\\\\.data\\\\.frame\\\\.AsIs|as\\\\.data\\\\.frame\\\\.character|as\\\\.data\\\\.frame\\\\.complex|as\\\\.data\\\\.frame\\\\.data\\\\.frame|as\\\\.data\\\\.frame\\\\.Date|as\\\\.data\\\\.frame\\\\.default|as\\\\.data\\\\.frame\\\\.difftime|as\\\\.data\\\\.frame\\\\.factor|as\\\\.data\\\\.frame\\\\.integer|as\\\\.data\\\\.frame\\\\.list|as\\\\.data\\\\.frame\\\\.logical|as\\\\.data\\\\.frame\\\\.matrix|as\\\\.data\\\\.frame\\\\.model\\\\.matrix|as\\\\.data\\\\.frame\\\\.noquote|as\\\\.data\\\\.frame\\\\.numeric|as\\\\.data\\\\.frame\\\\.numeric_version|as\\\\.data\\\\.frame\\\\.ordered|as\\\\.data\\\\.frame\\\\.POSIXct|as\\\\.data\\\\.frame\\\\.POSIXlt|as\\\\.data\\\\.frame\\\\.raw|as\\\\.data\\\\.frame\\\\.table|as\\\\.data\\\\.frame\\\\.ts|as\\\\.data\\\\.frame\\\\.vector|as\\\\.Date|as\\\\.Date\\\\.character|as\\\\.Date\\\\.default|as\\\\.Date\\\\.factor|as\\\\.Date\\\\.numeric|as\\\\.Date\\\\.POSIXct|as\\\\.Date\\\\.POSIXlt|as\\\\.difftime|as\\\\.double|as\\\\.double\\\\.difftime|as\\\\.double\\\\.POSIXlt|as\\\\.environment|as\\\\.expression|as\\\\.expression\\\\.default|as\\\\.factor|as\\\\.function|as\\\\.function\\\\.default|as\\\\.hexmode|asin|asinh|as\\\\.integer|as\\\\.list|as\\\\.list\\\\.data\\\\.frame|as\\\\.list\\\\.Date|as\\\\.list\\\\.default|as\\\\.list\\\\.difftime|as\\\\.list\\\\.environment|as\\\\.list\\\\.factor|as\\\\.list\\\\.function|as\\\\.list\\\\.numeric_version|as\\\\.list\\\\.POSIXct|as\\\\.list\\\\.POSIXlt|as\\\\.logical|as\\\\.logical\\\\.factor|as\\\\.matrix|as\\\\.matrix\\\\.data\\\\.frame|as\\\\.matrix\\\\.default|as\\\\.matrix\\\\.noquote|as\\\\.matrix\\\\.POSIXlt|as\\\\.name|asNamespace|as\\\\.null|as\\\\.null\\\\.default|as\\\\.numeric|as\\\\.numeric_version|as\\\\.octmode|as\\\\.ordered|as\\\\.package_version|as\\\\.pairlist|asplit|as\\\\.POSIXct|as\\\\.POSIXct\\\\.Date|as\\\\.POSIXct\\\\.default|as\\\\.POSIXct\\\\.numeric|as\\\\.POSIXct\\\\.POSIXlt|as\\\\.POSIXlt|as\\\\.POSIXlt\\\\.character|as\\\\.POSIXlt\\\\.Date|as\\\\.POSIXlt\\\\.default|as\\\\.POSIXlt\\\\.factor|as\\\\.POSIXlt\\\\.numeric|as\\\\.POSIXlt\\\\.POSIXct|as\\\\.qr|as\\\\.raw|asS3|asS4|assign|as\\\\.single|as\\\\.single\\\\.default|as\\\\.symbol|as\\\\.table|as\\\\.table\\\\.default|as\\\\.vector|as\\\\.vector\\\\.factor|atan|atan2|atanh|attach|attachNamespace|attr|attr\\\\.all\\\\.equal|attributes|autoload|autoloader|backsolve|baseenv|basename|besselI|besselJ|besselK|besselY|beta|bindingIsActive|bindingIsLocked|bindtextdomain|bitwAnd|bitwNot|bitwOr|bitwShiftL|bitwShiftR|bitwXor|body|bquote|break|browser|browserCondition|browserSetDebug|browserText|builtins|by|by\\\\.data\\\\.frame|by\\\\.default|bzfile|c|call|callCC|capabilities|casefold|cat|cbind|cbind\\\\.data\\\\.frame|c\\\\.Date|c\\\\.difftime|ceiling|c\\\\.factor|character|char\\\\.expand|charmatch|charToRaw|chartr|check_tzones|chkDots|chol|chol2inv|chol\\\\.default|choose|class|clearPushBack|close|closeAllConnections|close\\\\.connection|close\\\\.srcfile|close\\\\.srcfilealias|c\\\\.noquote|c\\\\.numeric_version|col|colMeans|colnames|colSums|commandArgs|comment|complex|computeRestarts|conditionCall|conditionCall\\\\.condition|conditionMessage|conditionMessage\\\\.condition|conflictRules|conflicts|Conj|contributors|cos|cosh|cospi|c\\\\.POSIXct|c\\\\.POSIXlt|crossprod|Cstack_info|cummax|cummin|cumprod|cumsum|curlGetHeaders|cut|cut\\\\.Date|cut\\\\.default|cut\\\\.POSIXt|c\\\\.warnings|data\\\\.class|data\\\\.frame|data\\\\.matrix|date|debug|debuggingState|debugonce|default\\\\.stringsAsFactors|delayedAssign|deparse|deparse1|det|detach|determinant|determinant\\\\.matrix|dget|diag|diff|diff\\\\.Date|diff\\\\.default|diff\\\\.difftime|diff\\\\.POSIXt|difftime|digamma|dim|dim\\\\.data\\\\.frame|dimnames|dimnames\\\\.data\\\\.frame|dir|dir\\\\.create|dir\\\\.exists|dirname|do\\\\.call|dontCheck|double|dput|dQuote|drop|droplevels|droplevels\\\\.data\\\\.frame|droplevels\\\\.factor|dump|duplicated|duplicated\\\\.array|duplicated\\\\.data\\\\.frame|duplicated\\\\.default|duplicated\\\\.matrix|duplicated\\\\.numeric_version|duplicated\\\\.POSIXlt|duplicated\\\\.warnings|dynGet|dyn\\\\.load|dyn\\\\.unload|eapply|eigen|emptyenv|enc2native|enc2utf8|encodeString|Encoding|endsWith|enquote|environment|environmentIsLocked|environmentName|env\\\\.profile|errorCondition|eval|eval\\\\.parent|evalq|exists|exp|expand\\\\.grid|expm1|expression|extSoftVersion|factor|factorial|fifo|file|file\\\\.access|file\\\\.append|file\\\\.choose|file\\\\.copy|file\\\\.create|file\\\\.exists|file\\\\.info|file\\\\.link|file\\\\.mode|file\\\\.mtime|file\\\\.path|file\\\\.remove|file\\\\.rename|file\\\\.show|file\\\\.size|file\\\\.symlink|Filter|Find|findInterval|find\\\\.package|findPackageEnv|findRestart|floor|flush|flush\\\\.connection|for|force|forceAndCall|formals|format|format\\\\.AsIs|formatC|format\\\\.data\\\\.frame|format\\\\.Date|format\\\\.default|format\\\\.difftime|formatDL|format\\\\.factor|format\\\\.hexmode|format\\\\.info|format\\\\.libraryIQR|format\\\\.numeric_version|format\\\\.octmode|format\\\\.packageInfo|format\\\\.POSIXct|format\\\\.POSIXlt|format\\\\.pval|format\\\\.summaryDefault|forwardsolve|function|gamma|gc|gcinfo|gc\\\\.time|gctorture|gctorture2|get|get0|getAllConnections|getCallingDLL|getCallingDLLe|getConnection|getDLLRegisteredRoutines|getDLLRegisteredRoutines\\\\.character|getDLLRegisteredRoutines\\\\.DLLInfo|getElement|geterrmessage|getExportedValue|getHook|getLoadedDLLs|getNamespace|getNamespaceExports|getNamespaceImports|getNamespaceInfo|getNamespaceName|getNamespaceUsers|getNamespaceVersion|getNativeSymbolInfo|getOption|getRversion|getSrcLines|getTaskCallbackNames|gettext|gettextf|getwd|gl|globalCallingHandlers|globalenv|gregexec|gregexpr|grep|grepl|grepRaw|grouping|gsub|gzcon|gzfile|I|iconv|iconvlist|icuGetCollate|icuSetCollate|identical|identity|if|ifelse|Im|importIntoEnv|infoRDS|inherits|integer|interaction|interactive|intersect|intToBits|intToUtf8|inverse\\\\.rle|invisible|invokeRestart|invokeRestartInteractively|isa|is\\\\.array|is\\\\.atomic|isatty|isBaseNamespace|is\\\\.call|is\\\\.character|is\\\\.complex|is\\\\.data\\\\.frame|isdebugged|is\\\\.double|is\\\\.element|is\\\\.environment|is\\\\.expression|is\\\\.factor|isFALSE|is\\\\.finite|is\\\\.function|isIncomplete|is\\\\.infinite|is\\\\.integer|is\\\\.language|is\\\\.list|is\\\\.loaded|is\\\\.logical|is\\\\.matrix|is\\\\.na|is\\\\.na\\\\.data\\\\.frame|is\\\\.name|isNamespace|isNamespaceLoaded|is\\\\.nan|is\\\\.na\\\\.numeric_version|is\\\\.na\\\\.POSIXlt|is\\\\.null|is\\\\.numeric|is\\\\.numeric\\\\.Date|is\\\\.numeric\\\\.difftime|is\\\\.numeric\\\\.POSIXt|is\\\\.numeric_version|is\\\\.object|ISOdate|ISOdatetime|isOpen|is\\\\.ordered|is\\\\.package_version|is\\\\.pairlist|is\\\\.primitive|is\\\\.qr|is\\\\.R|is\\\\.raw|is\\\\.recursive|isRestart|isS4|isSeekable|is\\\\.single|is\\\\.symbol|isSymmetric|isSymmetric\\\\.matrix|is\\\\.table|isTRUE|is\\\\.unsorted|is\\\\.vector|jitter|julian|julian\\\\.Date|julian\\\\.POSIXt|kappa|kappa\\\\.default|kappa\\\\.lm|kappa\\\\.qr|kronecker|l10n_info|labels|labels\\\\.default|La_library|lapply|La\\\\.svd|La_version|lazyLoad|lazyLoadDBexec|lazyLoadDBfetch|lbeta|lchoose|length|length\\\\.POSIXlt|lengths|levels|levels\\\\.default|lfactorial|lgamma|libcurlVersion|library|library\\\\.dynam|library\\\\.dynam\\\\.unload|licence|license|list|list2DF|list2env|list\\\\.dirs|list\\\\.files|load|loadedNamespaces|loadingNamespaceInfo|loadNamespace|local|lockBinding|lockEnvironment|log|log10|log1p|log2|logb|logical|lower\\\\.tri|ls|makeActiveBinding|make\\\\.names|make\\\\.unique|Map|mapply|marginSums|margin\\\\.table|match|match\\\\.arg|match\\\\.call|match\\\\.fun|Math\\\\.data\\\\.frame|Math\\\\.Date|Math\\\\.difftime|Math\\\\.factor|Math\\\\.POSIXt|mat\\\\.or\\\\.vec|matrix|max|max\\\\.col|mean|mean\\\\.Date|mean\\\\.default|mean\\\\.difftime|mean\\\\.POSIXct|mean\\\\.POSIXlt|memCompress|memDecompress|mem\\\\.maxNSize|mem\\\\.maxVSize|memory\\\\.profile|merge|merge\\\\.data\\\\.frame|merge\\\\.default|message|mget|min|missing|Mod|mode|months|months\\\\.Date|months\\\\.POSIXt|names|namespaceExport|namespaceImport|namespaceImportClasses|namespaceImportFrom|namespaceImportMethods|names\\\\.POSIXlt|nargs|nchar|ncol|NCOL|Negate|new\\\\.env|next|NextMethod|ngettext|nlevels|noquote|norm|normalizePath|nrow|NROW|nullfile|numeric|numeric_version|numToBits|numToInts|nzchar|objects|oldClass|OlsonNames|on\\\\.exit|open|open\\\\.connection|open\\\\.srcfile|open\\\\.srcfilealias|open\\\\.srcfilecopy|Ops\\\\.data\\\\.frame|Ops\\\\.Date|Ops\\\\.difftime|Ops\\\\.factor|Ops\\\\.numeric_version|Ops\\\\.ordered|Ops\\\\.POSIXt|options|order|ordered|outer|packageEvent|packageHasNamespace|packageNotFoundError|packageStartupMessage|package_version|packBits|pairlist|parent\\\\.env|parent\\\\.frame|parse|parseNamespaceFile|paste|paste0|path\\\\.expand|path\\\\.package|pcre_config|pi|pipe|plot|pmatch|pmax|pmax\\\\.int|pmin|pmin\\\\.int|polyroot|Position|pos\\\\.to\\\\.env|pretty|pretty\\\\.default|prettyNum|print|print\\\\.AsIs|print\\\\.by|print\\\\.condition|print\\\\.connection|print\\\\.data\\\\.frame|print\\\\.Date|print\\\\.default|print\\\\.difftime|print\\\\.Dlist|print\\\\.DLLInfo|print\\\\.DLLInfoList|print\\\\.DLLRegisteredRoutines|print\\\\.eigen|print\\\\.factor|print\\\\.function|print\\\\.hexmode|print\\\\.libraryIQR|print\\\\.listof|print\\\\.NativeRoutineList|print\\\\.noquote|print\\\\.numeric_version|print\\\\.octmode|print\\\\.packageInfo|print\\\\.POSIXct|print\\\\.POSIXlt|print\\\\.proc_time|print\\\\.restart|print\\\\.rle|print\\\\.simple\\\\.list|print\\\\.srcfile|print\\\\.srcref|print\\\\.summaryDefault|print\\\\.summary\\\\.table|print\\\\.summary\\\\.warnings|print\\\\.table|print\\\\.warnings|prmatrix|proc\\\\.time|prod|proportions|prop\\\\.table|provideDimnames|psigamma|pushBack|pushBackLength|q|qr|qr\\\\.coef|qr\\\\.default|qr\\\\.fitted|qr\\\\.Q|qr\\\\.qty|qr\\\\.qy|qr\\\\.R|qr\\\\.resid|qr\\\\.solve|qr\\\\.X|quarters|quarters\\\\.Date|quarters\\\\.POSIXt|quit|quote|range|range\\\\.default|rank|rapply|raw|rawConnection|rawConnectionValue|rawShift|rawToBits|rawToChar|rbind|rbind\\\\.data\\\\.frame|rcond|Re|readBin|readChar|read\\\\.dcf|readline|readLines|readRDS|readRenviron|Recall|Reduce|regexec|regexpr|reg\\\\.finalizer|registerS3method|registerS3methods|regmatches|remove|removeTaskCallback|rep|rep\\\\.Date|rep\\\\.difftime|repeat|rep\\\\.factor|rep\\\\.int|replace|rep_len|replicate|rep\\\\.numeric_version|rep\\\\.POSIXct|rep\\\\.POSIXlt|require|requireNamespace|restartDescription|restartFormals|retracemem|return|returnValue|rev|rev\\\\.default|R\\\\.home|rle|rm|RNGkind|RNGversion|round|round\\\\.Date|round\\\\.POSIXt|row|rowMeans|rownames|row\\\\.names|row\\\\.names\\\\.data\\\\.frame|row\\\\.names\\\\.default|rowsum|rowsum\\\\.data\\\\.frame|rowsum\\\\.default|rowSums|R_system_version|R\\\\.version|R\\\\.Version|R\\\\.version\\\\.string|sample|sample\\\\.int|sapply|save|save\\\\.image|saveRDS|scale|scale\\\\.default|scan|search|searchpaths|seek|seek\\\\.connection|seq|seq_along|seq\\\\.Date|seq\\\\.default|seq\\\\.int|seq_len|seq\\\\.POSIXt|sequence|sequence\\\\.default|serialize|serverSocket|setdiff|setequal|setHook|setNamespaceInfo|set\\\\.seed|setSessionTimeLimit|setTimeLimit|setwd|showConnections|shQuote|sign|signalCondition|signif|simpleCondition|simpleError|simpleMessage|simpleWarning|simplify2array|sin|single|sinh|sink|sink\\\\.number|sinpi|slice\\\\.index|socketAccept|socketConnection|socketSelect|socketTimeout|solve|solve\\\\.default|solve\\\\.qr|sort|sort\\\\.default|sort\\\\.int|sort\\\\.list|sort\\\\.POSIXlt|source|split|split\\\\.data\\\\.frame|split\\\\.Date|split\\\\.default|split\\\\.POSIXct|sprintf|sqrt|sQuote|srcfile|srcfilealias|srcfilecopy|srcref|standardGeneric|startsWith|stderr|stdin|stdout|stop|stopifnot|storage\\\\.mode|str2expression|str2lang|strftime|strptime|strrep|strsplit|strtoi|strtrim|structure|strwrap|sub|subset|subset\\\\.data\\\\.frame|subset\\\\.default|subset\\\\.matrix|substitute|substr|substring|sum|summary|summary\\\\.connection|summary\\\\.data\\\\.frame|Summary\\\\.data\\\\.frame|summary\\\\.Date|Summary\\\\.Date|summary\\\\.default|Summary\\\\.difftime|summary\\\\.factor|Summary\\\\.factor|summary\\\\.matrix|Summary\\\\.numeric_version|Summary\\\\.ordered|summary\\\\.POSIXct|Summary\\\\.POSIXct|summary\\\\.POSIXlt|Summary\\\\.POSIXlt|summary\\\\.proc_time|summary\\\\.srcfile|summary\\\\.srcref|summary\\\\.table|summary\\\\.warnings|suppressMessages|suppressPackageStartupMessages|suppressWarnings|suspendInterrupts|svd|sweep|switch|sys\\\\.call|sys\\\\.calls|Sys\\\\.chmod|Sys\\\\.Date|sys\\\\.frame|sys\\\\.frames|sys\\\\.function|Sys\\\\.getenv|Sys\\\\.getlocale|Sys\\\\.getpid|Sys\\\\.glob|Sys\\\\.info|sys\\\\.load\\\\.image|Sys\\\\.localeconv|sys\\\\.nframe|sys\\\\.on\\\\.exit|sys\\\\.parent|sys\\\\.parents|Sys\\\\.readlink|sys\\\\.save\\\\.image|Sys\\\\.setenv|Sys\\\\.setFileTime|Sys\\\\.setlocale|Sys\\\\.sleep|sys\\\\.source|sys\\\\.status|system|system2|system\\\\.file|system\\\\.time|Sys\\\\.time|Sys\\\\.timezone|Sys\\\\.umask|Sys\\\\.unsetenv|Sys\\\\.which|t|table|tabulate|tan|tanh|tanpi|tapply|taskCallbackManager|tcrossprod|t\\\\.data\\\\.frame|t\\\\.default|tempdir|tempfile|textConnection|textConnectionValue|tolower|topenv|toString|toString\\\\.default|toupper|trace|traceback|tracemem|tracingState|transform|transform\\\\.data\\\\.frame|transform\\\\.default|trigamma|trimws|trunc|truncate|truncate\\\\.connection|trunc\\\\.Date|trunc\\\\.POSIXt|try|tryCatch|tryInvokeRestart|typeof|unclass|undebug|union|unique|unique\\\\.array|unique\\\\.data\\\\.frame|unique\\\\.default|unique\\\\.matrix|unique\\\\.numeric_version|unique\\\\.POSIXlt|unique\\\\.warnings|units|units\\\\.difftime|unix\\\\.time|unlink|unlist|unloadNamespace|unlockBinding|unname|unserialize|unsplit|untrace|untracemem|unz|upper\\\\.tri|url|UseMethod|utf8ToInt|validEnc|validUTF8|vapply|vector|Vectorize|version|warning|warningCondition|warnings|weekdays|weekdays\\\\.Date|weekdays\\\\.POSIXt|which|which\\\\.max|which\\\\.min|while|with|withAutoprint|withCallingHandlers|with\\\\.default|within|within\\\\.data\\\\.frame|within\\\\.list|withRestarts|withVisible|write|writeBin|writeChar|write\\\\.dcf|writeLines|xor|xpdrows\\\\.data\\\\.frame|xtfrm|xtfrm\\\\.AsIs|xtfrm\\\\.data\\\\.frame|xtfrm\\\\.Date|xtfrm\\\\.default|xtfrm\\\\.difftime|xtfrm\\\\.factor|xtfrm\\\\.numeric_version|xtfrm\\\\.POSIXct|xtfrm\\\\.POSIXlt|xzfile|zapsmall)\\\\s*(\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(abline|arrows|assocplot|axis|Axis|axis\\\\.Date|axis\\\\.POSIXct|axTicks|barplot|barplot\\\\.default|box|boxplot|boxplot\\\\.default|boxplot\\\\.matrix|bxp|cdplot|clip|close\\\\.screen|co\\\\.intervals|contour|contour\\\\.default|coplot|curve|dotchart|erase\\\\.screen|filled\\\\.contour|fourfoldplot|frame|grconvertX|grconvertY|grid|hist|hist\\\\.default|identify|image|image\\\\.default|layout|layout\\\\.show|lcm|legend|lines|lines\\\\.default|locator|matlines|matplot|matpoints|mosaicplot|mtext|pairs|pairs\\\\.default|panel\\\\.smooth|par|persp|pie|plot|plot\\\\.default|plot\\\\.design|plot\\\\.function|plot\\\\.new|plot\\\\.window|plot\\\\.xy|points|points\\\\.default|polygon|polypath|rasterImage|rect|rug|screen|segments|smoothScatter|spineplot|split\\\\.screen|stars|stem|strheight|stripchart|strwidth|sunflowerplot|symbols|text|text\\\\.default|title|xinch|xspline|xyinch|yinch)\\\\s*(\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(adjustcolor|as\\\\.graphicsAnnot|as\\\\.raster|axisTicks|bitmap|blues9|bmp|boxplot\\\\.stats|cairo_pdf|cairo_ps|cairoSymbolFont|check\\\\.options|chull|CIDFont|cm|cm\\\\.colors|col2rgb|colorConverter|colorRamp|colorRampPalette|colors|colorspaces|colours|contourLines|convertColor|densCols|dev2bitmap|devAskNewPage|dev\\\\.capabilities|dev\\\\.capture|dev\\\\.control|dev\\\\.copy|dev\\\\.copy2eps|dev\\\\.copy2pdf|dev\\\\.cur|dev\\\\.flush|dev\\\\.hold|deviceIsInteractive|dev\\\\.interactive|dev\\\\.list|dev\\\\.new|dev\\\\.next|dev\\\\.off|dev\\\\.prev|dev\\\\.print|dev\\\\.set|dev\\\\.size|embedFonts|extendrange|getGraphicsEvent|getGraphicsEventEnv|graphics\\\\.off|gray|gray\\\\.colors|grey|grey\\\\.colors|grSoftVersion|hcl|hcl\\\\.colors|hcl\\\\.pals|heat\\\\.colors|Hershey|hsv|is\\\\.raster|jpeg|make\\\\.rgb|n2mfrow|nclass\\\\.FD|nclass\\\\.scott|nclass\\\\.Sturges|palette|palette\\\\.colors|palette\\\\.pals|pdf|pdfFonts|pdf\\\\.options|pictex|png|postscript|postscriptFonts|ps\\\\.options|quartz|quartzFont|quartzFonts|quartz\\\\.options|quartz\\\\.save|rainbow|recordGraphics|recordPlot|replayPlot|rgb|rgb2hsv|savePlot|setEPS|setGraphicsEventEnv|setGraphicsEventHandlers|setPS|svg|terrain\\\\.colors|tiff|topo\\\\.colors|trans3d|Type1Font|x11|X11|X11Font|X11Fonts|X11\\\\.options|xfig|xy\\\\.coords|xyTable|xyz\\\\.coords)\\\\s*(\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(addNextMethod|allNames|Arith|as|asMethodDefinition|assignClassDef|assignMethodsMetaData|balanceMethodsList|cacheGenericsMetaData|cacheMetaData|cacheMethod|callGeneric|callNextMethod|canCoerce|cbind2|checkAtAssignment|checkSlotAssignment|classesToAM|classLabel|classMetaName|className|coerce|Compare|completeClassDefinition|completeExtends|completeSubclasses|Complex|conformMethod|defaultDumpName|defaultPrototype|doPrimitiveMethod|dumpMethod|dumpMethods|el|elNamed|empty\\\\.dump|emptyMethodsList|evalOnLoad|evalqOnLoad|evalSource|existsFunction|existsMethod|extends|externalRefMethod|finalDefaultMethod|findClass|findFunction|findMethod|findMethods|findMethodSignatures|findUnique|fixPre1\\\\.8|formalArgs|functionBody|generic\\\\.skeleton|getAllSuperClasses|getClass|getClassDef|getClasses|getDataPart|getFunction|getGeneric|getGenerics|getGroup|getGroupMembers|getLoadActions|getMethod|getMethods|getMethodsForDispatch|getMethodsMetaData|getPackageName|getRefClass|getSlots|getValidity|hasArg|hasLoadAction|hasMethod|hasMethods|implicitGeneric|inheritedSlotNames|initFieldArgs|initialize|initRefFields|insertClassMethods|insertMethod|insertSource|is|isClass|isClassDef|isClassUnion|isGeneric|isGrammarSymbol|isGroup|isRematched|isSealedClass|isSealedMethod|isVirtualClass|isXS3Class|kronecker|languageEl|linearizeMlist|listFromMethods|listFromMlist|loadMethod|Logic|makeClassRepresentation|makeExtends|makeGeneric|makeMethodsList|makePrototypeFromClassDef|makeStandardGeneric|matchSignature|Math|Math2|mergeMethods|metaNameUndo|MethodAddCoerce|methodSignatureMatrix|method\\\\.skeleton|MethodsList|MethodsListSelect|methodsPackageMetaName|missingArg|multipleClasses|new|newBasic|newClassRepresentation|newEmptyObject|Ops|packageSlot|possibleExtends|prohibitGeneric|promptClass|promptMethods|prototype|Quote|rbind2|reconcilePropertiesAndPrototype|registerImplicitGenerics|rematchDefinition|removeClass|removeGeneric|removeMethod|removeMethods|representation|requireMethods|resetClass|resetGeneric|S3Class|S3Part|sealClass|selectMethod|selectSuperClasses|setAs|setClass|setClassUnion|setDataPart|setGeneric|setGenericImplicit|setGroupGeneric|setIs|setLoadAction|setLoadActions|setMethod|setOldClass|setPackageName|setPrimitiveMethods|setRefClass|setReplaceMethod|setValidity|show|showClass|showDefault|showExtends|showMethods|showMlist|signature|SignatureMethod|sigToEnv|slot|slotNames|slotsFromS3|substituteDirect|substituteFunctionArgs|Summary|superClassDepth|testInheritedMethods|testVirtual|tryNew|unRematchDefinition|validObject|validSlotNames)\\\\s*(\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(acf|acf2AR|add1|addmargins|add\\\\.scope|aggregate|aggregate\\\\.data\\\\.frame|aggregate\\\\.ts|AIC|alias|anova|ansari\\\\.test|aov|approx|approxfun|ar|ar\\\\.burg|arima|arima0|arima0\\\\.diag|arima\\\\.sim|ARMAacf|ARMAtoMA|ar\\\\.mle|ar\\\\.ols|ar\\\\.yw|as\\\\.dendrogram|as\\\\.dist|as\\\\.formula|as\\\\.hclust|asOneSidedFormula|as\\\\.stepfun|as\\\\.ts|ave|bandwidth\\\\.kernel|bartlett\\\\.test|BIC|binomial|binom\\\\.test|biplot|Box\\\\.test|bw\\\\.bcv|bw\\\\.nrd|bw\\\\.nrd0|bw\\\\.SJ|bw\\\\.ucv|C|cancor|case\\\\.names|ccf|chisq\\\\.test|cmdscale|coef|coefficients|complete\\\\.cases|confint|confint\\\\.default|confint\\\\.lm|constrOptim|contrasts|contr\\\\.helmert|contr\\\\.poly|contr\\\\.SAS|contr\\\\.sum|contr\\\\.treatment|convolve|cooks\\\\.distance|cophenetic|cor|cor\\\\.test|cov|cov2cor|covratio|cov\\\\.wt|cpgram|cutree|cycle|D|dbeta|dbinom|dcauchy|dchisq|decompose|delete\\\\.response|deltat|dendrapply|density|density\\\\.default|deriv|deriv3|deviance|dexp|df|DF2formula|dfbeta|dfbetas|dffits|df\\\\.kernel|df\\\\.residual|dgamma|dgeom|dhyper|diffinv|dist|dlnorm|dlogis|dmultinom|dnbinom|dnorm|dpois|drop1|drop\\\\.scope|drop\\\\.terms|dsignrank|dt|dummy\\\\.coef|dummy\\\\.coef\\\\.lm|dunif|dweibull|dwilcox|ecdf|eff\\\\.aovlist|effects|embed|end|estVar|expand\\\\.model\\\\.frame|extractAIC|factanal|factor\\\\.scope|family|fft|filter|fisher\\\\.test|fitted|fitted\\\\.values|fivenum|fligner\\\\.test|formula|frequency|friedman\\\\.test|ftable|Gamma|gaussian|get_all_vars|getCall|getInitial|glm|glm\\\\.control|glm\\\\.fit|hasTsp|hat|hatvalues|hclust|heatmap|HoltWinters|influence|influence\\\\.measures|integrate|interaction\\\\.plot|inverse\\\\.gaussian|IQR|is\\\\.empty\\\\.model|is\\\\.leaf|is\\\\.mts|isoreg|is\\\\.stepfun|is\\\\.ts|is\\\\.tskernel|KalmanForecast|KalmanLike|KalmanRun|KalmanSmooth|kernapply|kernel|kmeans|knots|kruskal\\\\.test|ksmooth|ks\\\\.test|lag|lag\\\\.plot|line|lm|lm\\\\.fit|lm\\\\.influence|lm\\\\.wfit|loadings|loess|loess\\\\.control|loess\\\\.smooth|logLik|loglin|lowess|ls\\\\.diag|lsfit|ls\\\\.print|mad|mahalanobis|makeARIMA|make\\\\.link|makepredictcall|manova|mantelhaen\\\\.test|mauchly\\\\.test|mcnemar\\\\.test|median|median\\\\.default|medpolish|model\\\\.extract|model\\\\.frame|model\\\\.frame\\\\.default|model\\\\.matrix|model\\\\.matrix\\\\.default|model\\\\.matrix\\\\.lm|model\\\\.offset|model\\\\.response|model\\\\.tables|model\\\\.weights|monthplot|mood\\\\.test|mvfft|na\\\\.action|na\\\\.contiguous|na\\\\.exclude|na\\\\.fail|na\\\\.omit|na\\\\.pass|napredict|naprint|naresid|nextn|nlm|nlminb|nls|nls\\\\.control|NLSstAsymptotic|NLSstClosestX|NLSstLfAsymptote|NLSstRtAsymptote|nobs|numericDeriv|offset|oneway\\\\.test|optim|optimHess|optimise|optimize|order\\\\.dendrogram|pacf|p\\\\.adjust|p\\\\.adjust\\\\.methods|Pair|pairwise\\\\.prop\\\\.test|pairwise\\\\.table|pairwise\\\\.t\\\\.test|pairwise\\\\.wilcox\\\\.test|pbeta|pbinom|pbirthday|pcauchy|pchisq|pexp|pf|pgamma|pgeom|phyper|plclust|plnorm|plogis|plot\\\\.ecdf|plot\\\\.spec\\\\.coherency|plot\\\\.spec\\\\.phase|plot\\\\.stepfun|plot\\\\.ts|pnbinom|pnorm|poisson|poisson\\\\.test|poly|polym|power|power\\\\.anova\\\\.test|power\\\\.prop\\\\.test|power\\\\.t\\\\.test|ppoints|ppois|ppr|PP\\\\.test|prcomp|predict|predict\\\\.glm|predict\\\\.lm|preplot|princomp|printCoefmat|profile|proj|promax|prop\\\\.test|prop\\\\.trend\\\\.test|psignrank|pt|ptukey|punif|pweibull|pwilcox|qbeta|qbinom|qbirthday|qcauchy|qchisq|qexp|qf|qgamma|qgeom|qhyper|qlnorm|qlogis|qnbinom|qnorm|qpois|qqline|qqnorm|qqplot|qsignrank|qt|qtukey|quade\\\\.test|quantile|quasi|quasibinomial|quasipoisson|qunif|qweibull|qwilcox|r2dtable|rbeta|rbinom|rcauchy|rchisq|read\\\\.ftable|rect\\\\.hclust|reformulate|relevel|reorder|replications|reshape|resid|residuals|residuals\\\\.glm|residuals\\\\.lm|rexp|rf|rgamma|rgeom|rhyper|rlnorm|rlogis|rmultinom|rnbinom|rnorm|rpois|rsignrank|rstandard|rstudent|rt|runif|runmed|rweibull|rwilcox|rWishart|scatter\\\\.smooth|screeplot|sd|se\\\\.contrast|selfStart|setNames|shapiro\\\\.test|sigma|simulate|smooth|smoothEnds|smooth\\\\.spline|sortedXyData|spec\\\\.ar|spec\\\\.pgram|spec\\\\.taper|spectrum|spline|splinefun|splinefunH|SSasymp|SSasympOff|SSasympOrig|SSbiexp|SSD|SSfol|SSfpl|SSgompertz|SSlogis|SSmicmen|SSweibull|start|stat\\\\.anova|step|stepfun|stl|StructTS|summary\\\\.aov|summary\\\\.glm|summary\\\\.lm|summary\\\\.manova|summary\\\\.stepfun|supsmu|symnum|termplot|terms|terms\\\\.formula|time|toeplitz|ts|tsdiag|ts\\\\.intersect|tsp|ts\\\\.plot|tsSmooth|ts\\\\.union|t\\\\.test|TukeyHSD|uniroot|update|update\\\\.default|update\\\\.formula|var|variable\\\\.names|varimax|var\\\\.test|vcov|weighted\\\\.mean|weighted\\\\.residuals|weights|wilcox\\\\.test|window|write\\\\.ftable|xtabs)\\\\s*(\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"support.function.r\" } }, \"match\": \"\\\\b(adist|alarm|apropos|aregexec|argsAnywhere|asDateBuilt|askYesNo|aspell|aspell_package_C_files|aspell_package_Rd_files|aspell_package_R_files|aspell_package_vignettes|aspell_write_personal_dictionary_file|as\\\\.person|as\\\\.personList|as\\\\.relistable|as\\\\.roman|assignInMyNamespace|assignInNamespace|available\\\\.packages|bibentry|browseEnv|browseURL|browseVignettes|bug\\\\.report|capture\\\\.output|changedFiles|charClass|checkCRAN|chooseBioCmirror|chooseCRANmirror|citation|cite|citeNatbib|citEntry|citFooter|citHeader|close\\\\.socket|combn|compareVersion|contrib\\\\.url|count\\\\.fields|create\\\\.post|data|dataentry|data\\\\.entry|de|debugcall|debugger|demo|de\\\\.ncols|de\\\\.restore|de\\\\.setup|download\\\\.file|download\\\\.packages|dump\\\\.frames|edit|emacs|example|file\\\\.edit|fileSnapshot|file_test|find|findLineNum|fix|fixInNamespace|flush\\\\.console|formatOL|formatUL|getAnywhere|getCRANmirrors|getFromNamespace|getParseData|getParseText|getS3method|getSrcDirectory|getSrcFilename|getSrcLocation|getSrcref|getTxtProgressBar|glob2rx|globalVariables|hasName|head|head\\\\.matrix|help|help\\\\.request|help\\\\.search|help\\\\.start|history|hsearch_db|hsearch_db_concepts|hsearch_db_keywords|installed\\\\.packages|install\\\\.packages|is\\\\.relistable|isS3method|isS3stdGeneric|limitedLabels|loadhistory|localeToCharset|lsf\\\\.str|ls\\\\.str|maintainer|make\\\\.packages\\\\.html|makeRweaveLatexCodeRunner|make\\\\.socket|memory\\\\.limit|memory\\\\.size|menu|methods|mirror2html|modifyList|new\\\\.packages|news|nsl|object\\\\.size|old\\\\.packages|osVersion|packageDate|packageDescription|packageName|package\\\\.skeleton|packageStatus|packageVersion|page|person|personList|pico|process\\\\.events|prompt|promptData|promptImport|promptPackage|rc\\\\.getOption|rc\\\\.options|rc\\\\.settings|rc\\\\.status|readCitationFile|read\\\\.csv|read\\\\.csv2|read\\\\.delim|read\\\\.delim2|read\\\\.DIF|read\\\\.fortran|read\\\\.fwf|read\\\\.socket|read\\\\.table|recover|relist|remove\\\\.packages|removeSource|Rprof|Rprofmem|RShowDoc|RSiteSearch|rtags|Rtangle|RtangleFinish|RtangleRuncode|RtangleSetup|RtangleWritedoc|RweaveChunkPrefix|RweaveEvalWithOpt|RweaveLatex|RweaveLatexFinish|RweaveLatexOptions|RweaveLatexSetup|RweaveLatexWritedoc|RweaveTryStop|savehistory|select\\\\.list|sessionInfo|setBreakpoint|setRepositories|setTxtProgressBar|stack|Stangle|str|strcapture|strOptions|summaryRprof|suppressForeignCheck|Sweave|SweaveHooks|SweaveSyntaxLatex|SweaveSyntaxNoweb|SweaveSyntConv|tail|tail\\\\.matrix|tar|timestamp|toBibtex|toLatex|txtProgressBar|type\\\\.convert|undebugcall|unstack|untar|unzip|update\\\\.packages|upgrade|URLdecode|URLencode|url\\\\.show|vi|View|vignette|warnErrList|write\\\\.csv|write\\\\.csv2|write\\\\.socket|write\\\\.table|xedit|xemacs|zip)\\\\s*(\\\\()\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"comment.line.pragma.r\" }, \"2\": { \"name\": \"entity.name.pragma.name.r\" } }, \"match\": \"^(#pragma[ \\\\t]+mark)[ \\\\t](.*)\", \"name\": \"comment.line.pragma-mark.r\" }, { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.r\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.r\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.r\" }] }] }, \"constants\": { \"patterns\": [{ \"match\": \"\\\\b(pi|letters|LETTERS|month\\\\.abb|month\\\\.name)\\\\b\", \"name\": \"support.constant.misc.r\" }, { \"match\": \"\\\\b(TRUE|FALSE|NULL|NA|NA_integer_|NA_real_|NA_complex_|NA_character_|Inf|NaN)\\\\b\", \"name\": \"constant.language.r\" }, { \"match\": \"\\\\b0(x|X)[0-9a-fA-F]+i\\\\b\", \"name\": \"constant.numeric.imaginary.hexadecimal.r\" }, { \"match\": \"\\\\b\\\\d+\\\\.?\\\\d*(?:(e|E)(\\\\+|-)?\\\\d+)?i\\\\b\", \"name\": \"constant.numeric.imaginary.decimal.r\" }, { \"match\": \"\\\\.\\\\d+(?:(e|E)(\\\\+|-)?\\\\d+)?i\\\\b\", \"name\": \"constant.numeric.imaginary.decimal.r\" }, { \"match\": \"\\\\b0(x|X)[0-9a-fA-F]+L\\\\b\", \"name\": \"constant.numeric.integer.hexadecimal.r\" }, { \"match\": \"\\\\b(?:\\\\d+\\\\.?\\\\d*)(?:(e|E)(\\\\+|-)?\\\\d+)?L\\\\b\", \"name\": \"constant.numeric.integer.decimal.r\" }, { \"match\": \"\\\\b0(x|X)[0-9a-fA-F]+\\\\b\", \"name\": \"constant.numeric.float.hexadecimal.r\" }, { \"match\": \"\\\\b\\\\d+\\\\.?\\\\d*(?:(e|E)(\\\\+|-)?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.float.decimal.r\" }, { \"match\": \"\\\\.\\\\d+(?:(e|E)(\\\\+|-)?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.float.decimal.r\" }] }, \"function-calls\": { \"begin\": \"(?:\\\\b|(?=\\\\.))((?:[a-zA-Z._][\\\\w.]*|`[^`]+`))\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.function.r\" }, \"2\": { \"name\": \"punctuation.section.parens.begin.r\" } }, \"contentName\": \"meta.function-call.arguments.r\", \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.parens.end.r\" } }, \"name\": \"meta.function-call.r\", \"patterns\": [{ \"include\": \"#function-parameters\" }] }, \"function-declarations\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.r\" }, \"2\": { \"name\": \"keyword.operator.assignment.r\" }, \"3\": { \"name\": \"keyword.control.r\" } }, \"match\": \"((?:`[^`\\\\\\\\]*(?:\\\\\\\\.[^`\\\\\\\\]*)*`)|(?:[A-Za-z.][0-9A-Za-z._]*))\\\\s*(<?<-|=(?!=))\\\\s*(function|\\\\\\\\)(?!\\\\w)\", \"name\": \"meta.function.r\", \"patterns\": [{ \"include\": \"#lambda-functions\" }] }] }, \"function-parameters\": { \"patterns\": [{ \"contentName\": \"meta.function-call.parameters.r\", \"name\": \"meta.function-call.r\" }, { \"match\": \"(?:[a-zA-Z._][\\\\w.]*|`[^`]+`)(?=\\\\s[^=])\", \"name\": \"variable.other.r\" }, { \"begin\": \"(?==)\", \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"source.r\" }] }, { \"match\": \",\", \"name\": \"punctuation.separator.parameters.r\" }, { \"include\": \"source.r\" }] }, \"general-variables\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.r\" }, \"2\": { \"name\": \"keyword.operator.assignment.r\" } }, \"match\": \"([A-Za-z.][0-9A-Za-z._]*)\\\\s*(=)(?=[^=])\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.r\" }, \"2\": { \"name\": \"keyword.operator.assignment.r\" } }, \"match\": \"(`[^`]+`)\\\\s*(=)(?=[^=])\" }, { \"match\": \"\\\\b([\\\\d_][0-9A-Za-z._]+)\\\\b\", \"name\": \"invalid.illegal.variable.other.r\" }, { \"match\": \"\\\\b([0-9A-Za-z_]+)(?=::)\", \"name\": \"entity.namespace.r\" }, { \"match\": \"\\\\b([0-9A-Za-z._]+)\\\\b\", \"name\": \"variable.other.r\" }, { \"match\": \"(`[^`]+`)\", \"name\": \"variable.other.r\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(break|next|repeat|else|in)\\\\b\", \"name\": \"keyword.control.r\" }, { \"match\": \"\\\\b(ifelse|if|for|return|switch|while|invisible)\\\\b(?=\\\\s*\\\\()\", \"name\": \"keyword.control.r\" }, { \"match\": \"(-|\\\\+|\\\\*|\\\\/|%\\\\/%|%%|%\\\\*%|%o%|%x%|\\\\^)\", \"name\": \"keyword.operator.arithmetic.r\" }, { \"match\": \"(:=|<-|<<-|->|->>)\", \"name\": \"keyword.operator.assignment.r\" }, { \"match\": \"(==|<=|>=|!=|<>|<|>|%in%)\", \"name\": \"keyword.operator.comparison.r\" }, { \"match\": \"(!|&{1,2}|[|]{1,2})\", \"name\": \"keyword.operator.logical.r\" }, { \"match\": \"(\\\\|>)\", \"name\": \"keyword.operator.pipe.r\" }, { \"match\": \"(%between%|%chin%|%like%|%\\\\+%|%\\\\+replace%|%:%|%do%|%dopar%|%>%|%<>%|%T>%|%\\\\$%)\", \"name\": \"keyword.operator.other.r\" }, { \"match\": \"(\\\\.\\\\.\\\\.|\\\\$|:|\\\\~|@)\", \"name\": \"keyword.other.r\" }] }, \"lambda-functions\": { \"patterns\": [{ \"begin\": \"\\\\b(function)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.r\" }, \"2\": { \"name\": \"punctuation.section.parens.begin.r\" } }, \"contentName\": \"meta.function.parameters.r\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.r\" } }, \"name\": \"meta.function.r\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(?:[a-zA-Z._][\\\\w.]*|`[^`]+`)\", \"name\": \"variable.other.r\" }, { \"begin\": \"(?==)\", \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"source.r\" }] }, { \"match\": \",\", \"name\": \"punctuation.separator.parameters.r\" }] }] }, \"roxygen\": { \"patterns\": [{ \"begin\": \"^\\\\s*(#')\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.r\" } }, \"end\": \"$\\\\n?\", \"name\": \"comment.line.roxygen.r\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.r\" }, \"2\": { \"name\": \"variable.parameter.r\" } }, \"match\": \"(@param)\\\\s*((?:[a-zA-Z._][\\\\w.]*|`[^`]+`))\" }, { \"match\": \"@[a-zA-Z0-9]+\", \"name\": \"keyword.other.r\" }] }] }, \"storage-type\": { \"patterns\": [{ \"match\": \"\\\\b(character|complex|double|expression|integer|list|logical|numeric|single|raw)\\\\b(?=\\\\s*\\\\()\", \"name\": \"storage.type.r\" }] }, \"strings\": { \"patterns\": [{ \"begin\": '[rR]\"(-*)\\\\[', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": '\\\\]\\\\1\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.double.raw.r\" }, { \"begin\": \"[rR]'(-*)\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": \"\\\\]\\\\1'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.single.raw.r\" }, { \"begin\": '[rR]\"(-*)\\\\{', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": '\\\\}\\\\1\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.double.raw.r\" }, { \"begin\": \"[rR]'(-*)\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": \"\\\\}\\\\1'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.single.raw.r\" }, { \"begin\": '[rR]\"(-*)\\\\(', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": '\\\\)\\\\1\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.double.raw.r\" }, { \"begin\": \"[rR]'(-*)\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.begin.r\" } }, \"end\": \"\\\\)\\\\1'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.raw.end.r\" } }, \"name\": \"string.quoted.single.raw.r\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.r\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.r\" } }, \"name\": \"string.quoted.double.r\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.r\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.r\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.r\" } }, \"name\": \"string.quoted.single.r\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.r\" }] }] } }, \"scopeName\": \"source.r\" });\nvar r = [\n  lang\n];\n\nexport { r as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAK,QAAQ;IAAK,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAyB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAqB;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,eAAe;oBAAgC,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAAw9b;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAAw1B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAAgvC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAA8/E;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAA60I;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,SAAS;gBAA6nF;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAAmC,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAA6B;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuD,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAqF,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAA6C,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAiD,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAAmC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA2D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,eAAe;YAAkC,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;oBAA+G,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,eAAe;oBAAmC,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAS,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAA2C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAA2B;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAmB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqC,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAA8C,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAqF,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAAkB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,eAAe;oBAA8B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAiC,QAAQ;wBAAmB;wBAAG;4BAAE,SAAS;4BAAS,OAAO;4BAAY,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAqC;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAS,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkB;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;wBAA8C;wBAAG;4BAAE,SAAS;4BAAiB,QAAQ;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkG,QAAQ;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAW;AAC3tlC,IAAI,IAAI;IACN;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/julia.mjs"], "sourcesContent": ["import cpp from './cpp.mjs';\nimport python from './python.mjs';\nimport javascript from './javascript.mjs';\nimport r from './r.mjs';\nimport sql from './sql.mjs';\nimport './cpp-macro.mjs';\nimport './regexp.mjs';\nimport './glsl.mjs';\nimport './c.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Julia\", \"name\": \"julia\", \"patterns\": [{ \"include\": \"#operator\" }, { \"include\": \"#array\" }, { \"include\": \"#string\" }, { \"include\": \"#parentheses\" }, { \"include\": \"#bracket\" }, { \"include\": \"#function_decl\" }, { \"include\": \"#function_call\" }, { \"include\": \"#for_block\" }, { \"include\": \"#keyword\" }, { \"include\": \"#number\" }, { \"include\": \"#comment\" }, { \"include\": \"#type_decl\" }, { \"include\": \"#symbol\" }, { \"include\": \"#punctuation\" }], \"repository\": { \"array\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"meta.bracket.julia\" } }, \"end\": \"(\\\\])((?:\\\\.)?'*)\", \"endCaptures\": { \"1\": { \"name\": \"meta.bracket.julia\" }, \"2\": { \"name\": \"keyword.operator.transpose.julia\" } }, \"name\": \"meta.array.julia\", \"patterns\": [{ \"match\": \"\\\\bbegin\\\\b\", \"name\": \"constant.numeric.julia\" }, { \"match\": \"\\\\bend\\\\b\", \"name\": \"constant.numeric.julia\" }, { \"include\": \"#self_no_for_block\" }] }] }, \"bracket\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"meta.bracket.julia\" } }, \"end\": \"(\\\\})((?:\\\\.)?'*)\", \"endCaptures\": { \"1\": { \"name\": \"meta.bracket.julia\" }, \"2\": { \"name\": \"keyword.operator.transpose.julia\" } }, \"patterns\": [{ \"include\": \"#self_no_for_block\" }] }] }, \"comment\": { \"patterns\": [{ \"include\": \"#comment_block\" }, { \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.julia\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.julia\", \"patterns\": [{ \"include\": \"#comment_tags\" }] }] }, \"comment_block\": { \"patterns\": [{ \"begin\": \"#=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.julia\" } }, \"end\": \"=#\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.julia\" } }, \"name\": \"comment.block.number-sign-equals.julia\", \"patterns\": [{ \"include\": \"#comment_tags\" }, { \"include\": \"#comment_block\" }] }] }, \"comment_tags\": { \"patterns\": [{ \"match\": \"\\\\bTODO\\\\b\", \"name\": \"keyword.other.comment-annotation.julia\" }, { \"match\": \"\\\\bFIXME\\\\b\", \"name\": \"keyword.other.comment-annotation.julia\" }, { \"match\": \"\\\\bCHANGED\\\\b\", \"name\": \"keyword.other.comment-annotation.julia\" }, { \"match\": \"\\\\bXXX\\\\b\", \"name\": \"keyword.other.comment-annotation.julia\" }] }, \"for_block\": { \"comment\": \"for blocks need to be special-cased to support tokenizing 'outer' properly\", \"patterns\": [{ \"begin\": \"\\\\b(for)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.julia\" } }, \"end\": \"(?<!,|\\\\s)(\\\\s*\\\\n)\", \"patterns\": [{ \"match\": \"\\\\bouter\\\\b\", \"name\": \"keyword.other.julia\" }, { \"include\": \"$self\" }] }] }, \"function_call\": { \"patterns\": [{ \"begin\": \"((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?\\\\.?(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.julia\" }, \"2\": { \"name\": \"support.type.julia\" }, \"3\": { \"name\": \"meta.bracket.julia\" } }, \"end\": \"\\\\)(('|(\\\\.'))*\\\\.?')?\", \"endCaptures\": { \"0\": { \"name\": \"meta.bracket.julia\" }, \"1\": { \"name\": \"keyword.operator.transposed-func.julia\" } }, \"patterns\": [{ \"include\": \"#self_no_for_block\" }] }] }, \"function_decl\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.julia\" }, \"2\": { \"name\": \"support.type.julia\" } }, \"comment\": \"first group is function name\\nSecond group is type parameters (e.g. {T<:Number, S})\\nThen open parens\\nThen a lookahead ensures that we are followed by:\\n  - anything (function arguments)\\n  - 0 or more spaces\\n  - Finally an equal sign\\nNegative lookahead ensures we don't have another equal sign (not `==`)\", \"match\": \"((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?(?=\\\\([^#]*\\\\)(::[^\\\\s]+)?(\\\\s*\\\\bwhere\\\\b\\\\s+.+?)?\\\\s*?=(?![=>]))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.julia\" }, \"2\": { \"name\": \"keyword.operator.dots.julia\" }, \"3\": { \"name\": \"entity.name.function.julia\" }, \"4\": { \"name\": \"support.type.julia\" } }, \"comment\": \"similar regex to previous, but with keyword not 1-line syntax\", \"match\": \"\\\\b(function|macro)(?:\\\\s+(?:(?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*(\\\\.))?((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?|\\\\s*)(?=\\\\()\" }] }, \"keyword\": { \"patterns\": [{ \"match\": \"\\\\b(?<![:_\\\\.])(?:function|mutable\\\\s+struct|struct|macro|quote|abstract\\\\s+type|primitive\\\\s+type|module|baremodule|where)\\\\b\", \"name\": \"keyword.other.julia\" }, { \"match\": \"\\\\b(?<![:_])(?:if|else|elseif|for|while|begin|let|do|try|catch|finally|return|break|continue)\\\\b\", \"name\": \"keyword.control.julia\" }, { \"match\": \"\\\\b(?<![:_])end\\\\b\", \"name\": \"keyword.control.end.julia\" }, { \"match\": \"\\\\b(?<![:_])(?:global|local|const)\\\\b\", \"name\": \"keyword.storage.modifier.julia\" }, { \"match\": \"\\\\b(?<![:_])(?:export)\\\\b\", \"name\": \"keyword.control.export.julia\" }, { \"match\": \"^(?:public)\\\\b\", \"name\": \"keyword.control.public.julia\" }, { \"match\": \"\\\\b(?<![:_])(?:import)\\\\b\", \"name\": \"keyword.control.import.julia\" }, { \"match\": \"\\\\b(?<![:_])(?:using)\\\\b\", \"name\": \"keyword.control.using.julia\" }, { \"match\": \"(?<=\\\\S\\\\s+)\\\\b(as)\\\\b(?=\\\\s+\\\\S)\", \"name\": \"keyword.control.as.julia\" }, { \"match\": \"(@(\\\\.|(?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*))\", \"name\": \"support.function.macro.julia\" }] }, \"number\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.julia\" }, \"2\": { \"name\": \"keyword.operator.conjugate-number.julia\" } }, \"match\": \"((?<!(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF]))(?:(?:\\\\b0(?:x|X)[0-9a-fA-F](?:_?[0-9a-fA-F])*)|(?:\\\\b0o[0-7](?:_?[0-7])*)|(?:\\\\b0b[0-1](?:_?[0-1])*)|(?:(?:\\\\b\\\\d(?:_?\\\\d)*\\\\.?(?!\\\\.)(?:[_0-9]*))|(?:\\\\b\\\\.\\\\d(?:_?\\\\d)*))(?:[efE][+-]?\\\\d(?:_?\\\\d)*)?(?:im\\\\b|Inf(?:16|32|64)?\\\\b|NaN(?:16|32|64)?\\\\b|\\u03C0\\\\b|pi\\\\b|\\u212F\\\\b)?|\\\\b\\\\d+|\\\\bInf(?:16|32|64)?\\\\b|\\\\bNaN(?:16|32|64)?\\\\b|\\\\b\\u03C0\\\\b|\\\\bpi\\\\b|\\\\b\\u212F\\\\b))('*)\" }, { \"match\": \"\\\\bARGS\\\\b|\\\\bC_NULL\\\\b|\\\\bDEPOT_PATH\\\\b|\\\\bENDIAN_BOM\\\\b|\\\\bENV\\\\b|\\\\bLOAD_PATH\\\\b|\\\\bPROGRAM_FILE\\\\b|\\\\bstdin\\\\b|\\\\bstdout\\\\b|\\\\bstderr\\\\b|\\\\bVERSION\\\\b|\\\\bdevnull\\\\b\", \"name\": \"constant.global.julia\" }, { \"match\": \"\\\\btrue\\\\b|\\\\bfalse\\\\b|\\\\bnothing\\\\b|\\\\bmissing\\\\b\", \"name\": \"constant.language.julia\" }] }, \"operator\": { \"patterns\": [{ \"match\": \"\\\\.?(?:<-->|->|-->|<--|\\u2190|\\u2192|\\u2194|\\u219A|\\u219B|\\u219E|\\u21A0|\\u21A2|\\u21A3|\\u21A6|\\u21A4|\\u21AE|\\u21CE|\\u21CD|\\u21CF|\\u21D0|\\u21D2|\\u21D4|\\u21F4|\\u21F6|\\u21F7|\\u21F8|\\u21F9|\\u21FA|\\u21FB|\\u21FC|\\u21FD|\\u21FE|\\u21FF|\\u27F5|\\u27F6|\\u27F7|\\u27F9|\\u27FA|\\u27FB|\\u27FC|\\u27FD|\\u27FE|\\u27FF|\\u2900|\\u2901|\\u2902|\\u2903|\\u2904|\\u2905|\\u2906|\\u2907|\\u290C|\\u290D|\\u290E|\\u290F|\\u2910|\\u2911|\\u2914|\\u2915|\\u2916|\\u2917|\\u2918|\\u291D|\\u291E|\\u291F|\\u2920|\\u2944|\\u2945|\\u2946|\\u2947|\\u2948|\\u294A|\\u294B|\\u294E|\\u2950|\\u2952|\\u2953|\\u2956|\\u2957|\\u295A|\\u295B|\\u295E|\\u295F|\\u2962|\\u2964|\\u2966|\\u2967|\\u2968|\\u2969|\\u296A|\\u296B|\\u296C|\\u296D|\\u2970|\\u29F4|\\u2B31|\\u2B30|\\u2B32|\\u2B33|\\u2B34|\\u2B35|\\u2B36|\\u2B37|\\u2B38|\\u2B39|\\u2B3A|\\u2B3B|\\u2B3C|\\u2B3D|\\u2B3E|\\u2B3F|\\u2B40|\\u2B41|\\u2B42|\\u2B43|\\u2977|\\u2B44|\\u297A|\\u2B47|\\u2B48|\\u2B49|\\u2B4A|\\u2B4B|\\u2B4C|\\uFFE9|\\uFFEB|\\u21DC|\\u21DD|\\u219C|\\u219D|\\u21A9|\\u21AA|\\u21AB|\\u21AC|\\u21BC|\\u21BD|\\u21C0|\\u21C1|\\u21C4|\\u21C6|\\u21C7|\\u21C9|\\u21CB|\\u21CC|\\u21DA|\\u21DB|\\u21E0|\\u21E2|\\u21B7|\\u21B6|\\u21BA|\\u21BB|=>)\", \"name\": \"keyword.operator.arrow.julia\" }, { \"match\": \"(?::=|\\\\+=|-=|\\\\*=|//=|/=|\\\\.//=|\\\\./=|\\\\.\\\\*=|\\\\\\\\=|\\\\.\\\\\\\\=|\\\\^=|\\\\.\\\\^=|%=|\\\\.%=|\\xF7=|\\\\.\\xF7=|\\\\|=|&=|\\\\.&=|\\u22BB=|\\\\.\\u22BB=|\\\\$=|<<=|>>=|>>>=|=(?!=))\", \"name\": \"keyword.operator.update.julia\" }, { \"match\": \"(?:<<|>>>|>>|\\\\.>>>|\\\\.>>|\\\\.<<)\", \"name\": \"keyword.operator.shift.julia\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.relation.types.julia\" }, \"2\": { \"name\": \"support.type.julia\" }, \"3\": { \"name\": \"keyword.operator.transpose.julia\" } }, \"match\": `(?:\\\\s*(::|>:|<:)\\\\s*((?:(?:Union)?\\\\([^)]*\\\\)|[A-Za-z_$\\u2207][\\\\w\\u207A-\\u209C!\\u2032\\\\.]*(?:(?:{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})|(?:\".+?(?<!\\\\\\\\)\"))?)))(?:\\\\.\\\\.\\\\.)?((?:\\\\.)?'*)` }, { \"match\": \"(\\\\.?((?<!<)<=|(?<!>)>=|>|<|\\u2265|\\u2264|===|==|\\u2261|!=|\\u2260|!==|\\u2262|\\u2208|\\u2209|\\u220B|\\u220C|\\u2286|\\u2288|\\u2282|\\u2284|\\u228A|\\u221D|\\u220A|\\u220D|\\u2225|\\u2226|\\u2237|\\u223A|\\u223B|\\u223D|\\u223E|\\u2241|\\u2243|\\u2242|\\u2244|\\u2245|\\u2246|\\u2247|\\u2248|\\u2249|\\u224A|\\u224B|\\u224C|\\u224D|\\u224E|\\u2250|\\u2251|\\u2252|\\u2253|\\u2256|\\u2257|\\u2258|\\u2259|\\u225A|\\u225B|\\u225C|\\u225D|\\u225E|\\u225F|\\u2263|\\u2266|\\u2267|\\u2268|\\u2269|\\u226A|\\u226B|\\u226C|\\u226D|\\u226E|\\u226F|\\u2270|\\u2271|\\u2272|\\u2273|\\u2274|\\u2275|\\u2276|\\u2277|\\u2278|\\u2279|\\u227A|\\u227B|\\u227C|\\u227D|\\u227E|\\u227F|\\u2280|\\u2281|\\u2283|\\u2285|\\u2287|\\u2289|\\u228B|\\u228F|\\u2290|\\u2291|\\u2292|\\u229C|\\u22A9|\\u22AC|\\u22AE|\\u22B0|\\u22B1|\\u22B2|\\u22B3|\\u22B4|\\u22B5|\\u22B6|\\u22B7|\\u22CD|\\u22D0|\\u22D1|\\u22D5|\\u22D6|\\u22D7|\\u22D8|\\u22D9|\\u22DA|\\u22DB|\\u22DC|\\u22DD|\\u22DE|\\u22DF|\\u22E0|\\u22E1|\\u22E2|\\u22E3|\\u22E4|\\u22E5|\\u22E6|\\u22E7|\\u22E8|\\u22E9|\\u22EA|\\u22EB|\\u22EC|\\u22ED|\\u22F2|\\u22F3|\\u22F4|\\u22F5|\\u22F6|\\u22F7|\\u22F8|\\u22F9|\\u22FA|\\u22FB|\\u22FC|\\u22FD|\\u22FE|\\u22FF|\\u27C8|\\u27C9|\\u27D2|\\u29B7|\\u29C0|\\u29C1|\\u29E1|\\u29E3|\\u29E4|\\u29E5|\\u2A66|\\u2A67|\\u2A6A|\\u2A6B|\\u2A6C|\\u2A6D|\\u2A6E|\\u2A6F|\\u2A70|\\u2A71|\\u2A72|\\u2A73|\\u2A75|\\u2A76|\\u2A77|\\u2A78|\\u2A79|\\u2A7A|\\u2A7B|\\u2A7C|\\u2A7D|\\u2A7E|\\u2A7F|\\u2A80|\\u2A81|\\u2A82|\\u2A83|\\u2A84|\\u2A85|\\u2A86|\\u2A87|\\u2A88|\\u2A89|\\u2A8A|\\u2A8B|\\u2A8C|\\u2A8D|\\u2A8E|\\u2A8F|\\u2A90|\\u2A91|\\u2A92|\\u2A93|\\u2A94|\\u2A95|\\u2A96|\\u2A97|\\u2A98|\\u2A99|\\u2A9A|\\u2A9B|\\u2A9C|\\u2A9D|\\u2A9E|\\u2A9F|\\u2AA0|\\u2AA1|\\u2AA2|\\u2AA3|\\u2AA4|\\u2AA5|\\u2AA6|\\u2AA7|\\u2AA8|\\u2AA9|\\u2AAA|\\u2AAB|\\u2AAC|\\u2AAD|\\u2AAE|\\u2AAF|\\u2AB0|\\u2AB1|\\u2AB2|\\u2AB3|\\u2AB4|\\u2AB5|\\u2AB6|\\u2AB7|\\u2AB8|\\u2AB9|\\u2ABA|\\u2ABB|\\u2ABC|\\u2ABD|\\u2ABE|\\u2ABF|\\u2AC0|\\u2AC1|\\u2AC2|\\u2AC3|\\u2AC4|\\u2AC5|\\u2AC6|\\u2AC7|\\u2AC8|\\u2AC9|\\u2ACA|\\u2ACB|\\u2ACC|\\u2ACD|\\u2ACE|\\u2ACF|\\u2AD0|\\u2AD1|\\u2AD2|\\u2AD3|\\u2AD4|\\u2AD5|\\u2AD6|\\u2AD7|\\u2AD8|\\u2AD9|\\u2AF7|\\u2AF8|\\u2AF9|\\u2AFA|\\u22A2|\\u22A3|\\u27C2|\\u2AEA|\\u2AEB|<:|>:))\", \"name\": \"keyword.operator.relation.julia\" }, { \"match\": \"(?<=\\\\s)(?:\\\\?)(?=\\\\s)\", \"name\": \"keyword.operator.ternary.julia\" }, { \"match\": \"(?<=\\\\s)(?::)(?=\\\\s)\", \"name\": \"keyword.operator.ternary.julia\" }, { \"match\": \"(?:\\\\|\\\\||&&|(?<!(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF]))!)\", \"name\": \"keyword.operator.boolean.julia\" }, { \"match\": \"(?<=[\\\\w\\u207A-\\u209C!\\u2032\\u2207)\\\\]}])(?::)\", \"name\": \"keyword.operator.range.julia\" }, { \"match\": \"(?:\\\\|>)\", \"name\": \"keyword.operator.applies.julia\" }, { \"match\": \"(?:\\\\||\\\\.\\\\||\\\\&|\\\\.\\\\&|~|\\xAC|\\\\.~|\\u22BB|\\\\.\\u22BB)\", \"name\": \"keyword.operator.bitwise.julia\" }, { \"match\": \"\\\\.?(?:\\\\+\\\\+|--|\\\\+|-|\\u2212|\\xA6|\\\\||\\u2295|\\u2296|\\u229E|\\u229F|\\u222A|\\u2228|\\u2294|\\xB1|\\u2213|\\u2214|\\u2238|\\u224F|\\u228E|\\u22BB|\\u22BD|\\u22CE|\\u22D3|\\u27C7|\\u29FA|\\u29FB|\\u2A08|\\u2A22|\\u2A23|\\u2A24|\\u2A25|\\u2A26|\\u2A27|\\u2A28|\\u2A29|\\u2A2A|\\u2A2B|\\u2A2C|\\u2A2D|\\u2A2E|\\u2A39|\\u2A3A|\\u2A41|\\u2A42|\\u2A45|\\u2A4A|\\u2A4C|\\u2A4F|\\u2A50|\\u2A52|\\u2A54|\\u2A56|\\u2A57|\\u2A5B|\\u2A5D|\\u2A61|\\u2A62|\\u2A63|\\\\*|//?|\\u233F|\\xF7|%|&|\\xB7|\\u0387|\\u22C5|\\u2218|\\xD7|\\\\\\\\|\\u2229|\\u2227|\\u2297|\\u2298|\\u2299|\\u229A|\\u229B|\\u22A0|\\u22A1|\\u2293|\\u2217|\\u2219|\\u2224|\\u214B|\\u2240|\\u22BC|\\u22C4|\\u22C6|\\u22C7|\\u22C9|\\u22CA|\\u22CB|\\u22CC|\\u22CF|\\u22D2|\\u27D1|\\u29B8|\\u29BC|\\u29BE|\\u29BF|\\u29F6|\\u29F7|\\u2A07|\\u2A30|\\u2A31|\\u2A32|\\u2A33|\\u2A34|\\u2A35|\\u2A36|\\u2A37|\\u2A38|\\u2A3B|\\u2A3C|\\u2A3D|\\u2A40|\\u2A43|\\u2A44|\\u2A4B|\\u2A4D|\\u2A4E|\\u2A51|\\u2A53|\\u2A55|\\u2A58|\\u2A5A|\\u2A5C|\\u2A5E|\\u2A5F|\\u2A60|\\u2ADB|\\u228D|\\u25B7|\\u2A1D|\\u27D5|\\u27D6|\\u27D7|\\u2A1F|\\\\^|\\u2191|\\u2193|\\u21F5|\\u27F0|\\u27F1|\\u2908|\\u2909|\\u290A|\\u290B|\\u2912|\\u2913|\\u2949|\\u294C|\\u294D|\\u294F|\\u2951|\\u2954|\\u2955|\\u2958|\\u2959|\\u295C|\\u295D|\\u2960|\\u2961|\\u2963|\\u2965|\\u296E|\\u296F|\\uFFEA|\\uFFEC|\\u221A|\\u221B|\\u221C|\\u22C6|\\xB1|\\u2213)\", \"name\": \"keyword.operator.arithmetic.julia\" }, { \"match\": \"(?:\\u2218)\", \"name\": \"keyword.operator.compose.julia\" }, { \"match\": \"(?:::|(?<=\\\\s)isa(?=\\\\s))\", \"name\": \"keyword.operator.isa.julia\" }, { \"match\": \"(?:(?<=\\\\s)in(?=\\\\s))\", \"name\": \"keyword.operator.relation.in.julia\" }, { \"match\": \"(?:\\\\.(?=(?:@|_|\\\\p{L}))|\\\\.\\\\.+|\\u2026|\\u205D|\\u22EE|\\u22F1|\\u22F0|\\u22EF)\", \"name\": \"keyword.operator.dots.julia\" }, { \"match\": \"(?:\\\\$)(?=.+)\", \"name\": \"keyword.operator.interpolation.julia\" }, { \"captures\": { \"2\": { \"name\": \"keyword.operator.transposed-variable.julia\" } }, \"match\": \"((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)(('|(\\\\.'))*\\\\.?')\" }, { \"captures\": { \"1\": { \"name\": \"bracket.end.julia\" }, \"2\": { \"name\": \"keyword.operator.transposed-matrix.julia\" } }, \"match\": \"(\\\\])((?:'|(?:\\\\.'))*\\\\.?')\" }, { \"captures\": { \"1\": { \"name\": \"bracket.end.julia\" }, \"2\": { \"name\": \"keyword.operator.transposed-parens.julia\" } }, \"match\": \"(\\\\))((?:'|(?:\\\\.'))*\\\\.?')\" }] }, \"parentheses\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.bracket.julia\" } }, \"end\": \"(\\\\))((?:\\\\.)?'*)\", \"endCaptures\": { \"1\": { \"name\": \"meta.bracket.julia\" }, \"2\": { \"name\": \"keyword.operator.transpose.julia\" } }, \"patterns\": [{ \"include\": \"#self_no_for_block\" }] }] }, \"punctuation\": { \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.comma.julia\" }, { \"match\": \";\", \"name\": \"punctuation.separator.semicolon.julia\" }] }, \"self_no_for_block\": { \"comment\": \"Same as $self, but does not contain #for_block. 'outer' is not valid in some contexts (e.g. generators, comprehensions, indexing), so use this when matching those in begin/end patterns. Keep this up-to-date with $self!\", \"patterns\": [{ \"include\": \"#operator\" }, { \"include\": \"#array\" }, { \"include\": \"#string\" }, { \"include\": \"#parentheses\" }, { \"include\": \"#bracket\" }, { \"include\": \"#function_decl\" }, { \"include\": \"#function_call\" }, { \"include\": \"#keyword\" }, { \"include\": \"#number\" }, { \"include\": \"#comment\" }, { \"include\": \"#type_decl\" }, { \"include\": \"#symbol\" }, { \"include\": \"#punctuation\" }] }, \"string\": { \"patterns\": [{ \"begin\": '(?:(@doc)\\\\s((?:doc)?\"\"\")|(doc\"\"\"))', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"end\": '(\"\"\") ?(->)?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" }, \"2\": { \"name\": \"keyword.operator.arrow.julia\" } }, \"name\": \"string.docstring.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '(i?cxx)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"contentName\": \"meta.embedded.inline.cpp\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"embed.cxx.julia\", \"patterns\": [{ \"include\": \"source.cpp#root_context\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '(py)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"contentName\": \"meta.embedded.inline.python\", \"end\": '([\\\\s\\\\w]*)(\"\"\")', \"endCaptures\": { \"2\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"embed.python.julia\", \"patterns\": [{ \"include\": \"source.python\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '(js)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"contentName\": \"meta.embedded.inline.javascript\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"embed.js.julia\", \"patterns\": [{ \"include\": \"source.js\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '(R)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"contentName\": \"meta.embedded.inline.r\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"embed.R.julia\", \"patterns\": [{ \"include\": \"source.r\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '(raw)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"string.quoted.other.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '(raw)(\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"string.quoted.other.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '(sql)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"contentName\": \"meta.embedded.inline.sql\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"embed.sql.julia\", \"patterns\": [{ \"include\": \"source.sql\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": 'var\"\"\"', \"end\": '\"\"\"', \"name\": \"constant.other.symbol.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": 'var\"', \"end\": '\"', \"name\": \"constant.other.symbol.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '^\\\\s?(doc)?(\"\"\")\\\\s?$', \"beginCaptures\": { \"1\": { \"name\": \"support.function.macro.julia\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"comment\": \"This only matches docstrings that start and end with triple quotes on\\ntheir own line in the void\", \"end\": '(\"\"\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"string.docstring.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"end\": \"'(?!')\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"string.quoted.single.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.multiline.begin.julia\" } }, \"comment\": \"multi-line string with triple double quotes\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.multiline.end.julia\" } }, \"name\": \"string.quoted.triple.double.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": '\"(?!\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" } }, \"comment\": \"String with single pair of double quotes. Regex matches isolated double quote\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.julia\" } }, \"name\": \"string.quoted.double.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": 'r\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.regexp.begin.julia\" } }, \"end\": '(\"\"\")([imsx]{0,4})?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.regexp.end.julia\" }, \"2\": { \"comment\": \"I took this scope name from python regex grammar\", \"name\": \"keyword.other.option-toggle.regexp.julia\" } }, \"name\": \"string.regexp.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": 'r\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.regexp.begin.julia\" } }, \"end\": '(\")([imsx]{0,4})?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.regexp.end.julia\" }, \"2\": { \"comment\": \"I took this scope name from python regex grammar\", \"name\": \"keyword.other.option-toggle.regexp.julia\" } }, \"name\": \"string.regexp.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '(?<!\")((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" }, \"1\": { \"name\": \"support.function.macro.julia\" } }, \"end\": '(\"\"\")((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" }, \"2\": { \"name\": \"support.function.macro.julia\" } }, \"name\": \"string.quoted.other.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": '(?<!\")((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" }, \"1\": { \"name\": \"support.function.macro.julia\" } }, \"end\": '(?<![^\\\\\\\\]\\\\\\\\)(\")((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" }, \"2\": { \"name\": \"support.function.macro.julia\" } }, \"name\": \"string.quoted.other.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }] }, { \"begin\": \"(?<!`)((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?```\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" }, \"1\": { \"name\": \"support.function.macro.julia\" } }, \"end\": \"(```)((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" }, \"2\": { \"name\": \"support.function.macro.julia\" } }, \"name\": \"string.interpolated.backtick.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }, { \"begin\": \"(?<!`)((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.julia\" }, \"1\": { \"name\": \"support.function.macro.julia\" } }, \"end\": \"(?<![^\\\\\\\\]\\\\\\\\)(`)((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.julia\" }, \"2\": { \"name\": \"support.function.macro.julia\" } }, \"name\": \"string.interpolated.backtick.julia\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_dollar_sign_interpolate\" }] }] }, \"string_dollar_sign_interpolate\": { \"patterns\": [{ \"match\": \"\\\\$(?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF]|[^\\\\p{^Sc}$])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF]|[^\\\\p{^Sc}$])*\", \"name\": \"variable.interpolation.julia\" }, { \"begin\": \"\\\\$(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"meta.bracket.julia\" } }, \"comment\": \"`punctuation.section.embedded`, `constant.escape`,\\n& `meta.embedded.line` were considered but appear to have even spottier\\nsupport among popular syntaxes.\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.bracket.julia\" } }, \"name\": \"variable.interpolation.julia\", \"patterns\": [{ \"include\": \"#self_no_for_block\" }] }] }, \"string_escaped_char\": { \"patterns\": [{ \"match\": \"\\\\\\\\(\\\\\\\\|[0-3]\\\\d{,2}|[4-7]\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8}|.)\", \"name\": \"constant.character.escape.julia\" }] }, \"symbol\": { \"patterns\": [{ \"comment\": \"This is string.quoted.symbol.julia in tpoisot's package\", \"match\": '(?<![\\\\w\\u207A-\\u209C!\\u2032\\u2207)\\\\]}]):(?:(?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)(?!(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF]))(?![\"`])', \"name\": \"constant.other.symbol.julia\" }] }, \"type_decl\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.type.julia\" }, \"2\": { \"name\": \"entity.other.inherited-class.julia\" }, \"3\": { \"name\": \"punctuation.separator.inheritance.julia\" } }, \"match\": \"(?>!:_)(?:struct|mutable\\\\s+struct|abstract\\\\s+type|primitive\\\\s+type)\\\\s+((?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*)(\\\\s*(<:)\\\\s*(?:[A-Za-z_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{So}\\u2190-\\u21FF])(?:[\\\\w_!\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Sc}\\u2140-\\u2144\\u223F\\u22BE\\u22BF\\u22A4\\u22A5\\u2202\\u2205-\\u2207\\u220E\\u220F\\u2210\\u2211\\u221E\\u221F\\u222B-\\u2233\\u22C0-\\u22C3\\u25F8-\\u25FF\\u266F\\u27D8\\u27D9\\u27C0\\u27C1\\u29B0-\\u29B4\\u2A00-\\u2A06\\u2A09-\\u2A16\\u2A1B\\u2A1C\\u{1D6C1}\\u{1D6DB}\\u{1D6FB}\\u{1D715}\\u{1D735}\\u{1D74F}\\u{1D76F}\\u{1D789}\\u{1D7A9}\\u{1D7C3}\\u2071-\\u207E\\u2081-\\u208E\\u2220-\\u2222\\u299B-\\u29AF\\u2118\\u212E\\u309B-\\u309C\\u{1D7CE}-\\u{1D7E1}]|[^\\\\P{Mn}\u0001-\\xA1]|[^\\\\P{Mc}\u0001-\\xA1]|[^\\\\P{Nd}\u0001-\\xA1]|[^\\\\P{Pc}\u0001-\\xA1]|[^\\\\P{Sk}\u0001-\\xA1]|[^\\\\P{Me}\u0001-\\xA1]|[^\\\\P{No}\u0001-\\xA1]|[\\u2032-\\u2037\\u2057]|[^\\\\P{So}\\u2190-\\u21FF])*(?:{.*})?)?\", \"name\": \"meta.type.julia\" }] } }, \"scopeName\": \"source.julia\", \"embeddedLangs\": [\"cpp\", \"python\", \"javascript\", \"r\", \"sql\"], \"aliases\": [\"jl\"] });\nvar julia = [\n  ...cpp,\n  ...python,\n  ...javascript,\n  ...r,\n  ...sql,\n  lang\n];\n\nexport { julia as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAMA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAe;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAyB;wBAAG;4BAAE,SAAS;4BAAa,QAAQ;wBAAyB;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAyC;aAAE;QAAC;QAAG,aAAa;YAAE,WAAW;YAA8E,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAuB,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAsB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAopC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAA0B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,WAAW;oBAAwT,SAAS;gBAA4sC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,WAAW;oBAAiE,SAAS;gBAA2xE;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkI,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAA6mC,QAAQ;gBAA+B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAi/B;gBAAG;oBAAE,SAAS;oBAA4K,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAsD,QAAQ;gBAA0B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0iC,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAiK,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS,CAAC,uLAAuL,CAAC;gBAAC;gBAAG;oBAAE,SAAS;oBAAi8D,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA6oB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAkD,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA0D,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA2qC,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA+E,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAuC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,SAAS;gBAAunC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,SAAS;gBAA8B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,SAAS;gBAA8B;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;aAAE;QAAC;QAAG,qBAAqB;YAAE,WAAW;YAA8N,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAgB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA+B,OAAO;oBAAoB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAAmC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAkB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA0B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAO,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,OAAO;oBAAK,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,WAAW;oBAAqG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,WAAW;oBAA+C,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,WAAW;oBAAiF,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,OAAO;oBAAuB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,WAAW;4BAAoD,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,WAAW;4BAAoD,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+mC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA4mC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6mC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA0nC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgnC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA4mC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8mC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA0nC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kCAAkC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmnC,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,WAAW;oBAAgK,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0F,QAAQ;gBAAkC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAA2D,SAAS;oBAAmxD,QAAQ;gBAA8B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;oBAAwyE,QAAQ;gBAAkB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,iBAAiB;QAAC;QAAO;QAAU;QAAc;QAAK;KAAM;IAAE,WAAW;QAAC;KAAK;AAAC;AACzm3C,IAAI,QAAQ;OACP,iMAAA,CAAA,UAAG;OACH,oMAAA,CAAA,UAAM;OACN,wMAAA,CAAA,UAAU;OACV,+LAAA,CAAA,UAAC;OACD,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}