{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/cypher.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Cypher\", \"fileTypes\": [\"cql\", \"cyp\", \"cypher\"], \"name\": \"cypher\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#keywords\" }, { \"include\": \"#functions\" }, { \"include\": \"#path-patterns\" }, { \"include\": \"#operators\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#properties_literal\" }, { \"include\": \"#numbers\" }, { \"include\": \"#strings\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"match\": \"//.*$\\\\n?\", \"name\": \"comment.line.double-slash.cypher\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"(?i)\\\\bTRUE|FALSE\\\\b\", \"name\": \"constant.language.bool.cypher\" }, { \"match\": \"(?i)\\\\bNULL\\\\b\", \"name\": \"constant.language.missing.cypher\" }] }, \"functions\": { \"patterns\": [{ \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b((NOT)(?=\\\\s*\\\\()|IS\\\\s+NULL|IS\\\\s+NOT\\\\s+NULL)\", \"name\": \"keyword.control.function.boolean.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(ALL|ANY|NONE|SINGLE)(?=\\\\s*\\\\()\", \"name\": \"support.function.predicate.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(LENGTH|TYPE|ID|COALESCE|HEAD|LAST|TIMESTAMP|STARTNODE|ENDNODE|TOINT|TOFLOAT)(?=\\\\s*\\\\()\", \"name\": \"support.function.scalar.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(NODES|RELATIONSHIPS|LABELS|EXTRACT|FILTER|TAIL|RANGE|REDUCE)(?=\\\\s*\\\\()\", \"name\": \"support.function.collection.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(ABS|ACOS|ASIN|ATAN|ATAN2|COS|COT|DEGREES|E|EXP|FLOOR|HAVERSIN|LOG|LOG10|PI|RADIANS|RAND|ROUND|SIGN|SIN|SQRT|TAN)(?=\\\\s*\\\\()\", \"name\": \"support.function.math.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(COUNT|sum|avg|max|min|stdev|stdevp|percentileDisc|percentileCont|collect)(?=\\\\s*\\\\()\", \"name\": \"support.function.aggregation.cypher\" }, { \"comment\": \"List of Cypher built-in functions from http://docs.neo4j.org/chunked/milestone/query-function.html\", \"match\": \"(?i)\\\\b(STR|REPLACE|SUBSTRING|LEFT|RIGHT|LTRIM|RTRIM|TRIM|LOWER|UPPER|SPLIT)(?=\\\\s*\\\\()\", \"name\": \"support.function.string.cypher\" }] }, \"identifiers\": { \"patterns\": [{ \"match\": \"`.+?`\", \"name\": \"variable.other.quoted-identifier.cypher\" }, { \"match\": \"[\\\\p{L}_][\\\\p{L}0-9_]*\", \"name\": \"variable.other.identifier.cypher\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(?i)\\\\b(START|MATCH|WHERE|RETURN|UNION|FOREACH|WITH|AS|LIMIT|SKIP|UNWIND|HAS|DISTINCT|OPTIONAL\\\\\\\\s+MATCH|ORDER\\\\s+BY|CALL|YIELD)\\\\b\", \"name\": \"keyword.control.clause.cypher\" }, { \"match\": \"(?i)\\\\b(ELSE|END|THEN|CASE|WHEN)\\\\b\", \"name\": \"keyword.control.case.cypher\" }, { \"match\": \"(?i)\\\\b(FIELDTERMINATOR|USING\\\\s+PERIODIC\\\\s+COMMIT|HEADERS|LOAD\\\\s+CSV|FROM)\\\\b\", \"name\": \"keyword.data.import.cypher\" }, { \"match\": \"(?i)\\\\b(USING\\\\s+INDEX|CREATE\\\\s+INDEX\\\\s+ON|DROP\\\\s+INDEX\\\\s+ON|CREATE\\\\s+CONSTRAINT\\\\s+ON|DROP\\\\s+CONSTRAINT\\\\s+ON)\\\\b\", \"name\": \"keyword.other.indexes.cypher\" }, { \"match\": \"(?i)\\\\b(MERGE|DELETE|SET|REMOVE|ON\\\\s+CREATE|ON\\\\s+MATCH|CREATE\\\\s+UNIQUE|CREATE)\\\\b\", \"name\": \"keyword.data.definition.cypher\" }, { \"match\": \"(?i)\\\\b(DESC|ASC)\\\\b\", \"name\": \"keyword.other.order.cypher\" }, { \"begin\": \"(?i)\\\\b(node|relationship|rel)((:)([\\\\p{L}_-][\\\\p{L}0-9_]*))?(?=\\\\s*\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.class.starting-functions-point.cypher\" }, \"2\": { \"name\": \"keyword.control.index-seperator.cypher\" }, \"3\": { \"name\": \"keyword.control.index-seperator.cypher\" }, \"4\": { \"name\": \"support.class.index.cypher\" } }, \"end\": \"\\\\)\", \"name\": \"source.starting-functions.cypher\", \"patterns\": [{ \"match\": \"((?:`.+?`)|(?:[\\\\p{L}_][\\\\p{L}0-9_]*))\", \"name\": \"variable.parameter.relationship-name.cypher\" }, { \"match\": \"(\\\\*)\", \"name\": \"keyword.control.starting-function-params.cypher\" }, { \"include\": \"#comments\" }, { \"include\": \"#numbers\" }, { \"include\": \"#strings\" }] }] }, \"numbers\": { \"patterns\": [{ \"match\": \"\\\\b\\\\d+(\\\\.\\\\d+)?\\\\b\", \"name\": \"constant.numeric.cypher\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(\\\\+|-|\\\\/|\\\\*|\\\\%|\\\\?|!)\", \"name\": \"keyword.operator.math.cypher\" }, { \"match\": \"(<=|=>|<>|<|>|=~|=)\", \"name\": \"keyword.operator.compare.cypher\" }, { \"match\": \"(?i)\\\\b(OR|AND|XOR|IS)\\\\b\", \"name\": \"keyword.operator.logical.cypher\" }, { \"match\": \"(?i)\\\\b(IN)\\\\b\", \"name\": \"keyword.operator.in.cypher\" }] }, \"path-patterns\": { \"patterns\": [{ \"match\": \"(<--|-->|--)\", \"name\": \"support.function.relationship-pattern.cypher\" }, { \"begin\": \"(<-|-)(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.relationship-pattern-start.cypher\" }, \"2\": { \"name\": \"keyword.operator.relationship-pattern-start.cypher\" } }, \"end\": \"(])(->|-)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.relationship-pattern-end.cypher\" }, \"2\": { \"name\": \"support.function.relationship-pattern-end.cypher\" } }, \"name\": \"path-pattern.cypher\", \"patterns\": [{ \"include\": \"#identifiers\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.relationship-type-start.cypher\" }, \"2\": { \"name\": \"entity.name.class.relationship.type.cypher\" } }, \"match\": \"(:)((?:`.+?`)|(?:[\\\\p{L}_][\\\\p{L}0-9_]*))\", \"name\": \"entity.name.class.relationship-type.cypher\" }, { \"captures\": { \"1\": { \"name\": \"support.type.operator.relationship-type-or.cypher\" }, \"2\": { \"name\": \"entity.name.class.relationship.type-or.cypher\" } }, \"match\": \"(\\\\|)(\\\\s*)((?:`.+?`)|(?:[\\\\p{L}_][\\\\p{L}0-9_]*))\", \"name\": \"entity.name.class.relationship-type-ored.cypher\" }, { \"match\": \"(?:\\\\?\\\\*|\\\\?|\\\\*)\\\\s*(?:\\\\d+\\\\s*(?:\\\\.\\\\.\\\\s*\\\\d+)?)?\", \"name\": \"support.function.relationship-pattern.quant.cypher\" }, { \"include\": \"#properties_literal\" }] }] }, \"properties_literal\": { \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.properties_literal.cypher\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.properties_literal.cypher\" } }, \"name\": \"source.cypher\", \"patterns\": [{ \"match\": \":|,\", \"name\": \"keyword.control.properties_literal.seperator.cypher\" }, { \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#functions\" }, { \"include\": \"#operators\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#numbers\" }, { \"include\": \"#strings\" }] }] }, \"string_escape\": { \"captures\": { \"2\": { \"name\": \"string.quoted.double.cypher\" } }, \"match\": `(\\\\\\\\\\\\\\\\|\\\\\\\\[tbnrf])|(\\\\\\\\'|\\\\\\\\\")`, \"name\": \"constant.character.escape.cypher\" }, \"strings\": { \"patterns\": [{ \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.cypher\", \"patterns\": [{ \"include\": \"#string_escape\" }] }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.cypher\", \"patterns\": [{ \"include\": \"#string_escape\" }] }] } }, \"scopeName\": \"source.cypher\", \"aliases\": [\"cql\"] });\nvar cypher = [\n  lang\n];\n\nexport { cypher as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;QAAO;QAAO;KAAS;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAsB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAAmC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAmC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAsG,SAAS;oBAA0D,QAAQ;gBAA0C;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAA2C,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAAmG,QAAQ;gBAAiC;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAAmF,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAAuI,QAAQ;gBAA+B;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAAgG,QAAQ;gBAAsC;gBAAG;oBAAE,WAAW;oBAAsG,SAAS;oBAA2F,QAAQ;gBAAiC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAmC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwI,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAoF,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA4H,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAwF,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA4E,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,SAAS;4BAA0C,QAAQ;wBAA8C;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkD;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAA6B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAA+C;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;wBAAG,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkD;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,SAAS;4BAA6C,QAAQ;wBAA6C;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoD;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,SAAS;4BAAqD,QAAQ;wBAAkD;wBAAG;4BAAE,SAAS;4BAA0D,QAAQ;wBAAqD;wBAAG;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAsD;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS,CAAC,oCAAoC,CAAC;YAAE,QAAQ;QAAmC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAiB,WAAW;QAAC;KAAM;AAAC;AAC33N,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}