{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/synthwave-84.mjs"], "sourcesContent": ["var synthwave84 = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#171520\",\n    \"activityBar.dropBackground\": \"#34294f66\",\n    \"activityBar.foreground\": \"#ffffffCC\",\n    \"activityBarBadge.background\": \"#f97e72\",\n    \"activityBarBadge.foreground\": \"#2a2139\",\n    \"badge.background\": \"#2a2139\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumbPicker.background\": \"#232530\",\n    \"button.background\": \"#614D85\",\n    \"debugToolBar.background\": \"#463465\",\n    \"diffEditor.insertedTextBackground\": \"#0beb9935\",\n    \"diffEditor.removedTextBackground\": \"#fe445035\",\n    \"dropdown.background\": \"#232530\",\n    \"dropdown.listBackground\": \"#2a2139\",\n    \"editor.background\": \"#262335\",\n    \"editor.findMatchBackground\": \"#D18616bb\",\n    \"editor.findMatchHighlightBackground\": \"#D1861655\",\n    \"editor.findRangeHighlightBackground\": \"#34294f1a\",\n    \"editor.hoverHighlightBackground\": \"#463564\",\n    \"editor.lineHighlightBorder\": \"#7059AB66\",\n    \"editor.rangeHighlightBackground\": \"#49549539\",\n    \"editor.selectionBackground\": \"#ffffff20\",\n    \"editor.selectionHighlightBackground\": \"#ffffff20\",\n    \"editor.wordHighlightBackground\": \"#34294f88\",\n    \"editor.wordHighlightStrongBackground\": \"#34294f88\",\n    \"editorBracketMatch.background\": \"#34294f66\",\n    \"editorBracketMatch.border\": \"#495495\",\n    \"editorCodeLens.foreground\": \"#ffffff7c\",\n    \"editorCursor.background\": \"#241b2f\",\n    \"editorCursor.foreground\": \"#f97e72\",\n    \"editorError.foreground\": \"#fe4450\",\n    \"editorGroup.border\": \"#495495\",\n    \"editorGroup.dropBackground\": \"#4954954a\",\n    \"editorGroupHeader.tabsBackground\": \"#241b2f\",\n    \"editorGutter.addedBackground\": \"#206d4bd6\",\n    \"editorGutter.deletedBackground\": \"#fa2e46a4\",\n    \"editorGutter.modifiedBackground\": \"#b893ce8f\",\n    \"editorIndentGuide.activeBackground\": \"#A148AB80\",\n    \"editorIndentGuide.background\": \"#444251\",\n    \"editorLineNumber.activeForeground\": \"#ffffffcc\",\n    \"editorLineNumber.foreground\": \"#ffffff73\",\n    \"editorOverviewRuler.addedForeground\": \"#09f7a099\",\n    \"editorOverviewRuler.border\": \"#34294fb3\",\n    \"editorOverviewRuler.deletedForeground\": \"#fe445099\",\n    \"editorOverviewRuler.errorForeground\": \"#fe4450dd\",\n    \"editorOverviewRuler.findMatchForeground\": \"#D1861699\",\n    \"editorOverviewRuler.modifiedForeground\": \"#b893ce99\",\n    \"editorOverviewRuler.warningForeground\": \"#72f1b8cc\",\n    \"editorRuler.foreground\": \"#A148AB80\",\n    \"editorSuggestWidget.highlightForeground\": \"#f97e72\",\n    \"editorSuggestWidget.selectedBackground\": \"#ffffff36\",\n    \"editorWarning.foreground\": \"#72f1b8cc\",\n    \"editorWidget.background\": \"#171520DC\",\n    \"editorWidget.border\": \"#ffffff22\",\n    \"editorWidget.resizeBorder\": \"#ffffff44\",\n    \"errorForeground\": \"#fe4450\",\n    \"extensionButton.prominentBackground\": \"#f97e72\",\n    \"extensionButton.prominentHoverBackground\": \"#ff7edb\",\n    \"focusBorder\": \"#1f212b\",\n    \"foreground\": \"#ffffff\",\n    \"gitDecoration.addedResourceForeground\": \"#72f1b8cc\",\n    \"gitDecoration.deletedResourceForeground\": \"#fe4450\",\n    \"gitDecoration.ignoredResourceForeground\": \"#ffffff59\",\n    \"gitDecoration.modifiedResourceForeground\": \"#b893ceee\",\n    \"gitDecoration.untrackedResourceForeground\": \"#72f1b8\",\n    \"input.background\": \"#2a2139\",\n    \"inputOption.activeBorder\": \"#ff7edb99\",\n    \"inputValidation.errorBackground\": \"#fe445080\",\n    \"inputValidation.errorBorder\": \"#fe445000\",\n    \"list.activeSelectionBackground\": \"#ffffff20\",\n    \"list.activeSelectionForeground\": \"#ffffff\",\n    \"list.dropBackground\": \"#34294f66\",\n    \"list.errorForeground\": \"#fe4450E6\",\n    \"list.focusBackground\": \"#ffffff20\",\n    \"list.focusForeground\": \"#ffffff\",\n    \"list.highlightForeground\": \"#f97e72\",\n    \"list.hoverBackground\": \"#37294d99\",\n    \"list.hoverForeground\": \"#ffffff\",\n    \"list.inactiveFocusBackground\": \"#2a213999\",\n    \"list.inactiveSelectionBackground\": \"#ffffff20\",\n    \"list.inactiveSelectionForeground\": \"#ffffff\",\n    \"list.warningForeground\": \"#72f1b8bb\",\n    \"menu.background\": \"#463465\",\n    \"minimapGutter.addedBackground\": \"#09f7a099\",\n    \"minimapGutter.deletedBackground\": \"#fe4450\",\n    \"minimapGutter.modifiedBackground\": \"#b893ce\",\n    \"panelTitle.activeBorder\": \"#f97e72\",\n    \"peekView.border\": \"#495495\",\n    \"peekViewEditor.background\": \"#232530\",\n    \"peekViewEditor.matchHighlightBackground\": \"#D18616bb\",\n    \"peekViewResult.background\": \"#232530\",\n    \"peekViewResult.matchHighlightBackground\": \"#D1861655\",\n    \"peekViewResult.selectionBackground\": \"#2a213980\",\n    \"peekViewTitle.background\": \"#232530\",\n    \"pickerGroup.foreground\": \"#f97e72ea\",\n    \"progressBar.background\": \"#f97e72\",\n    \"scrollbar.shadow\": \"#2a2139\",\n    \"scrollbarSlider.activeBackground\": \"#9d8bca20\",\n    \"scrollbarSlider.background\": \"#9d8bca30\",\n    \"scrollbarSlider.hoverBackground\": \"#9d8bca50\",\n    \"selection.background\": \"#ffffff20\",\n    \"sideBar.background\": \"#241b2f\",\n    \"sideBar.dropBackground\": \"#34294f4c\",\n    \"sideBar.foreground\": \"#ffffff99\",\n    \"sideBarSectionHeader.background\": \"#241b2f\",\n    \"sideBarSectionHeader.foreground\": \"#ffffffca\",\n    \"statusBar.background\": \"#241b2f\",\n    \"statusBar.debuggingBackground\": \"#f97e72\",\n    \"statusBar.debuggingForeground\": \"#08080f\",\n    \"statusBar.foreground\": \"#ffffff80\",\n    \"statusBar.noFolderBackground\": \"#241b2f\",\n    \"statusBarItem.prominentBackground\": \"#2a2139\",\n    \"statusBarItem.prominentHoverBackground\": \"#34294f\",\n    \"tab.activeBorder\": \"#880088\",\n    \"tab.border\": \"#241b2f00\",\n    \"tab.inactiveBackground\": \"#262335\",\n    \"terminal.ansiBlue\": \"#03edf9\",\n    \"terminal.ansiBrightBlue\": \"#03edf9\",\n    \"terminal.ansiBrightCyan\": \"#03edf9\",\n    \"terminal.ansiBrightGreen\": \"#72f1b8\",\n    \"terminal.ansiBrightMagenta\": \"#ff7edb\",\n    \"terminal.ansiBrightRed\": \"#fe4450\",\n    \"terminal.ansiBrightYellow\": \"#fede5d\",\n    \"terminal.ansiCyan\": \"#03edf9\",\n    \"terminal.ansiGreen\": \"#72f1b8\",\n    \"terminal.ansiMagenta\": \"#ff7edb\",\n    \"terminal.ansiRed\": \"#fe4450\",\n    \"terminal.ansiYellow\": \"#f3e70f\",\n    \"terminal.foreground\": \"#ffffff\",\n    \"terminal.selectionBackground\": \"#ffffff20\",\n    \"terminalCursor.background\": \"#ffffff\",\n    \"terminalCursor.foreground\": \"#03edf9\",\n    \"textLink.activeForeground\": \"#ff7edb\",\n    \"textLink.foreground\": \"#f97e72\",\n    \"titleBar.activeBackground\": \"#241b2f\",\n    \"titleBar.inactiveBackground\": \"#241b2f\",\n    \"walkThrough.embeddedEditorBackground\": \"#232530\",\n    \"widget.shadow\": \"#2a2139\"\n  },\n  \"displayName\": \"Synthwave '84\",\n  \"name\": \"synthwave-84\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"string.quoted.docstring.multi.python\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.begin.python\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.end.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#848bbd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted\",\n        \"string.template\",\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8b39\"\n      }\n    },\n    {\n      \"scope\": \"string.template meta.embedded.line\",\n      \"settings\": {\n        \"foreground\": \"#b6b1b1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"entity.name.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"constant\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"constant.language\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.html\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type\",\n        \"meta.attribute.class.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"foreground\": \"#D50\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.export.js\",\n        \"keyword.control.import.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric.decimal.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.new\",\n        \"keyword.operator.expression\",\n        \"keyword.operator.logical\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.object-literal.key\",\n        \"support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value\",\n      \"settings\": {\n        \"foreground\": \"#b6b1b1\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name.css\",\n        \"support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"switch-block.expr.js\",\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant.property.js, variable.other.property.js\",\n      \"settings\": {\n        \"foreground\": \"#2ee2fa\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.color\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.font-name\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.pseudo-element\",\n        \"entity.other.attribute-name.pseudo-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D50\"\n      }\n    },\n    {\n      \"scope\": \"support.function.misc.css\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"entity.name.section\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html\",\n        \"keyword.operator.assignment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffffffee\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b6b1b1cc\"\n      }\n    },\n    {\n      \"scope\": \"beginning.punctuation.definition.list\",\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link\",\n      \"settings\": {\n        \"foreground\": \"#D50\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.description\",\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.generic.python\",\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function-call.python\",\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.cs\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.variable.local.cs\",\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.variable.field.cs\",\n        \"entity.name.variable.property.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.placeholder.c\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive.include.c\",\n        \"keyword.control.directive.define.c\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.c\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": \"source.cpp keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.placeholder.cpp\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive.include.cpp\",\n        \"keyword.control.directive.define.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.specifier.const.cpp\",\n      \"settings\": {\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir support.type.elixir\",\n        \"source.elixir meta.module.elixir entity.name.class.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir constant.other.symbol.elixir\",\n        \"source.elixir constant.other.keywords.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir punctuation.definition.string\",\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir variable.other.readwrite.module.elixir\",\n        \"source.elixir variable.other.readwrite.module.elixir punctuation.definition.variable.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir .punctuation.binary.elixir\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.global.clojure\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.control.clojure\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.metadata.simple.clojure\",\n        \"meta.metadata.map.clojure\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fe4450\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.quoted-expression.clojure\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.symbol.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edbff\"\n      }\n    },\n    {\n      \"scope\": \"source.go\",\n      \"settings\": {\n        \"foreground\": \"#ff7edbff\"\n      }\n    },\n    {\n      \"scope\": \"source.go meta.function-call.go\",\n      \"settings\": {\n        \"foreground\": \"#36f9f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go keyword.package.go\",\n        \"source.go keyword.import.go\",\n        \"source.go keyword.function.go\",\n        \"source.go keyword.type.go\",\n        \"source.go keyword.const.go\",\n        \"source.go keyword.var.go\",\n        \"source.go keyword.map.go\",\n        \"source.go keyword.channel.go\",\n        \"source.go keyword.control.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go storage.type\",\n        \"source.go keyword.struct.go\",\n        \"source.go keyword.interface.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go constant.language.go\",\n        \"source.go constant.other.placeholder.go\",\n        \"source.go variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link.markdown\",\n        \"markup.inline.raw.string.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fede5d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading.markdown\",\n        \"entity.name.section.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#ff7edb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.quote.begin.markdown\",\n        \"markup.quote.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.dart\",\n        \"source.python\",\n        \"source.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7edbff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.interpolated.single.dart\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f97e72\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.dart\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric.dart\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2EE2FA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.template.expression.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f1b8\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { synthwave84 as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc,OAAO,MAAM,CAAC;IAC9B,UAAU;QACR,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,+BAA+B;QAC/B,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,mCAAmC;QACnC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,qCAAqC;QACrC,+BAA+B;QAC/B,uCAAuC;QACvC,8BAA8B;QAC9B,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,0CAA0C;QAC1C,yCAAyC;QACzC,0BAA0B;QAC1B,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,2BAA2B;QAC3B,uBAAuB;QACvB,6BAA6B;QAC7B,mBAAmB;QACnB,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,oBAAoB;QACpB,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,mBAAmB;QACnB,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,2BAA2B;QAC3B,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,0CAA0C;QAC1C,oBAAoB;QACpB,cAAc;QACd,0BAA0B;QAC1B,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,uBAAuB;QACvB,6BAA6B;QAC7B,+BAA+B;QAC/B,wCAAwC;QACxC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}