{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/purescript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PureScript\", \"fileTypes\": [\"purs\"], \"name\": \"purescript\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.purescript\" }, \"2\": { \"name\": \"punctuation.definition.entity.purescript\" } }, \"match\": \"(`)(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(`)\", \"name\": \"keyword.operator.function.infix.purescript\" }, { \"begin\": \"^\\\\s*\\\\b(module)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"end\": \"(where)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.module.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }, { \"match\": \"[a-z]+\", \"name\": \"invalid.purescript\" }] }, { \"begin\": \"^\\\\s*\\\\b(class)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.purescript\" } }, \"end\": \"\\\\b(where)\\\\b|$\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.typeclass.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^\\\\s*\\\\b(else\\\\s+)?(derive\\\\s+)?(newtype\\\\s+)?(instance)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" }, \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"keyword.other.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"\\\\b(where)\\\\b|$\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.instance.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(foreign)\\\\s+(import)\\\\s+(data)\\\\s+([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"keyword.other.purescript\" }, \"5\": { \"name\": \"entity.name.type.purescript\" }, \"6\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.kind-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.foreign.data.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#kind_signature\" }] }, { \"begin\": \"^(\\\\s*)(foreign)\\\\s+(import)\\\\s+([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"entity.name.function.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.foreign.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^\\\\s*\\\\b(import)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"end\": \"($|(?=--))\", \"name\": \"meta.import.purescript\", \"patterns\": [{ \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"match\": \"\\\\b(as|hiding)\\\\b\" }] }, { \"begin\": \"^(\\\\s)*(data|newtype)\\\\s+(.+?)\\\\s*(?==|$)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.data.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.declaration.type.data.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"0\": { \"name\": \"keyword.operator.assignment.purescript\" } }, \"match\": \"=\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#data_ctor\" }] }, \"2\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"match\": \"(?:(?:\\\\b([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<ctorArgs>(?:(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*|(?:(?:[\\\\w()'\\u2192\\u21D2\\\\[\\\\],]|->|=>)+\\\\s*)+))(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<ctorArgs>)?)?))\" }, { \"captures\": { \"0\": { \"name\": \"punctuation.separator.pipe.purescript\" } }, \"match\": \"\\\\|\" }, { \"include\": \"#record_types\" }] }, { \"begin\": \"^(\\\\s)*(type)\\\\s+(.+?)\\\\s*(?==|$)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.data.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.declaration.type.type.purescript\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"keyword.operator.assignment.purescript\" } }, \"match\": \"=\" }, { \"include\": \"#type_signature\" }, { \"include\": \"#record_types\" }, { \"include\": \"#comments\" }] }, { \"match\": \"^\\\\s*\\\\b(derive|where|data|type|newtype|infix[lr]?|foreign(\\\\s+import)?(\\\\s+data)?)(?!')\\\\b\", \"name\": \"keyword.other.purescript\" }, { \"match\": \"\\\\?(?:[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*|[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"name\": \"entity.name.function.typed-hole.purescript\" }, { \"match\": \"^\\\\s*\\\\b(data|type|newtype)(?!')\\\\b\", \"name\": \"storage.type.purescript\" }, { \"match\": \"\\\\b(do|ado|if|then|else|case|of|let|in)(?!('|\\\\s*(:|=)))\\\\b\", \"name\": \"keyword.control.purescript\" }, { \"match\": \"\\\\b(?<!\\\\$)0(x|X)[0-9a-fA-F]+\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.purescript\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.purescript\" }, \"1\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"2\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"3\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"4\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"5\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"6\": { \"name\": \"meta.delimiter.decimal.period.purescript\" } }, \"match\": \"(?<!\\\\$)(?:(?:\\\\b\\\\d+(\\\\.)\\\\d+[eE][+-]?\\\\d+\\\\b)|(?:\\\\b\\\\d+[eE][+-]?\\\\d+\\\\b)|(?:\\\\b\\\\d+(\\\\.)\\\\d+\\\\b)|(?:\\\\b\\\\d+\\\\b(?!\\\\.)))(?!\\\\$)\", \"name\": \"constant.numeric.decimal.purescript\" }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.purescript\" }, { \"match\": \"\\\\b((\\\\d+_?)*\\\\d+|0([xX][0-9a-fA-F]+|[oO][0-7]+))\\\\b\", \"name\": \"constant.numeric.purescript\" }, { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.purescript\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"name\": \"string.quoted.triple.purescript\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.purescript\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"name\": \"string.quoted.double.purescript\", \"patterns\": [{ \"include\": \"#characters\" }, { \"begin\": \"\\\\\\\\\\\\s\", \"beginCaptures\": { \"0\": { \"name\": \"markup.other.escape.newline.begin.purescript\" } }, \"end\": \"\\\\\\\\\", \"endCaptures\": { \"0\": { \"name\": \"markup.other.escape.newline.end.purescript\" } }, \"patterns\": [{ \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.purescript\" }] }] }, { \"match\": \"\\\\\\\\$\", \"name\": \"markup.other.escape.newline.purescript\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.purescript\" }, \"2\": { \"patterns\": [{ \"include\": \"#characters\" }] }, \"7\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"match\": `(')((?:[ -\\\\[\\\\]-~]|(\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\&]))|(\\\\\\\\o[0-7]+)|(\\\\\\\\x[0-9A-Fa-f]+)|(\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_])))(')`, \"name\": \"string.quoted.single.purescript\" }, { \"include\": \"#function_type_declaration\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] }, \"2\": { \"name\": \"keyword.other.double-colon.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"match\": \"\\\\((?<paren>(?:[^()]|\\\\(\\\\g<paren>\\\\))*)(::|\\u2237)(?<paren2>(?:[^()]|\\\\(\\\\g<paren2>\\\\))*)\\\\)\" }, { \"begin\": \"^(\\\\s*)(?:(::|\\u2237))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"end\": \"^(?!\\\\1[ \\\\t]*|[ \\\\t]*$)\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"include\": \"#data_ctor\" }, { \"include\": \"#comments\" }, { \"include\": \"#infix_op\" }, { \"match\": \"<-|->\", \"name\": \"keyword.other.arrow.purescript\" }, { \"match\": \"[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+\", \"name\": \"keyword.operator.purescript\" }, { \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }], \"repository\": { \"block_comment\": { \"patterns\": [{ \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\\\\s*\\\\|\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"end\": \"-\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"name\": \"comment.block.documentation.purescript\", \"patterns\": [{ \"include\": \"#block_comment\" }] }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.purescript\" } }, \"end\": \"-\\\\}\", \"name\": \"comment.block.purescript\", \"patterns\": [{ \"include\": \"#block_comment\" }] }] }, \"characters\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.escape.purescript\" }, \"2\": { \"name\": \"constant.character.escape.octal.purescript\" }, \"3\": { \"name\": \"constant.character.escape.hexadecimal.purescript\" }, \"4\": { \"name\": \"constant.character.escape.control.purescript\" } }, \"match\": `(?:[ -\\\\[\\\\]-~]|(\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\&]))|(\\\\\\\\o[0-7]+)|(\\\\\\\\x[0-9A-Fa-f]+)|(\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_]))` }] }, \"class_constraint\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.type.purescript\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type_name\" }, { \"include\": \"#generic_type\" }] } }, \"match\": \"(?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?)))\", \"name\": \"meta.class-constraint.purescript\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=--+\\\\s+\\\\|)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.purescript\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(--+)\\\\s+(\\\\|)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.purescript\" }, \"2\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.documentation.purescript\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.purescript\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.purescript\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.purescript\" }] }, { \"include\": \"#block_comment\" }] }, \"data_ctor\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.tag.purescript\" }] }, \"double_colon\": { \"patterns\": [{ \"match\": \"(?:::|\\u2237)\", \"name\": \"keyword.other.double-colon.purescript\" }] }, \"function_type_declaration\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(?:(::|\\u2237)(?!.*<-))\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.function.purescript\" }, \"3\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.function.type-declaration.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }] }] }, \"generic_type\": { \"patterns\": [{ \"match\": \"\\\\b(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"variable.other.generic-type.purescript\" }] }, \"infix_op\": { \"patterns\": [{ \"match\": \"(?:\\\\((?!--+\\\\))[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+\\\\))\", \"name\": \"entity.name.function.infix.purescript\" }] }, \"kind_signature\": { \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"keyword.other.star.purescript\" }, { \"match\": \"!\", \"name\": \"keyword.other.exclaimation-point.purescript\" }, { \"match\": \"#\", \"name\": \"keyword.other.pound-sign.purescript\" }, { \"match\": \"->|\\u2192\", \"name\": \"keyword.other.arrow.purescript\" }] }, \"module_exports\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"name\": \"meta.declaration.exports.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"entity.name.function.purescript\" }, { \"include\": \"#type_name\" }, { \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }, { \"include\": \"#infix_op\" }, { \"match\": \"\\\\(.*?\\\\)\", \"name\": \"meta.other.constructor-list.purescript\" }] }] }, \"module_name\": { \"patterns\": [{ \"match\": \"(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)*[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.?\", \"name\": \"support.other.module.purescript\" }] }, \"record_field_declaration\": { \"patterns\": [{ \"begin\": \"([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(::|\\u2237)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"entity.other.attribute-name.purescript\" }] }, \"2\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"(?=([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(::|\\u2237)|})\", \"name\": \"meta.record-field.type-declaration.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }, { \"include\": \"#record_types\" }] }] }, \"record_types\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.type.record.begin.purescript\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.type.record.end.purescript\" } }, \"name\": \"meta.type.record.purescript\", \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }, { \"include\": \"#record_field_declaration\" }, { \"include\": \"#comments\" }] }] }, \"type_name\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.type.purescript\" }] }, \"type_signature\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#class_constraint\" }] }, \"4\": { \"name\": \"keyword.other.big-arrow.purescript\" } }, \"match\": \"(?:(?:\\\\()(?:(?<classConstraints>(?:(?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?))))(?:\\\\s*(?:,)\\\\s*\\\\g<classConstraints>)?))(?:\\\\))(?:\\\\s*(=>|<=|\\u21D0|\\u21D2)))\", \"name\": \"meta.class-constraints.purescript\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#class_constraint\" }] }, \"4\": { \"name\": \"keyword.other.big-arrow.purescript\" } }, \"match\": \"((?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?))))\\\\s*(=>|<=|\\u21D0|\\u21D2)\", \"name\": \"meta.class-constraints.purescript\" }, { \"match\": \"->|\\u2192\", \"name\": \"keyword.other.arrow.purescript\" }, { \"match\": \"=>|\\u21D2\", \"name\": \"keyword.other.big-arrow.purescript\" }, { \"match\": \"<=|\\u21D0\", \"name\": \"keyword.other.big-arrow-left.purescript\" }, { \"match\": \"forall|\\u2200\", \"name\": \"keyword.other.forall.purescript\" }, { \"include\": \"#generic_type\" }, { \"include\": \"#type_name\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.purescript\" });\nvar purescript = [\n  lang\n];\n\nexport { purescript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,aAAa;QAAC;KAAO;IAAE,QAAQ;IAAc,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,SAAS;YAAuK,QAAQ;QAA6C;QAAG;YAAE,SAAS;YAA4B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAqB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA2B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAmB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAoE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,eAAe;YAAkC,OAAO;YAAmB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,eAAe;YAAkC,OAAO;YAA2B,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,eAAe;YAAkC,OAAO;YAA2B,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAc,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAoB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA2B,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;gBAAI;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAkf;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;gBAAM;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,eAAe;YAAkC,OAAO;YAA2B,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;gBAAI;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,SAAS;YAA+F,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAwG,QAAQ;QAA6C;QAAG;YAAE,SAAS;YAAuC,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAA+D,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAA2C,QAAQ;QAAkC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,SAAS;YAAqI,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAAuC;QAAG;YAAE,SAAS;YAAwD,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAQ,QAAQ;wBAAwD;qBAAE;gBAAC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAS,QAAQ;QAAyC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,SAAS,CAAC,6OAA6O,CAAC;YAAE,QAAQ;QAAkC;QAAG;YAAE,WAAW;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAgG;QAAG;YAAE,SAAS;YAA0B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAS,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAyC,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAAK,QAAQ;QAAyC;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,uBAAuB;oBAAG,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,uBAAuB;oBAAG,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,SAAS,CAAC,qOAAqO,CAAC;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiH,QAAQ;gCAA8B;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAyc,QAAQ;gBAAmC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAkB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAA0D;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAoD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAsC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiH,QAAQ;gBAA6B;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,QAAQ;gBAAwC;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAA2B,QAAQ;oBAA6C,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoK,QAAQ;gBAAyC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,QAAQ;gBAAwC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA8C;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAO,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAoK,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAyC;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAa,QAAQ;wBAAyC;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoO,QAAQ;gBAAkC;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgE,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiK,QAAQ;gCAAyC;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAAsE,QAAQ;oBAAiD,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAAK,QAAQ;wBAAyC;wBAAG;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiH,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAA4jB,QAAQ;gBAAoC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAAoe,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAoB;AAC7thB,IAAI,aAAa;IACf;CACD", "ignoreList": [0], "debugId": null}}]}