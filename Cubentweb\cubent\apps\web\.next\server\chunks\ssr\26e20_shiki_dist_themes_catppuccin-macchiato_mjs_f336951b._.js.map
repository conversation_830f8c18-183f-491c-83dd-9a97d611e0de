{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/catppuccin-macchiato.mjs"], "sourcesContent": ["var catppuccinMacchiato = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#00000000\",\n    \"activityBar.activeBorder\": \"#00000000\",\n    \"activityBar.activeFocusBorder\": \"#00000000\",\n    \"activityBar.background\": \"#181926\",\n    \"activityBar.border\": \"#00000000\",\n    \"activityBar.dropBorder\": \"#c6a0f633\",\n    \"activityBar.foreground\": \"#c6a0f6\",\n    \"activityBar.inactiveForeground\": \"#6e738d\",\n    \"activityBarBadge.background\": \"#c6a0f6\",\n    \"activityBarBadge.foreground\": \"#181926\",\n    \"activityBarTop.activeBorder\": \"#00000000\",\n    \"activityBarTop.dropBorder\": \"#c6a0f633\",\n    \"activityBarTop.foreground\": \"#c6a0f6\",\n    \"activityBarTop.inactiveForeground\": \"#6e738d\",\n    \"badge.background\": \"#494d64\",\n    \"badge.foreground\": \"#cad3f5\",\n    \"banner.background\": \"#494d64\",\n    \"banner.foreground\": \"#cad3f5\",\n    \"banner.iconForeground\": \"#cad3f5\",\n    \"breadcrumb.activeSelectionForeground\": \"#c6a0f6\",\n    \"breadcrumb.background\": \"#24273a\",\n    \"breadcrumb.focusForeground\": \"#c6a0f6\",\n    \"breadcrumb.foreground\": \"#cad3f5cc\",\n    \"breadcrumbPicker.background\": \"#1e2030\",\n    \"button.background\": \"#c6a0f6\",\n    \"button.border\": \"#00000000\",\n    \"button.foreground\": \"#181926\",\n    \"button.hoverBackground\": \"#dac1f9\",\n    \"button.secondaryBackground\": \"#5b6078\",\n    \"button.secondaryBorder\": \"#c6a0f6\",\n    \"button.secondaryForeground\": \"#cad3f5\",\n    \"button.secondaryHoverBackground\": \"#6a708c\",\n    \"button.separator\": \"#00000000\",\n    \"charts.blue\": \"#8aadf4\",\n    \"charts.foreground\": \"#cad3f5\",\n    \"charts.green\": \"#a6da95\",\n    \"charts.lines\": \"#b8c0e0\",\n    \"charts.orange\": \"#f5a97f\",\n    \"charts.purple\": \"#c6a0f6\",\n    \"charts.red\": \"#ed8796\",\n    \"charts.yellow\": \"#eed49f\",\n    \"checkbox.background\": \"#494d64\",\n    \"checkbox.border\": \"#00000000\",\n    \"checkbox.foreground\": \"#c6a0f6\",\n    \"commandCenter.activeBackground\": \"#5b607833\",\n    \"commandCenter.activeBorder\": \"#c6a0f6\",\n    \"commandCenter.activeForeground\": \"#c6a0f6\",\n    \"commandCenter.background\": \"#1e2030\",\n    \"commandCenter.border\": \"#00000000\",\n    \"commandCenter.foreground\": \"#b8c0e0\",\n    \"commandCenter.inactiveBorder\": \"#00000000\",\n    \"commandCenter.inactiveForeground\": \"#b8c0e0\",\n    \"debugConsole.errorForeground\": \"#ed8796\",\n    \"debugConsole.infoForeground\": \"#8aadf4\",\n    \"debugConsole.sourceForeground\": \"#f4dbd6\",\n    \"debugConsole.warningForeground\": \"#f5a97f\",\n    \"debugConsoleInputIcon.foreground\": \"#cad3f5\",\n    \"debugExceptionWidget.background\": \"#181926\",\n    \"debugExceptionWidget.border\": \"#c6a0f6\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#5b6078\",\n    \"debugIcon.breakpointDisabledForeground\": \"#ed879699\",\n    \"debugIcon.breakpointForeground\": \"#ed8796\",\n    \"debugIcon.breakpointStackframeForeground\": \"#5b6078\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#a47487\",\n    \"debugIcon.continueForeground\": \"#a6da95\",\n    \"debugIcon.disconnectForeground\": \"#5b6078\",\n    \"debugIcon.pauseForeground\": \"#8aadf4\",\n    \"debugIcon.restartForeground\": \"#8bd5ca\",\n    \"debugIcon.startForeground\": \"#a6da95\",\n    \"debugIcon.stepBackForeground\": \"#5b6078\",\n    \"debugIcon.stepIntoForeground\": \"#cad3f5\",\n    \"debugIcon.stepOutForeground\": \"#cad3f5\",\n    \"debugIcon.stepOverForeground\": \"#c6a0f6\",\n    \"debugIcon.stopForeground\": \"#ed8796\",\n    \"debugTokenExpression.boolean\": \"#c6a0f6\",\n    \"debugTokenExpression.error\": \"#ed8796\",\n    \"debugTokenExpression.number\": \"#f5a97f\",\n    \"debugTokenExpression.string\": \"#a6da95\",\n    \"debugToolBar.background\": \"#181926\",\n    \"debugToolBar.border\": \"#00000000\",\n    \"descriptionForeground\": \"#cad3f5\",\n    \"diffEditor.border\": \"#5b6078\",\n    \"diffEditor.diagonalFill\": \"#5b607899\",\n    \"diffEditor.insertedLineBackground\": \"#a6da9526\",\n    \"diffEditor.insertedTextBackground\": \"#a6da951a\",\n    \"diffEditor.removedLineBackground\": \"#ed879626\",\n    \"diffEditor.removedTextBackground\": \"#ed87961a\",\n    \"diffEditorOverview.insertedForeground\": \"#a6da95cc\",\n    \"diffEditorOverview.removedForeground\": \"#ed8796cc\",\n    \"disabledForeground\": \"#a5adcb\",\n    \"dropdown.background\": \"#1e2030\",\n    \"dropdown.border\": \"#c6a0f6\",\n    \"dropdown.foreground\": \"#cad3f5\",\n    \"dropdown.listBackground\": \"#5b6078\",\n    \"editor.background\": \"#24273a\",\n    \"editor.findMatchBackground\": \"#604456\",\n    \"editor.findMatchBorder\": \"#ed879633\",\n    \"editor.findMatchHighlightBackground\": \"#455c6d\",\n    \"editor.findMatchHighlightBorder\": \"#91d7e333\",\n    \"editor.findRangeHighlightBackground\": \"#455c6d\",\n    \"editor.findRangeHighlightBorder\": \"#91d7e333\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#a6da9526\",\n    \"editor.foldBackground\": \"#91d7e340\",\n    \"editor.foreground\": \"#cad3f5\",\n    \"editor.hoverHighlightBackground\": \"#91d7e340\",\n    \"editor.lineHighlightBackground\": \"#cad3f512\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#91d7e340\",\n    \"editor.rangeHighlightBorder\": \"#00000000\",\n    \"editor.selectionBackground\": \"#939ab740\",\n    \"editor.selectionHighlightBackground\": \"#939ab733\",\n    \"editor.selectionHighlightBorder\": \"#939ab733\",\n    \"editor.stackFrameHighlightBackground\": \"#eed49f26\",\n    \"editor.wordHighlightBackground\": \"#939ab733\",\n    \"editorBracketHighlight.foreground1\": \"#ed8796\",\n    \"editorBracketHighlight.foreground2\": \"#f5a97f\",\n    \"editorBracketHighlight.foreground3\": \"#eed49f\",\n    \"editorBracketHighlight.foreground4\": \"#a6da95\",\n    \"editorBracketHighlight.foreground5\": \"#7dc4e4\",\n    \"editorBracketHighlight.foreground6\": \"#c6a0f6\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#ee99a0\",\n    \"editorBracketMatch.background\": \"#939ab71a\",\n    \"editorBracketMatch.border\": \"#939ab7\",\n    \"editorCodeLens.foreground\": \"#8087a2\",\n    \"editorCursor.background\": \"#24273a\",\n    \"editorCursor.foreground\": \"#f4dbd6\",\n    \"editorError.background\": \"#00000000\",\n    \"editorError.border\": \"#00000000\",\n    \"editorError.foreground\": \"#ed8796\",\n    \"editorGroup.border\": \"#5b6078\",\n    \"editorGroup.dropBackground\": \"#c6a0f633\",\n    \"editorGroup.emptyBackground\": \"#24273a\",\n    \"editorGroupHeader.tabsBackground\": \"#181926\",\n    \"editorGutter.addedBackground\": \"#a6da95\",\n    \"editorGutter.background\": \"#24273a\",\n    \"editorGutter.commentGlyphForeground\": \"#c6a0f6\",\n    \"editorGutter.commentRangeForeground\": \"#363a4f\",\n    \"editorGutter.deletedBackground\": \"#ed8796\",\n    \"editorGutter.foldingControlForeground\": \"#939ab7\",\n    \"editorGutter.modifiedBackground\": \"#eed49f\",\n    \"editorHoverWidget.background\": \"#1e2030\",\n    \"editorHoverWidget.border\": \"#5b6078\",\n    \"editorHoverWidget.foreground\": \"#cad3f5\",\n    \"editorIndentGuide.activeBackground\": \"#5b6078\",\n    \"editorIndentGuide.background\": \"#494d64\",\n    \"editorInfo.background\": \"#00000000\",\n    \"editorInfo.border\": \"#00000000\",\n    \"editorInfo.foreground\": \"#8aadf4\",\n    \"editorInlayHint.background\": \"#1e2030bf\",\n    \"editorInlayHint.foreground\": \"#5b6078\",\n    \"editorInlayHint.parameterBackground\": \"#1e2030bf\",\n    \"editorInlayHint.parameterForeground\": \"#a5adcb\",\n    \"editorInlayHint.typeBackground\": \"#1e2030bf\",\n    \"editorInlayHint.typeForeground\": \"#b8c0e0\",\n    \"editorLightBulb.foreground\": \"#eed49f\",\n    \"editorLineNumber.activeForeground\": \"#c6a0f6\",\n    \"editorLineNumber.foreground\": \"#8087a2\",\n    \"editorLink.activeForeground\": \"#c6a0f6\",\n    \"editorMarkerNavigation.background\": \"#1e2030\",\n    \"editorMarkerNavigationError.background\": \"#ed8796\",\n    \"editorMarkerNavigationInfo.background\": \"#8aadf4\",\n    \"editorMarkerNavigationWarning.background\": \"#f5a97f\",\n    \"editorOverviewRuler.background\": \"#1e2030\",\n    \"editorOverviewRuler.border\": \"#cad3f512\",\n    \"editorOverviewRuler.modifiedForeground\": \"#eed49f\",\n    \"editorRuler.foreground\": \"#5b6078\",\n    \"editorStickyScrollHover.background\": \"#363a4f\",\n    \"editorSuggestWidget.background\": \"#1e2030\",\n    \"editorSuggestWidget.border\": \"#5b6078\",\n    \"editorSuggestWidget.foreground\": \"#cad3f5\",\n    \"editorSuggestWidget.highlightForeground\": \"#c6a0f6\",\n    \"editorSuggestWidget.selectedBackground\": \"#363a4f\",\n    \"editorWarning.background\": \"#00000000\",\n    \"editorWarning.border\": \"#00000000\",\n    \"editorWarning.foreground\": \"#f5a97f\",\n    \"editorWhitespace.foreground\": \"#939ab766\",\n    \"editorWidget.background\": \"#1e2030\",\n    \"editorWidget.foreground\": \"#cad3f5\",\n    \"editorWidget.resizeBorder\": \"#5b6078\",\n    \"errorForeground\": \"#ed8796\",\n    \"errorLens.errorBackground\": \"#ed879626\",\n    \"errorLens.errorBackgroundLight\": \"#ed879626\",\n    \"errorLens.errorForeground\": \"#ed8796\",\n    \"errorLens.errorForegroundLight\": \"#ed8796\",\n    \"errorLens.errorMessageBackground\": \"#ed879626\",\n    \"errorLens.hintBackground\": \"#a6da9526\",\n    \"errorLens.hintBackgroundLight\": \"#a6da9526\",\n    \"errorLens.hintForeground\": \"#a6da95\",\n    \"errorLens.hintForegroundLight\": \"#a6da95\",\n    \"errorLens.hintMessageBackground\": \"#a6da9526\",\n    \"errorLens.infoBackground\": \"#8aadf426\",\n    \"errorLens.infoBackgroundLight\": \"#8aadf426\",\n    \"errorLens.infoForeground\": \"#8aadf4\",\n    \"errorLens.infoForegroundLight\": \"#8aadf4\",\n    \"errorLens.infoMessageBackground\": \"#8aadf426\",\n    \"errorLens.statusBarErrorForeground\": \"#ed8796\",\n    \"errorLens.statusBarHintForeground\": \"#a6da95\",\n    \"errorLens.statusBarIconErrorForeground\": \"#ed8796\",\n    \"errorLens.statusBarIconWarningForeground\": \"#f5a97f\",\n    \"errorLens.statusBarInfoForeground\": \"#8aadf4\",\n    \"errorLens.statusBarWarningForeground\": \"#f5a97f\",\n    \"errorLens.warningBackground\": \"#f5a97f26\",\n    \"errorLens.warningBackgroundLight\": \"#f5a97f26\",\n    \"errorLens.warningForeground\": \"#f5a97f\",\n    \"errorLens.warningForegroundLight\": \"#f5a97f\",\n    \"errorLens.warningMessageBackground\": \"#f5a97f26\",\n    \"extensionBadge.remoteBackground\": \"#8aadf4\",\n    \"extensionBadge.remoteForeground\": \"#181926\",\n    \"extensionButton.prominentBackground\": \"#c6a0f6\",\n    \"extensionButton.prominentForeground\": \"#181926\",\n    \"extensionButton.prominentHoverBackground\": \"#dac1f9\",\n    \"extensionButton.separator\": \"#24273a\",\n    \"extensionIcon.preReleaseForeground\": \"#5b6078\",\n    \"extensionIcon.sponsorForeground\": \"#f5bde6\",\n    \"extensionIcon.starForeground\": \"#eed49f\",\n    \"extensionIcon.verifiedForeground\": \"#a6da95\",\n    \"focusBorder\": \"#c6a0f6\",\n    \"foreground\": \"#cad3f5\",\n    \"gitDecoration.addedResourceForeground\": \"#a6da95\",\n    \"gitDecoration.conflictingResourceForeground\": \"#c6a0f6\",\n    \"gitDecoration.deletedResourceForeground\": \"#ed8796\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6e738d\",\n    \"gitDecoration.modifiedResourceForeground\": \"#eed49f\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#ed8796\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#eed49f\",\n    \"gitDecoration.submoduleResourceForeground\": \"#8aadf4\",\n    \"gitDecoration.untrackedResourceForeground\": \"#a6da95\",\n    \"gitlens.closedAutolinkedIssueIconColor\": \"#c6a0f6\",\n    \"gitlens.closedPullRequestIconColor\": \"#ed8796\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#a6da95\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#f5a97f\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#eed49f\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#f5a97f\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#a6da95\",\n    \"gitlens.decorations.statusMergingOrRebasingConflictForegroundColor\": \"#ee99a0\",\n    \"gitlens.decorations.statusMergingOrRebasingForegroundColor\": \"#eed49f\",\n    \"gitlens.decorations.workspaceCurrentForegroundColor\": \"#c6a0f6\",\n    \"gitlens.decorations.workspaceRepoMissingForegroundColor\": \"#a5adcb\",\n    \"gitlens.decorations.workspaceRepoOpenForegroundColor\": \"#c6a0f6\",\n    \"gitlens.decorations.worktreeHasUncommittedChangesForegroundColor\": \"#f5a97f\",\n    \"gitlens.decorations.worktreeMissingForegroundColor\": \"#ee99a0\",\n    \"gitlens.graphChangesColumnAddedColor\": \"#a6da95\",\n    \"gitlens.graphChangesColumnDeletedColor\": \"#ed8796\",\n    \"gitlens.graphLane10Color\": \"#f5bde6\",\n    \"gitlens.graphLane1Color\": \"#c6a0f6\",\n    \"gitlens.graphLane2Color\": \"#eed49f\",\n    \"gitlens.graphLane3Color\": \"#8aadf4\",\n    \"gitlens.graphLane4Color\": \"#f0c6c6\",\n    \"gitlens.graphLane5Color\": \"#a6da95\",\n    \"gitlens.graphLane6Color\": \"#b7bdf8\",\n    \"gitlens.graphLane7Color\": \"#f4dbd6\",\n    \"gitlens.graphLane8Color\": \"#ed8796\",\n    \"gitlens.graphLane9Color\": \"#8bd5ca\",\n    \"gitlens.graphMinimapMarkerHeadColor\": \"#a6da95\",\n    \"gitlens.graphMinimapMarkerHighlightsColor\": \"#eed49f\",\n    \"gitlens.graphMinimapMarkerLocalBranchesColor\": \"#8aadf4\",\n    \"gitlens.graphMinimapMarkerRemoteBranchesColor\": \"#739df2\",\n    \"gitlens.graphMinimapMarkerStashesColor\": \"#c6a0f6\",\n    \"gitlens.graphMinimapMarkerTagsColor\": \"#f0c6c6\",\n    \"gitlens.graphMinimapMarkerUpstreamColor\": \"#96d382\",\n    \"gitlens.graphScrollMarkerHeadColor\": \"#a6da95\",\n    \"gitlens.graphScrollMarkerHighlightsColor\": \"#eed49f\",\n    \"gitlens.graphScrollMarkerLocalBranchesColor\": \"#8aadf4\",\n    \"gitlens.graphScrollMarkerRemoteBranchesColor\": \"#739df2\",\n    \"gitlens.graphScrollMarkerStashesColor\": \"#c6a0f6\",\n    \"gitlens.graphScrollMarkerTagsColor\": \"#f0c6c6\",\n    \"gitlens.graphScrollMarkerUpstreamColor\": \"#96d382\",\n    \"gitlens.gutterBackgroundColor\": \"#363a4f4d\",\n    \"gitlens.gutterForegroundColor\": \"#cad3f5\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#c6a0f6\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#c6a0f626\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#c6a0f6cc\",\n    \"gitlens.mergedPullRequestIconColor\": \"#c6a0f6\",\n    \"gitlens.openAutolinkedIssueIconColor\": \"#a6da95\",\n    \"gitlens.openPullRequestIconColor\": \"#a6da95\",\n    \"gitlens.trailingLineBackgroundColor\": \"#00000000\",\n    \"gitlens.trailingLineForegroundColor\": \"#cad3f54d\",\n    \"gitlens.unpublishedChangesIconColor\": \"#a6da95\",\n    \"gitlens.unpublishedCommitIconColor\": \"#a6da95\",\n    \"gitlens.unpulledChangesIconColor\": \"#f5a97f\",\n    \"icon.foreground\": \"#c6a0f6\",\n    \"input.background\": \"#363a4f\",\n    \"input.border\": \"#00000000\",\n    \"input.foreground\": \"#cad3f5\",\n    \"input.placeholderForeground\": \"#cad3f573\",\n    \"inputOption.activeBackground\": \"#5b6078\",\n    \"inputOption.activeBorder\": \"#c6a0f6\",\n    \"inputOption.activeForeground\": \"#cad3f5\",\n    \"inputValidation.errorBackground\": \"#ed8796\",\n    \"inputValidation.errorBorder\": \"#18192633\",\n    \"inputValidation.errorForeground\": \"#181926\",\n    \"inputValidation.infoBackground\": \"#8aadf4\",\n    \"inputValidation.infoBorder\": \"#18192633\",\n    \"inputValidation.infoForeground\": \"#181926\",\n    \"inputValidation.warningBackground\": \"#f5a97f\",\n    \"inputValidation.warningBorder\": \"#18192633\",\n    \"inputValidation.warningForeground\": \"#181926\",\n    \"issues.closed\": \"#c6a0f6\",\n    \"issues.newIssueDecoration\": \"#f4dbd6\",\n    \"issues.open\": \"#a6da95\",\n    \"list.activeSelectionBackground\": \"#363a4f\",\n    \"list.activeSelectionForeground\": \"#cad3f5\",\n    \"list.dropBackground\": \"#c6a0f633\",\n    \"list.focusAndSelectionBackground\": \"#494d64\",\n    \"list.focusBackground\": \"#363a4f\",\n    \"list.focusForeground\": \"#cad3f5\",\n    \"list.focusOutline\": \"#00000000\",\n    \"list.highlightForeground\": \"#c6a0f6\",\n    \"list.hoverBackground\": \"#363a4f80\",\n    \"list.hoverForeground\": \"#cad3f5\",\n    \"list.inactiveSelectionBackground\": \"#363a4f\",\n    \"list.inactiveSelectionForeground\": \"#cad3f5\",\n    \"list.warningForeground\": \"#f5a97f\",\n    \"listFilterWidget.background\": \"#494d64\",\n    \"listFilterWidget.noMatchesOutline\": \"#ed8796\",\n    \"listFilterWidget.outline\": \"#00000000\",\n    \"menu.background\": \"#24273a\",\n    \"menu.border\": \"#24273a80\",\n    \"menu.foreground\": \"#cad3f5\",\n    \"menu.selectionBackground\": \"#5b6078\",\n    \"menu.selectionBorder\": \"#00000000\",\n    \"menu.selectionForeground\": \"#cad3f5\",\n    \"menu.separatorBackground\": \"#5b6078\",\n    \"menubar.selectionBackground\": \"#494d64\",\n    \"menubar.selectionForeground\": \"#cad3f5\",\n    \"merge.commonContentBackground\": \"#494d64\",\n    \"merge.commonHeaderBackground\": \"#5b6078\",\n    \"merge.currentContentBackground\": \"#a6da9533\",\n    \"merge.currentHeaderBackground\": \"#a6da9566\",\n    \"merge.incomingContentBackground\": \"#8aadf433\",\n    \"merge.incomingHeaderBackground\": \"#8aadf466\",\n    \"minimap.background\": \"#1e203080\",\n    \"minimap.errorHighlight\": \"#ed8796bf\",\n    \"minimap.findMatchHighlight\": \"#91d7e34d\",\n    \"minimap.selectionHighlight\": \"#5b6078bf\",\n    \"minimap.selectionOccurrenceHighlight\": \"#5b6078bf\",\n    \"minimap.warningHighlight\": \"#f5a97fbf\",\n    \"minimapGutter.addedBackground\": \"#a6da95bf\",\n    \"minimapGutter.deletedBackground\": \"#ed8796bf\",\n    \"minimapGutter.modifiedBackground\": \"#eed49fbf\",\n    \"minimapSlider.activeBackground\": \"#c6a0f699\",\n    \"minimapSlider.background\": \"#c6a0f633\",\n    \"minimapSlider.hoverBackground\": \"#c6a0f666\",\n    \"notificationCenter.border\": \"#c6a0f6\",\n    \"notificationCenterHeader.background\": \"#1e2030\",\n    \"notificationCenterHeader.foreground\": \"#cad3f5\",\n    \"notificationLink.foreground\": \"#8aadf4\",\n    \"notificationToast.border\": \"#c6a0f6\",\n    \"notifications.background\": \"#1e2030\",\n    \"notifications.border\": \"#c6a0f6\",\n    \"notifications.foreground\": \"#cad3f5\",\n    \"notificationsErrorIcon.foreground\": \"#ed8796\",\n    \"notificationsInfoIcon.foreground\": \"#8aadf4\",\n    \"notificationsWarningIcon.foreground\": \"#f5a97f\",\n    \"panel.background\": \"#24273a\",\n    \"panel.border\": \"#5b6078\",\n    \"panelSection.border\": \"#5b6078\",\n    \"panelSection.dropBackground\": \"#c6a0f633\",\n    \"panelTitle.activeBorder\": \"#c6a0f6\",\n    \"panelTitle.activeForeground\": \"#cad3f5\",\n    \"panelTitle.inactiveForeground\": \"#a5adcb\",\n    \"peekView.border\": \"#c6a0f6\",\n    \"peekViewEditor.background\": \"#1e2030\",\n    \"peekViewEditor.matchHighlightBackground\": \"#91d7e34d\",\n    \"peekViewEditor.matchHighlightBorder\": \"#00000000\",\n    \"peekViewEditorGutter.background\": \"#1e2030\",\n    \"peekViewResult.background\": \"#1e2030\",\n    \"peekViewResult.fileForeground\": \"#cad3f5\",\n    \"peekViewResult.lineForeground\": \"#cad3f5\",\n    \"peekViewResult.matchHighlightBackground\": \"#91d7e34d\",\n    \"peekViewResult.selectionBackground\": \"#363a4f\",\n    \"peekViewResult.selectionForeground\": \"#cad3f5\",\n    \"peekViewTitle.background\": \"#24273a\",\n    \"peekViewTitleDescription.foreground\": \"#b8c0e0b3\",\n    \"peekViewTitleLabel.foreground\": \"#cad3f5\",\n    \"pickerGroup.border\": \"#c6a0f6\",\n    \"pickerGroup.foreground\": \"#c6a0f6\",\n    \"problemsErrorIcon.foreground\": \"#ed8796\",\n    \"problemsInfoIcon.foreground\": \"#8aadf4\",\n    \"problemsWarningIcon.foreground\": \"#f5a97f\",\n    \"progressBar.background\": \"#c6a0f6\",\n    \"pullRequests.closed\": \"#ed8796\",\n    \"pullRequests.draft\": \"#939ab7\",\n    \"pullRequests.merged\": \"#c6a0f6\",\n    \"pullRequests.notification\": \"#cad3f5\",\n    \"pullRequests.open\": \"#a6da95\",\n    \"sash.hoverBorder\": \"#c6a0f6\",\n    \"scrollbar.shadow\": \"#181926\",\n    \"scrollbarSlider.activeBackground\": \"#363a4f66\",\n    \"scrollbarSlider.background\": \"#5b607880\",\n    \"scrollbarSlider.hoverBackground\": \"#6e738d\",\n    \"selection.background\": \"#c6a0f666\",\n    \"settings.dropdownBackground\": \"#494d64\",\n    \"settings.dropdownListBorder\": \"#00000000\",\n    \"settings.focusedRowBackground\": \"#5b607833\",\n    \"settings.headerForeground\": \"#cad3f5\",\n    \"settings.modifiedItemIndicator\": \"#c6a0f6\",\n    \"settings.numberInputBackground\": \"#494d64\",\n    \"settings.numberInputBorder\": \"#00000000\",\n    \"settings.textInputBackground\": \"#494d64\",\n    \"settings.textInputBorder\": \"#00000000\",\n    \"sideBar.background\": \"#1e2030\",\n    \"sideBar.border\": \"#00000000\",\n    \"sideBar.dropBackground\": \"#c6a0f633\",\n    \"sideBar.foreground\": \"#cad3f5\",\n    \"sideBarSectionHeader.background\": \"#1e2030\",\n    \"sideBarSectionHeader.foreground\": \"#cad3f5\",\n    \"sideBarTitle.foreground\": \"#c6a0f6\",\n    \"statusBar.background\": \"#181926\",\n    \"statusBar.border\": \"#00000000\",\n    \"statusBar.debuggingBackground\": \"#f5a97f\",\n    \"statusBar.debuggingBorder\": \"#00000000\",\n    \"statusBar.debuggingForeground\": \"#181926\",\n    \"statusBar.foreground\": \"#cad3f5\",\n    \"statusBar.noFolderBackground\": \"#181926\",\n    \"statusBar.noFolderBorder\": \"#00000000\",\n    \"statusBar.noFolderForeground\": \"#cad3f5\",\n    \"statusBarItem.activeBackground\": \"#5b607866\",\n    \"statusBarItem.errorBackground\": \"#00000000\",\n    \"statusBarItem.errorForeground\": \"#ed8796\",\n    \"statusBarItem.hoverBackground\": \"#5b607833\",\n    \"statusBarItem.prominentBackground\": \"#00000000\",\n    \"statusBarItem.prominentForeground\": \"#c6a0f6\",\n    \"statusBarItem.prominentHoverBackground\": \"#5b607833\",\n    \"statusBarItem.remoteBackground\": \"#8aadf4\",\n    \"statusBarItem.remoteForeground\": \"#181926\",\n    \"statusBarItem.warningBackground\": \"#00000000\",\n    \"statusBarItem.warningForeground\": \"#f5a97f\",\n    \"symbolIcon.arrayForeground\": \"#f5a97f\",\n    \"symbolIcon.booleanForeground\": \"#c6a0f6\",\n    \"symbolIcon.classForeground\": \"#eed49f\",\n    \"symbolIcon.colorForeground\": \"#f5bde6\",\n    \"symbolIcon.constantForeground\": \"#f5a97f\",\n    \"symbolIcon.constructorForeground\": \"#b7bdf8\",\n    \"symbolIcon.enumeratorForeground\": \"#eed49f\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#eed49f\",\n    \"symbolIcon.eventForeground\": \"#f5bde6\",\n    \"symbolIcon.fieldForeground\": \"#cad3f5\",\n    \"symbolIcon.fileForeground\": \"#c6a0f6\",\n    \"symbolIcon.folderForeground\": \"#c6a0f6\",\n    \"symbolIcon.functionForeground\": \"#8aadf4\",\n    \"symbolIcon.interfaceForeground\": \"#eed49f\",\n    \"symbolIcon.keyForeground\": \"#8bd5ca\",\n    \"symbolIcon.keywordForeground\": \"#c6a0f6\",\n    \"symbolIcon.methodForeground\": \"#8aadf4\",\n    \"symbolIcon.moduleForeground\": \"#cad3f5\",\n    \"symbolIcon.namespaceForeground\": \"#eed49f\",\n    \"symbolIcon.nullForeground\": \"#ee99a0\",\n    \"symbolIcon.numberForeground\": \"#f5a97f\",\n    \"symbolIcon.objectForeground\": \"#eed49f\",\n    \"symbolIcon.operatorForeground\": \"#8bd5ca\",\n    \"symbolIcon.packageForeground\": \"#f0c6c6\",\n    \"symbolIcon.propertyForeground\": \"#ee99a0\",\n    \"symbolIcon.referenceForeground\": \"#eed49f\",\n    \"symbolIcon.snippetForeground\": \"#f0c6c6\",\n    \"symbolIcon.stringForeground\": \"#a6da95\",\n    \"symbolIcon.structForeground\": \"#8bd5ca\",\n    \"symbolIcon.textForeground\": \"#cad3f5\",\n    \"symbolIcon.typeParameterForeground\": \"#ee99a0\",\n    \"symbolIcon.unitForeground\": \"#cad3f5\",\n    \"symbolIcon.variableForeground\": \"#cad3f5\",\n    \"tab.activeBackground\": \"#24273a\",\n    \"tab.activeBorder\": \"#00000000\",\n    \"tab.activeBorderTop\": \"#c6a0f6\",\n    \"tab.activeForeground\": \"#c6a0f6\",\n    \"tab.activeModifiedBorder\": \"#eed49f\",\n    \"tab.border\": \"#1e2030\",\n    \"tab.hoverBackground\": \"#2e324a\",\n    \"tab.hoverBorder\": \"#00000000\",\n    \"tab.hoverForeground\": \"#c6a0f6\",\n    \"tab.inactiveBackground\": \"#1e2030\",\n    \"tab.inactiveForeground\": \"#6e738d\",\n    \"tab.inactiveModifiedBorder\": \"#eed49f4d\",\n    \"tab.lastPinnedBorder\": \"#c6a0f6\",\n    \"tab.unfocusedActiveBackground\": \"#1e2030\",\n    \"tab.unfocusedActiveBorder\": \"#00000000\",\n    \"tab.unfocusedActiveBorderTop\": \"#c6a0f64d\",\n    \"tab.unfocusedInactiveBackground\": \"#141620\",\n    \"table.headerBackground\": \"#363a4f\",\n    \"table.headerForeground\": \"#cad3f5\",\n    \"terminal.ansiBlack\": \"#a5adcb\",\n    \"terminal.ansiBlue\": \"#8aadf4\",\n    \"terminal.ansiBrightBlack\": \"#5b6078\",\n    \"terminal.ansiBrightBlue\": \"#8aadf4\",\n    \"terminal.ansiBrightCyan\": \"#91d7e3\",\n    \"terminal.ansiBrightGreen\": \"#a6da95\",\n    \"terminal.ansiBrightMagenta\": \"#f5bde6\",\n    \"terminal.ansiBrightRed\": \"#ed8796\",\n    \"terminal.ansiBrightWhite\": \"#494d64\",\n    \"terminal.ansiBrightYellow\": \"#eed49f\",\n    \"terminal.ansiCyan\": \"#91d7e3\",\n    \"terminal.ansiGreen\": \"#a6da95\",\n    \"terminal.ansiMagenta\": \"#f5bde6\",\n    \"terminal.ansiRed\": \"#ed8796\",\n    \"terminal.ansiWhite\": \"#b8c0e0\",\n    \"terminal.ansiYellow\": \"#eed49f\",\n    \"terminal.border\": \"#5b6078\",\n    \"terminal.dropBackground\": \"#c6a0f633\",\n    \"terminal.foreground\": \"#cad3f5\",\n    \"terminal.inactiveSelectionBackground\": \"#5b607880\",\n    \"terminal.selectionBackground\": \"#5b6078\",\n    \"terminal.tab.activeBorder\": \"#c6a0f6\",\n    \"terminalCommandDecoration.defaultBackground\": \"#5b6078\",\n    \"terminalCommandDecoration.errorBackground\": \"#ed8796\",\n    \"terminalCommandDecoration.successBackground\": \"#a6da95\",\n    \"terminalCursor.background\": \"#24273a\",\n    \"terminalCursor.foreground\": \"#f4dbd6\",\n    \"textBlockQuote.background\": \"#1e2030\",\n    \"textBlockQuote.border\": \"#181926\",\n    \"textCodeBlock.background\": \"#24273a\",\n    \"textLink.activeForeground\": \"#91d7e3\",\n    \"textLink.foreground\": \"#8aadf4\",\n    \"textPreformat.foreground\": \"#cad3f5\",\n    \"textSeparator.foreground\": \"#c6a0f6\",\n    \"titleBar.activeBackground\": \"#181926\",\n    \"titleBar.activeForeground\": \"#cad3f5\",\n    \"titleBar.border\": \"#00000000\",\n    \"titleBar.inactiveBackground\": \"#181926\",\n    \"titleBar.inactiveForeground\": \"#cad3f580\",\n    \"tree.inactiveIndentGuidesStroke\": \"#494d64\",\n    \"tree.indentGuidesStroke\": \"#939ab7\",\n    \"walkThrough.embeddedEditorBackground\": \"#24273a4d\",\n    \"welcomePage.progress.background\": \"#181926\",\n    \"welcomePage.progress.foreground\": \"#c6a0f6\",\n    \"welcomePage.tileBackground\": \"#1e2030\",\n    \"widget.shadow\": \"#1e203080\",\n    \"window.activeBorder\": \"#00000000\",\n    \"window.inactiveBorder\": \"#00000000\"\n  },\n  \"displayName\": \"Catppuccin Macchiato\",\n  \"name\": \"catppuccin-macchiato\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"boolean\": {\n      \"foreground\": \"#f5a97f\"\n    },\n    \"builtinAttribute.attribute.library:rust\": {\n      \"foreground\": \"#8aadf4\"\n    },\n    \"class.builtin:python\": {\n      \"foreground\": \"#c6a0f6\"\n    },\n    \"class:python\": {\n      \"foreground\": \"#eed49f\"\n    },\n    \"constant.builtin.readonly:nix\": {\n      \"foreground\": \"#c6a0f6\"\n    },\n    \"enumMember\": {\n      \"foreground\": \"#8bd5ca\"\n    },\n    \"function.decorator:python\": {\n      \"foreground\": \"#f5a97f\"\n    },\n    \"generic.attribute:rust\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"heading\": {\n      \"foreground\": \"#ed8796\"\n    },\n    \"number\": {\n      \"foreground\": \"#f5a97f\"\n    },\n    \"pol\": {\n      \"foreground\": \"#f0c6c6\"\n    },\n    \"property.readonly:javascript\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"property.readonly:javascriptreact\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"property.readonly:typescript\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"property.readonly:typescriptreact\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"selfKeyword\": {\n      \"foreground\": \"#ed8796\"\n    },\n    \"text.emph\": {\n      \"fontStyle\": \"italic\",\n      \"foreground\": \"#ed8796\"\n    },\n    \"text.math\": {\n      \"foreground\": \"#f0c6c6\"\n    },\n    \"text.strong\": {\n      \"fontStyle\": \"bold\",\n      \"foreground\": \"#ed8796\"\n    },\n    \"tomlArrayKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#8aadf4\"\n    },\n    \"tomlTableKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#8aadf4\"\n    },\n    \"type.defaultLibrary:go\": {\n      \"foreground\": \"#c6a0f6\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#ee99a0\"\n    },\n    \"variable.readonly.defaultLibrary:go\": {\n      \"foreground\": \"#c6a0f6\"\n    },\n    \"variable.readonly:javascript\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"variable.readonly:javascriptreact\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"variable.readonly:scala\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"variable.readonly:typescript\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"variable.readonly:typescriptreact\": {\n      \"foreground\": \"#cad3f5\"\n    },\n    \"variable.typeHint:python\": {\n      \"foreground\": \"#eed49f\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"text\",\n        \"source\",\n        \"variable.other.readwrite\",\n        \"punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#939ab7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e738d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"variable.other.constant\",\n        \"entity.name.constant\",\n        \"constant.language.boolean\",\n        \"constant.language.false\",\n        \"constant.language.true\",\n        \"keyword.other.unit.user-defined\",\n        \"keyword.other.unit.suffix.floating-point\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"keyword.operator.word\",\n        \"keyword.operator.new\",\n        \"variable.language.super\",\n        \"support.type.primitive\",\n        \"storage.type\",\n        \"storage.modifier\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.documentation\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"punctuation.accessor\",\n        \"punctuation.definition.generic\",\n        \"meta.function.closure punctuation.section.parameters\",\n        \"punctuation.definition.tag\",\n        \"punctuation.separator.key-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.method\",\n        \"support.function\",\n        \"support.function.misc\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.other.inherited-class\",\n        \"support.class\",\n        \"meta.function-call.constructor\",\n        \"entity.name.struct\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.enum\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.enum variable.other.readwrite\",\n        \"variable.other.enummember\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"meta.property.object\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type\",\n        \"meta.type-alias\",\n        \"support.type\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation variable.function\",\n        \"meta.annotation variable.annotation.function\",\n        \"meta.annotation punctuation.definition.annotation\",\n        \"meta.decorator\",\n        \"punctuation.decorator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"meta.function.parameters\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"support.function.builtin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.documentation\",\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive\",\n        \"punctuation.definition.directive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.typeparameters\",\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.this\",\n        \"variable.language.this punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.template variable\",\n        \"string variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.new\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.specifier.extern.cpp\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.scope-resolution.template.call.cpp\",\n        \"entity.name.scope-resolution.parameter.cpp\",\n        \"entity.name.scope-resolution.cpp\",\n        \"entity.name.scope-resolution.function.definition.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.doxygen\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.reference.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"meta.interpolation.cs\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.documentation.cs\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css entity.other.attribute-name.class.css\",\n        \"entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.operator.css\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.other.attribute-name.pseudo-class\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"source.css constant.other.unicode-range\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"source.css variable.parameter.url\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.vendored.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-value variable\",\n        \"source.css meta.property-value variable.other.less\",\n        \"source.css meta.property-value variable.other.less punctuation.definition.variable.less\",\n        \"meta.definition.variable.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-list variable\",\n        \"meta.property-list variable.other.less\",\n        \"meta.property-list variable.other.less punctuation.definition.variable.less\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.percentage.css\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"source.css meta.attribute-selector\",\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.definition.ini\",\n        \"punctuation.support.type.property-name.json\",\n        \"support.type.property-name.json\",\n        \"punctuation.support.type.property-name.toml\",\n        \"support.type.property-name.toml\",\n        \"entity.name.tag.yaml\",\n        \"punctuation.support.type.property-name.yaml\",\n        \"support.type.property-name.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.json\",\n        \"constant.language.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.anchor.yaml\",\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name.table\",\n        \"entity.name.section.group-title.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.time.datetime.offset.toml\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.anchor.yaml\",\n        \"punctuation.definition.alias.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.document.begin.yaml\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.header.from-file\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.from-file.diff\",\n        \"punctuation.definition.to-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.go\",\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.parameters.go\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.go\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"variable.graphql\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#f0c6c6\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql\",\n      \"settings\": {\n        \"foreground\": \"#f0c6c6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.doctype\",\n        \"meta.tag.sgml.doctype punctuation.definition.tag\",\n        \"meta.tag.metadata.doctype entity.name.tag\",\n        \"meta.tag.metadata.doctype punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html constant.character.entity\",\n        \"text.html constant.character.entity punctuation\",\n        \"constant.character.entity.xml\",\n        \"constant.character.entity.xml punctuation\",\n        \"constant.character.entity.js.jsx\",\n        \"constant.charactger.entity.js.jsx punctuation\",\n        \"constant.character.entity.tsx\",\n        \"constant.character.entity.tsx punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\",\n        \"support.class.component.jsx\",\n        \"support.class.component.tsx\",\n        \"support.class.component.vue\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.annotation\",\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.enum.java\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.javadoc.java keyword.other.documentation.javadoc.java\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"meta.export variable.other.readwrite.js\",\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.property.js\",\n        \"variable.other.property.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.jsdoc\",\n        \"comment.block.documentation variable.other\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.jsdoc\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console.js\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.node\",\n        \"support.type.object.module.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.implements\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.null.js\",\n        \"constant.language.null.ts\",\n        \"constant.language.undefined.js\",\n        \"constant.language.undefined.ts\",\n        \"support.type.builtin.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.generic\",\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.declaration.function.arrow.js\",\n        \"storage.type.function.arrow.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.decorator.ts\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.in.js\",\n        \"keyword.operator.expression.in.ts\",\n        \"keyword.operator.expression.infer.ts\",\n        \"keyword.operator.expression.instanceof.js\",\n        \"keyword.operator.expression.instanceof.ts\",\n        \"keyword.operator.expression.is\",\n        \"keyword.operator.expression.keyof.ts\",\n        \"keyword.operator.expression.of.js\",\n        \"keyword.operator.expression.of.ts\",\n        \"keyword.operator.expression.typeof.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.julia\",\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"text.tex keyword.control.preamble\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"text.tex support.function.be\",\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.general.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#f0c6c6\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-dash.documentation.lua storage.type.annotation.lua\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.double-dash.documentation.lua entity.name.variable.lua\",\n        \"comment.line.double-dash.documentation.lua variable.lua\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.1.markdown punctuation.definition.heading.markdown\",\n        \"heading.1.markdown\",\n        \"heading.1.quarto punctuation.definition.heading.quarto\",\n        \"heading.1.quarto\",\n        \"markup.heading.atx.1.mdx\",\n        \"markup.heading.atx.1.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.heading-0.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.2.markdown punctuation.definition.heading.markdown\",\n        \"heading.2.markdown\",\n        \"heading.2.quarto punctuation.definition.heading.quarto\",\n        \"heading.2.quarto\",\n        \"markup.heading.atx.2.mdx\",\n        \"markup.heading.atx.2.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.2.markdown\",\n        \"markup.heading.heading-1.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.3.markdown punctuation.definition.heading.markdown\",\n        \"heading.3.markdown\",\n        \"heading.3.quarto punctuation.definition.heading.quarto\",\n        \"heading.3.quarto\",\n        \"markup.heading.atx.3.mdx\",\n        \"markup.heading.atx.3.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-2.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.4.markdown punctuation.definition.heading.markdown\",\n        \"heading.4.markdown\",\n        \"heading.4.quarto punctuation.definition.heading.quarto\",\n        \"heading.4.quarto\",\n        \"markup.heading.atx.4.mdx\",\n        \"markup.heading.atx.4.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-3.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.5.markdown punctuation.definition.heading.markdown\",\n        \"heading.5.markdown\",\n        \"heading.5.quarto punctuation.definition.heading.quarto\",\n        \"heading.5.quarto\",\n        \"markup.heading.atx.5.mdx\",\n        \"markup.heading.atx.5.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-4.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.6.markdown punctuation.definition.heading.markdown\",\n        \"heading.6.markdown\",\n        \"heading.6.quarto punctuation.definition.heading.quarto\",\n        \"heading.6.quarto\",\n        \"markup.heading.atx.6.mdx\",\n        \"markup.heading.atx.6.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-5.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\",\n        \"foreground\": \"#a5adcb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.link\",\n        \"markup.underline.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.link.title\",\n        \"text.html.quarto punctuation.definition.link.title\",\n        \"string.other.link.title.markdown\",\n        \"string.other.link.title.quarto\",\n        \"markup.link\",\n        \"punctuation.definition.constant.markdown\",\n        \"punctuation.definition.constant.quarto\",\n        \"constant.other.reference.link.markdown\",\n        \"constant.other.reference.link.quarto\",\n        \"markup.substitution.attribute-reference\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b7bdf8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.raw.markdown\",\n        \"punctuation.definition.raw.quarto\",\n        \"markup.inline.raw.string.markdown\",\n        \"markup.inline.raw.string.quarto\",\n        \"markup.raw.block.markdown\",\n        \"markup.raw.block.quarto\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block punctuation.definition\",\n        \"markup.raw support.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#939ab7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\",\n        \"punctuation.definition.quote.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin.markdown\",\n        \"punctuation.definition.list.begin.quarto\",\n        \"markup.list.bullet\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.quarto\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.multipart.nix\",\n        \"entity.other.attribute-name.single.nix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#b7bdf8\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.path.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.attribute.builtin\",\n        \"meta.attribute.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.php punctuation.definition.variable.php\",\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"text.html.php support.function\",\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.phpdoc.php\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.magic.python\",\n        \"meta.function-call.arguments.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\",\n        \"variable.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow.python\",\n        \"keyword.operator.logical.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.python\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.token.decorator.python\",\n        \"meta.function.decorator.identifier.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.decorator.python\",\n        \"punctuation.definition.decorator.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.exception.python\",\n        \"support.function.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.indexed-name.python\",\n        \"meta.item-access.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.string.python\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.python\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.anchor.regexp\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.ts\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\",\n        \"keyword.other.back-reference.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6da95\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.range.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f4dbd6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.numeric.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.no-capture.regexp\",\n        \"meta.assertion.look-ahead.regexp\",\n        \"meta.assertion.negative-look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation.rust\",\n        \"meta.annotation.rust punctuation\",\n        \"meta.attribute.rust\",\n        \"punctuation.definition.attribute.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute.rust string.quoted.double.rust\",\n        \"meta.attribute.rust string.quoted.single.char.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.macro.rules.rust\",\n        \"storage.type.module.rust\",\n        \"storage.modifier.rust\",\n        \"storage.type.struct.rust\",\n        \"storage.type.enum.rust\",\n        \"storage.type.trait.rust\",\n        \"storage.type.union.rust\",\n        \"storage.type.impl.rust\",\n        \"storage.type.rust\",\n        \"storage.type.function.rust\",\n        \"storage.type.type.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.numeric.rust\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"meta.generic.rust\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.impl.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.trait.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.union.rust\",\n      \"settings\": {\n        \"foreground\": \"#eed49f\"\n      }\n    },\n    {\n      \"scope\": \"meta.enum.rust storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.macro.rust\",\n        \"meta.macro.rust support.function.rust\",\n        \"entity.name.function.macro.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.lifetime.rust\",\n        \"entity.name.type.lifetime\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.rust constant.other.placeholder.rust\",\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.return-type.rust meta.generic.rust storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust\",\n      \"settings\": {\n        \"foreground\": \"#8aadf4\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.brackets.angle.rust\",\n      \"settings\": {\n        \"foreground\": \"#91d7e3\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.caps.rust\",\n      \"settings\": {\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function.definition.rust variable.other.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee99a0\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust variable.other.rust\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.self.rust\",\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.metavariable.name.rust\",\n        \"meta.macro.metavariable.rust keyword.operator.macro.dollar.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.shebang\",\n        \"comment.line.shebang punctuation.definition.comment\",\n        \"comment.line.shebang\",\n        \"punctuation.definition.comment.shebang.shell\",\n        \"meta.shebang.shell\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f5bde6\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.shebang constant.language\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\",\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ed8796\"\n      }\n    },\n    {\n      \"scope\": \"meta.string meta.interpolation.parameter.shell variable.other.readwrite\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f5a97f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell punctuation.section.interpolation\",\n        \"punctuation.definition.evaluation.backticks.shell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8bd5ca\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#c6a0f6\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell variable.other.normal.shell\",\n      \"settings\": {\n        \"foreground\": \"#cad3f5\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { catppuccinMacchiato as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,sBAAsB,OAAO,MAAM,CAAC;IACtC,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,qCAAqC;QACrC,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,8BAA8B;QAC9B,mCAAmC;QACnC,oBAAoB;QACpB,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,oCAAoC;QACpC,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,wCAAwC;QACxC,kCAAkC;QAClC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,0BAA0B;QAC1B,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,6BAA6B;QAC7B,kCAAkC;QAClC,6BAA6B;QAC7B,kCAAkC;QAClC,oCAAoC;QACpC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,sCAAsC;QACtC,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,qCAAqC;QACrC,wCAAwC;QACxC,+BAA+B;QAC/B,oCAAoC;QACpC,+BAA+B;QAC/B,oCAAoC;QACpC,sCAAsC;QACtC,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,6BAA6B;QAC7B,sCAAsC;QACtC,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,0CAA0C;QAC1C,sCAAsC;QACtC,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,uDAAuD;QACvD,2DAA2D;QAC3D,wDAAwD;QACxD,oEAAoE;QACpE,sDAAsD;QACtD,wCAAwC;QACxC,0CAA0C;QAC1C,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,uCAAuC;QACvC,6CAA6C;QAC7C,gDAAgD;QAChD,iDAAiD;QACjD,0CAA0C;QAC1C,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,4CAA4C;QAC5C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,sCAAsC;QACtC,0CAA0C;QAC1C,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,wCAAwC;QACxC,oCAAoC;QACpC,uCAAuC;QACvC,uCAAuC;QACvC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,6BAA6B;QAC7B,eAAe;QACf,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,oCAAoC;QACpC,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,wCAAwC;QACxC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,uBAAuB;QACvB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,uBAAuB;QACvB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,4BAA4B;QAC5B,sBAAsB;QACtB,kBAAkB;QAClB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,gCAAgC;QAChC,mCAAmC;QACnC,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,uBAAuB;QACvB,wCAAwC;QACxC,gCAAgC;QAChC,6BAA6B;QAC7B,+CAA+C;QAC/C,6CAA6C;QAC7C,+CAA+C;QAC/C,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wCAAwC;QACxC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,WAAW;YACT,cAAc;QAChB;QACA,2CAA2C;YACzC,cAAc;QAChB;QACA,wBAAwB;YACtB,cAAc;QAChB;QACA,gBAAgB;YACd,cAAc;QAChB;QACA,iCAAiC;YAC/B,cAAc;QAChB;QACA,cAAc;YACZ,cAAc;QAChB;QACA,6BAA6B;YAC3B,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,WAAW;YACT,cAAc;QAChB;QACA,UAAU;YACR,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,eAAe;YACb,cAAc;QAChB;QACA,aAAa;YACX,aAAa;YACb,cAAc;QAChB;QACA,aAAa;YACX,cAAc;QAChB;QACA,eAAe;YACb,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,uCAAuC;YACrC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,4BAA4B;YAC1B,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}