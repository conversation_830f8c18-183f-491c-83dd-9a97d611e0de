"use strict";exports.id=3460,exports.ids=[3460],exports.modules={3460:(e,t,i)=>{i.r(t),i.d(t,{createOrReadKeyless:()=>S,removeKeyless:()=>m,safeParseClerkFile:()=>u});var s=i(76315),n=i(12024);let r=".clerk",l="clerk.lock",a=(...e)=>{let t=(0,n.dc)(),i=(0,n.y4)();return t.join(i(),r,...e)},o=".tmp",c=()=>a(o,"keyless.json"),d=()=>a(o,"README.md"),h=!1;function u(){let{readFileSync:e}=(0,n.p8)();try{let t,i=c();try{t=e(i,{encoding:"utf-8"})||"{}"}catch{t="{}"}return JSON.parse(t)}catch{return}}let p=()=>{let{writeFileSync:e}=(0,n.p8)();h=!0,e(l,"This file can be deleted. Please delete this file and refresh your application",{encoding:"utf8",mode:"0777",flag:"w"})},f=()=>{let{rmSync:e}=(0,n.p8)();try{e(l,{force:!0,recursive:!0})}catch{}h=!1},y=()=>{let{existsSync:e}=(0,n.p8)();return h||e(l)};async function S(){let{writeFileSync:e,mkdirSync:t}=(0,n.p8)();if(y())return null;p();let i=c(),l=d();t(a(o),{recursive:!0}),function(){let{existsSync:e,writeFileSync:t,readFileSync:i,appendFileSync:s}=(0,n.p8)(),l=(0,n.dc)(),a=(0,n.y4)(),o=l.join(a(),".gitignore");e(o)||t(o,""),i(o,"utf-8").includes(r+"/")||s(o,`
# clerk configuration (can include secrets)
/${r}/
`)}();let h=u();if((null==h?void 0:h.publishableKey)&&(null==h?void 0:h.secretKey))return f(),h;let S=(0,s.n)({}),m=await S.__experimental_accountlessApplications.createAccountlessApplication().catch(()=>null);return m&&(e(i,JSON.stringify(m),{encoding:"utf8",mode:"0777",flag:"w"}),e(l,`
## DO NOT COMMIT
This directory is auto-generated from \`@clerk/nextjs\` because you are running in Keyless mode. Avoid committing the \`.clerk/\` directory as it includes the secret key of the unclaimed instance.
  `,{encoding:"utf8",mode:"0777",flag:"w"})),f(),m}function m(){let{rmSync:e}=(0,n.p8)();if(!y()){p();try{e(a(),{force:!0,recursive:!0})}catch{}f()}}},12024:(e,t,i)=>{i.d(t,{y4:()=>o,p8:()=>l,dc:()=>a});var s=Object.getOwnPropertyNames;let n=((e,t)=>function(){return t||(0,e[s(e)[0]])((t={exports:{}}).exports,t),t.exports})({"src/runtime/node/safe-node-apis.js"(e,t){let{existsSync:s,writeFileSync:n,readFileSync:r,appendFileSync:l,mkdirSync:a,rmSync:o}=i(73024);t.exports={fs:{existsSync:s,writeFileSync:n,readFileSync:r,appendFileSync:l,mkdirSync:a,rmSync:o},path:i(76760),cwd:()=>process.cwd()}}})(),r=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},l=()=>(n.fs||r("fs"),n.fs),a=()=>(n.path||r("path"),n.path),o=()=>(n.cwd||r("cwd"),n.cwd)},26790:(e,t,i)=>{i.d(t,{z:()=>T});var s,n,r,l,a,o,c,d,h,u,p,f,y,S,m,k,g,w,b,v=i(45940);i(92867);var K=i(37081);i(27322),i(6264);var V=i(49530),E=i(57136),O=i(94051),M=class{constructor(){(0,O.VK)(this,r),(0,O.VK)(this,s,"clerk_telemetry_throttler"),(0,O.VK)(this,n,864e5)}isEventThrottled(e){if(!(0,O.S7)(this,r,o))return!1;let t=Date.now(),i=(0,O.jq)(this,r,l).call(this,e),c=(0,O.S7)(this,r,a)?.[i];if(!c){let e={...(0,O.S7)(this,r,a),[i]:t};localStorage.setItem((0,O.S7)(this,s),JSON.stringify(e))}if(c&&t-c>(0,O.S7)(this,n)){let e=(0,O.S7)(this,r,a);delete e[i],localStorage.setItem((0,O.S7)(this,s),JSON.stringify(e))}return!!c}};s=new WeakMap,n=new WeakMap,r=new WeakSet,l=function(e){let{sk:t,pk:i,payload:s,...n}=e,r={...s,...n};return JSON.stringify(Object.keys({...s,...n}).sort().map(e=>r[e]))},a=function(){let e=localStorage.getItem((0,O.S7)(this,s));return e?JSON.parse(e):{}},o=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,O.S7)(this,s)),!1}};var j={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},R=class{constructor(e){(0,O.VK)(this,f),(0,O.VK)(this,c),(0,O.VK)(this,d),(0,O.VK)(this,h,{}),(0,O.VK)(this,u,[]),(0,O.VK)(this,p),(0,O.OV)(this,c,{maxBufferSize:e.maxBufferSize??j.maxBufferSize,samplingRate:e.samplingRate??j.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:j.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,O.S7)(this,h).clerkVersion=e.clerkVersion??"":(0,O.S7)(this,h).clerkVersion="",(0,O.S7)(this,h).sdk=e.sdk,(0,O.S7)(this,h).sdkVersion=e.sdkVersion,(0,O.S7)(this,h).publishableKey=e.publishableKey??"";let t=(0,E.q5)(e.publishableKey);t&&((0,O.S7)(this,h).instanceType=t.instanceType),e.secretKey&&((0,O.S7)(this,h).secretKey=e.secretKey.substring(0,16)),(0,O.OV)(this,d,new M)}get isEnabled(){return!("development"!==(0,O.S7)(this,h).instanceType||(0,O.S7)(this,c).disabled||"undefined"!=typeof process&&(0,V.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,O.S7)(this,c).debug||"undefined"!=typeof process&&(0,V.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,O.jq)(this,f,b).call(this,e.event,e.payload);(0,O.jq)(this,f,g).call(this,t.event,t),(0,O.jq)(this,f,y).call(this,t,e.eventSamplingRate)&&((0,O.S7)(this,u).push(t),(0,O.jq)(this,f,m).call(this))}};c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakSet,y=function(e,t){return this.isEnabled&&!this.isDebug&&(0,O.jq)(this,f,S).call(this,e,t)},S=function(e,t){let i=Math.random();return!!(i<=(0,O.S7)(this,c).samplingRate&&(void 0===t||i<=t))&&!(0,O.S7)(this,d).isEventThrottled(e)},m=function(){if("undefined"==typeof window)return void(0,O.jq)(this,f,k).call(this);if((0,O.S7)(this,u).length>=(0,O.S7)(this,c).maxBufferSize){(0,O.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,O.S7)(this,p)),(0,O.jq)(this,f,k).call(this);return}(0,O.S7)(this,p)||("requestIdleCallback"in window?(0,O.OV)(this,p,requestIdleCallback(()=>{(0,O.jq)(this,f,k).call(this)})):(0,O.OV)(this,p,setTimeout(()=>{(0,O.jq)(this,f,k).call(this)},0)))},k=function(){fetch(new URL("/v1/event",(0,O.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,O.S7)(this,u)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,O.OV)(this,u,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},w=function(){let e={name:(0,O.S7)(this,h).sdk,version:(0,O.S7)(this,h).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},b=function(e,t){let i=(0,O.jq)(this,f,w).call(this);return{event:e,cv:(0,O.S7)(this,h).clerkVersion??"",it:(0,O.S7)(this,h).instanceType??"",sdk:i.name,sdkv:i.version,...(0,O.S7)(this,h).publishableKey?{pk:(0,O.S7)(this,h).publishableKey}:{},...(0,O.S7)(this,h).secretKey?{sk:(0,O.S7)(this,h).secretKey}:{},payload:t}};function T(e){let t={...e},i=(0,v.y3)(t),s=(0,v.Bs)({options:t,apiClient:i}),n=new R({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...i,...s,telemetry:n}}(0,K.C)(v.nr)},37081:(e,t,i)=>{function s(e){return async(...t)=>{let{data:i,errors:s}=await e(...t);if(s)throw s[0];return i}}function n(e){return(...t)=>{let{data:i,errors:s}=e(...t);if(s)throw s[0];return i}}i.d(t,{C:()=>s,R:()=>n})},76315:(e,t,i)=>{i.d(t,{n:()=>l});var s=i(26790),n=i(54726);let r={secretKey:n.rB,publishableKey:n.At,apiUrl:n.H$,apiVersion:n.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:n.Rg,domain:n.V2,isSatellite:n.fS,sdkMetadata:n.tm,telemetry:{disabled:n.nN,debug:n.Mh}},l=e=>(0,s.z)({...r,...e})}};