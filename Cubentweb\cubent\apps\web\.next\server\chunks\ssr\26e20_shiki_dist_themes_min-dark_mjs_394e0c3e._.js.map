{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/min-dark.mjs"], "sourcesContent": ["var minDark = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#1A1A1A\",\n    \"activityBar.foreground\": \"#7D7D7D\",\n    \"activityBarBadge.background\": \"#383838\",\n    \"badge.background\": \"#383838\",\n    \"badge.foreground\": \"#C1C1C1\",\n    \"button.background\": \"#333\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#79b8ff\",\n    \"debugIcon.breakpointDisabledForeground\": \"#848484\",\n    \"debugIcon.breakpointForeground\": \"#FF7A84\",\n    \"debugIcon.breakpointStackframeForeground\": \"#79b8ff\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#848484\",\n    \"debugIcon.continueForeground\": \"#FF7A84\",\n    \"debugIcon.disconnectForeground\": \"#FF7A84\",\n    \"debugIcon.pauseForeground\": \"#FF7A84\",\n    \"debugIcon.restartForeground\": \"#79b8ff\",\n    \"debugIcon.startForeground\": \"#79b8ff\",\n    \"debugIcon.stepBackForeground\": \"#FF7A84\",\n    \"debugIcon.stepIntoForeground\": \"#FF7A84\",\n    \"debugIcon.stepOutForeground\": \"#FF7A84\",\n    \"debugIcon.stepOverForeground\": \"#FF7A84\",\n    \"debugIcon.stopForeground\": \"#79b8ff\",\n    \"diffEditor.insertedTextBackground\": \"#3a632a4b\",\n    \"diffEditor.removedTextBackground\": \"#88063852\",\n    \"editor.background\": \"#1f1f1f\",\n    \"editor.lineHighlightBorder\": \"#303030\",\n    \"editorGroupHeader.tabsBackground\": \"#1A1A1A\",\n    \"editorGroupHeader.tabsBorder\": \"#1A1A1A\",\n    \"editorIndentGuide.activeBackground\": \"#383838\",\n    \"editorIndentGuide.background\": \"#2A2A2A\",\n    \"editorLineNumber.foreground\": \"#727272\",\n    \"editorRuler.foreground\": \"#2A2A2A\",\n    \"editorSuggestWidget.background\": \"#1A1A1A\",\n    \"focusBorder\": \"#444\",\n    \"foreground\": \"#888888\",\n    \"gitDecoration.ignoredResourceForeground\": \"#444444\",\n    \"input.background\": \"#2A2A2A\",\n    \"input.foreground\": \"#E0E0E0\",\n    \"inputOption.activeBackground\": \"#3a3a3a\",\n    \"list.activeSelectionBackground\": \"#212121\",\n    \"list.activeSelectionForeground\": \"#F5F5F5\",\n    \"list.focusBackground\": \"#292929\",\n    \"list.highlightForeground\": \"#EAEAEA\",\n    \"list.hoverBackground\": \"#262626\",\n    \"list.hoverForeground\": \"#9E9E9E\",\n    \"list.inactiveSelectionBackground\": \"#212121\",\n    \"list.inactiveSelectionForeground\": \"#F5F5F5\",\n    \"panelTitle.activeBorder\": \"#1f1f1f\",\n    \"panelTitle.activeForeground\": \"#FAFAFA\",\n    \"panelTitle.inactiveForeground\": \"#484848\",\n    \"peekView.border\": \"#444\",\n    \"peekViewEditor.background\": \"#242424\",\n    \"pickerGroup.border\": \"#363636\",\n    \"pickerGroup.foreground\": \"#EAEAEA\",\n    \"progressBar.background\": \"#FAFAFA\",\n    \"scrollbar.shadow\": \"#1f1f1f\",\n    \"sideBar.background\": \"#1A1A1A\",\n    \"sideBarSectionHeader.background\": \"#202020\",\n    \"statusBar.background\": \"#1A1A1A\",\n    \"statusBar.debuggingBackground\": \"#1A1A1A\",\n    \"statusBar.foreground\": \"#7E7E7E\",\n    \"statusBar.noFolderBackground\": \"#1A1A1A\",\n    \"statusBarItem.prominentBackground\": \"#fafafa1a\",\n    \"statusBarItem.remoteBackground\": \"#1a1a1a00\",\n    \"statusBarItem.remoteForeground\": \"#7E7E7E\",\n    \"symbolIcon.classForeground\": \"#FF9800\",\n    \"symbolIcon.constructorForeground\": \"#b392f0\",\n    \"symbolIcon.enumeratorForeground\": \"#FF9800\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#79b8ff\",\n    \"symbolIcon.eventForeground\": \"#FF9800\",\n    \"symbolIcon.fieldForeground\": \"#79b8ff\",\n    \"symbolIcon.functionForeground\": \"#b392f0\",\n    \"symbolIcon.interfaceForeground\": \"#79b8ff\",\n    \"symbolIcon.methodForeground\": \"#b392f0\",\n    \"symbolIcon.variableForeground\": \"#79b8ff\",\n    \"tab.activeBorder\": \"#1e1e1e\",\n    \"tab.activeForeground\": \"#FAFAFA\",\n    \"tab.border\": \"#1A1A1A\",\n    \"tab.inactiveBackground\": \"#1A1A1A\",\n    \"tab.inactiveForeground\": \"#727272\",\n    \"terminal.ansiBrightBlack\": \"#5c5c5c\",\n    \"textLink.activeForeground\": \"#fafafa\",\n    \"textLink.foreground\": \"#CCC\",\n    \"titleBar.activeBackground\": \"#1A1A1A\",\n    \"titleBar.border\": \"#00000000\"\n  },\n  \"displayName\": \"Min Dark\",\n  \"name\": \"min-dark\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#b392f0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"keyword.operator.accessor\",\n        \"meta.group.braces.round.function.arguments\",\n        \"meta.template.expression\",\n        \"markup.fenced_code meta.embedded.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b392f0\"\n      }\n    },\n    {\n      \"scope\": \"emphasis\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"strong\",\n        \"markup.heading.markdown\",\n        \"markup.bold.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#FF7A84\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"meta.link.inline.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#1976D2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"markup.fenced_code\",\n        \"markup.inline\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9db1c5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"string.quoted.docstring.multi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6b737c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"variable.language.this\",\n        \"variable.other.object\",\n        \"variable.other.class\",\n        \"variable.other.constant\",\n        \"meta.property-name\",\n        \"support\",\n        \"string.other.link.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#79b8ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.other.placeholder\",\n        \"constant.character.format.placeholder\",\n        \"meta.property-value\",\n        \"keyword.other.unit\",\n        \"keyword.other.template\",\n        \"entity.name.tag.yaml\",\n        \"entity.other.attribute-name\",\n        \"support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f8f8f8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"storage.modifier\",\n        \"storage.type\",\n        \"storage.control.clojure\",\n        \"entity.name.function.clojure\",\n        \"support.function.node\",\n        \"punctuation.separator.key-value\",\n        \"punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f97583\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#FF9800\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type\",\n        \"entity.other.inherited-class\",\n        \"meta.function-call\",\n        \"meta.instance.constructor\",\n        \"entity.other.attribute-name\",\n        \"entity.name.function\",\n        \"constant.keyword.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b392f0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"string.quoted\",\n        \"string.regexp\",\n        \"string.interpolated\",\n        \"string.template\",\n        \"string.unquoted.plain.out.yaml\",\n        \"keyword.other.template\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffab70\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#316bcd\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#cd9731\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#cd3131\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#800080\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.arguments\",\n        \"punctuation.definition.dict\",\n        \"punctuation.separator\",\n        \"meta.function-call.arguments\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bbbbbb\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link\",\n      \"settings\": {\n        \"foreground\": \"#ffab70\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF7A84\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.metadata.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffab70\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#79b8ff\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { minDark as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,0BAA0B;QAC1B,0BAA0B;QAC1B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,qBAAqB;QACrB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,0BAA0B;QAC1B,kCAAkC;QAClC,eAAe;QACf,cAAc;QACd,2CAA2C;QAC3C,oBAAoB;QACpB,oBAAoB;QACpB,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,sBAAsB;QACtB,mCAAmC;QACnC,wBAAwB;QACxB,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,kCAAkC;QAClC,+BAA+B;QAC/B,iCAAiC;QACjC,oBAAoB;QACpB,wBAAwB;QACxB,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,6BAA6B;QAC7B,mBAAmB;IACrB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}