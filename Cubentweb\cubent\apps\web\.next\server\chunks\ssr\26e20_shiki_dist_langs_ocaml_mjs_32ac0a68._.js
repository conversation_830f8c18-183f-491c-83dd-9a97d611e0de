module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/ocaml.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ocaml)
});
const lang = Object.freeze({
    "displayName": "OCaml",
    "fileTypes": [
        ".ml",
        ".mli"
    ],
    "name": "ocaml",
    "patterns": [
        {
            "include": "#comment"
        },
        {
            "include": "#pragma"
        },
        {
            "include": "#decl"
        }
    ],
    "repository": {
        "attribute": {
            "begin": "(\\[)[\\s]*((?<![#\\-:!?.@*/&%^+<=>|~$])@{1,3}(?![#\\-:!?.@*/&%^+<=>|~$]))",
            "beginCaptures": {
                "1": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                },
                "2": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": "\\]",
            "endCaptures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            },
            "patterns": [
                {
                    "include": "#attributePayload"
                }
            ]
        },
        "attributeIdentifier": {
            "captures": {
                "1": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                },
                "2": {
                    "name": "punctuation.definition.tag"
                }
            },
            "match": `((?<![#\\-:!?.@*/&%^+<=>|~$])%(?![#\\-:!?.@*/&%^+<=>|~$]))((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))`
        },
        "attributePayload": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]%|^%))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "((?<![#\\-:!?.@*/&%^+<=>|~$])[:?](?![#\\-:!?.@*/&%^+<=>|~$]))|(?<=[\\s])|(?=\\])",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#pathModuleExtended"
                        },
                        {
                            "include": "#pathRecord"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(?=\\])",
                    "patterns": [
                        {
                            "include": "#signature"
                        },
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\?|^\\?))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(?=\\])",
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\?|^\\?))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "(?=\\])|\\bwhen\\b",
                            "endCaptures": {
                                "1": {}
                            },
                            "patterns": [
                                {
                                    "include": "#pattern"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]when|^when))(?![\\w]))",
                            "end": "(?=\\])",
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        }
                    ]
                },
                {
                    "include": "#term"
                }
            ]
        },
        "bindClassTerm": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]class|^class|[^\\w]type|^type))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]class|^class|[^\\w]type|^type))(?![\\w]))",
                            "end": `(?=(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)[\\s]*,|[^\\sa-z%])|(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)|(?=\\btype\\b)`,
                            "endCaptures": {
                                "0": {
                                    "name": "entity.name.function strong emphasis"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#attributeIdentifier"
                                }
                            ]
                        },
                        {
                            "begin": "\\[",
                            "captures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "end": "\\]",
                            "patterns": [
                                {
                                    "include": "#type"
                                }
                            ]
                        },
                        {
                            "include": "#bindTermArgs"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$])|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#literalClassType"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\band\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#term"
                        }
                    ]
                }
            ]
        },
        "bindClassType": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]class|^class|[^\\w]type|^type))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]class|^class|[^\\w]type|^type))(?![\\w]))",
                            "end": `(?=(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)[\\s]*,|[^\\sa-z%])|(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)|(?=\\btype\\b)`,
                            "endCaptures": {
                                "0": {
                                    "name": "entity.name.function strong emphasis"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#attributeIdentifier"
                                }
                            ]
                        },
                        {
                            "begin": "\\[",
                            "captures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "end": "\\]",
                            "patterns": [
                                {
                                    "include": "#type"
                                }
                            ]
                        },
                        {
                            "include": "#bindTermArgs"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$])|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#literalClassType"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\band\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#literalClassType"
                        }
                    ]
                }
            ]
        },
        "bindConstructor": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]exception|^exception))(?![\\w]))|(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\+=|^\\+=|[^#\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\-:!?.@*/&%^+<=>|~$]\\||^\\|))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(:)|(\\bof\\b)|((?<![#\\-:!?.@*/&%^+<=>|~$])\\|(?![#\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "punctuation.definition.tag"
                        },
                        "3": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#attributeIdentifier"
                        },
                        {
                            "match": "\\.\\.",
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        {
                            "match": "\\b(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)\\b(?![\\s]*(?:\\.|\\([^\\*]))",
                            "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                        },
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\w]of|^of))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\|(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                }
            ]
        },
        "bindSignature": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModuleExtended"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\band\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#signature"
                        }
                    ]
                }
            ]
        },
        "bindStructure": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]and|^and))(?![\\w]))|(?=[A-Z])",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])(:(?!=))|(:?=)(?![#\\-:!?.@*/&%^+<=>|~$])|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "match": "\\bmodule\\b",
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                        },
                        {
                            "match": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)",
                            "name": "entity.name.function strong emphasis"
                        },
                        {
                            "begin": "\\((?!\\))",
                            "captures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "end": "\\)",
                            "patterns": [
                                {
                                    "include": "#comment"
                                },
                                {
                                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$]):(?![#\\-:!?.@*/&%^+<=>|~$])",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                                        }
                                    },
                                    "end": "(?=\\))",
                                    "patterns": [
                                        {
                                            "include": "#signature"
                                        }
                                    ]
                                },
                                {
                                    "include": "#variableModule"
                                }
                            ]
                        },
                        {
                            "include": "#literalUnit"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\b(and)\\b|((?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#signature"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:=|^:=|[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\b(?:(and)|(with))\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        },
                        "2": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#structure"
                        }
                    ]
                }
            ]
        },
        "bindTerm": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\w]and|^and|[^\\w]external|^external|[^\\w]let|^let|[^\\w]method|^method|[^\\w]val|^val))(?![\\w]))",
                    "end": "(\\bmodule\\b)|(\\bopen\\b)|(?<![#\\-:!?.@*/&%^+<=>|~$])(:)|((?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$]))(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                        },
                        "2": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "4": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\w]and|^and|[^\\w]external|^external|[^\\w]let|^let|[^\\w]method|^method|[^\\w]val|^val))(?![\\w]))",
                            "end": `(?=\\b(?:module|open)\\b)|(?=(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)[\\s]*,|[^\\sa-z%])|(\\brec\\b)|((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))`,
                            "endCaptures": {
                                "1": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                },
                                "2": {
                                    "name": "entity.name.function strong emphasis"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#attributeIdentifier"
                                },
                                {
                                    "include": "#comment"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]rec|^rec))(?![\\w]))",
                            "end": `((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))|(?=[^\\sA-Za-z])`,
                            "endCaptures": {
                                "0": {
                                    "name": "entity.name.function strong emphasis"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#bindTermArgs"
                                }
                            ]
                        },
                        {
                            "include": "#bindTermArgs"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]module|^module))(?![\\w]))",
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "include": "#declModule"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]open|^open))(?![\\w]))",
                    "end": "(?=\\bin\\b)|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "include": "#pathModuleSimple"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "\\btype\\b|(?=[^\\s])",
                            "endCaptures": {
                                "0": {
                                    "name": "keyword.control"
                                }
                            }
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))",
                            "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\.(?![#\\-:!?.@*/&%^+<=>|~$])",
                            "endCaptures": {
                                "0": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#pattern"
                                }
                            ]
                        },
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\band\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#term"
                        }
                    ]
                }
            ]
        },
        "bindTermArgs": {
            "patterns": [
                {
                    "applyEndPatternLast": true,
                    "begin": "~|\\?",
                    "beginCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "end": ":|(?=[^\\s])",
                    "endCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]~|^~|[^#\\-:!?.@*/&%^+<=>|~$]\\?|^\\?))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)|(?<=\\))`,
                            "endCaptures": {
                                "0": {
                                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#comment"
                                },
                                {
                                    "begin": "\\((?!\\*)",
                                    "captures": {
                                        "0": {
                                            "name": "punctuation.definition.tag"
                                        }
                                    },
                                    "end": "\\)",
                                    "patterns": [
                                        {
                                            "begin": "(?<=\\()",
                                            "end": ":|=",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "keyword"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                                                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                                                }
                                            ]
                                        },
                                        {
                                            "begin": "(?<=:)",
                                            "end": "=|(?=\\))",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "keyword"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "include": "#type"
                                                }
                                            ]
                                        },
                                        {
                                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                                            "end": "(?=\\))",
                                            "patterns": [
                                                {
                                                    "include": "#term"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "include": "#pattern"
                }
            ]
        },
        "bindType": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]type|^type))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\+=|=(?![#\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#attributeIdentifier"
                        },
                        {
                            "include": "#pathType"
                        },
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "entity.name.function strong"
                        },
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\+=|^\\+=|[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "\\band\\b|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#bindConstructor"
                        }
                    ]
                }
            ]
        },
        "comment": {
            "patterns": [
                {
                    "include": "#attribute"
                },
                {
                    "include": "#extension"
                },
                {
                    "include": "#commentBlock"
                },
                {
                    "include": "#commentDoc"
                }
            ]
        },
        "commentBlock": {
            "begin": "\\(\\*(?!\\*[^)])",
            "contentName": "emphasis",
            "end": "\\*\\)",
            "name": "comment constant.regexp meta.separator.markdown",
            "patterns": [
                {
                    "include": "#commentBlock"
                },
                {
                    "include": "#commentDoc"
                }
            ]
        },
        "commentDoc": {
            "begin": "\\(\\*\\*",
            "end": "\\*\\)",
            "name": "comment constant.regexp meta.separator.markdown",
            "patterns": [
                {
                    "match": "\\*"
                },
                {
                    "include": "#comment"
                }
            ]
        },
        "decl": {
            "patterns": [
                {
                    "include": "#declClass"
                },
                {
                    "include": "#declException"
                },
                {
                    "include": "#declInclude"
                },
                {
                    "include": "#declModule"
                },
                {
                    "include": "#declOpen"
                },
                {
                    "include": "#declTerm"
                },
                {
                    "include": "#declType"
                }
            ]
        },
        "declClass": {
            "begin": "\\bclass\\b",
            "beginCaptures": {
                "0": {
                    "name": "entity.name.class constant.numeric markup.underline"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]class|^class))(?![\\w]))",
                    "beginCaptures": {
                        "0": {
                            "name": "entity.name.class constant.numeric markup.underline"
                        }
                    },
                    "end": "\\btype\\b|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#bindClassTerm"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))",
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "include": "#bindClassType"
                        }
                    ]
                }
            ]
        },
        "declException": {
            "begin": "\\bexception\\b",
            "beginCaptures": {
                "0": {
                    "name": "keyword markup.underline"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#attributeIdentifier"
                },
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#bindConstructor"
                }
            ]
        },
        "declInclude": {
            "begin": "\\binclude\\b",
            "beginCaptures": {
                "0": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#attributeIdentifier"
                },
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#signature"
                }
            ]
        },
        "declModule": {
            "begin": "(?:(?<=(?:[^\\w]module|^module))(?![\\w]))|\\bmodule\\b",
            "beginCaptures": {
                "0": {
                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename markup.underline"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]module|^module))(?![\\w]))",
                    "end": "(\\btype\\b)|(?=[A-Z])",
                    "endCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#attributeIdentifier"
                        },
                        {
                            "include": "#comment"
                        },
                        {
                            "match": "\\brec\\b",
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))",
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "include": "#bindSignature"
                        }
                    ]
                },
                {
                    "begin": "(?=[A-Z])",
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "include": "#bindStructure"
                        }
                    ]
                }
            ]
        },
        "declOpen": {
            "begin": "\\bopen\\b",
            "beginCaptures": {
                "0": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#attributeIdentifier"
                },
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#pathModuleExtended"
                }
            ]
        },
        "declTerm": {
            "begin": "\\b(?:(external|val)|(method)|(let))\\b(!?)",
            "beginCaptures": {
                "1": {
                    "name": "support.type markup.underline"
                },
                "2": {
                    "name": "storage.type markup.underline"
                },
                "3": {
                    "name": "keyword.control markup.underline"
                },
                "4": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#bindTerm"
                }
            ]
        },
        "declType": {
            "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))|\\btype\\b",
            "beginCaptures": {
                "0": {
                    "name": "keyword markup.underline"
                }
            },
            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#bindType"
                }
            ]
        },
        "extension": {
            "begin": "(\\[)((?<![#\\-:!?.@*/&%^+<=>|~$])%{1,3}(?![#\\-:!?.@*/&%^+<=>|~$]))",
            "beginCaptures": {
                "1": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                },
                "2": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": "\\]",
            "endCaptures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            },
            "patterns": [
                {
                    "include": "#attributePayload"
                }
            ]
        },
        "literal": {
            "patterns": [
                {
                    "include": "#termConstructor"
                },
                {
                    "include": "#literalArray"
                },
                {
                    "include": "#literalBoolean"
                },
                {
                    "include": "#literalCharacter"
                },
                {
                    "include": "#literalList"
                },
                {
                    "include": "#literalNumber"
                },
                {
                    "include": "#literalObjectTerm"
                },
                {
                    "include": "#literalString"
                },
                {
                    "include": "#literalRecord"
                },
                {
                    "include": "#literalUnit"
                }
            ]
        },
        "literalArray": {
            "begin": "\\[\\|",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            },
            "end": "\\|\\]",
            "patterns": [
                {
                    "include": "#term"
                }
            ]
        },
        "literalBoolean": {
            "match": "\\bfalse|true\\b",
            "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
        },
        "literalCharacter": {
            "begin": "(?<![\\w])'",
            "end": "'",
            "name": "markup.punctuation.quote.beginning",
            "patterns": [
                {
                    "include": "#literalCharacterEscape"
                }
            ]
        },
        "literalCharacterEscape": {
            "match": `\\\\(?:[\\\\"'ntbr]|[\\d][\\d][\\d]|x[0-9A-Fa-f][0-9A-Fa-f]|o[0-3][0-7][0-7])`
        },
        "literalClassType": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "\\bobject\\b",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.tag emphasis"
                        }
                    },
                    "end": "\\bend\\b",
                    "patterns": [
                        {
                            "begin": "\\binherit\\b",
                            "beginCaptures": {
                                "0": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                }
                            },
                            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "begin": "\\bas\\b",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                        }
                                    },
                                    "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                                    "patterns": [
                                        {
                                            "include": "#variablePattern"
                                        }
                                    ]
                                },
                                {
                                    "include": "#type"
                                }
                            ]
                        },
                        {
                            "include": "#pattern"
                        },
                        {
                            "include": "#declTerm"
                        }
                    ]
                },
                {
                    "begin": "\\[",
                    "end": "\\]"
                }
            ]
        },
        "literalList": {
            "patterns": [
                {
                    "begin": "\\[",
                    "captures": {
                        "0": {
                            "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                        }
                    },
                    "end": "\\]",
                    "patterns": [
                        {
                            "include": "#term"
                        }
                    ]
                }
            ]
        },
        "literalNumber": {
            "match": "(?<![A-Za-z])[\\d][\\d]*(\\.[\\d][\\d]*)?",
            "name": "constant.numeric"
        },
        "literalObjectTerm": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "\\bobject\\b",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.tag emphasis"
                        }
                    },
                    "end": "\\bend\\b",
                    "patterns": [
                        {
                            "begin": "\\binherit\\b",
                            "beginCaptures": {
                                "0": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                }
                            },
                            "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "begin": "\\bas\\b",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                        }
                                    },
                                    "end": ";;|(?=\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                                    "patterns": [
                                        {
                                            "include": "#variablePattern"
                                        }
                                    ]
                                },
                                {
                                    "include": "#term"
                                }
                            ]
                        },
                        {
                            "include": "#pattern"
                        },
                        {
                            "include": "#declTerm"
                        }
                    ]
                },
                {
                    "begin": "\\[",
                    "end": "\\]"
                }
            ]
        },
        "literalRecord": {
            "begin": "\\{",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong strong"
                }
            },
            "end": "\\}",
            "patterns": [
                {
                    "begin": "(?<=\\{|;)",
                    "end": "(:)|(=)|(;)|(with)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "4": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModulePrefixSimple"
                        },
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]with|^with))(?![\\w]))",
                    "end": "(:)|(=)|(;)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(;)|(=)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": ";|(?=\\})",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#term"
                        }
                    ]
                }
            ]
        },
        "literalString": {
            "patterns": [
                {
                    "begin": '"',
                    "end": '"',
                    "name": "string beginning.punctuation.definition.quote.markdown",
                    "patterns": [
                        {
                            "include": "#literalStringEscape"
                        }
                    ]
                },
                {
                    "begin": "(\\{)([_a-z]*?)(\\|)",
                    "end": "(\\|)(\\2)(\\})",
                    "name": "string beginning.punctuation.definition.quote.markdown",
                    "patterns": [
                        {
                            "include": "#literalStringEscape"
                        }
                    ]
                }
            ]
        },
        "literalStringEscape": {
            "match": '\\\\(?:[\\\\"ntbr]|[\\d][\\d][\\d]|x[0-9A-Fa-f][0-9A-Fa-f]|o[0-3][0-7][0-7])'
        },
        "literalUnit": {
            "match": "\\(\\)",
            "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
        },
        "pathModuleExtended": {
            "patterns": [
                {
                    "include": "#pathModulePrefixExtended"
                },
                {
                    "match": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)",
                    "name": "entity.name.class constant.numeric"
                }
            ]
        },
        "pathModulePrefixExtended": {
            "begin": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\.|$|\\()",
            "beginCaptures": {
                "0": {
                    "name": "entity.name.class constant.numeric"
                }
            },
            "end": "(?![\\s\\.]|$|\\()",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "\\(",
                    "captures": {
                        "0": {
                            "name": "keyword.control"
                        }
                    },
                    "end": "\\)",
                    "patterns": [
                        {
                            "match": "((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\)))",
                            "name": "string.other.link variable.language variable.parameter emphasis"
                        },
                        {
                            "include": "#structure"
                        }
                    ]
                },
                {
                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\.(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword strong"
                        }
                    },
                    "end": "((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\.|$))|((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*(?:$|\\()))|((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\)))|(?![\\s\\.A-Z]|$|\\()",
                    "endCaptures": {
                        "1": {
                            "name": "entity.name.class constant.numeric"
                        },
                        "2": {
                            "name": "entity.name.function strong"
                        },
                        "3": {
                            "name": "string.other.link variable.language variable.parameter emphasis"
                        }
                    }
                }
            ]
        },
        "pathModulePrefixExtendedParens": {
            "begin": "\\(",
            "captures": {
                "0": {
                    "name": "keyword.control"
                }
            },
            "end": "\\)",
            "patterns": [
                {
                    "match": "((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\)))",
                    "name": "string.other.link variable.language variable.parameter emphasis"
                },
                {
                    "include": "#structure"
                }
            ]
        },
        "pathModulePrefixSimple": {
            "begin": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\.)",
            "beginCaptures": {
                "0": {
                    "name": "entity.name.class constant.numeric"
                }
            },
            "end": "(?![\\s\\.])",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\.(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword strong"
                        }
                    },
                    "end": "((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*\\.))|((?:\\b(?=[A-Z])[A-Za-z_][\\w']*)(?=[\\s]*))|(?![\\s\\.A-Z])",
                    "endCaptures": {
                        "1": {
                            "name": "entity.name.class constant.numeric"
                        },
                        "2": {
                            "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                        }
                    }
                }
            ]
        },
        "pathModuleSimple": {
            "patterns": [
                {
                    "include": "#pathModulePrefixSimple"
                },
                {
                    "match": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)",
                    "name": "entity.name.class constant.numeric"
                }
            ]
        },
        "pathRecord": {
            "patterns": [
                {
                    "begin": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                    "end": "(?=[^\\s\\.])(?!\\(\\*)",
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\.|^\\.))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\-:!?.@*/&%^+<=>|~$])\\.(?![#\\-:!?.@*/&%^+<=>|~$])",
                            "beginCaptures": {
                                "0": {
                                    "name": "keyword strong"
                                }
                            },
                            "end": `((?<![#\\-:!?.@*/&%^+<=>|~$])\\.(?![#\\-:!?.@*/&%^+<=>|~$]))|((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|mutable|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))|(?<=\\))|(?<=\\])`,
                            "endCaptures": {
                                "1": {
                                    "name": "keyword strong"
                                },
                                "2": {
                                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#comment"
                                },
                                {
                                    "include": "#pathModulePrefixSimple"
                                },
                                {
                                    "begin": "\\((?!\\*)",
                                    "captures": {
                                        "0": {
                                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                        }
                                    },
                                    "end": "\\)",
                                    "patterns": [
                                        {
                                            "include": "#term"
                                        }
                                    ]
                                },
                                {
                                    "begin": "\\[",
                                    "captures": {
                                        "0": {
                                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                        }
                                    },
                                    "end": "\\]",
                                    "patterns": [
                                        {
                                            "include": "#pattern"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "pattern": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#patternArray"
                },
                {
                    "include": "#patternLazy"
                },
                {
                    "include": "#patternList"
                },
                {
                    "include": "#patternMisc"
                },
                {
                    "include": "#patternModule"
                },
                {
                    "include": "#patternRecord"
                },
                {
                    "include": "#literal"
                },
                {
                    "include": "#patternParens"
                },
                {
                    "include": "#patternType"
                },
                {
                    "include": "#variablePattern"
                },
                {
                    "include": "#termOperator"
                }
            ]
        },
        "patternArray": {
            "begin": "\\[\\|",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            },
            "end": "\\|\\]",
            "patterns": [
                {
                    "include": "#pattern"
                }
            ]
        },
        "patternLazy": {
            "match": "lazy",
            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
        },
        "patternList": {
            "begin": "\\[",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            },
            "end": "\\]",
            "patterns": [
                {
                    "include": "#pattern"
                }
            ]
        },
        "patternMisc": {
            "captures": {
                "1": {
                    "name": "string.regexp strong"
                },
                "2": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                },
                "3": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "match": "((?<![#\\-:!?.@*/&%^+<=>|~$]),(?![#\\-:!?.@*/&%^+<=>|~$]))|([#\\-:!?.@*/&%^+<=>|~$]+)|\\b(as)\\b"
        },
        "patternModule": {
            "begin": "\\bmodule\\b",
            "beginCaptures": {
                "0": {
                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                }
            },
            "end": "(?=\\))",
            "patterns": [
                {
                    "include": "#declModule"
                }
            ]
        },
        "patternParens": {
            "begin": "\\((?!\\))",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "end": "\\)",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$]):(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        }
                    },
                    "end": "(?=\\))",
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "include": "#pattern"
                }
            ]
        },
        "patternRecord": {
            "begin": "\\{",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong strong"
                }
            },
            "end": "\\}",
            "patterns": [
                {
                    "begin": "(?<=\\{|;)",
                    "end": "(:)|(=)|(;)|(with)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "4": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModulePrefixSimple"
                        },
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]with|^with))(?![\\w]))",
                    "end": "(:)|(=)|(;)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(;)|(=)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": ";|(?=\\})",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#pattern"
                        }
                    ]
                }
            ]
        },
        "patternType": {
            "begin": "\\btype\\b",
            "beginCaptures": {
                "0": {
                    "name": "keyword"
                }
            },
            "end": "(?=\\))",
            "patterns": [
                {
                    "include": "#declType"
                }
            ]
        },
        "pragma": {
            "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$])#(?![#\\-:!?.@*/&%^+<=>|~$])",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#literalNumber"
                },
                {
                    "include": "#literalString"
                }
            ]
        },
        "signature": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#signatureLiteral"
                },
                {
                    "include": "#signatureFunctor"
                },
                {
                    "include": "#pathModuleExtended"
                },
                {
                    "include": "#signatureParens"
                },
                {
                    "include": "#signatureRecovered"
                },
                {
                    "include": "#signatureConstraints"
                }
            ]
        },
        "signatureConstraints": {
            "begin": "\\bwith\\b",
            "beginCaptures": {
                "0": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                }
            },
            "end": "(?=\\))|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]with|^with))(?![\\w]))",
                    "end": "\\b(?:(module)|(type))\\b",
                    "endCaptures": {
                        "1": {
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                        },
                        "2": {
                            "name": "keyword"
                        }
                    }
                },
                {
                    "include": "#declModule"
                },
                {
                    "include": "#declType"
                }
            ]
        },
        "signatureFunctor": {
            "patterns": [
                {
                    "begin": "\\bfunctor\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]functor|^functor))(?![\\w]))",
                            "end": "(\\(\\))|(\\((?!\\)))",
                            "endCaptures": {
                                "1": {
                                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                                },
                                "2": {
                                    "name": "punctuation.definition.tag"
                                }
                            }
                        },
                        {
                            "begin": "(?<=\\()",
                            "end": "(:)|(\\))",
                            "endCaptures": {
                                "1": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                                },
                                "2": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#variableModule"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#signature"
                                }
                            ]
                        },
                        {
                            "begin": "(?<=\\))",
                            "end": "(\\()|((?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "endCaptures": {
                                "1": {
                                    "name": "punctuation.definition.tag"
                                },
                                "2": {
                                    "name": "support.type strong"
                                }
                            }
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                            "patterns": [
                                {
                                    "include": "#signature"
                                }
                            ]
                        }
                    ]
                },
                {
                    "match": "(?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "name": "support.type strong"
                }
            ]
        },
        "signatureLiteral": {
            "begin": "\\bsig\\b",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag emphasis"
                }
            },
            "end": "\\bend\\b",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#decl"
                }
            ]
        },
        "signatureParens": {
            "begin": "\\((?!\\))",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "end": "\\)",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$]):(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        }
                    },
                    "end": "(?=\\))",
                    "patterns": [
                        {
                            "include": "#signature"
                        }
                    ]
                },
                {
                    "include": "#signature"
                }
            ]
        },
        "signatureRecovered": {
            "patterns": [
                {
                    "begin": "\\(|(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:|[^#\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\w]include|^include|[^\\w]open|^open))(?![\\w]))",
                    "end": "\\bmodule\\b|(?!$|[\\s]|\\bmodule\\b)",
                    "endCaptures": {
                        "0": {
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                        }
                    }
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]module|^module))(?![\\w]))",
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]module|^module))(?![\\w]))",
                            "end": "\\btype\\b",
                            "endCaptures": {
                                "0": {
                                    "name": "keyword"
                                }
                            }
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]type|^type))(?![\\w]))",
                            "end": "\\bof\\b",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            }
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]of|^of))(?![\\w]))",
                            "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                            "patterns": [
                                {
                                    "include": "#signature"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "structure": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#structureLiteral"
                },
                {
                    "include": "#structureFunctor"
                },
                {
                    "include": "#pathModuleExtended"
                },
                {
                    "include": "#structureParens"
                }
            ]
        },
        "structureFunctor": {
            "patterns": [
                {
                    "begin": "\\bfunctor\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]functor|^functor))(?![\\w]))",
                            "end": "(\\(\\))|(\\((?!\\)))",
                            "endCaptures": {
                                "1": {
                                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                                },
                                "2": {
                                    "name": "punctuation.definition.tag"
                                }
                            }
                        },
                        {
                            "begin": "(?<=\\()",
                            "end": "(:)|(\\))",
                            "endCaptures": {
                                "1": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                                },
                                "2": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#variableModule"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.tag"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#signature"
                                }
                            ]
                        },
                        {
                            "begin": "(?<=\\))",
                            "end": "(\\()|((?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "endCaptures": {
                                "1": {
                                    "name": "punctuation.definition.tag"
                                },
                                "2": {
                                    "name": "support.type strong"
                                }
                            }
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                            "patterns": [
                                {
                                    "include": "#structure"
                                }
                            ]
                        }
                    ]
                },
                {
                    "match": "(?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "name": "support.type strong"
                }
            ]
        },
        "structureLiteral": {
            "begin": "\\bstruct\\b",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag emphasis"
                }
            },
            "end": "\\bend\\b",
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#pragma"
                },
                {
                    "include": "#decl"
                }
            ]
        },
        "structureParens": {
            "begin": "\\(",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "end": "\\)",
            "patterns": [
                {
                    "include": "#structureUnpack"
                },
                {
                    "include": "#structure"
                }
            ]
        },
        "structureUnpack": {
            "begin": "\\bval\\b",
            "beginCaptures": {
                "0": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": "(?=\\))"
        },
        "term": {
            "patterns": [
                {
                    "include": "#termLet"
                },
                {
                    "include": "#termAtomic"
                }
            ]
        },
        "termAtomic": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "include": "#termConditional"
                },
                {
                    "include": "#termConstructor"
                },
                {
                    "include": "#termDelim"
                },
                {
                    "include": "#termFor"
                },
                {
                    "include": "#termFunction"
                },
                {
                    "include": "#literal"
                },
                {
                    "include": "#termMatch"
                },
                {
                    "include": "#termMatchRule"
                },
                {
                    "include": "#termPun"
                },
                {
                    "include": "#termOperator"
                },
                {
                    "include": "#termTry"
                },
                {
                    "include": "#termWhile"
                },
                {
                    "include": "#pathRecord"
                }
            ]
        },
        "termConditional": {
            "match": "\\b(?:if|then|else)\\b",
            "name": "keyword.control"
        },
        "termConstructor": {
            "patterns": [
                {
                    "include": "#pathModulePrefixSimple"
                },
                {
                    "match": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)",
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong"
                }
            ]
        },
        "termDelim": {
            "patterns": [
                {
                    "begin": "\\((?!\\))",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.tag"
                        }
                    },
                    "end": "\\)",
                    "patterns": [
                        {
                            "include": "#term"
                        }
                    ]
                },
                {
                    "begin": "\\bbegin\\b",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.tag"
                        }
                    },
                    "end": "\\bend\\b",
                    "patterns": [
                        {
                            "include": "#attributeIdentifier"
                        },
                        {
                            "include": "#term"
                        }
                    ]
                }
            ]
        },
        "termFor": {
            "patterns": [
                {
                    "begin": "\\bfor\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control"
                        }
                    },
                    "end": "\\bdone\\b",
                    "endCaptures": {
                        "0": {
                            "name": "keyword.control"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]for|^for))(?![\\w]))",
                            "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])=(?![#\\-:!?.@*/&%^+<=>|~$])",
                            "endCaptures": {
                                "0": {
                                    "name": "support.type strong"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#pattern"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "end": "\\b(?:downto|to)\\b",
                            "endCaptures": {
                                "0": {
                                    "name": "keyword.control"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]to|^to))(?![\\w]))",
                            "end": "\\bdo\\b",
                            "endCaptures": {
                                "0": {
                                    "name": "keyword.control"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]do|^do))(?![\\w]))",
                            "end": "(?=\\bdone\\b)",
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "termFunction": {
            "captures": {
                "1": {
                    "name": "storage.type"
                },
                "2": {
                    "name": "storage.type"
                }
            },
            "match": "\\b(?:(fun)|(function))\\b"
        },
        "termLet": {
            "patterns": [
                {
                    "begin": "(?:(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?<=;|\\())(?=[\\s]|\\blet\\b)|(?:(?<=(?:[^\\w]begin|^begin|[^\\w]do|^do|[^\\w]else|^else|[^\\w]in|^in|[^\\w]struct|^struct|[^\\w]then|^then|[^\\w]try|^try))(?![\\w]))|(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]@@|^@@))(?![#\\-:!?.@*/&%^+<=>|~$]))[\\s]+",
                    "end": "\\b(?:(and)|(let))\\b|(?=[^\\s])(?!\\(\\*)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        },
                        "2": {
                            "name": "storage.type markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]and|^and|[^\\w]let|^let))(?![\\w]))|(let)",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.type markup.underline"
                        }
                    },
                    "end": "\\b(?:(and)|(in))\\b|(?=\\}|\\)|\\]|\\b(?:end|class|exception|external|include|inherit|initializer|let|method|module|open|type|val)\\b)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp markup.underline"
                        },
                        "2": {
                            "name": "storage.type markup.underline"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#bindTerm"
                        }
                    ]
                }
            ]
        },
        "termMatch": {
            "begin": "\\bmatch\\b",
            "captures": {
                "0": {
                    "name": "keyword.control"
                }
            },
            "end": "\\bwith\\b",
            "patterns": [
                {
                    "include": "#term"
                }
            ]
        },
        "termMatchRule": {
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^\\w]fun|^fun|[^\\w]function|^function|[^\\w]with|^with))(?![\\w]))",
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])(\\|)|(->)(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "endCaptures": {
                        "1": {
                            "name": "support.type strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#attributeIdentifier"
                        },
                        {
                            "include": "#pattern"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\[#\\-:!?.@*/&%^+<=>|~$]\\||^\\|))(?![#\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\-:!?.@*/&%^+<=>|~$])\\|(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "support.type strong"
                        }
                    },
                    "end": "(?<![#\\-:!?.@*/&%^+<=>|~$])(\\|)|(->)(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "endCaptures": {
                        "1": {
                            "name": "support.type strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#pattern"
                        },
                        {
                            "begin": "\\bwhen\\b",
                            "beginCaptures": {
                                "0": {
                                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                                }
                            },
                            "end": "(?=(?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$]))",
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "termOperator": {
            "patterns": [
                {
                    "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$])#(?![#\\-:!?.@*/&%^+<=>|~$])",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword"
                        }
                    },
                    "end": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                    "endCaptures": {
                        "0": {
                            "name": "entity.name.function"
                        }
                    }
                },
                {
                    "captures": {
                        "0": {
                            "name": "keyword.control strong"
                        }
                    },
                    "match": "<-"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "2": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "match": "(,|[#\\-:!?.@*/&%^+<=>|~$]+)|(;)"
                },
                {
                    "match": "\\b(?:and|assert|asr|land|lazy|lsr|lxor|mod|new|or)\\b",
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            ]
        },
        "termPun": {
            "applyEndPatternLast": true,
            "begin": "(?<![#\\-:!?.@*/&%^+<=>|~$])\\?|~(?![#\\-:!?.@*/&%^+<=>|~$])",
            "beginCaptures": {
                "0": {
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                }
            },
            "end": ":|(?=[^\\s:])",
            "endCaptures": {
                "0": {
                    "name": "keyword"
                }
            },
            "patterns": [
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]\\?|^\\?|[^#\\-:!?.@*/&%^+<=>|~$]~|^~))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                    "endCaptures": {
                        "0": {
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                        }
                    }
                }
            ]
        },
        "termTry": {
            "begin": "\\btry\\b",
            "captures": {
                "0": {
                    "name": "keyword.control"
                }
            },
            "end": "\\bwith\\b",
            "patterns": [
                {
                    "include": "#term"
                }
            ]
        },
        "termWhile": {
            "patterns": [
                {
                    "begin": "\\bwhile\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control"
                        }
                    },
                    "end": "\\bdone\\b",
                    "endCaptures": {
                        "0": {
                            "name": "keyword.control"
                        }
                    },
                    "patterns": [
                        {
                            "begin": "(?:(?<=(?:[^\\w]while|^while))(?![\\w]))",
                            "end": "\\bdo\\b",
                            "endCaptures": {
                                "0": {
                                    "name": "keyword.control"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        },
                        {
                            "begin": "(?:(?<=(?:[^\\w]do|^do))(?![\\w]))",
                            "end": "(?=\\bdone\\b)",
                            "patterns": [
                                {
                                    "include": "#term"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "type": {
            "patterns": [
                {
                    "include": "#comment"
                },
                {
                    "match": "\\bnonrec\\b",
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                },
                {
                    "include": "#pathModulePrefixExtended"
                },
                {
                    "include": "#typeLabel"
                },
                {
                    "include": "#typeObject"
                },
                {
                    "include": "#typeOperator"
                },
                {
                    "include": "#typeParens"
                },
                {
                    "include": "#typePolymorphicVariant"
                },
                {
                    "include": "#typeRecord"
                },
                {
                    "include": "#typeConstructor"
                }
            ]
        },
        "typeConstructor": {
            "patterns": [
                {
                    "begin": `(_)|((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))|(')((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))|(?<=[^\\*]\\)|\\])`,
                    "beginCaptures": {
                        "1": {
                            "name": "comment constant.regexp meta.separator.markdown"
                        },
                        "3": {
                            "name": "string.other.link variable.language variable.parameter emphasis strong emphasis"
                        },
                        "4": {
                            "name": "keyword.control emphasis"
                        }
                    },
                    "end": `(?=\\((?!\\*)|\\*|:|,|=|\\.|>|-|\\{|\\[|\\+|\\}|\\)|\\]|;|\\|)|((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))[\\s]*(?!\\(\\*|[\\w])|(?=;;|\\}|\\)|\\]|\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\b)`,
                    "endCaptures": {
                        "1": {
                            "name": "entity.name.function strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModulePrefixExtended"
                        }
                    ]
                }
            ]
        },
        "typeLabel": {
            "patterns": [
                {
                    "begin": `(\\??)((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))[\\s]*((?<![#\\-:!?.@*/&%^+<=>|~$]):(?![#\\-:!?.@*/&%^+<=>|~$]))`,
                    "captures": {
                        "1": {
                            "name": "keyword strong emphasis"
                        },
                        "2": {
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        },
                        "3": {
                            "name": "keyword"
                        }
                    },
                    "end": "(?=(?<![#\\-:!?.@*/&%^+<=>|~$])->(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                }
            ]
        },
        "typeModule": {
            "begin": "\\bmodule\\b",
            "beginCaptures": {
                "0": {
                    "name": "markup.inserted constant.language support.constant.property-value entity.name.filename"
                }
            },
            "end": "(?=\\))",
            "patterns": [
                {
                    "include": "#pathModuleExtended"
                },
                {
                    "include": "#signatureConstraints"
                }
            ]
        },
        "typeObject": {
            "begin": "<",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong strong"
                }
            },
            "end": ">",
            "patterns": [
                {
                    "begin": "(?<=<|;)",
                    "end": "(:)|(?=>)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "4": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModulePrefixSimple"
                        },
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(;)|(?=>)",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                }
            ]
        },
        "typeOperator": {
            "patterns": [
                {
                    "match": ",|;|[#\\-:!?.@*/&%^+<=>|~$]+",
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                }
            ]
        },
        "typeParens": {
            "begin": "\\(",
            "captures": {
                "0": {
                    "name": "punctuation.definition.tag"
                }
            },
            "end": "\\)",
            "patterns": [
                {
                    "match": ",",
                    "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                },
                {
                    "include": "#typeModule"
                },
                {
                    "include": "#type"
                }
            ]
        },
        "typePolymorphicVariant": {
            "begin": "\\[",
            "end": "\\]",
            "patterns": []
        },
        "typeRecord": {
            "begin": "\\{",
            "captures": {
                "0": {
                    "name": "constant.language constant.numeric entity.other.attribute-name.id.css strong strong"
                }
            },
            "end": "\\}",
            "patterns": [
                {
                    "begin": "(?<=\\{|;)",
                    "end": "(:)|(=)|(;)|(with)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "4": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#comment"
                        },
                        {
                            "include": "#pathModulePrefixSimple"
                        },
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^\\w]with|^with))(?![\\w]))",
                    "end": "(:)|(=)|(;)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp strong"
                        },
                        "2": {
                            "name": "support.type strong"
                        },
                        "3": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "match": `(?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*)`,
                            "name": "markup.inserted constant.language support.constant.property-value entity.name.filename emphasis"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": "(;)|(=)|(?=\\})",
                    "endCaptures": {
                        "1": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        },
                        "2": {
                            "name": "support.type strong"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                },
                {
                    "begin": "(?:(?<=(?:[^#\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\-:!?.@*/&%^+<=>|~$]))",
                    "end": ";|(?=\\})",
                    "endCaptures": {
                        "0": {
                            "name": "variable.other.class.js message.error variable.interpolation string.regexp"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#type"
                        }
                    ]
                }
            ]
        },
        "variableModule": {
            "captures": {
                "0": {
                    "name": "string.other.link variable.language variable.parameter emphasis"
                }
            },
            "match": "(?:\\b(?=[A-Z])[A-Za-z_][\\w']*)"
        },
        "variablePattern": {
            "captures": {
                "1": {
                    "name": "comment constant.regexp meta.separator.markdown"
                },
                "2": {
                    "name": "string.other.link variable.language variable.parameter emphasis"
                }
            },
            "match": `(\\b_\\b)|((?:(?!\\b(?:and|'|as|asr|assert|\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\{|\\(|\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\+|private|\\?|"|rec|\\\\|\\}|\\)|\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\||virtual|when|while|with)\\b(?:[^']|$))\\b(?=[a-z_])[A-Za-z_][\\w']*))`
        }
    },
    "scopeName": "source.ocaml"
});
var ocaml = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_ocaml_mjs_32ac0a68._.js.map