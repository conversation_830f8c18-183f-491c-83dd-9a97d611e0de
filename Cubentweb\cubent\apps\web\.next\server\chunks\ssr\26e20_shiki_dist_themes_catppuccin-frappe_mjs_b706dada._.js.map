{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/catppuccin-frappe.mjs"], "sourcesContent": ["var catppuccinFrappe = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#00000000\",\n    \"activityBar.activeBorder\": \"#00000000\",\n    \"activityBar.activeFocusBorder\": \"#00000000\",\n    \"activityBar.background\": \"#232634\",\n    \"activityBar.border\": \"#00000000\",\n    \"activityBar.dropBorder\": \"#ca9ee633\",\n    \"activityBar.foreground\": \"#ca9ee6\",\n    \"activityBar.inactiveForeground\": \"#737994\",\n    \"activityBarBadge.background\": \"#ca9ee6\",\n    \"activityBarBadge.foreground\": \"#232634\",\n    \"activityBarTop.activeBorder\": \"#00000000\",\n    \"activityBarTop.dropBorder\": \"#ca9ee633\",\n    \"activityBarTop.foreground\": \"#ca9ee6\",\n    \"activityBarTop.inactiveForeground\": \"#737994\",\n    \"badge.background\": \"#51576d\",\n    \"badge.foreground\": \"#c6d0f5\",\n    \"banner.background\": \"#51576d\",\n    \"banner.foreground\": \"#c6d0f5\",\n    \"banner.iconForeground\": \"#c6d0f5\",\n    \"breadcrumb.activeSelectionForeground\": \"#ca9ee6\",\n    \"breadcrumb.background\": \"#303446\",\n    \"breadcrumb.focusForeground\": \"#ca9ee6\",\n    \"breadcrumb.foreground\": \"#c6d0f5cc\",\n    \"breadcrumbPicker.background\": \"#292c3c\",\n    \"button.background\": \"#ca9ee6\",\n    \"button.border\": \"#00000000\",\n    \"button.foreground\": \"#232634\",\n    \"button.hoverBackground\": \"#d9baed\",\n    \"button.secondaryBackground\": \"#626880\",\n    \"button.secondaryBorder\": \"#ca9ee6\",\n    \"button.secondaryForeground\": \"#c6d0f5\",\n    \"button.secondaryHoverBackground\": \"#727993\",\n    \"button.separator\": \"#00000000\",\n    \"charts.blue\": \"#8caaee\",\n    \"charts.foreground\": \"#c6d0f5\",\n    \"charts.green\": \"#a6d189\",\n    \"charts.lines\": \"#b5bfe2\",\n    \"charts.orange\": \"#ef9f76\",\n    \"charts.purple\": \"#ca9ee6\",\n    \"charts.red\": \"#e78284\",\n    \"charts.yellow\": \"#e5c890\",\n    \"checkbox.background\": \"#51576d\",\n    \"checkbox.border\": \"#00000000\",\n    \"checkbox.foreground\": \"#ca9ee6\",\n    \"commandCenter.activeBackground\": \"#62688033\",\n    \"commandCenter.activeBorder\": \"#ca9ee6\",\n    \"commandCenter.activeForeground\": \"#ca9ee6\",\n    \"commandCenter.background\": \"#292c3c\",\n    \"commandCenter.border\": \"#00000000\",\n    \"commandCenter.foreground\": \"#b5bfe2\",\n    \"commandCenter.inactiveBorder\": \"#00000000\",\n    \"commandCenter.inactiveForeground\": \"#b5bfe2\",\n    \"debugConsole.errorForeground\": \"#e78284\",\n    \"debugConsole.infoForeground\": \"#8caaee\",\n    \"debugConsole.sourceForeground\": \"#f2d5cf\",\n    \"debugConsole.warningForeground\": \"#ef9f76\",\n    \"debugConsoleInputIcon.foreground\": \"#c6d0f5\",\n    \"debugExceptionWidget.background\": \"#232634\",\n    \"debugExceptionWidget.border\": \"#ca9ee6\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#626880\",\n    \"debugIcon.breakpointDisabledForeground\": \"#e7828499\",\n    \"debugIcon.breakpointForeground\": \"#e78284\",\n    \"debugIcon.breakpointStackframeForeground\": \"#626880\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#a57582\",\n    \"debugIcon.continueForeground\": \"#a6d189\",\n    \"debugIcon.disconnectForeground\": \"#626880\",\n    \"debugIcon.pauseForeground\": \"#8caaee\",\n    \"debugIcon.restartForeground\": \"#81c8be\",\n    \"debugIcon.startForeground\": \"#a6d189\",\n    \"debugIcon.stepBackForeground\": \"#626880\",\n    \"debugIcon.stepIntoForeground\": \"#c6d0f5\",\n    \"debugIcon.stepOutForeground\": \"#c6d0f5\",\n    \"debugIcon.stepOverForeground\": \"#ca9ee6\",\n    \"debugIcon.stopForeground\": \"#e78284\",\n    \"debugTokenExpression.boolean\": \"#ca9ee6\",\n    \"debugTokenExpression.error\": \"#e78284\",\n    \"debugTokenExpression.number\": \"#ef9f76\",\n    \"debugTokenExpression.string\": \"#a6d189\",\n    \"debugToolBar.background\": \"#232634\",\n    \"debugToolBar.border\": \"#00000000\",\n    \"descriptionForeground\": \"#c6d0f5\",\n    \"diffEditor.border\": \"#626880\",\n    \"diffEditor.diagonalFill\": \"#62688099\",\n    \"diffEditor.insertedLineBackground\": \"#a6d18926\",\n    \"diffEditor.insertedTextBackground\": \"#a6d1891a\",\n    \"diffEditor.removedLineBackground\": \"#e7828426\",\n    \"diffEditor.removedTextBackground\": \"#e782841a\",\n    \"diffEditorOverview.insertedForeground\": \"#a6d189cc\",\n    \"diffEditorOverview.removedForeground\": \"#e78284cc\",\n    \"disabledForeground\": \"#a5adce\",\n    \"dropdown.background\": \"#292c3c\",\n    \"dropdown.border\": \"#ca9ee6\",\n    \"dropdown.foreground\": \"#c6d0f5\",\n    \"dropdown.listBackground\": \"#626880\",\n    \"editor.background\": \"#303446\",\n    \"editor.findMatchBackground\": \"#674b59\",\n    \"editor.findMatchBorder\": \"#e7828433\",\n    \"editor.findMatchHighlightBackground\": \"#506373\",\n    \"editor.findMatchHighlightBorder\": \"#99d1db33\",\n    \"editor.findRangeHighlightBackground\": \"#506373\",\n    \"editor.findRangeHighlightBorder\": \"#99d1db33\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#a6d18926\",\n    \"editor.foldBackground\": \"#99d1db40\",\n    \"editor.foreground\": \"#c6d0f5\",\n    \"editor.hoverHighlightBackground\": \"#99d1db40\",\n    \"editor.lineHighlightBackground\": \"#c6d0f512\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#99d1db40\",\n    \"editor.rangeHighlightBorder\": \"#00000000\",\n    \"editor.selectionBackground\": \"#949cbb40\",\n    \"editor.selectionHighlightBackground\": \"#949cbb33\",\n    \"editor.selectionHighlightBorder\": \"#949cbb33\",\n    \"editor.stackFrameHighlightBackground\": \"#e5c89026\",\n    \"editor.wordHighlightBackground\": \"#949cbb33\",\n    \"editorBracketHighlight.foreground1\": \"#e78284\",\n    \"editorBracketHighlight.foreground2\": \"#ef9f76\",\n    \"editorBracketHighlight.foreground3\": \"#e5c890\",\n    \"editorBracketHighlight.foreground4\": \"#a6d189\",\n    \"editorBracketHighlight.foreground5\": \"#85c1dc\",\n    \"editorBracketHighlight.foreground6\": \"#ca9ee6\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#ea999c\",\n    \"editorBracketMatch.background\": \"#949cbb1a\",\n    \"editorBracketMatch.border\": \"#949cbb\",\n    \"editorCodeLens.foreground\": \"#838ba7\",\n    \"editorCursor.background\": \"#303446\",\n    \"editorCursor.foreground\": \"#f2d5cf\",\n    \"editorError.background\": \"#00000000\",\n    \"editorError.border\": \"#00000000\",\n    \"editorError.foreground\": \"#e78284\",\n    \"editorGroup.border\": \"#626880\",\n    \"editorGroup.dropBackground\": \"#ca9ee633\",\n    \"editorGroup.emptyBackground\": \"#303446\",\n    \"editorGroupHeader.tabsBackground\": \"#232634\",\n    \"editorGutter.addedBackground\": \"#a6d189\",\n    \"editorGutter.background\": \"#303446\",\n    \"editorGutter.commentGlyphForeground\": \"#ca9ee6\",\n    \"editorGutter.commentRangeForeground\": \"#414559\",\n    \"editorGutter.deletedBackground\": \"#e78284\",\n    \"editorGutter.foldingControlForeground\": \"#949cbb\",\n    \"editorGutter.modifiedBackground\": \"#e5c890\",\n    \"editorHoverWidget.background\": \"#292c3c\",\n    \"editorHoverWidget.border\": \"#626880\",\n    \"editorHoverWidget.foreground\": \"#c6d0f5\",\n    \"editorIndentGuide.activeBackground\": \"#626880\",\n    \"editorIndentGuide.background\": \"#51576d\",\n    \"editorInfo.background\": \"#00000000\",\n    \"editorInfo.border\": \"#00000000\",\n    \"editorInfo.foreground\": \"#8caaee\",\n    \"editorInlayHint.background\": \"#292c3cbf\",\n    \"editorInlayHint.foreground\": \"#626880\",\n    \"editorInlayHint.parameterBackground\": \"#292c3cbf\",\n    \"editorInlayHint.parameterForeground\": \"#a5adce\",\n    \"editorInlayHint.typeBackground\": \"#292c3cbf\",\n    \"editorInlayHint.typeForeground\": \"#b5bfe2\",\n    \"editorLightBulb.foreground\": \"#e5c890\",\n    \"editorLineNumber.activeForeground\": \"#ca9ee6\",\n    \"editorLineNumber.foreground\": \"#838ba7\",\n    \"editorLink.activeForeground\": \"#ca9ee6\",\n    \"editorMarkerNavigation.background\": \"#292c3c\",\n    \"editorMarkerNavigationError.background\": \"#e78284\",\n    \"editorMarkerNavigationInfo.background\": \"#8caaee\",\n    \"editorMarkerNavigationWarning.background\": \"#ef9f76\",\n    \"editorOverviewRuler.background\": \"#292c3c\",\n    \"editorOverviewRuler.border\": \"#c6d0f512\",\n    \"editorOverviewRuler.modifiedForeground\": \"#e5c890\",\n    \"editorRuler.foreground\": \"#626880\",\n    \"editorStickyScrollHover.background\": \"#414559\",\n    \"editorSuggestWidget.background\": \"#292c3c\",\n    \"editorSuggestWidget.border\": \"#626880\",\n    \"editorSuggestWidget.foreground\": \"#c6d0f5\",\n    \"editorSuggestWidget.highlightForeground\": \"#ca9ee6\",\n    \"editorSuggestWidget.selectedBackground\": \"#414559\",\n    \"editorWarning.background\": \"#00000000\",\n    \"editorWarning.border\": \"#00000000\",\n    \"editorWarning.foreground\": \"#ef9f76\",\n    \"editorWhitespace.foreground\": \"#949cbb66\",\n    \"editorWidget.background\": \"#292c3c\",\n    \"editorWidget.foreground\": \"#c6d0f5\",\n    \"editorWidget.resizeBorder\": \"#626880\",\n    \"errorForeground\": \"#e78284\",\n    \"errorLens.errorBackground\": \"#e7828426\",\n    \"errorLens.errorBackgroundLight\": \"#e7828426\",\n    \"errorLens.errorForeground\": \"#e78284\",\n    \"errorLens.errorForegroundLight\": \"#e78284\",\n    \"errorLens.errorMessageBackground\": \"#e7828426\",\n    \"errorLens.hintBackground\": \"#a6d18926\",\n    \"errorLens.hintBackgroundLight\": \"#a6d18926\",\n    \"errorLens.hintForeground\": \"#a6d189\",\n    \"errorLens.hintForegroundLight\": \"#a6d189\",\n    \"errorLens.hintMessageBackground\": \"#a6d18926\",\n    \"errorLens.infoBackground\": \"#8caaee26\",\n    \"errorLens.infoBackgroundLight\": \"#8caaee26\",\n    \"errorLens.infoForeground\": \"#8caaee\",\n    \"errorLens.infoForegroundLight\": \"#8caaee\",\n    \"errorLens.infoMessageBackground\": \"#8caaee26\",\n    \"errorLens.statusBarErrorForeground\": \"#e78284\",\n    \"errorLens.statusBarHintForeground\": \"#a6d189\",\n    \"errorLens.statusBarIconErrorForeground\": \"#e78284\",\n    \"errorLens.statusBarIconWarningForeground\": \"#ef9f76\",\n    \"errorLens.statusBarInfoForeground\": \"#8caaee\",\n    \"errorLens.statusBarWarningForeground\": \"#ef9f76\",\n    \"errorLens.warningBackground\": \"#ef9f7626\",\n    \"errorLens.warningBackgroundLight\": \"#ef9f7626\",\n    \"errorLens.warningForeground\": \"#ef9f76\",\n    \"errorLens.warningForegroundLight\": \"#ef9f76\",\n    \"errorLens.warningMessageBackground\": \"#ef9f7626\",\n    \"extensionBadge.remoteBackground\": \"#8caaee\",\n    \"extensionBadge.remoteForeground\": \"#232634\",\n    \"extensionButton.prominentBackground\": \"#ca9ee6\",\n    \"extensionButton.prominentForeground\": \"#232634\",\n    \"extensionButton.prominentHoverBackground\": \"#d9baed\",\n    \"extensionButton.separator\": \"#303446\",\n    \"extensionIcon.preReleaseForeground\": \"#626880\",\n    \"extensionIcon.sponsorForeground\": \"#f4b8e4\",\n    \"extensionIcon.starForeground\": \"#e5c890\",\n    \"extensionIcon.verifiedForeground\": \"#a6d189\",\n    \"focusBorder\": \"#ca9ee6\",\n    \"foreground\": \"#c6d0f5\",\n    \"gitDecoration.addedResourceForeground\": \"#a6d189\",\n    \"gitDecoration.conflictingResourceForeground\": \"#ca9ee6\",\n    \"gitDecoration.deletedResourceForeground\": \"#e78284\",\n    \"gitDecoration.ignoredResourceForeground\": \"#737994\",\n    \"gitDecoration.modifiedResourceForeground\": \"#e5c890\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#e78284\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#e5c890\",\n    \"gitDecoration.submoduleResourceForeground\": \"#8caaee\",\n    \"gitDecoration.untrackedResourceForeground\": \"#a6d189\",\n    \"gitlens.closedAutolinkedIssueIconColor\": \"#ca9ee6\",\n    \"gitlens.closedPullRequestIconColor\": \"#e78284\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#a6d189\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#ef9f76\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#e5c890\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#ef9f76\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#a6d189\",\n    \"gitlens.decorations.statusMergingOrRebasingConflictForegroundColor\": \"#ea999c\",\n    \"gitlens.decorations.statusMergingOrRebasingForegroundColor\": \"#e5c890\",\n    \"gitlens.decorations.workspaceCurrentForegroundColor\": \"#ca9ee6\",\n    \"gitlens.decorations.workspaceRepoMissingForegroundColor\": \"#a5adce\",\n    \"gitlens.decorations.workspaceRepoOpenForegroundColor\": \"#ca9ee6\",\n    \"gitlens.decorations.worktreeHasUncommittedChangesForegroundColor\": \"#ef9f76\",\n    \"gitlens.decorations.worktreeMissingForegroundColor\": \"#ea999c\",\n    \"gitlens.graphChangesColumnAddedColor\": \"#a6d189\",\n    \"gitlens.graphChangesColumnDeletedColor\": \"#e78284\",\n    \"gitlens.graphLane10Color\": \"#f4b8e4\",\n    \"gitlens.graphLane1Color\": \"#ca9ee6\",\n    \"gitlens.graphLane2Color\": \"#e5c890\",\n    \"gitlens.graphLane3Color\": \"#8caaee\",\n    \"gitlens.graphLane4Color\": \"#eebebe\",\n    \"gitlens.graphLane5Color\": \"#a6d189\",\n    \"gitlens.graphLane6Color\": \"#babbf1\",\n    \"gitlens.graphLane7Color\": \"#f2d5cf\",\n    \"gitlens.graphLane8Color\": \"#e78284\",\n    \"gitlens.graphLane9Color\": \"#81c8be\",\n    \"gitlens.graphMinimapMarkerHeadColor\": \"#a6d189\",\n    \"gitlens.graphMinimapMarkerHighlightsColor\": \"#e5c890\",\n    \"gitlens.graphMinimapMarkerLocalBranchesColor\": \"#8caaee\",\n    \"gitlens.graphMinimapMarkerRemoteBranchesColor\": \"#769aeb\",\n    \"gitlens.graphMinimapMarkerStashesColor\": \"#ca9ee6\",\n    \"gitlens.graphMinimapMarkerTagsColor\": \"#eebebe\",\n    \"gitlens.graphMinimapMarkerUpstreamColor\": \"#98ca77\",\n    \"gitlens.graphScrollMarkerHeadColor\": \"#a6d189\",\n    \"gitlens.graphScrollMarkerHighlightsColor\": \"#e5c890\",\n    \"gitlens.graphScrollMarkerLocalBranchesColor\": \"#8caaee\",\n    \"gitlens.graphScrollMarkerRemoteBranchesColor\": \"#769aeb\",\n    \"gitlens.graphScrollMarkerStashesColor\": \"#ca9ee6\",\n    \"gitlens.graphScrollMarkerTagsColor\": \"#eebebe\",\n    \"gitlens.graphScrollMarkerUpstreamColor\": \"#98ca77\",\n    \"gitlens.gutterBackgroundColor\": \"#4145594d\",\n    \"gitlens.gutterForegroundColor\": \"#c6d0f5\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#ca9ee6\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#ca9ee626\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#ca9ee6cc\",\n    \"gitlens.mergedPullRequestIconColor\": \"#ca9ee6\",\n    \"gitlens.openAutolinkedIssueIconColor\": \"#a6d189\",\n    \"gitlens.openPullRequestIconColor\": \"#a6d189\",\n    \"gitlens.trailingLineBackgroundColor\": \"#00000000\",\n    \"gitlens.trailingLineForegroundColor\": \"#c6d0f54d\",\n    \"gitlens.unpublishedChangesIconColor\": \"#a6d189\",\n    \"gitlens.unpublishedCommitIconColor\": \"#a6d189\",\n    \"gitlens.unpulledChangesIconColor\": \"#ef9f76\",\n    \"icon.foreground\": \"#ca9ee6\",\n    \"input.background\": \"#414559\",\n    \"input.border\": \"#00000000\",\n    \"input.foreground\": \"#c6d0f5\",\n    \"input.placeholderForeground\": \"#c6d0f573\",\n    \"inputOption.activeBackground\": \"#626880\",\n    \"inputOption.activeBorder\": \"#ca9ee6\",\n    \"inputOption.activeForeground\": \"#c6d0f5\",\n    \"inputValidation.errorBackground\": \"#e78284\",\n    \"inputValidation.errorBorder\": \"#23263433\",\n    \"inputValidation.errorForeground\": \"#232634\",\n    \"inputValidation.infoBackground\": \"#8caaee\",\n    \"inputValidation.infoBorder\": \"#23263433\",\n    \"inputValidation.infoForeground\": \"#232634\",\n    \"inputValidation.warningBackground\": \"#ef9f76\",\n    \"inputValidation.warningBorder\": \"#23263433\",\n    \"inputValidation.warningForeground\": \"#232634\",\n    \"issues.closed\": \"#ca9ee6\",\n    \"issues.newIssueDecoration\": \"#f2d5cf\",\n    \"issues.open\": \"#a6d189\",\n    \"list.activeSelectionBackground\": \"#414559\",\n    \"list.activeSelectionForeground\": \"#c6d0f5\",\n    \"list.dropBackground\": \"#ca9ee633\",\n    \"list.focusAndSelectionBackground\": \"#51576d\",\n    \"list.focusBackground\": \"#414559\",\n    \"list.focusForeground\": \"#c6d0f5\",\n    \"list.focusOutline\": \"#00000000\",\n    \"list.highlightForeground\": \"#ca9ee6\",\n    \"list.hoverBackground\": \"#41455980\",\n    \"list.hoverForeground\": \"#c6d0f5\",\n    \"list.inactiveSelectionBackground\": \"#414559\",\n    \"list.inactiveSelectionForeground\": \"#c6d0f5\",\n    \"list.warningForeground\": \"#ef9f76\",\n    \"listFilterWidget.background\": \"#51576d\",\n    \"listFilterWidget.noMatchesOutline\": \"#e78284\",\n    \"listFilterWidget.outline\": \"#00000000\",\n    \"menu.background\": \"#303446\",\n    \"menu.border\": \"#30344680\",\n    \"menu.foreground\": \"#c6d0f5\",\n    \"menu.selectionBackground\": \"#626880\",\n    \"menu.selectionBorder\": \"#00000000\",\n    \"menu.selectionForeground\": \"#c6d0f5\",\n    \"menu.separatorBackground\": \"#626880\",\n    \"menubar.selectionBackground\": \"#51576d\",\n    \"menubar.selectionForeground\": \"#c6d0f5\",\n    \"merge.commonContentBackground\": \"#51576d\",\n    \"merge.commonHeaderBackground\": \"#626880\",\n    \"merge.currentContentBackground\": \"#a6d18933\",\n    \"merge.currentHeaderBackground\": \"#a6d18966\",\n    \"merge.incomingContentBackground\": \"#8caaee33\",\n    \"merge.incomingHeaderBackground\": \"#8caaee66\",\n    \"minimap.background\": \"#292c3c80\",\n    \"minimap.errorHighlight\": \"#e78284bf\",\n    \"minimap.findMatchHighlight\": \"#99d1db4d\",\n    \"minimap.selectionHighlight\": \"#626880bf\",\n    \"minimap.selectionOccurrenceHighlight\": \"#626880bf\",\n    \"minimap.warningHighlight\": \"#ef9f76bf\",\n    \"minimapGutter.addedBackground\": \"#a6d189bf\",\n    \"minimapGutter.deletedBackground\": \"#e78284bf\",\n    \"minimapGutter.modifiedBackground\": \"#e5c890bf\",\n    \"minimapSlider.activeBackground\": \"#ca9ee699\",\n    \"minimapSlider.background\": \"#ca9ee633\",\n    \"minimapSlider.hoverBackground\": \"#ca9ee666\",\n    \"notificationCenter.border\": \"#ca9ee6\",\n    \"notificationCenterHeader.background\": \"#292c3c\",\n    \"notificationCenterHeader.foreground\": \"#c6d0f5\",\n    \"notificationLink.foreground\": \"#8caaee\",\n    \"notificationToast.border\": \"#ca9ee6\",\n    \"notifications.background\": \"#292c3c\",\n    \"notifications.border\": \"#ca9ee6\",\n    \"notifications.foreground\": \"#c6d0f5\",\n    \"notificationsErrorIcon.foreground\": \"#e78284\",\n    \"notificationsInfoIcon.foreground\": \"#8caaee\",\n    \"notificationsWarningIcon.foreground\": \"#ef9f76\",\n    \"panel.background\": \"#303446\",\n    \"panel.border\": \"#626880\",\n    \"panelSection.border\": \"#626880\",\n    \"panelSection.dropBackground\": \"#ca9ee633\",\n    \"panelTitle.activeBorder\": \"#ca9ee6\",\n    \"panelTitle.activeForeground\": \"#c6d0f5\",\n    \"panelTitle.inactiveForeground\": \"#a5adce\",\n    \"peekView.border\": \"#ca9ee6\",\n    \"peekViewEditor.background\": \"#292c3c\",\n    \"peekViewEditor.matchHighlightBackground\": \"#99d1db4d\",\n    \"peekViewEditor.matchHighlightBorder\": \"#00000000\",\n    \"peekViewEditorGutter.background\": \"#292c3c\",\n    \"peekViewResult.background\": \"#292c3c\",\n    \"peekViewResult.fileForeground\": \"#c6d0f5\",\n    \"peekViewResult.lineForeground\": \"#c6d0f5\",\n    \"peekViewResult.matchHighlightBackground\": \"#99d1db4d\",\n    \"peekViewResult.selectionBackground\": \"#414559\",\n    \"peekViewResult.selectionForeground\": \"#c6d0f5\",\n    \"peekViewTitle.background\": \"#303446\",\n    \"peekViewTitleDescription.foreground\": \"#b5bfe2b3\",\n    \"peekViewTitleLabel.foreground\": \"#c6d0f5\",\n    \"pickerGroup.border\": \"#ca9ee6\",\n    \"pickerGroup.foreground\": \"#ca9ee6\",\n    \"problemsErrorIcon.foreground\": \"#e78284\",\n    \"problemsInfoIcon.foreground\": \"#8caaee\",\n    \"problemsWarningIcon.foreground\": \"#ef9f76\",\n    \"progressBar.background\": \"#ca9ee6\",\n    \"pullRequests.closed\": \"#e78284\",\n    \"pullRequests.draft\": \"#949cbb\",\n    \"pullRequests.merged\": \"#ca9ee6\",\n    \"pullRequests.notification\": \"#c6d0f5\",\n    \"pullRequests.open\": \"#a6d189\",\n    \"sash.hoverBorder\": \"#ca9ee6\",\n    \"scrollbar.shadow\": \"#232634\",\n    \"scrollbarSlider.activeBackground\": \"#41455966\",\n    \"scrollbarSlider.background\": \"#62688080\",\n    \"scrollbarSlider.hoverBackground\": \"#737994\",\n    \"selection.background\": \"#ca9ee666\",\n    \"settings.dropdownBackground\": \"#51576d\",\n    \"settings.dropdownListBorder\": \"#00000000\",\n    \"settings.focusedRowBackground\": \"#62688033\",\n    \"settings.headerForeground\": \"#c6d0f5\",\n    \"settings.modifiedItemIndicator\": \"#ca9ee6\",\n    \"settings.numberInputBackground\": \"#51576d\",\n    \"settings.numberInputBorder\": \"#00000000\",\n    \"settings.textInputBackground\": \"#51576d\",\n    \"settings.textInputBorder\": \"#00000000\",\n    \"sideBar.background\": \"#292c3c\",\n    \"sideBar.border\": \"#00000000\",\n    \"sideBar.dropBackground\": \"#ca9ee633\",\n    \"sideBar.foreground\": \"#c6d0f5\",\n    \"sideBarSectionHeader.background\": \"#292c3c\",\n    \"sideBarSectionHeader.foreground\": \"#c6d0f5\",\n    \"sideBarTitle.foreground\": \"#ca9ee6\",\n    \"statusBar.background\": \"#232634\",\n    \"statusBar.border\": \"#00000000\",\n    \"statusBar.debuggingBackground\": \"#ef9f76\",\n    \"statusBar.debuggingBorder\": \"#00000000\",\n    \"statusBar.debuggingForeground\": \"#232634\",\n    \"statusBar.foreground\": \"#c6d0f5\",\n    \"statusBar.noFolderBackground\": \"#232634\",\n    \"statusBar.noFolderBorder\": \"#00000000\",\n    \"statusBar.noFolderForeground\": \"#c6d0f5\",\n    \"statusBarItem.activeBackground\": \"#62688066\",\n    \"statusBarItem.errorBackground\": \"#00000000\",\n    \"statusBarItem.errorForeground\": \"#e78284\",\n    \"statusBarItem.hoverBackground\": \"#62688033\",\n    \"statusBarItem.prominentBackground\": \"#00000000\",\n    \"statusBarItem.prominentForeground\": \"#ca9ee6\",\n    \"statusBarItem.prominentHoverBackground\": \"#62688033\",\n    \"statusBarItem.remoteBackground\": \"#8caaee\",\n    \"statusBarItem.remoteForeground\": \"#232634\",\n    \"statusBarItem.warningBackground\": \"#00000000\",\n    \"statusBarItem.warningForeground\": \"#ef9f76\",\n    \"symbolIcon.arrayForeground\": \"#ef9f76\",\n    \"symbolIcon.booleanForeground\": \"#ca9ee6\",\n    \"symbolIcon.classForeground\": \"#e5c890\",\n    \"symbolIcon.colorForeground\": \"#f4b8e4\",\n    \"symbolIcon.constantForeground\": \"#ef9f76\",\n    \"symbolIcon.constructorForeground\": \"#babbf1\",\n    \"symbolIcon.enumeratorForeground\": \"#e5c890\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#e5c890\",\n    \"symbolIcon.eventForeground\": \"#f4b8e4\",\n    \"symbolIcon.fieldForeground\": \"#c6d0f5\",\n    \"symbolIcon.fileForeground\": \"#ca9ee6\",\n    \"symbolIcon.folderForeground\": \"#ca9ee6\",\n    \"symbolIcon.functionForeground\": \"#8caaee\",\n    \"symbolIcon.interfaceForeground\": \"#e5c890\",\n    \"symbolIcon.keyForeground\": \"#81c8be\",\n    \"symbolIcon.keywordForeground\": \"#ca9ee6\",\n    \"symbolIcon.methodForeground\": \"#8caaee\",\n    \"symbolIcon.moduleForeground\": \"#c6d0f5\",\n    \"symbolIcon.namespaceForeground\": \"#e5c890\",\n    \"symbolIcon.nullForeground\": \"#ea999c\",\n    \"symbolIcon.numberForeground\": \"#ef9f76\",\n    \"symbolIcon.objectForeground\": \"#e5c890\",\n    \"symbolIcon.operatorForeground\": \"#81c8be\",\n    \"symbolIcon.packageForeground\": \"#eebebe\",\n    \"symbolIcon.propertyForeground\": \"#ea999c\",\n    \"symbolIcon.referenceForeground\": \"#e5c890\",\n    \"symbolIcon.snippetForeground\": \"#eebebe\",\n    \"symbolIcon.stringForeground\": \"#a6d189\",\n    \"symbolIcon.structForeground\": \"#81c8be\",\n    \"symbolIcon.textForeground\": \"#c6d0f5\",\n    \"symbolIcon.typeParameterForeground\": \"#ea999c\",\n    \"symbolIcon.unitForeground\": \"#c6d0f5\",\n    \"symbolIcon.variableForeground\": \"#c6d0f5\",\n    \"tab.activeBackground\": \"#303446\",\n    \"tab.activeBorder\": \"#00000000\",\n    \"tab.activeBorderTop\": \"#ca9ee6\",\n    \"tab.activeForeground\": \"#ca9ee6\",\n    \"tab.activeModifiedBorder\": \"#e5c890\",\n    \"tab.border\": \"#292c3c\",\n    \"tab.hoverBackground\": \"#3a3f55\",\n    \"tab.hoverBorder\": \"#00000000\",\n    \"tab.hoverForeground\": \"#ca9ee6\",\n    \"tab.inactiveBackground\": \"#292c3c\",\n    \"tab.inactiveForeground\": \"#737994\",\n    \"tab.inactiveModifiedBorder\": \"#e5c8904d\",\n    \"tab.lastPinnedBorder\": \"#ca9ee6\",\n    \"tab.unfocusedActiveBackground\": \"#292c3c\",\n    \"tab.unfocusedActiveBorder\": \"#00000000\",\n    \"tab.unfocusedActiveBorderTop\": \"#ca9ee64d\",\n    \"tab.unfocusedInactiveBackground\": \"#1f212d\",\n    \"table.headerBackground\": \"#414559\",\n    \"table.headerForeground\": \"#c6d0f5\",\n    \"terminal.ansiBlack\": \"#a5adce\",\n    \"terminal.ansiBlue\": \"#8caaee\",\n    \"terminal.ansiBrightBlack\": \"#626880\",\n    \"terminal.ansiBrightBlue\": \"#8caaee\",\n    \"terminal.ansiBrightCyan\": \"#99d1db\",\n    \"terminal.ansiBrightGreen\": \"#a6d189\",\n    \"terminal.ansiBrightMagenta\": \"#f4b8e4\",\n    \"terminal.ansiBrightRed\": \"#e78284\",\n    \"terminal.ansiBrightWhite\": \"#51576d\",\n    \"terminal.ansiBrightYellow\": \"#e5c890\",\n    \"terminal.ansiCyan\": \"#99d1db\",\n    \"terminal.ansiGreen\": \"#a6d189\",\n    \"terminal.ansiMagenta\": \"#f4b8e4\",\n    \"terminal.ansiRed\": \"#e78284\",\n    \"terminal.ansiWhite\": \"#b5bfe2\",\n    \"terminal.ansiYellow\": \"#e5c890\",\n    \"terminal.border\": \"#626880\",\n    \"terminal.dropBackground\": \"#ca9ee633\",\n    \"terminal.foreground\": \"#c6d0f5\",\n    \"terminal.inactiveSelectionBackground\": \"#62688080\",\n    \"terminal.selectionBackground\": \"#626880\",\n    \"terminal.tab.activeBorder\": \"#ca9ee6\",\n    \"terminalCommandDecoration.defaultBackground\": \"#626880\",\n    \"terminalCommandDecoration.errorBackground\": \"#e78284\",\n    \"terminalCommandDecoration.successBackground\": \"#a6d189\",\n    \"terminalCursor.background\": \"#303446\",\n    \"terminalCursor.foreground\": \"#f2d5cf\",\n    \"textBlockQuote.background\": \"#292c3c\",\n    \"textBlockQuote.border\": \"#232634\",\n    \"textCodeBlock.background\": \"#303446\",\n    \"textLink.activeForeground\": \"#99d1db\",\n    \"textLink.foreground\": \"#8caaee\",\n    \"textPreformat.foreground\": \"#c6d0f5\",\n    \"textSeparator.foreground\": \"#ca9ee6\",\n    \"titleBar.activeBackground\": \"#232634\",\n    \"titleBar.activeForeground\": \"#c6d0f5\",\n    \"titleBar.border\": \"#00000000\",\n    \"titleBar.inactiveBackground\": \"#232634\",\n    \"titleBar.inactiveForeground\": \"#c6d0f580\",\n    \"tree.inactiveIndentGuidesStroke\": \"#51576d\",\n    \"tree.indentGuidesStroke\": \"#949cbb\",\n    \"walkThrough.embeddedEditorBackground\": \"#3034464d\",\n    \"welcomePage.progress.background\": \"#232634\",\n    \"welcomePage.progress.foreground\": \"#ca9ee6\",\n    \"welcomePage.tileBackground\": \"#292c3c\",\n    \"widget.shadow\": \"#292c3c80\",\n    \"window.activeBorder\": \"#00000000\",\n    \"window.inactiveBorder\": \"#00000000\"\n  },\n  \"displayName\": \"Catppuccin Frapp\\xE9\",\n  \"name\": \"catppuccin-frappe\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"boolean\": {\n      \"foreground\": \"#ef9f76\"\n    },\n    \"builtinAttribute.attribute.library:rust\": {\n      \"foreground\": \"#8caaee\"\n    },\n    \"class.builtin:python\": {\n      \"foreground\": \"#ca9ee6\"\n    },\n    \"class:python\": {\n      \"foreground\": \"#e5c890\"\n    },\n    \"constant.builtin.readonly:nix\": {\n      \"foreground\": \"#ca9ee6\"\n    },\n    \"enumMember\": {\n      \"foreground\": \"#81c8be\"\n    },\n    \"function.decorator:python\": {\n      \"foreground\": \"#ef9f76\"\n    },\n    \"generic.attribute:rust\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"heading\": {\n      \"foreground\": \"#e78284\"\n    },\n    \"number\": {\n      \"foreground\": \"#ef9f76\"\n    },\n    \"pol\": {\n      \"foreground\": \"#eebebe\"\n    },\n    \"property.readonly:javascript\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"property.readonly:javascriptreact\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"property.readonly:typescript\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"property.readonly:typescriptreact\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"selfKeyword\": {\n      \"foreground\": \"#e78284\"\n    },\n    \"text.emph\": {\n      \"fontStyle\": \"italic\",\n      \"foreground\": \"#e78284\"\n    },\n    \"text.math\": {\n      \"foreground\": \"#eebebe\"\n    },\n    \"text.strong\": {\n      \"fontStyle\": \"bold\",\n      \"foreground\": \"#e78284\"\n    },\n    \"tomlArrayKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#8caaee\"\n    },\n    \"tomlTableKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#8caaee\"\n    },\n    \"type.defaultLibrary:go\": {\n      \"foreground\": \"#ca9ee6\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#ea999c\"\n    },\n    \"variable.readonly.defaultLibrary:go\": {\n      \"foreground\": \"#ca9ee6\"\n    },\n    \"variable.readonly:javascript\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"variable.readonly:javascriptreact\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"variable.readonly:scala\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"variable.readonly:typescript\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"variable.readonly:typescriptreact\": {\n      \"foreground\": \"#c6d0f5\"\n    },\n    \"variable.typeHint:python\": {\n      \"foreground\": \"#e5c890\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"text\",\n        \"source\",\n        \"variable.other.readwrite\",\n        \"punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#949cbb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#737994\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"variable.other.constant\",\n        \"entity.name.constant\",\n        \"constant.language.boolean\",\n        \"constant.language.false\",\n        \"constant.language.true\",\n        \"keyword.other.unit.user-defined\",\n        \"keyword.other.unit.suffix.floating-point\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"keyword.operator.word\",\n        \"keyword.operator.new\",\n        \"variable.language.super\",\n        \"support.type.primitive\",\n        \"storage.type\",\n        \"storage.modifier\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.documentation\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"punctuation.accessor\",\n        \"punctuation.definition.generic\",\n        \"meta.function.closure punctuation.section.parameters\",\n        \"punctuation.definition.tag\",\n        \"punctuation.separator.key-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.method\",\n        \"support.function\",\n        \"support.function.misc\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.other.inherited-class\",\n        \"support.class\",\n        \"meta.function-call.constructor\",\n        \"entity.name.struct\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.enum\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.enum variable.other.readwrite\",\n        \"variable.other.enummember\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"meta.property.object\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type\",\n        \"meta.type-alias\",\n        \"support.type\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation variable.function\",\n        \"meta.annotation variable.annotation.function\",\n        \"meta.annotation punctuation.definition.annotation\",\n        \"meta.decorator\",\n        \"punctuation.decorator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"meta.function.parameters\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"support.function.builtin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.documentation\",\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive\",\n        \"punctuation.definition.directive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.typeparameters\",\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.this\",\n        \"variable.language.this punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.template variable\",\n        \"string variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.new\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.specifier.extern.cpp\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.scope-resolution.template.call.cpp\",\n        \"entity.name.scope-resolution.parameter.cpp\",\n        \"entity.name.scope-resolution.cpp\",\n        \"entity.name.scope-resolution.function.definition.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.doxygen\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.reference.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"meta.interpolation.cs\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.documentation.cs\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css entity.other.attribute-name.class.css\",\n        \"entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.operator.css\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.other.attribute-name.pseudo-class\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"source.css constant.other.unicode-range\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"source.css variable.parameter.url\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.vendored.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-value variable\",\n        \"source.css meta.property-value variable.other.less\",\n        \"source.css meta.property-value variable.other.less punctuation.definition.variable.less\",\n        \"meta.definition.variable.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-list variable\",\n        \"meta.property-list variable.other.less\",\n        \"meta.property-list variable.other.less punctuation.definition.variable.less\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.percentage.css\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"source.css meta.attribute-selector\",\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.definition.ini\",\n        \"punctuation.support.type.property-name.json\",\n        \"support.type.property-name.json\",\n        \"punctuation.support.type.property-name.toml\",\n        \"support.type.property-name.toml\",\n        \"entity.name.tag.yaml\",\n        \"punctuation.support.type.property-name.yaml\",\n        \"support.type.property-name.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.json\",\n        \"constant.language.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.anchor.yaml\",\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name.table\",\n        \"entity.name.section.group-title.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.time.datetime.offset.toml\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.anchor.yaml\",\n        \"punctuation.definition.alias.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.document.begin.yaml\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.header.from-file\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.from-file.diff\",\n        \"punctuation.definition.to-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.go\",\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.parameters.go\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.go\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"variable.graphql\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#eebebe\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql\",\n      \"settings\": {\n        \"foreground\": \"#eebebe\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.doctype\",\n        \"meta.tag.sgml.doctype punctuation.definition.tag\",\n        \"meta.tag.metadata.doctype entity.name.tag\",\n        \"meta.tag.metadata.doctype punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html constant.character.entity\",\n        \"text.html constant.character.entity punctuation\",\n        \"constant.character.entity.xml\",\n        \"constant.character.entity.xml punctuation\",\n        \"constant.character.entity.js.jsx\",\n        \"constant.charactger.entity.js.jsx punctuation\",\n        \"constant.character.entity.tsx\",\n        \"constant.character.entity.tsx punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\",\n        \"support.class.component.jsx\",\n        \"support.class.component.tsx\",\n        \"support.class.component.vue\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.annotation\",\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.enum.java\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.javadoc.java keyword.other.documentation.javadoc.java\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"meta.export variable.other.readwrite.js\",\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.property.js\",\n        \"variable.other.property.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.jsdoc\",\n        \"comment.block.documentation variable.other\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.jsdoc\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console.js\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.node\",\n        \"support.type.object.module.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.implements\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.null.js\",\n        \"constant.language.null.ts\",\n        \"constant.language.undefined.js\",\n        \"constant.language.undefined.ts\",\n        \"support.type.builtin.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.generic\",\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.declaration.function.arrow.js\",\n        \"storage.type.function.arrow.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.decorator.ts\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.in.js\",\n        \"keyword.operator.expression.in.ts\",\n        \"keyword.operator.expression.infer.ts\",\n        \"keyword.operator.expression.instanceof.js\",\n        \"keyword.operator.expression.instanceof.ts\",\n        \"keyword.operator.expression.is\",\n        \"keyword.operator.expression.keyof.ts\",\n        \"keyword.operator.expression.of.js\",\n        \"keyword.operator.expression.of.ts\",\n        \"keyword.operator.expression.typeof.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.julia\",\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"text.tex keyword.control.preamble\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"text.tex support.function.be\",\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.general.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#eebebe\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-dash.documentation.lua storage.type.annotation.lua\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.double-dash.documentation.lua entity.name.variable.lua\",\n        \"comment.line.double-dash.documentation.lua variable.lua\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.1.markdown punctuation.definition.heading.markdown\",\n        \"heading.1.markdown\",\n        \"heading.1.quarto punctuation.definition.heading.quarto\",\n        \"heading.1.quarto\",\n        \"markup.heading.atx.1.mdx\",\n        \"markup.heading.atx.1.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.heading-0.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.2.markdown punctuation.definition.heading.markdown\",\n        \"heading.2.markdown\",\n        \"heading.2.quarto punctuation.definition.heading.quarto\",\n        \"heading.2.quarto\",\n        \"markup.heading.atx.2.mdx\",\n        \"markup.heading.atx.2.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.2.markdown\",\n        \"markup.heading.heading-1.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.3.markdown punctuation.definition.heading.markdown\",\n        \"heading.3.markdown\",\n        \"heading.3.quarto punctuation.definition.heading.quarto\",\n        \"heading.3.quarto\",\n        \"markup.heading.atx.3.mdx\",\n        \"markup.heading.atx.3.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-2.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.4.markdown punctuation.definition.heading.markdown\",\n        \"heading.4.markdown\",\n        \"heading.4.quarto punctuation.definition.heading.quarto\",\n        \"heading.4.quarto\",\n        \"markup.heading.atx.4.mdx\",\n        \"markup.heading.atx.4.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-3.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.5.markdown punctuation.definition.heading.markdown\",\n        \"heading.5.markdown\",\n        \"heading.5.quarto punctuation.definition.heading.quarto\",\n        \"heading.5.quarto\",\n        \"markup.heading.atx.5.mdx\",\n        \"markup.heading.atx.5.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-4.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.6.markdown punctuation.definition.heading.markdown\",\n        \"heading.6.markdown\",\n        \"heading.6.quarto punctuation.definition.heading.quarto\",\n        \"heading.6.quarto\",\n        \"markup.heading.atx.6.mdx\",\n        \"markup.heading.atx.6.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-5.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\",\n        \"foreground\": \"#a5adce\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.link\",\n        \"markup.underline.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.link.title\",\n        \"text.html.quarto punctuation.definition.link.title\",\n        \"string.other.link.title.markdown\",\n        \"string.other.link.title.quarto\",\n        \"markup.link\",\n        \"punctuation.definition.constant.markdown\",\n        \"punctuation.definition.constant.quarto\",\n        \"constant.other.reference.link.markdown\",\n        \"constant.other.reference.link.quarto\",\n        \"markup.substitution.attribute-reference\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#babbf1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.raw.markdown\",\n        \"punctuation.definition.raw.quarto\",\n        \"markup.inline.raw.string.markdown\",\n        \"markup.inline.raw.string.quarto\",\n        \"markup.raw.block.markdown\",\n        \"markup.raw.block.quarto\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block punctuation.definition\",\n        \"markup.raw support.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#949cbb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\",\n        \"punctuation.definition.quote.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin.markdown\",\n        \"punctuation.definition.list.begin.quarto\",\n        \"markup.list.bullet\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.quarto\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.multipart.nix\",\n        \"entity.other.attribute-name.single.nix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#babbf1\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.path.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.attribute.builtin\",\n        \"meta.attribute.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.php punctuation.definition.variable.php\",\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"text.html.php support.function\",\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.phpdoc.php\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.magic.python\",\n        \"meta.function-call.arguments.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\",\n        \"variable.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow.python\",\n        \"keyword.operator.logical.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.python\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.token.decorator.python\",\n        \"meta.function.decorator.identifier.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.decorator.python\",\n        \"punctuation.definition.decorator.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.exception.python\",\n        \"support.function.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.indexed-name.python\",\n        \"meta.item-access.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.string.python\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.python\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.anchor.regexp\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.ts\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\",\n        \"keyword.other.back-reference.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6d189\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.range.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f2d5cf\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.numeric.regexp\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.no-capture.regexp\",\n        \"meta.assertion.look-ahead.regexp\",\n        \"meta.assertion.negative-look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation.rust\",\n        \"meta.annotation.rust punctuation\",\n        \"meta.attribute.rust\",\n        \"punctuation.definition.attribute.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute.rust string.quoted.double.rust\",\n        \"meta.attribute.rust string.quoted.single.char.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.macro.rules.rust\",\n        \"storage.type.module.rust\",\n        \"storage.modifier.rust\",\n        \"storage.type.struct.rust\",\n        \"storage.type.enum.rust\",\n        \"storage.type.trait.rust\",\n        \"storage.type.union.rust\",\n        \"storage.type.impl.rust\",\n        \"storage.type.rust\",\n        \"storage.type.function.rust\",\n        \"storage.type.type.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.numeric.rust\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"meta.generic.rust\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.impl.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.trait.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.union.rust\",\n      \"settings\": {\n        \"foreground\": \"#e5c890\"\n      }\n    },\n    {\n      \"scope\": \"meta.enum.rust storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.macro.rust\",\n        \"meta.macro.rust support.function.rust\",\n        \"entity.name.function.macro.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.lifetime.rust\",\n        \"entity.name.type.lifetime\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.rust constant.other.placeholder.rust\",\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.return-type.rust meta.generic.rust storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust\",\n      \"settings\": {\n        \"foreground\": \"#8caaee\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.brackets.angle.rust\",\n      \"settings\": {\n        \"foreground\": \"#99d1db\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.caps.rust\",\n      \"settings\": {\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function.definition.rust variable.other.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea999c\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust variable.other.rust\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.self.rust\",\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.metavariable.name.rust\",\n        \"meta.macro.metavariable.rust keyword.operator.macro.dollar.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.shebang\",\n        \"comment.line.shebang punctuation.definition.comment\",\n        \"comment.line.shebang\",\n        \"punctuation.definition.comment.shebang.shell\",\n        \"meta.shebang.shell\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f4b8e4\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.shebang constant.language\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\",\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e78284\"\n      }\n    },\n    {\n      \"scope\": \"meta.string meta.interpolation.parameter.shell variable.other.readwrite\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ef9f76\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell punctuation.section.interpolation\",\n        \"punctuation.definition.evaluation.backticks.shell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#81c8be\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#ca9ee6\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell variable.other.normal.shell\",\n      \"settings\": {\n        \"foreground\": \"#c6d0f5\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { catppuccinFrappe as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,mBAAmB,OAAO,MAAM,CAAC;IACnC,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,qCAAqC;QACrC,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,8BAA8B;QAC9B,mCAAmC;QACnC,oBAAoB;QACpB,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,oCAAoC;QACpC,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,wCAAwC;QACxC,kCAAkC;QAClC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,0BAA0B;QAC1B,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,6BAA6B;QAC7B,kCAAkC;QAClC,6BAA6B;QAC7B,kCAAkC;QAClC,oCAAoC;QACpC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,sCAAsC;QACtC,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,qCAAqC;QACrC,wCAAwC;QACxC,+BAA+B;QAC/B,oCAAoC;QACpC,+BAA+B;QAC/B,oCAAoC;QACpC,sCAAsC;QACtC,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,6BAA6B;QAC7B,sCAAsC;QACtC,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,0CAA0C;QAC1C,sCAAsC;QACtC,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,uDAAuD;QACvD,2DAA2D;QAC3D,wDAAwD;QACxD,oEAAoE;QACpE,sDAAsD;QACtD,wCAAwC;QACxC,0CAA0C;QAC1C,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,uCAAuC;QACvC,6CAA6C;QAC7C,gDAAgD;QAChD,iDAAiD;QACjD,0CAA0C;QAC1C,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,4CAA4C;QAC5C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,sCAAsC;QACtC,0CAA0C;QAC1C,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,wCAAwC;QACxC,oCAAoC;QACpC,uCAAuC;QACvC,uCAAuC;QACvC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,6BAA6B;QAC7B,eAAe;QACf,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,oCAAoC;QACpC,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,wCAAwC;QACxC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,uBAAuB;QACvB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,uBAAuB;QACvB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,4BAA4B;QAC5B,sBAAsB;QACtB,kBAAkB;QAClB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,gCAAgC;QAChC,mCAAmC;QACnC,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,uBAAuB;QACvB,wCAAwC;QACxC,gCAAgC;QAChC,6BAA6B;QAC7B,+CAA+C;QAC/C,6CAA6C;QAC7C,+CAA+C;QAC/C,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wCAAwC;QACxC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,WAAW;YACT,cAAc;QAChB;QACA,2CAA2C;YACzC,cAAc;QAChB;QACA,wBAAwB;YACtB,cAAc;QAChB;QACA,gBAAgB;YACd,cAAc;QAChB;QACA,iCAAiC;YAC/B,cAAc;QAChB;QACA,cAAc;YACZ,cAAc;QAChB;QACA,6BAA6B;YAC3B,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,WAAW;YACT,cAAc;QAChB;QACA,UAAU;YACR,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,eAAe;YACb,cAAc;QAChB;QACA,aAAa;YACX,aAAa;YACb,cAAc;QAChB;QACA,aAAa;YACX,cAAc;QAChB;QACA,eAAe;YACb,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,uCAAuC;YACrC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,4BAA4B;YAC1B,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}