{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/one-dark-pro.mjs"], "sourcesContent": ["var oneDarkPro = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#282c34\",\n    \"activityBar.foreground\": \"#d7dae0\",\n    \"activityBarBadge.background\": \"#4d78cc\",\n    \"activityBarBadge.foreground\": \"#f8fafd\",\n    \"badge.background\": \"#282c34\",\n    \"button.background\": \"#404754\",\n    \"button.secondaryBackground\": \"#30333d\",\n    \"button.secondaryForeground\": \"#c0bdbd\",\n    \"checkbox.border\": \"#404754\",\n    \"debugToolBar.background\": \"#21252b\",\n    \"descriptionForeground\": \"#abb2bf\",\n    \"diffEditor.insertedTextBackground\": \"#00809b33\",\n    \"dropdown.background\": \"#21252b\",\n    \"dropdown.border\": \"#21252b\",\n    \"editor.background\": \"#282c34\",\n    \"editor.findMatchBackground\": \"#d19a6644\",\n    \"editor.findMatchBorder\": \"#ffffff5a\",\n    \"editor.findMatchHighlightBackground\": \"#ffffff22\",\n    \"editor.foreground\": \"#abb2bf\",\n    \"editor.lineHighlightBackground\": \"#2c313c\",\n    \"editor.selectionBackground\": \"#67769660\",\n    \"editor.selectionHighlightBackground\": \"#ffffff10\",\n    \"editor.selectionHighlightBorder\": \"#dddddd\",\n    \"editor.wordHighlightBackground\": \"#d2e0ff2f\",\n    \"editor.wordHighlightBorder\": \"#7f848e\",\n    \"editor.wordHighlightStrongBackground\": \"#abb2bf26\",\n    \"editor.wordHighlightStrongBorder\": \"#7f848e\",\n    \"editorBracketHighlight.foreground1\": \"#d19a66\",\n    \"editorBracketHighlight.foreground2\": \"#c678dd\",\n    \"editorBracketHighlight.foreground3\": \"#56b6c2\",\n    \"editorBracketMatch.background\": \"#515a6b\",\n    \"editorBracketMatch.border\": \"#515a6b\",\n    \"editorCursor.background\": \"#ffffffc9\",\n    \"editorCursor.foreground\": \"#528bff\",\n    \"editorError.foreground\": \"#c24038\",\n    \"editorGroup.background\": \"#181a1f\",\n    \"editorGroup.border\": \"#181a1f\",\n    \"editorGroupHeader.tabsBackground\": \"#21252b\",\n    \"editorGutter.addedBackground\": \"#109868\",\n    \"editorGutter.deletedBackground\": \"#9A353D\",\n    \"editorGutter.modifiedBackground\": \"#948B60\",\n    \"editorHoverWidget.background\": \"#21252b\",\n    \"editorHoverWidget.border\": \"#181a1f\",\n    \"editorHoverWidget.highlightForeground\": \"#61afef\",\n    \"editorIndentGuide.activeBackground\": \"#c8c8c859\",\n    \"editorIndentGuide.background\": \"#3b4048\",\n    \"editorInlayHint.background\": \"#2c313c\",\n    \"editorInlayHint.foreground\": \"#abb2bf\",\n    \"editorLineNumber.activeForeground\": \"#abb2bf\",\n    \"editorLineNumber.foreground\": \"#495162\",\n    \"editorMarkerNavigation.background\": \"#21252b\",\n    \"editorOverviewRuler.addedBackground\": \"#109868\",\n    \"editorOverviewRuler.deletedBackground\": \"#9A353D\",\n    \"editorOverviewRuler.modifiedBackground\": \"#948B60\",\n    \"editorRuler.foreground\": \"#abb2bf26\",\n    \"editorSuggestWidget.background\": \"#21252b\",\n    \"editorSuggestWidget.border\": \"#181a1f\",\n    \"editorSuggestWidget.selectedBackground\": \"#2c313a\",\n    \"editorWarning.foreground\": \"#d19a66\",\n    \"editorWhitespace.foreground\": \"#ffffff1d\",\n    \"editorWidget.background\": \"#21252b\",\n    \"focusBorder\": \"#3e4452\",\n    \"gitDecoration.ignoredResourceForeground\": \"#636b78\",\n    \"input.background\": \"#1d1f23\",\n    \"input.foreground\": \"#abb2bf\",\n    \"list.activeSelectionBackground\": \"#2c313a\",\n    \"list.activeSelectionForeground\": \"#d7dae0\",\n    \"list.focusBackground\": \"#323842\",\n    \"list.focusForeground\": \"#f0f0f0\",\n    \"list.highlightForeground\": \"#ecebeb\",\n    \"list.hoverBackground\": \"#2c313a\",\n    \"list.hoverForeground\": \"#abb2bf\",\n    \"list.inactiveSelectionBackground\": \"#323842\",\n    \"list.inactiveSelectionForeground\": \"#d7dae0\",\n    \"list.warningForeground\": \"#d19a66\",\n    \"menu.foreground\": \"#abb2bf\",\n    \"menu.separatorBackground\": \"#343a45\",\n    \"minimapGutter.addedBackground\": \"#109868\",\n    \"minimapGutter.deletedBackground\": \"#9A353D\",\n    \"minimapGutter.modifiedBackground\": \"#948B60\",\n    \"panel.border\": \"#3e4452\",\n    \"panelSectionHeader.background\": \"#21252b\",\n    \"peekViewEditor.background\": \"#1b1d23\",\n    \"peekViewEditor.matchHighlightBackground\": \"#29244b\",\n    \"peekViewResult.background\": \"#22262b\",\n    \"scrollbar.shadow\": \"#23252c\",\n    \"scrollbarSlider.activeBackground\": \"#747d9180\",\n    \"scrollbarSlider.background\": \"#4e566660\",\n    \"scrollbarSlider.hoverBackground\": \"#5a637580\",\n    \"settings.focusedRowBackground\": \"#282c34\",\n    \"settings.headerForeground\": \"#fff\",\n    \"sideBar.background\": \"#21252b\",\n    \"sideBar.foreground\": \"#abb2bf\",\n    \"sideBarSectionHeader.background\": \"#282c34\",\n    \"sideBarSectionHeader.foreground\": \"#abb2bf\",\n    \"statusBar.background\": \"#21252b\",\n    \"statusBar.debuggingBackground\": \"#cc6633\",\n    \"statusBar.debuggingBorder\": \"#ff000000\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.foreground\": \"#9da5b4\",\n    \"statusBar.noFolderBackground\": \"#21252b\",\n    \"statusBarItem.remoteBackground\": \"#4d78cc\",\n    \"statusBarItem.remoteForeground\": \"#f8fafd\",\n    \"tab.activeBackground\": \"#282c34\",\n    \"tab.activeBorder\": \"#b4b4b4\",\n    \"tab.activeForeground\": \"#dcdcdc\",\n    \"tab.border\": \"#181a1f\",\n    \"tab.hoverBackground\": \"#323842\",\n    \"tab.inactiveBackground\": \"#21252b\",\n    \"tab.unfocusedHoverBackground\": \"#323842\",\n    \"terminal.ansiBlack\": \"#3f4451\",\n    \"terminal.ansiBlue\": \"#4aa5f0\",\n    \"terminal.ansiBrightBlack\": \"#4f5666\",\n    \"terminal.ansiBrightBlue\": \"#4dc4ff\",\n    \"terminal.ansiBrightCyan\": \"#4cd1e0\",\n    \"terminal.ansiBrightGreen\": \"#a5e075\",\n    \"terminal.ansiBrightMagenta\": \"#de73ff\",\n    \"terminal.ansiBrightRed\": \"#ff616e\",\n    \"terminal.ansiBrightWhite\": \"#e6e6e6\",\n    \"terminal.ansiBrightYellow\": \"#f0a45d\",\n    \"terminal.ansiCyan\": \"#42b3c2\",\n    \"terminal.ansiGreen\": \"#8cc265\",\n    \"terminal.ansiMagenta\": \"#c162de\",\n    \"terminal.ansiRed\": \"#e05561\",\n    \"terminal.ansiWhite\": \"#d7dae0\",\n    \"terminal.ansiYellow\": \"#d18f52\",\n    \"terminal.background\": \"#282c34\",\n    \"terminal.border\": \"#3e4452\",\n    \"terminal.foreground\": \"#abb2bf\",\n    \"terminal.selectionBackground\": \"#abb2bf30\",\n    \"textBlockQuote.background\": \"#2e3440\",\n    \"textBlockQuote.border\": \"#4b5362\",\n    \"textLink.foreground\": \"#61afef\",\n    \"textPreformat.foreground\": \"#d19a66\",\n    \"titleBar.activeBackground\": \"#282c34\",\n    \"titleBar.activeForeground\": \"#9da5b4\",\n    \"titleBar.inactiveBackground\": \"#282c34\",\n    \"titleBar.inactiveForeground\": \"#6b717d\",\n    \"tree.indentGuidesStroke\": \"#ffffff1d\",\n    \"walkThrough.embeddedEditorBackground\": \"#2e3440\",\n    \"welcomePage.buttonHoverBackground\": \"#404754\"\n  },\n  \"displayName\": \"One Dark Pro\",\n  \"name\": \"one-dark-pro\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"annotation:dart\": {\n      \"foreground\": \"#d19a66\"\n    },\n    \"enumMember\": {\n      \"foreground\": \"#56b6c2\"\n    },\n    \"macro\": {\n      \"foreground\": \"#d19a66\"\n    },\n    \"memberOperatorOverload\": {\n      \"foreground\": \"#c678dd\"\n    },\n    \"parameter.label:dart\": {\n      \"foreground\": \"#abb2bf\"\n    },\n    \"property:dart\": {\n      \"foreground\": \"#d19a66\"\n    },\n    \"tomlArrayKey\": {\n      \"foreground\": \"#e5c07b\"\n    },\n    \"variable.constant\": {\n      \"foreground\": \"#d19a66\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#e5c07b\"\n    },\n    \"variable:dart\": {\n      \"foreground\": \"#d19a66\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": \"meta.embedded\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.delayed.unison,punctuation.definition.list.begin.unison,punctuation.definition.list.end.unison,punctuation.definition.ability.begin.unison,punctuation.definition.ability.end.unison,punctuation.operator.assignment.as.unison,punctuation.separator.pipe.unison,punctuation.separator.delimiter.unison,punctuation.definition.hash.unison\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.generic-type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.magic.python\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.period.python,punctuation.separator.element.python,punctuation.parenthesis.begin.python,punctuation.parenthesis.end.python\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.language.special.self.python\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.language.special.cls.python\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.lifetime.rust\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"support.function.std.rust\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.lifetime.rust\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.rust\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.edge\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.word\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"comment markup.link\",\n      \"settings\": {\n        \"foreground\": \"#5c6370\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header.from-file,meta.diff.header.to-file,punctuation.definition.from-file.diff,punctuation.definition.to-file.diff\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.c,meta.function.cpp\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.block.begin.bracket.curly.cpp,punctuation.section.block.end.bracket.curly.cpp,punctuation.terminator.statement.c,punctuation.section.block.begin.bracket.curly.c,punctuation.section.block.end.bracket.curly.c,punctuation.section.parens.begin.bracket.round.c,punctuation.section.parens.end.bracket.round.c,punctuation.section.parameters.begin.bracket.round.c,punctuation.section.parameters.end.bracket.round.c\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.expression.import\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.math\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property.math\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.annotation.java\",\n        \"storage.type.object.array.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"source.java\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.block.begin.java,punctuation.section.block.end.java,punctuation.definition.method-parameters.begin.java,punctuation.definition.method-parameters.end.java,meta.method.identifier.java,punctuation.section.method.begin.java,punctuation.section.method.end.java,punctuation.terminator.java,punctuation.section.class.begin.java,punctuation.section.class.end.java,punctuation.section.inner-class.begin.java,punctuation.section.inner-class.end.java,meta.method-call.java,punctuation.section.class.begin.bracket.curly.java,punctuation.section.class.end.bracket.curly.java,punctuation.section.method.begin.bracket.curly.java,punctuation.section.method.end.bracket.curly.java,punctuation.separator.period.java,punctuation.bracket.angle.java,punctuation.definition.annotation.java,meta.method.body.java\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"meta.method.java\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java,storage.type.java,storage.type.generic.java\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.instanceof.java\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.variable.name.java\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.bitwise\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.channel\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property-value.scss,support.constant.property-value.css\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.css,keyword.operator.scss,keyword.operator.less\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.color.w3c-standard-color-name.css,support.constant.color.w3c-standard-color-name.scss\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.list.comma.css\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.color.w3c-standard-color-name.css\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"support.type.vendored.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.module.node,support.type.object.module,support.module.node\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite,meta.object-literal.key,support.variable.property,support.variable.object.process,support.variable.object.node\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.json\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.instanceof\",\n        \"keyword.operator.new\",\n        \"keyword.operator.ternary\",\n        \"keyword.operator.optional\",\n        \"keyword.operator.expression.keyof\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.property.process\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function,support.function.console\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.misc.rust\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.sigil.rust\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.delete\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.dom\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.dom,support.variable.property.dom\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.arithmetic,keyword.operator.comparison,keyword.operator.decrement,keyword.operator.increment,keyword.operator.relational\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.c,keyword.operator.comparison.c,keyword.operator.c,keyword.operator.increment.c,keyword.operator.decrement.c,keyword.operator.bitwise.shift.c,keyword.operator.assignment.cpp,keyword.operator.comparison.cpp,keyword.operator.cpp,keyword.operator.increment.cpp,keyword.operator.decrement.cpp,keyword.operator.bitwise.shift.cpp\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.delimiter\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.c,punctuation.separator.cpp\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"support.type.posix-reserved.c,support.type.posix-reserved.cpp\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.sizeof.c,keyword.operator.sizeof.cpp\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.language.python\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"support.type.python\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical.python\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.python\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.arguments.begin.python,punctuation.definition.arguments.end.python,punctuation.separator.arguments.python,punctuation.definition.list.begin.python,punctuation.definition.list.end.python\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.generic.python\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.compound\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.compound.js,keyword.operator.assignment.compound.ts\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"variable.c\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"token.variable.parameter.java\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"import.storage.java\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"token.package.keyword\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"token.package\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.require\",\n        \"support.function.any-method\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.namespace\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"support.class, entity.name.type.class\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.identifier.namespace.type\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"variable.other.class.js\",\n        \"variable.other.class.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.class.php\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"control.elements, keyword.operator.less\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.special-method\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"token.storage\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.expression.delete,keyword.operator.expression.in,keyword.operator.expression.of,keyword.operator.expression.instanceof,keyword.operator.new,keyword.operator.expression.typeof,keyword.operator.expression.void\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"token.storage.type.java\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.toml, support.type.property-name.table.toml, support.type.property-name.array.toml\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property-value\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.font-name\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"constant\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.constant\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.css\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading punctuation.definition.heading, entity.name.section\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold,todo.bold\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic, punctuation.definition.italic,todo.emphasis\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"emphasis md\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.setext\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.markdown\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\",\n        \"punctuation.definition.metadata.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.metadata.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link.markdown,markup.underline.link.image.markdown\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown,string.other.link.description.markdown\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"markup.raw.monospace.asciidoc\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.asciidoc\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"markup.list.asciidoc\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"markup.link.asciidoc,markup.other.url.asciidoc\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.asciidoc,markup.other.url.asciidoc\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded, variable.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded.begin,punctuation.section.embedded.end\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal.bad-ampersand.html\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal.unrecognized-tag.html\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated.entity.other.attribute-name.html\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > string.quoted.json\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > value.json > string.quoted.json,source.json meta.structure.array.json > value.json > string.quoted.json,source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation,source.json meta.structure.array.json > value.json > string.quoted.json > punctuation\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > constant.language.json,source.json meta.structure.array.json > constant.language.json\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json punctuation\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"text.html.laravel-blade source.php.embedded.line.html entity.name.tag.laravel-blade\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"text.html.laravel-blade source.php.embedded.line.html support.constant.laravel-blade\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"support.other.namespace.use.php,support.other.namespace.use-as.php,entity.other.alias.php,meta.interface.php\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.error-control.php\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.php\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.array.begin.php\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.array.end.php\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal.non-null-typehinted.php\",\n      \"settings\": {\n        \"foreground\": \"#f44747\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.php,meta.other.type.phpdoc.php,keyword.other.type.php,keyword.other.array.phpdoc.php\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.php,meta.function-call.object.php,meta.function-call.static.php\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.parameters.begin.bracket.round.php,punctuation.definition.parameters.end.bracket.round.php,punctuation.separator.delimiter.php,punctuation.section.scope.begin.php,punctuation.section.scope.end.php,punctuation.terminator.expression.php,punctuation.definition.arguments.begin.bracket.round.php,punctuation.definition.arguments.end.bracket.round.php,punctuation.definition.storage-type.begin.bracket.round.php,punctuation.definition.storage-type.end.bracket.round.php,punctuation.definition.array.begin.bracket.round.php,punctuation.definition.array.end.bracket.round.php,punctuation.definition.begin.bracket.round.php,punctuation.definition.end.bracket.round.php,punctuation.definition.begin.bracket.curly.php,punctuation.definition.end.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php,punctuation.definition.section.switch-block.start.bracket.curly.php,punctuation.definition.section.switch-block.begin.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.core.rust\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.ext.php,support.constant.std.php,support.constant.core.php,support.constant.parser-token.php\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.goto-label.php,support.other.php\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical.php,keyword.operator.bitwise.php,keyword.operator.arithmetic.php\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.regexp.php\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.comparison.php\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.heredoc.php,keyword.operator.nowdoc.php\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.decorator.python\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"support.token.decorator.python,meta.function.decorator.identifier.python\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"function.parameter\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"function.brace\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"function.parameter.ruby, function.parameter.cs\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.symbol.ruby\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.symbol.hashkey.ruby\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": \"inline-color-decoration rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"less rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"selector.sass\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"support.type.primitive.ts,support.type.builtin.ts,support.type.primitive.tsx,support.type.builtin.tsx\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"block.scope.end,block.scope.begin\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.cs\",\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.variable.local.cs\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#f44747\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\",\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.template.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.module\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.type.flowtype\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.primitive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.template.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.template.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.substitution.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.substitution.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.assignment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.assignment.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.arithmetic.go\",\n        \"keyword.operator.address.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.arithmetic.c\",\n        \"keyword.operator.arithmetic.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.package.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.prelude.elm\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.elm\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.quasi.element\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.pseudo-element\",\n        \"entity.other.attribute-name.pseudo-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.global.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.symbol.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.keyword.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.arguments.coffee\",\n        \"variable.parameter.function.coffee\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.scope.prerequisites.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.import.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.method.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.definition.variable.name.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.definition.class.inherited.classes.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.semantic.hlsl\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.texture.hlsl\",\n        \"support.type.sampler.hlsl\",\n        \"support.type.object.hlsl\",\n        \"support.type.object.rw.hlsl\",\n        \"support.type.fx.hlsl\",\n        \"support.type.object.hlsl\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.variable\",\n        \"text.bracketed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.swift\",\n        \"support.type.vb.asp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.character-class.regexp.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.regexp.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.quote.markdown.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7f848e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"accent.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#61afef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"wikiword.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.color.rgb-value.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5c6370\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.label.cs\",\n        \"entity.name.scope-resolution.function.call\",\n        \"entity.name.scope-resolution.function.definition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.label.cs\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.setext.2.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \" meta.brace.square\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": \"comment, punctuation.definition.comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#7f848e\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote.markdown\",\n      \"settings\": {\n        \"foreground\": \"#5c6370\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.block.sequence.item.yaml\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.symbol.elixir\",\n        \"constant.language.symbol.double-quoted.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.variable.parameter.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.variable.field.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline\",\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded.begin.php\",\n        \"punctuation.section.embedded.end.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#BE5046\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.other.namespace.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.latex\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.property\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite.c\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.variable.parameter.php,punctuation.separator.colon.php,constant.other.php\",\n      \"settings\": {\n        \"foreground\": \"#abb2bf\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric.decimal.asm.x86_64\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.other.parenthesis.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d19a66\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56b6c2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.info\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#98c379\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.warning\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e5c07b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.error\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.expression.is\",\n      \"settings\": {\n        \"foreground\": \"#c678dd\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.label\",\n      \"settings\": {\n        \"foreground\": \"#e06c75\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.js,entity.other.attribute-name.ts,entity.other.attribute-name.jsx,entity.other.attribute-name.tsx,variable.parameter,variable.language.super\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-slash,comment.block.documentation\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { oneDarkPro as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa,OAAO,MAAM,CAAC;IAC7B,UAAU;QACR,0BAA0B;QAC1B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,qBAAqB;QACrB,8BAA8B;QAC9B,8BAA8B;QAC9B,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,yCAAyC;QACzC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,qCAAqC;QACrC,uCAAuC;QACvC,yCAAyC;QACzC,0CAA0C;QAC1C,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,eAAe;QACf,2CAA2C;QAC3C,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,mBAAmB;QACnB,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,gBAAgB;QAChB,iCAAiC;QACjC,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,iCAAiC;QACjC,6BAA6B;QAC7B,sBAAsB;QACtB,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,yBAAyB;QACzB,uBAAuB;QACvB,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,wCAAwC;QACxC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,mBAAmB;YACjB,cAAc;QAChB;QACA,cAAc;YACZ,cAAc;QAChB;QACA,SAAS;YACP,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,wBAAwB;YACtB,cAAc;QAChB;QACA,iBAAiB;YACf,cAAc;QAChB;QACA,gBAAgB;YACd,cAAc;QAChB;QACA,qBAAqB;YACnB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,iBAAiB;YACf,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}