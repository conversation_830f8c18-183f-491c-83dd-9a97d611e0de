{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/components/sidebar.tsx"], "sourcesContent": ["import { capitalize } from '@repo/design-system/lib/utils';\nimport type { ReactNode } from 'react';\n\ntype SidebarProperties = {\n  readonly date: Date;\n  readonly readingTime: string;\n  readonly tags?: string[];\n  readonly toc?: ReactNode;\n};\n\nexport const Sidebar = async ({\n  date,\n  readingTime,\n  tags,\n  toc: Toc,\n}: SidebarProperties) => (\n  <div className=\"col-span-4 flex w-72 flex-col items-start gap-8 border-foreground/10 border-l px-6 lg:col-span-2\">\n    <div className=\"grid gap-2\">\n      <p className=\"text-muted-foreground text-sm\">Published</p>\n      <p className=\"rounded-sm text-foreground text-sm\">\n        {new Intl.DateTimeFormat('en-US', {\n          month: 'short',\n          day: 'numeric',\n          year: 'numeric',\n          timeZone: 'America/New_York',\n        }).format(date)}\n      </p>\n    </div>\n    <div className=\"grid gap-2\">\n      <p className=\"text-muted-foreground text-sm\">Reading Time</p>\n      <p className=\"rounded-sm text-foreground text-sm\">{readingTime}</p>\n    </div>\n    {tags && (\n      <div className=\"grid gap-2\">\n        <p className=\"text-muted-foreground text-sm\">Tags</p>\n        <p className=\"rounded-sm text-foreground text-sm\">\n          {tags.map(capitalize).join(', ')}\n        </p>\n      </div>\n    )}\n    {Toc ? (\n      <div className=\"-mx-2\">\n        <div className=\"grid gap-2 p-2\">\n          <p className=\"text-muted-foreground text-sm\">Sections</p>\n          {Toc}\n        </div>\n      </div>\n    ) : undefined}\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;;;AAUO,MAAM,UAAU,OAAO,EAC5B,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,KAAK,GAAG,EACU,iBAClB,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCACV,IAAI,KAAK,cAAc,CAAC,SAAS;4BAChC,OAAO;4BACP,KAAK;4BACL,MAAM;4BACN,UAAU;wBACZ,GAAG,MAAM,CAAC;;;;;;;;;;;;0BAGd,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;YAEpD,sBACC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCACV,KAAK,GAAG,CAAC,4IAAA,CAAA,aAAU,EAAE,IAAI,CAAC;;;;;;;;;;;;YAIhC,oBACC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAE,WAAU;sCAAgC;;;;;;wBAC5C;;;;;;;;;;;uBAGH", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/body.tsx"], "sourcesContent": ["import { RichText } from '../.basehub/react-rich-text';\n\nexport const Body = RichText;\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,MAAM,OAAO,iQAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/code-block.tsx"], "sourcesContent": ["export { CodeBlock } from '../.basehub/react-code-block';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/image.tsx"], "sourcesContent": ["export { BaseHubImage as Image } from '../.basehub/next-image';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/toc.tsx"], "sourcesContent": ["import { RichText } from '../.basehub/react-rich-text';\nimport type { ComponentProps } from 'react';\n\ntype TableOfContentsProperties = Omit<\n  ComponentProps<typeof RichText>,\n  'children'\n> & {\n  readonly data: ComponentProps<typeof RichText>['children'];\n};\n\nexport const TableOfContents = ({\n  data,\n  ...props\n}: TableOfContentsProperties) => (\n  <div>\n    <RichText\n      // @ts-expect-error \"idk\"\n      components={{\n        ol: ({ children }) => (\n          <ol className=\"flex list-none flex-col gap-2 text-sm\">{children}</ol>\n        ),\n        ul: ({ children }) => (\n          <ul className=\"flex list-none flex-col gap-2 text-sm\">{children}</ul>\n        ),\n        li: ({ children }) => <li className=\"pl-3\">{children}</li>,\n        a: ({ children, href }) => (\n          <a\n            className=\"line-clamp-3 flex rounded-sm text-foreground text-sm underline decoration-foreground/0 transition-colors hover:decoration-foreground/50\"\n            href={`#${href?.split('#').at(1)}`}\n          >\n            {children}\n          </a>\n        ),\n      }}\n      {...props}\n    >\n      {data}\n    </RichText>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAUO,MAAM,kBAAkB,CAAC,EAC9B,IAAI,EACJ,GAAG,OACuB,iBAC1B,6VAAC;kBACC,cAAA,6VAAC,iQAAA,CAAA,WAAQ;YACP,yBAAyB;YACzB,YAAY;gBACV,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6VAAC;wBAAG,WAAU;kCAAyC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6VAAC;wBAAG,WAAU;kCAAyC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6VAAC;wBAAG,WAAU;kCAAQ;;;;;;gBAC5C,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,6VAAC;wBACC,WAAU;wBACV,MAAM,CAAC,CAAC,EAAE,MAAM,MAAM,KAAK,GAAG,IAAI;kCAEjC;;;;;;YAGP;YACC,GAAG,KAAK;sBAER", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/json-ld.tsx"], "sourcesContent": ["import type { Thing, WithContext } from 'schema-dts';\n\ntype JsonLdProps = {\n  code: WithContext<Thing>;\n};\n\nexport const JsonLd = ({ code }: JsonLdProps) => (\n  <script\n    type=\"application/ld+json\"\n    // biome-ignore lint/security/noDangerouslySetInnerHtml: \"This is a JSON-LD script, not user-generated content.\"\n    dangerouslySetInnerHTML={{ __html: JSON.stringify(code) }}\n  />\n);\n\nexport * from 'schema-dts';\n"], "names": [], "mappings": ";;;;AAcA;;AARO,MAAM,SAAS,CAAC,EAAE,IAAI,EAAe,iBAC1C,6VAAC;QACC,MAAK;QACL,gHAAgH;QAChH,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAM", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'Cubent';\nconst author: Metadata['authors'] = {\n  name: '<PERSON><PERSON><PERSON>',\n  url: 'https://cubent.dev/',\n};\nconst publisher = 'Cubent';\nconst twitterHandle = '@cubent';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Sidebar } from '@/components/sidebar';\nimport { env } from '@/env';\nimport { ArrowLeftIcon } from '@radix-ui/react-icons';\nimport { blog } from '@repo/cms';\nimport { Body } from '@repo/cms/components/body';\nimport { CodeBlock } from '@repo/cms/components/code-block';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Image } from '@repo/cms/components/image';\nimport { TableOfContents } from '@repo/cms/components/toc';\nimport { JsonLd } from '@repo/seo/json-ld';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport Link from 'next/link';\nimport { notFound } from 'next/navigation';\n\nconst protocol = env.VERCEL_PROJECT_PRODUCTION_URL?.startsWith('https')\n  ? 'https'\n  : 'http';\nconst url = new URL(`${protocol}://${env.VERCEL_PROJECT_PRODUCTION_URL}`);\n\ntype BlogPostProperties = {\n  readonly params: Promise<{\n    slug: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: BlogPostProperties): Promise<Metadata> => {\n  const { slug } = await params;\n  const post = await blog.getPost(slug);\n\n  if (!post) {\n    return {};\n  }\n\n  return createMetadata({\n    title: post._title,\n    description: post.description,\n    image: post.image.url,\n  });\n};\n\nexport const generateStaticParams = async (): Promise<{ slug: string }[]> => {\n  const posts = await blog.getPosts();\n\n  return posts.map(({ _slug }) => ({ slug: _slug }));\n};\n\nconst BlogPost = async ({ params }: BlogPostProperties) => {\n  const { slug } = await params;\n\n  return (\n    <Feed queries={[blog.postQuery(slug)]}>\n      {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n      {async ([data]: [any]) => {\n        'use server';\n\n        const page = data.blog.posts.item;\n\n        if (!page) {\n          notFound();\n        }\n\n        return (\n          <>\n            <JsonLd\n              code={{\n                '@type': 'BlogPosting',\n                '@context': 'https://schema.org',\n                datePublished: page.date,\n                description: page.description,\n                mainEntityOfPage: {\n                  '@type': 'WebPage',\n                  '@id': new URL(`/blog/${page._slug}`, url).toString(),\n                },\n                headline: page._title,\n                image: page.image.url,\n                dateModified: page.date,\n                author: page.authors.at(0)?._title,\n                isAccessibleForFree: true,\n              }}\n            />\n            <div className=\"container mx-auto px-8 sm:px-12 lg:px-16 xl:px-24 pt-32 pb-16\" style={{paddingTop: '60px'}}>\n              <Link\n                className=\"mb-4 inline-flex items-center gap-1 text-muted-foreground text-sm focus:underline focus:outline-none\"\n                href=\"/blog\"\n              >\n                <ArrowLeftIcon className=\"h-4 w-4\" />\n                Back to Blog\n              </Link>\n              <div className=\"mt-16 flex flex-col items-start gap-8 sm:flex-row\">\n                <div className=\"sm:flex-1\">\n                  <div className=\"prose prose-neutral dark:prose-invert max-w-none\">\n                    <h1 className=\"scroll-m-20 text-balance font-extrabold text-4xl tracking-tight lg:text-5xl\">\n                      {page._title}\n                    </h1>\n                    <p className=\"text-balance leading-7 [&:not(:first-child)]:mt-6\">\n                      {page.description}\n                    </p>\n                    {page.image ? (\n                      <Image\n                        src={page.image.url}\n                        width={page.image.width}\n                        height={page.image.height}\n                        alt={page.image.alt ?? ''}\n                        className=\"my-16 h-full w-full rounded-xl\"\n                        priority\n                      />\n                    ) : undefined}\n                    <div className=\"mx-auto max-w-prose\">\n                      <Body\n                        content={page.body.json.content}\n                        components={{\n                          pre: ({ code, language }) => {\n                            return (\n                              <CodeBlock\n                                theme=\"vesper\"\n                                snippets={[{ code, language }]}\n                              />\n                            );\n                          },\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n                <div className=\"sticky top-24 hidden shrink-0 md:block\">\n                  <Sidebar\n                    toc={<TableOfContents data={page.body.json.toc} />}\n                    readingTime={`${page.body.readingTime} min read`}\n                    date={new Date(page.date)}\n                  />\n                </div>\n              </div>\n            </div>\n          </>\n        );\n      }}\n    </Feed>\n  );\n};\n\nexport default BlogPost;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;;;;;;;;;;;;;;;;;AAEA,MAAM,WAAW,kHAAA,CAAA,MAAG,CAAC,6BAA6B,EAAE,WAAW,WAC3D,UACA;AACJ,MAAM,MAAM,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,kHAAA,CAAA,MAAG,CAAC,6BAA6B,EAAE;AAQjE,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACa;IACnB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO,CAAC;IAEhC,IAAI,CAAC,MAAM;QACT,OAAO,CAAC;IACV;IAEA,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;QACpB,OAAO,KAAK,MAAM;QAClB,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK,CAAC,GAAG;IACvB;AACF;AAEO,MAAM,uBAAuB;IAClC,MAAM,QAAQ,MAAM,wHAAA,CAAA,OAAI,CAAC,QAAQ;IAEjC,OAAO,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,MAAM;QAAM,CAAC;AAClD;MAQO,gDAAO,CAAC,KAAY;IAGnB,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;IAEjC,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,oSAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE;;0BACE,6VAAC,8IAAA,CAAA,SAAM;gBACL,MAAM;oBACJ,SAAS;oBACT,YAAY;oBACZ,eAAe,KAAK,IAAI;oBACxB,aAAa,KAAK,WAAW;oBAC7B,kBAAkB;wBAChB,SAAS;wBACT,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE,EAAE,KAAK,QAAQ;oBACrD;oBACA,UAAU,KAAK,MAAM;oBACrB,OAAO,KAAK,KAAK,CAAC,GAAG;oBACrB,cAAc,KAAK,IAAI;oBACvB,QAAQ,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI;oBAC5B,qBAAqB;gBACvB;;;;;;0BAEF,6VAAC;gBAAI,WAAU;gBAAgE,OAAO;oBAAC,YAAY;gBAAM;;kCACvG,6VAAC,2QAAA,CAAA,UAAI;wBACH,WAAU;wBACV,MAAK;;0CAEL,6VAAC,kRAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGvC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDACX,KAAK,MAAM;;;;;;sDAEd,6VAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;wCAElB,KAAK,KAAK,iBACT,6VAAC,kSAAA,CAAA,QAAK;4CACJ,KAAK,KAAK,KAAK,CAAC,GAAG;4CACnB,OAAO,KAAK,KAAK,CAAC,KAAK;4CACvB,QAAQ,KAAK,KAAK,CAAC,MAAM;4CACzB,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI;4CACvB,WAAU;4CACV,QAAQ;;;;;mDAER;sDACJ,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC,sIAAA,CAAA,OAAI;gDACH,SAAS,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO;gDAC/B,YAAY;oDACV,KAAK,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;wDACtB,qBACE,6VAAC,2RAAA,CAAA,YAAS;4DACR,OAAM;4DACN,UAAU;gEAAC;oEAAE;oEAAM;gEAAS;6DAAE;;;;;;oDAGpC;gDACF;;;;;;;;;;;;;;;;;;;;;;0CAKR,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,qIAAA,CAAA,UAAO;oCACN,mBAAK,6VAAC,qIAAA,CAAA,kBAAe;wCAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG;;;;;;oCAC9C,aAAa,GAAG,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;oCAChD,MAAM,IAAI,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;AAzFN,MAAM,WAAW,OAAO,EAAE,MAAM,EAAsB;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,qBACE,6VAAC,sLAAA,CAAA,OAAI;QAAC,SAAS;YAAC,wHAAA,CAAA,OAAI,CAAC,SAAS,CAAC;SAAM;kBAElC,8VAAA;;;;;;AAsFP;uCAEe", "debugId": null}}]}