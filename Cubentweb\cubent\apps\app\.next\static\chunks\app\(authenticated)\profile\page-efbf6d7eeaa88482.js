(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9945],{2373:(e,t,s)=>{"use strict";s.d(t,{ExtensionConnectionStatus:()=>v});var r=s(6024),n=s(70234),a=s(29011),i=s(50628),o=s(13957),l=s(45707);let c=(0,l.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var d=s(84665);let u=(0,l.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function v(e){let{isConnected:t,lastSync:s,activeSessions:l,termsAccepted:v}=e,[f,m]=(0,i.useState)(!1),[h,x]=(0,i.useState)(!1),[p,g]=(0,i.useState)(null),[y,b]=(0,i.useState)(new Date),w=async()=>{try{let e=await fetch("/api/extension/status");if(e.ok){let t=await e.json();g(t.status),b(new Date)}}catch(e){console.error("Failed to fetch extension status:",e)}};(0,i.useEffect)(()=>{w();let e=setInterval(w,3e4);return()=>clearInterval(e)},[]);let j=async()=>{x(!0),await w(),x(!1),o.toast.success("Status refreshed")},N=async()=>{if(!v){o.toast.error("Please accept the terms of service first"),window.location.href="/terms";return}m(!0);try{let e="vscode://cubent.cubent/connect?website=".concat(encodeURIComponent(window.location.origin));window.open(e,"_blank"),o.toast.success("Opening VS Code extension..."),setTimeout(()=>{window.location.reload()},3e3)}catch(e){o.toast.error("Failed to connect extension")}finally{m(!1)}},k=async()=>{try{(await fetch("/api/extension/sessions?all=true",{method:"DELETE"})).ok?(o.toast.success("All extension sessions terminated"),await w()):o.toast.error("Failed to disconnect extension")}catch(e){o.toast.error("Failed to disconnect extension")}},S=async()=>{try{m(!0);let e=await fetch("/api/extension/status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:"test_".concat(Date.now()),extensionVersion:"test-button",vscodeVersion:"webapp",platform:"browser"})}),t=await e.json();e.ok?(o.toast.success("Test session created successfully!"),await w(),setTimeout(()=>window.location.reload(),1e3)):o.toast.error("Test failed: ".concat(t.error||"Unknown error"))}catch(e){o.toast.error("Test button failed"),console.error("Test error:",e)}finally{m(!1)}},E=p||{connected:t,lastActive:(null==s?void 0:s.toISOString())||null,activeSessions:l,health:t?"healthy":"disconnected",sessions:[]};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Status:"}),(0,r.jsxs)(a.E,{variant:(e=>{switch(e){case"healthy":return"default";case"warning":return"secondary";default:return"outline"}})(E.health),className:"flex items-center gap-1",children:["warning"===E.health?(0,r.jsx)(c,{className:"h-3 w-3"}):null,E.connected?"Connected":"Disconnected"]})]}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:j,disabled:h,className:"h-6 w-6 p-0",children:(0,r.jsx)(d.A,{className:"h-3 w-3 ".concat(h?"animate-spin":"")})})]}),E.connected&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Active Sessions:"}),(0,r.jsx)("span",{className:"text-sm font-mono",children:E.activeSessions})]}),E.lastActive&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Last Active:"}),(0,r.jsx)("span",{className:"text-sm",children:new Date(E.lastActive).toLocaleString()})]}),E.sessions.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Recent Sessions:"}),(0,r.jsx)("div",{className:"space-y-1",children:E.sessions.slice(0,2).map(e=>(0,r.jsxs)("div",{className:"text-xs bg-muted p-2 rounded",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{children:["VS Code ",e.vscodeVersion]}),(0,r.jsx)("span",{children:e.platform})]}),e.extensionVersion&&(0,r.jsxs)("div",{className:"text-muted-foreground",children:["Extension v",e.extensionVersion]})]},e.id))})]})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",y.toLocaleTimeString()]}),(0,r.jsx)("div",{className:"pt-2 space-y-2",children:E.connected?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.$,{onClick:k,variant:"destructive",size:"sm",className:"w-full",children:"Disconnect All Sessions"}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,r.jsxs)("a",{href:"/profile/extension",className:"flex items-center gap-2",children:[(0,r.jsx)(u,{className:"h-3 w-3"}),"Manage Extension"]})})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.$,{onClick:N,disabled:f||!v,size:"sm",className:"w-full",children:f?"Connecting...":"Connect Extension"}),(0,r.jsx)(n.$,{onClick:S,disabled:f,variant:"outline",size:"sm",className:"w-full",children:"\uD83E\uDDEA Test Connection"})]})}),!v&&(0,r.jsxs)("div",{className:"flex items-start gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800",children:[(0,r.jsx)(c,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-xs",children:[(0,r.jsx)("p",{className:"font-medium text-yellow-800 dark:text-yellow-200",children:"Terms Required"}),(0,r.jsx)("p",{className:"text-yellow-700 dark:text-yellow-300",children:"You must accept the terms of service before connecting the extension."})]})]})]})}},17087:(e,t,s)=>{"use strict";s.d(t,{H4:()=>k,_V:()=>N,bL:()=>j});var r=s(50628),n=s(48733),a=s(72336),i=s(84268),o=s(64826),l=s(92144);function c(){return()=>{}}var d=s(6024),u="Avatar",[v,f]=(0,n.A)(u),[m,h]=v(u),x=r.forwardRef((e,t)=>{let{__scopeAvatar:s,...n}=e,[a,i]=r.useState("idle");return(0,d.jsx)(m,{scope:s,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,d.jsx)(o.sG.span,{...n,ref:t})})});x.displayName=u;var p="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:s,src:n,onLoadingStatusChange:u=()=>{},...v}=e,f=h(p,s),m=function(e,t){let{referrerPolicy:s,crossOrigin:n}=t,a=(0,l.useSyncExternalStore)(c,()=>!0,()=>!1),o=r.useRef(null),d=a?(o.current||(o.current=new window.Image),o.current):null,[u,v]=r.useState(()=>w(d,e));return(0,i.N)(()=>{v(w(d,e))},[d,e]),(0,i.N)(()=>{let e=e=>()=>{v(e)};if(!d)return;let t=e("loaded"),r=e("error");return d.addEventListener("load",t),d.addEventListener("error",r),s&&(d.referrerPolicy=s),"string"==typeof n&&(d.crossOrigin=n),()=>{d.removeEventListener("load",t),d.removeEventListener("error",r)}},[d,n,s]),u}(n,v),x=(0,a.c)(e=>{u(e),f.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==m&&x(m)},[m,x]),"loaded"===m?(0,d.jsx)(o.sG.img,{...v,ref:t,src:n}):null});g.displayName=p;var y="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:n,...a}=e,i=h(y,s),[l,c]=r.useState(void 0===n);return r.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),l&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(o.sG.span,{...a,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var j=x,N=g,k=b},29011:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(6024);s(50628);var n=s(89840),a=s(81197),i=s(31918);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,asChild:a=!1,...l}=e,c=a?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:s}),t),...l})}},31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a}),s(63410);var r=s(49973);s(13957);var n=s(22928);let a=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,r.$)(t))}},48733:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,q:()=>a});var r=s(50628),n=s(6024);function a(e,t){let s=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,n.jsx)(s.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(n){let a=r.useContext(s);if(a)return a;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,t=[]){let s=[],a=()=>{let t=s.map(e=>r.createContext(e));return function(s){let n=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:n}}),[s,n])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),o=s.length;s=[...s,a];let l=t=>{let{scope:s,children:a,...l}=t,c=s?.[e]?.[o]||i,d=r.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:d,children:a})};return l.displayName=t+"Provider",[l,function(s,n){let l=n?.[e]?.[o]||i,c=r.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=s.reduce((t,{useScope:s,scopeName:r})=>{let n=s(e)[`__scope${r}`];return{...t,...n}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return s.scopeName=t.scopeName,s}(a,...t)]}},64826:(e,t,s)=>{"use strict";s.d(t,{hO:()=>l,sG:()=>o});var r=s(50628),n=s(6341),a=s(89840),i=s(6024),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,a.TL)(`Primitive.${t}`),n=r.forwardRef((e,r)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?s:t,{...a,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(6024);s(50628);var n=s(89840),a=s(81197),i=s(31918);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:a,asChild:l=!1,...c}=e,d=l?n.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:a,className:t})),...c})}},72336:(e,t,s)=>{"use strict";s.d(t,{c:()=>n});var r=s(50628);function n(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},77229:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>i,AvatarFallback:()=>l,AvatarImage:()=>o});var r=s(6024);s(50628);var n=s(17087),a=s(31918);function i(e){let{className:t,...s}=e;return(0,r.jsx)(n.bL,{"data-slot":"avatar",className:(0,a.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)(n._V,{"data-slot":"avatar-image",className:(0,a.cn)("aspect-square size-full",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,a.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}},81197:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(49973);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],r=null==o?void 0:o[e];if(null===t)return null;let a=n(t)||n(r);return i[e][a]}),c=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return a(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:s,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...o,...c}[t]):({...o,...c})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},84268:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(50628),n=globalThis?.document?r.useLayoutEffect:()=>{}},84665:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(45707).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},84902:(e,t,s)=>{Promise.resolve().then(s.bind(s,2373)),Promise.resolve().then(s.bind(s,43432)),Promise.resolve().then(s.t.bind(s,35685,23)),Promise.resolve().then(s.bind(s,13957)),Promise.resolve().then(s.bind(s,77229))},89959:(e,t,s)=>{"use strict";var r=s(50628),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,i=r.useEffect,o=r.useLayoutEffect,l=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!n(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=a({inst:{value:s,getSnapshot:t}}),n=r[0].inst,d=r[1];return o(function(){n.value=s,n.getSnapshot=t,c(n)&&d({inst:n})},[e,s,t]),i(function(){return c(n)&&d({inst:n}),e(function(){c(n)&&d({inst:n})})},[e]),l(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},92144:(e,t,s)=>{"use strict";e.exports=s(89959)}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,7651,2913,4499,7358],()=>t(84902)),_N_E=e.O()}]);