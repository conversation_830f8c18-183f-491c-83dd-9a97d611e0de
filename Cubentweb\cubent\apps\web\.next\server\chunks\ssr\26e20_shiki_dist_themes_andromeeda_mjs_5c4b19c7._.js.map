{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/andromeeda.mjs"], "sourcesContent": ["var andromeeda = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#23262E\",\n    \"activityBar.dropBackground\": \"#3a404e\",\n    \"activityBar.foreground\": \"#BAAFC0\",\n    \"activityBarBadge.background\": \"#00b0ff\",\n    \"activityBarBadge.foreground\": \"#20232B\",\n    \"badge.background\": \"#00b0ff\",\n    \"badge.foreground\": \"#20232B\",\n    \"button.background\": \"#00e8c5cc\",\n    \"button.hoverBackground\": \"#07d4b6cc\",\n    \"debugExceptionWidget.background\": \"#FF9F2E60\",\n    \"debugExceptionWidget.border\": \"#FF9F2E60\",\n    \"debugToolBar.background\": \"#20232A\",\n    \"diffEditor.insertedTextBackground\": \"#29BF1220\",\n    \"diffEditor.removedTextBackground\": \"#F21B3F20\",\n    \"dropdown.background\": \"#2b303b\",\n    \"dropdown.border\": \"#363c49\",\n    \"editor.background\": \"#23262E\",\n    \"editor.findMatchBackground\": \"#f39d1256\",\n    \"editor.findMatchBorder\": \"#f39d12b6\",\n    \"editor.findMatchHighlightBackground\": \"#59b8b377\",\n    \"editor.foreground\": \"#D5CED9\",\n    \"editor.hoverHighlightBackground\": \"#373941\",\n    \"editor.lineHighlightBackground\": \"#2e323d\",\n    \"editor.lineHighlightBorder\": \"#2e323d\",\n    \"editor.rangeHighlightBackground\": \"#372F3C\",\n    \"editor.selectionBackground\": \"#3D4352\",\n    \"editor.selectionHighlightBackground\": \"#4F435580\",\n    \"editor.wordHighlightBackground\": \"#4F4355\",\n    \"editor.wordHighlightStrongBackground\": \"#db45a280\",\n    \"editorBracketMatch.background\": \"#746f77\",\n    \"editorBracketMatch.border\": \"#746f77\",\n    \"editorCodeLens.foreground\": \"#746f77\",\n    \"editorCursor.foreground\": \"#FFF\",\n    \"editorError.foreground\": \"#FC644D\",\n    \"editorGroup.background\": \"#23262E\",\n    \"editorGroup.dropBackground\": \"#495061d7\",\n    \"editorGroupHeader.tabsBackground\": \"#23262E\",\n    \"editorGutter.addedBackground\": \"#9BC53DBB\",\n    \"editorGutter.deletedBackground\": \"#FC644DBB\",\n    \"editorGutter.modifiedBackground\": \"#5BC0EBBB\",\n    \"editorHoverWidget.background\": \"#373941\",\n    \"editorHoverWidget.border\": \"#00e8c5cc\",\n    \"editorIndentGuide.activeBackground\": \"#585C66\",\n    \"editorIndentGuide.background\": \"#333844\",\n    \"editorLineNumber.foreground\": \"#746f77\",\n    \"editorLink.activeForeground\": \"#3B79C7\",\n    \"editorOverviewRuler.border\": \"#1B1D23\",\n    \"editorRuler.foreground\": \"#4F4355\",\n    \"editorSuggestWidget.background\": \"#20232A\",\n    \"editorSuggestWidget.border\": \"#372F3C\",\n    \"editorSuggestWidget.selectedBackground\": \"#373941\",\n    \"editorWarning.foreground\": \"#FF9F2E\",\n    \"editorWhitespace.foreground\": \"#333844\",\n    \"editorWidget.background\": \"#20232A\",\n    \"errorForeground\": \"#FC644D\",\n    \"extensionButton.prominentBackground\": \"#07d4b6cc\",\n    \"extensionButton.prominentHoverBackground\": \"#07d4b5b0\",\n    \"focusBorder\": \"#746f77\",\n    \"foreground\": \"#D5CED9\",\n    \"gitDecoration.ignoredResourceForeground\": \"#555555\",\n    \"input.background\": \"#2b303b\",\n    \"input.placeholderForeground\": \"#746f77\",\n    \"inputOption.activeBorder\": \"#C668BA\",\n    \"inputValidation.errorBackground\": \"#D65343\",\n    \"inputValidation.errorBorder\": \"#D65343\",\n    \"inputValidation.infoBackground\": \"#3A6395\",\n    \"inputValidation.infoBorder\": \"#3A6395\",\n    \"inputValidation.warningBackground\": \"#DE9237\",\n    \"inputValidation.warningBorder\": \"#DE9237\",\n    \"list.activeSelectionBackground\": \"#23262E\",\n    \"list.activeSelectionForeground\": \"#00e8c6\",\n    \"list.dropBackground\": \"#3a404e\",\n    \"list.focusBackground\": \"#282b35\",\n    \"list.focusForeground\": \"#eee\",\n    \"list.hoverBackground\": \"#23262E\",\n    \"list.hoverForeground\": \"#eee\",\n    \"list.inactiveSelectionBackground\": \"#23262E\",\n    \"list.inactiveSelectionForeground\": \"#00e8c6\",\n    \"merge.currentContentBackground\": \"#F9267240\",\n    \"merge.currentHeaderBackground\": \"#F92672\",\n    \"merge.incomingContentBackground\": \"#3B79C740\",\n    \"merge.incomingHeaderBackground\": \"#3B79C7BB\",\n    \"minimapSlider.activeBackground\": \"#60698060\",\n    \"minimapSlider.background\": \"#58607460\",\n    \"minimapSlider.hoverBackground\": \"#60698060\",\n    \"notification.background\": \"#2d313b\",\n    \"notification.buttonBackground\": \"#00e8c5cc\",\n    \"notification.buttonHoverBackground\": \"#07d4b5b0\",\n    \"notification.errorBackground\": \"#FC644D\",\n    \"notification.infoBackground\": \"#00b0ff\",\n    \"notification.warningBackground\": \"#FF9F2E\",\n    \"panel.background\": \"#23262E\",\n    \"panel.border\": \"#1B1D23\",\n    \"panelTitle.activeBorder\": \"#23262E\",\n    \"panelTitle.inactiveForeground\": \"#746f77\",\n    \"peekView.border\": \"#23262E\",\n    \"peekViewEditor.background\": \"#1A1C22\",\n    \"peekViewEditor.matchHighlightBackground\": \"#FF9F2E60\",\n    \"peekViewResult.background\": \"#1A1C22\",\n    \"peekViewResult.matchHighlightBackground\": \"#FF9F2E60\",\n    \"peekViewResult.selectionBackground\": \"#23262E\",\n    \"peekViewTitle.background\": \"#1A1C22\",\n    \"peekViewTitleDescription.foreground\": \"#746f77\",\n    \"pickerGroup.border\": \"#4F4355\",\n    \"pickerGroup.foreground\": \"#746f77\",\n    \"progressBar.background\": \"#C668BA\",\n    \"scrollbar.shadow\": \"#23262E\",\n    \"scrollbarSlider.activeBackground\": \"#3A3F4CCC\",\n    \"scrollbarSlider.background\": \"#3A3F4C77\",\n    \"scrollbarSlider.hoverBackground\": \"#3A3F4CAA\",\n    \"selection.background\": \"#746f77\",\n    \"sideBar.background\": \"#23262E\",\n    \"sideBar.foreground\": \"#999999\",\n    \"sideBarSectionHeader.background\": \"#23262E\",\n    \"sideBarTitle.foreground\": \"#00e8c6\",\n    \"statusBar.background\": \"#23262E\",\n    \"statusBar.debuggingBackground\": \"#FC644D\",\n    \"statusBar.noFolderBackground\": \"#23262E\",\n    \"statusBarItem.activeBackground\": \"#00e8c5cc\",\n    \"statusBarItem.hoverBackground\": \"#07d4b5b0\",\n    \"statusBarItem.prominentBackground\": \"#07d4b5b0\",\n    \"statusBarItem.prominentHoverBackground\": \"#00e8c5cc\",\n    \"tab.activeBackground\": \"#23262e\",\n    \"tab.activeBorder\": \"#00e8c6\",\n    \"tab.activeForeground\": \"#00e8c6\",\n    \"tab.inactiveBackground\": \"#23262E\",\n    \"tab.inactiveForeground\": \"#746f77\",\n    \"terminal.ansiBlue\": \"#7cb7ff\",\n    \"terminal.ansiBrightBlue\": \"#7cb7ff\",\n    \"terminal.ansiBrightCyan\": \"#00e8c6\",\n    \"terminal.ansiBrightGreen\": \"#96E072\",\n    \"terminal.ansiBrightMagenta\": \"#ff00aa\",\n    \"terminal.ansiBrightRed\": \"#ee5d43\",\n    \"terminal.ansiBrightYellow\": \"#FFE66D\",\n    \"terminal.ansiCyan\": \"#00e8c6\",\n    \"terminal.ansiGreen\": \"#96E072\",\n    \"terminal.ansiMagenta\": \"#ff00aa\",\n    \"terminal.ansiRed\": \"#ee5d43\",\n    \"terminal.ansiYellow\": \"#FFE66D\",\n    \"terminalCursor.background\": \"#23262E\",\n    \"terminalCursor.foreground\": \"#FFE66D\",\n    \"titleBar.activeBackground\": \"#23262E\",\n    \"walkThrough.embeddedEditorBackground\": \"#23262E\",\n    \"widget.shadow\": \"#14151A\"\n  },\n  \"displayName\": \"Andromeeda\",\n  \"name\": \"andromeeda\",\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#23262E\",\n        \"foreground\": \"#D5CED9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"markup.quote.markdown\",\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#A0A1A7cc\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.template.expression.js\",\n        \"constant.name.attribute.tag.jade\",\n        \"punctuation.definition.metadata.markdown\",\n        \"punctuation.definition.string.end.markdown\",\n        \"punctuation.definition.string.begin.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D5CED9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"support.variable\",\n        \"entity.name.tag.yaml\",\n        \"constant.character.entity.html\",\n        \"source.css entity.name.tag.reference\",\n        \"beginning.punctuation.definition.list.markdown\",\n        \"source.css entity.other.attribute-name.parent-selector\",\n        \"meta.structure.dictionary.json support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00e8c6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"constant.numeric\",\n        \"meta.group.regexp\",\n        \"constant.other.php\",\n        \"support.constant.ext.php\",\n        \"constant.other.class.php\",\n        \"support.constant.core.php\",\n        \"fenced_code.block.language\",\n        \"constant.other.caps.python\",\n        \"entity.other.attribute-name\",\n        \"support.type.exception.python\",\n        \"source.css keyword.other.unit\",\n        \"variable.other.object.property.js.jsx\",\n        \"variable.other.object.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f39c12\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.list\",\n        \"text.xml string\",\n        \"entity.name.type\",\n        \"support.function\",\n        \"entity.other.attribute-name\",\n        \"meta.at-rule.extend\",\n        \"entity.name.function\",\n        \"entity.other.inherited-class\",\n        \"entity.other.keyframe-offset.css\",\n        \"text.html.markdown string.quoted\",\n        \"meta.function-call.generic.python\",\n        \"meta.at-rule.extend support.constant\",\n        \"entity.other.attribute-name.class.jade\",\n        \"source.css entity.other.attribute-name\",\n        \"text.xml punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFE66D\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"variable.language.this.js\",\n        \"variable.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff00aa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.interpolation\",\n        \"punctuation.section.embedded.end.php\",\n        \"punctuation.section.embedded.end.ruby\",\n        \"punctuation.section.embedded.begin.php\",\n        \"punctuation.section.embedded.begin.ruby\",\n        \"punctuation.definition.template-expression\",\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f92672\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"keyword\",\n        \"meta.link\",\n        \"meta.image\",\n        \"markup.italic\",\n        \"source.js support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c74ded\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"markup.changed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7cb7ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"support.class\",\n        \"keyword.operator\",\n        \"support.constant\",\n        \"text.html.markdown string\",\n        \"source.css support.function\",\n        \"source.php support.function\",\n        \"support.function.magic.python\",\n        \"entity.other.attribute-name.id\",\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee5d43\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"text.html.php string\",\n        \"markup.inline.raw\",\n        \"markup.inserted\",\n        \"punctuation.definition.string\",\n        \"punctuation.definition.markdown\",\n        \"text.html meta.embedded source.js string\",\n        \"text.html.php punctuation.definition.string\",\n        \"text.html meta.embedded source.js punctuation.definition.string\",\n        \"text.html punctuation.definition.string\",\n        \"text.html string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#96E072\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { andromeeda as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa,OAAO,MAAM,CAAC;IAC7B,UAAU;QACR,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,0BAA0B;QAC1B,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,qBAAqB;QACrB,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,2CAA2C;QAC3C,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,2BAA2B;QAC3B,iCAAiC;QACjC,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,oBAAoB;QACpB,gBAAgB;QAChB,2BAA2B;QAC3B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,sBAAsB;QACtB,sBAAsB;QACtB,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,qCAAqC;QACrC,0CAA0C;QAC1C,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,0BAA0B;QAC1B,0BAA0B;QAC1B,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,wCAAwC;QACxC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,eAAe;QACb;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}