{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/vitesse-light.mjs"], "sourcesContent": ["var vitesseLight = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#1c6b48\",\n    \"activityBar.background\": \"#ffffff\",\n    \"activityBar.border\": \"#f0f0f0\",\n    \"activityBar.foreground\": \"#393a34\",\n    \"activityBar.inactiveForeground\": \"#393a3450\",\n    \"activityBarBadge.background\": \"#4e4f47\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"badge.background\": \"#393a3490\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumb.activeSelectionForeground\": \"#22222218\",\n    \"breadcrumb.background\": \"#f7f7f7\",\n    \"breadcrumb.focusForeground\": \"#393a34\",\n    \"breadcrumb.foreground\": \"#6a737d\",\n    \"breadcrumbPicker.background\": \"#ffffff\",\n    \"button.background\": \"#1c6b48\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#1c6b48\",\n    \"checkbox.background\": \"#f7f7f7\",\n    \"checkbox.border\": \"#d1d5da\",\n    \"debugToolBar.background\": \"#ffffff\",\n    \"descriptionForeground\": \"#393a3490\",\n    \"diffEditor.insertedTextBackground\": \"#1c6b4830\",\n    \"diffEditor.removedTextBackground\": \"#ab595940\",\n    \"dropdown.background\": \"#ffffff\",\n    \"dropdown.border\": \"#f0f0f0\",\n    \"dropdown.foreground\": \"#393a34\",\n    \"dropdown.listBackground\": \"#f7f7f7\",\n    \"editor.background\": \"#ffffff\",\n    \"editor.findMatchBackground\": \"#e6cc7744\",\n    \"editor.findMatchHighlightBackground\": \"#e6cc7766\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#fff5b1\",\n    \"editor.foldBackground\": \"#22222210\",\n    \"editor.foreground\": \"#393a34\",\n    \"editor.inactiveSelectionBackground\": \"#22222210\",\n    \"editor.lineHighlightBackground\": \"#f7f7f7\",\n    \"editor.selectionBackground\": \"#22222218\",\n    \"editor.selectionHighlightBackground\": \"#22222210\",\n    \"editor.stackFrameHighlightBackground\": \"#fffbdd\",\n    \"editor.wordHighlightBackground\": \"#1c6b4805\",\n    \"editor.wordHighlightStrongBackground\": \"#1c6b4810\",\n    \"editorBracketHighlight.foreground1\": \"#2993a3\",\n    \"editorBracketHighlight.foreground2\": \"#1e754f\",\n    \"editorBracketHighlight.foreground3\": \"#a65e2b\",\n    \"editorBracketHighlight.foreground4\": \"#a13865\",\n    \"editorBracketHighlight.foreground5\": \"#bda437\",\n    \"editorBracketHighlight.foreground6\": \"#296aa3\",\n    \"editorBracketMatch.background\": \"#1c6b4820\",\n    \"editorError.foreground\": \"#ab5959\",\n    \"editorGroup.border\": \"#f0f0f0\",\n    \"editorGroupHeader.tabsBackground\": \"#ffffff\",\n    \"editorGroupHeader.tabsBorder\": \"#f0f0f0\",\n    \"editorGutter.addedBackground\": \"#1e754f\",\n    \"editorGutter.commentRangeForeground\": \"#393a3450\",\n    \"editorGutter.deletedBackground\": \"#ab5959\",\n    \"editorGutter.foldingControlForeground\": \"#393a3490\",\n    \"editorGutter.modifiedBackground\": \"#296aa3\",\n    \"editorHint.foreground\": \"#1e754f\",\n    \"editorIndentGuide.activeBackground\": \"#00000030\",\n    \"editorIndentGuide.background\": \"#00000015\",\n    \"editorInfo.foreground\": \"#296aa3\",\n    \"editorInlayHint.background\": \"#f7f7f7\",\n    \"editorInlayHint.foreground\": \"#999999\",\n    \"editorLineNumber.activeForeground\": \"#4e4f47\",\n    \"editorLineNumber.foreground\": \"#393a3450\",\n    \"editorOverviewRuler.border\": \"#fff\",\n    \"editorStickyScroll.background\": \"#f7f7f7\",\n    \"editorStickyScrollHover.background\": \"#f7f7f7\",\n    \"editorWarning.foreground\": \"#a65e2b\",\n    \"editorWhitespace.foreground\": \"#00000015\",\n    \"editorWidget.background\": \"#ffffff\",\n    \"errorForeground\": \"#ab5959\",\n    \"focusBorder\": \"#00000000\",\n    \"foreground\": \"#393a34\",\n    \"gitDecoration.addedResourceForeground\": \"#1e754f\",\n    \"gitDecoration.conflictingResourceForeground\": \"#a65e2b\",\n    \"gitDecoration.deletedResourceForeground\": \"#ab5959\",\n    \"gitDecoration.ignoredResourceForeground\": \"#393a3450\",\n    \"gitDecoration.modifiedResourceForeground\": \"#296aa3\",\n    \"gitDecoration.submoduleResourceForeground\": \"#393a3490\",\n    \"gitDecoration.untrackedResourceForeground\": \"#2993a3\",\n    \"input.background\": \"#f7f7f7\",\n    \"input.border\": \"#f0f0f0\",\n    \"input.foreground\": \"#393a34\",\n    \"input.placeholderForeground\": \"#393a3490\",\n    \"inputOption.activeBackground\": \"#393a3450\",\n    \"list.activeSelectionBackground\": \"#f7f7f7\",\n    \"list.activeSelectionForeground\": \"#393a34\",\n    \"list.focusBackground\": \"#f7f7f7\",\n    \"list.highlightForeground\": \"#1c6b48\",\n    \"list.hoverBackground\": \"#f7f7f7\",\n    \"list.hoverForeground\": \"#393a34\",\n    \"list.inactiveFocusBackground\": \"#ffffff\",\n    \"list.inactiveSelectionBackground\": \"#f7f7f7\",\n    \"list.inactiveSelectionForeground\": \"#393a34\",\n    \"menu.separatorBackground\": \"#f0f0f0\",\n    \"notificationCenterHeader.background\": \"#ffffff\",\n    \"notificationCenterHeader.foreground\": \"#6a737d\",\n    \"notifications.background\": \"#ffffff\",\n    \"notifications.border\": \"#f0f0f0\",\n    \"notifications.foreground\": \"#393a34\",\n    \"notificationsErrorIcon.foreground\": \"#ab5959\",\n    \"notificationsInfoIcon.foreground\": \"#296aa3\",\n    \"notificationsWarningIcon.foreground\": \"#a65e2b\",\n    \"panel.background\": \"#ffffff\",\n    \"panel.border\": \"#f0f0f0\",\n    \"panelInput.border\": \"#e1e4e8\",\n    \"panelTitle.activeBorder\": \"#1c6b48\",\n    \"panelTitle.activeForeground\": \"#393a34\",\n    \"panelTitle.inactiveForeground\": \"#6a737d\",\n    \"peekViewEditor.background\": \"#ffffff\",\n    \"peekViewResult.background\": \"#ffffff\",\n    \"pickerGroup.border\": \"#f0f0f0\",\n    \"pickerGroup.foreground\": \"#393a34\",\n    \"problemsErrorIcon.foreground\": \"#ab5959\",\n    \"problemsInfoIcon.foreground\": \"#296aa3\",\n    \"problemsWarningIcon.foreground\": \"#a65e2b\",\n    \"progressBar.background\": \"#1c6b48\",\n    \"quickInput.background\": \"#ffffff\",\n    \"quickInput.foreground\": \"#393a34\",\n    \"quickInputList.focusBackground\": \"#f7f7f7\",\n    \"scrollbar.shadow\": \"#6a737d33\",\n    \"scrollbarSlider.activeBackground\": \"#393a3450\",\n    \"scrollbarSlider.background\": \"#393a3410\",\n    \"scrollbarSlider.hoverBackground\": \"#393a3450\",\n    \"settings.headerForeground\": \"#393a34\",\n    \"settings.modifiedItemIndicator\": \"#1c6b48\",\n    \"sideBar.background\": \"#ffffff\",\n    \"sideBar.border\": \"#f0f0f0\",\n    \"sideBar.foreground\": \"#4e4f47\",\n    \"sideBarSectionHeader.background\": \"#ffffff\",\n    \"sideBarSectionHeader.border\": \"#f0f0f0\",\n    \"sideBarSectionHeader.foreground\": \"#393a34\",\n    \"sideBarTitle.foreground\": \"#393a34\",\n    \"statusBar.background\": \"#ffffff\",\n    \"statusBar.border\": \"#f0f0f0\",\n    \"statusBar.debuggingBackground\": \"#f7f7f7\",\n    \"statusBar.debuggingForeground\": \"#4e4f47\",\n    \"statusBar.foreground\": \"#4e4f47\",\n    \"statusBar.noFolderBackground\": \"#ffffff\",\n    \"statusBarItem.prominentBackground\": \"#f7f7f7\",\n    \"tab.activeBackground\": \"#ffffff\",\n    \"tab.activeBorder\": \"#f0f0f0\",\n    \"tab.activeBorderTop\": \"#393a3490\",\n    \"tab.activeForeground\": \"#393a34\",\n    \"tab.border\": \"#f0f0f0\",\n    \"tab.hoverBackground\": \"#f7f7f7\",\n    \"tab.inactiveBackground\": \"#ffffff\",\n    \"tab.inactiveForeground\": \"#6a737d\",\n    \"tab.unfocusedActiveBorder\": \"#f0f0f0\",\n    \"tab.unfocusedActiveBorderTop\": \"#f0f0f0\",\n    \"tab.unfocusedHoverBackground\": \"#ffffff\",\n    \"terminal.ansiBlack\": \"#121212\",\n    \"terminal.ansiBlue\": \"#296aa3\",\n    \"terminal.ansiBrightBlack\": \"#aaaaaa\",\n    \"terminal.ansiBrightBlue\": \"#296aa3\",\n    \"terminal.ansiBrightCyan\": \"#2993a3\",\n    \"terminal.ansiBrightGreen\": \"#1e754f\",\n    \"terminal.ansiBrightMagenta\": \"#a13865\",\n    \"terminal.ansiBrightRed\": \"#ab5959\",\n    \"terminal.ansiBrightWhite\": \"#dddddd\",\n    \"terminal.ansiBrightYellow\": \"#bda437\",\n    \"terminal.ansiCyan\": \"#2993a3\",\n    \"terminal.ansiGreen\": \"#1e754f\",\n    \"terminal.ansiMagenta\": \"#a13865\",\n    \"terminal.ansiRed\": \"#ab5959\",\n    \"terminal.ansiWhite\": \"#dbd7ca\",\n    \"terminal.ansiYellow\": \"#bda437\",\n    \"terminal.foreground\": \"#393a34\",\n    \"terminal.selectionBackground\": \"#22222218\",\n    \"textBlockQuote.background\": \"#ffffff\",\n    \"textBlockQuote.border\": \"#f0f0f0\",\n    \"textCodeBlock.background\": \"#ffffff\",\n    \"textLink.activeForeground\": \"#1c6b48\",\n    \"textLink.foreground\": \"#1c6b48\",\n    \"textPreformat.foreground\": \"#586069\",\n    \"textSeparator.foreground\": \"#d1d5da\",\n    \"titleBar.activeBackground\": \"#ffffff\",\n    \"titleBar.activeForeground\": \"#4e4f47\",\n    \"titleBar.border\": \"#f7f7f7\",\n    \"titleBar.inactiveBackground\": \"#ffffff\",\n    \"titleBar.inactiveForeground\": \"#6a737d\",\n    \"tree.indentGuidesStroke\": \"#e1e4e8\",\n    \"welcomePage.buttonBackground\": \"#f6f8fa\",\n    \"welcomePage.buttonHoverBackground\": \"#e1e4e8\"\n  },\n  \"displayName\": \"Vitesse Light\",\n  \"name\": \"vitesse-light\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"class\": \"#5a6aa6\",\n    \"interface\": \"#2e808f\",\n    \"namespace\": \"#b05a78\",\n    \"property\": \"#998418\",\n    \"type\": \"#2e808f\"\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a0ada0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"delimiter.bracket\",\n        \"delimiter\",\n        \"invalid.illegal.character-not-allowed-here.html\",\n        \"keyword.operator.rest\",\n        \"keyword.operator.spread\",\n        \"keyword.operator.type.annotation\",\n        \"keyword.operator.relational\",\n        \"keyword.operator.assignment\",\n        \"keyword.operator.type\",\n        \"meta.brace\",\n        \"meta.tag.block.any.html\",\n        \"meta.tag.inline.any.html\",\n        \"meta.tag.structure.input.void.html\",\n        \"meta.type.annotation\",\n        \"meta.embedded.block.github-actions-expression\",\n        \"storage.type.function.arrow\",\n        \"meta.objectliteral.ts\",\n        \"punctuation\",\n        \"punctuation.definition.string.begin.html.vue\",\n        \"punctuation.definition.string.end.html.vue\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#999999\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.language\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a65e2b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity\",\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59873a\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#393a34\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"tag.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e754f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#59873a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"storage.type.class.jsdoc\",\n        \"punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e754f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\",\n        \"support.type.builtin\",\n        \"constant.language.undefined\",\n        \"constant.language.null\",\n        \"constant.language.import-export-all.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ab5959\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.derivative\",\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#393a34\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\",\n        \"attribute.value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b56959\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b5695977\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#99841877\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#998418\"\n      }\n    },\n    {\n      \"scope\": [\n        \"property\",\n        \"meta.property-name\",\n        \"meta.object-literal.key\",\n        \"entity.name.tag.yaml\",\n        \"attribute.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#998418\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"invalid.deprecated.entity.other.attribute-name.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b07d48\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"identifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b07d48\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.primitive\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2e8f82\"\n      }\n    },\n    {\n      \"scope\": \"namespace\",\n      \"settings\": {\n        \"foreground\": \"#b05a78\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"keyword.operator.assignment.compound\",\n        \"meta.var.expr.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ab5959\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#d73a49\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#fafbfc\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#b56959\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ab5e3f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b56959\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#bda437\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a65e2b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.quantifier.regexp\",\n        \"constant.numeric\",\n        \"number\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2f798a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.unit\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ab5959\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.boolean\",\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1e754f\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#1c6b48\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#a65e2b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#1c6b48\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#2e808f\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#393a34\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#393a34\"\n      }\n    },\n    {\n      \"scope\": \"markup.raw\",\n      \"settings\": {\n        \"foreground\": \"#1c6b48\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#ffeef0\",\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#f0fff4\",\n        \"foreground\": \"#22863a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#ffebda\",\n        \"foreground\": \"#e36209\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#005cc5\",\n        \"foreground\": \"#f6f8fa\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#6f42c1\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#005cc5\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#005cc5\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#005cc5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#586069\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#b31d28\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\",\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b56959\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link.markdown\",\n        \"markup.underline.link.image.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#393a3490\"\n      }\n    },\n    {\n      \"scope\": [\n        \"type.identifier\",\n        \"constant.other.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5a6aa6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.html.vue\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59873a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.illegal.unrecognized-tag.html\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { vitesseLight as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe,OAAO,MAAM,CAAC;IAC/B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,yBAAyB;QACzB,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,iCAAiC;QACjC,sCAAsC;QACtC,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,4BAA4B;QAC5B,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,kCAAkC;QAClC,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,SAAS;QACT,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;IACV;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}