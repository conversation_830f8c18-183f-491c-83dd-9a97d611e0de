{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-scroll-area_a8c211c8bb673ac9e3ab9da8681a3338_node_mo-7d49cd.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-direction@1_e32f95a12a0e23976853758865c76117/node_modules/@radix-ui/react-direction/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-scroll-area_a8c211c8bb673ac9e3ab9da8681a3338/node_modules/@radix-ui/react-scroll-area/dist/index.mjs"], "sourcesContent": ["// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/scroll-area.tsx\nimport * as React2 from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\n\n// src/use-state-machine.ts\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/scroll-area.tsx\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = React2.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = \"hover\",\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React2.useState(null);\n    const [viewport, setViewport] = React2.useState(null);\n    const [content, setContent] = React2.useState(null);\n    const [scrollbarX, setScrollbarX] = React2.useState(null);\n    const [scrollbarY, setScrollbarY] = React2.useState(null);\n    const [cornerWidth, setCornerWidth] = React2.useState(0);\n    const [cornerHeight, setCornerHeight] = React2.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React2.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React2.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n    return /* @__PURE__ */ jsx(\n      ScrollAreaProvider,\n      {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n              position: \"relative\",\n              // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n              [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n              [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n              ...props.style\n            }\n          }\n        )\n      }\n    );\n  }\n);\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React2.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-scroll-area-viewport\": \"\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n            overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n            ...props.style\n          },\n          children: /* @__PURE__ */ jsx(\"div\", { ref: context.onContentChange, style: { minWidth: \"100%\", display: \"table\" }, children })\n        }\n      )\n    ] });\n  }\n);\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    React2.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n    return context.type === \"hover\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarHover, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"scroll\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarScroll, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"auto\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarAuto, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"always\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarVisible, { ...scrollbarProps, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React2.useState(false);\n  React2.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n      scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarAuto,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarScroll = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const debounceScrollEnd = useDebounceCallback(() => send(\"SCROLL_END\"), 100);\n  const [state, send] = useStateMachine(\"hidden\", {\n    hidden: {\n      SCROLL: \"scrolling\"\n    },\n    scrolling: {\n      SCROLL_END: \"idle\",\n      POINTER_ENTER: \"interacting\"\n    },\n    interacting: {\n      SCROLL: \"interacting\",\n      POINTER_LEAVE: \"idle\"\n    },\n    idle: {\n      HIDE: \"hidden\",\n      SCROLL: \"scrolling\",\n      POINTER_ENTER: \"interacting\"\n    }\n  });\n  React2.useEffect(() => {\n    if (state === \"idle\") {\n      const hideTimer = window.setTimeout(() => send(\"HIDE\"), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n  React2.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send(\"SCROLL\");\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener(\"scroll\", handleScroll);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || state !== \"hidden\", children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n      ...scrollbarProps,\n      ref: forwardedRef,\n      onPointerEnter: composeEventHandlers(props.onPointerEnter, () => send(\"POINTER_ENTER\")),\n      onPointerLeave: composeEventHandlers(props.onPointerLeave, () => send(\"POINTER_LEAVE\"))\n    }\n  ) });\n});\nvar ScrollAreaScrollbarAuto = React2.forwardRef((props, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React2.useState(false);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarVisible = React2.forwardRef((props, forwardedRef) => {\n  const { orientation = \"vertical\", ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React2.useRef(null);\n  const pointerOffsetRef = React2.useRef(0);\n  const [sizes, setSizes] = React2.useState({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n  const commonProps = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => thumbRef.current = thumb,\n    onThumbPointerUp: () => pointerOffsetRef.current = 0,\n    onThumbPointerDown: (pointerPos) => pointerOffsetRef.current = pointerPos\n  };\n  function getScrollPosition(pointerPos, dir) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n  if (orientation === \"horizontal\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarX,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }\n      }\n    );\n  }\n  if (orientation === \"vertical\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarY,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }\n      }\n    );\n  }\n  return null;\n});\nvar ScrollAreaScrollbarX = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"horizontal\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        bottom: 0,\n        left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.x),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.x),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar ScrollAreaScrollbarY = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"vertical\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        top: 0,\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: \"var(--radix-scroll-area-corner-height)\",\n        [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.y),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.y),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = React2.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React2.useState(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React2.useRef(null);\n  const prevWebkitUserSelectRef = React2.useRef(\"\");\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n  function handleDragScroll(event) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n  React2.useEffect(() => {\n    const handleWheel = (event) => {\n      const element = event.target;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener(\"wheel\", handleWheel, { passive: false });\n    return () => document.removeEventListener(\"wheel\", handleWheel, { passive: false });\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n  React2.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(\n    ScrollbarProvider,\n    {\n      scope: __scopeScrollArea,\n      scrollbar,\n      hasThumb,\n      onThumbChange: useCallbackRef(onThumbChange),\n      onThumbPointerUp: useCallbackRef(onThumbPointerUp),\n      onThumbPositionChange: handleThumbPositionChange,\n      onThumbPointerDown: useCallbackRef(onThumbPointerDown),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          ...scrollbarProps,\n          ref: composeRefs,\n          style: { position: \"absolute\", ...scrollbarProps.style },\n          onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n            const mainPointer = 0;\n            if (event.button === mainPointer) {\n              const element = event.target;\n              element.setPointerCapture(event.pointerId);\n              rectRef.current = scrollbar.getBoundingClientRect();\n              prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n              document.body.style.webkitUserSelect = \"none\";\n              if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n              handleDragScroll(event);\n            }\n          }),\n          onPointerMove: composeEventHandlers(props.onPointerMove, handleDragScroll),\n          onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n            const element = event.target;\n            if (element.hasPointerCapture(event.pointerId)) {\n              element.releasePointerCapture(event.pointerId);\n            }\n            document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n            if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n            rectRef.current = null;\n          })\n        }\n      )\n    }\n  );\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || scrollbarContext.hasThumb, children: /* @__PURE__ */ jsx(ScrollAreaThumbImpl, { ref: forwardedRef, ...thumbProps }) });\n  }\n);\nvar ScrollAreaThumbImpl = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(\n      forwardedRef,\n      (node) => scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React2.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = void 0;\n      }\n    }, 100);\n    React2.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener(\"scroll\", handleScroll);\n        return () => viewport.removeEventListener(\"scroll\", handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n          width: \"var(--radix-scroll-area-thumb-width)\",\n          height: \"var(--radix-scroll-area-thumb-height)\",\n          ...style\n        },\n        onPointerDownCapture: composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        }),\n        onPointerUp: composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n      }\n    );\n  }\n);\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = React2.forwardRef(\n  (props, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ jsx(ScrollAreaCornerImpl, { ...props, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = React2.forwardRef((props, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React2.useState(0);\n  const [height, setHeight] = React2.useState(0);\n  const hasSize = Boolean(width && height);\n  useResizeObserver(context.scrollbarX, () => {\n    const height2 = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height2);\n    setHeight(height2);\n  });\n  useResizeObserver(context.scrollbarY, () => {\n    const width2 = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width2);\n    setWidth(width2);\n  });\n  return hasSize ? /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      ...cornerProps,\n      ref: forwardedRef,\n      style: {\n        width,\n        height,\n        position: \"absolute\",\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: 0,\n        ...props.style\n      }\n    }\n  ) : null;\n});\nfunction toInt(value) {\n  return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);\n  return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = () => {\n}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React2.useRef(0);\n  React2.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React2.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\nfunction useResizeObserver(element, onResize) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\nexport {\n  Corner,\n  Root,\n  ScrollArea,\n  ScrollAreaCorner,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaViewport,\n  Scrollbar,\n  Thumb,\n  Viewport,\n  createScrollAreaScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}