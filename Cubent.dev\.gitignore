.pnpm-store
dist
out
out-*
node_modules
coverage/
mock/

.DS_Store

# IDEs
.idea

# Builds
bin/
*.vsix

# Local prompts and rules
/local-prompts

# Test environment
.test_env
.vscode-test/

# Docs
docs/_site/

# Dotenv
.env
.env.*
!.env.*.sample

# Logging
logs

# Vite development
.vite-port

# Turborepo
.turbo

# IntelliJ and Qodo plugin folders
.idea/
.qodo/

# API Keys - keep built-in keys local only
src/core/config/ProviderSettingsManager.ts
