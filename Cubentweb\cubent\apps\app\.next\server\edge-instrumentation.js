(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{356:e=>{"use strict";e.exports=require("node:buffer")},552:e=>{"use strict";e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},812:(e,t,n)=>{"use strict";let r,i,a,s,o;n.r(t),n.d(t,{register:()=>oX});let u=globalThis,l="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,c="9.22.0";function d(){return p(u),u}function p(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||c,t[c]=t[c]||{}}function h(e,t,n=u){let r=n.__SENTRY__=n.__SENTRY__||{},i=r[c]=r[c]||{};return i[e]||(i[e]=t())}let f=Object.prototype.toString;function _(e){switch(f.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return S(e,Error)}}function m(e,t){return f.call(e)===`[object ${t}]`}function g(e){return m(e,"String")}function y(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function v(e){return null===e||y(e)||"object"!=typeof e&&"function"!=typeof e}function E(e){return m(e,"Object")}function T(e){return!!(e?.then&&"function"==typeof e.then)}function S(e,t){try{return e instanceof t}catch(e){return!1}}function b(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function O(e){return"undefined"!=typeof Request&&S(e,Request)}let x=["debug","info","warn","error","log","assert","trace"],w={};function R(e){if(!("console"in u))return e();let t=u.console,n={},r=Object.keys(w);r.forEach(e=>{let r=w[e];n[e]=t[e],t[e]=r});try{return e()}finally{r.forEach(e=>{t[e]=n[e]})}}let A=h("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return l?x.forEach(n=>{t[n]=(...t)=>{e&&R(()=>{u.console[n](`Sentry Logger [${n}]:`,...t)})}}):x.forEach(e=>{t[e]=()=>void 0}),t});function I(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function P(e,t=[],n=!1){return t.some(t=>(function(e,t,n=!1){return!!g(e)&&(m(t,"RegExp")?t.test(e):!!g(t)&&(n?e===t:e.includes(t)))})(e,t,n))}function C(e,t,n){if(!(t in e))return;let r=e[t];if("function"!=typeof r)return;let i=n(r);"function"==typeof i&&function(e,t){try{let n=t.prototype||{};e.prototype=t.prototype=n,k(e,"__sentry_original__",t)}catch(e){}}(i,r);try{e[t]=i}catch{l&&A.log(`Failed to replace method "${t}" in object`,e)}}function k(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch(n){l&&A.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function L(e){if(_(e))return{message:e.message,name:e.name,stack:e.stack,...M(e)};if(!("undefined"!=typeof Event&&S(e,Event)))return e;{let t={type:e.type,target:N(e.target),currentTarget:N(e.currentTarget),...M(e)};return"undefined"!=typeof CustomEvent&&S(e,CustomEvent)&&(t.detail=e.detail),t}}function N(e){try{return"undefined"!=typeof Element&&S(e,Element)?function(e,t={}){if(!e)return"<unknown>";try{let n,r=e,i=[],a=0,s=0,o=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;r&&a++<5&&(n=function(e,t){let n=[];if(!e?.tagName)return"";if(u.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}n.push(e.tagName.toLowerCase());let r=t?.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(r?.length)r.forEach(e=>{n.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&n.push(`#${e.id}`);let t=e.className;if(t&&g(t))for(let e of t.split(/\s+/))n.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let r=e.getAttribute(t);r&&n.push(`[${t}="${r}"]`)}return n.join("")}(r,o),"html"!==n&&(!(a>1)||!(s+3*i.length+n.length>=l)));)i.push(n),s+=n.length,r=r.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function M(e){if("object"!=typeof e||null===e)return{};{let t={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}function D(e=u.crypto||u.msCrypto){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function j(e){return e.exception?.values?.[0]}function $(e){let{message:t,event_id:n}=e;if(t)return t;let r=j(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function U(e,t){let n=j(e);if(!n)return;let r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){let e={...r?.data,...t.data};n.mechanism.data=e}}function B(e){if(function(e){try{return e.__sentry_captured__}catch{}}(e))return!0;try{k(e,"__sentry_captured__",!0)}catch(e){}return!1}function F(){return Date.now()/1e3}let Z=function(){let{performance:e}=u;if(!e?.now)return F;let t=Date.now()-e.now(),n=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(n+e.now())/1e3}();function V(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||Z(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:D()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function G(e,t,n=2){if(!t||"object"!=typeof t||n<=0)return t;if(e&&0===Object.keys(t).length)return e;let r={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=G(r[e],t[e],n-1));return r}let z="_sentrySpan";function X(e,t){t?k(e,z,t):delete e[z]}function H(){return D().substring(16)}class K{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:D(),sampleRand:Math.random()}}clone(){let e=new K;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,X(e,this[z]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&V(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,{tags:n,extra:r,user:i,contexts:a,level:s,fingerprint:o=[],propagationContext:u}=(t instanceof K?t.getScopeData():E(t)?e:void 0)||{};return this._tags={...this._tags,...n},this._extra={...this._extra,...r},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),s&&(this._level=s),o.length&&(this._fingerprint=o),u&&(this._propagationContext=u),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,X(this,void 0),this._attachments=[],this.setPropagationContext({traceId:D(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let n="number"==typeof t?t:100;if(n<=0)return this;let r={timestamp:F(),...e,message:e.message?I(e.message,2048):e.message};return this._breadcrumbs.push(r),this._breadcrumbs.length>n&&(this._breadcrumbs=this._breadcrumbs.slice(-n),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[z]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=G(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let n=t?.event_id||D();if(!this._client)return A.warn("No client configured on scope - will not capture exception!"),n;let r=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},this),n}captureMessage(e,t,n){let r=n?.event_id||D();if(!this._client)return A.warn("No client configured on scope - will not capture message!"),r;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...n,event_id:r},this),r}captureEvent(e,t){let n=t?.event_id||D();return this._client?this._client.captureEvent(e,{...t,event_id:n},this):A.warn("No client configured on scope - will not capture event!"),n}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}function Y(){return h("defaultCurrentScope",()=>new K)}function W(){return h("defaultIsolationScope",()=>new K)}class q{constructor(e,t){let n,r;n=e||new K,r=t||new K,this._stack=[{scope:n}],this._isolationScope=r}withScope(e){let t,n=this._pushScope();try{t=e(n)}catch(e){throw this._popScope(),e}return T(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function J(){let e=p(d());return e.stack=e.stack||new q(Y(),W())}function Q(e){return J().withScope(e)}function ee(e,t){let n=J();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function et(e){return J().withScope(()=>e(J().getIsolationScope()))}function en(e){let t=p(e);return t.acs?t.acs:{withIsolationScope:et,withScope:Q,withSetScope:ee,withSetIsolationScope:(e,t)=>et(t),getCurrentScope:()=>J().getScope(),getIsolationScope:()=>J().getIsolationScope()}}function er(){return en(d()).getCurrentScope()}function ei(){return en(d()).getIsolationScope()}function ea(...e){let t=en(d());if(2===e.length){let[n,r]=e;return n?t.withSetScope(n,r):t.withScope(r)}return t.withScope(e[0])}function es(){return er().getClient()}function eo(e){let{traceId:t,parentSpanId:n,propagationSpanId:r}=e.getPropagationContext(),i={trace_id:t,span_id:r||H()};return n&&(i.parent_span_id=n),i}let eu="sentry.source",el="sentry.sample_rate",ec="sentry.op",ed="sentry.origin",ep="sentry.custom_span_name",eh="sentry.profile_id",ef="sentry.exclusive_time";function e_(e){if(e<400&&e>=100)return{code:1};if(e>=400&&e<500)switch(e){case 401:return{code:2,message:"unauthenticated"};case 403:return{code:2,message:"permission_denied"};case 404:return{code:2,message:"not_found"};case 409:return{code:2,message:"already_exists"};case 413:return{code:2,message:"failed_precondition"};case 429:return{code:2,message:"resource_exhausted"};case 499:return{code:2,message:"cancelled"};default:return{code:2,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:2,message:"unimplemented"};case 503:return{code:2,message:"unavailable"};case 504:return{code:2,message:"deadline_exceeded"};default:return{code:2,message:"internal_error"}}return{code:2,message:"unknown_error"}}let em="_sentryScope",eg="_sentryIsolationScope";function ey(e,t,n){e&&(k(e,eg,n),k(e,em,t))}function ev(e){return{scope:e[em],isolationScope:e[eg]}}function eE(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}let eT="sentry-",eS=/^sentry-/;function eb(e){let t=ex(e);if(!t)return;let n=Object.entries(t).reduce((e,[t,n])=>(t.match(eS)&&(e[t.slice(eT.length)]=n),e),{});return Object.keys(n).length>0?n:void 0}function eO(e){if(e){var t=Object.entries(e).reduce((e,[t,n])=>(n&&(e[`${eT}${t}`]=n),e),{});return 0!==Object.keys(t).length?Object.entries(t).reduce((e,[t,n],r)=>{let i=`${encodeURIComponent(t)}=${encodeURIComponent(n)}`,a=0===r?i:`${e},${i}`;return a.length>8192?(l&&A.warn(`Not adding key: ${t} with val: ${n} to baggage header due to exceeding baggage size limits.`),e):a},""):void 0}}function ex(e){if(e&&(g(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(ew(t)).forEach(([t,n])=>{e[t]=n}),e),{}):ew(e)}function ew(e){return e.split(",").map(e=>e.split("=").map(e=>{try{return decodeURIComponent(e.trim())}catch{return}})).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}let eR=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function eA(e=D(),t=H(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${e}-${t}${r}`}let eI=!1;function eP(e){let{spanId:t,traceId:n,isRemote:r}=e.spanContext(),i=r?t:eN(e).parent_span_id,a=ev(e).scope;return{parent_span_id:i,span_id:r?a?.getPropagationContext().propagationSpanId||H():t,trace_id:n}}function eC(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:n,...r},attributes:i})=>({span_id:e,trace_id:t,sampled:1===n,attributes:i,...r})):void 0}function ek(e){return"number"==typeof e?eL(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?eL(e.getTime()):Z()}function eL(e){return e>0x2540be3ff?e/1e3:e}function eN(e){var t;if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:n,traceId:r}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:a,endTime:s,status:o,links:u}=e;return{span_id:n,trace_id:r,data:t,description:a,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0,start_timestamp:ek(i),timestamp:ek(s)||void 0,status:eD(o),op:t[ec],origin:t[ed],links:eC(u)}}return{span_id:n,trace_id:r,start_timestamp:0,data:{}}}function eM(e){let{traceFlags:t}=e.spanContext();return 1===t}function eD(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}let ej="_sentryChildSpans",e$="_sentryRootSpan";function eU(e,t){let n=e[e$]||e;k(t,e$,n),e[ej]?e[ej].add(t):k(e,ej,new Set([t]))}function eB(e){return e[e$]||e}function eF(){let e=en(d());return e.getActiveSpan?e.getActiveSpan():er()[z]}function eZ(){eI||(R(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),eI=!0)}let eV=/\(error: (.*)\)/,eG=/captureMessage|captureException/;function ez(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,n=0,r=0)=>{let i=[],a=e.split("\n");for(let e=n;e<a.length;e++){let n=a[e];if(n.length>1024)continue;let s=eV.test(n)?n.replace(eV,"$1"):n;if(!s.match(/\S*Error: /)){for(let e of t){let t=e(s);if(t){i.push(t);break}}if(i.length>=50+r)break}}var s=i.slice(r);if(!s.length)return[];let o=Array.from(s);return/sentryWrapped/.test(eX(o).function||"")&&o.pop(),o.reverse(),eG.test(eX(o).function||"")&&(o.pop(),eG.test(eX(o).function||"")&&o.pop()),o.slice(0,50).map(e=>({...e,filename:e.filename||eX(o).filename,function:e.function||"?"}))}}function eX(e){return e[e.length-1]||{}}let eH="<anonymous>";function eK(e){try{if(!e||"function"!=typeof e)return eH;return e.name||eH}catch(e){return eH}}function eY(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){}}}let eW={},eq={};function eJ(e,t){eW[e]=eW[e]||[],eW[e].push(t)}function eQ(e,t){if(!eq[e]){eq[e]=!0;try{t()}catch(t){l&&A.error(`Error while instrumenting ${e}`,t)}}}function e0(e,t){let n=e&&eW[e];if(n)for(let r of n)try{r(t)}catch(t){l&&A.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${eK(r)}
Error:`,t)}}let e1=null;function e2(){e1=u.onerror,u.onerror=function(e,t,n,r,i){return e0("error",{column:r,error:i,line:n,msg:e,url:t}),!!e1&&e1.apply(this,arguments)},u.onerror.__SENTRY_INSTRUMENTED__=!0}let e4=null;function e9(){e4=u.onunhandledrejection,u.onunhandledrejection=function(e){return e0("unhandledrejection",e),!e4||e4.apply(this,arguments)},u.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let e5=!1;function e3(){if(!e5){e5=!0;let e="error";eJ(e,e6),eQ(e,e2);let t="unhandledrejection";eJ(t,e6),eQ(t,e9)}}function e6(){let e=eF(),t=e&&eB(e);if(t){let e="internal_error";l&&A.log(`[Tracing] Root span: ${e} -> Global error occurred`),t.setStatus({code:2,message:e})}}function e8(e,t,n=[t],r="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:n.map(e=>({name:`${r}:@sentry/${e}`,version:c})),version:c}),e._metadata=i}function e7(e){return"isRelative"in e}e6.tag="sentry_tracingErrorCallback";function te(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function tt(e){return e.split(/[?#]/,1)[0]}function tn(e){let{protocol:t,host:n,path:r}=e,i=n?.replace(/^.*@/,"[filtered]:[filtered]@").replace(/(:80)$/,"").replace(/(:443)$/,"")||"";return`${t?`${t}://`:""}${i}${r}`}let tr=/^o(\d+)\./,ti=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function ta(e,t=!1){let{host:n,path:r,pass:i,port:a,projectId:s,protocol:o,publicKey:u}=e;return`${o}://${u}${t&&i?`:${i}`:""}@${n}${a?`:${a}`:""}/${r?`${r}/`:r}${s}`}function ts(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function to(e,t=100,n=Infinity){try{return function e(t,n,r=Infinity,i=Infinity,a=function(){let e=new WeakSet;return[function(t){return!!e.has(t)||(e.add(t),!1)},function(t){e.delete(t)}]}()){let[s,o]=a;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;let u=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(b(t))return"[VueViewModel]";if(E(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${eK(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let n=function(e){let t=Object.getPrototypeOf(e);return t?.constructor?t.constructor.name:"null prototype"}(t);if(/^HTML(\w*)Element$/.test(n))return`[HTMLElement: ${n}]`;return`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}}(t,n);if(!u.startsWith("[object "))return u;if(n.__sentry_skip_normalization__)return n;let l="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:r;if(0===l)return u.replace("object ","");if(s(n))return"[Circular ~]";if(n&&"function"==typeof n.toJSON)try{let t=n.toJSON();return e("",t,l-1,i,a)}catch(e){}let c=Array.isArray(n)?[]:{},d=0,p=L(n);for(let t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(d>=i){c[t]="[MaxProperties ~]";break}let n=p[t];c[t]=e(t,n,l-1,i,a),d++}return o(n),c}("",e,t,n)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function tu(e,t=[]){return[e,t]}function tl(e,t){for(let n of e[1]){let e=n[0].type;if(t(n,e))return!0}return!1}function tc(e){let t=p(u);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}let td={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function tp(e){if(!e?.sdk)return;let{name:t,version:n}=e.sdk;return{name:t,version:n}}let th="production";function tf(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||es()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}let t_="_frozenDsc";function tm(e,t){let n,r=t.getOptions(),{publicKey:i,host:a}=t.getDsn()||{};r.orgId?n=String(r.orgId):a&&(n=function(e){let t=e.match(tr);return t?.[1]}(a));let s={environment:r.environment||th,release:r.release,public_key:i,trace_id:e,org_id:n};return t.emit("createDsc",s),s}function tg(e,t){let n=t.getPropagationContext();return n.dsc||tm(n.traceId,e)}function ty(e){let t=es();if(!t)return{};let n=eB(e),r=eN(n),i=r.data,a=n.spanContext().traceState,s=a?.get("sentry.sample_rate")??i[el]??i["sentry.previous_trace_sample_rate"];function o(e){return("number"==typeof s||"string"==typeof s)&&(e.sample_rate=`${s}`),e}let u=n[t_];if(u)return o(u);let l=a?.get("sentry.dsc"),c=l&&eb(l);if(c)return o(c);let d=tm(e.spanContext().traceId,t),p=i[eu],h=r.description;return"url"!==p&&h&&(d.transaction=h),tf()&&(d.sampled=String(eM(n)),d.sample_rand=a?.get("sentry.sample_rand")??ev(n).scope?.getPropagationContext().sampleRand.toString()),o(d),t.emit("createDsc",d,n),d}let tv=[];function tE(e,t){for(let n of t)n?.afterAllSetup&&n.afterAllSetup(e)}function tT(e,t,n){if(n[t.name]){l&&A.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,-1===tv.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tv.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let n=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,r)=>n(t,r,e))}if("function"==typeof t.processEvent){let n=t.processEvent.bind(t),r=Object.assign((t,r)=>n(t,r,e),{id:t.name});e.addEventProcessor(r)}l&&A.log(`Integration installed: ${t.name}`)}function tS(e){let t=[];e.message&&t.push(e.message);try{let n=e.exception.values[e.exception.values.length-1];n?.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch(e){}return t}function tb(e){return new tx(t=>{t(e)})}function tO(e){return new tx((t,n)=>{n(e)})}!function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(nP||(nP={}));class tx{constructor(e){this._state=nP.PENDING,this._handlers=[],this._runExecutor(e)}then(e,t){return new tx((n,r)=>{this._handlers.push([!1,t=>{if(e)try{n(e(t))}catch(e){r(e)}else n(t)},e=>{if(t)try{n(t(e))}catch(e){r(e)}else r(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new tx((t,n)=>{let r,i;return this.then(t=>{i=!1,r=t,e&&e()},t=>{i=!0,r=t,e&&e()}).then(()=>{if(i)return void n(r);t(r)})})}_executeHandlers(){if(this._state===nP.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===nP.RESOLVED&&e[1](this._value),this._state===nP.REJECTED&&e[2](this._value),e[0]=!0)})}_runExecutor(e){let t=(e,t)=>{if(this._state===nP.PENDING){if(T(t))return void t.then(n,r);this._state=e,this._value=t,this._executeHandlers()}},n=e=>{t(nP.RESOLVED,e)},r=e=>{t(nP.REJECTED,e)};try{e(n,r)}catch(e){r(e)}}}function tw(e,t){let{extra:n,tags:r,user:i,contexts:a,level:s,sdkProcessingMetadata:o,breadcrumbs:u,fingerprint:l,eventProcessors:c,attachments:d,propagationContext:p,transactionName:h,span:f}=t;tR(e,"extra",n),tR(e,"tags",r),tR(e,"user",i),tR(e,"contexts",a),e.sdkProcessingMetadata=G(e.sdkProcessingMetadata,o,2),s&&(e.level=s),h&&(e.transactionName=h),f&&(e.span=f),u.length&&(e.breadcrumbs=[...e.breadcrumbs,...u]),l.length&&(e.fingerprint=[...e.fingerprint,...l]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...p}}function tR(e,t,n){e[t]=G(e[t],n,1)}let tA="Not capturing exception because it's already been captured.",tI="Discarded session because of missing or non-string release",tP=Symbol.for("SentryInternalError"),tC=Symbol.for("SentryDoNotSendEventError");function tk(e){return{message:e,[tP]:!0}}function tL(e){return{message:e,[tC]:!0}}function tN(e){return!!e&&"object"==typeof e&&tP in e}function tM(e){return!!e&&"object"==typeof e&&tC in e}class tD{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=function(e){let t="string"==typeof e?function(e){let t=ti.exec(e);if(!t)return void R(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[n,r,i="",a="",s="",o=""]=t.slice(1),u="",l=o,c=l.split("/");if(c.length>1&&(u=c.slice(0,-1).join("/"),l=c.pop()),l){let e=l.match(/^\d+/);e&&(l=e[0])}return ts({host:a,pass:i,path:u,projectId:l,port:s,protocol:n,publicKey:r})}(e):ts(e);if(t&&function(e){if(!l)return!0;let{port:t,projectId:n,protocol:r}=e;return!["protocol","publicKey","host","projectId"].find(t=>!e[t]&&(A.error(`Invalid Sentry Dsn: ${t} missing`),!0))&&(n.match(/^\d+$/)?"http"!==r&&"https"!==r?(A.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):!(t&&isNaN(parseInt(t,10)))||(A.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):(A.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(t))return t}(e.dsn):l&&A.warn("No DSN provided, client will not send events."),this._dsn){let t=function(e,t,n){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let n={sentry_version:"7"};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}(e,n)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}}captureException(e,t,n){let r=D();if(B(e))return l&&A.log(tA),r;let i={event_id:r,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,n))),i.event_id}captureMessage(e,t,n,r){let i={event_id:D(),...n},a=y(e)?e:String(e),s=v(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(s.then(e=>this._captureEvent(e,i,r))),i.event_id}captureEvent(e,t,n){let r=D();if(t?.originalException&&B(t.originalException))return l&&A.log(tA),r;let i={event_id:r,...t},a=e.sdkProcessingMetadata||{},s=a.capturedSpanScope,o=a.capturedSpanIsolationScope;return this._process(this._captureEvent(e,i,s||n,o)),i.event_id}captureSession(e){this.sendSession(e),V(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(n=>t.flush(e).then(e=>n&&e))):tb(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];tT(this,e,this._integrations),t||tE(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let n=function(e,t,n,r){var i;let a=tp(n),s=e.type&&"replay_event"!==e.type?e.type:"event";(i=n?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let o=function(e,t,n,r){let i=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:ta(r)},...i&&{trace:i}}}(e,a,r,t);return delete e.sdkProcessingMetadata,tu(o,[[{type:s},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])n=function(e,t){let[n,r]=e;return[n,[...r,t]]}(n,function(e){let t="string"==typeof e.data?tc(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}(e));let r=this.sendEnvelope(n);r&&r.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let{release:t,environment:n=th}=this._options;if("aggregates"in e){let r=e.attrs||{};if(!r.release&&!t){l&&A.warn(tI);return}r.release=r.release||t,r.environment=r.environment||n,e.attrs=r}else{if(!e.release&&!t){l&&A.warn(tI);return}e.release=e.release||t,e.environment=e.environment||n}this.emit("beforeSendSession",e);let r=function(e,t,n,r){let i=tp(n);return tu({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&t&&{dsn:ta(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(r)}recordDroppedEvent(e,t,n=1){if(this._options.sendClientReports){let r=`${e}:${t}`;l&&A.log(`Recording outcome: "${r}"${n>1?` (${n} times)`:""}`),this._outcomes[r]=(this._outcomes[r]||0)+n}}on(e,t){let n=this._hooks[e]=this._hooks[e]||[];return n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}}emit(e,...t){let n=this._hooks[e];n&&n.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>(l&&A.error("Error while sending envelope:",e),e)):(l&&A.error("Transport disabled"),tb({}))}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let n={};return t.forEach(t=>{t&&tT(e,t,n)}),n}(this,e),tE(this,e)}_updateSessionFromEvent(e,t){let n="fatal"===t.level,r=!1,i=t.exception?.values;if(i)for(let e of(r=!0,i)){let t=e.mechanism;if(t?.handled===!1){n=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&n)&&(V(e,{...n&&{status:"crashed"},errors:e.errors||Number(r||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new tx(t=>{let n=0,r=setInterval(()=>{0==this._numProcessing?(clearInterval(r),t(!0)):(n+=1,e&&n>=e&&(clearInterval(r),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,n,s){let o=this.getOptions(),c=Object.keys(this._integrations);return!t.integrations&&c?.length&&(t.integrations=c),this.emit("preprocessEvent",e,t),e.type||s.setLastEventId(e.event_id||t.event_id),(function(e,t,n,s,o,c){var d,p,f,_,m,g;let{normalizeDepth:y=3,normalizeMaxBreadth:v=1e3}=e,E={...t,event_id:t.event_id||n.event_id||D(),timestamp:t.timestamp||F()},S=n.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:n,release:r,dist:i,maxValueLength:a=250}=t;e.environment=e.environment||n||th,!e.release&&r&&(e.release=r),!e.dist&&i&&(e.dist=i);let s=e.request;s?.url&&(s.url=I(s.url,a))})(E,e),d=E,(p=S).length>0&&(d.sdk=d.sdk||{},d.sdk.integrations=[...d.sdk.integrations||[],...p]),o&&o.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let n=function(e){let t=u._sentryDebugIds;if(!t)return{};let n=Object.keys(t);return a&&n.length===i?a:(i=n.length,a=n.reduce((n,i)=>{r||(r={});let a=r[i];if(a)n[a[0]]=a[1];else{let a=e(i);for(let e=a.length-1;e>=0;e--){let s=a[e],o=s?.filename,u=t[i];if(o&&u){n[o]=u,r[i]=[o,u];break}}}return n},{}))}(t);e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.filename&&(e.debug_id=n[e.filename])})})}(E,e.stackParser);let b=function(e,t){if(!t)return e;let n=e?e.clone():new K;return n.update(t),n}(s,n.captureContext);n.mechanism&&U(E,n.mechanism);let O=o?o.getEventProcessors():[],x=h("globalScope",()=>new K).getScopeData();c&&tw(x,c.getScopeData()),b&&tw(x,b.getScopeData());let w=[...n.attachments||[],...x.attachments];w.length&&(n.attachments=w);let{fingerprint:R,span:P,breadcrumbs:C,sdkProcessingMetadata:k}=x;return function(e,t){let{extra:n,tags:r,user:i,contexts:a,level:s,transactionName:o}=t;Object.keys(n).length&&(e.extra={...n,...e.extra}),Object.keys(r).length&&(e.tags={...r,...e.tags}),Object.keys(i).length&&(e.user={...i,...e.user}),Object.keys(a).length&&(e.contexts={...a,...e.contexts}),s&&(e.level=s),o&&"transaction"!==e.type&&(e.transaction=o)}(E,x),P&&function(e,t){e.contexts={trace:eP(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:ty(t),...e.sdkProcessingMetadata};let n=eN(eB(t)).description;n&&!e.transaction&&"transaction"===e.type&&(e.transaction=n)}(E,P),f=E,_=R,f.fingerprint=f.fingerprint?Array.isArray(f.fingerprint)?f.fingerprint:[f.fingerprint]:[],_&&(f.fingerprint=f.fingerprint.concat(_)),f.fingerprint.length||delete f.fingerprint,function(e,t){let n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}(E,C),m=E,g=k,m.sdkProcessingMetadata={...m.sdkProcessingMetadata,...g},(function e(t,n,r,i=0){return new tx((a,s)=>{let o=t[i];if(null===n||"function"!=typeof o)a(n);else{let u=o({...n},r);l&&o.id&&null===u&&A.log(`Event processor "${o.id}" dropped event`),T(u)?u.then(n=>e(t,n,r,i+1).then(a)).then(null,s):e(t,u,r,i+1).then(a).then(null,s)}})})([...O,...x.eventProcessors],E,n).then(e=>(e&&function(e){let t={};if(e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})}),0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let n=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{n.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof y&&y>0)?function(e,t,n){if(!e)return null;let r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:to(e.data,t,n)}}))},...e.user&&{user:to(e.user,t,n)},...e.contexts&&{contexts:to(e.contexts,t,n)},...e.extra&&{extra:to(e.extra,t,n)}};return e.contexts?.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=to(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(e=>({...e,...e.data&&{data:to(e.data,t,n)}}))),e.contexts?.flags&&r.contexts&&(r.contexts.flags=to(e.contexts.flags,3,n)),r}(e,y,v):e)})(o,e,t,n,this,s).then(e=>(null===e||(this.emit("postprocessEvent",e,t),e.contexts={trace:eo(n),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tg(this,n),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},n=er(),r=ei()){return l&&tj(e)&&A.log(`Captured error event \`${tS(e)[0]||"<unknown>"}\``),this._processEvent(e,t,n,r).then(e=>e.event_id,e=>{l&&(tM(e)?A.log(e.message):tN(e)?A.warn(e.message):A.warn(e))})}_processEvent(e,t,n,r){let i=this.getOptions(),{sampleRate:a}=i,s=t$(e),o=tj(e),u=e.type||"error",l=`before send for type \`${u}\``,c=void 0===a?void 0:eE(a);if(o&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error"),tO(tL(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));let d="replay_event"===u?"replay":u;return this._prepareEvent(e,t,n,r).then(e=>{if(null===e)throw this.recordDroppedEvent("event_processor",d),tL("An event processor returned `null`, will not send event.");return t.data&&!0===t.data.__sentry__?e:function(e,t){let n=`${t} must return \`null\` or a valid event.`;if(T(e))return e.then(e=>{if(!E(e)&&null!==e)throw tk(n);return e},e=>{throw tk(`${t} rejected with ${e}`)});if(!E(e)&&null!==e)throw tk(n);return e}(function(e,t,n,r){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:s}=t,o=n;if(tj(o)&&i)return i(o,r);if(t$(o)){if(s){let e=s(function(e){let{trace_id:t,parent_span_id:n,span_id:r,status:i,origin:a,data:s,op:o}=e.contexts?.trace??{};return{data:s??{},description:e.transaction,op:o,parent_span_id:n,span_id:r??"",start_timestamp:e.start_timestamp??0,status:i,timestamp:e.timestamp,trace_id:t??"",origin:a,profile_id:s?.[eh],exclusive_time:s?.[ef],measurements:e.measurements,is_segment:!0}}(o));if(e)o=G(n,{type:"transaction",timestamp:e.timestamp,start_timestamp:e.start_timestamp,transaction:e.description,contexts:{trace:{trace_id:e.trace_id,span_id:e.span_id,parent_span_id:e.parent_span_id,op:e.op,status:e.status,origin:e.origin,data:{...e.data,...e.profile_id&&{[eh]:e.profile_id},...e.exclusive_time&&{[ef]:e.exclusive_time}}}},measurements:e.measurements});else eZ();if(o.spans){let e=[];for(let t of o.spans){let n=s(t);n?e.push(n):(eZ(),e.push(t))}o.spans=e}}if(a){if(o.spans){let e=o.spans.length;o.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(o,r)}}return o}(0,i,e,t),l)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",d),s){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw tL(`${l} returned \`null\`, will not send event.`)}let a=n.getSession()||r.getSession();if(o&&a&&this._updateSessionFromEvent(a,i),s){let e=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let u=i.transaction_info;return s&&u&&i.transaction!==e.transaction&&(i.transaction_info={...u,source:"custom"}),this.sendEvent(i,t),i}).then(null,e=>{if(tM(e)||tN(e))throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),tk(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[n,r]=e.split(":");return{reason:n,category:r,quantity:t}})}_flushOutcomes(){var e;l&&A.log("Flushing outcomes...");let t=this._clearOutcomes();if(0===t.length){l&&A.log("No outcomes to send");return}if(!this._dsn){l&&A.log("No dsn provided, will not send outcomes");return}l&&A.log("Sending outcomes:",t);let n=tu((e=this._options.tunnel&&ta(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:F(),discarded_events:t}]]);this.sendEnvelope(n)}}function tj(e){return void 0===e.type}function t$(e){return"transaction"===e.type}function tU(e,t){if(!t)return[void 0,void 0];let n=t[z],r=n?eP(n):eo(t);return[n?ty(n):tg(e,t),r]}let tB={trace:1,debug:5,info:9,warn:13,error:17,fatal:21};function tF(e,t){let n=t??tZ(e)??[];if(0===n.length)return;let r=e.getOptions(),i=function(e,t,n,r){let i={};return t?.sdk&&(i.sdk={name:t.sdk.name,version:t.sdk.version}),n&&r&&(i.dsn=ta(r)),tu(i,[[{type:"log",item_count:e.length,content_type:"application/vnd.sentry.items.log+json"},{items:e}]])}(n,r._metadata,r.tunnel,e.getDsn());u._sentryClientToLogBufferMap?.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(i)}function tZ(e){return u._sentryClientToLogBufferMap?.get(e)}function tV(e,t){return e(t.stack||"",1)}function tG(e,t){let n={type:t.name||t.constructor.name,value:t.message},r=tV(e,t);return r.length&&(n.stacktrace={frames:r}),n}u._sentryClientToLogBufferMap=new WeakMap;class tz extends tD{constructor(e){if(e3(),super(e),this._logWeight=0,this._options._experiments?.enableLogs){let e=this;e.on("flushLogs",()=>{e._logWeight=0,clearTimeout(e._logFlushIdleTimeout)}),e.on("afterCaptureLog",t=>{e._logWeight+=function(e){let t=0;return e.message&&(t+=2*e.message.length),e.attributes&&Object.values(e.attributes).forEach(e=>{Array.isArray(e)?t+=e.length*tH(e[0]):v(e)?t+=tH(e):t+=100}),t}(t),e._logWeight>=8e5?tF(e):e._logFlushIdleTimeout=setTimeout(()=>{tF(e)},5e3)}),e.on("flush",()=>{tF(e)})}}eventFromException(e,t){let n=function(e,t,n,r){let i=r?.data&&r.data.mechanism||{handled:!0,type:"generic"},[a,s]=function(e,t,n,r){if(_(n))return[n,void 0];if(t.synthetic=!0,E(n)){let t={__serialized__:function e(t,n=3,r=102400){let i=to(t,n);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>r?e(t,n-1,r):i}(n,e?.getOptions().normalizeDepth)},i=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let n=e[t];if(n instanceof Error)return n}}(n);if(i)return[i,t];let a=function(e){if("name"in e&&"string"==typeof e.name){let t=`'${e.name}' captured as exception`;return"message"in e&&"string"==typeof e.message&&(t+=` with message '${e.message}'`),t}if("message"in e&&"string"==typeof e.message)return e.message;let t=function(e,t=40){let n=Object.keys(L(e));n.sort();let r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return I(r,t);for(let e=n.length;e>0;e--){let r=n.slice(0,e).join(", ");if(!(r.length>t)){if(e===n.length)return r;return I(r,t)}}return""}(e);if(m(e,"ErrorEvent"))return`Event \`ErrorEvent\` captured as exception with message \`${e.message}\``;let n=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`${n&&"Object"!==n?`'${n}'`:"Object"} captured as exception with keys: ${t}`}(n),s=r?.syntheticException||Error(a);return s.message=a,[s,t]}let i=r?.syntheticException||Error(n);return i.message=`${n}`,[i,void 0]}(e,i,n,r),o={exception:{values:[tG(t,a)]}};return s&&(o.extra=s),!function(e,t,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],a=i[0]=i[0]||{};a.value||(a.value=t||""),a.type||(a.type=n||"Error")}(o,void 0,void 0),U(o,i),{...o,event_id:r?.event_id}}(this,this._options.stackParser,e,t);return n.level="error",tb(n)}eventFromMessage(e,t="info",n){return tb(function(e,t,n="info",r,i){let a={event_id:r?.event_id,level:n};if(i&&r?.syntheticException){let n=tV(e,r.syntheticException);n.length&&(a.exception={values:[{value:t,stacktrace:{frames:n}}]},U(a,{synthetic:!0}))}if(y(t)){let{__sentry_template_string__:e,__sentry_template_values__:n}=t;return a.logentry={message:e,params:n},a}return a.message=t,a}(this._options.stackParser,e,t,n,this._options.attachStacktrace))}captureException(e,t,n){return tX(t),super.captureException(e,t,n)}captureEvent(e,t,n){return!e.type&&e.exception?.values&&e.exception.values.length>0&&tX(t),super.captureEvent(e,t,n)}captureCheckIn(e,t,n){let r="checkInId"in e&&e.checkInId?e.checkInId:D();if(!this._isEnabled())return l&&A.warn("SDK not enabled, will not capture check-in."),r;let{release:i,environment:a,tunnel:s}=this.getOptions(),o={check_in_id:r,monitor_slug:e.monitorSlug,status:e.status,release:i,environment:a};"duration"in e&&(o.duration=e.duration),t&&(o.monitor_config={schedule:t.schedule,checkin_margin:t.checkinMargin,max_runtime:t.maxRuntime,timezone:t.timezone,failure_issue_threshold:t.failureIssueThreshold,recovery_threshold:t.recoveryThreshold});let[u,c]=tU(this,n);c&&(o.contexts={trace:c});let d=function(e,t,n,r,i){let a={sent_at:new Date().toISOString()};return n?.sdk&&(a.sdk={name:n.sdk.name,version:n.sdk.version}),r&&i&&(a.dsn=ta(i)),t&&(a.trace=t),tu(a,[[{type:"check_in"},e]])}(o,u,this.getSdkMetadata(),s,this.getDsn());return l&&A.info("Sending checkin:",e.monitorSlug,e.status),this.sendEnvelope(d),r}_prepareEvent(e,t,n,r){return this._options.platform&&(e.platform=e.platform||this._options.platform),this._options.runtime&&(e.contexts={...e.contexts,runtime:e.contexts?.runtime||this._options.runtime}),this._options.serverName&&(e.server_name=e.server_name||this._options.serverName),super._prepareEvent(e,t,n,r)}}function tX(e){let t=ei().getScopeData().sdkProcessingMetadata.requestSession;if(t){let n=e?.mechanism?.handled??!0;n&&"crashed"!==t.status?t.status="errored":n||(t.status="crashed")}}function tH(e){return"string"==typeof e?2*e.length:"number"==typeof e?8:4*("boolean"==typeof e)}class tK{constructor(e){this._maxSize=e,this._cache=new Map}get size(){return this._cache.size}get(e){let t=this._cache.get(e);if(void 0!==t)return this._cache.delete(e),this._cache.set(e,t),t}set(e,t){this._cache.size>=this._maxSize&&this._cache.delete(this._cache.keys().next().value),this._cache.set(e,t)}remove(e){let t=this._cache.get(e);return t&&this._cache.delete(e),t}clear(){this._cache.clear()}keys(){return Array.from(this._cache.keys())}values(){let e=[];return this._cache.forEach(t=>e.push(t)),e}}function tY(e,t,n=()=>{}){var r,i,a;let s;try{s=e()}catch(e){throw t(e),n(),e}return r=s,i=t,a=n,T(r)?r.then(e=>(a(),e),e=>{throw i(e),a(),e}):(a(),r)}function tW(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let n=e.attributes||{},r=n["sentry.measurement_unit"],i=n["sentry.measurement_value"];"string"==typeof r&&"number"==typeof i&&(t[e.name]={value:i,unit:r})}),t}async function tq(e){let t=es();return t?t.flush(e):(l&&A.warn("Cannot flush events. No client defined."),Promise.resolve(!1))}function tJ(e){if(!l)return;let{description:t="< unknown name >",op:n="< unknown op >",parent_span_id:r}=eN(e),{spanId:i}=e.spanContext(),a=eM(e),s=eB(e),o=s===e,u=`[Tracing] Starting ${a?"sampled":"unsampled"} ${o?"root ":""}span`,c=[`op: ${n}`,`name: ${t}`,`ID: ${i}`];if(r&&c.push(`parent ID: ${r}`),!o){let{op:e,description:t}=eN(s);c.push(`root ID: ${s.spanContext().spanId}`),e&&c.push(`root op: ${e}`),t&&c.push(`root description: ${t}`)}A.log(`${u}
  ${c.join("\n  ")}`)}function tQ(e){if(!l)return;let{description:t="< unknown name >",op:n="< unknown op >"}=eN(e),{spanId:r}=e.spanContext(),i=eB(e)===e,a=`[Tracing] Finishing "${n}" ${i?"root ":""}span "${t}" with ID ${r}`;A.log(a)}function t0(e,t,n){let r,i;if(!tf(e))return[!1];"function"==typeof e.tracesSampler?(r=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),i=!0):void 0!==t.parentSampled?r=t.parentSampled:void 0!==e.tracesSampleRate&&(r=e.tracesSampleRate,i=!0);let a=eE(r);if(void 0===a)return l&&A.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(r)} of type ${JSON.stringify(typeof r)}.`),[!1];if(!a)return l&&A.log(`[Tracing] Discarding transaction because ${"function"==typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),[!1,a,i];let s=n<a;return!s&&l&&A.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),[s,a,i]}async function t1(e,t){if(e?.body){let n=e.body,r=n.getReader(),i=setTimeout(()=>{n.cancel().then(null,()=>{})},9e4),a=!0;for(;a;){let e;try{e=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);let{done:i}=await r.read();clearTimeout(e),i&&(t(),a=!1)}catch(e){a=!1}finally{clearTimeout(e)}}clearTimeout(i),r.releaseLock(),n.cancel().then(null,()=>{})}}function t2(e,t){return!!e&&"object"==typeof e&&!!e[t]}function t4(e){return"string"==typeof e?e:e?t2(e,"url")?e.url:e.toString?e.toString():"":""}function t9(e){return"/"===e[e.length-1]?e.slice(0,-1):e}class t5{constructor(e={}){this._traceId=e.traceId||D(),this._spanId=e.spanId||H()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}class t3{constructor(e={}){this._traceId=e.traceId||D(),this._spanId=e.spanId||H(),this._startTime=e.startTimestamp||Z(),this._links=e.links,this._attributes={},this.setAttributes({[ed]:"manual",[ec]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:+!!n}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=ek(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(eu,"custom"),this}end(e){this._endTime||(this._endTime=ek(e),tQ(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[ec],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:eD(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[ed],profile_id:this._attributes[eh],exclusive_time:this._attributes[ef],measurements:tW(this._events),is_segment:this._isStandaloneSpan&&eB(this)===this||void 0,segment_id:this._isStandaloneSpan?eB(this).spanContext().spanId:void 0,links:eC(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){l&&A.log("[Tracing] Adding an event to span:",e);let r=t6(t)?t:n||Z(),i=t6(t)?{}:t||{},a={name:e,time:ek(r),attributes:i};return this._events.push(a),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=es();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===eB(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=es();if(!t)return;let n=e[1];if(!n||0===n.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}(function(e,t){let n=ty(e[0]),r=t?.getDsn(),i=t?.getOptions().tunnel,a={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!i&&r&&{dsn:ta(r)}},s=t?.getOptions().beforeSendSpan,o=s?e=>{let t=eN(e),n=s(t);return n||(eZ(),t)}:eN,u=[];for(let t of e){let e=o(t);e&&u.push([{type:"span"},e])}return tu(a,u)}([this],e)):(l&&A.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));let t=this._convertSpanToTransaction();t&&(ev(this).scope||er()).captureEvent(t)}_convertSpanToTransaction(){if(!t8(eN(this)))return;this._name||(l&&A.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=ev(this),n=e?.getScopeData().sdkProcessingMetadata?.normalizedRequest;if(!0!==this._sampled)return;let r=(function(e){let t=new Set;return!function e(n){if(!t.has(n)&&eM(n))for(let r of(t.add(n),n[ej]?Array.from(n[ej]):[]))e(r)}(e),Array.from(t)})(this).filter(e=>{var t;return e!==this&&!((t=e)instanceof t3&&t.isStandaloneSpan())}).map(e=>eN(e)).filter(t8),i=this._attributes[eu];delete this._attributes[ep],r.forEach(e=>{delete e.data[ep]});let a={contexts:{trace:function(e){let{spanId:t,traceId:n}=e.spanContext(),{data:r,op:i,parent_span_id:a,status:s,origin:o,links:u}=eN(e);return{parent_span_id:a,span_id:t,trace_id:n,data:r,op:i,status:s,origin:o,links:u}}(this)},spans:r.length>1e3?r.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,dynamicSamplingContext:ty(this)},request:n,...i&&{transaction_info:{source:i}}},s=tW(this._events);return s&&Object.keys(s).length&&(l&&A.log("[Measurements] Adding measurements to transaction event",JSON.stringify(s,void 0,2)),a.measurements=s),a}}function t6(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function t8(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let t7="__SENTRY_SUPPRESS_TRACING__";function ne(e,t){let n=function(){return en(d())}();return n.withActiveSpan?n.withActiveSpan(e,t):ea(n=>(X(n,e||void 0),t(n)))}function nt(){return en(d())}function nn(e,t,n){let r=es(),i=r?.getOptions()||{},{name:a=""}=e,s={spanAttributes:{...e.attributes},spanName:a,parentSampled:n};r?.emit("beforeSampling",s,{decision:!1});let o=s.parentSampled??n,u=s.spanAttributes,c=t.getPropagationContext(),[d,p,h]=t.getScopeData().sdkProcessingMetadata[t7]?[!1]:t0(i,{name:a,parentSampled:o,attributes:u,parentSampleRate:eE(c.dsc?.sample_rate)},c.sampleRand),f=new t3({...e,attributes:{[eu]:"custom",[el]:void 0!==p&&h?p:void 0,...u},sampled:d});return!d&&r&&(l&&A.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",f),f}function nr(e){return e.split(",").some(e=>e.trim().startsWith(eT))}function ni(e,t){let n=es(),r=ei();if(!n)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:a=100}=n.getOptions();if(a<=0)return;let s={timestamp:F(),...e},o=i?R(()=>i(s,t)):s;null!==o&&(n.emit&&n.emit("beforeAddBreadcrumb",o,t),r.addBreadcrumb(o,a))}let na=Symbol.for("SentryBufferFullError");function ns(e){return parseInt(e||"",10)||void 0}let no=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var n,r;if(n=t,(r=e)&&(function(e,t){let n=e.message,r=t.message;return(!!n||!!r)&&(!n||!!r)&&(!!n||!r)&&n===r&&!!nl(e,t)&&!!nu(e,t)&&!0}(n,r)||function(e,t){let n=nc(t),r=nc(e);return!!n&&!!r&&n.type===r.type&&n.value===r.value&&!!nl(e,t)&&!!nu(e,t)}(n,r)))return l&&A.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return e=t}}};function nu(e,t){let n=eY(e),r=eY(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let e=0;e<r.length;e++){let t=r[e],i=n[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function nl(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch(e){return!1}}function nc(e){return e.exception?.values&&e.exception.values[0]}let nd=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],np=(e={})=>{let t;return{name:"EventFilters",setup(n){t=nf(e,n.getOptions())},processEvent:(n,r,i)=>(t||(t=nf(e,i.getOptions())),!function(e,t){if(e.type){if("transaction"===e.type&&function(e,t){if(!t?.length)return!1;let n=e.transaction;return!!n&&P(n,t)}(e,t.ignoreTransactions))return l&&A.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${$(e)}`),!0}else{var n,r,i;if(n=e,r=t.ignoreErrors,r?.length&&tS(n).some(e=>P(e,r)))return l&&A.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${$(e)}`),!0;if(i=e,i.exception?.values?.length&&!i.message&&!i.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value))return l&&A.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${$(e)}`),!0;if(function(e,t){if(!t?.length)return!1;let n=n_(e);return!!n&&P(n,t)}(e,t.denyUrls))return l&&A.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${$(e)}.
Url: ${n_(e)}`),!0;if(!function(e,t){if(!t?.length)return!0;let n=n_(e);return!n||P(n,t)}(e,t.allowUrls))return l&&A.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${$(e)}.
Url: ${n_(e)}`),!0}return!1}(n,t)?n:null)}},nh=(e={})=>({...np(e),name:"InboundFilters"});function nf(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:nd],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function n_(e){try{let t=[...e.exception?.values??[]].reverse().find(e=>e.mechanism?.parent_id===void 0&&e.stacktrace?.frames?.length),n=t?.stacktrace?.frames;return n?function(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(t){return l&&A.error(`Cannot extract url for event ${$(e)}`),null}}let nm=new WeakMap,ng=()=>({name:"FunctionToString",setupOnce(){s=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=this.__sentry_original__,n=nm.has(es())&&void 0!==t?t:this;return s.apply(n,e)}}catch{}},setup(e){nm.set(e,!0)}});function ny(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function nv(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}let nE=(e={})=>{let t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,r,i){!function(e,t,n,r,i,a){if(!i.exception?.values||!a||!S(a.originalException,Error))return;let s=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;s&&(i.exception.values=function e(t,n,r,i,a,s,o,u){if(s.length>=r+1)return s;let l=[...s];if(S(i[a],Error)){ny(o,u);let s=t(n,i[a]),c=l.length;nv(s,a,c,u),l=e(t,n,r,i[a],a,[s,...l],s,c)}return Array.isArray(i.errors)&&i.errors.forEach((i,s)=>{if(S(i,Error)){ny(o,u);let c=t(n,i),d=l.length;nv(c,`errors[${s}]`,d,u),l=e(t,n,r,i,a,[c,...l],c,d)}}),l}(e,t,r,a.originalException,n,i.exception.values,s,0))}(tG,i.getOptions().stackParser,n,t,e,r)}}};function nT(){"console"in u&&x.forEach(function(e){e in u.console&&C(u.console,e,function(t){return w[e]=t,function(...t){e0("console",{args:t,level:e});let n=w[e];n?.apply(u.console,t)}})})}let nS=(e={})=>{let t=new Set(e.levels||x);return{name:"Console",setup(e){let n="console";eJ(n,({args:n,level:r})=>{es()===e&&t.has(r)&&function(e,t){let n={category:"console",data:{arguments:t,logger:"console"},level:"warn"===e?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log",message:nb(t)};if("assert"===e)if(!1!==t[0])return;else{let e=t.slice(1);n.message=e.length>0?`Assertion failed: ${nb(e)}`:"Assertion failed",n.data.arguments=e}ni(n,{input:t,level:e})}(r,n)}),eQ(n,nT)}}};function nb(e){return"util"in u&&"function"==typeof u.util.format?u.util.format(...e):function(e,t){if(!Array.isArray(e))return"";let n=[];for(let t=0;t<e.length;t++){let r=e[t];try{b(r)?n.push("[VueViewModel]"):n.push(String(r))}catch(e){n.push("[value cannot be serialized]")}}return n.join(" ")}(e,0)}let nO=["X-Client-IP","X-Forwarded-For","Fly-Client-IP","CF-Connecting-IP","Fastly-Client-Ip","True-Client-Ip","X-Real-IP","X-Cluster-Client-IP","X-Forwarded","Forwarded-For","Forwarded","X-Vercel-Forwarded-For"],nx={cookies:!0,data:!0,headers:!0,query_string:!0,url:!0},nw=(e={})=>{let t={...nx,...e.include};return{name:"RequestData",processEvent(e,n,r){let{sdkProcessingMetadata:i={}}=e,{normalizedRequest:a,ipAddress:s}=i,o={...t,ip:t.ip??r.getOptions().sendDefaultPii};return a&&function(e,t,n,r){if(e.request={...e.request,...function(e,t){let n={},r={...e.headers};return t.headers&&(n.headers=r,t.cookies||delete r.cookie,t.ip||nO.forEach(e=>{delete r[e]})),n.method=e.method,t.url&&(n.url=e.url),t.cookies&&(n.cookies=e.cookies||(r?.cookie?function(e){let t={},n=0;for(;n<e.length;){let r=e.indexOf("=",n);if(-1===r)break;let i=e.indexOf(";",n);if(-1===i)i=e.length;else if(i<r){n=e.lastIndexOf(";",r-1)+1;continue}let a=e.slice(n,r).trim();if(void 0===t[a]){let n=e.slice(r+1,i).trim();34===n.charCodeAt(0)&&(n=n.slice(1,-1));try{t[a]=-1!==n.indexOf("%")?decodeURIComponent(n):n}catch(e){t[a]=n}}n=i+1}return t}(r.cookie):void 0)||{}),t.query_string&&(n.query_string=e.query_string),t.data&&(n.data=e.data),n}(t,r)},r.ip){var i;let r=t.headers&&(i=t.headers,nO.map(e=>{let t=i[e],n=Array.isArray(t)?t.join(";"):t;return"Forwarded"===e?function(e){if(!e)return null;for(let t of e.split(";"))if(t.startsWith("for="))return t.slice(4);return null}(n):n?.split(",").map(e=>e.trim())}).reduce((e,t)=>t?e.concat(t):e,[]).find(e=>{var t;return null!==e&&(t=e,/(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-fA-F\d]{1,4}:){7}(?:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,2}|:)|(?:[a-fA-F\d]{1,4}:){4}(?:(?::[a-fA-F\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,3}|:)|(?:[a-fA-F\d]{1,4}:){3}(?:(?::[a-fA-F\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,4}|:)|(?:[a-fA-F\d]{1,4}:){2}(?:(?::[a-fA-F\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,5}|:)|(?:[a-fA-F\d]{1,4}:){1}(?:(?::[a-fA-F\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/.test(t))})||null)||n.ipAddress;r&&(e.user={...e.user,ip_address:r})}}(e,a,{ipAddress:s},o),e}}};function nR(e){return Symbol.for(e)}var nA,nI,nP,nC,nk,nL,nN,nM,nD,nj,n$,nU,nB,nF,nZ,nV,nG,nz,nX,nH=new function e(t){var n=this;n._currentContext=t?new Map(t):new Map,n.getValue=function(e){return n._currentContext.get(e)},n.setValue=function(t,r){var i=new e(n._currentContext);return i._currentContext.set(t,r),i},n.deleteValue=function(t){var r=new e(n._currentContext);return r._currentContext.delete(t),r}},nK=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},nY=function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},nW=function(){function e(){}return e.prototype.active=function(){return nH},e.prototype.with=function(e,t,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];return t.call.apply(t,nY([n],nK(r),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),nq="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof n.g?n.g:{},nJ="1.9.0",nQ=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,n0=function(e){var t=new Set([e]),n=new Set,r=e.match(nQ);if(!r)return function(){return!1};var i={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=i.prerelease)return function(t){return t===e};function a(e){return n.add(e),!1}return function(e){if(t.has(e))return!0;if(n.has(e))return!1;var r=e.match(nQ);if(!r)return a(e);var s={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=s.prerelease||i.major!==s.major)return a(e);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):a(e);return i.minor<=s.minor?(t.add(e),!0):a(e)}}(nJ),n1=Symbol.for("opentelemetry.js.api."+nJ.split(".")[0]);function n2(e,t,n,r){void 0===r&&(r=!1);var i,a=nq[n1]=null!=(i=nq[n1])?i:{version:nJ};if(!r&&a[e]){var s=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return n.error(s.stack||s.message),!1}if(a.version!==nJ){var s=Error("@opentelemetry/api: Registration of version v"+a.version+" for "+e+" does not match previously registered API v"+nJ);return n.error(s.stack||s.message),!1}return a[e]=t,n.debug("@opentelemetry/api: Registered a global for "+e+" v"+nJ+"."),!0}function n4(e){var t,n,r=null==(t=nq[n1])?void 0:t.version;if(r&&n0(r))return null==(n=nq[n1])?void 0:n[e]}function n9(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+nJ+".");var n=nq[n1];n&&delete n[e]}var n5=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},n3=function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},n6=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n8("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n8("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n8("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n8("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n8("verbose",this._namespace,e)},e}();function n8(e,t,n){var r=n4("diag");if(r)return n.unshift(t),r[e].apply(r,n3([],n5(n),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(nC||(nC={}));var n7=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},re=function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},rt=function(){function e(){function e(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=n4("diag");if(r)return r[e].apply(r,re([],n7(t),!1))}}var t=this;t.setLogger=function(e,n){if(void 0===n&&(n={logLevel:nC.INFO}),e===t){var r,i,a,s=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(r=s.stack)?r:s.message),!1}"number"==typeof n&&(n={logLevel:n});var o=n4("diag"),u=function(e,t){function n(n,r){var i=t[n];return"function"==typeof i&&e>=r?i.bind(t):function(){}}return e<nC.NONE?e=nC.NONE:e>nC.ALL&&(e=nC.ALL),t=t||{},{error:n("error",nC.ERROR),warn:n("warn",nC.WARN),info:n("info",nC.INFO),debug:n("debug",nC.DEBUG),verbose:n("verbose",nC.VERBOSE)}}(null!=(i=n.logLevel)?i:nC.INFO,e);if(o&&!n.suppressOverrideMessage){var l=null!=(a=Error().stack)?a:"<failed to generate stacktrace>";o.warn("Current logger will be overwritten from "+l),u.warn("Current logger will overwrite one already registered from "+l)}return n2("diag",u,t,!0)},t.disable=function(){n9("diag",t)},t.createComponentLogger=function(e){return new n6(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),rn=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},rr=function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},ri="context",ra=new nW,rs=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return n2(ri,e,rt.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,n){for(var r,i=[],a=3;a<arguments.length;a++)i[a-3]=arguments[a];return(r=this._getContextManager()).with.apply(r,rr([e,t,n],rn(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return n4(ri)||ra},e.prototype.disable=function(){this._getContextManager().disable(),n9(ri,rt.instance())},e}(),ro=rs.getInstance();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(nk||(nk={}));var ru="0000000000000000",rl="00000000000000000000000000000000",rc={traceId:rl,spanId:ru,traceFlags:nk.NONE},rd=function(){function e(e){void 0===e&&(e=rc),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),rp=nR("OpenTelemetry Context Key SPAN");function rh(e){return e.getValue(rp)||void 0}function rf(){return rh(rs.getInstance().active())}function r_(e,t){return e.setValue(rp,t)}function rm(e){return e.deleteValue(rp)}function rg(e,t){return r_(e,new rd(t))}function ry(e){var t;return null==(t=rh(e))?void 0:t.spanContext()}var rv=/^([0-9a-f]{32})$/i,rE=/^[0-9a-f]{16}$/i;function rT(e){return rv.test(e)&&e!==rl}function rS(e){var t;return rT(e.traceId)&&(t=e.spanId,rE.test(t)&&t!==ru)}function rb(e){return new rd(e)}var rO=rs.getInstance(),rx=function(){function e(){}return e.prototype.startSpan=function(e,t,n){if(void 0===n&&(n=rO.active()),null==t?void 0:t.root)return new rd;var r,i=n&&ry(n);return"object"==typeof(r=i)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&rS(i)?new rd(i):new rd},e.prototype.startActiveSpan=function(e,t,n,r){if(!(arguments.length<2)){2==arguments.length?s=t:3==arguments.length?(i=t,s=n):(i=t,a=n,s=r);var i,a,s,o=null!=a?a:rO.active(),u=this.startSpan(e,i,o),l=r_(o,u);return rO.with(l,s,void 0,u)}},e}(),rw=new rx,rR=function(){function e(e,t,n,r){this._provider=e,this.name=t,this.version=n,this.options=r}return e.prototype.startSpan=function(e,t,n){return this._getTracer().startSpan(e,t,n)},e.prototype.startActiveSpan=function(e,t,n,r){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):rw},e}(),rA=new(function(){function e(){}return e.prototype.getTracer=function(e,t,n){return new rx},e}()),rI=function(){function e(){}return e.prototype.getTracer=function(e,t,n){var r;return null!=(r=this.getDelegateTracer(e,t,n))?r:new rR(this,e,t,n)},e.prototype.getDelegate=function(){var e;return null!=(e=this._delegate)?e:rA},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,n){var r;return null==(r=this._delegate)?void 0:r.getTracer(e,t,n)},e}(),rP="trace",rC=(function(){function e(){this._proxyTracerProvider=new rI,this.wrapSpanContext=rb,this.isSpanContextValid=rS,this.deleteSpan=rm,this.getSpan=rh,this.getActiveSpan=rf,this.getSpanContext=ry,this.setSpan=r_,this.setSpanContext=rg}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=n2(rP,this._proxyTracerProvider,rt.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return n4(rP)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){n9(rP,rt.instance()),this._proxyTracerProvider=new rI},e})().getInstance(),rk=rt.instance();!function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(nL||(nL={})),function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(nN||(nN={}));var rL=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},rN=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},rM=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=rL(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,n){var r=new e(this._entries);return r._entries.set(t,n),r},e.prototype.removeEntry=function(t){var n=new e(this._entries);return n._entries.delete(t),n},e.prototype.removeEntries=function(){for(var t,n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var a=new e(this._entries);try{for(var s=rN(r),o=s.next();!o.done;o=s.next()){var u=o.value;a._entries.delete(u)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return a},e.prototype.clear=function(){return new e},e}(),rD=Symbol("BaggageEntryMetadata"),rj=rt.instance();function r$(e){return void 0===e&&(e={}),new rM(new Map(Object.entries(e)))}var rU=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),rB={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},rF={set:function(e,t,n){null!=e&&(e[t]=n)}},rZ=nR("OpenTelemetry Baggage Key");function rV(e){return e.getValue(rZ)||void 0}function rG(){return rV(rs.getInstance().active())}function rz(e,t){return e.setValue(rZ,t)}function rX(e){return e.deleteValue(rZ)}var rH="propagation",rK=new rU,rY=(function(){function e(){this.createBaggage=r$,this.getBaggage=rV,this.getActiveBaggage=rG,this.setBaggage=rz,this.deleteBaggage=rX}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return n2(rH,e,rt.instance())},e.prototype.inject=function(e,t,n){return void 0===n&&(n=rF),this._getGlobalPropagator().inject(e,t,n)},e.prototype.extract=function(e,t,n){return void 0===n&&(n=rB),this._getGlobalPropagator().extract(e,t,n)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){n9(rH,rt.instance())},e.prototype._getGlobalPropagator=function(){return n4(rH)||rK},e})().getInstance();!function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(nM||(nM={}));var rW=n(356).Buffer;void 0===globalThis.performance&&(globalThis.performance={timeOrigin:0,now:()=>Date.now()});class rq extends tz{constructor(e){e8(e,"vercel-edge"),e._metadata=e._metadata||{},super({...e,platform:"javascript",runtime:{name:"vercel-edge"},serverName:e.serverName||process.env.SENTRY_NAME})}async flush(e){let t=this.traceProvider,n=t?.activeSpanProcessor;return n&&await n.forceFlush(),this.getOptions().sendClientReports&&this._flushOutcomes(),super.flush(e)}}var rJ="telemetry.sdk.name",rQ="telemetry.sdk.language",r0="telemetry.sdk.version",r1=nR("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function r2(e){return e.setValue(r1,!0)}function r4(e){return!0===e.getValue(r1)}var r9="baggage",r5=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},r3=function(){function e(){}return e.prototype.inject=function(e,t,n){var r=rY.getBaggage(e);if(!(!r||r4(e))){var i=r.getAllEntries().map(function(e){var t=r5(e,2),n=t[0],r=t[1],i=encodeURIComponent(n)+"="+encodeURIComponent(r.value);return void 0!==r.metadata&&(i+=";"+r.metadata.toString()),i}).filter(function(e){return e.length<=4096}).slice(0,180).reduce(function(e,t){var n=""+e+(""!==e?",":"")+t;return n.length>8192?e:n},"");i.length>0&&n.set(t,r9,i)}},e.prototype.extract=function(e,t,n){var r=n.get(t,r9),i=Array.isArray(r)?r.join(","):r;if(!i)return e;var a={};return 0===i.length||(i.split(",").forEach(function(e){var t=function(e){var t,n=e.split(";");if(!(n.length<=0)){var r=n.shift();if(r){var i=r.indexOf("=");if(!(i<=0)){var a,s=decodeURIComponent(r.substring(0,i).trim()),o=decodeURIComponent(r.substring(i+1).trim());return n.length>0&&("string"!=typeof(a=n.join(";"))&&(rj.error("Cannot create baggage metadata from unknown type: "+typeof a),a=""),t={__TYPE__:rD,toString:function(){return a}}),{key:s,value:o,metadata:t}}}}}(e);if(t){var n={value:t.value};t.metadata&&(n.metadata=t.metadata),a[t.key]=n}}),0===Object.entries(a).length)?e:rY.setBaggage(e,rY.createBaggage(a))},e.prototype.fields=function(){return[r9]},e}(),r6=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},r8=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s};function r7(e){var t,n,r={};if("object"!=typeof e||null==e)return r;try{for(var i=r6(Object.entries(e)),a=i.next();!a.done;a=i.next()){var s,o=r8(a.value,2),u=o[0],l=o[1];if(s=u,"string"!=typeof s||!(s.length>0)){rk.warn("Invalid attribute key: "+u);continue}if(!ie(l)){rk.warn("Invalid attribute value set for key: "+u);continue}Array.isArray(l)?r[u]=l.slice():r[u]=l}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return r}function ie(e){return null==e||(Array.isArray(e)?function(e){try{for(var t,n,r,i=r6(e),a=i.next();!a.done;a=i.next()){var s=a.value;if(null!=s){if(!r){if(it(s)){r=typeof s;continue}return!1}if(typeof s!==r)return!1}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return!0}(e):it(e))}function it(e){switch(typeof e){case"number":case"boolean":case"string":return!0}return!1}var ir=function(e){var t;rk.error("string"==typeof(t=e)?t:JSON.stringify(function(e){for(var t={},n=e;null!==n;)Object.getOwnPropertyNames(n).forEach(function(e){if(!t[e]){var r=n[e];r&&(t[e]=String(r))}}),n=Object.getPrototypeOf(n);return t}(t)))};function ii(e){try{ir(e)}catch(e){}}!function(e){e.AlwaysOff="always_off",e.AlwaysOn="always_on",e.ParentBasedAlwaysOff="parentbased_always_off",e.ParentBasedAlwaysOn="parentbased_always_on",e.ParentBasedTraceIdRatio="parentbased_traceidratio",e.TraceIdRatio="traceidratio"}(nD||(nD={}));var ia=["OTEL_SDK_DISABLED"],is=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"],io=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"],iu=1/0,il={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:nC.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:iu,OTEL_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:iu,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:128,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:iu,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:128,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:nD.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]},ic={ALL:nC.ALL,VERBOSE:nC.VERBOSE,DEBUG:nC.DEBUG,INFO:nC.INFO,WARN:nC.WARN,ERROR:nC.ERROR,NONE:nC.NONE};function id(e){var t={};for(var n in il)if("OTEL_LOG_LEVEL"===n)!function(e,t,n){var r=n[e];if("string"==typeof r){var i=ic[r.toUpperCase()];null!=i&&(t[e]=i)}}(n,t,e);else if(ia.indexOf(n)>-1){if(void 0!==e[n]){var r=String(e[n]);t[n]="true"===r.toLowerCase()}}else if(is.indexOf(n)>-1)!function(e,t,n,r,i){if(void 0===r&&(r=-1/0),void 0===i&&(i=1/0),void 0!==n[e]){var a=Number(n[e]);isNaN(a)||(a<r?t[e]=r:a>i?t[e]=i:t[e]=a)}}(n,t,e);else if(io.indexOf(n)>-1)!function(e,t,n,r){void 0===r&&(r=",");var i=n[e];"string"==typeof i&&(t[e]=i.split(r).map(function(e){return e.trim()}))}(n,t,e);else{var i=e[n];null!=i&&(t[n]=String(i))}return t}function ip(){return Object.assign({},il,id(process.env))}var ih={timeOrigin:0,now:()=>Date.now()},i_=((nj={})["telemetry.sdk.name"]="opentelemetry",nj["process.runtime.name"]="node",nj["telemetry.sdk.language"]="nodejs",nj["telemetry.sdk.version"]="1.30.1",nj);function im(e){return[Math.trunc(e/1e3),Math.round(e%1e3*1e6)]}function ig(){return ih.timeOrigin}function iy(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function iv(e){return iy(e)||"number"==typeof e||e instanceof Date}function iE(e,t){var n=[e[0]+t[0],e[1]+t[1]];return n[1]>=1e9&&(n[1]-=1e9,n[0]+=1),n}!function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILED=1]="FAILED"}(n$||(n$={}));var iT=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},iS=function(){function e(e){var t;void 0===e&&(e={}),this._propagators=null!=(t=e.propagators)?t:[],this._fields=Array.from(new Set(this._propagators.map(function(e){return"function"==typeof e.fields?e.fields():[]}).reduce(function(e,t){return e.concat(t)},[])))}return e.prototype.inject=function(e,t,n){var r,i;try{for(var a=iT(this._propagators),s=a.next();!s.done;s=a.next()){var o=s.value;try{o.inject(e,t,n)}catch(e){rk.warn("Failed to inject with "+o.constructor.name+". Err: "+e.message)}}}catch(e){r={error:e}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(r)throw r.error}}},e.prototype.extract=function(e,t,n){return this._propagators.reduce(function(e,r){try{return r.extract(e,t,n)}catch(e){rk.warn("Failed to extract with "+r.constructor.name+". Err: "+e.message)}return e},e)},e.prototype.fields=function(){return this._fields.slice()},e}(),ib="[_0-9a-z-*/]",iO=RegExp("^(?:[a-z]"+ib+"{0,255}|"+("[a-z0-9]"+ib+"{0,240}@[a-z]")+ib+"{0,13})$"),ix=/^[ -~]{0,255}[!-~]$/,iw=/,|=/,iR=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var n=this._clone();return n._internalState.has(e)&&n._internalState.delete(e),n._internalState.set(e,t),n},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,n){return t.push(n+"="+e.get(n)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var n=t.trim(),r=n.indexOf("=");if(-1!==r){var i=n.slice(0,r),a=n.slice(r+1,t.length);iO.test(i)&&ix.test(a)&&!iw.test(a)&&e.set(i,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}(),iA="traceparent",iI="tracestate",iP=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$"),iC=function(){function e(){}return e.prototype.inject=function(e,t,n){var r=rC.getSpanContext(e);if(!(!r||r4(e))&&rS(r)){var i="00-"+r.traceId+"-"+r.spanId+"-0"+Number(r.traceFlags||nk.NONE).toString(16);n.set(t,iA,i),r.traceState&&n.set(t,iI,r.traceState.serialize())}},e.prototype.extract=function(e,t,n){var r,i=n.get(t,iA);if(!i)return e;var a=Array.isArray(i)?i[0]:i;if("string"!=typeof a)return e;var s=(r=iP.exec(a))&&("00"!==r[1]||!r[5])?{traceId:r[2],spanId:r[3],traceFlags:parseInt(r[4],16)}:null;if(!s)return e;s.isRemote=!0;var o=n.get(t,iI);if(o){var u=Array.isArray(o)?o.join(","):o;s.traceState=new iR("string"==typeof u?u:void 0)}return rC.setSpanContext(e,s)},e.prototype.fields=function(){return[iA,iI]},e}(),ik=Function.prototype.toString,iL=ik.call(Object),iN=(nA=Object.getPrototypeOf,nI=Object,function(e){return nA(nI(e))}),iM=Object.prototype,iD=iM.hasOwnProperty,ij=Symbol?Symbol.toStringTag:void 0,i$=iM.toString;function iU(e){if(null==(t=e)||"object"!=typeof t||"[object Object]"!==(null==(n=e)?void 0===n?"[object Undefined]":"[object Null]":ij&&ij in Object(n)?function(e){var t=iD.call(e,ij),n=e[ij],r=!1;try{e[ij]=void 0,r=!0}catch(e){}var i=i$.call(e);return r&&(t?e[ij]=n:delete e[ij]),i}(n):(r=n,i$.call(r))))return!1;var t,n,r,i=iN(e);if(null===i)return!0;var a=iD.call(i,"constructor")&&i.constructor;return"function"==typeof a&&a instanceof a&&ik.call(a)===iL}function iB(e){return iZ(e)?e.slice():e}function iF(e,t,n){for(var r=n.get(e[t])||[],i=0,a=r.length;i<a;i++){var s=r[i];if(s.key===t&&s.obj===e)return!0}return!1}function iZ(e){return Array.isArray(e)}function iV(e){return"function"==typeof e}function iG(e){return!iz(e)&&!iZ(e)&&!iV(e)&&"object"==typeof e}function iz(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||void 0===e||e instanceof Date||e instanceof RegExp||null===e}var iX=function(){function e(){var e=this;this._promise=new Promise(function(t,n){e._resolve=t,e._reject=n})}return Object.defineProperty(e.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),e.prototype.resolve=function(e){this._resolve(e)},e.prototype.reject=function(e){this._reject(e)},e}(),iH=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},iK=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},iY=function(){function e(e,t){this._callback=e,this._that=t,this._isCalled=!1,this._deferred=new iX}return Object.defineProperty(e.prototype,"isCalled",{get:function(){return this._isCalled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"promise",{get:function(){return this._deferred.promise},enumerable:!1,configurable:!0}),e.prototype.call=function(){for(var e,t=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!this._isCalled){this._isCalled=!0;try{Promise.resolve((e=this._callback).call.apply(e,iK([this._that],iH(n),!1))).then(function(e){return t._deferred.resolve(e)},function(e){return t._deferred.reject(e)})}catch(e){this._deferred.reject(e)}}return this._deferred.promise},e}(),iW=globalThis&&globalThis.__assign||function(){return(iW=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},iq=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,a){function s(e){try{u(r.next(e))}catch(e){a(e)}}function o(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,o)}u((r=r.apply(e,t||[])).next())})},iJ=globalThis&&globalThis.__generator||function(e,t){var n,r,i,a,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){var u=[a,o];if(n)throw TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,r=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){s=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){s.label=u[1];break}if(6===u[0]&&s.label<i[1]){s.label=i[1],i=u;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(u);break}i[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},iQ=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},i0=function(){function e(e,t){var n,r=this;this._attributes=e,this.asyncAttributesPending=null!=t,this._syncAttributes=null!=(n=this._attributes)?n:{},this._asyncAttributesPromise=null==t?void 0:t.then(function(e){return r._attributes=Object.assign({},r._attributes,e),r.asyncAttributesPending=!1,e},function(e){return rk.debug("a resource's async attributes promise rejected: %s",e),r.asyncAttributesPending=!1,{}})}return e.empty=function(){return e.EMPTY},e.default=function(){var t;return new e(((t={})["service.name"]="unknown_service:",t[rQ]=i_[rQ],t[rJ]=i_[rJ],t[r0]=i_[r0],t))},Object.defineProperty(e.prototype,"attributes",{get:function(){var e;return this.asyncAttributesPending&&rk.error("Accessing resource attributes before async attributes settled"),null!=(e=this._attributes)?e:{}},enumerable:!1,configurable:!0}),e.prototype.waitForAsyncAttributes=function(){return iq(this,void 0,void 0,function(){return iJ(this,function(e){switch(e.label){case 0:if(!this.asyncAttributesPending)return[3,2];return[4,this._asyncAttributesPromise];case 1:e.sent(),e.label=2;case 2:return[2]}})})},e.prototype.merge=function(t){var n,r=this;if(!t)return this;var i=iW(iW({},this._syncAttributes),null!=(n=t._syncAttributes)?n:t.attributes);return this._asyncAttributesPromise||t._asyncAttributesPromise?new e(i,Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then(function(e){var n,i=iQ(e,2),a=i[0],s=i[1];return iW(iW(iW(iW({},r._syncAttributes),a),null!=(n=t._syncAttributes)?n:t.attributes),s)})):new e(i)},e.EMPTY=new e({}),e}(),i1="exception.type",i2="exception.message",i4=globalThis&&globalThis.__assign||function(){return(i4=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},i9=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i5=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},i3=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},i6=function(){function e(e,t,n,r,i,a,s,o,u,l){void 0===s&&(s=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:nM.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=r,this.parentSpanId=a,this.kind=i,this.links=s;var c=Date.now();this._performanceStartTime=ih.now(),this._performanceOffset=c-(this._performanceStartTime+ig()),this._startTimeProvided=null!=o,this.startTime=this._getTime(null!=o?o:c),this.resource=e.resource,this.instrumentationLibrary=e.instrumentationLibrary,this._spanLimits=e.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,null!=l&&this.setAttributes(l),this._spanProcessor=e.getActiveSpanProcessor(),this._spanProcessor.onStart(this,t)}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return null==t||this._isSpanEnded()||(0===e.length?rk.warn("Invalid attribute key: "+e):ie(t)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,e)?this._droppedAttributesCount++:this.attributes[e]=this._truncateToSize(t):rk.warn("Invalid attribute value set for key: "+e)),this},e.prototype.setAttributes=function(e){var t,n;try{for(var r=i9(Object.entries(e)),i=r.next();!i.done;i=r.next()){var a=i5(i.value,2),s=a[0],o=a[1];this.setAttribute(s,o)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return this},e.prototype.addEvent=function(e,t,n){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return rk.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(0===this._droppedEventsCount&&rk.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),iv(t)&&(iv(n)||(n=t),t=void 0);var r=r7(t);return this.events.push({name:e,attributes:r,time:this._getTime(n),droppedAttributesCount:0}),this},e.prototype.addLink=function(e){return this.links.push(e),this},e.prototype.addLinks=function(e){var t;return(t=this.links).push.apply(t,i3([],i5(e),!1)),this},e.prototype.setStatus=function(e){return this._isSpanEnded()||(this.status=i4({},e),null!=this.status.message&&"string"!=typeof e.message&&(rk.warn("Dropping invalid status.message of type '"+typeof e.message+"', expected 'string'"),delete this.status.message)),this},e.prototype.updateName=function(e){return this._isSpanEnded()||(this.name=e),this},e.prototype.end=function(e){var t,n,r,i;if(this._isSpanEnded())return void rk.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once.");this._ended=!0,this.endTime=this._getTime(e),this._duration=(t=this.startTime,r=(n=this.endTime)[0]-t[0],(i=n[1]-t[1])<0&&(r-=1,i+=1e9),[r,i]),this._duration[0]<0&&(rk.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&rk.warn("Dropped "+this._droppedEventsCount+" events because eventCountLimit reached"),this._spanProcessor.onEnd(this)},e.prototype._getTime=function(e){if("number"==typeof e&&e<=ih.now()){var t;return t=e+this._performanceOffset,iE(im(ig()),im("number"==typeof t?t:ih.now()))}if("number"==typeof e)return im(e);if(e instanceof Date)return im(e.getTime());if(iy(e))return e;if(this._startTimeProvided)return im(Date.now());var n=ih.now()-this._performanceStartTime;return iE(this.startTime,im(n))},e.prototype.isRecording=function(){return!1===this._ended},e.prototype.recordException=function(e,t){var n={};"string"==typeof e?n[i2]=e:e&&(e.code?n[i1]=e.code.toString():e.name&&(n[i1]=e.name),e.message&&(n[i2]=e.message),e.stack&&(n["exception.stacktrace"]=e.stack)),n[i1]||n[i2]?this.addEvent("exception",n,t):rk.warn("Failed to record an exception "+e)},Object.defineProperty(e.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),e.prototype._isSpanEnded=function(){return this._ended&&rk.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},e.prototype._truncateToLimitUtil=function(e,t){return e.length<=t?e:e.substring(0,t)},e.prototype._truncateToSize=function(e){var t=this,n=this._attributeValueLengthLimit;return n<=0?(rk.warn("Attribute value limit must be positive, got "+n),e):"string"==typeof e?this._truncateToLimitUtil(e,n):Array.isArray(e)?e.map(function(e){return"string"==typeof e?t._truncateToLimitUtil(e,n):e}):e},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(nU||(nU={}));var i8=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:nU.NOT_RECORD}},e.prototype.toString=function(){return"AlwaysOffSampler"},e}(),i7=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:nU.RECORD_AND_SAMPLED}},e.prototype.toString=function(){return"AlwaysOnSampler"},e}(),ae=function(){function e(e){var t,n,r,i;this._root=e.root,this._root||(ii(Error("ParentBasedSampler must have a root sampler configured")),this._root=new i7),this._remoteParentSampled=null!=(t=e.remoteParentSampled)?t:new i7,this._remoteParentNotSampled=null!=(n=e.remoteParentNotSampled)?n:new i8,this._localParentSampled=null!=(r=e.localParentSampled)?r:new i7,this._localParentNotSampled=null!=(i=e.localParentNotSampled)?i:new i8}return e.prototype.shouldSample=function(e,t,n,r,i,a){var s=rC.getSpanContext(e);return s&&rS(s)?s.isRemote?s.traceFlags&nk.SAMPLED?this._remoteParentSampled.shouldSample(e,t,n,r,i,a):this._remoteParentNotSampled.shouldSample(e,t,n,r,i,a):s.traceFlags&nk.SAMPLED?this._localParentSampled.shouldSample(e,t,n,r,i,a):this._localParentNotSampled.shouldSample(e,t,n,r,i,a):this._root.shouldSample(e,t,n,r,i,a)},e.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},e}(),at=function(){function e(e){void 0===e&&(e=0),this._ratio=e,this._ratio=this._normalize(e),this._upperBound=Math.floor(0xffffffff*this._ratio)}return e.prototype.shouldSample=function(e,t){return{decision:rT(t)&&this._accumulate(t)<this._upperBound?nU.RECORD_AND_SAMPLED:nU.NOT_RECORD}},e.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},e.prototype._normalize=function(e){return"number"!=typeof e||isNaN(e)?0:e>=1?1:e<=0?0:e},e.prototype._accumulate=function(e){for(var t=0,n=0;n<e.length/8;n++){var r=8*n;t=(t^parseInt(e.slice(r,r+8),16))>>>0}return t},e}(),an=nD.AlwaysOn;function ar(){var e=ip();return{sampler:ai(e),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:e.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:e.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:e.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:e.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function ai(e){switch(void 0===e&&(e=ip()),e.OTEL_TRACES_SAMPLER){case nD.AlwaysOn:return new i7;case nD.AlwaysOff:return new i8;case nD.ParentBasedAlwaysOn:return new ae({root:new i7});case nD.ParentBasedAlwaysOff:return new ae({root:new i8});case nD.TraceIdRatio:return new at(aa(e));case nD.ParentBasedTraceIdRatio:return new ae({root:new at(aa(e))});default:return rk.error('OTEL_TRACES_SAMPLER value "'+e.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+an+'".'),new i7}}function aa(e){if(void 0===e.OTEL_TRACES_SAMPLER_ARG||""===e.OTEL_TRACES_SAMPLER_ARG)return rk.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(rk.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):t<0||t>1?(rk.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):t}var as=function(){function e(e,t){this._exporter=e,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var n=ip();this._maxExportBatchSize="number"==typeof(null==t?void 0:t.maxExportBatchSize)?t.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==t?void 0:t.maxQueueSize)?t.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==t?void 0:t.scheduledDelayMillis)?t.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==t?void 0:t.exportTimeoutMillis)?t.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new iY(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(rk.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return e.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){this._shutdownOnce.isCalled||(e.spanContext().traceFlags&nk.SAMPLED)!=0&&this._addToBuffer(e)},e.prototype.shutdown=function(){return this._shutdownOnce.call()},e.prototype._shutdown=function(){var e=this;return Promise.resolve().then(function(){return e.onShutdown()}).then(function(){return e._flushAll()}).then(function(){return e._exporter.shutdown()})},e.prototype._addToBuffer=function(e){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&rk.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(rk.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(e),this._maybeStartTimer()},e.prototype._flushAll=function(){var e=this;return new Promise(function(t,n){for(var r=[],i=Math.ceil(e._finishedSpans.length/e._maxExportBatchSize),a=0;a<i;a++)r.push(e._flushOneBatch());Promise.all(r).then(function(){t()}).catch(n)})},e.prototype._flushOneBatch=function(){var e=this;return(this._clearTimer(),0===this._finishedSpans.length)?Promise.resolve():new Promise(function(t,n){var r=setTimeout(function(){n(Error("Timeout"))},e._exportTimeoutMillis);ro.with(r2(ro.active()),function(){e._finishedSpans.length<=e._maxExportBatchSize?(i=e._finishedSpans,e._finishedSpans=[]):i=e._finishedSpans.splice(0,e._maxExportBatchSize);for(var i,a=function(){return e._exporter.export(i,function(e){var i;clearTimeout(r),e.code===n$.SUCCESS?t():n(null!=(i=e.error)?i:Error("BatchSpanProcessor: span export failed"))})},s=null,o=0,u=i.length;o<u;o++){var l=i[o];l.resource.asyncAttributesPending&&l.resource.waitForAsyncAttributes&&(null!=s||(s=[]),s.push(l.resource.waitForAsyncAttributes()))}null===s?a():Promise.all(s).then(a,function(e){ii(e),n(e)})})})},e.prototype._maybeStartTimer=function(){var e=this;if(!this._isExporting){var t=function(){e._isExporting=!0,e._flushOneBatch().finally(function(){e._isExporting=!1,e._finishedSpans.length>0&&(e._clearTimer(),e._maybeStartTimer())}).catch(function(t){e._isExporting=!1,ii(t)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return t();void 0===this._timer&&(this._timer=setTimeout(function(){return t()},this._scheduledDelayMillis),this._timer.unref())}},e.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},e}(),ao=globalThis&&globalThis.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),au=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return ao(t,e),t.prototype.onShutdown=function(){},t}(as),al=function(){this.generateTraceId=ad(16),this.generateSpanId=ad(8)},ac=rW.allocUnsafe(16);function ad(e){return function(){for(var t=0;t<e/4;t++)ac.writeUInt32BE(0x100000000*Math.random()>>>0,4*t);for(var t=0;t<e;t++)if(ac[t]>0)break;else t===e-1&&(ac[e-1]=1);return ac.toString("hex",0,e)}}var ap=function(){function e(e,t,n){this._tracerProvider=n;var r,i,a,s=(r={sampler:ai()},(a=Object.assign({},i=ar(),r,t)).generalLimits=Object.assign({},i.generalLimits,t.generalLimits||{}),a.spanLimits=Object.assign({},i.spanLimits,t.spanLimits||{}),a);this._sampler=s.sampler,this._generalLimits=s.generalLimits,this._spanLimits=s.spanLimits,this._idGenerator=t.idGenerator||new al,this.resource=n.resource,this.instrumentationLibrary=e}return e.prototype.startSpan=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n=ro.active()),t.root&&(n=rC.deleteSpan(n));var r,i,a,s,o,u,l=rC.getSpan(n);if(r4(n)){rk.debug("Instrumentation suppressed, returning Noop Span");var c=rC.wrapSpanContext(rc);return c}var d=null==l?void 0:l.spanContext(),p=this._idGenerator.generateSpanId();d&&rC.isSpanContextValid(d)?(s=d.traceId,o=d.traceState,u=d.spanId):s=this._idGenerator.generateTraceId();var h=null!=(r=t.kind)?r:nL.INTERNAL,f=(null!=(i=t.links)?i:[]).map(function(e){return{context:e.context,attributes:r7(e.attributes)}}),_=r7(t.attributes),m=this._sampler.shouldSample(n,s,e,h,_,f);o=null!=(a=m.traceState)?a:o;var g={traceId:s,spanId:p,traceFlags:m.decision===nN.RECORD_AND_SAMPLED?nk.SAMPLED:nk.NONE,traceState:o};if(m.decision===nN.NOT_RECORD){rk.debug("Recording is off, propagating context in a non-recording span");var c=rC.wrapSpanContext(g);return c}var y=r7(Object.assign(_,m.attributes));return new i6(this,n,e,g,h,u,f,t.startTime,void 0,y)},e.prototype.startActiveSpan=function(e,t,n,r){if(!(arguments.length<2)){2==arguments.length?s=t:3==arguments.length?(i=t,s=n):(i=t,a=n,s=r);var i,a,s,o=null!=a?a:ro.active(),u=this.startSpan(e,i,o),l=rC.setSpan(o,u);return ro.with(l,s,void 0,u)}},e.prototype.getGeneralLimits=function(){return this._generalLimits},e.prototype.getSpanLimits=function(){return this._spanLimits},e.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},e}(),ah=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},af=function(){function e(e){this._spanProcessors=e}return e.prototype.forceFlush=function(){var e,t,n=[];try{for(var r=ah(this._spanProcessors),i=r.next();!i.done;i=r.next()){var a=i.value;n.push(a.forceFlush())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return new Promise(function(e){Promise.all(n).then(function(){e()}).catch(function(t){ii(t||Error("MultiSpanProcessor: forceFlush failed")),e()})})},e.prototype.onStart=function(e,t){var n,r;try{for(var i=ah(this._spanProcessors),a=i.next();!a.done;a=i.next())a.value.onStart(e,t)}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}},e.prototype.onEnd=function(e){var t,n;try{for(var r=ah(this._spanProcessors),i=r.next();!i.done;i=r.next())i.value.onEnd(e)}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}},e.prototype.shutdown=function(){var e,t,n=[];try{for(var r=ah(this._spanProcessors),i=r.next();!i.done;i=r.next()){var a=i.value;n.push(a.shutdown())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return new Promise(function(e,t){Promise.all(n).then(function(){e()},t)})},e}(),a_=function(){function e(){}return e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){},e.prototype.shutdown=function(){return Promise.resolve()},e.prototype.forceFlush=function(){return Promise.resolve()},e}(),am=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return s},ag=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};!function(e){e[e.resolved=0]="resolved",e[e.timeout=1]="timeout",e[e.error=2]="error",e[e.unresolved=3]="unresolved"}(nB||(nB={}));var ay=function(){function e(e){void 0===e&&(e={}),this._registeredSpanProcessors=[],this._tracers=new Map;var t,n,r,i,a,s,o,u,l,c,d,p,h,f,_,m,g,y=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=e.shift(),r=new WeakMap;e.length>0;)n=function e(t,n,r,i){if(void 0===r&&(r=0),!(r>20)){if(r++,iz(t)||iz(n)||iV(n))o=iB(n);else if(iZ(t)){if(o=t.slice(),iZ(n))for(var a,s,o,u=0,l=n.length;u<l;u++)o.push(iB(n[u]));else if(iG(n))for(var c=Object.keys(n),u=0,l=c.length;u<l;u++){var d=c[u];o[d]=iB(n[d])}}else if(iG(t))if(iG(n)){if(a=t,s=n,!(iU(a)&&iU(s)))return n;o=Object.assign({},t);for(var c=Object.keys(n),u=0,l=c.length;u<l;u++){var d=c[u],p=n[d];if(iz(p))void 0===p?delete o[d]:o[d]=p;else{var h=o[d];if(iF(t,d,i)||iF(n,d,i))delete o[d];else{if(iG(h)&&iG(p)){var f=i.get(h)||[],_=i.get(p)||[];f.push({obj:t,key:d}),_.push({obj:n,key:d}),i.set(h,f),i.set(p,_)}o[d]=e(o[d],p,r,i)}}}}else o=n;return o}}(n,e.shift(),0,r);return n}({},ar(),(f=Object.assign({},(t=e).spanLimits),_=id(process.env),f.attributeCountLimit=null!=(o=null!=(s=null!=(a=null!=(r=null==(n=t.spanLimits)?void 0:n.attributeCountLimit)?r:null==(i=t.generalLimits)?void 0:i.attributeCountLimit)?a:_.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)?s:_.OTEL_ATTRIBUTE_COUNT_LIMIT)?o:128,f.attributeValueLengthLimit=null!=(h=null!=(p=null!=(d=null!=(l=null==(u=t.spanLimits)?void 0:u.attributeValueLengthLimit)?l:null==(c=t.generalLimits)?void 0:c.attributeValueLengthLimit)?d:_.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)?p:_.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?h:iu,Object.assign({},t,{spanLimits:f})));if(this.resource=null!=(m=y.resource)?m:i0.empty(),y.mergeResourceWithDefaults&&(this.resource=i0.default().merge(this.resource)),this._config=Object.assign({},y,{resource:this.resource}),null==(g=e.spanProcessors)?void 0:g.length)this._registeredSpanProcessors=ag([],am(e.spanProcessors),!1),this.activeSpanProcessor=new af(this._registeredSpanProcessors);else{var v=this._buildExporterFromEnv();if(void 0!==v){var E=new au(v);this.activeSpanProcessor=E}else this.activeSpanProcessor=new a_}}return e.prototype.getTracer=function(e,t,n){var r=e+"@"+(t||"")+":"+((null==n?void 0:n.schemaUrl)||"");return this._tracers.has(r)||this._tracers.set(r,new ap({name:e,version:t,schemaUrl:null==n?void 0:n.schemaUrl},this._config,this)),this._tracers.get(r)},e.prototype.addSpanProcessor=function(e){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(function(e){return rk.error("Error while trying to shutdown current span processor",e)}),this._registeredSpanProcessors.push(e),this.activeSpanProcessor=new af(this._registeredSpanProcessors)},e.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},e.prototype.register=function(e){void 0===e&&(e={}),rC.setGlobalTracerProvider(this),void 0===e.propagator&&(e.propagator=this._buildPropagatorFromEnv()),e.contextManager&&ro.setGlobalContextManager(e.contextManager),e.propagator&&rY.setGlobalPropagator(e.propagator)},e.prototype.forceFlush=function(){var e=this._config.forceFlushTimeoutMillis,t=this._registeredSpanProcessors.map(function(t){return new Promise(function(n){var r,i=setTimeout(function(){n(Error("Span processor did not completed within timeout period of "+e+" ms")),r=nB.timeout},e);t.forceFlush().then(function(){clearTimeout(i),r!==nB.timeout&&n(r=nB.resolved)}).catch(function(e){clearTimeout(i),r=nB.error,n(e)})})});return new Promise(function(e,n){Promise.all(t).then(function(t){var r=t.filter(function(e){return e!==nB.resolved});r.length>0?n(r):e()}).catch(function(e){return n([e])})})},e.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},e.prototype._getPropagator=function(e){var t;return null==(t=this.constructor._registeredPropagators.get(e))?void 0:t()},e.prototype._getSpanExporter=function(e){var t;return null==(t=this.constructor._registeredExporters.get(e))?void 0:t()},e.prototype._buildPropagatorFromEnv=function(){var e=this,t=Array.from(new Set(ip().OTEL_PROPAGATORS)),n=t.map(function(t){var n=e._getPropagator(t);return n||rk.warn('Propagator "'+t+'" requested through environment variable is unavailable.'),n}).reduce(function(e,t){return t&&e.push(t),e},[]);return 0===n.length?void 0:1===t.length?n[0]:new iS({propagators:n})},e.prototype._buildExporterFromEnv=function(){var e=ip().OTEL_TRACES_EXPORTER;if("none"!==e&&""!==e){var t=this._getSpanExporter(e);return t||rk.error('Exporter "'+e+'" requested through environment variable is unavailable.'),t}},e._registeredPropagators=new Map([["tracecontext",function(){return new iC}],["baggage",function(){return new r3}]]),e._registeredExporters=new Map,e}();let av="http.method",aE="http.url",aT="http.status_code",aS="http.request.method",ab="http.response.status_code",aO="url.full",ax="sentry.parentIsRemote";function aw(e){return"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0}function aR(e){return!!e.attributes&&"object"==typeof e.attributes}let aA="sentry-trace",aI="baggage",aP="sentry.dsc",aC="sentry.sampled_not_recording",ak="sentry.url",aL=nR("sentry_scopes"),aN=nR("sentry_fork_isolation_scope"),aM=nR("sentry_fork_set_scope"),aD=nR("sentry_fork_set_isolation_scope"),aj="_scopeContext";function a$(e){return e.getValue(aL)}function aU(e,t){return e.setValue(aL,t)}function aB(e){let{traceFlags:t,traceState:n}=e,r=!!n&&"1"===n.get(aC);if(t===nk.SAMPLED)return!0;if(r)return!1;let i=n?n.get(aP):void 0,a=i?eb(i):void 0;return a?.sampled==="true"||a?.sampled!=="false"&&void 0}function aF(e,t,n){let r=t[aS]||t[av];if(r)return function({name:e,kind:t,attributes:n},r){let i=["http"];switch(t){case nL.CLIENT:i.push("client");break;case nL.SERVER:i.push("server")}n["sentry.http.prefetch"]&&i.push("prefetch");let{urlPath:a,url:s,query:o,fragment:u,hasRoute:l}=function(e,t){let n=e["http.target"],r=e[aE]||e[aO],i=e["http.route"],a="string"==typeof r?te(r):void 0,s=a?tn(a):void 0,o=a?.search||void 0,u=a?.hash||void 0;return"string"==typeof i?{urlPath:i,url:s,query:o,fragment:u,hasRoute:!0}:t===nL.SERVER&&"string"==typeof n?{urlPath:tt(n),url:s,query:o,fragment:u,hasRoute:!1}:a?{urlPath:s,url:s,query:o,fragment:u,hasRoute:!1}:"string"==typeof n?{urlPath:tt(n),url:s,query:o,fragment:u,hasRoute:!1}:{urlPath:void 0,url:s,query:o,fragment:u,hasRoute:!1}}(n,t);if(!a)return{...aV(e,n),op:i.join(".")};let c=n["sentry.graphql.operation"],d=`${r} ${a}`,p=c?`${d} (${function(e){if(Array.isArray(e)){let t=e.slice().sort();return t.length<=5?t.join(", "):`${t.slice(0,5).join(", ")}, +${t.length-5}`}return`${e}`}(c)})`:d,h={};s&&(h.url=s),o&&(h["http.query"]=o),u&&(h["http.fragment"]=u);let f=t===nL.CLIENT||t===nL.SERVER,_=n[ed]||"manual",m=!`${_}`.startsWith("auto"),g="custom"===n[eu],y=n[ep],{description:v,source:E}=g||null!=y||!f&&m?aV(e,n):{description:p,source:l||"/"===a?"route":"url"};return{op:i.join("."),description:v,source:E,data:h}}({attributes:t,name:e,kind:n},r);let i=t["db.system"],a="string"==typeof t[ec]&&t[ec].startsWith("cache.");if(i&&!a)return function({attributes:e,name:t}){let n=e[ep];if("string"==typeof n)return{op:"db",description:n,source:e[eu]||"custom"};if("custom"===e[eu])return{op:"db",description:t,source:"custom"};let r=e["db.statement"];return{op:"db",description:r?r.toString():t,source:"task"}}({attributes:t,name:e});let s="custom"===t[eu]?"custom":"route";if(t["rpc.service"])return{...aV(e,t,"route"),op:"rpc"};if(t["messaging.system"])return{...aV(e,t,s),op:"message"};let o=t["faas.trigger"];return o?{...aV(e,t,s),op:o.toString()}:{op:void 0,description:e,source:"custom"}}function aZ(e){let t=aR(e)?e.attributes:{};return aF(e.name?e.name:"<unknown>",t,"number"==typeof e.kind?e.kind:nL.INTERNAL)}function aV(e,t,n="custom"){let r=t[eu]||n,i=t[ep];return i&&"string"==typeof i?{description:i,source:r}:{description:e,source:r}}function aG(){return rC.getActiveSpan()}let az="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function aX({dsc:e,sampled:t}){let n=e?eO(e):void 0,r=new iR,i=n?r.set(aP,n):r;return!1===t?i.set(aC,"1"):i}let aH=new Set;function aK(e){aH.add(e)}class aY extends r3{constructor(){super(),aK("SentryPropagator"),this._urlMatchesTargetsMap=new tK(100)}inject(e,t,n){if(r4(e)){az&&A.log("[Tracing] Not injecting trace data for url because tracing is suppressed.");return}let r=rC.getSpan(e),i=r&&function(e){let t=eN(e).data,n=t[aE]||t[aO];if("string"==typeof n)return n;let r=e.spanContext().traceState?.get(ak);if(r)return r}(r);if(!function(e,t,n){if("string"!=typeof e||!t)return!0;let r=n?.get(e);if(void 0!==r)return az&&!r&&A.log(aW,e),r;let i=P(e,t);return n?.set(e,i),az&&!i&&A.log(aW,e),i}(i,es()?.getOptions()?.tracePropagationTargets,this._urlMatchesTargetsMap)){az&&A.log("[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:",i);return}let a=function(e){try{let t=e[aI];return Array.isArray(t)?t.join(","):t}catch{return}}(t),s=rY.getBaggage(e)||rY.createBaggage({}),{dynamicSamplingContext:o,traceId:u,spanId:l,sampled:c}=aq(e);if(a){let e=ex(a);e&&Object.entries(e).forEach(([e,t])=>{s=s.setEntry(e,{value:t})})}o&&(s=Object.entries(o).reduce((e,[t,n])=>n?e.setEntry(`${eT}${t}`,{value:n}):e,s)),u&&u!==rl&&n.set(t,aA,eA(u,l,c)),super.inject(rY.setBaggage(e,s),t,n)}extract(e,t,n){let r=n.get(t,aA),i=n.get(t,aI);return aQ(aJ(e,{sentryTrace:r?Array.isArray(r)?r[0]:r:void 0,baggage:i}))}fields(){return[aA,aI]}}let aW="[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:";function aq(e){let t=rC.getSpan(e);if(t?.spanContext().isRemote){let e=t.spanContext();return{dynamicSamplingContext:ty(t),traceId:e.traceId,spanId:void 0,sampled:aB(e)}}if(t){let e=t.spanContext();return{dynamicSamplingContext:ty(t),traceId:e.traceId,spanId:e.spanId,sampled:aB(e)}}let n=a$(e)?.scope||er(),r=es(),i=n.getPropagationContext();return{dynamicSamplingContext:r?tg(r,n):void 0,traceId:i.traceId,spanId:i.propagationSpanId,sampled:i.sampled}}function aJ(e,{sentryTrace:t,baggage:n}){let{traceId:r,parentSpanId:i,sampled:a,dsc:s}=function(e,t){let n=function(e){let t;if(!e)return;let n=e.match(eR);if(n)return"1"===n[3]?t=!0:"0"===n[3]&&(t=!1),{traceId:n[1],parentSampled:t,parentSpanId:n[2]}}(e),r=eb(t);if(!n?.traceId)return{traceId:D(),sampleRand:Math.random()};let i=function(e,t){let n=eE(t?.sample_rand);if(void 0!==n)return n;let r=eE(t?.sample_rate);return r&&e?.parentSampled!==void 0?e.parentSampled?Math.random()*r:r+Math.random()*(1-r):Math.random()}(n,r);r&&(r.sample_rand=i.toString());let{traceId:a,parentSpanId:s,parentSampled:o}=n;return{traceId:a,parentSpanId:s,sampled:o,dsc:r||{},sampleRand:i}}(t,n);if(!i)return e;let o=function({spanId:e,traceId:t,sampled:n,dsc:r}){let i=aX({dsc:r,sampled:n});return{traceId:t,spanId:e,isRemote:!0,traceFlags:n?nk.SAMPLED:nk.NONE,traceState:i}}({traceId:r,spanId:i,sampled:a,dsc:s});return rC.setSpanContext(e,o)}function aQ(e){let t=a$(e);return aU(e,{scope:t?t.scope:er().clone(),isolationScope:t?t.isolationScope:ei()})}function a0(e,t){let n=a9(),{name:r,parentSpan:i}=e;return a8(i)(()=>{let i=a3(e.scope,e.forceTransaction),a=e.onlyIfParent&&!rC.getSpan(i)?r2(i):i,s=a5(e);return n.startActiveSpan(r,s,a,e=>tY(()=>t(e),()=>{void 0===eN(e).status&&e.setStatus({code:nM.ERROR})},()=>e.end()))})}function a1(e,t){let n=a9(),{name:r,parentSpan:i}=e;return a8(i)(()=>{let i=a3(e.scope,e.forceTransaction),a=e.onlyIfParent&&!rC.getSpan(i)?r2(i):i,s=a5(e);return n.startActiveSpan(r,s,a,e=>tY(()=>t(e,()=>e.end()),()=>{void 0===eN(e).status&&e.setStatus({code:nM.ERROR})}))})}function a2(e){let t=a9(),{name:n,parentSpan:r}=e;return a8(r)(()=>{let r=a3(e.scope,e.forceTransaction),i=e.onlyIfParent&&!rC.getSpan(r)?r2(r):r,a=a5(e);return t.startSpan(n,a,i)})}function a4(e,t){let n=e?rC.setSpan(ro.active(),e):rC.deleteSpan(ro.active());return ro.with(n,()=>t(er()))}function a9(){let e=es();return e?.tracer||rC.getTracer("@sentry/opentelemetry",c)}function a5(e){var t;let{startTime:n,attributes:r,kind:i,op:a,links:s}=e,o="number"==typeof n?(t=n)<0x2540be3ff?1e3*t:t:n;return{attributes:a?{[ec]:a,...r}:r,kind:i,links:s,startTime:o}}function a3(e,t){let n=function(e){if(e){let t=e[aj];if(t)return t}return ro.active()}(e),r=rC.getSpan(n);if(!r||!t)return n;let i=rC.deleteSpan(n),{spanId:a,traceId:s}=r.spanContext(),o=aB(r.spanContext()),u=aX({dsc:ty(eB(r)),sampled:o}),l={traceId:s,spanId:a,isRemote:!0,traceFlags:o?nk.SAMPLED:nk.NONE,traceState:u};return rC.setSpanContext(i,l)}function a6(e,t){let n=aQ(aJ(ro.active(),e));return ro.with(n,t)}function a8(e){return void 0!==e?t=>a4(e,t):e=>e()}function a7(e){let t=r2(ro.active());return ro.with(t,e)}function se({span:e}={}){let t=ro.active();if(e){let{scope:n}=ev(e);t=n&&n[aj]||rC.setSpan(ro.active(),e)}let{traceId:n,spanId:r,sampled:i,dynamicSamplingContext:a}=aq(t);return{"sentry-trace":eA(n,r,i),baggage:eO(a)}}function st(e){return!0===e.attributes[ax]?void 0:aw(e)}function sn(e,t){let n=e.get(t.id);return n?.span?n:n&&!n.span?(n.span=t.span,n.parentNode=t.parentNode,n):(e.set(t.id,t),t)}let sr={1:"cancelled",2:"unknown_error",3:"invalid_argument",4:"deadline_exceeded",5:"not_found",6:"already_exists",7:"permission_denied",8:"resource_exhausted",9:"failed_precondition",10:"aborted",11:"out_of_range",12:"unimplemented",13:"internal_error",14:"unavailable",15:"data_loss",16:"unauthenticated"},si=e=>Object.values(sr).includes(e);function sa(e){let t=aR(e)?e.attributes:{},n=e.status?e.status:void 0;if(n){if(n.code===nM.OK)return{code:1};else if(n.code===nM.ERROR){if(void 0===n.message){let e=ss(t);if(e)return e}return n.message&&si(n.message)?{code:2,message:n.message}:{code:2,message:"unknown_error"}}}let r=ss(t);return r||(n?.code===nM.UNSET?{code:1}:{code:2,message:"unknown_error"})}function ss(e){let t=e[ab]||e[aT],n=e["rpc.grpc.status_code"],r="number"==typeof t?t:"string"==typeof t?parseInt(t):void 0;return"number"==typeof r?e_(r):"string"==typeof n?{code:2,message:sr[n]||"unknown_error"}:void 0}class so{constructor(e){this._finishedSpanBucketSize=e?.timeout||300,this._finishedSpanBuckets=Array(this._finishedSpanBucketSize).fill(void 0),this._lastCleanupTimestampInS=Math.floor(Date.now()/1e3),this._spansToBucketEntry=new WeakMap}export(e){let t=Math.floor(Date.now()/1e3);if(this._lastCleanupTimestampInS!==t){let e=0;this._finishedSpanBuckets.forEach((n,r)=>{n&&n.timestampInS<=t-this._finishedSpanBucketSize&&(e+=n.spans.size,this._finishedSpanBuckets[r]=void 0)}),e>0&&az&&A.log(`SpanExporter dropped ${e} spans because they were pending for more than ${this._finishedSpanBucketSize} seconds.`),this._lastCleanupTimestampInS=t}let n=t%this._finishedSpanBucketSize,r=this._finishedSpanBuckets[n]||{timestampInS:t,spans:new Set};this._finishedSpanBuckets[n]=r,r.spans.add(e),this._spansToBucketEntry.set(e,r),st(e)||(this._clearTimeout(),this._flushTimeout=setTimeout(()=>{this.flush()},1))}flush(){this._clearTimeout();let e=[];this._finishedSpanBuckets.forEach(t=>{t&&e.push(...t.spans)});let t=function(e){let t=function(e){let t=new Map;for(let n of e)!function(e,t){let n=t.spanContext().spanId,r=st(t);if(!r)return sn(e,{id:n,span:t,children:[]});let i=function(e,t){let n=e.get(t);return n||sn(e,{id:t,children:[]})}(e,r),a=sn(e,{id:n,span:t,parentNode:i,children:[]});i.children.push(a)}(t,n);return Array.from(t,function([e,t]){return t})}(e),n=new Set;return t.filter(su).forEach(e=>{let t=e.span;n.add(t);let r=function(e){let{op:t,description:n,data:r,origin:i="manual",source:a}=sl(e),s=ev(e),o=e.attributes[el],u={[eu]:a,[el]:o,[ec]:t,[ed]:i,...r,...sc(e.attributes)},{links:l}=e,{traceId:c,spanId:d}=e.spanContext(),p={parent_span_id:aw(e),span_id:d,trace_id:c,data:u,origin:i,op:t,status:eD(sa(e)),links:eC(l)},h=u[ab];return{contexts:{trace:p,otel:{resource:e.resource.attributes},..."number"==typeof h?{response:{status_code:h}}:void 0},spans:[],start_timestamp:ek(e.startTime),timestamp:ek(e.endTime),transaction:n,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:s.scope,capturedSpanIsolationScope:s.isolationScope,sampleRate:o,dynamicSamplingContext:ty(e)},...a&&{transaction_info:{source:a}}}}(t),i=r.spans||[];e.children.forEach(e=>{!function e(t,n,r){let i=t.span;if(i&&r.add(i),!i)return void t.children.forEach(t=>{e(t,n,r)});let a=i.spanContext().spanId,s=i.spanContext().traceId,o=aw(i),{attributes:u,startTime:l,endTime:c,links:d}=i,{op:p,description:h,data:f,origin:_="manual"}=sl(i),m={[ed]:_,[ec]:p,...sc(u),...f},g=sa(i),y={span_id:a,trace_id:s,data:m,description:h,parent_span_id:o,start_timestamp:ek(l),timestamp:ek(c)||void 0,status:eD(g),op:p,origin:_,measurements:tW(i.events),links:eC(d)};n.push(y),t.children.forEach(t=>{e(t,n,r)})}(e,i,n)}),r.spans=i.length>1e3?i.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):i;let a=tW(t.events);a&&(r.measurements=a),er().captureEvent(r,void 0)}),n}(e),n=t.size,r=e.length-n;az&&A.log(`SpanExporter exported ${n} spans, ${r} spans are waiting for their parent spans to finish`),t.forEach(e=>{let t=this._spansToBucketEntry.get(e);t&&t.spans.delete(e)})}clear(){this._finishedSpanBuckets=this._finishedSpanBuckets.fill(void 0),this._clearTimeout()}_clearTimeout(){this._flushTimeout&&(clearTimeout(this._flushTimeout),this._flushTimeout=void 0)}}function su(e){return!!e.span&&!e.parentNode}function sl(e){let{op:t,source:n,origin:r}=function(e){let t=e.attributes,n=t[ed];return{origin:n,op:t[ec],source:t[eu]}}(e),{op:i,description:a,source:s,data:o}=aZ(e);return{op:t||i,description:a,source:n||s,origin:r,data:{...o,...function(e){let t=e.attributes,n={};e.kind!==nL.INTERNAL&&(n["otel.kind"]=nL[e.kind]);let r=t[aT];r&&(n[ab]=r);let i=function(e){if(!aR(e))return{};let t=e.attributes[aO]||e.attributes[aE],n={url:t,"http.method":e.attributes[aS]||e.attributes[av]};!n["http.method"]&&n.url&&(n["http.method"]="GET");try{if("string"==typeof t){let e=te(t);n.url=tn(e),e.search&&(n["http.query"]=e.search),e.hash&&(n["http.fragment"]=e.hash)}}catch{}return n}(e);return i.url&&(n.url=i.url),i["http.query"]&&(n["http.query"]=i["http.query"].slice(1)),i["http.fragment"]&&(n["http.fragment"]=i["http.fragment"].slice(1)),n}(e)}}}function sc(e){let t={...e};return delete t[el],delete t[ax],delete t[ep],t}class sd{constructor(e){aK("SentrySpanProcessor"),this._exporter=new so(e)}async forceFlush(){this._exporter.flush()}async shutdown(){this._exporter.clear()}onStart(e,t){let n=rC.getSpan(t),r=a$(t);n&&!n.spanContext().isRemote&&eU(n,e),n?.spanContext().isRemote&&e.setAttribute(ax,!0),t===nH&&(r={scope:Y(),isolationScope:W()}),r&&ey(e,r.scope,r.isolationScope),tJ(e);let i=es();i?.emit("spanStart",e)}onEnd(e){tQ(e);let t=es();t?.emit("spanEnd",e),this._exporter.export(e)}}class sp{constructor(e){this._client=e,aK("SentrySampler")}shouldSample(e,t,n,r,i,a){let s=this._client.getOptions(),o=function(e){let t=rC.getSpan(e);return t&&rS(t.spanContext())?t:void 0}(e),u=o?.spanContext();if(!tf(s))return sh({decision:void 0,context:e,spanAttributes:i});let l=i[av]||i[aS];if(r===nL.CLIENT&&l&&(!o||u?.isRemote))return sh({decision:void 0,context:e,spanAttributes:i});let c=o?function(e,t,n){let r=e.spanContext();if(rS(r)&&r.traceId===t){if(r.isRemote){let t=aB(e.spanContext());return az&&A.log(`[Tracing] Inheriting remote parent's sampled decision for ${n}: ${t}`),t}let t=aB(r);return az&&A.log(`[Tracing] Inheriting parent's sampled decision for ${n}: ${t}`),t}}(o,t,n):void 0;if(!(!o||u?.isRemote))return sh({decision:c?nU.RECORD_AND_SAMPLED:nU.NOT_RECORD,context:e,spanAttributes:i});let{description:d,data:p,op:h}=aF(n,i,r),f={...p,...i};h&&(f[ec]=h);let _={decision:!0};if(this._client.emit("beforeSampling",{spanAttributes:f,spanName:d,parentSampled:c,parentContext:u},_),!_.decision)return sh({decision:void 0,context:e,spanAttributes:i});let{isolationScope:m}=a$(e)??{},g=u?.traceState?u.traceState.get(aP):void 0,y=g?eb(g):void 0,v=eE(y?.sample_rand)??Math.random(),[E,T,S]=t0(s,{name:d,attributes:f,normalizedRequest:m?.getScopeData().sdkProcessingMetadata.normalizedRequest,parentSampled:c,parentSampleRate:eE(y?.sample_rate)},v),b=`${l}`.toUpperCase();return"OPTIONS"===b||"HEAD"===b?(az&&A.log(`[Tracing] Not sampling span because HTTP method is '${b}' for ${n}`),sh({decision:nU.NOT_RECORD,context:e,spanAttributes:i,sampleRand:v,downstreamTraceSampleRate:0})):(E||void 0!==c||(az&&A.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),this._client.recordDroppedEvent("sample_rate","transaction")),{...sh({decision:E?nU.RECORD_AND_SAMPLED:nU.NOT_RECORD,context:e,spanAttributes:i,sampleRand:v,downstreamTraceSampleRate:S?T:void 0}),attributes:{[el]:S?T:void 0}})}toString(){return"SentrySampler"}}function sh({decision:e,context:t,spanAttributes:n,sampleRand:r,downstreamTraceSampleRate:i}){let a=function(e,t){let n=rC.getSpan(e),r=n?.spanContext(),i=r?.traceState||new iR,a=t[aE]||t[aO];return a&&"string"==typeof a&&(i=i.set(ak,a)),i}(t,n);return(void 0!==i&&(a=a.set("sentry.sample_rate",`${i}`)),void 0!==r&&(a=a.set("sentry.sample_rand",`${r}`)),void 0==e)?{decision:nU.NOT_RECORD,traceState:a}:e===nU.NOT_RECORD?{decision:e,traceState:a.set(aC,"1")}:{decision:e,traceState:a}}let sf="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,s_=new WeakMap,sm=(e={})=>{let t=void 0===e.breadcrumbs||e.breadcrumbs,n=e.shouldCreateSpanForRequest,r=new tK(100),i=new tK(100),a={};function s(e){let t=es();if(!t)return!1;let n=t.getOptions();if(void 0===n.tracePropagationTargets)return!0;let r=i.get(e);if(void 0!==r)return r;let a=P(e,n.tracePropagationTargets);return i.set(e,a),a}function o(e){if(void 0===n)return!0;let t=r.get(e);if(void 0!==t)return t;let i=n(e);return r.set(e,i),i}return{name:"WinterCGFetch",setupOnce(){!function(e,t){let n="fetch";eJ(n,e),eQ(n,()=>(function(e,t=!1){C(u,"fetch",function(t){return function(...n){let r=Error(),{method:i,url:a}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,n]=e;return{url:t4(t),method:t2(n,"method")?String(n.method).toUpperCase():"GET"}}let t=e[0];return{url:t4(t),method:t2(t,"method")?String(t.method).toUpperCase():"GET"}}(n),s={args:n,fetchData:{method:i,url:a},startTimestamp:1e3*Z(),virtualError:r,headers:function(e){let[t,n]=e;try{if("object"==typeof n&&null!==n&&"headers"in n&&n.headers)return new Headers(n.headers);if(O(t))return new Headers(t.headers)}catch{}}(n)};return e||e0("fetch",{...s}),t.apply(u,n).then(async t=>(e?e(t):e0("fetch",{...s,endTimestamp:1e3*Z(),response:t}),t),e=>{if(e0("fetch",{...s,endTimestamp:1e3*Z(),error:e}),_(e)&&void 0===e.stack&&(e.stack=r.stack,k(e,"framesToPop",1)),e instanceof TypeError&&("Failed to fetch"===e.message||"Load failed"===e.message||"NetworkError when attempting to fetch resource."===e.message))try{let t=new URL(s.fetchData.url);e.message=`${e.message} (${t.host})`}catch{}throw e})}})})(void 0,void 0))}(e=>{let n=es();n&&s_.get(n)&&!function(e,t){var n,r,i,a;let s=t?.getDsn(),o=t?.getOptions().tunnel;return n=e,!!(r=s)&&n.includes(r.host)||(i=e,!!(a=o)&&t9(i)===t9(a))}(e.fetchData.url,n)&&(!function(e,t,n,r,i="auto.http.browser"){if(!e.fetchData)return;let{method:a,url:s}=e.fetchData,o=tf()&&t(s);if(e.endTimestamp&&o){let t=e.fetchData.__span;if(!t)return;let n=r[t];n&&(function(e,t){if(t.response){var n=t.response.status;e.setAttribute("http.response.status_code",n);let r=e_(n);"unknown_error"!==r.message&&e.setStatus(r);let i=t.response?.headers&&t.response.headers.get("content-length");if(i){let t=parseInt(i);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:2,message:"internal_error"});e.end()}(n,e),delete r[t]);return}let u=!!eF(),l=o&&u?function(e){let t=en(d());if(t.startInactiveSpan)return t.startInactiveSpan(e);let n=function(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let n={...t};return n.startTimestamp=ek(e.startTime),delete n.startTime,n}return t}(e),{forceTransaction:r,parentSpan:i}=e;return(e.scope?t=>ea(e.scope,t):void 0!==i?e=>ne(i,e):e=>e())(()=>{let t=er(),i=function(e){let t=e[z];if(!t)return;let n=es();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?eB(t):t}(t);return e.onlyIfParent&&!i?new t5:function({parentSpan:e,spanArguments:t,forceTransaction:n,scope:r}){var i,a;let s;if(!tf()){let r=new t5;if(n||!e){let e={sampled:"false",sample_rate:"0",transaction:t.name,...ty(r)};k(r,t_,e)}return r}let o=ei();if(e&&!n)s=function(e,t,n){let{spanId:r,traceId:i}=e.spanContext(),a=!t.getScopeData().sdkProcessingMetadata[t7]&&eM(e),s=a?new t3({...n,parentSpanId:r,traceId:i,sampled:a}):new t5({traceId:i});eU(e,s);let o=es();return o&&(o.emit("spanStart",s),n.endTimestamp&&o.emit("spanEnd",s)),s}(e,r,t),eU(e,s);else if(e){let n=ty(e),{traceId:i,spanId:a}=e.spanContext(),o=eM(e);k(s=nn({traceId:i,parentSpanId:a,...t},r,o),t_,n)}else{let{traceId:e,dsc:n,parentSpanId:i,sampled:a}={...o.getPropagationContext(),...r.getPropagationContext()};s=nn({traceId:e,parentSpanId:i,...t},r,a),n&&k(s,t_,n)}return tJ(s),ey(s,r,o),s}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:t})})}(function(e,t,n){let r=function(e,t){let n=0>=e.indexOf("://")&&0!==e.indexOf("//"),r=(void 0)??(n?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(e,r))return;let t=new URL(e,r);if(n)return{isRelative:n,pathname:t.pathname,search:t.search,hash:t.hash};return t}catch{}}(e);return{name:r?`${t} ${function(e){if(e7(e))return e.pathname;let t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}(r)}`:t,attributes:function(e,t,n,r){let i={url:e,type:"fetch","http.method":n,[ed]:r,[ec]:"http.client"};return t&&(e7(t)||(i["http.url"]=t.href,i["server.address"]=t.host),t.search&&(i["http.query"]=t.search),t.hash&&(i["http.fragment"]=t.hash)),i}(e,r,t,n)}}(s,a,i)):new t5;if(e.fetchData.__span=l.spanContext().spanId,r[l.spanContext().spanId]=l,n(e.fetchData.url)){let t=e.args[0],n=e.args[1]||{},r=function(e,t,n){var r;let i=function(e={}){let t=es();if(!function(){let e=es();return e?.getOptions().enabled!==!1&&!!e?.getTransport()}()||!t)return{};let n=en(d());if(n.getTraceData)return n.getTraceData(e);let r=er(),i=e.span||eF(),a=i?function(e){let{traceId:t,spanId:n}=e.spanContext();return eA(t,n,eM(e))}(i):function(e){let{traceId:t,sampled:n,propagationSpanId:r}=e.getPropagationContext();return eA(t,r,n)}(r),s=eO(i?ty(i):tg(t,r));return eR.test(a)?{"sentry-trace":a,baggage:s}:(A.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}({span:n}),a=i["sentry-trace"],s=i.baggage;if(!a)return;let o=t.headers||(O(e)?e.headers:void 0);if(!o)return{...i};if(r=o,"undefined"!=typeof Headers&&S(r,Headers)){let e=new Headers(o);if(e.get("sentry-trace")||e.set("sentry-trace",a),s){let t=e.get("baggage");t?nr(t)||e.set("baggage",`${t},${s}`):e.set("baggage",s)}return e}if(Array.isArray(o)){let e=[...o];o.find(e=>"sentry-trace"===e[0])||e.push(["sentry-trace",a]);let t=o.find(e=>"baggage"===e[0]&&nr(e[1]));return s&&!t&&e.push(["baggage",s]),e}{let e="sentry-trace"in o?o["sentry-trace"]:void 0,t="baggage"in o?o.baggage:void 0,n=t?Array.isArray(t)?[...t]:[t]:[],r=t&&(Array.isArray(t)?t.find(e=>nr(e)):nr(t));return s&&!r&&n.push(s),{...o,"sentry-trace":e??a,baggage:n.length>0?n.join(","):void 0}}}(t,n,tf()&&u?l:void 0);r&&(e.args[1]=n,n.headers=r)}let c=es();if(c){let t={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};c.emit("beforeOutgoingRequestSpan",l,t)}}(e,o,s,a,"auto.http.wintercg_fetch"),t&&function(e){let{startTimestamp:t,endTimestamp:n}=e;if(!n)return;let r={method:e.fetchData.method,url:e.fetchData.url};if(e.error)ni({category:"fetch",data:r,level:"error",type:"http"},{data:e.error,input:e.args,startTimestamp:t,endTimestamp:n});else{let i=e.response;r.request_body_size=e.fetchData.request_body_size,r.response_body_size=e.fetchData.response_body_size,r.status_code=i?.status;let a={input:e.args,response:i,startTimestamp:t,endTimestamp:n},s=function(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}(r.status_code);ni({category:"fetch",data:r,type:"http",level:s},a)}}(e))})},setup(e){s_.set(e,!0)}}};class sg{constructor(e=30){this.$=[],this._taskProducers=[],this._bufferSize=e}add(e){return this._taskProducers.length>=this._bufferSize?Promise.reject(na):(this._taskProducers.push(e),Promise.resolve({}))}drain(e){let t=[...this._taskProducers];return this._taskProducers=[],new Promise(n=>{let r=setTimeout(()=>{e&&e>0&&n(!1)},e);Promise.all(t.map(e=>e().then(null,()=>{}))).then(()=>{clearTimeout(r),n(!0)})})}}function sy(e){return function(e,t,n=function(e){let t=[];function n(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(void 0===e||t.length<e))return tO(na);let i=r();return -1===t.indexOf(i)&&t.push(i),i.then(()=>n(i)).then(null,()=>n(i).then(null,()=>{})),i},drain:function(e){return new tx((n,r)=>{let i=t.length;if(!i)return n(!0);let a=setTimeout(()=>{e&&e>0&&n(!1)},e);t.forEach(e=>{tb(e).then(()=>{--i||(clearTimeout(a),n(!0))},r)})})}}}(e.bufferSize||64)){let r={};return{send:function(i){let a=[];if(tl(i,(t,n)=>{let i=td[n];!function(e,t,n=Date.now()){return(e[t]||e.all||0)>n}(r,i)?a.push(t):e.recordDroppedEvent("ratelimit_backoff",i)}),0===a.length)return tb({});let s=tu(i[0],a),o=t=>{tl(s,(n,r)=>{e.recordDroppedEvent(t,td[r])})};return n.add(()=>t({body:function(e){let[t,n]=e,r=JSON.stringify(t);function i(e){"string"==typeof r?r="string"==typeof e?r+e:[tc(r),e]:r.push("string"==typeof e?tc(e):e)}for(let e of n){let[t,n]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof n||n instanceof Uint8Array)i(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify(to(n))}i(e)}}return"string"==typeof r?r:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),n=0;for(let r of e)t.set(r,n),n+=r.length;return t}(r)}(s)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&l&&A.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),r=function(e,{statusCode:t,headers:n},r=Date.now()){let i={...e},a=n?.["x-sentry-rate-limits"],s=n?.["retry-after"];if(a)for(let e of a.trim().split(",")){let[t,n,,,a]=e.split(":",5),s=parseInt(t,10),o=(isNaN(s)?60:s)*1e3;if(n)for(let e of n.split(";"))"metric_bucket"===e?(!a||a.split(";").includes("custom"))&&(i[e]=r+o):i[e]=r+o;else i.all=r+o}else s?i.all=r+function(e,t=Date.now()){let n=parseInt(`${e}`,10);if(!isNaN(n))return 1e3*n;let r=Date.parse(`${e}`);return isNaN(r)?6e4:r-t}(s,r):429===t&&(i.all=r+6e4);return i}(r,e),e),e=>{throw o("network_error"),l&&A.error("Encountered error running transport request:",e),e})).then(e=>e,e=>{if(e===na)return l&&A.error("Skipped sending event because buffer is full."),o("queue_overflow"),tb({});throw e})},flush:e=>n.drain(e)}}(e,function(t){let n={body:t.body,method:"POST",headers:e.headers,...e.fetchOptions};var r=()=>fetch(e.url,n).then(e=>({statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}));let i=en(d());return i.suppressTracing?i.suppressTracing(r):ea(e=>(e.setSDKProcessingMetadata({[t7]:!0}),r()))},new sg(e.bufferSize))}let sv=["addListener","on","once","prependListener","prependOnceListener"];class sE{constructor(){sE.prototype.__init.call(this),sE.prototype.__init2.call(this)}bind(e,t){return"object"==typeof t&&null!==t&&"on"in t?this._bindEventEmitter(e,t):"function"==typeof t?this._bindFunction(e,t):t}_bindFunction(e,t){let n=this,r=function(...r){return n.with(e,()=>t.apply(this,r))};return Object.defineProperty(r,"length",{enumerable:!1,configurable:!0,writable:!1,value:t.length}),r}_bindEventEmitter(e,t){return void 0!==this._getPatchMap(t)||(this._createPatchMap(t),sv.forEach(n=>{void 0!==t[n]&&(t[n]=this._patchAddListener(t,t[n],e))}),"function"==typeof t.removeListener&&(t.removeListener=this._patchRemoveListener(t,t.removeListener)),"function"==typeof t.off&&(t.off=this._patchRemoveListener(t,t.off)),"function"==typeof t.removeAllListeners&&(t.removeAllListeners=this._patchRemoveAllListeners(t,t.removeAllListeners))),t}_patchRemoveListener(e,t){let n=this;return function(r,i){let a=n._getPatchMap(e)?.[r];if(void 0===a)return t.call(this,r,i);let s=a.get(i);return t.call(this,r,s||i)}}_patchRemoveAllListeners(e,t){let n=this;return function(r){let i=n._getPatchMap(e);return void 0!==i&&(0==arguments.length?n._createPatchMap(e):void 0!==i[r]&&delete i[r]),t.apply(this,arguments)}}_patchAddListener(e,t,n){let r=this;return function(i,a){if(r._wrapped)return t.call(this,i,a);let s=r._getPatchMap(e);void 0===s&&(s=r._createPatchMap(e));let o=s[i];void 0===o&&(o=new WeakMap,s[i]=o);let u=r.bind(n,a);o.set(a,u),r._wrapped=!0;try{return t.call(this,i,u)}finally{r._wrapped=!1}}}_createPatchMap(e){let t=Object.create(null);return e[this._kOtListeners]=t,t}_getPatchMap(e){return e[this._kOtListeners]}__init(){this._kOtListeners=Symbol("OtListeners")}__init2(){this._wrapped=!1}}class sT extends sE{constructor(){super();let e=u.AsyncLocalStorage;e?this._asyncLocalStorage=new e:(sf&&A.warn("Tried to register AsyncLocalStorage async context strategy in a runtime that doesn't support AsyncLocalStorage."),this._asyncLocalStorage={getStore(){},run(e,t,...n){return t.apply(this,n)},disable(){}})}active(){return this._asyncLocalStorage.getStore()??nH}with(e,t,n,...r){let i=null==n?t:t.bind(n);return this._asyncLocalStorage.run(e,i,...r)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}}let sS=ez([90,function(e){let t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{let i=r.match(n);if(i){let t,n,r,a,s;if(i[1]){let e=(r=i[1]).lastIndexOf(".");if("."===r[e-1]&&e--,e>0){t=r.slice(0,e),n=r.slice(e+1);let i=t.indexOf(".Module");i>0&&(r=r.slice(i+1),t=t.slice(0,i))}a=void 0}n&&(a=t,s=n),"<anonymous>"===n&&(s=void 0,r=void 0),void 0===r&&(s=s||"?",r=a?`${a}.${s}`:s);let o=i[2]?.startsWith("file://")?i[2].slice(7):i[2],u="native"===i[5];return o?.match(/\/[A-Z]:/)&&(o=o.slice(1)),o||!i[5]||u||(o=i[5]),{filename:o?decodeURI(o):void 0,module:e?e(o):void 0,function:r,lineno:ns(i[3]),colno:ns(i[4]),in_app:function(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==e&&!e.includes("node_modules/")}(o||"",u)}}if(r.match(t))return{filename:r}}}(void 0)]);function sb(e){return[no(),nh(),ng(),nE(),sm(),nS(),...e.sendDefaultPii?[nw()]:[]]}Symbol.toStringTag,n(552);let sO={client:"client",server:"server",edgeServer:"edge-server"};sO.client,sO.server,sO.edgeServer,Symbol("polyfills");let sx="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;async function sw(){try{sx&&A.log("Flushing events..."),await tq(2e3),sx&&A.log("Done flushing events")}catch(e){sx&&A.log("Error while flushing events:\n",e)}}let sR=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function sA(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:"/";i&&(t=`${i}/${t}`,n="/"===i.charAt(0))}return t=(function(e,t){let n=0;for(let t=e.length-1;t>=0;t--){let r=e[t];"."===r?e.splice(t,1):".."===r?(e.splice(t,1),n++):n&&(e.splice(t,1),n--)}if(t)for(;n--;)e.unshift("..");return e})(t.split("/").filter(e=>!!e),!n).join("/"),(n?"/":"")+t||"."}function sI(e){let t=0;for(;t<e.length&&""===e[t];t++);let n=e.length-1;for(;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}let sP=(e={})=>{let t=e.root,n=e.prefix||"app:///",r="window"in u&&!!u.window,i=e.iteratee||function({isBrowser:e,root:t,prefix:n}){return r=>{if(!r.filename)return r;let i=/^[a-zA-Z]:\\/.test(r.filename)||r.filename.includes("\\")&&!r.filename.includes("/"),a=/^\//.test(r.filename);if(e){if(t){let e=r.filename;0===e.indexOf(t)&&(r.filename=e.replace(t,n))}}else if(i||a){let e=i?r.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):r.filename,a=t?function(e,t){e=sA(e).slice(1),t=sA(t).slice(1);let n=sI(e.split("/")),r=sI(t.split("/")),i=Math.min(n.length,r.length),a=i;for(let e=0;e<i;e++)if(n[e]!==r[e]){a=e;break}let s=[];for(let e=a;e<n.length;e++)s.push("..");return(s=s.concat(r.slice(a))).join("/")}(t,e):function(e){let t=e.length>1024?`<truncated>${e.slice(-1024)}`:e,n=sR.exec(t);return n?n.slice(1):[]}(e)[2]||"";r.filename=`${n}${a}`}return r}}({isBrowser:r,root:t,prefix:n});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t?.frames&&t.frames.map(e=>i(e))}}}})}}}catch(t){return e}}(t)),t}}},sC=({distDirName:e})=>{let t=e.replace(/(\/|\\)$/,""),n=RegExp(`.*${t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}`);return{...sP({iteratee:e=>(e.filename=e.filename?.replace(n,"app:///_next"),e)}),name:"DistDirRewriteFrames"}};function sk(e,t){if(e instanceof Promise)throw Error(t)}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let n of e)t[n]=n;return t},e.getValidEnumValues=t=>{let n=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(let n of e)if(t(n))return n},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(nF||(nF={})),(nZ||(nZ={})).mergeShapes=(e,t)=>({...e,...t});let sL=nF.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),sN=e=>{switch(typeof e){case"undefined":return sL.undefined;case"string":return sL.string;case"number":return Number.isNaN(e)?sL.nan:sL.number;case"boolean":return sL.boolean;case"function":return sL.function;case"bigint":return sL.bigint;case"symbol":return sL.symbol;case"object":if(Array.isArray(e))return sL.array;if(null===e)return sL.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return sL.promise;if("undefined"!=typeof Map&&e instanceof Map)return sL.map;if("undefined"!=typeof Set&&e instanceof Set)return sL.set;if("undefined"!=typeof Date&&e instanceof Date)return sL.date;return sL.object;default:return sL.unknown}},sM=nF.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class sD extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(r);else if("invalid_return_type"===i.code)r(i.returnTypeError);else if("invalid_arguments"===i.code)r(i.argumentsError);else if(0===i.path.length)n._errors.push(t(i));else{let e=n,r=0;for(;r<i.path.length;){let n=i.path[r];r===i.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(i))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(e){if(!(e instanceof sD))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,nF.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},n=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}sD.create=e=>new sD(e);let sj=(e,t)=>{let n;switch(e.code){case sM.invalid_type:n=e.received===sL.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case sM.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,nF.jsonStringifyReplacer)}`;break;case sM.unrecognized_keys:n=`Unrecognized key(s) in object: ${nF.joinValues(e.keys,", ")}`;break;case sM.invalid_union:n="Invalid input";break;case sM.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${nF.joinValues(e.options)}`;break;case sM.invalid_enum_value:n=`Invalid enum value. Expected ${nF.joinValues(e.options)}, received '${e.received}'`;break;case sM.invalid_arguments:n="Invalid function arguments";break;case sM.invalid_return_type:n="Invalid function return type";break;case sM.invalid_date:n="Invalid date";break;case sM.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:nF.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case sM.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case sM.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case sM.custom:n="Invalid input";break;case sM.invalid_intersection_types:n="Intersection results could not be merged";break;case sM.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case sM.not_finite:n="Number must be finite";break;default:n=t.defaultError,nF.assertNever(e)}return{message:n}},s$=e=>{let{data:t,path:n,errorMaps:r,issueData:i}=e,a=[...n,...i.path||[]],s={...i,path:a};if(void 0!==i.message)return{...i,path:a,message:i.message};let o="";for(let e of r.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return{...i,path:a,message:o}};function sU(e,t){let n=s$({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,sj,sj==sj?void 0:sj].filter(e=>!!e)});e.common.issues.push(n)}class sB{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let n=[];for(let r of t){if("aborted"===r.status)return sF;"dirty"===r.status&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let n=[];for(let e of t){let t=await e.key,r=await e.value;n.push({key:t,value:r})}return sB.mergeObjectSync(e,n)}static mergeObjectSync(e,t){let n={};for(let r of t){let{key:t,value:i}=r;if("aborted"===t.status||"aborted"===i.status)return sF;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||r.alwaysSet)&&(n[t.value]=i.value)}return{status:e.value,value:n}}}let sF=Object.freeze({status:"aborted"}),sZ=e=>({status:"dirty",value:e}),sV=e=>({status:"valid",value:e}),sG=e=>"aborted"===e.status,sz=e=>"dirty"===e.status,sX=e=>"valid"===e.status,sH=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(nV||(nV={}));var sK=function(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)},sY=function(e,t,n,r,i){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n};class sW{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let sq=(e,t)=>{if(sX(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new sD(e.common.issues);return this._error=t,this._error}}};function sJ(e){if(!e)return{};let{errorMap:t,invalid_type_error:n,required_error:r,description:i}=e;if(t&&(n||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:a}=e;return"invalid_enum_value"===t.code?{message:a??i.defaultError}:void 0===i.data?{message:a??r??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:a??n??i.defaultError}},description:i}}class sQ{get description(){return this._def.description}_getType(e){return sN(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:sN(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new sB,ctx:{common:e.parent.common,data:e.data,parsedType:sN(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(sH(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){let n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:sN(e)},r=this._parseSync({data:e,path:n.path,parent:n});return sq(n,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:sN(e)};if(!this["~standard"].async)try{let n=this._parseSync({data:e,path:[],parent:t});return sX(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>sX(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:sN(e)},r=this._parse({data:e,path:n.path,parent:n});return sq(n,await (sH(r)?r:Promise.resolve(r)))}refine(e,t){let n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let i=e(t),a=()=>r.addIssue({code:sM.custom,...n(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(a(),!1)):!!i||(a(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue("function"==typeof t?t(n,r):t),!1))}_refinement(e){return new oD({schema:this,typeName:nX.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return oj.create(this,this._def)}nullable(){return o$.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ov.create(this)}promise(){return oM.create(this,this._def)}or(e){return oT.create([this,e],this._def)}and(e){return oO.create(this,e,this._def)}transform(e){return new oD({...sJ(this._def),schema:this,typeName:nX.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new oU({...sJ(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:nX.ZodDefault})}brand(){return new oZ({typeName:nX.ZodBranded,type:this,...sJ(this._def)})}catch(e){return new oB({...sJ(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:nX.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return oV.create(this,e)}readonly(){return oG.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let s0=/^c[^\s-]{8,}$/i,s1=/^[0-9a-z]+$/,s2=/^[0-9A-HJKMNP-TV-Z]{26}$/i,s4=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,s9=/^[a-z0-9_-]{21}$/i,s5=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,s3=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,s6=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,s8=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,s7=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,oe=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ot=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,on=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,or=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,oi="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",oa=RegExp(`^${oi}$`);function os(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}class oo extends sQ{_parse(e){var t,n,r,i;let a;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==sL.string){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.string,received:t.parsedType}),sF}let s=new sB;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(sU(a=this._getOrReturnCtx(e,a),{code:sM.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),s.dirty());else if("max"===u.kind)e.data.length>u.value&&(sU(a=this._getOrReturnCtx(e,a),{code:sM.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),s.dirty());else if("length"===u.kind){let t=e.data.length>u.value,n=e.data.length<u.value;(t||n)&&(a=this._getOrReturnCtx(e,a),t?sU(a,{code:sM.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):n&&sU(a,{code:sM.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),s.dirty())}else if("email"===u.kind)s6.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"email",code:sM.invalid_string,message:u.message}),s.dirty());else if("emoji"===u.kind)o||(o=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),o.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"emoji",code:sM.invalid_string,message:u.message}),s.dirty());else if("uuid"===u.kind)s4.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"uuid",code:sM.invalid_string,message:u.message}),s.dirty());else if("nanoid"===u.kind)s9.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"nanoid",code:sM.invalid_string,message:u.message}),s.dirty());else if("cuid"===u.kind)s0.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"cuid",code:sM.invalid_string,message:u.message}),s.dirty());else if("cuid2"===u.kind)s1.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"cuid2",code:sM.invalid_string,message:u.message}),s.dirty());else if("ulid"===u.kind)s2.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"ulid",code:sM.invalid_string,message:u.message}),s.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{sU(a=this._getOrReturnCtx(e,a),{validation:"url",code:sM.invalid_string,message:u.message}),s.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"regex",code:sM.invalid_string,message:u.message}),s.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),s.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:{startsWith:u.value},message:u.message}),s.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:{endsWith:u.value},message:u.message}),s.dirty()):"datetime"===u.kind?(function(e){let t=`${oi}T${os(e)}`,n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,RegExp(`^${t}$`)})(u).test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:"datetime",message:u.message}),s.dirty()):"date"===u.kind?oa.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:"date",message:u.message}),s.dirty()):"time"===u.kind?RegExp(`^${os(u)}$`).test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{code:sM.invalid_string,validation:"time",message:u.message}),s.dirty()):"duration"===u.kind?s3.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"duration",code:sM.invalid_string,message:u.message}),s.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(n=u.version)||!n)&&s8.test(t)||("v6"===n||!n)&&oe.test(t))&&1&&(sU(a=this._getOrReturnCtx(e,a),{validation:"ip",code:sM.invalid_string,message:u.message}),s.dirty())):"jwt"===u.kind?!function(e,t){if(!s5.test(e))return!1;try{let[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),i=JSON.parse(atob(r));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(sU(a=this._getOrReturnCtx(e,a),{validation:"jwt",code:sM.invalid_string,message:u.message}),s.dirty()):"cidr"===u.kind?(r=e.data,!(("v4"===(i=u.version)||!i)&&s7.test(r)||("v6"===i||!i)&&ot.test(r))&&1&&(sU(a=this._getOrReturnCtx(e,a),{validation:"cidr",code:sM.invalid_string,message:u.message}),s.dirty())):"base64"===u.kind?on.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"base64",code:sM.invalid_string,message:u.message}),s.dirty()):"base64url"===u.kind?or.test(e.data)||(sU(a=this._getOrReturnCtx(e,a),{validation:"base64url",code:sM.invalid_string,message:u.message}),s.dirty()):nF.assertNever(u);return{status:s.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:sM.invalid_string,...nV.errToObj(n)})}_addCheck(e){return new oo({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...nV.errToObj(e)})}url(e){return this._addCheck({kind:"url",...nV.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...nV.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...nV.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...nV.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...nV.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...nV.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...nV.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...nV.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...nV.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...nV.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...nV.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...nV.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...nV.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...nV.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...nV.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...nV.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...nV.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...nV.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...nV.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...nV.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...nV.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...nV.errToObj(t)})}nonempty(e){return this.min(1,nV.errToObj(e))}trim(){return new oo({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new oo({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new oo({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}oo.create=e=>new oo({checks:[],typeName:nX.ZodString,coerce:e?.coerce??!1,...sJ(e)});class ou extends sQ{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==sL.number){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.number,received:t.parsedType}),sF}let n=new sB;for(let r of this._def.checks)"int"===r.kind?nF.isInteger(e.data)||(sU(t=this._getOrReturnCtx(e,t),{code:sM.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):"multipleOf"===r.kind?0!==function(e,t){let n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,i=n>r?n:r;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,r.value)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(sU(t=this._getOrReturnCtx(e,t),{code:sM.not_finite,message:r.message}),n.dirty()):nF.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,nV.toString(t))}gt(e,t){return this.setLimit("min",e,!1,nV.toString(t))}lte(e,t){return this.setLimit("max",e,!0,nV.toString(t))}lt(e,t){return this.setLimit("max",e,!1,nV.toString(t))}setLimit(e,t,n,r){return new ou({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:nV.toString(r)}]})}_addCheck(e){return new ou({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:nV.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:nV.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:nV.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:nV.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:nV.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:nV.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:nV.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:nV.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:nV.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&nF.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks)if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;else"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value);return Number.isFinite(t)&&Number.isFinite(e)}}ou.create=e=>new ou({checks:[],typeName:nX.ZodNumber,coerce:e?.coerce||!1,...sJ(e)});class ol extends sQ{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==sL.bigint)return this._getInvalidInput(e);let n=new sB;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):nF.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.bigint,received:t.parsedType}),sF}gte(e,t){return this.setLimit("min",e,!0,nV.toString(t))}gt(e,t){return this.setLimit("min",e,!1,nV.toString(t))}lte(e,t){return this.setLimit("max",e,!0,nV.toString(t))}lt(e,t){return this.setLimit("max",e,!1,nV.toString(t))}setLimit(e,t,n,r){return new ol({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:nV.toString(r)}]})}_addCheck(e){return new ol({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:nV.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:nV.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:nV.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:nV.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:nV.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ol.create=e=>new ol({checks:[],typeName:nX.ZodBigInt,coerce:e?.coerce??!1,...sJ(e)});class oc extends sQ{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==sL.boolean){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.boolean,received:t.parsedType}),sF}return sV(e.data)}}oc.create=e=>new oc({typeName:nX.ZodBoolean,coerce:e?.coerce||!1,...sJ(e)});class od extends sQ{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==sL.date){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.date,received:t.parsedType}),sF}if(Number.isNaN(e.data.getTime()))return sU(this._getOrReturnCtx(e),{code:sM.invalid_date}),sF;let n=new sB;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),n.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(sU(t=this._getOrReturnCtx(e,t),{code:sM.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),n.dirty()):nF.assertNever(r);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new od({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:nV.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:nV.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}od.create=e=>new od({checks:[],coerce:e?.coerce||!1,typeName:nX.ZodDate,...sJ(e)});class op extends sQ{_parse(e){if(this._getType(e)!==sL.symbol){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.symbol,received:t.parsedType}),sF}return sV(e.data)}}op.create=e=>new op({typeName:nX.ZodSymbol,...sJ(e)});class oh extends sQ{_parse(e){if(this._getType(e)!==sL.undefined){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.undefined,received:t.parsedType}),sF}return sV(e.data)}}oh.create=e=>new oh({typeName:nX.ZodUndefined,...sJ(e)});class of extends sQ{_parse(e){if(this._getType(e)!==sL.null){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.null,received:t.parsedType}),sF}return sV(e.data)}}of.create=e=>new of({typeName:nX.ZodNull,...sJ(e)});class o_ extends sQ{constructor(){super(...arguments),this._any=!0}_parse(e){return sV(e.data)}}o_.create=e=>new o_({typeName:nX.ZodAny,...sJ(e)});class om extends sQ{constructor(){super(...arguments),this._unknown=!0}_parse(e){return sV(e.data)}}om.create=e=>new om({typeName:nX.ZodUnknown,...sJ(e)});class og extends sQ{_parse(e){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.never,received:t.parsedType}),sF}}og.create=e=>new og({typeName:nX.ZodNever,...sJ(e)});class oy extends sQ{_parse(e){if(this._getType(e)!==sL.undefined){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.void,received:t.parsedType}),sF}return sV(e.data)}}oy.create=e=>new oy({typeName:nX.ZodVoid,...sJ(e)});class ov extends sQ{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==sL.array)return sU(t,{code:sM.invalid_type,expected:sL.array,received:t.parsedType}),sF;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,i=t.data.length<r.exactLength.value;(e||i)&&(sU(t,{code:e?sM.too_big:sM.too_small,minimum:i?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(sU(t,{code:sM.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(sU(t,{code:sM.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>r.type._parseAsync(new sW(t,e,t.path,n)))).then(e=>sB.mergeArray(n,e));let i=[...t.data].map((e,n)=>r.type._parseSync(new sW(t,e,t.path,n)));return sB.mergeArray(n,i)}get element(){return this._def.type}min(e,t){return new ov({...this._def,minLength:{value:e,message:nV.toString(t)}})}max(e,t){return new ov({...this._def,maxLength:{value:e,message:nV.toString(t)}})}length(e,t){return new ov({...this._def,exactLength:{value:e,message:nV.toString(t)}})}nonempty(e){return this.min(1,e)}}ov.create=(e,t)=>new ov({type:e,minLength:null,maxLength:null,exactLength:null,typeName:nX.ZodArray,...sJ(t)});class oE extends sQ{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=nF.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==sL.object){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.object,received:t.parsedType}),sF}let{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof og&&"strip"===this._def.unknownKeys))for(let e in n.data)i.includes(e)||a.push(e);let s=[];for(let e of i){let t=r[e],i=n.data[e];s.push({key:{status:"valid",value:e},value:t._parse(new sW(n,i,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof og){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of a)s.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)a.length>0&&(sU(n,{code:sM.unrecognized_keys,keys:a}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of a){let r=n.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new sW(n,r,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>sB.mergeObjectSync(t,e)):sB.mergeObjectSync(t,s)}get shape(){return this._def.shape()}strict(e){return nV.errToObj,new oE({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{let r=this._def.errorMap?.(t,n).message??n.defaultError;return"unrecognized_keys"===t.code?{message:nV.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new oE({...this._def,unknownKeys:"strip"})}passthrough(){return new oE({...this._def,unknownKeys:"passthrough"})}extend(e){return new oE({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new oE({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:nX.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new oE({...this._def,catchall:e})}pick(e){let t={};for(let n of nF.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new oE({...this._def,shape:()=>t})}omit(e){let t={};for(let n of nF.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new oE({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof oE){let n={};for(let r in t.shape){let i=t.shape[r];n[r]=oj.create(e(i))}return new oE({...t._def,shape:()=>n})}if(t instanceof ov)return new ov({...t._def,type:e(t.element)});if(t instanceof oj)return oj.create(e(t.unwrap()));if(t instanceof o$)return o$.create(e(t.unwrap()));if(t instanceof ox)return ox.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let n of nF.objectKeys(this.shape)){let r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new oE({...this._def,shape:()=>t})}required(e){let t={};for(let n of nF.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof oj;)e=e._def.innerType;t[n]=e}return new oE({...this._def,shape:()=>t})}keyof(){return ok(nF.objectKeys(this.shape))}}oE.create=(e,t)=>new oE({shape:()=>e,unknownKeys:"strip",catchall:og.create(),typeName:nX.ZodObject,...sJ(t)}),oE.strictCreate=(e,t)=>new oE({shape:()=>e,unknownKeys:"strict",catchall:og.create(),typeName:nX.ZodObject,...sJ(t)}),oE.lazycreate=(e,t)=>new oE({shape:e,unknownKeys:"strip",catchall:og.create(),typeName:nX.ZodObject,...sJ(t)});class oT extends sQ{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async e=>{let n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let n of e)if("dirty"===n.result.status)return t.common.issues.push(...n.ctx.common.issues),n.result;let n=e.map(e=>new sD(e.ctx.common.issues));return sU(t,{code:sM.invalid_union,unionErrors:n}),sF});{let e,r=[];for(let i of n){let n={...t,common:{...t.common,issues:[]},parent:null},a=i._parseSync({data:t.data,path:t.path,parent:n});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=r.map(e=>new sD(e));return sU(t,{code:sM.invalid_union,unionErrors:i}),sF}}get options(){return this._def.options}}oT.create=(e,t)=>new oT({options:e,typeName:nX.ZodUnion,...sJ(t)});let oS=e=>{if(e instanceof oP)return oS(e.schema);if(e instanceof oD)return oS(e.innerType());if(e instanceof oC)return[e.value];if(e instanceof oL)return e.options;if(e instanceof oN)return nF.objectValues(e.enum);else if(e instanceof oU)return oS(e._def.innerType);else if(e instanceof oh)return[void 0];else if(e instanceof of)return[null];else if(e instanceof oj)return[void 0,...oS(e.unwrap())];else if(e instanceof o$)return[null,...oS(e.unwrap())];else if(e instanceof oZ)return oS(e.unwrap());else if(e instanceof oG)return oS(e.unwrap());else if(e instanceof oB)return oS(e._def.innerType);else return[]};class ob extends sQ{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==sL.object)return sU(t,{code:sM.invalid_type,expected:sL.object,received:t.parsedType}),sF;let n=this.discriminator,r=t.data[n],i=this.optionsMap.get(r);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(sU(t,{code:sM.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),sF)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){let r=new Map;for(let n of t){let t=oS(n.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(r.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);r.set(i,n)}}return new ob({typeName:nX.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...sJ(n)})}}class oO extends sQ{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(sG(e)||sG(r))return sF;let i=function e(t,n){let r=sN(t),i=sN(n);if(t===n)return{valid:!0,data:t};if(r===sL.object&&i===sL.object){let r=nF.objectKeys(n),i=nF.objectKeys(t).filter(e=>-1!==r.indexOf(e)),a={...t,...n};for(let r of i){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1};a[r]=i.data}return{valid:!0,data:a}}if(r===sL.array&&i===sL.array){if(t.length!==n.length)return{valid:!1};let r=[];for(let i=0;i<t.length;i++){let a=e(t[i],n[i]);if(!a.valid)return{valid:!1};r.push(a.data)}return{valid:!0,data:r}}if(r===sL.date&&i===sL.date&&+t==+n)return{valid:!0,data:t};return{valid:!1}}(e.value,r.value);return i.valid?((sz(e)||sz(r))&&t.dirty(),{status:t.value,value:i.data}):(sU(n,{code:sM.invalid_intersection_types}),sF)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}oO.create=(e,t,n)=>new oO({left:e,right:t,typeName:nX.ZodIntersection,...sJ(n)});class ox extends sQ{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==sL.array)return sU(n,{code:sM.invalid_type,expected:sL.array,received:n.parsedType}),sF;if(n.data.length<this._def.items.length)return sU(n,{code:sM.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),sF;!this._def.rest&&n.data.length>this._def.items.length&&(sU(n,{code:sM.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...n.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new sW(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(r).then(e=>sB.mergeArray(t,e)):sB.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ox({...this._def,rest:e})}}ox.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ox({items:e,typeName:nX.ZodTuple,rest:null,...sJ(t)})};class ow extends sQ{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==sL.object)return sU(n,{code:sM.invalid_type,expected:sL.object,received:n.parsedType}),sF;let r=[],i=this._def.keyType,a=this._def.valueType;for(let e in n.data)r.push({key:i._parse(new sW(n,e,n.path,e)),value:a._parse(new sW(n,n.data[e],n.path,e)),alwaysSet:e in n.data});return n.common.async?sB.mergeObjectAsync(t,r):sB.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new ow(t instanceof sQ?{keyType:e,valueType:t,typeName:nX.ZodRecord,...sJ(n)}:{keyType:oo.create(),valueType:e,typeName:nX.ZodRecord,...sJ(t)})}}class oR extends sQ{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==sL.map)return sU(n,{code:sM.invalid_type,expected:sL.map,received:n.parsedType}),sF;let r=this._def.keyType,i=this._def.valueType,a=[...n.data.entries()].map(([e,t],a)=>({key:r._parse(new sW(n,e,n.path,[a,"key"])),value:i._parse(new sW(n,t,n.path,[a,"value"]))}));if(n.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let n of a){let r=await n.key,i=await n.value;if("aborted"===r.status||"aborted"===i.status)return sF;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let n of a){let r=n.key,i=n.value;if("aborted"===r.status||"aborted"===i.status)return sF;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}}}}oR.create=(e,t,n)=>new oR({valueType:t,keyType:e,typeName:nX.ZodMap,...sJ(n)});class oA extends sQ{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==sL.set)return sU(n,{code:sM.invalid_type,expected:sL.set,received:n.parsedType}),sF;let r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(sU(n,{code:sM.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(sU(n,{code:sM.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let i=this._def.valueType;function a(e){let n=new Set;for(let r of e){if("aborted"===r.status)return sF;"dirty"===r.status&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}let s=[...n.data.values()].map((e,t)=>i._parse(new sW(n,e,n.path,t)));return n.common.async?Promise.all(s).then(e=>a(e)):a(s)}min(e,t){return new oA({...this._def,minSize:{value:e,message:nV.toString(t)}})}max(e,t){return new oA({...this._def,maxSize:{value:e,message:nV.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}oA.create=(e,t)=>new oA({valueType:e,minSize:null,maxSize:null,typeName:nX.ZodSet,...sJ(t)});class oI extends sQ{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==sL.function)return sU(t,{code:sM.invalid_type,expected:sL.function,received:t.parsedType}),sF;function n(e,n){return s$({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sj,sj].filter(e=>!!e),issueData:{code:sM.invalid_arguments,argumentsError:n}})}function r(e,n){return s$({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sj,sj].filter(e=>!!e),issueData:{code:sM.invalid_return_type,returnTypeError:n}})}let i={errorMap:t.common.contextualErrorMap},a=t.data;if(this._def.returns instanceof oM){let e=this;return sV(async function(...t){let s=new sD([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw s.addIssue(n(t,e)),s}),u=await Reflect.apply(a,this,o);return await e._def.returns._def.type.parseAsync(u,i).catch(e=>{throw s.addIssue(r(u,e)),s})})}{let e=this;return sV(function(...t){let s=e._def.args.safeParse(t,i);if(!s.success)throw new sD([n(t,s.error)]);let o=Reflect.apply(a,this,s.data),u=e._def.returns.safeParse(o,i);if(!u.success)throw new sD([r(o,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new oI({...this._def,args:ox.create(e).rest(om.create())})}returns(e){return new oI({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new oI({args:e||ox.create([]).rest(om.create()),returns:t||om.create(),typeName:nX.ZodFunction,...sJ(n)})}}class oP extends sQ{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}oP.create=(e,t)=>new oP({getter:e,typeName:nX.ZodLazy,...sJ(t)});class oC extends sQ{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return sU(t,{received:t.data,code:sM.invalid_literal,expected:this._def.value}),sF}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ok(e,t){return new oL({values:e,typeName:nX.ZodEnum,...sJ(t)})}oC.create=(e,t)=>new oC({value:e,typeName:nX.ZodLiteral,...sJ(t)});class oL extends sQ{constructor(){super(...arguments),nG.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),n=this._def.values;return sU(t,{expected:nF.joinValues(n),received:t.parsedType,code:sM.invalid_type}),sF}if(sK(this,nG,"f")||sY(this,nG,new Set(this._def.values),"f"),!sK(this,nG,"f").has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return sU(t,{received:t.data,code:sM.invalid_enum_value,options:n}),sF}return sV(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return oL.create(e,{...this._def,...t})}exclude(e,t=this._def){return oL.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}nG=new WeakMap,oL.create=ok;class oN extends sQ{constructor(){super(...arguments),nz.set(this,void 0)}_parse(e){let t=nF.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==sL.string&&n.parsedType!==sL.number){let e=nF.objectValues(t);return sU(n,{expected:nF.joinValues(e),received:n.parsedType,code:sM.invalid_type}),sF}if(sK(this,nz,"f")||sY(this,nz,new Set(nF.getValidEnumValues(this._def.values)),"f"),!sK(this,nz,"f").has(e.data)){let e=nF.objectValues(t);return sU(n,{received:n.data,code:sM.invalid_enum_value,options:e}),sF}return sV(e.data)}get enum(){return this._def.values}}nz=new WeakMap,oN.create=(e,t)=>new oN({values:e,typeName:nX.ZodNativeEnum,...sJ(t)});class oM extends sQ{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==sL.promise&&!1===t.common.async?(sU(t,{code:sM.invalid_type,expected:sL.promise,received:t.parsedType}),sF):sV((t.parsedType===sL.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}oM.create=(e,t)=>new oM({type:e,typeName:nX.ZodPromise,...sJ(t)});class oD extends sQ{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===nX.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,i={addIssue:e=>{sU(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===r.type){let e=r.transform(n.data,i);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return sF;let r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===r.status?sF:"dirty"===r.status||"dirty"===t.value?sZ(r.value):r});{if("aborted"===t.value)return sF;let r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===r.status?sF:"dirty"===r.status||"dirty"===t.value?sZ(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,i);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>"aborted"===n.status?sF:("dirty"===n.status&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))));{let r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?sF:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type)if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>sX(e)?Promise.resolve(r.transform(e.value,i)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!sX(e))return e;let a=r.transform(e.value,i);if(a instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}nF.assertNever(r)}}oD.create=(e,t,n)=>new oD({schema:e,typeName:nX.ZodEffects,effect:t,...sJ(n)}),oD.createWithPreprocess=(e,t,n)=>new oD({schema:t,effect:{type:"preprocess",transform:e},typeName:nX.ZodEffects,...sJ(n)});class oj extends sQ{_parse(e){return this._getType(e)===sL.undefined?sV(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}oj.create=(e,t)=>new oj({innerType:e,typeName:nX.ZodOptional,...sJ(t)});class o$ extends sQ{_parse(e){return this._getType(e)===sL.null?sV(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}o$.create=(e,t)=>new o$({innerType:e,typeName:nX.ZodNullable,...sJ(t)});class oU extends sQ{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===sL.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}oU.create=(e,t)=>new oU({innerType:e,typeName:nX.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...sJ(t)});class oB extends sQ{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return sH(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new sD(n.common.issues)},input:n.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new sD(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}oB.create=(e,t)=>new oB({innerType:e,typeName:nX.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...sJ(t)});class oF extends sQ{_parse(e){if(this._getType(e)!==sL.nan){let t=this._getOrReturnCtx(e);return sU(t,{code:sM.invalid_type,expected:sL.nan,received:t.parsedType}),sF}return{status:"valid",value:e.data}}}oF.create=e=>new oF({typeName:nX.ZodNaN,...sJ(e)}),Symbol("zod_brand");class oZ extends sQ{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class oV extends sQ{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?sF:"dirty"===e.status?(t.dirty(),sZ(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})();{let e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?sF:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new oV({in:e,out:t,typeName:nX.ZodPipeline})}}class oG extends sQ{_parse(e){let t=this._def.innerType._parse(e),n=e=>(sX(e)&&(e.value=Object.freeze(e.value)),e);return sH(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}}oG.create=(e,t)=>new oG({innerType:e,typeName:nX.ZodReadonly,...sJ(t)}),oE.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(nX||(nX={}));let oz=oo.create;ou.create,oF.create,ol.create,oc.create,od.create,op.create,oh.create,of.create,o_.create,om.create,og.create,oy.create,ov.create,oE.create,oE.strictCreate,oT.create,ob.create,oO.create,ox.create,ow.create,oR.create,oA.create,oI.create,oP.create,oC.create,oL.create,oN.create,oM.create,oD.create,oj.create,o$.create,oD.createWithPreprocess,oV.create;let oX=void function(e={}){if(e3(),"phase-production-build"===process.env.NEXT_PHASE)return;let t=sb(e),n=process.env._sentryRewriteFramesDistDir||u._sentryRewriteFramesDistDir;n&&t.push(sC({distDirName:n}));let r={defaultIntegrations:t,release:process.env._sentryRelease||u._sentryRelease,...e};e8(r,"nextjs",["nextjs","vercel-edge"]);let i=function(e={}){var t,n;function r(){let e=a$(ro.active());return e||{scope:Y(),isolationScope:W()}}function i(){return r().scope}function a(){return r().isolationScope}if(t={withScope:function(e){let t=ro.active();return ro.with(t,()=>e(i()))},withSetScope:function(e,t){let n=ro.active();return ro.with(n.setValue(aM,e),()=>t(e))},withSetIsolationScope:function(e,t){let n=ro.active();return ro.with(n.setValue(aD,e),()=>t(a()))},withIsolationScope:function(e){let t=ro.active();return ro.with(t.setValue(aN,!0),()=>e(a()))},getCurrentScope:i,getIsolationScope:a,startSpan:a0,startSpanManual:a1,startInactiveSpan:a2,getActiveSpan:aG,suppressTracing:a7,getTraceData:se,continueTrace:a6,withActiveSpan:a4},p(d()).acs=t,er().update(e.initialScope),void 0===e.defaultIntegrations&&(e.defaultIntegrations=sb(e)),void 0===e.dsn&&process.env.SENTRY_DSN&&(e.dsn=process.env.SENTRY_DSN),void 0===e.tracesSampleRate&&process.env.SENTRY_TRACES_SAMPLE_RATE){let t=parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE);isFinite(t)&&(e.tracesSampleRate=t)}if(void 0===e.release){let t=function(e){if(process.env.SENTRY_RELEASE)return process.env.SENTRY_RELEASE;if(u.SENTRY_RELEASE?.id)return u.SENTRY_RELEASE.id;let t=process.env.GITHUB_SHA||process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_SHA||process.env.CI_BUILD_REF||process.env.CI_COMMIT_SHA||process.env.BITBUCKET_COMMIT,n=process.env.APPVEYOR_PULL_REQUEST_HEAD_COMMIT||process.env.APPVEYOR_REPO_COMMIT||process.env.CODEBUILD_RESOLVED_SOURCE_VERSION||process.env.AWS_COMMIT_ID||process.env.BUILD_SOURCEVERSION||process.env.GIT_CLONE_COMMIT_HASH||process.env.BUDDY_EXECUTION_REVISION||process.env.BUILDKITE_COMMIT||process.env.CIRCLE_SHA1||process.env.CIRRUS_CHANGE_IN_REPO||process.env.CF_REVISION||process.env.CM_COMMIT||process.env.CF_PAGES_COMMIT_SHA||process.env.DRONE_COMMIT_SHA||process.env.FC_GIT_COMMIT_SHA||process.env.HEROKU_TEST_RUN_COMMIT_VERSION||process.env.HEROKU_SLUG_COMMIT||process.env.RAILWAY_GIT_COMMIT_SHA||process.env.RENDER_GIT_COMMIT||process.env.SEMAPHORE_GIT_SHA||process.env.TRAVIS_PULL_REQUEST_SHA||process.env.VERCEL_GIT_COMMIT_SHA||process.env.VERCEL_GITHUB_COMMIT_SHA||process.env.VERCEL_GITLAB_COMMIT_SHA||process.env.VERCEL_BITBUCKET_COMMIT_SHA||process.env.ZEIT_GITHUB_COMMIT_SHA||process.env.ZEIT_GITLAB_COMMIT_SHA||process.env.ZEIT_BITBUCKET_COMMIT_SHA,r=process.env.CI_COMMIT_ID||process.env.SOURCE_COMMIT||process.env.SOURCE_VERSION||process.env.GIT_COMMIT||process.env.COMMIT_REF||process.env.BUILD_VCS_NUMBER||process.env.CI_COMMIT_SHA;return t||n||r||void 0}();void 0!==t&&(e.release=t)}e.environment=e.environment||process.env.SENTRY_ENVIRONMENT||function(e){let t=process.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}()||"production";let s=new rq({...e,stackParser:Array.isArray(n=e.stackParser||sS)?ez(...n):n,integrations:function(e){let t,n=e.defaultIntegrations||[],r=e.integrations;if(n.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(r))t=[...n,...r];else if("function"==typeof r){let e=r(n);t=Array.isArray(e)?e:[e]}else t=n;let i={};return t.forEach(e=>{let{name:t}=e,n=i[t];n&&!n.isDefaultInstance&&e.isDefaultInstance||(i[t]=e)}),Object.values(i)}(e),transport:e.transport||sy});return er().setClient(s),s.init(),e.skipOpenTelemetrySetup||(function(e){e.getOptions().debug&&function(){let e=new Proxy(A,{get:(e,t,n)=>Reflect.get(e,"verbose"===t?"debug":t,n)});rk.disable(),rk.setLogger(e,nC.DEBUG)}();let t=new ay({sampler:new sp(e),resource:new i0({"service.name":"edge","service.namespace":"sentry","service.version":c}),forceFlushTimeoutMillis:500,spanProcessors:[new sd({timeout:e.getOptions().maxSpanWaitDuration})]}),n=function(e){class t extends e{constructor(...e){super(...e),aK("SentryContextManager")}with(e,t,n,...r){let i=a$(e),a=i?.scope||er(),s=i?.isolationScope||ei(),o=!0===e.getValue(aN),u=e.getValue(aM),l=e.getValue(aD),c=u||a.clone(),d=aU(e,{scope:c,isolationScope:l||(o?s.clone():s)}).deleteValue(aN).deleteValue(aM).deleteValue(aD);return k(c,aj,d),super.with(d,t,n,...r)}}return t}(sT);rC.setGlobalTracerProvider(t),rY.setGlobalPropagator(new aY),ro.setGlobalContextManager(new n),e.traceProvider=t}(s),function(){if(!sf)return;let e=Array.from(aH),t=["SentryContextManager","SentryPropagator"];for(let n of(tf()&&t.push("SentrySpanProcessor"),t))e.includes(n)||A.error(`You have to set up the ${n}. Without this, the OpenTelemetry & Sentry integration will not work properly.`);e.includes("SentrySampler")||A.warn("You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.")}()),s.on("createDsc",(e,t)=>{if(!t)return;let n=eN(t).data[eu],{description:r}=t.name?aZ(t):{description:void 0};if("url"!==n&&r&&(e.transaction=r),tf()){let n=aB(t.spanContext());e.sampled=void 0==n?void 0:String(n)}}),s.on("preprocessEvent",e=>{let t=aG();if(t&&"transaction"!==e.type)return e.contexts={trace:eP(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:ty(eB(t)),...e.sdkProcessingMetadata},e}),s}(r);i?.on("spanStart",e=>{let t=eN(e).data;t?.["next.span_type"]!==void 0&&e.setAttribute(ed,"auto"),t?.["next.span_type"]==="Middleware.execute"&&(e.setAttribute(ec,"http.server.middleware"),e.setAttribute(eu,"url"))}),i?.on("preprocessEvent",e=>{"transaction"===e.type&&e.contexts?.trace?.data?.["next.span_type"]==="Middleware.execute"&&e.contexts?.trace?.data?.["next.span_name"]&&e.transaction&&(e.transaction=tt(e.contexts.trace.data["next.span_name"]))}),i?.on("spanEnd",e=>{e===eB(e)&&function(e){let t=u[Symbol.for("@vercel/request-context")],n=t?.get&&t.get()?t.get():{};n?.waitUntil&&n.waitUntil(e)}(sw())})}({dsn:function(e){let t="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},r=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return function(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,n]of Object.entries(t))""===n&&delete t[e];if(e.skipValidation)return t;let n="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},a=e.isServer??("undefined"==typeof window||"Deno"in window),s=a?{...r,...i,...n}:{...n,...i},o=e.createFinalSchema?.(s,a)["~standard"].validate(t)??function(e,t){let n={},r=[];for(let i in e){let a=e[i]["~standard"].validate(t[i]);if(sk(a,`Validation must be synchronous, but ${i} returned a Promise.`),a.issues){r.push(...a.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}n[i]=a.value}return r.length?{issues:r}:{value:n}}(s,t);sk(o,"Validation must be synchronous");let u=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),l=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(o.issues)return u(o.issues);let c=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),d=e=>a||!c(e),p=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),o.value),{get(e,t){if("string"==typeof t&&!p(t))return d(t)?Reflect.get(e,t):l(t)}})}({...e,shared:r,client:t,server:n,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}({server:{BETTERSTACK_API_KEY:oz().optional(),BETTERSTACK_URL:oz().optional(),SENTRY_ORG:oz().optional(),SENTRY_PROJECT:oz().optional()},client:{NEXT_PUBLIC_SENTRY_DSN:oz().url().optional()},runtimeEnv:{BETTERSTACK_API_KEY:process.env.BETTERSTACK_API_KEY,BETTERSTACK_URL:process.env.BETTERSTACK_URL,SENTRY_ORG:process.env.SENTRY_ORG,SENTRY_PROJECT:process.env.SENTRY_PROJECT,NEXT_PUBLIC_SENTRY_DSN:process.env.NEXT_PUBLIC_SENTRY_DSN}}).NEXT_PUBLIC_SENTRY_DSN})}},e=>{var t=e(e.s=812);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_instrumentation=t}]);
//# sourceMappingURL=edge-instrumentation.js.map