import{_ as e,l as s,K as n,e as i,L as p}from"./index.js";import{p as g}from"./radar-MK3ICKWK.js";import"./_baseUniq.js";import"./_basePickBy.js";import"./clone.js";var v={parse:e(async r=>{const a=await g("info",r);s.debug(a)},"parse")},d={version:p.version},m=e(()=>d.version,"getVersion"),c={getVersion:m},l=e((r,a,o)=>{s.debug(`rendering info diagram
`+r);const t=n(a);i(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${o}`)},"draw"),f={draw:l},S={parser:v,db:c,renderer:f};export{S as diagram};
//# sourceMappingURL=infoDiagram-PH2N3AL5.js.map
