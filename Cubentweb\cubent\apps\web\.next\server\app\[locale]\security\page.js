(()=>{var e={};e.id=560,e.ids=[560],e.modules={389:()=>{throw Error('Module build failed (from ../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Unexpected token `div`. Expected jsx identifier\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\security\\page.tsx\x1b[0m:10:1]\n \x1b[2m 7\x1b[0m │ \n \x1b[2m 8\x1b[0m │ export default function SecurityPolicy() {\n \x1b[2m 9\x1b[0m │   return (\n \x1b[2m10\x1b[0m │     <div className="min-h-screen bg-gradient-to-br from-orange-950/20 via-black to-orange-900/10 text-white">\n    \xb7 \x1b[35;1m     ───\x1b[0m\n \x1b[2m11\x1b[0m │       <div className="container mx-auto px-6 py-20 max-w-4xl">\n \x1b[2m12\x1b[0m │         <div className="bg-black/40 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-8 md:p-12">\n \x1b[2m13\x1b[0m │           <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent">\n    ╰────\n\n\nCaused by:\n    Syntax Error')},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22338:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>l,tree:()=>c});var s=t(57864),o=t(94327),i=t(73391),n=t.n(i),a=t(17984),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let c={children:["",{children:["[locale]",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,389,23)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\security\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\security\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/security/page",pathname:"/[locale]/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89259:()=>{},91102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a93fb4bd742ae20ac4f2efc682a9dd8b873c54ce":()=>s.z2,"40f08c3d29a2f32c36fa37e5a9ba2c32897f96adb3":()=>o.x,"600ae2011570e2df1d46454ad223098fc4baa55559":()=>s.qr,"60800baff73db42f3be2da01face57da531e4ef986":()=>s.q2,"608b2f8eca36791b674ce9740d60d899091a017080":()=>s.xK});var s=t(22589),o=t(18362)},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,3396,415,2644,365,9752,6270],()=>t(22338));module.exports=s})();