// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// @ts-nocheck
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type Scalars = {
    BSHBEventSchema: ({
  name: string;
  required: boolean;
  placeholder?: string;
  defaultValue?: string;
  helpText?: string
} & {
  id: string;
  label: string
} & ({
  type: "text" | "textarea" | "number" | "date" | "datetime" | "email" | "checkbox" | "hidden"
} | {
  type: "select" | "radio";
  options: string[];
  multiple: boolean
} | {
  type: "file";
  private: boolean
}))[],
    BSHBRichTextContentSchema: RichTextNode[],
    BSHBRichTextTOCSchema: RichTextTocNode[],
    Boolean: boolean,
    CodeSnippetLanguage: B_Language,
    DateTime: any,
    Float: number,
    ID: string,
    Int: number,
    JSON: any,
    String: string,
}

export type AnalyticsKeyScope = 'query' | 'send'

export interface Authors {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AuthorsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AuthorsItem[]
    __typename: 'Authors'
}

export interface AuthorsItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    avatar: BlockImage
    xUrl: (Scalars['String'] | null)
    __typename: 'AuthorsItem'
}

export type AuthorsItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'avatar__ASC' | 'avatar__DESC' | 'xUrl__ASC' | 'xUrl__DESC'

export interface BaseRichTextJson {
    blocks: Scalars['String']
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'BaseRichTextJson'
}

export interface BlockAudio {
    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration: Scalars['Float']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    __typename: 'BlockAudio'
}

export interface BlockCodeSnippet {
    allowedLanguages: Scalars['CodeSnippetLanguage'][]
    code: Scalars['String']
    /** @deprecated Figuring out the correct api. */
    html: Scalars['String']
    language: Scalars['CodeSnippetLanguage']
    __typename: 'BlockCodeSnippet'
}

export interface BlockColor {
    b: Scalars['Int']
    g: Scalars['Int']
    hex: Scalars['String']
    hsl: Scalars['String']
    r: Scalars['Int']
    rgb: Scalars['String']
    __typename: 'BlockColor'
}

export type BlockDocument = (Authors | AuthorsItem | Blog | Categories | CategoriesItem | LegalPages | LegalPagesItem | Posts | PostsItem | _AgentSTART | authorsItem_AsList | categoriesItem_AsList | legalPagesItem_AsList | postsItem_AsList) & { __isUnion?: true }

export interface BlockDocumentSys {
    apiNamePath: Scalars['String']
    createdAt: Scalars['String']
    hash: Scalars['String']
    id: Scalars['ID']
    idPath: Scalars['String']
    lastModifiedAt: Scalars['String']
    slug: Scalars['String']
    slugPath: Scalars['String']
    title: Scalars['String']
    __typename: 'BlockDocumentSys'
}

export interface BlockFile {
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    __typename: 'BlockFile'
}

export interface BlockImage {
    alt: (Scalars['String'] | null)
    aspectRatio: Scalars['String']
    blurDataURL: Scalars['String']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    height: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */
    placeholderURL: Scalars['String']
    /** @deprecated Use `url` instead. */
    rawUrl: Scalars['String']
    thumbhash: Scalars['String']
    /**
     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:
     * 
     * ```graphql
     * {
     *   imageBlock {
     *     url(width: 200, height: 200)
     *   }
     * }
     * ```
     * 
     * This will return the URL with the width and height set to 200 pixels.
     * 
     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).
     * 
     */
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockImage'
}

export type BlockList = (Authors | Categories | LegalPages | Posts | authorsItem_AsList | categoriesItem_AsList | legalPagesItem_AsList | postsItem_AsList) & { __isUnion?: true }

export interface BlockOgImage {
    height: Scalars['Int']
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockOgImage'
}


/** Rich text block */
export type BlockRichText = (Body | Body_1) & { __isUnion?: true }

export interface BlockVideo {
    aspectRatio: Scalars['String']
    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration: Scalars['Float']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    height: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockVideo'
}

export interface Blog {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    authors: Authors
    categories: Categories
    posts: Posts
    __typename: 'Blog'
}

export interface Body {
    html: Scalars['String']
    json: BodyRichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Body'
}

export interface BodyRichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'BodyRichText'
}

export interface Body_1 {
    html: Scalars['String']
    json: Body_1RichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Body_1'
}

export interface Body_1RichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'Body_1RichText'
}

export interface Categories {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CategoriesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CategoriesItem[]
    __typename: 'Categories'
}

export interface CategoriesItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'CategoriesItem'
}

export type CategoriesItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC'

export interface GetUploadSignedURL {
    signedURL: Scalars['String']
    uploadURL: Scalars['String']
    __typename: 'GetUploadSignedURL'
}

export interface LegalPages {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (LegalPagesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: LegalPagesItem[]
    __typename: 'LegalPages'
}

export interface LegalPagesItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    body: Body_1
    description: Scalars['String']
    __typename: 'LegalPagesItem'
}

export type LegalPagesItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'body__ASC' | 'body__DESC' | 'description__ASC' | 'description__DESC'

export interface ListMeta {
    /** Number of items after applying filters but before pagination */
    filteredCount: Scalars['Int']
    /** Total number of items in collection before any filtering/pagination */
    totalCount: Scalars['Int']
    __typename: 'ListMeta'
}

export type MediaBlock = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }

export type MediaBlockUnion = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }

export interface Mutation {
    /**
     * Returns a signed url and an upload url so that you can upload files into your repository.
     * 
     * Example usage with JavaScript:
     * ```js
     * async function handleUpload(file: File) {
     *   const { getUploadSignedURL } = await basehub().mutation({
     *     getUploadSignedURL: {
     *       __args: { fileName: file.name },
     *       signedURL: true,
     *       uploadURL: true,
     *     }
     *   })
     * 
     *   const { signedURL, uploadURL } = getUploadSignedURL
     * 
     *   await fetch(signedURL, { method: 'PUT', body: file })
     * 
     *   // done! do something with the uploadURL now
     * }
     * ```
     * 
     */
    getUploadSignedURL: GetUploadSignedURL
    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */
    transaction: TransactionStatus
    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */
    transactionAsync: Scalars['String']
    transactionStatus: TransactionStatus
    __typename: 'Mutation'
}

export interface Posts {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PostsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PostsItem[]
    __typename: 'Posts'
}

export interface PostsItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    authors: AuthorsItem[]
    body: Body
    categories: (CategoriesItem[] | null)
    /** ISO 8601 date string. */
    date: Scalars['String']
    description: Scalars['String']
    image: BlockImage
    __typename: 'PostsItem'
}

export type PostsItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'authors__ASC' | 'authors__DESC' | 'body__ASC' | 'body__DESC' | 'categories__ASC' | 'categories__DESC' | 'date__ASC' | 'date__DESC' | 'description__ASC' | 'description__DESC' | 'image__ASC' | 'image__DESC'

export interface Query {
    _agent: (_AgentSTART | null)
    /** Query across the custom AI agents in the repository. */
    _agents: _agents
    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */
    _componentInstances: _components
    /** The structure of the repository. Used by START. */
    _structure: Scalars['JSON']
    _sys: RepoSys
    blog: Blog
    legalPages: LegalPages
    __typename: 'Query'
}

export interface RepoSys {
    branches: _Branches
    hash: Scalars['String']
    id: Scalars['ID']
    playgroundInfo: (_PlaygroundInfo | null)
    slug: Scalars['String']
    title: Scalars['String']
    __typename: 'RepoSys'
}

export type RichTextJson = (BaseRichTextJson | BodyRichText | Body_1RichText) & { __isUnion?: true }

export interface TransactionStatus {
    /** Duration in milliseconds. */
    duration: (Scalars['Int'] | null)
    endedAt: (Scalars['String'] | null)
    id: Scalars['String']
    message: (Scalars['String'] | null)
    startedAt: Scalars['String']
    status: TransactionStatusEnum
    __typename: 'TransactionStatus'
}

export type TransactionStatusEnum = 'Cancelled' | 'Completed' | 'Failed' | 'Running' | 'Scheduled'

export interface Variant {
    apiName: Scalars['String']
    color: Scalars['String']
    id: Scalars['String']
    isDefault: Scalars['Boolean']
    label: Scalars['String']
    __typename: 'Variant'
}

export interface _AgentSTART {
    _agentKey: Scalars['String']
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    accent: Scalars['String']
    avatar: Scalars['String']
    chatUrl: Scalars['String']
    commit: Scalars['Boolean']
    description: Scalars['String']
    edit: Scalars['Boolean']
    embedUrl: Scalars['String']
    getUserInfo: Scalars['Boolean']
    grayscale: Scalars['String']
    manageBranches: Scalars['Boolean']
    mcpUrl: Scalars['String']
    model: Scalars['String']
    searchTheWeb: Scalars['Boolean']
    slackInstallUrl: Scalars['String']
    systemPrompt: Scalars['String']
    __typename: '_AgentSTART'
}

export interface _BranchInfo {
    archivedAt: (Scalars['String'] | null)
    archivedBy: (Scalars['String'] | null)
    authorId: (Scalars['String'] | null)
    contributors: (Scalars['String'][] | null)
    createdAt: Scalars['String']
    description: (Scalars['String'] | null)
    git: (_GitInfo | null)
    headCommit: (_CommitInfo | null)
    headCommitId: (Scalars['String'] | null)
    id: Scalars['ID']
    inlineSuggestionAppliedAt: (Scalars['String'] | null)
    isDefault: Scalars['Boolean']
    isInlineSuggestion: (Scalars['Boolean'] | null)
    name: Scalars['String']
    playgroundId: (Scalars['String'] | null)
    rollbackCommitId: (Scalars['String'] | null)
    rollbackIsoDate: (Scalars['String'] | null)
    sourceBranchId: (Scalars['String'] | null)
    updatedAt: (Scalars['String'] | null)
    workingRootBlockId: (Scalars['String'] | null)
    __typename: '_BranchInfo'
}

export interface _Branches {
    _meta: ListMeta
    items: _BranchInfo[]
    __typename: '_Branches'
}

export interface _CommitInfo {
    authorId: Scalars['String']
    branchId: Scalars['String']
    contributors: (Scalars['String'][] | null)
    createdAt: Scalars['String']
    hash: Scalars['String']
    id: Scalars['String']
    mergeParentCommitId: (Scalars['String'] | null)
    message: Scalars['String']
    parentCommitId: (Scalars['String'] | null)
    /** Whether this commit is from a playground branch. */
    playgroundId: (Scalars['String'] | null)
    repoId: Scalars['String']
    rootBlockId: Scalars['String']
    __typename: '_CommitInfo'
}

export interface _GitInfo {
    branch: Scalars['String']
    deploymentUrl: (Scalars['String'] | null)
    __typename: '_GitInfo'
}

export interface _PlaygroundInfo {
    claimUrl: (Scalars['String'] | null)
    editUrl: Scalars['String']
    expiresAt: (Scalars['String'] | null)
    id: (Scalars['String'] | null)
    __typename: '_PlaygroundInfo'
}

export type _ResolveTargetsWithEnum = 'id' | 'objectName'

export type _StructureFormatEnum = 'json' | 'xml'

export interface _agents {
    start: _AgentSTART
    __typename: '_agents'
}

export interface _components {
    authorsItem: authorsItem_AsList
    categoriesItem: categoriesItem_AsList
    legalPagesItem: legalPagesItem_AsList
    postsItem: postsItem_AsList
    __typename: '_components'
}

export interface authorsItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AuthorsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AuthorsItem[]
    __typename: 'authorsItem_AsList'
}

export interface categoriesItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CategoriesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CategoriesItem[]
    __typename: 'categoriesItem_AsList'
}

export interface legalPagesItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (LegalPagesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: LegalPagesItem[]
    __typename: 'legalPagesItem_AsList'
}

export interface postsItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PostsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PostsItem[]
    __typename: 'postsItem_AsList'
}

export interface AuthorsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AuthorsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AuthorsItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface AuthorsItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    avatar?: BlockImageGenqlSelection
    xUrl?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface AuthorsItemFilterInput {AND?: (AuthorsItemFilterInput | null),OR?: (AuthorsItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),xUrl?: (StringFilter | null)}

export interface BaseRichTextJsonGenqlSelection{
    blocks?: boolean | number
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockAudioGenqlSelection{
    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockCodeSnippetGenqlSelection{
    allowedLanguages?: boolean | number
    code?: boolean | number
    /** @deprecated Figuring out the correct api. */
    html?: { __args: {
    /** Theme for the code snippet */
    theme?: (Scalars['String'] | null)} } | boolean | number
    language?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockColorGenqlSelection{
    b?: boolean | number
    g?: boolean | number
    hex?: boolean | number
    hsl?: boolean | number
    r?: boolean | number
    rgb?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockDocumentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    on_Authors?: AuthorsGenqlSelection
    on_AuthorsItem?: AuthorsItemGenqlSelection
    on_Blog?: BlogGenqlSelection
    on_Categories?: CategoriesGenqlSelection
    on_CategoriesItem?: CategoriesItemGenqlSelection
    on_LegalPages?: LegalPagesGenqlSelection
    on_LegalPagesItem?: LegalPagesItemGenqlSelection
    on_Posts?: PostsGenqlSelection
    on_PostsItem?: PostsItemGenqlSelection
    on__AgentSTART?: _AgentSTARTGenqlSelection
    on_authorsItem_AsList?: authorsItem_AsListGenqlSelection
    on_categoriesItem_AsList?: categoriesItem_AsListGenqlSelection
    on_legalPagesItem_AsList?: legalPagesItem_AsListGenqlSelection
    on_postsItem_AsList?: postsItem_AsListGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockDocumentSysGenqlSelection{
    apiNamePath?: boolean | number
    createdAt?: boolean | number
    hash?: boolean | number
    id?: boolean | number
    idPath?: boolean | number
    lastModifiedAt?: boolean | number
    slug?: boolean | number
    slugPath?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockFileGenqlSelection{
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockImageGenqlSelection{
    alt?: boolean | number
    aspectRatio?: boolean | number
    blurDataURL?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    height?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */
    placeholderURL?: boolean | number
    /** @deprecated Use `url` instead. */
    rawUrl?: boolean | number
    thumbhash?: boolean | number
    /**
     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:
     * 
     * ```graphql
     * {
     *   imageBlock {
     *     url(width: 200, height: 200)
     *   }
     * }
     * ```
     * 
     * This will return the URL with the width and height set to 200 pixels.
     * 
     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).
     * 
     */
    url?: { __args: {anim?: (Scalars['String'] | null), background?: (Scalars['String'] | null), blur?: (Scalars['Int'] | null), border?: (Scalars['String'] | null), brightness?: (Scalars['Int'] | null), compression?: (Scalars['String'] | null), contrast?: (Scalars['Int'] | null), dpr?: (Scalars['Int'] | null), fit?: (Scalars['String'] | null), format?: (Scalars['String'] | null), gamma?: (Scalars['String'] | null), gravity?: (Scalars['String'] | null), height?: (Scalars['Int'] | null), metadata?: (Scalars['String'] | null), quality?: (Scalars['Int'] | null), rotate?: (Scalars['String'] | null), sharpen?: (Scalars['String'] | null), trim?: (Scalars['String'] | null), width?: (Scalars['Int'] | null)} } | boolean | number
    width?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    on_Authors?: AuthorsGenqlSelection
    on_Categories?: CategoriesGenqlSelection
    on_LegalPages?: LegalPagesGenqlSelection
    on_Posts?: PostsGenqlSelection
    on_authorsItem_AsList?: authorsItem_AsListGenqlSelection
    on_categoriesItem_AsList?: categoriesItem_AsListGenqlSelection
    on_legalPagesItem_AsList?: legalPagesItem_AsListGenqlSelection
    on_postsItem_AsList?: postsItem_AsListGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockOgImageGenqlSelection{
    height?: boolean | number
    url?: boolean | number
    width?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}


/** Rich text block */
export interface BlockRichTextGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: RichTextJsonGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    on_Body?: BodyGenqlSelection
    on_Body_1?: Body_1GenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlockVideoGenqlSelection{
    aspectRatio?: boolean | number
    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    height?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    width?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BlogGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    authors?: (AuthorsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AuthorsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AuthorsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    categories?: (CategoriesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CategoriesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CategoriesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    posts?: (PostsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PostsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PostsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BodyGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: BodyRichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface BodyRichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface Body_1GenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: Body_1RichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface Body_1RichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface CategoriesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CategoriesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CategoriesItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface CategoriesItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface CategoriesItemFilterInput {AND?: (CategoriesItemFilterInput | null),OR?: (CategoriesItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface DateFilter {eq?: (Scalars['DateTime'] | null),isAfter?: (Scalars['DateTime'] | null),isBefore?: (Scalars['DateTime'] | null),isNull?: (Scalars['Boolean'] | null),neq?: (Scalars['DateTime'] | null),onOrAfter?: (Scalars['DateTime'] | null),onOrBefore?: (Scalars['DateTime'] | null)}

export interface GetUploadSignedURLGenqlSelection{
    signedURL?: boolean | number
    uploadURL?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface LegalPagesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: LegalPagesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: LegalPagesItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface LegalPagesItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    body?: Body_1GenqlSelection
    description?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface LegalPagesItemFilterInput {AND?: (LegalPagesItemFilterInput | null),OR?: (LegalPagesItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null)}

export interface ListFilter {isEmpty?: (Scalars['Boolean'] | null),length?: (Scalars['Int'] | null)}

export interface ListMetaGenqlSelection{
    /** Number of items after applying filters but before pagination */
    filteredCount?: boolean | number
    /** Total number of items in collection before any filtering/pagination */
    totalCount?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface MediaBlockGenqlSelection{
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    on_BlockAudio?: BlockAudioGenqlSelection
    on_BlockFile?: BlockFileGenqlSelection
    on_BlockImage?: BlockImageGenqlSelection
    on_BlockVideo?: BlockVideoGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface MediaBlockUnionGenqlSelection{
    on_BlockAudio?:BlockAudioGenqlSelection,
    on_BlockFile?:BlockFileGenqlSelection,
    on_BlockImage?:BlockImageGenqlSelection,
    on_BlockVideo?:BlockVideoGenqlSelection,
    on_MediaBlock?: MediaBlockGenqlSelection,
    __typename?: boolean | number
}

export interface MutationGenqlSelection{
    /**
     * Returns a signed url and an upload url so that you can upload files into your repository.
     * 
     * Example usage with JavaScript:
     * ```js
     * async function handleUpload(file: File) {
     *   const { getUploadSignedURL } = await basehub().mutation({
     *     getUploadSignedURL: {
     *       __args: { fileName: file.name },
     *       signedURL: true,
     *       uploadURL: true,
     *     }
     *   })
     * 
     *   const { signedURL, uploadURL } = getUploadSignedURL
     * 
     *   await fetch(signedURL, { method: 'PUT', body: file })
     * 
     *   // done! do something with the uploadURL now
     * }
     * ```
     * 
     */
    getUploadSignedURL?: (GetUploadSignedURLGenqlSelection & { __args: {
    /** SHA256 hash of the file. Used for reusing existing files. */
    fileHash?: (Scalars['String'] | null), 
    /** The file name */
    fileName: Scalars['String']} })
    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */
    transaction?: (TransactionStatusGenqlSelection & { __args: {
    /** The ID of the author of the transaction. If not provided, the API Token will be used. */
    authorId?: (Scalars['String'] | null), 
    /** Auto make a commit in your Repo with the specified message. */
    autoCommit?: (Scalars['String'] | null), 
    /** Transaction data. */
    data: Scalars['String'], 
    /** Skip running workflows and event subscribers. Defaults to false. */
    skipWorkflows?: (Scalars['Boolean'] | null), 
    /** Timeout in milliseconds. */
    timeout?: (Scalars['Int'] | null)} })
    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */
    transactionAsync?: { __args: {
    /** The ID of the author of the transaction. If not provided, the API Token will be used. */
    authorId?: (Scalars['String'] | null), 
    /** Auto make a commit in your Repo with the specified message. */
    autoCommit?: (Scalars['String'] | null), 
    /** Transaction data. */
    data: Scalars['String'], 
    /** Skip running workflows and event subscribers. Defaults to false. */
    skipWorkflows?: (Scalars['Boolean'] | null)} }
    transactionStatus?: (TransactionStatusGenqlSelection & { __args: {
    /** Transaction ID */
    id: Scalars['String']} })
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface NumberFilter {eq?: (Scalars['Float'] | null),gt?: (Scalars['Float'] | null),gte?: (Scalars['Float'] | null),isNull?: (Scalars['Boolean'] | null),lt?: (Scalars['Float'] | null),lte?: (Scalars['Float'] | null),neq?: (Scalars['Float'] | null)}

export interface PostsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PostsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PostsItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface PostsItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    authors?: AuthorsItemGenqlSelection
    body?: BodyGenqlSelection
    categories?: CategoriesItemGenqlSelection
    /** ISO 8601 date string. */
    date?: boolean | number
    description?: boolean | number
    image?: BlockImageGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface PostsItemFilterInput {AND?: (PostsItemFilterInput | null),OR?: (PostsItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),authors?: (PostsItemFilterInput__authors_0___untitled | null),categories?: (PostsItemFilterInput__categories_0___untitled | null),date?: (DateFilter | null),description?: (StringFilter | null)}

export interface PostsItemFilterInput__authors_0___untitled {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),xUrl?: (StringFilter | null)}

export interface PostsItemFilterInput__categories_0___untitled {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface QueryGenqlSelection{
    _agent?: (_AgentSTARTGenqlSelection & { __args: {
    /** The ID of the agent. */
    id: Scalars['String']} })
    /** Query across the custom AI agents in the repository. */
    _agents?: _agentsGenqlSelection
    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */
    _componentInstances?: _componentsGenqlSelection
    /** The structure of the repository. Used by START. */
    _structure?: { __args: {
    /** The format of the structure. */
    format?: (_StructureFormatEnum | null), 
    /** The format of the structure. */
    resolveTargetsWith?: (_ResolveTargetsWithEnum | null), 
    /** A target block to forcefully resolve in the schema. */
    targetBlock?: (TargetBlock | null), 
    /** Whether to include constraints in the structure. */
    withConstraints?: (Scalars['Boolean'] | null), 
    /** Whether to include IDs in the structure. */
    withIDs?: (Scalars['Boolean'] | null), 
    /** Whether to include type options in the structure. */
    withTypeOptions?: (Scalars['Boolean'] | null)} } | boolean | number
    _sys?: RepoSysGenqlSelection
    blog?: BlogGenqlSelection
    legalPages?: (LegalPagesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (LegalPagesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (LegalPagesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface RepoSysGenqlSelection{
    branches?: (_BranchesGenqlSelection & { __args?: {limit?: (Scalars['Int'] | null), offset?: (Scalars['Int'] | null)} })
    hash?: boolean | number
    id?: boolean | number
    playgroundInfo?: _PlaygroundInfoGenqlSelection
    slug?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface RichTextJsonGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    on_BaseRichTextJson?: BaseRichTextJsonGenqlSelection
    on_BodyRichText?: BodyRichTextGenqlSelection
    on_Body_1RichText?: Body_1RichTextGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface SelectFilter {excludes?: (Scalars['String'] | null),excludesAll?: (Scalars['String'][] | null),includes?: (Scalars['String'] | null),includesAll?: (Scalars['String'][] | null),includesAny?: (Scalars['String'][] | null),isEmpty?: (Scalars['Boolean'] | null)}

export interface StringFilter {contains?: (Scalars['String'] | null),endsWith?: (Scalars['String'] | null),eq?: (Scalars['String'] | null),in?: (Scalars['String'][] | null),isNull?: (Scalars['Boolean'] | null),matches?: (StringMatchesFilter | null),notEq?: (Scalars['String'] | null),notIn?: (Scalars['String'][] | null),startsWith?: (Scalars['String'] | null)}

export interface StringMatchesFilter {caseSensitive?: (Scalars['Boolean'] | null),pattern: Scalars['String']}

export interface TargetBlock {focus?: (Scalars['Boolean'] | null),id: Scalars['String'],label: Scalars['String']}

export interface TransactionStatusGenqlSelection{
    /** Duration in milliseconds. */
    duration?: boolean | number
    endedAt?: boolean | number
    id?: boolean | number
    message?: boolean | number
    startedAt?: boolean | number
    status?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface VariantGenqlSelection{
    apiName?: boolean | number
    color?: boolean | number
    id?: boolean | number
    isDefault?: boolean | number
    label?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _AgentSTARTGenqlSelection{
    _agentKey?: boolean | number
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    accent?: boolean | number
    avatar?: boolean | number
    chatUrl?: boolean | number
    commit?: boolean | number
    description?: boolean | number
    edit?: boolean | number
    embedUrl?: boolean | number
    getUserInfo?: boolean | number
    grayscale?: boolean | number
    manageBranches?: boolean | number
    mcpUrl?: boolean | number
    model?: boolean | number
    searchTheWeb?: boolean | number
    slackInstallUrl?: boolean | number
    systemPrompt?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _BranchInfoGenqlSelection{
    archivedAt?: boolean | number
    archivedBy?: boolean | number
    authorId?: boolean | number
    contributors?: boolean | number
    createdAt?: boolean | number
    description?: boolean | number
    git?: _GitInfoGenqlSelection
    headCommit?: _CommitInfoGenqlSelection
    headCommitId?: boolean | number
    id?: boolean | number
    inlineSuggestionAppliedAt?: boolean | number
    isDefault?: boolean | number
    isInlineSuggestion?: boolean | number
    name?: boolean | number
    playgroundId?: boolean | number
    rollbackCommitId?: boolean | number
    rollbackIsoDate?: boolean | number
    sourceBranchId?: boolean | number
    updatedAt?: boolean | number
    workingRootBlockId?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _BranchesGenqlSelection{
    _meta?: ListMetaGenqlSelection
    items?: _BranchInfoGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _CommitInfoGenqlSelection{
    authorId?: boolean | number
    branchId?: boolean | number
    contributors?: boolean | number
    createdAt?: boolean | number
    hash?: boolean | number
    id?: boolean | number
    mergeParentCommitId?: boolean | number
    message?: boolean | number
    parentCommitId?: boolean | number
    /** Whether this commit is from a playground branch. */
    playgroundId?: boolean | number
    repoId?: boolean | number
    rootBlockId?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _GitInfoGenqlSelection{
    branch?: boolean | number
    deploymentUrl?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _PlaygroundInfoGenqlSelection{
    claimUrl?: boolean | number
    editUrl?: boolean | number
    expiresAt?: boolean | number
    id?: boolean | number
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _agentsGenqlSelection{
    start?: _AgentSTARTGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface _componentsGenqlSelection{
    authorsItem?: (authorsItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AuthorsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AuthorsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    categoriesItem?: (categoriesItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CategoriesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CategoriesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    legalPagesItem?: (legalPagesItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (LegalPagesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (LegalPagesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    postsItem?: (postsItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PostsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PostsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface authorsItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AuthorsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AuthorsItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface categoriesItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CategoriesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CategoriesItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface legalPagesItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: LegalPagesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: LegalPagesItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

export interface postsItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PostsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PostsItemGenqlSelection
    __typename?: boolean | number
    __scalar?: boolean | number
}

type FragmentsMap = {
  Authors: {
    root: Authors,
    selection: AuthorsGenqlSelection,
}
  AuthorsItem: {
    root: AuthorsItem,
    selection: AuthorsItemGenqlSelection,
}
  BaseRichTextJson: {
    root: BaseRichTextJson,
    selection: BaseRichTextJsonGenqlSelection,
}
  BlockAudio: {
    root: BlockAudio,
    selection: BlockAudioGenqlSelection,
}
  BlockCodeSnippet: {
    root: BlockCodeSnippet,
    selection: BlockCodeSnippetGenqlSelection,
}
  BlockColor: {
    root: BlockColor,
    selection: BlockColorGenqlSelection,
}
  BlockDocument: {
    root: BlockDocument,
    selection: BlockDocumentGenqlSelection,
}
  BlockDocumentSys: {
    root: BlockDocumentSys,
    selection: BlockDocumentSysGenqlSelection,
}
  BlockFile: {
    root: BlockFile,
    selection: BlockFileGenqlSelection,
}
  BlockImage: {
    root: BlockImage,
    selection: BlockImageGenqlSelection,
}
  BlockList: {
    root: BlockList,
    selection: BlockListGenqlSelection,
}
  BlockOgImage: {
    root: BlockOgImage,
    selection: BlockOgImageGenqlSelection,
}
  BlockRichText: {
    root: BlockRichText,
    selection: BlockRichTextGenqlSelection,
}
  BlockVideo: {
    root: BlockVideo,
    selection: BlockVideoGenqlSelection,
}
  Blog: {
    root: Blog,
    selection: BlogGenqlSelection,
}
  Body: {
    root: Body,
    selection: BodyGenqlSelection,
}
  BodyRichText: {
    root: BodyRichText,
    selection: BodyRichTextGenqlSelection,
}
  Body_1: {
    root: Body_1,
    selection: Body_1GenqlSelection,
}
  Body_1RichText: {
    root: Body_1RichText,
    selection: Body_1RichTextGenqlSelection,
}
  Categories: {
    root: Categories,
    selection: CategoriesGenqlSelection,
}
  CategoriesItem: {
    root: CategoriesItem,
    selection: CategoriesItemGenqlSelection,
}
  GetUploadSignedURL: {
    root: GetUploadSignedURL,
    selection: GetUploadSignedURLGenqlSelection,
}
  LegalPages: {
    root: LegalPages,
    selection: LegalPagesGenqlSelection,
}
  LegalPagesItem: {
    root: LegalPagesItem,
    selection: LegalPagesItemGenqlSelection,
}
  ListMeta: {
    root: ListMeta,
    selection: ListMetaGenqlSelection,
}
  MediaBlock: {
    root: MediaBlock,
    selection: MediaBlockGenqlSelection,
}
  Mutation: {
    root: Mutation,
    selection: MutationGenqlSelection,
}
  Posts: {
    root: Posts,
    selection: PostsGenqlSelection,
}
  PostsItem: {
    root: PostsItem,
    selection: PostsItemGenqlSelection,
}
  Query: {
    root: Query,
    selection: QueryGenqlSelection,
}
  RepoSys: {
    root: RepoSys,
    selection: RepoSysGenqlSelection,
}
  RichTextJson: {
    root: RichTextJson,
    selection: RichTextJsonGenqlSelection,
}
  TransactionStatus: {
    root: TransactionStatus,
    selection: TransactionStatusGenqlSelection,
}
  Variant: {
    root: Variant,
    selection: VariantGenqlSelection,
}
  _AgentSTART: {
    root: _AgentSTART,
    selection: _AgentSTARTGenqlSelection,
}
  _BranchInfo: {
    root: _BranchInfo,
    selection: _BranchInfoGenqlSelection,
}
  _Branches: {
    root: _Branches,
    selection: _BranchesGenqlSelection,
}
  _CommitInfo: {
    root: _CommitInfo,
    selection: _CommitInfoGenqlSelection,
}
  _GitInfo: {
    root: _GitInfo,
    selection: _GitInfoGenqlSelection,
}
  _PlaygroundInfo: {
    root: _PlaygroundInfo,
    selection: _PlaygroundInfoGenqlSelection,
}
  _agents: {
    root: _agents,
    selection: _agentsGenqlSelection,
}
  _components: {
    root: _components,
    selection: _componentsGenqlSelection,
}
  authorsItem_AsList: {
    root: authorsItem_AsList,
    selection: authorsItem_AsListGenqlSelection,
}
  categoriesItem_AsList: {
    root: categoriesItem_AsList,
    selection: categoriesItem_AsListGenqlSelection,
}
  legalPagesItem_AsList: {
    root: legalPagesItem_AsList,
    selection: legalPagesItem_AsListGenqlSelection,
}
  postsItem_AsList: {
    root: postsItem_AsList,
    selection: postsItem_AsListGenqlSelection,
}
}

import { FieldsSelection } from "./runtime";

export function fragmentOn<
    TypeName extends keyof FragmentsMap,
    Selection extends FragmentsMap[TypeName]["selection"],
>(name: TypeName, fields: Selection) {
  return { __fragmentOn: name, ...fields } as const;
}

// credits: https://stackoverflow.com/a/54487392
type OmitDistributive<T, K extends PropertyKey> = T extends any
    ? T extends object
        ? Id<OmitRecursively<T, K>>
        : T
    : never
type Id<T> = {} & { [P in keyof T]: T[P] } // Cosmetic use only makes the tooltips expad the type can be removed
type OmitRecursively<T, K extends PropertyKey> = Omit<
    { [P in keyof T]: OmitDistributive<T[P], K> },
    K
>

export namespace fragmentOn {
    export type infer<T> = T extends {
      __fragmentOn: infer U extends keyof FragmentsMap;
    }
      ? OmitRecursively<FieldsSelection<FragmentsMap[U]["root"], Omit<T, "__fragmentOn">>, "__fragmentOn">
      : never;
  }


// This is a BaseHub-specific thing:

type RecursiveCollection<T, Key extends keyof T> = T & {
[key in Key]: { items: RecursiveCollection<T, Key> };
};

export function fragmentOnRecursiveCollection<
  TypeName extends keyof FragmentsMap,
  Selection extends FragmentsMap[TypeName]["selection"],
  RecursiveKey extends keyof FragmentsMap[TypeName]["selection"]
>(
  name: TypeName,
  fields: Selection,
  options: {
    recursiveKey: RecursiveKey;
    levels: number;
    getLevelArgs?: (level: number) => unknown;
  }
) {
  let current = {
    ...fields,
  } as RecursiveCollection<
    { readonly __fragmentOn: TypeName } & Selection,
    RecursiveKey
  >;
  if (options.levels > 0) {
    current[options.recursiveKey] = {
      ...(options.getLevelArgs
        ? { __args: options.getLevelArgs(options.levels) }
        : {}),
      items: fragmentOnRecursiveCollection(name, fields, {
        ...options,
        levels: options.levels - 1,
      }),
    } as any;
  }
  return current;
}




    const Authors_possibleTypes: string[] = ['Authors']
    export const isAuthors = (obj?: { __typename?: any } | null): obj is Authors => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isAuthors"')
      return Authors_possibleTypes.includes(obj.__typename)
    }
    


    const AuthorsItem_possibleTypes: string[] = ['AuthorsItem']
    export const isAuthorsItem = (obj?: { __typename?: any } | null): obj is AuthorsItem => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isAuthorsItem"')
      return AuthorsItem_possibleTypes.includes(obj.__typename)
    }
    


    const BaseRichTextJson_possibleTypes: string[] = ['BaseRichTextJson']
    export const isBaseRichTextJson = (obj?: { __typename?: any } | null): obj is BaseRichTextJson => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBaseRichTextJson"')
      return BaseRichTextJson_possibleTypes.includes(obj.__typename)
    }
    


    const BlockAudio_possibleTypes: string[] = ['BlockAudio']
    export const isBlockAudio = (obj?: { __typename?: any } | null): obj is BlockAudio => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockAudio"')
      return BlockAudio_possibleTypes.includes(obj.__typename)
    }
    


    const BlockCodeSnippet_possibleTypes: string[] = ['BlockCodeSnippet']
    export const isBlockCodeSnippet = (obj?: { __typename?: any } | null): obj is BlockCodeSnippet => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockCodeSnippet"')
      return BlockCodeSnippet_possibleTypes.includes(obj.__typename)
    }
    


    const BlockColor_possibleTypes: string[] = ['BlockColor']
    export const isBlockColor = (obj?: { __typename?: any } | null): obj is BlockColor => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockColor"')
      return BlockColor_possibleTypes.includes(obj.__typename)
    }
    


    const BlockDocument_possibleTypes: string[] = ['Authors','AuthorsItem','Blog','Categories','CategoriesItem','LegalPages','LegalPagesItem','Posts','PostsItem','_AgentSTART','authorsItem_AsList','categoriesItem_AsList','legalPagesItem_AsList','postsItem_AsList']
    export const isBlockDocument = (obj?: { __typename?: any } | null): obj is BlockDocument => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockDocument"')
      return BlockDocument_possibleTypes.includes(obj.__typename)
    }
    


    const BlockDocumentSys_possibleTypes: string[] = ['BlockDocumentSys']
    export const isBlockDocumentSys = (obj?: { __typename?: any } | null): obj is BlockDocumentSys => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockDocumentSys"')
      return BlockDocumentSys_possibleTypes.includes(obj.__typename)
    }
    


    const BlockFile_possibleTypes: string[] = ['BlockFile']
    export const isBlockFile = (obj?: { __typename?: any } | null): obj is BlockFile => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockFile"')
      return BlockFile_possibleTypes.includes(obj.__typename)
    }
    


    const BlockImage_possibleTypes: string[] = ['BlockImage']
    export const isBlockImage = (obj?: { __typename?: any } | null): obj is BlockImage => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockImage"')
      return BlockImage_possibleTypes.includes(obj.__typename)
    }
    


    const BlockList_possibleTypes: string[] = ['Authors','Categories','LegalPages','Posts','authorsItem_AsList','categoriesItem_AsList','legalPagesItem_AsList','postsItem_AsList']
    export const isBlockList = (obj?: { __typename?: any } | null): obj is BlockList => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockList"')
      return BlockList_possibleTypes.includes(obj.__typename)
    }
    


    const BlockOgImage_possibleTypes: string[] = ['BlockOgImage']
    export const isBlockOgImage = (obj?: { __typename?: any } | null): obj is BlockOgImage => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockOgImage"')
      return BlockOgImage_possibleTypes.includes(obj.__typename)
    }
    


    const BlockRichText_possibleTypes: string[] = ['Body','Body_1']
    export const isBlockRichText = (obj?: { __typename?: any } | null): obj is BlockRichText => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockRichText"')
      return BlockRichText_possibleTypes.includes(obj.__typename)
    }
    


    const BlockVideo_possibleTypes: string[] = ['BlockVideo']
    export const isBlockVideo = (obj?: { __typename?: any } | null): obj is BlockVideo => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlockVideo"')
      return BlockVideo_possibleTypes.includes(obj.__typename)
    }
    


    const Blog_possibleTypes: string[] = ['Blog']
    export const isBlog = (obj?: { __typename?: any } | null): obj is Blog => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBlog"')
      return Blog_possibleTypes.includes(obj.__typename)
    }
    


    const Body_possibleTypes: string[] = ['Body']
    export const isBody = (obj?: { __typename?: any } | null): obj is Body => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBody"')
      return Body_possibleTypes.includes(obj.__typename)
    }
    


    const BodyRichText_possibleTypes: string[] = ['BodyRichText']
    export const isBodyRichText = (obj?: { __typename?: any } | null): obj is BodyRichText => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBodyRichText"')
      return BodyRichText_possibleTypes.includes(obj.__typename)
    }
    


    const Body_1_possibleTypes: string[] = ['Body_1']
    export const isBody_1 = (obj?: { __typename?: any } | null): obj is Body_1 => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBody_1"')
      return Body_1_possibleTypes.includes(obj.__typename)
    }
    


    const Body_1RichText_possibleTypes: string[] = ['Body_1RichText']
    export const isBody_1RichText = (obj?: { __typename?: any } | null): obj is Body_1RichText => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isBody_1RichText"')
      return Body_1RichText_possibleTypes.includes(obj.__typename)
    }
    


    const Categories_possibleTypes: string[] = ['Categories']
    export const isCategories = (obj?: { __typename?: any } | null): obj is Categories => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isCategories"')
      return Categories_possibleTypes.includes(obj.__typename)
    }
    


    const CategoriesItem_possibleTypes: string[] = ['CategoriesItem']
    export const isCategoriesItem = (obj?: { __typename?: any } | null): obj is CategoriesItem => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isCategoriesItem"')
      return CategoriesItem_possibleTypes.includes(obj.__typename)
    }
    


    const GetUploadSignedURL_possibleTypes: string[] = ['GetUploadSignedURL']
    export const isGetUploadSignedURL = (obj?: { __typename?: any } | null): obj is GetUploadSignedURL => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isGetUploadSignedURL"')
      return GetUploadSignedURL_possibleTypes.includes(obj.__typename)
    }
    


    const LegalPages_possibleTypes: string[] = ['LegalPages']
    export const isLegalPages = (obj?: { __typename?: any } | null): obj is LegalPages => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isLegalPages"')
      return LegalPages_possibleTypes.includes(obj.__typename)
    }
    


    const LegalPagesItem_possibleTypes: string[] = ['LegalPagesItem']
    export const isLegalPagesItem = (obj?: { __typename?: any } | null): obj is LegalPagesItem => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isLegalPagesItem"')
      return LegalPagesItem_possibleTypes.includes(obj.__typename)
    }
    


    const ListMeta_possibleTypes: string[] = ['ListMeta']
    export const isListMeta = (obj?: { __typename?: any } | null): obj is ListMeta => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isListMeta"')
      return ListMeta_possibleTypes.includes(obj.__typename)
    }
    


    const MediaBlock_possibleTypes: string[] = ['BlockAudio','BlockFile','BlockImage','BlockVideo']
    export const isMediaBlock = (obj?: { __typename?: any } | null): obj is MediaBlock => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isMediaBlock"')
      return MediaBlock_possibleTypes.includes(obj.__typename)
    }
    


    const MediaBlockUnion_possibleTypes: string[] = ['BlockAudio','BlockFile','BlockImage','BlockVideo']
    export const isMediaBlockUnion = (obj?: { __typename?: any } | null): obj is MediaBlockUnion => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isMediaBlockUnion"')
      return MediaBlockUnion_possibleTypes.includes(obj.__typename)
    }
    


    const Mutation_possibleTypes: string[] = ['Mutation']
    export const isMutation = (obj?: { __typename?: any } | null): obj is Mutation => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isMutation"')
      return Mutation_possibleTypes.includes(obj.__typename)
    }
    


    const Posts_possibleTypes: string[] = ['Posts']
    export const isPosts = (obj?: { __typename?: any } | null): obj is Posts => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isPosts"')
      return Posts_possibleTypes.includes(obj.__typename)
    }
    


    const PostsItem_possibleTypes: string[] = ['PostsItem']
    export const isPostsItem = (obj?: { __typename?: any } | null): obj is PostsItem => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isPostsItem"')
      return PostsItem_possibleTypes.includes(obj.__typename)
    }
    


    const Query_possibleTypes: string[] = ['Query']
    export const isQuery = (obj?: { __typename?: any } | null): obj is Query => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isQuery"')
      return Query_possibleTypes.includes(obj.__typename)
    }
    


    const RepoSys_possibleTypes: string[] = ['RepoSys']
    export const isRepoSys = (obj?: { __typename?: any } | null): obj is RepoSys => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isRepoSys"')
      return RepoSys_possibleTypes.includes(obj.__typename)
    }
    


    const RichTextJson_possibleTypes: string[] = ['BaseRichTextJson','BodyRichText','Body_1RichText']
    export const isRichTextJson = (obj?: { __typename?: any } | null): obj is RichTextJson => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isRichTextJson"')
      return RichTextJson_possibleTypes.includes(obj.__typename)
    }
    


    const TransactionStatus_possibleTypes: string[] = ['TransactionStatus']
    export const isTransactionStatus = (obj?: { __typename?: any } | null): obj is TransactionStatus => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isTransactionStatus"')
      return TransactionStatus_possibleTypes.includes(obj.__typename)
    }
    


    const Variant_possibleTypes: string[] = ['Variant']
    export const isVariant = (obj?: { __typename?: any } | null): obj is Variant => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isVariant"')
      return Variant_possibleTypes.includes(obj.__typename)
    }
    


    const _AgentSTART_possibleTypes: string[] = ['_AgentSTART']
    export const is_AgentSTART = (obj?: { __typename?: any } | null): obj is _AgentSTART => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_AgentSTART"')
      return _AgentSTART_possibleTypes.includes(obj.__typename)
    }
    


    const _BranchInfo_possibleTypes: string[] = ['_BranchInfo']
    export const is_BranchInfo = (obj?: { __typename?: any } | null): obj is _BranchInfo => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_BranchInfo"')
      return _BranchInfo_possibleTypes.includes(obj.__typename)
    }
    


    const _Branches_possibleTypes: string[] = ['_Branches']
    export const is_Branches = (obj?: { __typename?: any } | null): obj is _Branches => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_Branches"')
      return _Branches_possibleTypes.includes(obj.__typename)
    }
    


    const _CommitInfo_possibleTypes: string[] = ['_CommitInfo']
    export const is_CommitInfo = (obj?: { __typename?: any } | null): obj is _CommitInfo => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_CommitInfo"')
      return _CommitInfo_possibleTypes.includes(obj.__typename)
    }
    


    const _GitInfo_possibleTypes: string[] = ['_GitInfo']
    export const is_GitInfo = (obj?: { __typename?: any } | null): obj is _GitInfo => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_GitInfo"')
      return _GitInfo_possibleTypes.includes(obj.__typename)
    }
    


    const _PlaygroundInfo_possibleTypes: string[] = ['_PlaygroundInfo']
    export const is_PlaygroundInfo = (obj?: { __typename?: any } | null): obj is _PlaygroundInfo => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_PlaygroundInfo"')
      return _PlaygroundInfo_possibleTypes.includes(obj.__typename)
    }
    


    const _agents_possibleTypes: string[] = ['_agents']
    export const is_agents = (obj?: { __typename?: any } | null): obj is _agents => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_agents"')
      return _agents_possibleTypes.includes(obj.__typename)
    }
    


    const _components_possibleTypes: string[] = ['_components']
    export const is_components = (obj?: { __typename?: any } | null): obj is _components => {
      if (!obj?.__typename) throw new Error('__typename is missing in "is_components"')
      return _components_possibleTypes.includes(obj.__typename)
    }
    


    const authorsItem_AsList_possibleTypes: string[] = ['authorsItem_AsList']
    export const isauthorsItem_AsList = (obj?: { __typename?: any } | null): obj is authorsItem_AsList => {
      if (!obj?.__typename) throw new Error('__typename is missing in "isauthorsItem_AsList"')
      return authorsItem_AsList_possibleTypes.includes(obj.__typename)
    }
    


    const categoriesItem_AsList_possibleTypes: string[] = ['categoriesItem_AsList']
    export const iscategoriesItem_AsList = (obj?: { __typename?: any } | null): obj is categoriesItem_AsList => {
      if (!obj?.__typename) throw new Error('__typename is missing in "iscategoriesItem_AsList"')
      return categoriesItem_AsList_possibleTypes.includes(obj.__typename)
    }
    


    const legalPagesItem_AsList_possibleTypes: string[] = ['legalPagesItem_AsList']
    export const islegalPagesItem_AsList = (obj?: { __typename?: any } | null): obj is legalPagesItem_AsList => {
      if (!obj?.__typename) throw new Error('__typename is missing in "islegalPagesItem_AsList"')
      return legalPagesItem_AsList_possibleTypes.includes(obj.__typename)
    }
    


    const postsItem_AsList_possibleTypes: string[] = ['postsItem_AsList']
    export const ispostsItem_AsList = (obj?: { __typename?: any } | null): obj is postsItem_AsList => {
      if (!obj?.__typename) throw new Error('__typename is missing in "ispostsItem_AsList"')
      return postsItem_AsList_possibleTypes.includes(obj.__typename)
    }
    

export const enumAnalyticsKeyScope = {
   query: 'query' as const,
   send: 'send' as const
}

export const enumAuthorsItemOrderByEnum = {
   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,
   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,
   _sys_hash__ASC: '_sys_hash__ASC' as const,
   _sys_hash__DESC: '_sys_hash__DESC' as const,
   _sys_id__ASC: '_sys_id__ASC' as const,
   _sys_id__DESC: '_sys_id__DESC' as const,
   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,
   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,
   _sys_slug__ASC: '_sys_slug__ASC' as const,
   _sys_slug__DESC: '_sys_slug__DESC' as const,
   _sys_title__ASC: '_sys_title__ASC' as const,
   _sys_title__DESC: '_sys_title__DESC' as const,
   avatar__ASC: 'avatar__ASC' as const,
   avatar__DESC: 'avatar__DESC' as const,
   xUrl__ASC: 'xUrl__ASC' as const,
   xUrl__DESC: 'xUrl__DESC' as const
}

export const enumCategoriesItemOrderByEnum = {
   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,
   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,
   _sys_hash__ASC: '_sys_hash__ASC' as const,
   _sys_hash__DESC: '_sys_hash__DESC' as const,
   _sys_id__ASC: '_sys_id__ASC' as const,
   _sys_id__DESC: '_sys_id__DESC' as const,
   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,
   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,
   _sys_slug__ASC: '_sys_slug__ASC' as const,
   _sys_slug__DESC: '_sys_slug__DESC' as const,
   _sys_title__ASC: '_sys_title__ASC' as const,
   _sys_title__DESC: '_sys_title__DESC' as const
}

export const enumLegalPagesItemOrderByEnum = {
   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,
   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,
   _sys_hash__ASC: '_sys_hash__ASC' as const,
   _sys_hash__DESC: '_sys_hash__DESC' as const,
   _sys_id__ASC: '_sys_id__ASC' as const,
   _sys_id__DESC: '_sys_id__DESC' as const,
   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,
   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,
   _sys_slug__ASC: '_sys_slug__ASC' as const,
   _sys_slug__DESC: '_sys_slug__DESC' as const,
   _sys_title__ASC: '_sys_title__ASC' as const,
   _sys_title__DESC: '_sys_title__DESC' as const,
   body__ASC: 'body__ASC' as const,
   body__DESC: 'body__DESC' as const,
   description__ASC: 'description__ASC' as const,
   description__DESC: 'description__DESC' as const
}

export const enumPostsItemOrderByEnum = {
   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,
   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,
   _sys_hash__ASC: '_sys_hash__ASC' as const,
   _sys_hash__DESC: '_sys_hash__DESC' as const,
   _sys_id__ASC: '_sys_id__ASC' as const,
   _sys_id__DESC: '_sys_id__DESC' as const,
   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,
   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,
   _sys_slug__ASC: '_sys_slug__ASC' as const,
   _sys_slug__DESC: '_sys_slug__DESC' as const,
   _sys_title__ASC: '_sys_title__ASC' as const,
   _sys_title__DESC: '_sys_title__DESC' as const,
   authors__ASC: 'authors__ASC' as const,
   authors__DESC: 'authors__DESC' as const,
   body__ASC: 'body__ASC' as const,
   body__DESC: 'body__DESC' as const,
   categories__ASC: 'categories__ASC' as const,
   categories__DESC: 'categories__DESC' as const,
   date__ASC: 'date__ASC' as const,
   date__DESC: 'date__DESC' as const,
   description__ASC: 'description__ASC' as const,
   description__DESC: 'description__DESC' as const,
   image__ASC: 'image__ASC' as const,
   image__DESC: 'image__DESC' as const
}

export const enumTransactionStatusEnum = {
   Cancelled: 'Cancelled' as const,
   Completed: 'Completed' as const,
   Failed: 'Failed' as const,
   Running: 'Running' as const,
   Scheduled: 'Scheduled' as const
}

export const enum_resolveTargetsWithEnum = {
   id: 'id' as const,
   objectName: 'objectName' as const
}

export const enum_structureFormatEnum = {
   json: 'json' as const,
   xml: 'xml' as const
}

import type { RichTextNode, RichTextTocNode } from './api-transaction';
import type { Language as B_Language } from './react-code-block';
