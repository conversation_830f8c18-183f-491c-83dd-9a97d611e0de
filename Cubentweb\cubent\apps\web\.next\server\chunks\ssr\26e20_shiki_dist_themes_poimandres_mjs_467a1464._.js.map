{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/poimandres.mjs"], "sourcesContent": ["var poimandres = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#a6accd\",\n    \"activityBar.background\": \"#1b1e28\",\n    \"activityBar.dropBorder\": \"#a6accd\",\n    \"activityBar.foreground\": \"#a6accd\",\n    \"activityBar.inactiveForeground\": \"#a6accd66\",\n    \"activityBarBadge.background\": \"#303340\",\n    \"activityBarBadge.foreground\": \"#e4f0fb\",\n    \"badge.background\": \"#303340\",\n    \"badge.foreground\": \"#e4f0fb\",\n    \"breadcrumb.activeSelectionForeground\": \"#e4f0fb\",\n    \"breadcrumb.background\": \"#00000000\",\n    \"breadcrumb.focusForeground\": \"#e4f0fb\",\n    \"breadcrumb.foreground\": \"#767c9dcc\",\n    \"breadcrumbPicker.background\": \"#1b1e28\",\n    \"button.background\": \"#303340\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#50647750\",\n    \"button.secondaryBackground\": \"#a6accd\",\n    \"button.secondaryForeground\": \"#ffffff\",\n    \"button.secondaryHoverBackground\": \"#a6accd\",\n    \"charts.blue\": \"#ADD7FF\",\n    \"charts.foreground\": \"#a6accd\",\n    \"charts.green\": \"#5DE4c7\",\n    \"charts.lines\": \"#a6accd80\",\n    \"charts.orange\": \"#89ddff\",\n    \"charts.purple\": \"#f087bd\",\n    \"charts.red\": \"#d0679d\",\n    \"charts.yellow\": \"#fffac2\",\n    \"checkbox.background\": \"#1b1e28\",\n    \"checkbox.border\": \"#ffffff10\",\n    \"checkbox.foreground\": \"#e4f0fb\",\n    \"debugConsole.errorForeground\": \"#d0679d\",\n    \"debugConsole.infoForeground\": \"#ADD7FF\",\n    \"debugConsole.sourceForeground\": \"#a6accd\",\n    \"debugConsole.warningForeground\": \"#fffac2\",\n    \"debugConsoleInputIcon.foreground\": \"#a6accd\",\n    \"debugExceptionWidget.background\": \"#d0679d\",\n    \"debugExceptionWidget.border\": \"#d0679d\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#fffac2\",\n    \"debugIcon.breakpointDisabledForeground\": \"#7390AA\",\n    \"debugIcon.breakpointForeground\": \"#d0679d\",\n    \"debugIcon.breakpointStackframeForeground\": \"#5fb3a1\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#7390AA\",\n    \"debugIcon.continueForeground\": \"#ADD7FF\",\n    \"debugIcon.disconnectForeground\": \"#d0679d\",\n    \"debugIcon.pauseForeground\": \"#ADD7FF\",\n    \"debugIcon.restartForeground\": \"#5fb3a1\",\n    \"debugIcon.startForeground\": \"#5fb3a1\",\n    \"debugIcon.stepBackForeground\": \"#ADD7FF\",\n    \"debugIcon.stepIntoForeground\": \"#ADD7FF\",\n    \"debugIcon.stepOutForeground\": \"#ADD7FF\",\n    \"debugIcon.stepOverForeground\": \"#ADD7FF\",\n    \"debugIcon.stopForeground\": \"#d0679d\",\n    \"debugTokenExpression.boolean\": \"#89ddff\",\n    \"debugTokenExpression.error\": \"#d0679d\",\n    \"debugTokenExpression.name\": \"#e4f0fb\",\n    \"debugTokenExpression.number\": \"#5fb3a1\",\n    \"debugTokenExpression.string\": \"#89ddff\",\n    \"debugTokenExpression.value\": \"#a6accd99\",\n    \"debugToolBar.background\": \"#303340\",\n    \"debugView.exceptionLabelBackground\": \"#d0679d\",\n    \"debugView.exceptionLabelForeground\": \"#e4f0fb\",\n    \"debugView.stateLabelBackground\": \"#303340\",\n    \"debugView.stateLabelForeground\": \"#a6accd\",\n    \"debugView.valueChangedHighlight\": \"#89ddff\",\n    \"descriptionForeground\": \"#a6accdb3\",\n    \"diffEditor.diagonalFill\": \"#a6accd33\",\n    \"diffEditor.insertedTextBackground\": \"#50647715\",\n    \"diffEditor.removedTextBackground\": \"#d0679d20\",\n    \"dropdown.background\": \"#1b1e28\",\n    \"dropdown.border\": \"#ffffff10\",\n    \"dropdown.foreground\": \"#e4f0fb\",\n    \"editor.background\": \"#1b1e28\",\n    \"editor.findMatchBackground\": \"#ADD7FF40\",\n    \"editor.findMatchBorder\": \"#ADD7FF\",\n    \"editor.findMatchHighlightBackground\": \"#ADD7FF40\",\n    \"editor.findRangeHighlightBackground\": \"#ADD7FF40\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#7abd7a4d\",\n    \"editor.foldBackground\": \"#717cb40b\",\n    \"editor.foreground\": \"#a6accd\",\n    \"editor.hoverHighlightBackground\": \"#264f7840\",\n    \"editor.inactiveSelectionBackground\": \"#717cb425\",\n    \"editor.lineHighlightBackground\": \"#717cb425\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.linkedEditingBackground\": \"#d0679d4d\",\n    \"editor.rangeHighlightBackground\": \"#ffffff0b\",\n    \"editor.selectionBackground\": \"#717cb425\",\n    \"editor.selectionHighlightBackground\": \"#00000000\",\n    \"editor.selectionHighlightBorder\": \"#ADD7FF80\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#525252\",\n    \"editor.snippetTabstopHighlightBackground\": \"#7c7c7c4d\",\n    \"editor.stackFrameHighlightBackground\": \"#ffff0033\",\n    \"editor.symbolHighlightBackground\": \"#89ddff60\",\n    \"editor.wordHighlightBackground\": \"#ADD7FF20\",\n    \"editor.wordHighlightStrongBackground\": \"#ADD7FF40\",\n    \"editorBracketMatch.background\": \"#00000000\",\n    \"editorBracketMatch.border\": \"#e4f0fb40\",\n    \"editorCodeLens.foreground\": \"#a6accd\",\n    \"editorCursor.foreground\": \"#a6accd\",\n    \"editorError.foreground\": \"#d0679d\",\n    \"editorGroup.border\": \"#00000030\",\n    \"editorGroup.dropBackground\": \"#7390AA80\",\n    \"editorGroupHeader.noTabsBackground\": \"#1b1e28\",\n    \"editorGroupHeader.tabsBackground\": \"#1b1e28\",\n    \"editorGutter.addedBackground\": \"#5fb3a140\",\n    \"editorGutter.background\": \"#1b1e28\",\n    \"editorGutter.commentRangeForeground\": \"#a6accd\",\n    \"editorGutter.deletedBackground\": \"#d0679d40\",\n    \"editorGutter.foldingControlForeground\": \"#a6accd\",\n    \"editorGutter.modifiedBackground\": \"#ADD7FF20\",\n    \"editorHint.foreground\": \"#7390AAb3\",\n    \"editorHoverWidget.background\": \"#1b1e28\",\n    \"editorHoverWidget.border\": \"#ffffff10\",\n    \"editorHoverWidget.foreground\": \"#a6accd\",\n    \"editorHoverWidget.statusBarBackground\": \"#202430\",\n    \"editorIndentGuide.activeBackground\": \"#e3e4e229\",\n    \"editorIndentGuide.background\": \"#303340\",\n    \"editorInfo.foreground\": \"#ADD7FF\",\n    \"editorInlineHint.background\": \"#a6accd\",\n    \"editorInlineHint.foreground\": \"#1b1e28\",\n    \"editorLightBulb.foreground\": \"#fffac2\",\n    \"editorLightBulbAutoFix.foreground\": \"#ADD7FF\",\n    \"editorLineNumber.activeForeground\": \"#a6accd\",\n    \"editorLineNumber.foreground\": \"#767c9d50\",\n    \"editorLink.activeForeground\": \"#ADD7FF\",\n    \"editorMarkerNavigation.background\": \"#2d2d30\",\n    \"editorMarkerNavigationError.background\": \"#d0679d\",\n    \"editorMarkerNavigationInfo.background\": \"#ADD7FF\",\n    \"editorMarkerNavigationWarning.background\": \"#fffac2\",\n    \"editorOverviewRuler.addedForeground\": \"#5fb3a199\",\n    \"editorOverviewRuler.border\": \"#00000000\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#a0a0a0\",\n    \"editorOverviewRuler.commonContentForeground\": \"#a6accd66\",\n    \"editorOverviewRuler.currentContentForeground\": \"#5fb3a180\",\n    \"editorOverviewRuler.deletedForeground\": \"#d0679d99\",\n    \"editorOverviewRuler.errorForeground\": \"#d0679db3\",\n    \"editorOverviewRuler.findMatchForeground\": \"#e4f0fb20\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#89ddff80\",\n    \"editorOverviewRuler.infoForeground\": \"#ADD7FF\",\n    \"editorOverviewRuler.modifiedForeground\": \"#89ddff99\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#89ddff99\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#a0a0a0cc\",\n    \"editorOverviewRuler.warningForeground\": \"#fffac2\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#a0a0a0cc\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#89ddffcc\",\n    \"editorPane.background\": \"#1b1e28\",\n    \"editorRuler.foreground\": \"#e4f0fb10\",\n    \"editorSuggestWidget.background\": \"#1b1e28\",\n    \"editorSuggestWidget.border\": \"#ffffff10\",\n    \"editorSuggestWidget.foreground\": \"#a6accd\",\n    \"editorSuggestWidget.highlightForeground\": \"#5DE4c7\",\n    \"editorSuggestWidget.selectedBackground\": \"#00000050\",\n    \"editorUnnecessaryCode.opacity\": \"#000000aa\",\n    \"editorWarning.foreground\": \"#fffac2\",\n    \"editorWhitespace.foreground\": \"#303340\",\n    \"editorWidget.background\": \"#1b1e28\",\n    \"editorWidget.border\": \"#a6accd\",\n    \"editorWidget.foreground\": \"#a6accd\",\n    \"errorForeground\": \"#d0679d\",\n    \"extensionBadge.remoteBackground\": \"#303340\",\n    \"extensionBadge.remoteForeground\": \"#e4f0fb\",\n    \"extensionButton.prominentBackground\": \"#30334090\",\n    \"extensionButton.prominentForeground\": \"#ffffff\",\n    \"extensionButton.prominentHoverBackground\": \"#303340\",\n    \"extensionIcon.starForeground\": \"#fffac2\",\n    \"focusBorder\": \"#00000000\",\n    \"foreground\": \"#a6accd\",\n    \"gitDecoration.addedResourceForeground\": \"#5fb3a1\",\n    \"gitDecoration.conflictingResourceForeground\": \"#d0679d\",\n    \"gitDecoration.deletedResourceForeground\": \"#d0679d\",\n    \"gitDecoration.ignoredResourceForeground\": \"#767c9d70\",\n    \"gitDecoration.modifiedResourceForeground\": \"#ADD7FF\",\n    \"gitDecoration.renamedResourceForeground\": \"#5DE4c7\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#d0679d\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#ADD7FF\",\n    \"gitDecoration.submoduleResourceForeground\": \"#89ddff\",\n    \"gitDecoration.untrackedResourceForeground\": \"#5DE4c7\",\n    \"icon.foreground\": \"#a6accd\",\n    \"imagePreview.border\": \"#303340\",\n    \"input.background\": \"#ffffff05\",\n    \"input.border\": \"#ffffff10\",\n    \"input.foreground\": \"#e4f0fb\",\n    \"input.placeholderForeground\": \"#a6accd60\",\n    \"inputOption.activeBackground\": \"#00000000\",\n    \"inputOption.activeBorder\": \"#00000000\",\n    \"inputOption.activeForeground\": \"#ffffff\",\n    \"inputValidation.errorBackground\": \"#1b1e28\",\n    \"inputValidation.errorBorder\": \"#d0679d\",\n    \"inputValidation.errorForeground\": \"#d0679d\",\n    \"inputValidation.infoBackground\": \"#506477\",\n    \"inputValidation.infoBorder\": \"#89ddff\",\n    \"inputValidation.warningBackground\": \"#506477\",\n    \"inputValidation.warningBorder\": \"#fffac2\",\n    \"list.activeSelectionBackground\": \"#30334080\",\n    \"list.activeSelectionForeground\": \"#e4f0fb\",\n    \"list.deemphasizedForeground\": \"#767c9d\",\n    \"list.dropBackground\": \"#506477\",\n    \"list.errorForeground\": \"#d0679d\",\n    \"list.filterMatchBackground\": \"#89ddff60\",\n    \"list.focusBackground\": \"#30334080\",\n    \"list.focusForeground\": \"#a6accd\",\n    \"list.focusOutline\": \"#00000000\",\n    \"list.highlightForeground\": \"#5fb3a1\",\n    \"list.hoverBackground\": \"#30334080\",\n    \"list.hoverForeground\": \"#e4f0fb\",\n    \"list.inactiveSelectionBackground\": \"#30334080\",\n    \"list.inactiveSelectionForeground\": \"#e4f0fb\",\n    \"list.invalidItemForeground\": \"#fffac2\",\n    \"list.warningForeground\": \"#fffac2\",\n    \"listFilterWidget.background\": \"#303340\",\n    \"listFilterWidget.noMatchesOutline\": \"#d0679d\",\n    \"listFilterWidget.outline\": \"#00000000\",\n    \"menu.background\": \"#1b1e28\",\n    \"menu.foreground\": \"#e4f0fb\",\n    \"menu.selectionBackground\": \"#303340\",\n    \"menu.selectionForeground\": \"#7390AA\",\n    \"menu.separatorBackground\": \"#767c9d\",\n    \"menubar.selectionBackground\": \"#717cb425\",\n    \"menubar.selectionForeground\": \"#a6accd\",\n    \"merge.commonContentBackground\": \"#a6accd29\",\n    \"merge.commonHeaderBackground\": \"#a6accd66\",\n    \"merge.currentContentBackground\": \"#5fb3a133\",\n    \"merge.currentHeaderBackground\": \"#5fb3a180\",\n    \"merge.incomingContentBackground\": \"#89ddff33\",\n    \"merge.incomingHeaderBackground\": \"#89ddff80\",\n    \"minimap.errorHighlight\": \"#d0679d\",\n    \"minimap.findMatchHighlight\": \"#ADD7FF\",\n    \"minimap.selectionHighlight\": \"#e4f0fb40\",\n    \"minimap.warningHighlight\": \"#fffac2\",\n    \"minimapGutter.addedBackground\": \"#5fb3a180\",\n    \"minimapGutter.deletedBackground\": \"#d0679d80\",\n    \"minimapGutter.modifiedBackground\": \"#ADD7FF80\",\n    \"minimapSlider.activeBackground\": \"#a6accd30\",\n    \"minimapSlider.background\": \"#a6accd20\",\n    \"minimapSlider.hoverBackground\": \"#a6accd30\",\n    \"notebook.cellBorderColor\": \"#1b1e28\",\n    \"notebook.cellInsertionIndicator\": \"#00000000\",\n    \"notebook.cellStatusBarItemHoverBackground\": \"#ffffff26\",\n    \"notebook.cellToolbarSeparator\": \"#303340\",\n    \"notebook.focusedCellBorder\": \"#00000000\",\n    \"notebook.focusedEditorBorder\": \"#00000000\",\n    \"notebook.focusedRowBorder\": \"#00000000\",\n    \"notebook.inactiveFocusedCellBorder\": \"#00000000\",\n    \"notebook.outputContainerBackgroundColor\": \"#1b1e28\",\n    \"notebook.rowHoverBackground\": \"#30334000\",\n    \"notebook.selectedCellBackground\": \"#303340\",\n    \"notebook.selectedCellBorder\": \"#1b1e28\",\n    \"notebook.symbolHighlightBackground\": \"#ffffff0b\",\n    \"notebookScrollbarSlider.activeBackground\": \"#a6accd25\",\n    \"notebookScrollbarSlider.background\": \"#00000050\",\n    \"notebookScrollbarSlider.hoverBackground\": \"#a6accd25\",\n    \"notebookStatusErrorIcon.foreground\": \"#d0679d\",\n    \"notebookStatusRunningIcon.foreground\": \"#a6accd\",\n    \"notebookStatusSuccessIcon.foreground\": \"#5fb3a1\",\n    \"notificationCenterHeader.background\": \"#303340\",\n    \"notificationLink.foreground\": \"#ADD7FF\",\n    \"notifications.background\": \"#1b1e28\",\n    \"notifications.border\": \"#303340\",\n    \"notifications.foreground\": \"#e4f0fb\",\n    \"notificationsErrorIcon.foreground\": \"#d0679d\",\n    \"notificationsInfoIcon.foreground\": \"#ADD7FF\",\n    \"notificationsWarningIcon.foreground\": \"#fffac2\",\n    \"panel.background\": \"#1b1e28\",\n    \"panel.border\": \"#00000030\",\n    \"panel.dropBorder\": \"#a6accd\",\n    \"panelSection.border\": \"#1b1e28\",\n    \"panelSection.dropBackground\": \"#7390AA80\",\n    \"panelSectionHeader.background\": \"#303340\",\n    \"panelTitle.activeBorder\": \"#a6accd\",\n    \"panelTitle.activeForeground\": \"#a6accd\",\n    \"panelTitle.inactiveForeground\": \"#a6accd99\",\n    \"peekView.border\": \"#00000030\",\n    \"peekViewEditor.background\": \"#a6accd05\",\n    \"peekViewEditor.matchHighlightBackground\": \"#303340\",\n    \"peekViewEditorGutter.background\": \"#a6accd05\",\n    \"peekViewResult.background\": \"#a6accd05\",\n    \"peekViewResult.fileForeground\": \"#ffffff\",\n    \"peekViewResult.lineForeground\": \"#a6accd\",\n    \"peekViewResult.matchHighlightBackground\": \"#303340\",\n    \"peekViewResult.selectionBackground\": \"#717cb425\",\n    \"peekViewResult.selectionForeground\": \"#ffffff\",\n    \"peekViewTitle.background\": \"#a6accd05\",\n    \"peekViewTitleDescription.foreground\": \"#a6accd60\",\n    \"peekViewTitleLabel.foreground\": \"#ffffff\",\n    \"pickerGroup.border\": \"#a6accd\",\n    \"pickerGroup.foreground\": \"#89ddff\",\n    \"problemsErrorIcon.foreground\": \"#d0679d\",\n    \"problemsInfoIcon.foreground\": \"#ADD7FF\",\n    \"problemsWarningIcon.foreground\": \"#fffac2\",\n    \"progressBar.background\": \"#89ddff\",\n    \"quickInput.background\": \"#1b1e28\",\n    \"quickInput.foreground\": \"#a6accd\",\n    \"quickInputList.focusBackground\": \"#a6accd10\",\n    \"quickInputTitle.background\": \"#ffffff1b\",\n    \"sash.hoverBorder\": \"#00000000\",\n    \"scm.providerBorder\": \"#e4f0fb10\",\n    \"scrollbar.shadow\": \"#00000000\",\n    \"scrollbarSlider.activeBackground\": \"#a6accd25\",\n    \"scrollbarSlider.background\": \"#00000080\",\n    \"scrollbarSlider.hoverBackground\": \"#a6accd25\",\n    \"searchEditor.findMatchBackground\": \"#ADD7FF50\",\n    \"searchEditor.textInputBorder\": \"#ffffff10\",\n    \"selection.background\": \"#a6accd\",\n    \"settings.checkboxBackground\": \"#1b1e28\",\n    \"settings.checkboxBorder\": \"#ffffff10\",\n    \"settings.checkboxForeground\": \"#e4f0fb\",\n    \"settings.dropdownBackground\": \"#1b1e28\",\n    \"settings.dropdownBorder\": \"#ffffff10\",\n    \"settings.dropdownForeground\": \"#e4f0fb\",\n    \"settings.dropdownListBorder\": \"#e4f0fb10\",\n    \"settings.focusedRowBackground\": \"#00000000\",\n    \"settings.headerForeground\": \"#e4f0fb\",\n    \"settings.modifiedItemIndicator\": \"#ADD7FF\",\n    \"settings.numberInputBackground\": \"#ffffff05\",\n    \"settings.numberInputBorder\": \"#ffffff10\",\n    \"settings.numberInputForeground\": \"#e4f0fb\",\n    \"settings.textInputBackground\": \"#ffffff05\",\n    \"settings.textInputBorder\": \"#ffffff10\",\n    \"settings.textInputForeground\": \"#e4f0fb\",\n    \"sideBar.background\": \"#1b1e28\",\n    \"sideBar.dropBackground\": \"#7390AA80\",\n    \"sideBar.foreground\": \"#767c9d\",\n    \"sideBarSectionHeader.background\": \"#1b1e28\",\n    \"sideBarSectionHeader.foreground\": \"#a6accd\",\n    \"sideBarTitle.foreground\": \"#a6accd\",\n    \"statusBar.background\": \"#1b1e28\",\n    \"statusBar.debuggingBackground\": \"#303340\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.foreground\": \"#a6accd\",\n    \"statusBar.noFolderBackground\": \"#1b1e28\",\n    \"statusBar.noFolderForeground\": \"#a6accd\",\n    \"statusBarItem.activeBackground\": \"#ffffff2e\",\n    \"statusBarItem.errorBackground\": \"#d0679d\",\n    \"statusBarItem.errorForeground\": \"#ffffff\",\n    \"statusBarItem.hoverBackground\": \"#ffffff1f\",\n    \"statusBarItem.prominentBackground\": \"#00000080\",\n    \"statusBarItem.prominentForeground\": \"#a6accd\",\n    \"statusBarItem.prominentHoverBackground\": \"#0000004d\",\n    \"statusBarItem.remoteBackground\": \"#303340\",\n    \"statusBarItem.remoteForeground\": \"#e4f0fb\",\n    \"symbolIcon.arrayForeground\": \"#a6accd\",\n    \"symbolIcon.booleanForeground\": \"#a6accd\",\n    \"symbolIcon.classForeground\": \"#fffac2\",\n    \"symbolIcon.colorForeground\": \"#a6accd\",\n    \"symbolIcon.constantForeground\": \"#a6accd\",\n    \"symbolIcon.constructorForeground\": \"#f087bd\",\n    \"symbolIcon.enumeratorForeground\": \"#fffac2\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#ADD7FF\",\n    \"symbolIcon.eventForeground\": \"#fffac2\",\n    \"symbolIcon.fieldForeground\": \"#ADD7FF\",\n    \"symbolIcon.fileForeground\": \"#a6accd\",\n    \"symbolIcon.folderForeground\": \"#a6accd\",\n    \"symbolIcon.functionForeground\": \"#f087bd\",\n    \"symbolIcon.interfaceForeground\": \"#ADD7FF\",\n    \"symbolIcon.keyForeground\": \"#a6accd\",\n    \"symbolIcon.keywordForeground\": \"#a6accd\",\n    \"symbolIcon.methodForeground\": \"#f087bd\",\n    \"symbolIcon.moduleForeground\": \"#a6accd\",\n    \"symbolIcon.namespaceForeground\": \"#a6accd\",\n    \"symbolIcon.nullForeground\": \"#a6accd\",\n    \"symbolIcon.numberForeground\": \"#a6accd\",\n    \"symbolIcon.objectForeground\": \"#a6accd\",\n    \"symbolIcon.operatorForeground\": \"#a6accd\",\n    \"symbolIcon.packageForeground\": \"#a6accd\",\n    \"symbolIcon.propertyForeground\": \"#a6accd\",\n    \"symbolIcon.referenceForeground\": \"#a6accd\",\n    \"symbolIcon.snippetForeground\": \"#a6accd\",\n    \"symbolIcon.stringForeground\": \"#a6accd\",\n    \"symbolIcon.structForeground\": \"#a6accd\",\n    \"symbolIcon.textForeground\": \"#a6accd\",\n    \"symbolIcon.typeParameterForeground\": \"#a6accd\",\n    \"symbolIcon.unitForeground\": \"#a6accd\",\n    \"symbolIcon.variableForeground\": \"#ADD7FF\",\n    \"tab.activeBackground\": \"#30334080\",\n    \"tab.activeForeground\": \"#e4f0fb\",\n    \"tab.activeModifiedBorder\": \"#ADD7FF\",\n    \"tab.border\": \"#00000000\",\n    \"tab.inactiveBackground\": \"#1b1e28\",\n    \"tab.inactiveForeground\": \"#767c9d\",\n    \"tab.inactiveModifiedBorder\": \"#ADD7FF80\",\n    \"tab.lastPinnedBorder\": \"#00000000\",\n    \"tab.unfocusedActiveBackground\": \"#1b1e28\",\n    \"tab.unfocusedActiveForeground\": \"#a6accd\",\n    \"tab.unfocusedActiveModifiedBorder\": \"#ADD7FF40\",\n    \"tab.unfocusedInactiveBackground\": \"#1b1e28\",\n    \"tab.unfocusedInactiveForeground\": \"#a6accd80\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#ADD7FF40\",\n    \"terminal.ansiBlack\": \"#1b1e28\",\n    \"terminal.ansiBlue\": \"#89ddff\",\n    \"terminal.ansiBrightBlack\": \"#a6accd\",\n    \"terminal.ansiBrightBlue\": \"#ADD7FF\",\n    \"terminal.ansiBrightCyan\": \"#ADD7FF\",\n    \"terminal.ansiBrightGreen\": \"#5DE4c7\",\n    \"terminal.ansiBrightMagenta\": \"#f087bd\",\n    \"terminal.ansiBrightRed\": \"#d0679d\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#fffac2\",\n    \"terminal.ansiCyan\": \"#89ddff\",\n    \"terminal.ansiGreen\": \"#5DE4c7\",\n    \"terminal.ansiMagenta\": \"#f087bd\",\n    \"terminal.ansiRed\": \"#d0679d\",\n    \"terminal.ansiWhite\": \"#ffffff\",\n    \"terminal.ansiYellow\": \"#fffac2\",\n    \"terminal.border\": \"#00000000\",\n    \"terminal.foreground\": \"#a6accd\",\n    \"terminal.selectionBackground\": \"#717cb425\",\n    \"terminalCommandDecoration.defaultBackground\": \"#767c9d\",\n    \"terminalCommandDecoration.errorBackground\": \"#d0679d\",\n    \"terminalCommandDecoration.successBackground\": \"#5DE4c7\",\n    \"testing.iconErrored\": \"#d0679d\",\n    \"testing.iconFailed\": \"#d0679d\",\n    \"testing.iconPassed\": \"#5DE4c7\",\n    \"testing.iconQueued\": \"#fffac2\",\n    \"testing.iconSkipped\": \"#7390AA\",\n    \"testing.iconUnset\": \"#7390AA\",\n    \"testing.message.error.decorationForeground\": \"#d0679d\",\n    \"testing.message.error.lineBackground\": \"#d0679d33\",\n    \"testing.message.hint.decorationForeground\": \"#7390AAb3\",\n    \"testing.message.info.decorationForeground\": \"#ADD7FF\",\n    \"testing.message.info.lineBackground\": \"#89ddff33\",\n    \"testing.message.warning.decorationForeground\": \"#fffac2\",\n    \"testing.message.warning.lineBackground\": \"#fffac233\",\n    \"testing.peekBorder\": \"#d0679d\",\n    \"testing.runAction\": \"#5DE4c7\",\n    \"textBlockQuote.background\": \"#7390AA1a\",\n    \"textBlockQuote.border\": \"#89ddff80\",\n    \"textCodeBlock.background\": \"#00000050\",\n    \"textLink.activeForeground\": \"#ADD7FF\",\n    \"textLink.foreground\": \"#ADD7FF\",\n    \"textPreformat.foreground\": \"#e4f0fb\",\n    \"textSeparator.foreground\": \"#ffffff2e\",\n    \"titleBar.activeBackground\": \"#1b1e28\",\n    \"titleBar.activeForeground\": \"#a6accd\",\n    \"titleBar.inactiveBackground\": \"#1b1e28\",\n    \"titleBar.inactiveForeground\": \"#767c9d\",\n    \"tree.indentGuidesStroke\": \"#303340\",\n    \"tree.tableColumnsBorder\": \"#a6accd20\",\n    \"welcomePage.progress.background\": \"#ffffff05\",\n    \"welcomePage.progress.foreground\": \"#5fb3a1\",\n    \"welcomePage.tileBackground\": \"#1b1e28\",\n    \"welcomePage.tileHoverBackground\": \"#303340\",\n    \"widget.shadow\": \"#00000030\"\n  },\n  \"displayName\": \"Poimandres\",\n  \"name\": \"poimandres\",\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#767c9dB0\"\n      }\n    },\n    {\n      \"scope\": \"meta.parameters comment.block\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#a6accd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.object\",\n        \"variable.other.readwrite.alias\",\n        \"meta.import variable.other.readwrite\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other\",\n        \"support.type.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.object.property\",\n        \"variable.other.property\",\n        \"support.variable.property\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.method\",\n        \"string.unquoted\",\n        \"meta.object.member\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable - meta.import\",\n        \"constant.other.placeholder\",\n        \"meta.object-literal.key-meta.object.member\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7c0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.new\",\n        \"keyword.control.new\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.this\",\n        \"storage.modifier.async\",\n        \"storage.modifier\",\n        \"variable.language.super\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.error\",\n        \"keyword.control.trycatch\",\n        \"keyword.operator.expression.delete\",\n        \"keyword.operator.expression.void\",\n        \"keyword.operator.void\",\n        \"keyword.operator.delete\",\n        \"constant.language.null\",\n        \"constant.language.boolean.false\",\n        \"constant.language.undefined\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d0679d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"variable.other.readwrite.js\",\n        \"meta.definition.variable variable.other.constant\",\n        \"meta.definition.variable variable.other.readwrite\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.color\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\",\n        \"invalid.illegal\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d0679d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d0679d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control\",\n        \"keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6accd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.module\",\n        \"keyword.control.import\",\n        \"keyword.control.export\",\n        \"keyword.control.default\",\n        \"meta.import\",\n        \"meta.export\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"Keyword\",\n        \"Storage\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword-meta.export\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.brace\",\n        \"punctuation\",\n        \"keyword.operator.existential\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6accd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.color\",\n        \"meta.tag\",\n        \"punctuation.definition.tag\",\n        \"punctuation.separator.inheritance.php\",\n        \"punctuation.definition.tag.html\",\n        \"punctuation.definition.tag.begin.html\",\n        \"punctuation.definition.tag.end.html\",\n        \"punctuation.section.embedded\",\n        \"keyword.other.template\",\n        \"keyword.other.substitution\",\n        \"meta.objectliteral\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"entity.name.tag\",\n        \"meta.tag.sgml\",\n        \"markup.deleted.git_gutter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": \"variable.function, source meta.function-call entity.name.function, source meta.function-call entity.name.function, source meta.method-call entity.name.function, meta.class meta.group.braces.curly meta.function-call variable.function, meta.class meta.field.declaration meta.function-call entity.name.function, variable.function.constructor, meta.block meta.var.expr meta.function-call entity.name.function, support.function.console, meta.function-call support.function, meta.property.class variable.other.class, punctuation.definition.entity.css\",\n      \"settings\": {\n        \"foreground\": \"#e4f0fbd0\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, meta.class entity.name.class, meta.class entity.name.type.class, meta.class meta.function-call variable.function, keyword.other.important\",\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.cpp meta.block variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.other.variable\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"support.constant\",\n        \"constant.character\",\n        \"constant.escape\",\n        \"keyword.other.unit\",\n        \"keyword.other\",\n        \"string\",\n        \"constant.language\",\n        \"constant.other.symbol\",\n        \"constant.other.key\",\n        \"markup.heading\",\n        \"markup.inserted.git_gutter\",\n        \"meta.group.braces.curly constant.other.object.key.js string.unquoted.label.js\",\n        \"text.html.derivative\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type.declaration\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.alias\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6accd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.as\",\n        \"entity.name.type\",\n        \"support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6accdC0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"support.orther.namespace.use.php\",\n        \"meta.use.php\",\n        \"support.other.namespace.php\",\n        \"markup.changed.git_gutter\",\n        \"support.type.sys-types\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class\",\n        \"support.constant\",\n        \"variable.other.constant.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.type.property-name\",\n        \"source.sass support.type.property-name\",\n        \"source.scss support.type.property-name\",\n        \"source.less support.type.property-name\",\n        \"source.stylus support.type.property-name\",\n        \"source.postcss support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.module.js\",\n        \"variable.import.parameter.js\",\n        \"variable.other.class.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.method.js\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.class-method.js entity.name.function.js\",\n        \"variable.function.constructor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.basic entity.other.attribute-name.html\",\n        \"text.html.basic entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#5fb3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5fb3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.sass keyword.control\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#42675A\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#506477\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5fb3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5fb3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"*url*\",\n        \"*link*\",\n        \"*uri*\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"tag.decorator.js entity.name.tag.js\",\n        \"tag.decorator.js punctuation.definition.tag.js\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#42675A\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js constant.other.object.key.js string.unquoted.label.js\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#5fb3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown\",\n        \"punctuation.definition.list_item.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown markup.inline.raw.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown markup.inline.raw.markdown punctuation.definition.raw.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markdown.heading\",\n        \"markup.heading | markup.heading entity.name\",\n        \"markup.heading.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.bold string\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold markup.italic\",\n        \"markup.italic markup.bold\",\n        \"markup.quote markup.bold\",\n        \"markup.bold markup.italic string\",\n        \"markup.italic markup.bold string\",\n        \"markup.quote markup.bold string\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strike\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote punctuation.definition.blockquote.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link.description.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.raw.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.raw.block.fenced.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50647750\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.fenced.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#50647750\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.raw.block.fenced.markdown\",\n        \"variable.language.fenced.markdown\",\n        \"punctuation.section.class.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.fenced.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91B4D5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.separator\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.table\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#fffac2\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#d0679d\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.section.markdown\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.setext.2.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e4f0fb\"\n      }\n    },\n    {\n      \"scope\": \"meta.paragraph.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e4f0fbd0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.from-file.diff\",\n        \"meta.diff.header.from-file\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#506477\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#7390AA\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown\",\n      \"settings\": {\n        \"foreground\": \"#767c9d\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\",\n        \"punctuation.definition.list.begin.markdown\",\n        \"markup.list.unnumbered.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link.description.title.markdown punctuation.definition.string.markdown\",\n        \"meta.link.inline.markdown string.other.link.description.title.markdown\",\n        \"string.other.link.description.title.markdown punctuation.definition.string.begin.markdown\",\n        \"string.other.link.description.title.markdown punctuation.definition.string.end.markdown\",\n        \"meta.image.inline.markdown string.other.link.description.title.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.link.inline.markdown string.other.link.title.markdown\",\n        \"meta.link.reference.markdown string.other.link.title.markdown\",\n        \"meta.link.reference.def.markdown markup.underline.link.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link.markdown\",\n        \"string.other.link.description.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5DE4c7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"fenced_code.block.language\",\n        \"markup.inline.raw.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.markdown\",\n        \"punctuation.definition.raw.markdown\",\n        \"punctuation.definition.heading.markdown\",\n        \"punctuation.definition.bold.markdown\",\n        \"punctuation.definition.italic.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADD7FF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ignore\",\n        \"log.error\",\n        \"log.exception\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d0679d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.verbose\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6accd\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { poimandres as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa,OAAO,MAAM,CAAC;IAC7B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,sCAAsC;QACtC,sCAAsC;QACtC,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,yBAAyB;QACzB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,6CAA6C;QAC7C,4CAA4C;QAC5C,wCAAwC;QACxC,oCAAoC;QACpC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,yCAAyC;QACzC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,8BAA8B;QAC9B,8CAA8C;QAC9C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,yBAAyB;QACzB,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,iCAAiC;QACjC,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,mBAAmB;QACnB,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,gCAAgC;QAChC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,2CAA2C;QAC3C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,uBAAuB;QACvB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,+BAA+B;QAC/B,uBAAuB;QACvB,wBAAwB;QACxB,8BAA8B;QAC9B,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,mBAAmB;QACnB,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,mCAAmC;QACnC,6CAA6C;QAC7C,iCAAiC;QACjC,8BAA8B;QAC9B,gCAAgC;QAChC,6BAA6B;QAC7B,sCAAsC;QACtC,2CAA2C;QAC3C,+BAA+B;QAC/B,mCAAmC;QACnC,+BAA+B;QAC/B,sCAAsC;QACtC,4CAA4C;QAC5C,sCAAsC;QACtC,2CAA2C;QAC3C,sCAAsC;QACtC,wCAAwC;QACxC,wCAAwC;QACxC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,+BAA+B;QAC/B,iCAAiC;QACjC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,kCAAkC;QAClC,8BAA8B;QAC9B,oBAAoB;QACpB,sBAAsB;QACtB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,gCAAgC;QAChC,wBAAwB;QACxB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,+CAA+C;QAC/C,6CAA6C;QAC7C,+CAA+C;QAC/C,uBAAuB;QACvB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,qBAAqB;QACrB,8CAA8C;QAC9C,wCAAwC;QACxC,6CAA6C;QAC7C,6CAA6C;QAC7C,uCAAuC;QACvC,gDAAgD;QAChD,0CAA0C;QAC1C,sBAAsB;QACtB,qBAAqB;QACrB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,mCAAmC;QACnC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,eAAe;QACb;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}