(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7167],{31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i}),s(63410);var a=s(49973);s(13957);var n=s(22928);let i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}},63553:(e,t,s)=>{Promise.resolve().then(s.bind(s,94073)),Promise.resolve().then(s.bind(s,43432)),Promise.resolve().then(s.t.bind(s,35685,23)),Promise.resolve().then(s.bind(s,13957))},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(6024);s(50628);var n=s(89840),i=s(81197),r=s(31918);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:i,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,r.cn)(o({variant:s,size:i,className:t})),...d})}},79242:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(6024);s(50628);var n=s(31918);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},94073:(e,t,s)=>{"use strict";s.d(t,{SettingsForm:()=>C});var a=s(6024),n=s(70234),i=s(79242),r=s(50628),o=s(11676),l=s(31918);function d(e){let{className:t,...s}=e;return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}var c=s(96155);function u(e){let{className:t,...s}=e;return(0,a.jsx)(c.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:(0,a.jsx)(c.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}var x=s(38083),h=s(76227),p=s(3671),v=s(53138);function g(e){let{...t}=e;return(0,a.jsx)(x.bL,{"data-slot":"select",...t})}function f(e){let{...t}=e;return(0,a.jsx)(x.WT,{"data-slot":"select-value",...t})}function b(e){let{className:t,size:s="default",children:n,...i}=e;return(0,a.jsxs)(x.l9,{"data-slot":"select-trigger","data-size":s,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[n,(0,a.jsx)(x.In,{asChild:!0,children:(0,a.jsx)(h.A,{className:"size-4 opacity-50"})})]})}function j(e){let{className:t,children:s,position:n="popper",...i}=e;return(0,a.jsx)(x.ZL,{children:(0,a.jsxs)(x.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(k,{}),(0,a.jsx)(x.LM,{className:(0,l.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(w,{})]})})}function y(e){let{className:t,children:s,...n}=e;return(0,a.jsxs)(x.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(x.VF,{children:(0,a.jsx)(p.A,{className:"size-4"})})}),(0,a.jsx)(x.p4,{children:s})]})}function k(e){let{className:t,...s}=e;return(0,a.jsx)(x.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(v.A,{className:"size-4"})})}function w(e){let{className:t,...s}=e;return(0,a.jsx)(x.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(h.A,{className:"size-4"})})}var N=s(13957);function C(e){var t,s,o,l,c;let{userId:x,extensionSettings:h,preferences:p}=e,[v,k]=(0,r.useState)({defaultModel:h.defaultModel||"claude-3-sonnet",autoSave:null==(t=h.autoSave)||t,codeCompletion:null==(s=h.codeCompletion)||s,maxTokens:h.maxTokens||4e3,temperature:h.temperature||.7,customPrompt:h.customPrompt||"",theme:p.theme||"system",notifications:null==(o=p.notifications)||o,analytics:null==(l=p.analytics)||l,autoConnect:null==(c=p.autoConnect)||c}),[w,C]=(0,r.useState)(!1),z=(e,t)=>{k(s=>({...s,[e]:t}))},S=async()=>{C(!0);try{let e={defaultModel:v.defaultModel,autoSave:v.autoSave,codeCompletion:v.codeCompletion,maxTokens:v.maxTokens,temperature:v.temperature,customPrompt:v.customPrompt},t={theme:v.theme,notifications:v.notifications,analytics:v.analytics,autoConnect:v.autoConnect};(await fetch("/api/extension/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({extensionSettings:e,preferences:t})})).ok?N.toast.success("Settings saved successfully"):N.toast.error("Failed to save settings")}catch(e){N.toast.error("Failed to save settings")}finally{C(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"AI Model Settings"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d,{htmlFor:"defaultModel",children:"Default Model"}),(0,a.jsxs)(g,{value:v.defaultModel,onValueChange:e=>z("defaultModel",e),children:[(0,a.jsx)(b,{children:(0,a.jsx)(f,{})}),(0,a.jsxs)(j,{children:[(0,a.jsx)(y,{value:"claude-3-sonnet",children:"Claude 3 Sonnet"}),(0,a.jsx)(y,{value:"claude-3-haiku",children:"Claude 3 Haiku"}),(0,a.jsx)(y,{value:"gpt-4",children:"GPT-4"}),(0,a.jsx)(y,{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"}),(0,a.jsx)(y,{value:"gemini-pro",children:"Gemini Pro"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d,{htmlFor:"maxTokens",children:"Max Tokens"}),(0,a.jsx)(i.p,{id:"maxTokens",type:"number",min:"100",max:"8000",value:v.maxTokens,onChange:e=>z("maxTokens",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d,{htmlFor:"temperature",children:"Temperature"}),(0,a.jsx)(i.p,{id:"temperature",type:"number",min:"0",max:"2",step:"0.1",value:v.temperature,onChange:e=>z("temperature",parseFloat(e.target.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d,{htmlFor:"customPrompt",children:"Custom System Prompt"}),(0,a.jsx)(m,{id:"customPrompt",placeholder:"Enter a custom system prompt for the AI...",value:v.customPrompt,onChange:e=>z("customPrompt",e.target.value),rows:3})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Extension Behavior"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d,{children:"Auto Save"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically save changes to files"})]}),(0,a.jsx)(u,{checked:v.autoSave,onCheckedChange:e=>z("autoSave",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d,{children:"Code Completion"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable AI-powered code completion"})]}),(0,a.jsx)(u,{checked:v.codeCompletion,onCheckedChange:e=>z("codeCompletion",e)})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"User Preferences"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d,{htmlFor:"theme",children:"Theme"}),(0,a.jsxs)(g,{value:v.theme,onValueChange:e=>z("theme",e),children:[(0,a.jsx)(b,{children:(0,a.jsx)(f,{})}),(0,a.jsxs)(j,{children:[(0,a.jsx)(y,{value:"system",children:"System"}),(0,a.jsx)(y,{value:"light",children:"Light"}),(0,a.jsx)(y,{value:"dark",children:"Dark"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d,{children:"Notifications"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications about updates and usage"})]}),(0,a.jsx)(u,{checked:v.notifications,onCheckedChange:e=>z("notifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d,{children:"Usage Analytics"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Allow collection of usage data for analytics"})]}),(0,a.jsx)(u,{checked:v.analytics,onCheckedChange:e=>z("analytics",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d,{children:"Auto Connect"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically connect extension when VS Code starts"})]}),(0,a.jsx)(u,{checked:v.autoConnect,onCheckedChange:e=>z("autoConnect",e)})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)(n.$,{onClick:S,disabled:w,className:"w-full",children:w?"Saving...":"Save Settings"})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,3572,5514,7651,7764,6655,2913,4499,7358],()=>t(63553)),_N_E=e.O()}]);