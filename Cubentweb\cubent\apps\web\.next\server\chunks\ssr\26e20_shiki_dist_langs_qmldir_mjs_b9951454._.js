module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/qmldir.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>qmldir)
});
const lang = Object.freeze({
    "displayName": "QML Directory",
    "name": "qmldir",
    "patterns": [
        {
            "include": "#comment"
        },
        {
            "include": "#keywords"
        },
        {
            "include": "#version"
        },
        {
            "include": "#names"
        }
    ],
    "repository": {
        "comment": {
            "patterns": [
                {
                    "begin": "#",
                    "end": "$",
                    "name": "comment.line.number-sign.qmldir"
                }
            ]
        },
        "file-name": {
            "patterns": [
                {
                    "match": "\\b\\w+\\.(qmltypes|qml|js)\\b",
                    "name": "string.unquoted.qmldir"
                }
            ]
        },
        "identifier": {
            "patterns": [
                {
                    "match": "\\b\\w+\\b",
                    "name": "variable.parameter.qmldir"
                }
            ]
        },
        "keywords": {
            "patterns": [
                {
                    "match": "\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\b",
                    "name": "keyword.other.qmldir"
                }
            ]
        },
        "module-name": {
            "patterns": [
                {
                    "match": "\\b[A-Z]\\w*\\b",
                    "name": "entity.name.type.qmldir"
                }
            ]
        },
        "names": {
            "patterns": [
                {
                    "include": "#file-name"
                },
                {
                    "include": "#module-name"
                },
                {
                    "include": "#identifier"
                }
            ]
        },
        "version": {
            "patterns": [
                {
                    "match": "\\b\\d+\\.\\d+\\b",
                    "name": "constant.numeric.qml"
                }
            ]
        }
    },
    "scopeName": "source.qmldir"
});
var qmldir = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_qmldir_mjs_b9951454._.js.map