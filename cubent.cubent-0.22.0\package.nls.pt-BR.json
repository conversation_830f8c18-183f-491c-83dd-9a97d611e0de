{"extension.displayName": "cubent coder", "extension.description": "Uma equipe completa de desenvolvimento de agentes de IA no seu editor.", "command.newTask.title": "Nova Tarefa", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "Adicionar ao Contexto", "command.openInNewTab.title": "Abrir em Nova Aba", "command.focusInput.title": "Focar Campo de Entrada", "command.setCustomStoragePath.title": "Definir Caminho de Armazenamento Personalizado", "command.terminal.addToContext.title": "Adicionar <PERSON>teúdo do Terminal ao Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceitar Entrada/Sugestão", "views.activitybar.title": "cubent Code", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent Code", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Hist<PERSON><PERSON><PERSON>", "command.openInEditor.title": "Abrir no Editor", "command.settings.title": "Configurações", "command.documentation.title": "Documentação", "configuration.title": "cubent coder", "commands.allowedCommands.description": "Comandos que podem ser executados automaticamente quando 'Sempre aprovar operações de execução' está ativado", "settings.vsCodeLmModelSelector.description": "Configurações para a API do modelo de linguagem do VSCode", "settings.vsCodeLmModelSelector.vendor.description": "O fornecedor do modelo de linguagem (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "A família do modelo de linguagem (ex: gpt-4)", "settings.customStoragePath.description": "Caminho de armazenamento personalizado. Deixe vazio para usar o local padrão. Suporta caminhos absolutos (ex: 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "Habilitar cubent coder Cloud."}