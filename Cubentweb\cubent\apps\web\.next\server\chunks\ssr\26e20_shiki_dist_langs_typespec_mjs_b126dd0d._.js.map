{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/typespec.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"TypeSpec\", \"fileTypes\": [\"tsp\"], \"name\": \"typespec\", \"patterns\": [{ \"include\": \"#statement\" }], \"repository\": { \"alias-id\": { \"begin\": \"(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.alias-id.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"alias-statement\": { \"begin\": \"\\\\b(alias)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.type.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.alias-statement.typespec\", \"patterns\": [{ \"include\": \"#alias-id\" }, { \"include\": \"#type-parameters\" }] }, \"augment-decorator-statement\": { \"begin\": \"((@@)\\\\b[_$A-Za-z](?:[_$0-9A-Za-z]|\\\\.[_$A-Za-z])*\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.tsp\" }, \"2\": { \"name\": \"entity.name.tag.tsp\" } }, \"end\": \"(?=[_$A-Za-z])|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.augment-decorator-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#parenthesized-expression\" }] }, \"block-comment\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.tsp\" }, \"boolean-literal\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.tsp\" }, \"callExpression\": { \"begin\": \"(\\\\b[_$A-Za-z](?:[_$0-9A-Za-z]|\\\\.[_$A-Za-z])*\\\\b)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tsp\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.tsp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.tsp\" } }, \"name\": \"meta.callExpression.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"const-statement\": { \"begin\": \"\\\\b(const)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"variable.name.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.const-statement.typespec\", \"patterns\": [{ \"include\": \"#type-annotation\" }, { \"include\": \"#operator-assignment\" }, { \"include\": \"#expression\" }] }, \"decorator\": { \"begin\": \"((@)\\\\b[_$A-Za-z](?:[_$0-9A-Za-z]|\\\\.[_$A-Za-z])*\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.tsp\" }, \"2\": { \"name\": \"entity.name.tag.tsp\" } }, \"end\": \"(?=[_$A-Za-z])|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.decorator.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#parenthesized-expression\" }] }, \"decorator-declaration-statement\": { \"begin\": \"(?:(extern)\\\\s+)?\\\\b(dec)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"keyword.other.tsp\" }, \"3\": { \"name\": \"entity.name.function.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.decorator-declaration-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#operation-parameters\" }] }, \"directive\": { \"begin\": \"\\\\s*(#\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.directive.name.tsp\" } }, \"end\": \"$|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.directive.typespec\", \"patterns\": [{ \"include\": \"#string-literal\" }, { \"include\": \"#identifier-expression\" }] }, \"doc-comment\": { \"begin\": \"/\\\\*\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"comment.block.tsp\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"comment.block.tsp\" } }, \"name\": \"comment.block.tsp\", \"patterns\": [{ \"include\": \"#doc-comment-block\" }] }, \"doc-comment-block\": { \"patterns\": [{ \"include\": \"#doc-comment-param\" }, { \"include\": \"#doc-comment-return-tag\" }, { \"include\": \"#doc-comment-unknown-tag\" }] }, \"doc-comment-param\": { \"captures\": { \"1\": { \"name\": \"keyword.tag.tspdoc\" }, \"2\": { \"name\": \"keyword.tag.tspdoc\" }, \"3\": { \"name\": \"variable.name.tsp\" } }, \"match\": \"((@)(?:param|template|prop))\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\b\", \"name\": \"comment.block.tsp\" }, \"doc-comment-return-tag\": { \"captures\": { \"1\": { \"name\": \"keyword.tag.tspdoc\" }, \"2\": { \"name\": \"keyword.tag.tspdoc\" } }, \"match\": \"((@)(?:returns))\\\\b\", \"name\": \"comment.block.tsp\" }, \"doc-comment-unknown-tag\": { \"captures\": { \"1\": { \"name\": \"entity.name.tag.tsp\" }, \"2\": { \"name\": \"entity.name.tag.tsp\" } }, \"match\": \"((@)(?:\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`))\\\\b\", \"name\": \"comment.block.tsp\" }, \"else-expression\": { \"begin\": \"\\\\b(else)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.else-expression.typespec\", \"patterns\": [{ \"include\": \"#projection-expression\" }, { \"include\": \"#projection-body\" }] }, \"else-if-expression\": { \"begin\": \"\\\\b(else)\\\\s+(if)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.else-if-expression.typespec\", \"patterns\": [{ \"include\": \"#projection-expression\" }, { \"include\": \"#projection-body\" }] }, \"enum-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.enum-body.typespec\", \"patterns\": [{ \"include\": \"#enum-member\" }, { \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#decorator\" }, { \"include\": \"#punctuation-comma\" }] }, \"enum-member\": { \"begin\": \"(?:(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*(:?))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.name.tsp\" }, \"2\": { \"name\": \"keyword.operator.type.annotation.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.enum-member.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-annotation\" }] }, \"enum-statement\": { \"begin\": \"\\\\b(enum)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.type.tsp\" } }, \"end\": \"(?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.enum-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#enum-body\" }] }, \"escape-character\": { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.tsp\" }, \"expression\": { \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#parenthesized-expression\" }, { \"include\": \"#valueof\" }, { \"include\": \"#typeof\" }, { \"include\": \"#type-arguments\" }, { \"include\": \"#object-literal\" }, { \"include\": \"#tuple-literal\" }, { \"include\": \"#tuple-expression\" }, { \"include\": \"#model-expression\" }, { \"include\": \"#callExpression\" }, { \"include\": \"#identifier-expression\" }] }, \"function-call\": { \"begin\": \"(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tsp\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.tsp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.tsp\" } }, \"name\": \"meta.function-call.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"function-declaration-statement\": { \"begin\": \"(?:(extern)\\\\s+)?\\\\b(fn)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"keyword.other.tsp\" }, \"3\": { \"name\": \"entity.name.function.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.function-declaration-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#operation-parameters\" }, { \"include\": \"#type-annotation\" }] }, \"identifier-expression\": { \"match\": \"\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`\", \"name\": \"entity.name.type.tsp\" }, \"if-expression\": { \"begin\": \"\\\\b(if)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.if-expression.typespec\", \"patterns\": [{ \"include\": \"#projection-expression\" }, { \"include\": \"#projection-body\" }] }, \"import-statement\": { \"begin\": \"\\\\b(import)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.import-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }] }, \"interface-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.interface-body.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#decorator\" }, { \"include\": \"#interface-member\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"interface-heritage\": { \"begin\": \"\\\\b(extends)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?=\\\\{)|(?=;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.interface-heritage.typespec\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"interface-member\": { \"begin\": \"(?:\\\\b(op)\\\\b\\\\s+)?(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.function.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.interface-member.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#operation-signature\" }] }, \"interface-statement\": { \"begin\": \"\\\\b(interface)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.interface-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#interface-heritage\" }, { \"include\": \"#interface-body\" }, { \"include\": \"#expression\" }] }, \"line-comment\": { \"match\": \"//.*$\", \"name\": \"comment.line.double-slash.tsp\" }, \"model-expression\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.model-expression.typespec\", \"patterns\": [{ \"include\": \"#model-property\" }, { \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#decorator\" }, { \"include\": \"#spread-operator\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"model-heritage\": { \"begin\": \"\\\\b(extends|is)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?=\\\\{)|(?=;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.model-heritage.typespec\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"model-property\": { \"begin\": '(?:(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)|(\\\\\"(?:[^\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\"))', \"beginCaptures\": { \"1\": { \"name\": \"variable.name.tsp\" }, \"2\": { \"name\": \"string.quoted.double.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.model-property.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#operator-assignment\" }, { \"include\": \"#expression\" }] }, \"model-statement\": { \"begin\": \"\\\\b(model)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.model-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#model-heritage\" }, { \"include\": \"#expression\" }] }, \"namespace-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.namespace-body.typespec\", \"patterns\": [{ \"include\": \"#statement\" }] }, \"namespace-name\": { \"begin\": \"(?=[_$A-Za-z])\", \"end\": \"((?=\\\\{)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.namespace-name.typespec\", \"patterns\": [{ \"include\": \"#identifier-expression\" }, { \"include\": \"#punctuation-accessor\" }] }, \"namespace-statement\": { \"begin\": \"\\\\b(namespace)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.namespace-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#namespace-name\" }, { \"include\": \"#namespace-body\" }] }, \"numeric-literal\": { \"match\": \"(?:\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$)|\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$)|(?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))\", \"name\": \"constant.numeric.tsp\" }, \"object-literal\": { \"begin\": \"#\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.hashcurlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.object-literal.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#object-literal-property\" }, { \"include\": \"#directive\" }, { \"include\": \"#spread-operator\" }, { \"include\": \"#punctuation-comma\" }] }, \"object-literal-property\": { \"begin\": \"(?:(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*(:))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.name.tsp\" }, \"2\": { \"name\": \"keyword.operator.type.annotation.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.object-literal-property.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#expression\" }] }, \"operation-heritage\": { \"begin\": \"\\\\b(is)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.operation-heritage.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"operation-parameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.tsp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.tsp\" } }, \"name\": \"meta.operation-parameters.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#decorator\" }, { \"include\": \"#model-property\" }, { \"include\": \"#spread-operator\" }, { \"include\": \"#punctuation-comma\" }] }, \"operation-signature\": { \"patterns\": [{ \"include\": \"#type-parameters\" }, { \"include\": \"#operation-heritage\" }, { \"include\": \"#operation-parameters\" }, { \"include\": \"#type-annotation\" }] }, \"operation-statement\": { \"begin\": \"\\\\b(op)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.function.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.operation-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#operation-signature\" }] }, \"operator-assignment\": { \"match\": \"=\", \"name\": \"keyword.operator.assignment.tsp\" }, \"parenthesized-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.tsp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.tsp\" } }, \"name\": \"meta.parenthesized-expression.typespec\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"projection\": { \"begin\": \"(from|to)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.projection.typespec\", \"patterns\": [{ \"include\": \"#projection-parameters\" }, { \"include\": \"#projection-body\" }] }, \"projection-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.projection-body.typespec\", \"patterns\": [{ \"include\": \"#projection-expression\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"projection-expression\": { \"patterns\": [{ \"include\": \"#else-if-expression\" }, { \"include\": \"#if-expression\" }, { \"include\": \"#else-expression\" }, { \"include\": \"#function-call\" }] }, \"projection-parameter\": { \"begin\": \"(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.name.tsp\" } }, \"end\": \"(?=\\\\))|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.projection-parameter.typespec\", \"patterns\": [] }, \"projection-parameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.tsp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.tsp\" } }, \"name\": \"meta.projection-parameters.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#projection-parameter\" }] }, \"projection-statement\": { \"begin\": \"\\\\b(projection)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)(#)(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"keyword.other.tsp\" }, \"3\": { \"name\": \"keyword.operator.selector.tsp\" }, \"4\": { \"name\": \"variable.name.tsp\" } }, \"end\": \"((?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b))\", \"name\": \"meta.projection-statement.typespec\", \"patterns\": [{ \"include\": \"#projection-statement-body\" }] }, \"projection-statement-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.projection-statement-body.typespec\", \"patterns\": [{ \"include\": \"#projection\" }] }, \"punctuation-accessor\": { \"match\": \"\\\\.\", \"name\": \"punctuation.accessor.tsp\" }, \"punctuation-comma\": { \"match\": \",\", \"name\": \"punctuation.comma.tsp\" }, \"punctuation-semicolon\": { \"match\": \";\", \"name\": \"punctuation.terminator.statement.tsp\" }, \"scalar-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.scalar-body.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#scalar-constructor\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"scalar-constructor\": { \"begin\": \"\\\\b(init)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.function.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.scalar-constructor.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#operation-parameters\" }] }, \"scalar-extends\": { \"begin\": \"\\\\b(extends)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.scalar-extends.typespec\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"scalar-statement\": { \"begin\": \"\\\\b(scalar)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.type.tsp\" } }, \"end\": \"(?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.scalar-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#scalar-extends\" }, { \"include\": \"#scalar-body\" }] }, \"spread-operator\": { \"begin\": \"\\\\.\\\\.\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.spread.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.spread-operator.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"statement\": { \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#augment-decorator-statement\" }, { \"include\": \"#decorator\" }, { \"include\": \"#model-statement\" }, { \"include\": \"#scalar-statement\" }, { \"include\": \"#union-statement\" }, { \"include\": \"#interface-statement\" }, { \"include\": \"#enum-statement\" }, { \"include\": \"#alias-statement\" }, { \"include\": \"#const-statement\" }, { \"include\": \"#namespace-statement\" }, { \"include\": \"#operation-statement\" }, { \"include\": \"#import-statement\" }, { \"include\": \"#using-statement\" }, { \"include\": \"#decorator-declaration-statement\" }, { \"include\": \"#function-declaration-statement\" }, { \"include\": \"#projection-statement\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"string-literal\": { \"begin\": '\"', \"end\": '\"|$', \"name\": \"string.quoted.double.tsp\", \"patterns\": [{ \"include\": \"#template-expression\" }, { \"include\": \"#escape-character\" }] }, \"template-expression\": { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.tsp\" } }, \"name\": \"meta.template-expression.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"token\": { \"patterns\": [{ \"include\": \"#doc-comment\" }, { \"include\": \"#line-comment\" }, { \"include\": \"#block-comment\" }, { \"include\": \"#triple-quoted-string-literal\" }, { \"include\": \"#string-literal\" }, { \"include\": \"#boolean-literal\" }, { \"include\": \"#numeric-literal\" }] }, \"triple-quoted-string-literal\": { \"begin\": '\"\"\"', \"end\": '\"\"\"', \"name\": \"string.quoted.triple.tsp\", \"patterns\": [{ \"include\": \"#template-expression\" }, { \"include\": \"#escape-character\" }] }, \"tuple-expression\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.open.tsp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.close.tsp\" } }, \"name\": \"meta.tuple-expression.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"tuple-literal\": { \"begin\": \"#\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.hashsquarebracket.open.tsp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.close.tsp\" } }, \"name\": \"meta.tuple-literal.typespec\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-annotation\": { \"begin\": \"\\\\s*(\\\\??)\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.optional.tsp\" }, \"2\": { \"name\": \"keyword.operator.type.annotation.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|=|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.type-annotation.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"type-argument\": { \"begin\": \"(?:(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*(=))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.type.tsp\" }, \"2\": { \"name\": \"keyword.operator.assignment.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.assignment.tsp\" } }, \"name\": \"meta.type-argument.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-arguments\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.tsp\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.tsp\" } }, \"name\": \"meta.type-arguments.typespec\", \"patterns\": [{ \"include\": \"#type-argument\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-parameter\": { \"begin\": \"(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.type.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.type-parameter.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#type-parameter-constraint\" }, { \"include\": \"#type-parameter-default\" }] }, \"type-parameter-constraint\": { \"begin\": \"extends\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.type-parameter-constraint.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"type-parameter-default\": { \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.assignment.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.type-parameter-default.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"type-parameters\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.tsp\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.tsp\" } }, \"name\": \"meta.type-parameters.typespec\", \"patterns\": [{ \"include\": \"#type-parameter\" }, { \"include\": \"#punctuation-comma\" }] }, \"typeof\": { \"begin\": \"\\\\b(typeof)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.typeof.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"union-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.tsp\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.tsp\" } }, \"name\": \"meta.union-body.typespec\", \"patterns\": [{ \"include\": \"#union-variant\" }, { \"include\": \"#token\" }, { \"include\": \"#directive\" }, { \"include\": \"#decorator\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"union-statement\": { \"begin\": \"\\\\b(union)\\\\b\\\\s+(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" }, \"2\": { \"name\": \"entity.name.type.tsp\" } }, \"end\": \"(?<=\\\\})|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.union-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#union-body\" }] }, \"union-variant\": { \"begin\": \"(?:(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b|`(?:[^`\\\\\\\\]|\\\\\\\\.)*`)\\\\s*(:))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.name.tsp\" }, \"2\": { \"name\": \"keyword.operator.type.annotation.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.union-variant.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#expression\" }] }, \"using-statement\": { \"begin\": \"\\\\b(using)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.using-statement.typespec\", \"patterns\": [{ \"include\": \"#token\" }, { \"include\": \"#identifier-expression\" }, { \"include\": \"#punctuation-accessor\" }] }, \"valueof\": { \"begin\": \"\\\\b(valueof)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tsp\" } }, \"end\": \"(?=>)|(?=,|;|@|\\\\)|\\\\}|\\\\b(?:extern)\\\\b|\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\b)\", \"name\": \"meta.valueof.typespec\", \"patterns\": [{ \"include\": \"#expression\" }] } }, \"scopeName\": \"source.tsp\", \"aliases\": [\"tsp\"] });\nvar typespec = [\n  lang\n];\n\nexport { typespec as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAY,YAAY;QAAC;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA+E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAA0D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAiI,QAAQ;YAA6C,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;QAAoB;QAAG,mBAAmB;YAAE,SAAS;YAAsB,QAAQ;QAAwB;QAAG,kBAAkB;YAAE,SAAS;YAA+D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA2E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAyD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAiI,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAA0F,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAiD,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAoH,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAA6F,QAAQ;QAAoB;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,SAAS;YAAuB,QAAQ;QAAoB;QAAG,2BAA2B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;YAAoE,QAAQ;QAAoB;QAAG,mBAAmB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAsE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA0E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAA2H,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAS,QAAQ;QAAgC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAmE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,kCAAkC;YAAE,SAAS;YAAyF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAgD,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAwD,QAAQ;QAAuB;QAAG,iBAAiB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA0H,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA2H,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAS,QAAQ;QAAgC;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA0H,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA4F,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA2H,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAkB,OAAO;YAA4H,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA6c,QAAQ;QAAuB;QAAG,kBAAkB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAqE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAwE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAK,QAAQ;QAAkC;QAAG,4BAA4B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAA0D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA0H,QAAQ;YAAsC,YAAY,EAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAyI,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAO,QAAQ;QAA2B;QAAG,qBAAqB;YAAE,SAAS;YAAK,QAAQ;QAAwB;QAAG,yBAAyB;YAAE,SAAS;YAAK,QAAQ;QAAuC;QAAG,eAAe;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAA0E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAgH,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA4E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAA2H,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,OAAO;YAAO,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAmG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAqE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAwH,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA0D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAwH,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAwH,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAwH,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAwH,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA2E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAA2H,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAqE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAkH,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAwH,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;IAAE;IAAG,aAAa;IAAc,WAAW;QAAC;KAAM;AAAC;AACvj5B,IAAI,WAAW;IACb;CACD", "ignoreList": [0], "debugId": null}}]}