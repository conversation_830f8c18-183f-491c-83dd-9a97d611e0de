{"extension.displayName": "cubent coder", "extension.description": "Une équipe complète de développement d'agents IA dans votre éditeur.", "command.newTask.title": "Nouvelle Tâche", "command.explainCode.title": "Expliquer le Code", "command.fixCode.title": "Corriger le Code", "command.improveCode.title": "Améliorer le Code", "command.addToContext.title": "A<PERSON>ter au Contexte", "command.openInNewTab.title": "Ouv<PERSON>r dans un Nouvel Onglet", "command.focusInput.title": "Focus sur le Champ de Saisie", "command.setCustomStoragePath.title": "Définir le Chemin de Stockage Personnalisé", "command.terminal.addToContext.title": "Ajouter le Contenu du Terminal au Contexte", "command.terminal.fixCommand.title": "<PERSON>rri<PERSON> cette <PERSON>e", "command.terminal.explainCommand.title": "Expliquer cette <PERSON>e", "command.acceptInput.title": "Accepter l'Entrée/Suggestion", "views.activitybar.title": "cubent Code", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent Code", "command.mcpServers.title": "Serveurs MCP", "command.prompts.title": "Modes", "command.history.title": "Historique", "command.openInEditor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans l'Éditeur", "command.settings.title": "Paramètres", "command.documentation.title": "Documentation", "configuration.title": "cubent coder", "commands.allowedCommands.description": "Commandes pouvant être exécutées automatiquement lorsque 'Toujours approuver les opérations d'exécution' est activé", "settings.vsCodeLmModelSelector.description": "Paramètres pour l'API du modèle de langage VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Le fournisseur du modèle de langage (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "La famille du modèle de langage (ex: gpt-4)", "settings.customStoragePath.description": "Chemin de stockage personnalisé. Laisser vide pour utiliser l'emplacement par défaut. Prend en charge les chemins absolus (ex: 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "Activer cubent coder Cloud."}