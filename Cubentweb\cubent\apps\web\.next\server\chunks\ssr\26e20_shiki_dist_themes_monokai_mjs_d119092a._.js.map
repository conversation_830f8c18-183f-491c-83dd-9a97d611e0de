{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/monokai.mjs"], "sourcesContent": ["var monokai = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#272822\",\n    \"activityBar.foreground\": \"#f8f8f2\",\n    \"badge.background\": \"#75715E\",\n    \"badge.foreground\": \"#f8f8f2\",\n    \"button.background\": \"#75715E\",\n    \"debugToolBar.background\": \"#1e1f1c\",\n    \"diffEditor.insertedTextBackground\": \"#4b661680\",\n    \"diffEditor.removedTextBackground\": \"#90274A70\",\n    \"dropdown.background\": \"#414339\",\n    \"dropdown.listBackground\": \"#1e1f1c\",\n    \"editor.background\": \"#272822\",\n    \"editor.foreground\": \"#f8f8f2\",\n    \"editor.lineHighlightBackground\": \"#3e3d32\",\n    \"editor.selectionBackground\": \"#878b9180\",\n    \"editor.selectionHighlightBackground\": \"#575b6180\",\n    \"editor.wordHighlightBackground\": \"#4a4a7680\",\n    \"editor.wordHighlightStrongBackground\": \"#6a6a9680\",\n    \"editorCursor.foreground\": \"#f8f8f0\",\n    \"editorGroup.border\": \"#34352f\",\n    \"editorGroup.dropBackground\": \"#41433980\",\n    \"editorGroupHeader.tabsBackground\": \"#1e1f1c\",\n    \"editorHoverWidget.background\": \"#414339\",\n    \"editorHoverWidget.border\": \"#75715E\",\n    \"editorIndentGuide.activeBackground\": \"#767771\",\n    \"editorIndentGuide.background\": \"#464741\",\n    \"editorLineNumber.activeForeground\": \"#c2c2bf\",\n    \"editorLineNumber.foreground\": \"#90908a\",\n    \"editorSuggestWidget.background\": \"#272822\",\n    \"editorSuggestWidget.border\": \"#75715E\",\n    \"editorWhitespace.foreground\": \"#464741\",\n    \"editorWidget.background\": \"#1e1f1c\",\n    \"focusBorder\": \"#99947c\",\n    \"input.background\": \"#414339\",\n    \"inputOption.activeBorder\": \"#75715E\",\n    \"inputValidation.errorBackground\": \"#90274A\",\n    \"inputValidation.errorBorder\": \"#f92672\",\n    \"inputValidation.infoBackground\": \"#546190\",\n    \"inputValidation.infoBorder\": \"#819aff\",\n    \"inputValidation.warningBackground\": \"#848528\",\n    \"inputValidation.warningBorder\": \"#e2e22e\",\n    \"list.activeSelectionBackground\": \"#75715E\",\n    \"list.dropBackground\": \"#414339\",\n    \"list.highlightForeground\": \"#f8f8f2\",\n    \"list.hoverBackground\": \"#3e3d32\",\n    \"list.inactiveSelectionBackground\": \"#414339\",\n    \"menu.background\": \"#1e1f1c\",\n    \"menu.foreground\": \"#cccccc\",\n    \"minimap.selectionHighlight\": \"#878b9180\",\n    \"panel.border\": \"#414339\",\n    \"panelTitle.activeBorder\": \"#75715E\",\n    \"panelTitle.activeForeground\": \"#f8f8f2\",\n    \"panelTitle.inactiveForeground\": \"#75715E\",\n    \"peekView.border\": \"#75715E\",\n    \"peekViewEditor.background\": \"#272822\",\n    \"peekViewEditor.matchHighlightBackground\": \"#75715E\",\n    \"peekViewResult.background\": \"#1e1f1c\",\n    \"peekViewResult.matchHighlightBackground\": \"#75715E\",\n    \"peekViewResult.selectionBackground\": \"#414339\",\n    \"peekViewTitle.background\": \"#1e1f1c\",\n    \"pickerGroup.foreground\": \"#75715E\",\n    \"ports.iconRunningProcessForeground\": \"#ccccc7\",\n    \"progressBar.background\": \"#75715E\",\n    \"quickInputList.focusBackground\": \"#414339\",\n    \"selection.background\": \"#878b9180\",\n    \"settings.focusedRowBackground\": \"#4143395A\",\n    \"sideBar.background\": \"#1e1f1c\",\n    \"sideBarSectionHeader.background\": \"#272822\",\n    \"statusBar.background\": \"#414339\",\n    \"statusBar.debuggingBackground\": \"#75715E\",\n    \"statusBar.noFolderBackground\": \"#414339\",\n    \"statusBarItem.remoteBackground\": \"#AC6218\",\n    \"tab.border\": \"#1e1f1c\",\n    \"tab.inactiveBackground\": \"#34352f\",\n    \"tab.inactiveForeground\": \"#ccccc7\",\n    \"tab.lastPinnedBorder\": \"#414339\",\n    \"terminal.ansiBlack\": \"#333333\",\n    \"terminal.ansiBlue\": \"#6A7EC8\",\n    \"terminal.ansiBrightBlack\": \"#666666\",\n    \"terminal.ansiBrightBlue\": \"#819aff\",\n    \"terminal.ansiBrightCyan\": \"#66D9EF\",\n    \"terminal.ansiBrightGreen\": \"#A6E22E\",\n    \"terminal.ansiBrightMagenta\": \"#AE81FF\",\n    \"terminal.ansiBrightRed\": \"#f92672\",\n    \"terminal.ansiBrightWhite\": \"#f8f8f2\",\n    \"terminal.ansiBrightYellow\": \"#e2e22e\",\n    \"terminal.ansiCyan\": \"#56ADBC\",\n    \"terminal.ansiGreen\": \"#86B42B\",\n    \"terminal.ansiMagenta\": \"#8C6BC8\",\n    \"terminal.ansiRed\": \"#C4265E\",\n    \"terminal.ansiWhite\": \"#e3e3dd\",\n    \"terminal.ansiYellow\": \"#B3B42B\",\n    \"titleBar.activeBackground\": \"#1e1f1c\",\n    \"widget.shadow\": \"#00000098\"\n  },\n  \"displayName\": \"Monokai\",\n  \"name\": \"monokai\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded\",\n        \"source.groovy.embedded\",\n        \"string meta.image.inline.markdown\",\n        \"variable.legacy.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"foreground\": \"#88846f\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#E6DB74\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression\",\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.template.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#AE81FF\"\n      }\n    },\n    {\n      \"scope\": \"constant.language\",\n      \"settings\": {\n        \"foreground\": \"#AE81FF\"\n      }\n    },\n    {\n      \"scope\": \"constant.character, constant.other\",\n      \"settings\": {\n        \"foreground\": \"#AE81FF\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F8F8F2\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": \"storage.type\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#66D9EF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type, entity.name.class, entity.name.namespace, entity.name.scope-resolution\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FD971F\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#66D9EF\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#66D9EF\"\n      }\n    },\n    {\n      \"scope\": \"support.type, support.class\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#66D9EF\"\n      }\n    },\n    {\n      \"scope\": \"support.other.variable\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"invalid\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F44747\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"foreground\": \"#F44747\"\n      }\n    },\n    {\n      \"scope\": \"meta.structure.dictionary.json string.quoted.double.json\",\n      \"settings\": {\n        \"foreground\": \"#CFCFC2\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff, meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#75715E\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"foreground\": \"#E6DB74\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric.line-number.find-in-files - match\",\n      \"settings\": {\n        \"foreground\": \"#AE81FFA0\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.filename.find-in-files\",\n      \"settings\": {\n        \"foreground\": \"#E6DB74\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#F92672\"\n      }\n    },\n    {\n      \"scope\": \"markup.list\",\n      \"settings\": {\n        \"foreground\": \"#E6DB74\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold, markup.italic\",\n      \"settings\": {\n        \"foreground\": \"#66D9EF\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#FD971F\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.setext\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#75715E\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown,string.other.link.description.markdown\",\n      \"settings\": {\n        \"foreground\": \"#AE81FF\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link.markdown,markup.underline.link.image.markdown\",\n      \"settings\": {\n        \"foreground\": \"#E6DB74\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.list.unnumbered.markdown, markup.list.numbered.markdown\",\n      \"settings\": {\n        \"foreground\": \"#f8f8f2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#A6E22E\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#6796e6\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#cd9731\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#f44747\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#b267e6\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#FD971F\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { monokai as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,2BAA2B;QAC3B,sBAAsB;QACtB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,qCAAqC;QACrC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,eAAe;QACf,oBAAoB;QACpB,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,kCAAkC;QAClC,uBAAuB;QACvB,4BAA4B;QAC5B,wBAAwB;QACxB,oCAAoC;QACpC,mBAAmB;QACnB,mBAAmB;QACnB,8BAA8B;QAC9B,gBAAgB;QAChB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,0BAA0B;QAC1B,sCAAsC;QACtC,0BAA0B;QAC1B,kCAAkC;QAClC,wBAAwB;QACxB,iCAAiC;QACjC,sBAAsB;QACtB,mCAAmC;QACnC,wBAAwB;QACxB,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}