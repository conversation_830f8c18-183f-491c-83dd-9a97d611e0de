(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2731],{29011:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(6024);r(50628);var n=r(89840),a=r(81197),i=r(31918);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,asChild:a=!1,...c}=e,d=a?n.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),t),...c})}},31918:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a}),r(63410);var s=r(49973);r(13957);var n=r(22928);let a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},45707:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(50628);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:l="",children:u,iconNode:v,...g}=e;return(0,s.createElement)("svg",{ref:t,...d,width:n,height:n,stroke:r,strokeWidth:i?24*Number(a)/Number(n):a,className:o("lucide",l),...!u&&!c(g)&&{"aria-hidden":"true"},...g},[...v.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:c,...d}=r;return(0,s.createElement)(l,{ref:a,iconNode:t,className:o("lucide-".concat(n(i(e))),"lucide-".concat(e),c),...d})});return r.displayName=i(e),r}},67205:(e,t,r)=>{Promise.resolve().then(r.bind(r,77485))},69680:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(6024);r(50628);var n=r(31918);function a(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},70234:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(6024);r(50628);var n=r(89840),a=r(81197),i=r(31918);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:a,asChild:c=!1,...d}=e,l=c?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:a,className:t})),...d})}},70645:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(45707).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},71499:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(45707).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},77485:(e,t,r)=>{"use strict";r.d(t,{TermsAcceptance:()=>u});var s=r(6024),n=r(70234),a=r(69680),i=r(29011),o=r(50628),c=r(13957),d=r(71499),l=r(70645);function u(e){let{userId:t,termsAccepted:r,termsAcceptedAt:u}=e,[v,g]=(0,o.useState)(!1),p=async()=>{g(!0);try{(await fetch("/api/terms/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t})})).ok?(c.toast.success("Terms accepted successfully!"),setTimeout(()=>{let e="vscode://cubent.cubent/connect?website=".concat(encodeURIComponent(window.location.origin),"&termsAccepted=true");window.open(e,"_blank"),c.toast.success("Opening VS Code extension...")},1e3),setTimeout(()=>{window.location.href="/profile"},3e3)):c.toast.error("Failed to accept terms")}catch(e){c.toast.error("Failed to accept terms")}finally{g(!1)}};return(0,s.jsxs)(a.Zp,{className:r?"border-green-200 bg-green-50 dark:bg-green-950":"border-orange-200 bg-orange-50 dark:bg-orange-950",children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r?(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-600"}):(0,s.jsx)(l.A,{className:"h-5 w-5 text-orange-600"}),(0,s.jsx)(a.ZB,{children:r?"Terms Accepted":"Accept Terms to Continue"})]}),(0,s.jsx)(a.BT,{children:r?"You have accepted the terms of service and can use the extension.":"You must accept these terms to connect your VS Code extension."})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Status:"}),(0,s.jsx)(i.E,{variant:r?"default":"secondary",children:r?"Accepted":"Pending"})]}),u&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Accepted on:"}),(0,s.jsx)("span",{className:"text-sm",children:new Date(u).toLocaleDateString()})]}),!r&&(0,s.jsxs)("div",{className:"pt-4",children:[(0,s.jsx)(n.$,{onClick:p,disabled:v,className:"w-full",size:"lg",children:v?"Accepting Terms...":"Accept Terms and Connect Extension"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2 text-center",children:"By clicking this button, you agree to the terms of service above and authorize the connection between your VS Code extension and this website."})]}),r&&(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(n.$,{variant:"outline",className:"w-full",onClick:()=>window.location.href="/profile",children:"Go to Profile"})})]})]})}},81197:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(49973);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,c=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let a=n(t)||n(s);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return a(e,c,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,2913,4499,7358],()=>t(67205)),_N_E=e.O()}]);