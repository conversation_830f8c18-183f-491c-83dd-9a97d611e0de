{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/shellscript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Shell\", \"name\": \"shellscript\", \"patterns\": [{ \"include\": \"#initial_context\" }], \"repository\": { \"alias_statement\": { \"begin\": \"(?:(?:[ \\\\t]*+)(alias)(?:[ \\\\t]*+)((?:(?:((?<!\\\\w)-\\\\w+\\\\b)(?:[ \\\\t]*+))*))(?:(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.alias.shell\" }, \"2\": { \"patterns\": [{ \"match\": \"(?<!\\\\w)-\\\\w+\\\\b\", \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }] }, \"3\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }, \"4\": { \"name\": \"variable.other.assignment.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"variable.other.assignment.shell\" }, \"7\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"8\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"9\": { \"name\": \"keyword.operator.assignment.shell\" }, \"10\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"11\": { \"name\": \"keyword.operator.assignment.compound.shell\" } }, \"end\": \"(?:(?= |\\\\t|$)|(?:(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&)))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.semicolon.shell\" }, \"2\": { \"name\": \"punctuation.separator.statement.and.shell\" }, \"3\": { \"name\": \"punctuation.separator.statement.or.shell\" }, \"4\": { \"name\": \"punctuation.separator.statement.background.shell\" } }, \"name\": \"meta.expression.assignment.alias.shell\", \"patterns\": [{ \"include\": \"#normal_context\" }] }, \"argument\": { \"begin\": \"(?:[ \\\\t]++)(?!(?:&|\\\\||\\\\(|\\\\[|#|\\\\n|$|;))\", \"beginCaptures\": {}, \"end\": \"(?= |\\\\t|;|\\\\||&|$|\\\\n|\\\\)|\\\\`)\", \"endCaptures\": {}, \"name\": \"meta.argument.shell\", \"patterns\": [{ \"include\": \"#argument_context\" }, { \"include\": \"#line_continuation\" }] }, \"argument_context\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.argument.shell\", \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"variable.language.special.wildcard.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#numeric_literal\" }, { \"captures\": { \"1\": { \"name\": \"constant.language.$1.shell\" } }, \"match\": \"(?<!\\\\w)(\\\\b(?:true|false)\\\\b)(?!\\\\w)\" }] } }, \"match\": \"(?:[ \\\\t]*+)((?:[^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]+)(?!>))\" }, { \"include\": \"#normal_context\" }] }, \"arithmetic_double\": { \"patterns\": [{ \"begin\": \"\\\\(\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.double.shell\" } }, \"end\": \"\\\\)(?:\\\\s*)\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.double.shell\" } }, \"name\": \"meta.arithmetic.shell\", \"patterns\": [{ \"include\": \"#math\" }, { \"include\": \"#string\" }] }] }, \"arithmetic_no_dollar\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.single.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.single.shell\" } }, \"name\": \"meta.arithmetic.shell\", \"patterns\": [{ \"include\": \"#math\" }, { \"include\": \"#string\" }] }] }, \"array_access_inline\": { \"captures\": { \"1\": { \"name\": \"punctuation.section.array.shell\" }, \"2\": { \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }] }, \"3\": { \"name\": \"punctuation.section.array.shell\" } }, \"match\": \"(?:(\\\\[)([^\\\\[\\\\]]+)(\\\\]))\" }, \"array_value\": { \"begin\": \"(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))(?:[ \\\\t]*+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"9\": { \"name\": \"punctuation.definition.array.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.shell\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.assignment.array.shell entity.other.attribute-name.shell\" }, \"2\": { \"name\": \"keyword.operator.assignment.shell punctuation.definition.assignment.shell\" } }, \"match\": \"(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(=))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.bracket.named-array.shell\" }, \"2\": { \"name\": \"string.unquoted.shell entity.other.attribute-name.bracket.shell\" }, \"3\": { \"name\": \"punctuation.definition.bracket.named-array.shell\" }, \"4\": { \"name\": \"punctuation.definition.assignment.shell\" } }, \"match\": \"(?:(\\\\[)(.+?)(\\\\])(=))\" }, { \"include\": \"#normal_context\" }, { \"include\": \"#simple_unquoted\" }] }, \"assignment_statement\": { \"patterns\": [{ \"include\": \"#array_value\" }, { \"include\": \"#modified_assignment_statement\" }, { \"include\": \"#normal_assignment_statement\" }] }, \"basic_command_name\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.shell\" }, \"2\": { \"name\": \"entity.name.function.call.shell entity.name.command.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\", \"name\": \"keyword.control.$0.shell\" }, { \"match\": \"(?<!\\\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\\\.)(?!\\\\/))(?!\\\\w)(?!-)\", \"name\": \"support.function.builtin.shell\" }, { \"include\": \"#variable\" }] } }, \"match\": `(?:(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$))(?:((?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$))|((?!\"|'|\\\\\\\\\\\\n?$)(?:[^!'\"<> \\\\t\\\\n\\\\r]+?)))(?:(?= |\\\\t)|(?:(?=;|\\\\||&|\\\\n|\\\\)|\\\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\))))`, \"name\": \"meta.statement.command.name.basic.shell\" }, \"block_comment\": { \"begin\": \"(?:(?:\\\\s*+)(\\\\/\\\\*))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.begin.shell\" } }, \"end\": \"\\\\*\\\\/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.shell\" } }, \"name\": \"comment.block.shell\" }, \"boolean\": { \"match\": \"\\\\b(?:true|false)\\\\b\", \"name\": \"constant.language.$0.shell\" }, \"case_statement\": { \"begin\": \"(?:(\\\\bcase\\\\b)(?:[ \\\\t]*+)(.+?)(?:[ \\\\t]*+)(\\\\bin\\\\b))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.case.shell\" }, \"2\": { \"patterns\": [{ \"include\": \"#initial_context\" }] }, \"3\": { \"name\": \"keyword.control.in.shell\" } }, \"end\": \"\\\\besac\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.esac.shell\" } }, \"name\": \"meta.case.shell\", \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.pattern.case.default.shell\" } }, \"match\": \"(?:[ \\\\t]*+)(\\\\* *\\\\))\" }, { \"begin\": \"(?<!\\\\))(?!(?:[ \\\\t]*+)(?:esac\\\\b|$))\", \"beginCaptures\": {}, \"end\": \"(?:(?=\\\\besac\\\\b)|(\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.pattern.case.shell\" } }, \"name\": \"meta.case.entry.pattern.shell\", \"patterns\": [{ \"include\": \"#case_statement_context\" }] }, { \"begin\": \"(?<=\\\\))\", \"beginCaptures\": {}, \"end\": \"(?:(;;)|(?=\\\\besac\\\\b))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.case.shell\" } }, \"name\": \"meta.case.entry.body.shell\", \"patterns\": [{ \"include\": \"#typical_statements\" }, { \"include\": \"#initial_context\" }] }] }, \"case_statement_context\": { \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"\\\\+\", \"name\": \"variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"\\\\?\", \"name\": \"variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"@\", \"name\": \"variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }, { \"match\": \"(?<=\\\\tin| in| |\\\\t|;;)\\\\(\", \"name\": \"keyword.operator.pattern.case.shell\" }, { \"begin\": \"(?<=\\\\S)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell punctuation.definition.regex.group.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.definition.regex.group.shell\" } }, \"name\": \"meta.parenthese.shell\", \"patterns\": [{ \"include\": \"#case_statement_context\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.shell\" } }, \"name\": \"string.regexp.character-class.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }] }, { \"include\": \"#string\" }, { \"match\": \"[^) \\\\t\\\\n\\\\[?\\\\*\\\\|\\\\@]\", \"name\": \"string.unquoted.pattern.shell string.regexp.unquoted.shell\" }] }, \"command_name_range\": { \"begin\": \"\\\\G\", \"beginCaptures\": {}, \"end\": \"(?:(?= |\\\\t|;|\\\\||&|$|\\\\n|\\\\)|\\\\`)|(?=<))\", \"endCaptures\": {}, \"name\": \"meta.statement.command.name.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\", \"name\": \"entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell\" }, { \"match\": \"(?<!\\\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\\\.)(?!\\\\/))(?!\\\\w)(?!-)\", \"name\": \"entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell\" }, { \"include\": \"#variable\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.call.shell entity.name.command.shell\" } }, \"match\": `(?:(?<!\\\\w)(?<=\\\\G|'|\"|\\\\}|\\\\))([^ \\\\n\\\\t\\\\r\"'=;&\\\\|\\`){<>]+))` }, { \"begin\": `(?:(?:\\\\G|(?<! |\\\\t|;|\\\\||&|\\\\n|\\\\{|#))(?:(\\\\$?)((?:(\")|(')))))`, \"beginCaptures\": { \"1\": { \"name\": \"meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell\" }, \"2\": {}, \"3\": { \"name\": \"meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\" }, \"4\": { \"name\": \"meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\" } }, \"end\": \"(?<!\\\\G)(?<=(?:\\\\2))\", \"endCaptures\": {}, \"patterns\": [{ \"include\": \"#continuation_of_single_quoted_command_name\" }, { \"include\": \"#continuation_of_double_quoted_command_name\" }] }, { \"include\": \"#line_continuation\" }, { \"include\": \"#simple_unquoted\" }] }, \"command_statement\": { \"begin\": \"(?:(?:[ \\\\t]*+)(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\\\\\\\n?$)))\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.command.shell\", \"patterns\": [{ \"include\": \"#command_name_range\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#option\" }, { \"include\": \"#argument\" }, { \"include\": \"#string\" }, { \"include\": \"#heredoc\" }] }, \"comment\": { \"captures\": { \"1\": { \"name\": \"comment.line.number-sign.shell meta.shebang.shell\" }, \"2\": { \"name\": \"punctuation.definition.comment.shebang.shell\" }, \"3\": { \"name\": \"comment.line.number-sign.shell\" }, \"4\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"match\": \"(?:(?:^|(?:[ \\\\t]++))(?:((?:(#!)(?:.*)))|((?:(#)(?:.*)))))\" }, \"comments\": { \"patterns\": [{ \"include\": \"#block_comment\" }, { \"include\": \"#line_comment\" }] }, \"compound-command\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical-expression\" }, { \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=\\\\s|^){(?=\\\\s|$)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell\" } }, \"end\": \"(?<=^|;)\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell\" } }, \"name\": \"meta.scope.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"continuation_of_double_quoted_command_name\": { \"begin\": '(?:\\\\G(?<=\"))', \"beginCaptures\": {}, \"contentName\": \"meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\" } }, \"patterns\": [{ \"match\": '\\\\\\\\[$\\\\n`\"\\\\\\\\]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, \"continuation_of_single_quoted_command_name\": { \"begin\": \"(?:\\\\G(?<='))\", \"beginCaptures\": {}, \"contentName\": \"meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\" } } }, \"custom_command_names\": { \"patterns\": [] }, \"custom_commands\": { \"patterns\": [] }, \"double_quote_context\": { \"patterns\": [{ \"match\": '\\\\\\\\[$`\"\\\\\\\\\\\\n]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, \"double_quote_escape_char\": { \"match\": '\\\\\\\\[$`\"\\\\\\\\\\\\n]', \"name\": \"constant.character.escape.shell\" }, \"floating_keyword\": { \"patterns\": [{ \"match\": \"(?<=^|;|&| |\\\\t)(?:then|elif|else|done|end|do|if|fi)(?= |\\\\t|;|&|$)\", \"name\": \"keyword.control.$0.shell\" }] }, \"for_statement\": { \"patterns\": [{ \"begin\": \"(?:(\\\\bfor\\\\b)(?:(?:[ \\\\t]*+)((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:[ \\\\t]*+)(\\\\bin\\\\b)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.shell\" }, \"2\": { \"name\": \"variable.other.for.shell\" }, \"3\": { \"name\": \"keyword.control.in.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.for.in.shell\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }] }, { \"begin\": \"(\\\\bfor\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.for.shell\", \"patterns\": [{ \"include\": \"#arithmetic_double\" }, { \"include\": \"#normal_context\" }] }] }, \"function_definition\": { \"applyEndPatternLast\": 1, \"begin\": `(?:[ \\\\t]*+)(?:(?:(\\\\bfunction\\\\b)(?:[ \\\\t]*+)([^ \\\\t\\\\n\\\\r()=\"']+)(?:(?:(\\\\()(?:[ \\\\t]*+)(\\\\)))?))|(?:([^ \\\\t\\\\n\\\\r()=\"']+)(?:[ \\\\t]*+)(\\\\()(?:[ \\\\t]*+)(\\\\))))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.shell\" }, \"2\": { \"name\": \"entity.name.function.shell\" }, \"3\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"4\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"5\": { \"name\": \"entity.name.function.shell\" }, \"6\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"7\": { \"name\": \"punctuation.definition.arguments.shell\" } }, \"end\": \"(?<=\\\\}|\\\\))\", \"endCaptures\": {}, \"name\": \"meta.function.shell\", \"patterns\": [{ \"match\": \"(?:\\\\G(?:\\\\t| |\\\\n))\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"name\": \"meta.function.body.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"name\": \"meta.function.body.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"include\": \"#initial_context\" }] }, \"heredoc\": { \"patterns\": [{ \"begin\": `(?:((?<!<)(?:<<-))(?:[ \\\\t]*+)(\"|')(?:[ \\\\t]*+)([^\"']+?)(?=\\\\s|;|&|<|\"|')((?:\\\\2))(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"4\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"5\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.quoted.heredoc.indent.$3\", \"end\": \"(?:(?:^\\\\t*)(?:\\\\3)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.$0.shell\" } }, \"patterns\": [] }, { \"begin\": `(?:((?<!<)(?:<<)(?!<))(?:[ \\\\t]*+)(\"|')(?:[ \\\\t]*+)([^\"']+?)(?=\\\\s|;|&|<|\"|')((?:\\\\2))(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"4\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"5\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.quoted.heredoc.no-indent.$3\", \"end\": \"(?:^(?:\\\\3)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [] }, { \"begin\": `(?:((?<!<)(?:<<-))(?:[ \\\\t]*+)([^\"' \\\\t]+)(?=\\\\s|;|&|<|\"|')(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"3\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.unquoted.heredoc.indent.$2\", \"end\": \"(?:(?:^\\\\t*)(?:\\\\2)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [{ \"include\": \"#double_quote_escape_char\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": `(?:((?<!<)(?:<<)(?!<))(?:[ \\\\t]*+)([^\"' \\\\t]+)(?=\\\\s|;|&|<|\"|')(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"3\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.unquoted.heredoc.no-indent.$2\", \"end\": \"(?:^(?:\\\\2)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [{ \"include\": \"#double_quote_escape_char\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }] }, \"herestring\": { \"patterns\": [{ \"begin\": \"(<<<)\\\\s*(('))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.quoted.single.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"contentName\": \"string.quoted.single.shell\", \"end\": \"(')\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.single.shell\" }, \"1\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"meta.herestring.shell\" }, { \"begin\": '(<<<)\\\\s*((\"))', \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.quoted.double.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"contentName\": \"string.quoted.double.shell\", \"end\": '(\")', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.shell\" }, \"1\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"meta.herestring.shell\", \"patterns\": [{ \"include\": \"#double_quote_context\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.unquoted.herestring.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] } }, \"match\": \"(<<<)\\\\s*(([^\\\\s)\\\\\\\\]|\\\\\\\\.)+)\", \"name\": \"meta.herestring.shell\" }] }, \"initial_context\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#logical_expression_single\" }, { \"include\": \"#assignment_statement\" }, { \"include\": \"#case_statement\" }, { \"include\": \"#for_statement\" }, { \"include\": \"#loop\" }, { \"include\": \"#function_definition\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#arithmetic_double\" }, { \"include\": \"#misc_ranges\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#alias_statement\" }, { \"include\": \"#normal_statement\" }, { \"include\": \"#string\" }, { \"include\": \"#support\" }] }, \"inline_comment\": { \"captures\": { \"1\": { \"name\": \"comment.block.shell punctuation.definition.comment.begin.shell\" }, \"2\": { \"name\": \"comment.block.shell\" }, \"3\": { \"patterns\": [{ \"match\": \"\\\\*\\\\/\", \"name\": \"comment.block.shell punctuation.definition.comment.end.shell\" }, { \"match\": \"\\\\*\", \"name\": \"comment.block.shell\" }] } }, \"match\": \"(\\\\/\\\\*)((?:(?:[^\\\\*]|(?:(?:\\\\*++)[^\\\\/]))*+)((?:(?:\\\\*++)\\\\/)))\" }, \"interpolation\": { \"patterns\": [{ \"include\": \"#arithmetic_dollar\" }, { \"include\": \"#subshell_dollar\" }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.evaluation.backticks.shell\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.evaluation.backticks.shell\" } }, \"name\": \"string.interpolated.backtick.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\[`\\\\\\\\$]\", \"name\": \"constant.character.escape.shell\" }, { \"begin\": \"(?<=\\\\W)(?=#)(?!#{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.shell\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"end\": \"(?=`)\", \"name\": \"comment.line.number-sign.shell\" }] }, { \"include\": \"#initial_context\" }] }] }, \"keyword\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=\\\\s|;|&|$)\", \"name\": \"keyword.control.shell\" }, { \"match\": \"(?<=^|;|&|\\\\s)(?:export|declare|typeset|local|readonly)(?=\\\\s|;|&|$)\", \"name\": \"storage.modifier.shell\" }] }, \"line_comment\": { \"begin\": \"(?:\\\\s*+)(\\\\/\\\\/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"end\": \"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\", \"endCaptures\": {}, \"name\": \"comment.line.double-slash.shell\", \"patterns\": [{ \"include\": \"#line_continuation_character\" }] }, \"line_continuation\": { \"match\": \"\\\\\\\\(?=\\\\n)\", \"name\": \"constant.character.escape.line-continuation.shell\" }, \"logical-expression\": { \"patterns\": [{ \"include\": \"#arithmetic_no_dollar\" }, { \"comment\": \"do we want a special rule for ( expr )?\", \"match\": \"=[=~]?|!=?|<|>|&&|\\\\|\\\\|\", \"name\": \"keyword.operator.logical.shell\" }, { \"match\": \"(?<!\\\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])\\\\b\", \"name\": \"keyword.operator.logical.shell\" }] }, \"logical_expression_context\": { \"patterns\": [{ \"include\": \"#regex_comparison\" }, { \"include\": \"#arithmetic_no_dollar\" }, { \"include\": \"#logical-expression\" }, { \"include\": \"#logical_expression_single\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#comment\" }, { \"include\": \"#boolean\" }, { \"include\": \"#redirect_number\" }, { \"include\": \"#numeric_literal\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }] }, \"logical_expression_double\": { \"begin\": \"\\\\[\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical_expression_context\" }] }, \"logical_expression_single\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical_expression_context\" }] }, \"loop\": { \"patterns\": [{ \"begin\": \"(?<=^|;|&|\\\\s)(for)\\\\s+(.+?)\\\\s+(in)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" }, \"2\": { \"name\": \"variable.other.loop.shell\", \"patterns\": [{ \"include\": \"#string\" }] }, \"3\": { \"name\": \"keyword.control.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)done(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.for-in-loop.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)(while|until)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)done(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.while-loop.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)(select)\\\\s+((?:[^\\\\s\\\\\\\\]|\\\\\\\\.)+)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" }, \"2\": { \"name\": \"variable.other.loop.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)(done)(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.select-block.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)if(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.if.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)fi(?=\\\\s|;|&|$)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.fi.shell\" } }, \"name\": \"meta.scope.if-block.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"math\": { \"patterns\": [{ \"include\": \"#variable\" }, { \"match\": \"\\\\+{1,2}|-{1,2}|!|~|\\\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\\\|{1,2}|&{1,2}|\\\\?|:|,|=|[*/%+\\\\-&^|]=|<<=|>>=\", \"name\": \"keyword.operator.arithmetic.shell\" }, { \"match\": \"0[xX][0-9A-Fa-f]+\", \"name\": \"constant.numeric.hex.shell\" }, { \"match\": \";\", \"name\": \"punctuation.separator.semicolon.range\" }, { \"match\": \"0\\\\d+\", \"name\": \"constant.numeric.octal.shell\" }, { \"match\": \"\\\\d{1,2}#[0-9a-zA-Z@_]+\", \"name\": \"constant.numeric.other.shell\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.shell\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }] }, \"math_operators\": { \"patterns\": [{ \"match\": \"\\\\+{1,2}|-{1,2}|!|~|\\\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\\\|{1,2}|&{1,2}|\\\\?|:|,|=|[*/%+\\\\-&^|]=|<<=|>>=\", \"name\": \"keyword.operator.arithmetic.shell\" }, { \"match\": \"0[xX][0-9A-Fa-f]+\", \"name\": \"constant.numeric.hex.shell\" }, { \"match\": \"0\\\\d+\", \"name\": \"constant.numeric.octal.shell\" }, { \"match\": \"\\\\d{1,2}#[0-9a-zA-Z@_]+\", \"name\": \"constant.numeric.other.shell\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.shell\" }] }, \"misc_ranges\": { \"patterns\": [{ \"include\": \"#logical_expression_single\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#subshell_dollar\" }, { \"begin\": \"(?<![^ \\\\t])({)(?!\\\\w|\\\\$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell\" } }, \"name\": \"meta.scope.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"modified_assignment_statement\": { \"begin\": \"(?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$)\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.$0.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.shell meta.expression.assignment.modified.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)-\\\\w+\\\\b\", \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }, { \"include\": \"#array_value\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"9\": { \"name\": \"constant.numeric.shell constant.numeric.hex.shell\" }, \"10\": { \"name\": \"constant.numeric.shell constant.numeric.octal.shell\" }, \"11\": { \"name\": \"constant.numeric.shell constant.numeric.other.shell\" }, \"12\": { \"name\": \"constant.numeric.shell constant.numeric.decimal.shell\" }, \"13\": { \"name\": \"constant.numeric.shell constant.numeric.version.shell\" }, \"14\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" } }, \"match\": \"(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?)(?:(?:(?:(=)|(\\\\+=))|(-=))?)(?:(?:(?<==| |\\\\t|^|\\\\{|\\\\(|\\\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\\\d+))|(\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\d+(?:\\\\.\\\\d+)))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?= |\\\\t|$|\\\\}|\\\\)|;))?))\" }, { \"include\": \"#normal_context\" }] }, \"modifiers\": { \"match\": \"(?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$)\", \"name\": \"storage.modifier.$0.shell\" }, \"normal_assignment_statement\": { \"begin\": \"(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.expression.assignment.shell\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#normal_assignment_statement\" }, { \"begin\": \"(?<= |\\\\t)(?! |\\\\t|\\\\w+=)\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.command.env.shell\", \"patterns\": [{ \"include\": \"#command_name_range\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#option\" }, { \"include\": \"#argument\" }, { \"include\": \"#string\" }] }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }] }, \"normal_context\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#misc_ranges\" }, { \"include\": \"#boolean\" }, { \"include\": \"#redirect_number\" }, { \"include\": \"#numeric_literal\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }, { \"include\": \"#parenthese\" }] }, \"normal_statement\": { \"begin\": \"(?:(?!^[ \\\\t]*+$)(?:(?<=^until | until |\\\\tuntil |^while | while |\\\\twhile |^elif | elif |\\\\telif |^else | else |\\\\telse |^then | then |\\\\tthen |^do | do |\\\\tdo |^if | if |\\\\tif )|(?<=(?:^|;|\\\\||&|!|\\\\(|\\\\{|\\\\`)))(?:[ \\\\t]*+)(?!nocorrect\\\\W|nocorrect\\\\$|function\\\\W|function\\\\$|foreach\\\\W|foreach\\\\$|repeat\\\\W|repeat\\\\$|logout\\\\W|logout\\\\$|coproc\\\\W|coproc\\\\$|select\\\\W|select\\\\$|while\\\\W|while\\\\$|pushd\\\\W|pushd\\\\$|until\\\\W|until\\\\$|case\\\\W|case\\\\$|done\\\\W|done\\\\$|elif\\\\W|elif\\\\$|else\\\\W|else\\\\$|esac\\\\W|esac\\\\$|popd\\\\W|popd\\\\$|then\\\\W|then\\\\$|time\\\\W|time\\\\$|for\\\\W|for\\\\$|end\\\\W|end\\\\$|fi\\\\W|fi\\\\$|do\\\\W|do\\\\$|in\\\\W|in\\\\$|if\\\\W|if\\\\$))\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.shell\", \"patterns\": [{ \"include\": \"#typical_statements\" }] }, \"normal_statement_seperator\": { \"captures\": { \"1\": { \"name\": \"punctuation.terminator.statement.semicolon.shell\" }, \"2\": { \"name\": \"punctuation.separator.statement.and.shell\" }, \"3\": { \"name\": \"punctuation.separator.statement.or.shell\" }, \"4\": { \"name\": \"punctuation.separator.statement.background.shell\" } }, \"match\": \"(?:(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&))\" }, \"numeric_literal\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.shell constant.numeric.hex.shell\" }, \"2\": { \"name\": \"constant.numeric.shell constant.numeric.octal.shell\" }, \"3\": { \"name\": \"constant.numeric.shell constant.numeric.other.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.decimal.shell\" }, \"5\": { \"name\": \"constant.numeric.shell constant.numeric.version.shell\" }, \"6\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" } }, \"match\": \"(?<==| |\\\\t|^|\\\\{|\\\\(|\\\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\\\d+))|(\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\d+(?:\\\\.\\\\d+)))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?= |\\\\t|$|\\\\}|\\\\)|;)\" }, \"option\": { \"begin\": \"(?:(?:[ \\\\t]++)(-)((?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))))\", \"beginCaptures\": { \"1\": { \"name\": \"string.unquoted.argument.shell constant.other.option.dash.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" } }, \"contentName\": \"string.unquoted.argument constant.other.option\", \"end\": \"(?:(?=[ \\\\t])|(?:(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)))\", \"endCaptures\": {}, \"patterns\": [{ \"include\": \"#option_context\" }] }, \"option_context\": { \"patterns\": [{ \"include\": \"#misc_ranges\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }] }, \"parenthese\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parenthese.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parenthese.shell\" } }, \"name\": \"meta.parenthese.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"pathname\": { \"patterns\": [{ \"match\": \"(?<=\\\\s|:|=|^)~\", \"name\": \"keyword.operator.tilde.shell\" }, { \"match\": \"\\\\*|\\\\?\", \"name\": \"keyword.operator.glob.shell\" }, { \"begin\": \"([?*+@!])(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.extglob.shell\" }, \"2\": { \"name\": \"punctuation.definition.extglob.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.extglob.shell\" } }, \"name\": \"meta.structure.extglob.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"pipeline\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(time)(?=\\\\s|;|&|$)\", \"name\": \"keyword.other.shell\" }, { \"match\": \"[|!]\", \"name\": \"keyword.operator.pipe.shell\" }] }, \"redirect_fix\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.redirect.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell\" } }, \"match\": \"(?:(>>?)(?:[ \\\\t]*+)([^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]+))\" }, \"redirect_number\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.redirect.stdout.shell\" }, \"2\": { \"name\": \"keyword.operator.redirect.stderr.shell\" }, \"3\": { \"name\": \"keyword.operator.redirect.$3.shell\" } }, \"match\": \"(?<=[ \\\\t])(?:(?:(1)|(2)|(\\\\d+))(?=>))\" }, \"redirection\": { \"patterns\": [{ \"begin\": \"[><]\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.interpolated.process-substitution.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"match\": \"(?<![<>])(&>|\\\\d*>&\\\\d*|\\\\d*(>>|>|<)|\\\\d*<&|\\\\d*<>)(?![<>])\", \"name\": \"keyword.operator.redirect.shell\" }] }, \"regex_comparison\": { \"match\": \"=~\", \"name\": \"keyword.operator.logical.regex.shell\" }, \"regexp\": { \"patterns\": [{ \"match\": \"(?:.+)\" }] }, \"simple_options\": { \"captures\": { \"0\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.argument.shell constant.other.option.dash.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" } }, \"match\": \"(?:[ \\\\t]++)(-)(\\\\w+)\" }] } }, \"match\": \"(?:(?:[ \\\\t]++)-(?:\\\\w+))*\" }, \"simple_unquoted\": { \"match\": \"[^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]\", \"name\": \"string.unquoted.shell\" }, \"special_expansion\": { \"match\": \"!|:[-=?]?|\\\\*|@|##|#|%%|%|\\\\/\", \"name\": \"keyword.operator.expansion.shell\" }, \"start_of_command\": { \"match\": \"(?:(?:[ \\\\t]*+)(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\\\\\\\n?$)))\" }, \"string\": { \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.single.shell\" }, { \"begin\": '\\\\$?\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.double.shell\", \"patterns\": [{ \"match\": '\\\\\\\\[$\\\\n`\"\\\\\\\\]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"\\\\$'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.single.dollar.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:a|b|e|f|n|r|t|v|\\\\\\\\|')\", \"name\": \"constant.character.escape.ansi-c.shell\" }, { \"match\": '\\\\\\\\\\\\d{3}\"', \"name\": \"constant.character.escape.octal.shell\" }, { \"match\": '\\\\\\\\x[0-9a-fA-F]{2}\"', \"name\": \"constant.character.escape.hex.shell\" }, { \"match\": '\\\\\\\\c.\"', \"name\": \"constant.character.escape.control-char.shell\" }] }] }, \"subshell_dollar\": { \"patterns\": [{ \"begin\": \"(?:\\\\$\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.subshell.single.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.subshell.single.shell\" } }, \"name\": \"meta.scope.subshell\", \"patterns\": [{ \"include\": \"#parenthese\" }, { \"include\": \"#initial_context\" }] }] }, \"support\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(?::|\\\\.)(?=\\\\s|;|&|$)\", \"name\": \"support.function.builtin.shell\" }] }, \"typical_statements\": { \"patterns\": [{ \"include\": \"#assignment_statement\" }, { \"include\": \"#case_statement\" }, { \"include\": \"#for_statement\" }, { \"include\": \"#while_statement\" }, { \"include\": \"#function_definition\" }, { \"include\": \"#command_statement\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#arithmetic_double\" }, { \"include\": \"#normal_context\" }] }, \"variable\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.all.shell\" }, \"2\": { \"name\": \"variable.parameter.positional.all.shell\" } }, \"match\": \"(?:(\\\\$)(\\\\@(?!\\\\w)))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.shell\" }, \"2\": { \"name\": \"variable.parameter.positional.shell\" } }, \"match\": \"(?:(\\\\$)(\\\\d(?!\\\\w)))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.language.special.shell\" }, \"2\": { \"name\": \"variable.language.special.shell\" } }, \"match\": \"(?:(\\\\$)([-*#?$!0_](?!\\\\w)))\" }, { \"begin\": \"(?:(\\\\$)(\\\\{)(?:[ \\\\t]*+)(?=\\\\d))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.shell\" }, \"2\": { \"name\": \"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell\" } }, \"contentName\": \"meta.parameter-expansion\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell\" } }, \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#array_access_inline\" }, { \"match\": \"\\\\d+\", \"name\": \"variable.parameter.positional.shell\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?:(\\\\$)(\\\\{))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell\" }, \"2\": { \"name\": \"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell\" } }, \"contentName\": \"meta.parameter-expansion\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell\" } }, \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#array_access_inline\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#string\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.other.normal.shell\" }, \"2\": { \"name\": \"variable.other.normal.shell\" } }, \"match\": \"(?:(\\\\$)((?:\\\\w+)(?!\\\\w)))\" }] }, \"while_statement\": { \"patterns\": [{ \"begin\": \"(\\\\bwhile\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.while.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.while.shell\", \"patterns\": [{ \"include\": \"#line_continuation\" }, { \"include\": \"#math_operators\" }, { \"include\": \"#option\" }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }, { \"include\": \"#string\" }] }] } }, \"scopeName\": \"source.shell\", \"aliases\": [\"bash\", \"sh\", \"shell\", \"zsh\"] });\nvar shellscript = [\n  lang\n];\n\nexport { shellscript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAe,YAAY;QAAC;YAAE,WAAW;QAAmB;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,SAAS;YAA2P,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAA6D;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,MAAM;oBAAE,QAAQ;gBAA6C;gBAAG,MAAM;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAqD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA+C,iBAAiB,CAAC;YAAG,OAAO;YAAmC,eAAe,CAAC;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,SAAS;oCAAO,QAAQ;gCAA2C;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAmB;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6B;oCAAE;oCAAG,SAAS;gCAAwC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAyD;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAkB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAA6B;QAAG,eAAe;YAAE,SAAS;YAA4L,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0E;wBAAG,KAAK;4BAAE,QAAQ;wBAA4E;oBAAE;oBAAG,SAAS;gBAA6C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkE;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAyB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;oBAA6D,YAAY;wBAAC;4BAAE,SAAS;4BAA4C,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAA2W,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,SAAS,CAAC,qyBAAqyB,CAAC;YAAE,QAAQ;QAA0C;QAAG,iBAAiB;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;QAAsB;QAAG,WAAW;YAAE,SAAS;YAAwB,QAAQ;QAA6B;QAAG,kBAAkB;YAAE,SAAS;YAA2D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAc,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,SAAS;gBAAyB;gBAAG;oBAAE,SAAS;oBAAyC,iBAAiB,CAAC;oBAAG,OAAO;oBAA4B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB,CAAC;oBAAG,OAAO;oBAA2B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAmM;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmM;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA2M;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqG;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6L;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8E;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8E;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA6D;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAO,iBAAiB,CAAC;YAAG,OAAO;YAA6C,eAAe,CAAC;YAAG,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAA4C,QAAQ;gBAAqF;gBAAG;oBAAE,SAAS;oBAA2W,QAAQ;gBAA2F;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,SAAS,CAAC,8DAA8D,CAAC;gBAAC;gBAAG;oBAAE,SAAS,CAAC,+DAA+D,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyI;wBAAG,KAAK,CAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0K;wBAAG,KAAK;4BAAE,QAAQ;wBAA0K;oBAAE;oBAAG,OAAO;oBAAwB,eAAe,CAAC;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA8C;wBAAG;4BAAE,WAAW;wBAA8C;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA8nB,iBAAiB,CAAC;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;QAA6D;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAiB,iBAAiB,CAAC;YAAG,eAAe;YAA+G,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+H;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAiB,iBAAiB,CAAC;YAAG,eAAe;YAA+G,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+H;YAAE;QAAE;QAAG,wBAAwB;YAAE,YAAY,EAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY,EAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAoB,QAAQ;QAAkC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuE,QAAQ;gBAA2B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAkB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,uBAAuB;YAAG,SAAS,CAAC,gKAAgK,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAgB,eAAe,CAAC;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,SAAS;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,uFAAuF,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAmC,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,YAAY,EAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,2FAA2F,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAsC,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY,EAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,gEAAgE,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAqC,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,oEAAoE,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAwC,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA8B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA8B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAmC,QAAQ;gBAAwB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiE;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAU,QAAQ;wBAA+D;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAsB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAmE;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA+C;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,SAAS;oCAAK,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAuC;oCAAE;oCAAG,OAAO;oCAAS,QAAQ;gCAAiC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAwE,QAAQ;gBAAyB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAwB,eAAe,CAAC;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAe,QAAQ;QAAoD;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;oBAA2C,SAAS;oBAA4B,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAiC;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;4BAA6B,YAAY;gCAAC;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAuC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAuC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAyC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAiC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA0G,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0G,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAiC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAA8E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAkE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAA6D;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,MAAM;4BAAE,QAAQ;wBAAsD;wBAAG,MAAM;4BAAE,QAAQ;wBAAsD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,SAAS;gBAAsV;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAA8E,QAAQ;QAA4B;QAAG,+BAA+B;YAAE,SAAS;YAA2K,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,SAAS;oBAA6B,iBAAiB,CAAC;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAmoB,iBAAiB,CAAC;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,SAAS;QAAoC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;YAAE;YAAG,SAAS;QAA6K;QAAG,UAAU;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkE;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,eAAe;YAAkD,OAAO;YAA4E,eAAe,CAAC;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqC,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA8B;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAyD;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;QAAyC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAkD,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+D,QAAQ;gBAAkC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAM,QAAQ;QAAuC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkE;gCAAG,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,SAAS;wBAAwB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAA6B;QAAG,mBAAmB;YAAE,SAAS;YAAkC,QAAQ;QAAwB;QAAG,qBAAqB;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG,oBAAoB;YAAE,SAAS;QAA6nB;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAAkC,QAAQ;wBAAyC;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAwC;wBAAG;4BAAE,SAAS;4BAAwB,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAA+C;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwC,QAAQ;gBAAiC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgF;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwE;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,SAAS;oBAAqC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4E;wBAAG,KAAK;4BAAE,QAAQ;wBAAmI;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiI;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,SAAS;4BAAQ,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+F;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoE;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAA6B;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,WAAW;QAAC;QAAQ;QAAM;QAAS;KAAM;AAAC;AAC3u2C,IAAI,cAAc;IAChB;CACD", "ignoreList": [0], "debugId": null}}]}