{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/swift.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"<PERSON>\", \"name\": \"swift\", \"patterns\": [{ \"include\": \"#root\" }], \"repository\": { \"async-throws\": { \"captures\": { \"1\": { \"name\": \"invalid.illegal.await-must-precede-throws.swift\" }, \"2\": { \"name\": \"storage.modifier.exception.swift\" }, \"3\": { \"name\": \"storage.modifier.async.swift\" } }, \"match\": \"\\\\b(?:(throws\\\\s+async|rethrows\\\\s+async)|(throws|rethrows)|(async))\\\\b\" }, \"attributes\": { \"patterns\": [{ \"begin\": \"((@)available)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.attribute.swift\" }, \"2\": { \"name\": \"punctuation.definition.attribute.swift\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.attribute.available.swift\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.platform.os.swift\" }, \"2\": { \"name\": \"constant.numeric.swift\" } }, \"match\": \"\\\\b(swift|(?:iOS|macOS|OSX|watchOS|tvOS|visionOS|UIKitForMac)(?:ApplicationExtension)?)\\\\b(?:\\\\s+(\\\\d+(?:\\\\.\\\\d+)*\\\\b))?\" }, { \"begin\": \"\\\\b(introduced|deprecated|obsoleted)\\\\s*(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.swift\" }, \"2\": { \"name\": \"punctuation.separator.key-value.swift\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"match\": \"\\\\b\\\\d+(?:\\\\.\\\\d+)*\\\\b\", \"name\": \"constant.numeric.swift\" }] }, { \"begin\": '\\\\b(message|renamed)\\\\s*(:)\\\\s*(?=\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.swift\" }, \"2\": { \"name\": \"punctuation.separator.key-value.swift\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#literals\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.other.platform.all.swift\" }, \"2\": { \"name\": \"keyword.other.swift\" }, \"3\": { \"name\": \"invalid.illegal.character-not-allowed-here.swift\" } }, \"match\": \"(?:(\\\\*)|\\\\b(deprecated|unavailable|noasync)\\\\b)\\\\s*(.*?)(?=[,)])\" }] }, { \"begin\": \"((@)objc)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.attribute.swift\" }, \"2\": { \"name\": \"punctuation.definition.attribute.swift\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.attribute.objc.swift\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.illegal.missing-colon-after-selector-piece.swift\" } }, \"match\": \"\\\\w*(?::(?:\\\\w*:)*(\\\\w*))?\", \"name\": \"entity.name.function.swift\" }] }, { \"begin\": \"(@)(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.attribute.swift\" }, \"1\": { \"name\": \"punctuation.definition.attribute.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"comment\": \"any other attribute\", \"end\": \"(?!\\\\G\\\\()\", \"name\": \"meta.attribute.swift\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.arguments.attribute.swift\", \"patterns\": [{ \"include\": \"#expressions\" }] }] }] }, \"builtin-functions\": { \"patterns\": [{ \"comment\": \"Member functions in the standard library in Swift 3 which may be used with trailing closures and no parentheses\", \"match\": \"(?<=\\\\.)(?:s(?:ort(?:ed)?|plit)|contains|index|partition|f(?:i(?:lter|rst)|orEach|latMap)|with(?:MutableCharacters|CString|U(?:nsafe(?:Mutable(?:BufferPointer|Pointer(?:s|To(?:Header|Elements)))|BufferPointer)|TF8Buffer))|m(?:in|a(?:p|x)))(?=\\\\s*[({])\\\\b\", \"name\": \"support.function.swift\" }, { \"comment\": \"Member functions in the standard library in Swift 3\", \"match\": \"(?<=\\\\.)(?:s(?:ymmetricDifference|t(?:oreBytes|arts|ride)|ortInPlace|u(?:ccessor|ffix|btract(?:ing|InPlace|WithOverflow)?)|quareRoot|amePosition)|h(?:oldsUnique(?:Reference|OrPinnedReference)|as(?:Suffix|Prefix))|ne(?:gate(?:d)?|xt)|c(?:o(?:untByEnumerating|py(?:Bytes)?)|lamp(?:ed)?|reate)|t(?:o(?:IntMax|Opaque|UIntMax)|ake(?:RetainedValue|UnretainedValue)|r(?:uncatingRemainder|a(?:nscodedLength|ilSurrogate)))|i(?:s(?:MutableAndUniquelyReferenced(?:OrPinned)?|S(?:trictSu(?:perset(?:Of)?|bset(?:Of)?)|u(?:perset(?:Of)?|bset(?:Of)?))|Continuation|T(?:otallyOrdered|railSurrogate)|Disjoint(?:With)?|Unique(?:Reference|lyReferenced(?:OrPinned)?)|Equal|Le(?:ss(?:ThanOrEqualTo)?|adSurrogate))|n(?:sert(?:ContentsOf)?|tersect(?:ion|InPlace)?|itialize(?:Memory|From)?|dex(?:Of|ForKey)))|o(?:verlaps|bjectAt)|d(?:i(?:stance(?:To)?|vide(?:d|WithOverflow)?)|e(?:s(?:cendant|troy)|code(?:CString)?|initialize|alloc(?:ate(?:Capacity)?)?)|rop(?:First|Last))|u(?:n(?:ion(?:InPlace)?|derestimateCount|wrappedOrError)|p(?:date(?:Value)?|percased))|join(?:ed|WithSeparator)|p(?:op(?:First|Last)|ass(?:Retained|Unretained)|re(?:decessor|fix))|e(?:scape(?:d)?|n(?:code|umerate(?:d)?)|lementsEqual|xclusiveOr(?:InPlace)?)|f(?:orm(?:Remainder|S(?:ymmetricDifference|quareRoot)|TruncatingRemainder|In(?:tersection|dex)|Union)|latten|rom(?:CString(?:RepairingIllFormedUTF8)?|Opaque))|w(?:i(?:thMemoryRebound|dth)|rite(?:To)?)|l(?:o(?:wercased|ad)|e(?:adSurrogate|xicographical(?:Compare|lyPrecedes)))|a(?:ss(?:ign(?:BackwardFrom|From)?|umingMemoryBound)|d(?:d(?:ing(?:Product)?|Product|WithOverflow)?|vanced(?:By)?)|utorelease|ppend(?:ContentsOf)?|lloc(?:ate)?|bs)|r(?:ound(?:ed)?|e(?:serveCapacity|tain|duce|place(?:Range|Subrange)?|verse(?:d)?|quest(?:NativeBuffer|UniqueMutableBackingBuffer)|lease|m(?:ove(?:Range|Subrange|Value(?:ForKey)?|First|Last|A(?:tIndex|ll))?|ainder(?:WithOverflow)?)))|ge(?:nerate|t(?:Objects|Element))|m(?:in(?:imum(?:Magnitude)?|Element)|ove(?:Initialize(?:Memory|BackwardFrom|From)?|Assign(?:From)?)?|ultipl(?:y(?:WithOverflow)?|ied)|easure|a(?:ke(?:Iterator|Description)|x(?:imum(?:Magnitude)?|Element)))|bindMemory)(?=\\\\s*\\\\()\", \"name\": \"support.function.swift\" }, { \"comment\": \"Member functions in the standard library in Swift 2 only\", \"match\": \"(?<=\\\\.)(?:s(?:uperclassMirror|amePositionIn|tartsWith)|nextObject|c(?:haracterAtIndex|o(?:untByEnumeratingWithState|pyWithZone)|ustom(?:Mirror|PlaygroundQuickLook))|is(?:EmptyInput|ASCII)|object(?:Enumerator|ForKey|AtIndex)|join|put|keyEnumerator|withUnsafeMutablePointerToValue|length|getMirror|m(?:oveInitializeAssignFrom|ember))(?=\\\\s*\\\\()\", \"name\": \"support.function.swift\" }] }, \"builtin-global-functions\": { \"patterns\": [{ \"begin\": \"\\\\b(type)(\\\\()\\\\s*(of)(:)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.dynamic-type.swift\" }, \"2\": { \"name\": \"punctuation.definition.arguments.begin.swift\" }, \"3\": { \"name\": \"support.variable.parameter.swift\" }, \"4\": { \"name\": \"punctuation.separator.argument-label.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"patterns\": [{ \"include\": \"#expressions\" }] }, { \"comment\": \"Global functions available in Swift 3 which may be used with trailing closures and no parentheses\", \"match\": \"\\\\b(?:anyGenerator|autoreleasepool)(?=\\\\s*[({])\\\\b\", \"name\": \"support.function.swift\" }, { \"comment\": \"Global functions available in Swift 3\", \"match\": \"\\\\b(?:s(?:tride(?:of(?:Value)?)?|izeof(?:Value)?|equence|wap)|numericCast|transcode|is(?:UniquelyReferenced(?:NonObjC)?|KnownUniquelyReferenced)|zip|d(?:ump|ebugPrint)|unsafe(?:BitCast|Downcast|Unwrap|Address(?:Of)?)|pr(?:int|econdition(?:Failure)?)|fatalError|with(?:Unsafe(?:MutablePointer|Pointer)|ExtendedLifetime|VaList)|a(?:ssert(?:ionFailure)?|lignof(?:Value)?|bs)|re(?:peatElement|adLine)|getVaList|m(?:in|ax))(?=\\\\s*\\\\()\", \"name\": \"support.function.swift\" }, { \"comment\": \"Global functions available in Swift 2 only\", \"match\": \"\\\\b(?:s(?:ort|uffix|pli(?:ce|t))|insert|overlaps|d(?:istance|rop(?:First|Last))|join|prefix|extend|withUnsafe(?:MutablePointers|Pointers)|lazy|advance|re(?:flect|move(?:Range|Last|A(?:tIndex|ll))))(?=\\\\s*\\\\()\", \"name\": \"support.function.swift\" }] }, \"builtin-properties\": { \"patterns\": [{ \"comment\": \"The simpler (?<=\\\\bProcess\\\\.|\\\\bCommandLine\\\\.) breaks VS Code / Atom, see https://github.com/textmate/swift.tmbundle/issues/29\", \"match\": \"(?<=^Process\\\\.|\\\\WProcess\\\\.|^CommandLine\\\\.|\\\\WCommandLine\\\\.)(arguments|argc|unsafeArgv)\", \"name\": \"support.variable.swift\" }, { \"comment\": \"Properties in the standard library in Swift 3\", \"match\": \"(?<=\\\\.)(?:s(?:t(?:artIndex|ri(?:ngValue|de))|i(?:ze|gn(?:BitIndex|ificand(?:Bit(?:Count|Pattern)|Width)?|alingNaN)?)|u(?:perclassMirror|mmary|bscriptBaseAddress))|h(?:eader|as(?:hValue|PointerRepresentation))|n(?:ulTerminatedUTF8|ext(?:Down|Up)|a(?:n|tiveOwner))|c(?:haracters|ount(?:TrailingZeros)?|ustom(?:Mirror|PlaygroundQuickLook)|apacity)|i(?:s(?:S(?:ign(?:Minus|aling(?:NaN)?)|ubnormal)|N(?:ormal|aN)|Canonical|Infinite|Zero|Empty|Finite|ASCII)|n(?:dices|finity)|dentity)|owner|de(?:scription|bugDescription)|u(?:n(?:safelyUnwrapped|icodeScalar(?:s)?|derestimatedCount)|tf(?:16|8(?:Start|C(?:String|odeUnitCount))?)|intValue|ppercaseString|lp(?:OfOne)?)|p(?:i|ointee)|e(?:ndIndex|lements|xponent(?:Bit(?:Count|Pattern))?)|value(?:s)?|keys|quietNaN|f(?:irst(?:ElementAddress(?:IfContiguous)?)?|loatingPointClass)|l(?:ittleEndian|owercaseString|eastNo(?:nzeroMagnitude|rmalMagnitude)|a(?:st|zy))|a(?:l(?:ignment|l(?:ocatedElementCount|Zeros))|rray(?:PropertyIsNativeTypeChecked)?)|ra(?:dix|wValue)|greatestFiniteMagnitude|m(?:in|emory|ax)|b(?:yteS(?:ize|wapped)|i(?:nade|tPattern|gEndian)|uffer|ase(?:Address)?))\\\\b\", \"name\": \"support.variable.swift\" }, { \"comment\": \"Properties in the standard library in Swift 2 only\", \"match\": \"(?<=\\\\.)(?:boolValue|disposition|end|objectIdentifier|quickLookObject|start|valueType)\\\\b\", \"name\": \"support.variable.swift\" }, { \"comment\": \"Enum cases in the standard library - note that there is some overlap between these and the properties\", \"match\": \"(?<=\\\\.)(?:s(?:calarValue|i(?:ze|gnalingNaN)|o(?:und|me)|uppressed|prite|et)|n(?:one|egative(?:Subnormal|Normal|Infinity|Zero))|c(?:ol(?:or|lection)|ustomized)|t(?:o(?:NearestOr(?:Even|AwayFromZero)|wardZero)|uple|ext)|i(?:nt|mage)|optional|d(?:ictionary|o(?:uble|wn))|u(?:Int|p|rl)|p(?:o(?:sitive(?:Subnormal|Normal|Infinity|Zero)|int)|lus)|e(?:rror|mptyInput)|view|quietNaN|float|a(?:ttributedString|wayFromZero)|r(?:ectangle|ange)|generated|minus|b(?:ool|ezierPath))\\\\b\", \"name\": \"support.variable.swift\" }] }, \"builtin-types\": { \"comment\": \"Types provided in the standard library\", \"patterns\": [{ \"include\": \"#builtin-types-builtin-class-type\" }, { \"include\": \"#builtin-types-builtin-enum-type\" }, { \"include\": \"#builtin-types-builtin-protocol-type\" }, { \"include\": \"#builtin-types-builtin-struct-type\" }, { \"include\": \"#builtin-types-builtin-typealias\" }, { \"match\": \"\\\\bAny\\\\b\", \"name\": \"support.type.any.swift\" }] }, \"builtin-types-builtin-class-type\": { \"comment\": \"Builtin class types\", \"match\": \"\\\\b(Managed(Buffer|ProtoBuffer)|NonObjectiveCBase|AnyGenerator)\\\\b\", \"name\": \"support.class.swift\" }, \"builtin-types-builtin-enum-type\": { \"patterns\": [{ \"comment\": \"CommandLine is an enum, but it acts like a constant\", \"match\": \"\\\\b(?:CommandLine|Process(?=\\\\.))\\\\b\", \"name\": \"support.constant.swift\" }, { \"comment\": \"The return type of a function that never returns\", \"match\": \"\\\\bNever\\\\b\", \"name\": \"support.constant.never.swift\" }, { \"comment\": \"Enum types in the standard library in Swift 3\", \"match\": \"\\\\b(?:ImplicitlyUnwrappedOptional|Representation|MemoryLayout|FloatingPointClassification|SetIndexRepresentation|SetIteratorRepresentation|FloatingPointRoundingRule|UnicodeDecodingResult|Optional|DictionaryIndexRepresentation|AncestorRepresentation|DisplayStyle|PlaygroundQuickLook|Never|FloatingPointSign|Bit|DictionaryIteratorRepresentation)\\\\b\", \"name\": \"support.type.swift\" }, { \"comment\": \"Enum types in the standard library in Swift 2 only\", \"match\": \"\\\\b(?:MirrorDisposition|QuickLookObject)\\\\b\", \"name\": \"support.type.swift\" }] }, \"builtin-types-builtin-protocol-type\": { \"patterns\": [{ \"comment\": \"Protocols in the standard library in Swift 3\", \"match\": \"\\\\b(?:Ra(?:n(?:domAccess(?:Collection|Indexable)|geReplaceable(?:Collection|Indexable))|wRepresentable)|M(?:irrorPath|utable(?:Collection|Indexable))|Bi(?:naryFloatingPoint|twiseOperations|directional(?:Collection|Indexable))|S(?:tr(?:ideable|eamable)|igned(?:Number|Integer)|e(?:tAlgebra|quence))|Hashable|C(?:o(?:llection|mparable)|ustom(?:Reflectable|StringConvertible|DebugStringConvertible|PlaygroundQuickLookable|LeafReflectable)|VarArg)|TextOutputStream|I(?:n(?:teger(?:Arithmetic)?|dexable(?:Base)?)|teratorProtocol)|OptionSet|Un(?:signedInteger|icodeCodec)|E(?:quatable|rror|xpressibleBy(?:BooleanLiteral|String(?:Interpolation|Literal)|NilLiteral|IntegerLiteral|DictionaryLiteral|UnicodeScalarLiteral|ExtendedGraphemeClusterLiteral|FloatLiteral|ArrayLiteral))|FloatingPoint|L(?:osslessStringConvertible|azy(?:SequenceProtocol|CollectionProtocol))|A(?:nyObject|bsoluteValuable))\\\\b\", \"name\": \"support.type.swift\" }, { \"comment\": \"Protocols in the standard library in Swift 2 only\", \"match\": \"\\\\b(?:Ran(?:domAccessIndexType|geReplaceableCollectionType)|GeneratorType|M(?:irror(?:Type|PathType)|utable(?:Sliceable|CollectionType))|B(?:i(?:twiseOperationsType|directionalIndexType)|oolean(?:Type|LiteralConvertible))|S(?:tring(?:InterpolationConvertible|LiteralConvertible)|i(?:nkType|gned(?:NumberType|IntegerType))|e(?:tAlgebraType|quenceType)|liceable)|NilLiteralConvertible|C(?:ollectionType|VarArgType)|Inte(?:rvalType|ger(?:Type|LiteralConvertible|ArithmeticType))|O(?:utputStreamType|ptionSetType)|DictionaryLiteralConvertible|Un(?:signedIntegerType|icode(?:ScalarLiteralConvertible|CodecType))|E(?:rrorType|xten(?:sibleCollectionType|dedGraphemeClusterLiteralConvertible))|F(?:orwardIndexType|loat(?:ingPointType|LiteralConvertible))|A(?:nyCollectionType|rrayLiteralConvertible))\\\\b\", \"name\": \"support.type.swift\" }] }, \"builtin-types-builtin-struct-type\": { \"patterns\": [{ \"comment\": \"Structs in the standard library in Swift 3\", \"match\": \"\\\\b(?:R(?:e(?:peat(?:ed)?|versed(?:RandomAccess(?:Collection|Index)|Collection|Index))|an(?:domAccessSlice|ge(?:Replaceable(?:RandomAccessSlice|BidirectionalSlice|Slice)|Generator)?))|Generator(?:Sequence|OfOne)|M(?:irror|utable(?:Ran(?:domAccessSlice|geReplaceable(?:RandomAccessSlice|BidirectionalSlice|Slice))|BidirectionalSlice|Slice)|anagedBufferPointer)|B(?:idirectionalSlice|ool)|S(?:t(?:aticString|ri(?:ng|deT(?:hrough(?:Generator|Iterator)?|o(?:Generator|Iterator)?)))|et(?:I(?:ndex|terator))?|lice)|HalfOpenInterval|C(?:haracter(?:View)?|o(?:ntiguousArray|untable(?:Range|ClosedRange)|llectionOfOne)|OpaquePointer|losed(?:Range(?:I(?:ndex|terator))?|Interval)|VaListPointer)|I(?:n(?:t(?:16|8|32|64)?|d(?:ices|ex(?:ing(?:Generator|Iterator))?))|terator(?:Sequence|OverOne)?)|Zip2(?:Sequence|Iterator)|O(?:paquePointer|bjectIdentifier)|D(?:ictionary(?:I(?:ndex|terator)|Literal)?|ouble|efault(?:RandomAccessIndices|BidirectionalIndices|Indices))|U(?:n(?:safe(?:RawPointer|Mutable(?:RawPointer|BufferPointer|Pointer)|BufferPointer(?:Generator|Iterator)?|Pointer)|icodeScalar(?:View)?|foldSequence|managed)|TF(?:16(?:View)?|8(?:View)?|32)|Int(?:16|8|32|64)?)|Join(?:Generator|ed(?:Sequence|Iterator))|PermutationGenerator|E(?:numerate(?:Generator|Sequence|d(?:Sequence|Iterator))|mpty(?:Generator|Collection|Iterator))|Fl(?:oat(?:80)?|atten(?:Generator|BidirectionalCollection(?:Index)?|Sequence|Collection(?:Index)?|Iterator))|L(?:egacyChildren|azy(?:RandomAccessCollection|Map(?:RandomAccessCollection|Generator|BidirectionalCollection|Sequence|Collection|Iterator)|BidirectionalCollection|Sequence|Collection|Filter(?:Generator|BidirectionalCollection|Sequence|Collection|I(?:ndex|terator))))|A(?:ny(?:RandomAccessCollection|Generator|BidirectionalCollection|Sequence|Hashable|Collection|I(?:ndex|terator))|utoreleasingUnsafeMutablePointer|rray(?:Slice)?))\\\\b\", \"name\": \"support.type.swift\" }, { \"comment\": \"Structs in the standard library in Swift 2 only\", \"match\": \"\\\\b(?:R(?:everse(?:RandomAccess(?:Collection|Index)|Collection|Index)|awByte)|Map(?:Generator|Sequence|Collection)|S(?:inkOf|etGenerator)|Zip2Generator|DictionaryGenerator|Filter(?:Generator|Sequence|Collection(?:Index)?)|LazyForwardCollection|Any(?:RandomAccessIndex|BidirectionalIndex|Forward(?:Collection|Index)))\\\\b\", \"name\": \"support.type.swift\" }] }, \"builtin-types-builtin-typealias\": { \"patterns\": [{ \"comment\": \"Typealiases in the standard library in Swift 3\", \"match\": \"\\\\b(?:Raw(?:Significand|Exponent|Value)|B(?:ooleanLiteralType|uffer|ase)|S(?:t(?:orage|r(?:i(?:ngLiteralType|de)|eam(?:1|2)))|ubSequence)|NativeBuffer|C(?:hild(?:ren)?|Bool|S(?:hort|ignedChar)|odeUnit|Char(?:16|32)?|Int|Double|Unsigned(?:Short|Char|Int|Long(?:Long)?)|Float|WideChar|Long(?:Long)?)|I(?:n(?:t(?:Max|egerLiteralType)|d(?:ices|ex(?:Distance)?))|terator)|Distance|U(?:n(?:icodeScalar(?:Type|Index|View|LiteralType)|foldFirstSequence)|TF(?:16(?:Index|View)|8Index)|IntMax)|E(?:lement(?:s)?|x(?:tendedGraphemeCluster(?:Type|LiteralType)|ponent))|V(?:oid|alue)|Key|Float(?:32|LiteralType|64)|AnyClass)\\\\b\", \"name\": \"support.type.swift\" }, { \"comment\": \"Typealiases in the standard library in Swift 2 only\", \"match\": \"\\\\b(?:Generator|PlaygroundQuickLook|UWord|Word)\\\\b\", \"name\": \"support.type.swift\" }] }, \"code-block\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.scope.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.scope.end.swift\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.swift\" } }, \"match\": \"\\\\A^(#!).*$\\\\n?\", \"name\": \"comment.line.number-sign.swift\" }, { \"begin\": \"/\\\\*\\\\*(?!/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.swift\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.swift\" } }, \"name\": \"comment.block.documentation.swift\", \"patterns\": [{ \"include\": \"#comments-nested\" }] }, { \"begin\": \"/\\\\*:\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.swift\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.swift\" } }, \"name\": \"comment.block.documentation.playground.swift\", \"patterns\": [{ \"include\": \"#comments-nested\" }] }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.swift\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.swift\" } }, \"name\": \"comment.block.swift\", \"patterns\": [{ \"include\": \"#comments-nested\" }] }, { \"match\": \"\\\\*/\", \"name\": \"invalid.illegal.unexpected-end-of-block-comment.swift\" }, { \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.swift\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"///\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.swift\" } }, \"end\": \"$\", \"name\": \"comment.line.triple-slash.documentation.swift\" }, { \"begin\": \"//:\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.swift\" } }, \"end\": \"$\", \"name\": \"comment.line.double-slash.documentation.swift\" }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.swift\" } }, \"end\": \"$\", \"name\": \"comment.line.double-slash.swift\" }] }] }, \"comments-nested\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"patterns\": [{ \"include\": \"#comments-nested\" }] }, \"compiler-control\": { \"patterns\": [{ \"begin\": \"^\\\\s*(#)(if|elseif)\\\\s+(false)\\\\b.*?(?=$|//|/\\\\*)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.preprocessor.conditional.swift\" }, \"1\": { \"name\": \"punctuation.definition.preprocessor.swift\" }, \"2\": { \"name\": \"keyword.control.import.preprocessor.conditional.swift\" }, \"3\": { \"name\": \"constant.language.boolean.swift\" } }, \"contentName\": \"comment.block.preprocessor.swift\", \"end\": \"(?=^\\\\s*(#(elseif|else|endif)\\\\b))\" }, { \"begin\": \"^\\\\s*(#)(if|elseif)\\\\s+\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.preprocessor.swift\" }, \"2\": { \"name\": \"keyword.control.import.preprocessor.conditional.swift\" } }, \"end\": \"(?=\\\\s*(?://|/\\\\*))|$\", \"name\": \"meta.preprocessor.conditional.swift\", \"patterns\": [{ \"match\": \"(&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.swift\" }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.swift\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.swift\" }, \"3\": { \"name\": \"support.constant.platform.architecture.swift\" }, \"4\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"match\": \"\\\\b(arch)\\\\s*(\\\\()\\\\s*(?:(arm|arm64|powerpc64|powerpc64le|i386|x86_64|s390x)|\\\\w+)\\\\s*(\\\\))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.swift\" }, \"3\": { \"name\": \"support.constant.platform.os.swift\" }, \"4\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"match\": \"\\\\b(os)\\\\s*(\\\\()\\\\s*(?:(macOS|OSX|iOS|tvOS|watchOS|visionOS|Android|Linux|FreeBSD|Windows|PS4)|\\\\w+)\\\\s*(\\\\))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.swift\" }, \"3\": { \"name\": \"entity.name.type.module.swift\" }, \"4\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"match\": \"\\\\b(canImport)\\\\s*(\\\\()([\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*)(\\\\))\" }, { \"begin\": \"\\\\b(targetEnvironment)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.swift\" } }, \"end\": \"(\\\\))|$\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"patterns\": [{ \"match\": \"\\\\b(simulator|UIKitForMac)\\\\b\", \"name\": \"support.constant.platform.environment.swift\" }] }, { \"begin\": \"\\\\b(swift|compiler)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.swift\" } }, \"end\": \"(\\\\))|$\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"patterns\": [{ \"match\": \">=|<\", \"name\": \"keyword.operator.comparison.swift\" }, { \"match\": \"\\\\b\\\\d+(?:\\\\.\\\\d+)*\\\\b\", \"name\": \"constant.numeric.swift\" }] }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.preprocessor.swift\" }, \"2\": { \"name\": \"keyword.control.import.preprocessor.conditional.swift\" }, \"3\": { \"patterns\": [{ \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] } }, \"match\": \"^\\\\s*(#)(else|endif)(.*?)(?=$|//|/\\\\*)\", \"name\": \"meta.preprocessor.conditional.swift\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.preprocessor.swift\" }, \"2\": { \"name\": \"keyword.control.import.preprocessor.sourcelocation.swift\" }, \"4\": { \"name\": \"punctuation.definition.parameters.begin.swift\" }, \"5\": { \"patterns\": [{ \"begin\": '(file)\\\\s*(:)\\\\s*(?=\")', \"beginCaptures\": { \"1\": { \"name\": \"support.variable.parameter.swift\" }, \"2\": { \"name\": \"punctuation.separator.key-value.swift\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#literals\" }] }, { \"captures\": { \"1\": { \"name\": \"support.variable.parameter.swift\" }, \"2\": { \"name\": \"punctuation.separator.key-value.swift\" }, \"3\": { \"name\": \"constant.numeric.integer.swift\" } }, \"match\": \"(line)\\\\s*(:)\\\\s*(\\\\d+)\" }, { \"match\": \",\", \"name\": \"punctuation.separator.parameters.swift\" }, { \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] }, \"6\": { \"name\": \"punctuation.definition.parameters.begin.swift\" }, \"7\": { \"patterns\": [{ \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] } }, \"match\": \"^\\\\s*(#)(sourceLocation)((\\\\()([^)]*)(\\\\)))(.*?)(?=$|//|/\\\\*)\", \"name\": \"meta.preprocessor.sourcelocation.swift\" }] }, \"conditionals\": { \"patterns\": [{ \"begin\": \"(?<!\\\\.)\\\\b(if|guard|switch|for)\\\\b\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#keywords\" }] } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures\" }] }, { \"begin\": \"(?<!\\\\.)\\\\b(while)\\\\b\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#keywords\" }] } }, \"comment\": \"while can be the end of a repeat-while statement so doesn't necessarily have braces after it\", \"end\": \"(?=\\\\{)|$\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures\" }] }] }, \"declarations\": { \"patterns\": [{ \"include\": \"#declarations-function\" }, { \"include\": \"#declarations-function-initializer\" }, { \"include\": \"#declarations-function-subscript\" }, { \"include\": \"#declarations-typed-variable-declaration\" }, { \"include\": \"#declarations-import\" }, { \"include\": \"#declarations-operator\" }, { \"include\": \"#declarations-precedencegroup\" }, { \"include\": \"#declarations-protocol\" }, { \"include\": \"#declarations-type\" }, { \"include\": \"#declarations-extension\" }, { \"include\": \"#declarations-typealias\" }, { \"include\": \"#declarations-macro\" }] }, \"declarations-available-types\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#builtin-types\" }, { \"include\": \"#attributes\" }, { \"match\": \"\\\\basync\\\\b\", \"name\": \"storage.modifier.async.swift\" }, { \"match\": \"\\\\b(?:throws|rethrows)\\\\b\", \"name\": \"storage.modifier.exception.swift\" }, { \"match\": \"\\\\bsome\\\\b\", \"name\": \"keyword.other.operator.type.opaque.swift\" }, { \"match\": \"\\\\bany\\\\b\", \"name\": \"keyword.other.operator.type.existential.swift\" }, { \"match\": \"\\\\b(?:repeat|each)\\\\b\", \"name\": \"keyword.control.loop.swift\" }, { \"match\": \"\\\\b(?:inout|isolated|borrowing|consuming)\\\\b\", \"name\": \"storage.modifier.swift\" }, { \"match\": \"\\\\bSelf\\\\b\", \"name\": \"variable.language.swift\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.type.function.swift\" } }, \"match\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(->)(?![/=\\\\-+!*%<>&|\\\\^~.])\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.type.composition.swift\" } }, \"comment\": \"Swift 3: A & B\", \"match\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(&)(?![/=\\\\-+!*%<>&|\\\\^~.])\" }, { \"match\": \"[?!]\", \"name\": \"keyword.operator.type.optional.swift\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.function.variadic-parameter.swift\" }, { \"comment\": \"Swift 2: protocol<A, B>\", \"match\": \"\\\\bprotocol\\\\b\", \"name\": \"keyword.other.type.composition.swift\" }, { \"match\": \"(?<=\\\\.)(?:Protocol|Type)\\\\b\", \"name\": \"keyword.other.type.metatype.swift\" }, { \"include\": \"#declarations-available-types-tuple-type\" }, { \"include\": \"#declarations-available-types-collection-type\" }, { \"include\": \"#declarations-generic-argument-clause\" }] }, \"declarations-available-types-collection-type\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.collection-type.begin.swift\" } }, \"comment\": \"array and dictionary types [Value] and [Key: Value]\", \"end\": \"\\\\]|(?=[>){}])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.collection-type.end.swift\" } }, \"patterns\": [{ \"include\": \"#declarations-available-types\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.swift\" } }, \"end\": \"(?=\\\\]|[>){}])\", \"patterns\": [{ \"match\": \":\", \"name\": \"invalid.illegal.extra-colon-in-dictionary-type.swift\" }, { \"include\": \"#declarations-available-types\" }] }] }, \"declarations-available-types-tuple-type\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tuple-type.begin.swift\" } }, \"end\": \"\\\\)|(?=[>\\\\]{}])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tuple-type.end.swift\" } }, \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"declarations-extension\": { \"begin\": \"\\\\b(extension)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.$1.swift\" }, \"2\": { \"name\": \"entity.name.type.swift\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.definition.type.$1.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"comment\": \"SE-0143: Conditional Conformances\", \"include\": \"#declarations-generic-where-clause\" }, { \"include\": \"#declarations-inheritance-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.end.swift\" } }, \"name\": \"meta.definition.type.body.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-function\": { \"begin\": \"\\\\b(func)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)|(?:((?<oph>[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}])(\\\\g<oph>|(?<opc>[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))*)|(\\\\.(\\\\g<oph>|\\\\g<opc>|\\\\.)+)))\\\\s*(?=\\\\(|<)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\" }, \"2\": { \"name\": \"entity.name.function.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?<=\\\\})|$\", \"name\": \"meta.definition.function.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#declarations-function-result\" }, { \"include\": \"#async-throws\" }, { \"comment\": \"Swift 3: generic constraints after the parameters and return type\", \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.function.begin.swift\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.end.swift\" } }, \"name\": \"meta.definition.function.body.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-function-initializer\": { \"begin\": \"(?<!\\\\.)\\\\b(init[?!]*)\\\\s*(?=\\\\(|<)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\", \"patterns\": [{ \"match\": \"(?<=[?!])[?!]+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] } }, \"end\": \"(?<=\\\\})|$\", \"name\": \"meta.definition.function.initializer.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#async-throws\" }, { \"comment\": \"Swift 3: generic constraints after the parameters and return type\", \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.function.begin.swift\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.end.swift\" } }, \"name\": \"meta.definition.function.body.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-function-result\": { \"begin\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(->)(?![/=\\\\-+!*%<>&|\\\\^~.])\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.function-result.swift\" } }, \"end\": \"(?!\\\\G)(?=\\\\{|\\\\bwhere\\\\b|;|=)|$\", \"name\": \"meta.function-result.swift\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"declarations-function-subscript\": { \"begin\": \"(?<!\\\\.)\\\\b(subscript)\\\\s*(?=\\\\(|<)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\" } }, \"end\": \"(?<=\\\\})|$\", \"name\": \"meta.definition.function.subscript.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#declarations-function-result\" }, { \"include\": \"#async-throws\" }, { \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.function.begin.swift\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.end.swift\" } }, \"name\": \"meta.definition.function.body.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-generic-argument-clause\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.generic-argument-clause.begin.swift\" } }, \"end\": \">|(?=[)\\\\]{}])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.generic-argument-clause.end.swift\" } }, \"name\": \"meta.generic-argument-clause.swift\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"declarations-generic-parameter-clause\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.generic-parameter-clause.begin.swift\" } }, \"end\": \">|(?=[^\\\\w\\\\d:<>\\\\s,=&`])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.generic-parameter-clause.end.swift\" } }, \"name\": \"meta.generic-parameter-clause.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"comment\": \"Swift 2: constraints inside the generic param list\", \"include\": \"#declarations-generic-where-clause\" }, { \"match\": \"\\\\beach\\\\b\", \"name\": \"keyword.control.loop.swift\" }, { \"captures\": { \"1\": { \"name\": \"variable.language.generic-parameter.swift\" } }, \"match\": \"\\\\b((?!\\\\d)\\\\w[\\\\w\\\\d]*)\\\\b\" }, { \"match\": \",\", \"name\": \"punctuation.separator.generic-parameters.swift\" }, { \"begin\": \"(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.generic-parameter-constraint.swift\" } }, \"end\": \"(?=[,>]|(?!\\\\G)\\\\bwhere\\\\b)\", \"name\": \"meta.generic-parameter-constraint.swift\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=[,>]|(?!\\\\G)\\\\bwhere\\\\b)\", \"name\": \"entity.other.inherited-class.swift\", \"patterns\": [{ \"include\": \"#declarations-type-identifier\" }, { \"include\": \"#declarations-type-operators\" }] }] }] }, \"declarations-generic-where-clause\": { \"begin\": \"\\\\b(where)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.generic-constraint-introducer.swift\" } }, \"end\": \"(?!\\\\G)$|(?=[>{};\\\\n]|//|/\\\\*)\", \"name\": \"meta.generic-where-clause.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-where-clause-requirement-list\" }] }, \"declarations-generic-where-clause-requirement-list\": { \"begin\": \"\\\\G|,\\\\s*\", \"end\": \"(?=[,>{};\\\\n]|//|/\\\\*)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#constraint\" }, { \"include\": \"#declarations-available-types\" }, { \"begin\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(==)(?![/=\\\\-+!*%<>&|\\\\^~.])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.generic-constraint.same-type.swift\" } }, \"end\": \"(?=\\\\s*[,>{};\\\\n]|//|/\\\\*)\", \"name\": \"meta.generic-where-clause.same-type-requirement.swift\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, { \"begin\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(:)(?![/=\\\\-+!*%<>&|\\\\^~.])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.generic-constraint.conforms-to.swift\" } }, \"end\": \"(?=\\\\s*[,>{};\\\\n]|//|/\\\\*)\", \"name\": \"meta.generic-where-clause.conformance-requirement.swift\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*\", \"contentName\": \"entity.other.inherited-class.swift\", \"end\": \"(?=\\\\s*[,>{};\\\\n]|//|/\\\\*)\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }] }] }, \"declarations-import\": { \"begin\": \"(?<!\\\\.)\\\\b(import)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import.swift\" } }, \"end\": \"(;)|$\\\\n?|(?=//|/\\\\*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.swift\" } }, \"name\": \"meta.import.swift\", \"patterns\": [{ \"begin\": \"\\\\G(?!;|$|//|/\\\\*)(?:(typealias|struct|class|actor|enum|protocol|var|func)\\\\s+)?\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.swift\" } }, \"end\": \"(?=;|$|//|/\\\\*)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"match\": \"(?<=\\\\G|\\\\.)(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)\", \"name\": \"entity.name.type.swift\" }, { \"match\": \"(?<=\\\\G|\\\\.)\\\\$\\\\d+\", \"name\": \"entity.name.type.swift\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"invalid.illegal.dot-not-allowed-here.swift\" }] } }, \"match\": \"(?<=\\\\G|\\\\.)(?:((?<oph>[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}])(\\\\g<oph>|(?<opc>[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))*)|(\\\\.(\\\\g<oph>|\\\\g<opc>|\\\\.)+))(?=\\\\.|;|$|//|/\\\\*|\\\\s)\", \"name\": \"entity.name.type.swift\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.import.swift\" }, { \"begin\": \"(?!\\\\s*(;|$|//|/\\\\*))\", \"end\": \"(?=\\\\s*(;|$|//|/\\\\*))\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] }] }, \"declarations-inheritance-clause\": { \"begin\": \"(:)(?=\\\\s*\\\\{)|(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.illegal.empty-inheritance-clause.swift\" }, \"2\": { \"name\": \"punctuation.separator.inheritance-clause.swift\" } }, \"end\": \"(?!\\\\G)$|(?=[={}]|(?!\\\\G)\\\\bwhere\\\\b)\", \"name\": \"meta.inheritance-clause.swift\", \"patterns\": [{ \"begin\": \"\\\\bclass\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.class.swift\" } }, \"end\": \"(?=[={}]|(?!\\\\G)\\\\bwhere\\\\b)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-inheritance-clause-more-types\" }] }, { \"begin\": \"\\\\G\", \"end\": \"(?!\\\\G)$|(?=[={}]|(?!\\\\G)\\\\bwhere\\\\b)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-inheritance-clause-inherited-type\" }, { \"include\": \"#declarations-inheritance-clause-more-types\" }, { \"include\": \"#declarations-type-operators\" }] }] }, \"declarations-inheritance-clause-inherited-type\": { \"begin\": \"(?=[`\\\\p{L}_])\", \"end\": \"(?!\\\\G)\", \"name\": \"entity.other.inherited-class.swift\", \"patterns\": [{ \"include\": \"#declarations-type-identifier\" }] }, \"declarations-inheritance-clause-more-types\": { \"begin\": \",\\\\s*\", \"end\": \"(?!\\\\G)(?!//|/\\\\*)|(?=[,={}]|(?!\\\\G)\\\\bwhere\\\\b)\", \"name\": \"meta.inheritance-list.more-types\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-inheritance-clause-inherited-type\" }, { \"include\": \"#declarations-inheritance-clause-more-types\" }, { \"include\": \"#declarations-type-operators\" }] }, \"declarations-macro\": { \"begin\": \"\\\\b(macro)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*(?=\\\\(|<|=)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\" }, \"2\": { \"name\": \"entity.name.function.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"$|(?=;|//|/\\\\*|\\\\}|=)\", \"name\": \"meta.definition.macro.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#declarations-function-result\" }, { \"include\": \"#async-throws\" }, { \"comment\": \"Swift 3: generic constraints after the parameters and return type\", \"include\": \"#declarations-generic-where-clause\" }] }, \"declarations-operator\": { \"begin\": \"(?:\\\\b(prefix|infix|postfix)\\\\s+)?\\\\b(operator)\\\\s+(((?<oph>[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}])(\\\\g<oph>|\\\\.|(?<opc>[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))*+)|(\\\\.(\\\\g<oph>|\\\\g<opc>|\\\\.)++))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"storage.type.function.operator.swift\" }, \"3\": { \"name\": \"entity.name.function.operator.swift\" }, \"4\": { \"comment\": \"workaround for https://github.com/microsoft/vscode-textmate/issues/140#issuecomment-1793610346\", \"name\": \"entity.name.function.operator.swift\", \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"invalid.illegal.dot-not-allowed-here.swift\" }] } }, \"end\": \"(;)|$\\\\n?|(?=//|/\\\\*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.swift\" } }, \"name\": \"meta.definition.operator.swift\", \"patterns\": [{ \"include\": \"#declarations-operator-swift2\" }, { \"include\": \"#declarations-operator-swift3\" }, { \"match\": \"((?!$|;|//|/\\\\*)\\\\S)+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] }, \"declarations-operator-swift2\": { \"begin\": \"\\\\G(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.operator.begin.swift\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.operator.end.swift\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"keyword.other.operator.associativity.swift\" } }, \"match\": \"\\\\b(associativity)\\\\s+(left|right)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"constant.numeric.integer.swift\" } }, \"match\": \"\\\\b(precedence)\\\\s+(\\\\d+)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" } }, \"match\": \"\\\\b(assignment)\\\\b\" }] }, \"declarations-operator-swift3\": { \"captures\": { \"2\": { \"name\": \"entity.other.inherited-class.swift\", \"patterns\": [{ \"include\": \"#declarations-types-precedencegroup\" }] }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"match\": \"\\\\G(:)\\\\s*((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\" }, \"declarations-parameter-clause\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.begin.swift\" } }, \"end\": \"(\\\\))(?:\\\\s*(async)\\\\b)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.end.swift\" }, \"2\": { \"name\": \"storage.modifier.async.swift\" } }, \"name\": \"meta.parameter-clause.swift\", \"patterns\": [{ \"include\": \"#declarations-parameter-list\" }] }, \"declarations-parameter-list\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"variable.parameter.function.swift\" }, \"5\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"6\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"comment\": \"External parameter labels are considered part of the function name\", \"match\": \"((?<q1>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q1>))\\\\s+((?<q2>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q2>))(?=\\\\s*:)\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.function.swift\" }, \"2\": { \"name\": \"entity.name.function.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"comment\": \"If no external label is given, the name is both the external label and the internal variable name\", \"match\": \"(((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)))(?=\\\\s*:)\" }, { \"begin\": \":\\\\s*(?!\\\\s)\", \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }, { \"match\": \":\", \"name\": \"invalid.illegal.extra-colon-in-parameter-list.swift\" }, { \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.assignment.swift\" } }, \"comment\": \"a parameter's default value\", \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"#expressions\" }] }] }] }, \"declarations-precedencegroup\": { \"begin\": \"\\\\b(precedencegroup)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*(?=\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.precedencegroup.swift\" }, \"2\": { \"name\": \"entity.name.type.precedencegroup.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?!\\\\G)\", \"name\": \"meta.definition.precedencegroup.swift\", \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.precedencegroup.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.precedencegroup.end.swift\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"entity.other.inherited-class.swift\", \"patterns\": [{ \"include\": \"#declarations-types-precedencegroup\" }] }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"match\": \"\\\\b(higherThan|lowerThan)\\\\s*:\\\\s*((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"keyword.other.operator.associativity.swift\" } }, \"match\": \"\\\\b(associativity)\\\\b(?:\\\\s*:\\\\s*(right|left|none)\\\\b)?\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.swift\" }, \"2\": { \"name\": \"constant.language.boolean.swift\" } }, \"match\": \"\\\\b(assignment)\\\\b(?:\\\\s*:\\\\s*(true|false)\\\\b)?\" }] }] }, \"declarations-protocol\": { \"begin\": \"\\\\b(protocol)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.$1.swift\" }, \"2\": { \"name\": \"entity.name.type.$1.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.definition.type.protocol.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-inheritance-clause\" }, { \"comment\": \"SE-0142: Permit where clauses to constrain associated types\", \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.end.swift\" } }, \"name\": \"meta.definition.type.body.swift\", \"patterns\": [{ \"include\": \"#declarations-protocol-protocol-method\" }, { \"include\": \"#declarations-protocol-protocol-initializer\" }, { \"include\": \"#declarations-protocol-associated-type\" }, { \"include\": \"$self\" }] }] }, \"declarations-protocol-associated-type\": { \"begin\": \"\\\\b(associatedtype)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.declaration-specifier.swift\" }, \"2\": { \"name\": \"variable.language.associatedtype.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?!\\\\G)$|(?=[;}]|$)\", \"name\": \"meta.definition.associatedtype.swift\", \"patterns\": [{ \"include\": \"#declarations-inheritance-clause\" }, { \"comment\": \"SE-0142: Permit where clauses to constrain associated types\", \"include\": \"#declarations-generic-where-clause\" }, { \"include\": \"#declarations-typealias-assignment\" }] }, \"declarations-protocol-protocol-initializer\": { \"begin\": \"(?<!\\\\.)\\\\b(init[?!]*)\\\\s*(?=\\\\(|<)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\", \"patterns\": [{ \"match\": \"(?<=[?!])[?!]+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] } }, \"end\": \"$|(?=;|//|/\\\\*|\\\\})\", \"name\": \"meta.definition.function.initializer.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#async-throws\" }, { \"comment\": \"Swift 3: generic constraints after the parameters and return type\", \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.swift\" } }, \"name\": \"invalid.illegal.function-body-not-allowed-in-protocol.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-protocol-protocol-method\": { \"begin\": \"\\\\b(func)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)|(?:((?<oph>[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}])(\\\\g<oph>|(?<opc>[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))*)|(\\\\.(\\\\g<oph>|\\\\g<opc>|\\\\.)+)))\\\\s*(?=\\\\(|<)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.swift\" }, \"2\": { \"name\": \"entity.name.function.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"$|(?=;|//|/\\\\*|\\\\})\", \"name\": \"meta.definition.function.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"include\": \"#declarations-parameter-clause\" }, { \"include\": \"#declarations-function-result\" }, { \"include\": \"#async-throws\" }, { \"comment\": \"Swift 3: generic constraints after the parameters and return type\", \"include\": \"#declarations-generic-where-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.swift\" } }, \"name\": \"invalid.illegal.function-body-not-allowed-in-protocol.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, \"declarations-type\": { \"patterns\": [{ \"begin\": \"\\\\b(class(?!\\\\s+(?:func|var|let)\\\\b)|struct|actor)\\\\b\\\\s*((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.$1.swift\" }, \"2\": { \"name\": \"entity.name.type.$1.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.definition.type.$1.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"comment\": \"Swift 3: generic constraints after the generic param list\", \"include\": \"#declarations-generic-where-clause\" }, { \"include\": \"#declarations-inheritance-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.end.swift\" } }, \"name\": \"meta.definition.type.body.swift\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"include\": \"#declarations-type-enum\" }] }, \"declarations-type-enum\": { \"begin\": \"\\\\b(enum)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.$1.swift\" }, \"2\": { \"name\": \"entity.name.type.$1.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.definition.type.$1.swift\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-generic-parameter-clause\" }, { \"comment\": \"Swift 3: generic constraints after the generic param list\", \"include\": \"#declarations-generic-where-clause\" }, { \"include\": \"#declarations-inheritance-clause\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.begin.swift\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.type.end.swift\" } }, \"name\": \"meta.definition.type.body.swift\", \"patterns\": [{ \"include\": \"#declarations-type-enum-enum-case-clause\" }, { \"include\": \"$self\" }] }] }, \"declarations-type-enum-associated-values\": { \"begin\": \"\\\\G\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.swift\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"(?:(_)|((?<q1>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*\\\\k<q1>))\\\\s+(((?<q2>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*\\\\k<q2>))\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.swift\" }, \"2\": { \"name\": \"invalid.illegal.distinct-labels-not-allowed.swift\" }, \"5\": { \"name\": \"variable.parameter.function.swift\" }, \"7\": { \"name\": \"punctuation.separator.argument-label.swift\" } }, \"end\": \"(?=[,)\\\\]])\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, { \"begin\": \"(((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*\\\\k<q>))\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.swift\" }, \"2\": { \"name\": \"variable.parameter.function.swift\" }, \"4\": { \"name\": \"punctuation.separator.argument-label.swift\" } }, \"end\": \"(?=[,)\\\\]])\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, { \"begin\": \"(?![,)\\\\]])(?=\\\\S)\", \"comment\": \"an element without a label (i.e. anything else)\", \"end\": \"(?=[,)\\\\]])\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }, { \"match\": \":\", \"name\": \"invalid.illegal.extra-colon-in-parameter-list.swift\" }] }] }, \"declarations-type-enum-enum-case\": { \"begin\": \"((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.enummember.swift\" } }, \"end\": \"(?<=\\\\))|(?![=(])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-type-enum-associated-values\" }, { \"include\": \"#declarations-type-enum-raw-value-assignment\" }] }, \"declarations-type-enum-enum-case-clause\": { \"begin\": \"\\\\b(case)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.enum.case.swift\" } }, \"end\": \"(?=[;}])|(?!\\\\G)(?!//|/\\\\*)(?=[^\\\\s,])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-type-enum-enum-case\" }, { \"include\": \"#declarations-type-enum-more-cases\" }] }, \"declarations-type-enum-more-cases\": { \"begin\": \",\\\\s*\", \"end\": \"(?!\\\\G)(?!//|/\\\\*)(?=[;}]|[^\\\\s,])\", \"name\": \"meta.enum-case.more-cases\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#declarations-type-enum-enum-case\" }, { \"include\": \"#declarations-type-enum-more-cases\" }] }, \"declarations-type-enum-raw-value-assignment\": { \"begin\": \"(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.swift\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#literals\" }] }, \"declarations-type-identifier\": { \"begin\": \"((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"meta.type-name.swift\", \"patterns\": [{ \"include\": \"#builtin-types\" }] }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?!<)\", \"patterns\": [{ \"begin\": \"(?=<)\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#declarations-generic-argument-clause\" }] }] }, \"declarations-type-operators\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.type.composition.swift\" } }, \"comment\": \"Swift 3: A & B\", \"match\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(&)(?![/=\\\\-+!*%<>&|\\\\^~.])\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.type.requirement-suppression.swift\" } }, \"comment\": \"SE-0390: Noncopyable structs and enums\", \"match\": \"(?<![/=\\\\-+!*%<>&|\\\\^~.])(~)(?![/=\\\\-+!*%<>&|\\\\^~.])\" }] }, \"declarations-typealias\": { \"begin\": \"\\\\b(typealias)\\\\s+((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.declaration-specifier.swift\" }, \"2\": { \"name\": \"entity.name.type.typealias.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"end\": \"(?!\\\\G)$|(?=;|//|/\\\\*|$)\", \"name\": \"meta.definition.typealias.swift\", \"patterns\": [{ \"begin\": \"\\\\G(?=<)\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#declarations-generic-parameter-clause\" }] }, { \"include\": \"#declarations-typealias-assignment\" }] }, \"declarations-typealias-assignment\": { \"begin\": \"(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.swift\" } }, \"end\": \"(?!\\\\G)$|(?=;|//|/\\\\*|$)\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"declarations-typed-variable-declaration\": { \"begin\": \"\\\\b(?:(async)\\\\s+)?(let|var)\\\\b\\\\s+(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>)\\\\s*:\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.swift\" }, \"2\": { \"name\": \"keyword.other.declaration-specifier.swift\" } }, \"end\": \"(?=$|[={])\", \"patterns\": [{ \"include\": \"#declarations-available-types\" }] }, \"declarations-types-precedencegroup\": { \"patterns\": [{ \"comment\": \"Precedence groups in the standard library\", \"match\": \"\\\\b(?:BitwiseShift|Assignment|RangeFormation|Casting|Addition|NilCoalescing|Comparison|LogicalConjunction|LogicalDisjunction|Default|Ternary|Multiplication|FunctionArrow)Precedence\\\\b\", \"name\": \"support.type.swift\" }] }, \"expressions\": { \"comment\": \"trailing closures need to be parsed before other member references\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references\" }, { \"include\": \"#expressions-trailing-closure\" }, { \"include\": \"#member-reference\" }] }, \"expressions-trailing-closure\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.function.any-method.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"comment\": \"foo { body } -- a call with a trailing closure and no argument clause\", \"match\": \"(#?(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))(?=\\\\s*\\\\{)\", \"name\": \"meta.function-call.trailing-closure-only.swift\" }, { \"captures\": { \"1\": { \"name\": \"support.function.any-method.trailing-closure-label.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.separator.argument-label.swift\" } }, \"comment\": \"foo: { body } -- labeled-trailing-closure (SE-0279)\", \"match\": \"((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*(:)(?=\\\\s*\\\\{)\" }] }, \"expressions-without-trailing-closures\": { \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references\" }, { \"include\": \"#member-references\" }] }, \"expressions-without-trailing-closures-or-member-references\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#code-block\" }, { \"include\": \"#attributes\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-closure-parameter\" }, { \"include\": \"#literals\" }, { \"include\": \"#operators\" }, { \"include\": \"#builtin-types\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#builtin-global-functions\" }, { \"include\": \"#builtin-properties\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-compound-name\" }, { \"include\": \"#conditionals\" }, { \"include\": \"#keywords\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-availability-condition\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-function-or-macro-call-expression\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-macro-expansion\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-subscript-expression\" }, { \"include\": \"#expressions-without-trailing-closures-or-member-references-parenthesized-expression\" }, { \"match\": \"\\\\b_\\\\b\", \"name\": \"support.variable.discard-value.swift\" }] }, \"expressions-without-trailing-closures-or-member-references-availability-condition\": { \"begin\": \"\\\\B(#(?:un)?available)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.availability-condition.swift\" }, \"2\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.platform.os.swift\" }, \"2\": { \"name\": \"constant.numeric.swift\" } }, \"match\": \"\\\\s*\\\\b((?:iOS|macOS|OSX|watchOS|tvOS|visionOS|UIKitForMac)(?:ApplicationExtension)?)\\\\b(?:\\\\s+(\\\\d+(?:\\\\.\\\\d+)*\\\\b))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.platform.all.swift\" }, \"2\": { \"name\": \"invalid.illegal.character-not-allowed-here.swift\" } }, \"match\": \"(\\\\*)\\\\s*(.*?)(?=[,)])\" }, { \"match\": \"[^\\\\s,)]+\", \"name\": \"invalid.illegal.character-not-allowed-here.swift\" }] }, \"expressions-without-trailing-closures-or-member-references-closure-parameter\": { \"match\": \"\\\\$\\\\d+\", \"name\": \"variable.language.closure-parameter.swift\" }, \"expressions-without-trailing-closures-or-member-references-compound-name\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.compound-name.swift\" }, \"2\": { \"name\": \"punctuation.definition.entity.swift\" }, \"3\": { \"name\": \"punctuation.definition.entity.swift\" }, \"4\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.swift\" }, \"2\": { \"name\": \"punctuation.definition.entity.swift\" } }, \"match\": \"(?<q>`?)(?!_:)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>):\", \"name\": \"entity.name.function.compound-name.swift\" }] } }, \"comment\": \"a reference to a function with disambiguating argument labels, such as foo(_:), foo(bar:), etc.\", \"match\": \"((?<q1>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q1>))\\\\(((((?<q2>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q2>)):)+)\\\\)\" }, \"expressions-without-trailing-closures-or-member-references-expression-element-list\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.separator.argument-label.swift\" } }, \"comment\": \"an element with a label\", \"end\": \"(?=[,)\\\\]])\", \"patterns\": [{ \"include\": \"#expressions\" }] }, { \"begin\": \"(?![,)\\\\]])(?=\\\\S)\", \"comment\": \"an element without a label (i.e. anything else)\", \"end\": \"(?=[,)\\\\]])\", \"patterns\": [{ \"include\": \"#expressions\" }] }] }, \"expressions-without-trailing-closures-or-member-references-function-or-macro-call-expression\": { \"patterns\": [{ \"begin\": \"(#?(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"4\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"comment\": \"foo(args) -- a call whose callee is a highlightable name\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.function-call.swift\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references-expression-element-list\" }] }, { \"begin\": \"(?<=[`\\\\])}>\\\\p{L}_\\\\p{N}\\\\p{M}])\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"comment\": \"[Int](args) -- a call whose callee is a more complicated expression\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.function-call.swift\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references-expression-element-list\" }] }] }, \"expressions-without-trailing-closures-or-member-references-macro-expansion\": { \"match\": \"(#(?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\", \"name\": \"support.function.any-method.swift\" }, \"expressions-without-trailing-closures-or-member-references-parenthesized-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tuple.begin.swift\" } }, \"comment\": 'correctly matching closure expressions is too hard (depends on trailing \"in\") so we just tack on some basics to the end of parenthesized-expression', \"end\": \"(\\\\))\\\\s*((?:\\\\b(?:async|throws|rethrows)\\\\s)*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.tuple.end.swift\" }, \"2\": { \"patterns\": [{ \"match\": \"\\\\brethrows\\\\b\", \"name\": \"invalid.illegal.rethrows-only-allowed-on-function-declarations.swift\" }, { \"include\": \"#async-throws\" }] } }, \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references-expression-element-list\" }] }, \"expressions-without-trailing-closures-or-member-references-subscript-expression\": { \"begin\": \"(?<=[`\\\\p{L}_\\\\p{N}\\\\p{M}])\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.swift\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"name\": \"meta.subscript-expression.swift\", \"patterns\": [{ \"include\": \"#expressions-without-trailing-closures-or-member-references-expression-element-list\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(?<!\\\\.)\\\\b(?:if|else|guard|where|switch|case|default|fallthrough)\\\\b\", \"name\": \"keyword.control.branch.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:continue|break|fallthrough|return)\\\\b\", \"name\": \"keyword.control.transfer.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:while|for|in|each)\\\\b\", \"name\": \"keyword.control.loop.swift\" }, { \"match\": \"\\\\bany\\\\b(?=\\\\s*`?[\\\\p{L}_])\", \"name\": \"keyword.other.operator.type.existential.swift\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.loop.swift\" }, \"2\": { \"name\": \"punctuation.whitespace.trailing.repeat.swift\" } }, \"comment\": \"extra scopes for repeat-while snippet\", \"match\": \"(?<!\\\\.)\\\\b(repeat)\\\\b(\\\\s*)\" }, { \"match\": \"(?<!\\\\.)\\\\bdefer\\\\b\", \"name\": \"keyword.control.defer.swift\" }, { \"captures\": { \"1\": { \"name\": \"invalid.illegal.try-must-precede-await.swift\" }, \"2\": { \"name\": \"keyword.control.await.swift\" } }, \"match\": \"(?<!\\\\.)\\\\b(?:(await\\\\s+try)|(await))\\\\b\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:catch|throw|try)\\\\b|\\\\btry[?!]\\\\B\", \"name\": \"keyword.control.exception.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:throws|rethrows)\\\\b\", \"name\": \"storage.modifier.exception.swift\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.exception.swift\" }, \"2\": { \"name\": \"punctuation.whitespace.trailing.do.swift\" } }, \"comment\": \"extra scopes for do-catch snippet\", \"match\": \"(?<!\\\\.)\\\\b(do)\\\\b(\\\\s*)\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.async.swift\" }, \"2\": { \"name\": \"keyword.other.declaration-specifier.swift\" } }, \"match\": \"(?<!\\\\.)\\\\b(?:(async)\\\\s+)?(let|var)\\\\b\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:associatedtype|operator|typealias)\\\\b\", \"name\": \"keyword.other.declaration-specifier.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(class|enum|extension|precedencegroup|protocol|struct|actor)\\\\b(?=\\\\s*`?[\\\\p{L}_])\", \"name\": \"storage.type.$1.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:inout|static|final|lazy|mutating|nonmutating|optional|indirect|required|override|dynamic|convenience|infix|prefix|postfix|distributed|nonisolated|borrowing|consuming)\\\\b\", \"name\": \"storage.modifier.swift\" }, { \"match\": \"\\\\binit[?!]|\\\\binit\\\\b|(?<!\\\\.)\\\\b(?:func|deinit|subscript|didSet|get|set|willSet)\\\\b\", \"name\": \"storage.type.function.swift\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:fileprivate|private|internal|public|open|package)\\\\b\", \"name\": \"keyword.other.declaration-specifier.accessibility.swift\" }, { \"comment\": \"matches weak, unowned, unowned(safe), unowned(unsafe)\", \"match\": \"(?<!\\\\.)\\\\bunowned\\\\((?:safe|unsafe)\\\\)|(?<!\\\\.)\\\\b(?:weak|unowned)\\\\b\", \"name\": \"keyword.other.capture-specifier.swift\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.type.swift\" }, \"2\": { \"name\": \"keyword.other.type.metatype.swift\" } }, \"match\": \"(?<=\\\\.)(?:(dynamicType|self)|(Protocol|Type))\\\\b\" }, { \"match\": \"(?<!\\\\.)\\\\b(?:super|self|Self)\\\\b\", \"name\": \"variable.language.swift\" }, { \"match\": \"\\\\B(?:#file|#filePath|#fileID|#line|#column|#function|#dsohandle)\\\\b|\\\\b(?:__FILE__|__LINE__|__COLUMN__|__FUNCTION__|__DSO_HANDLE__)\\\\b\", \"name\": \"support.variable.swift\" }, { \"match\": \"(?<!\\\\.)\\\\bimport\\\\b\", \"name\": \"keyword.control.import.swift\" }, { \"comment\": 'SE-0366: \"consume behaves as a contextual keyword. In order to avoid interfering with existing code that calls functions named consume, the operand to consume must begin with another identifier, and must consist of an identifier or postfix expression\"', \"match\": \"(?<!\\\\.)\\\\bconsume(?=\\\\s+`?[\\\\p{L}_])\", \"name\": \"keyword.control.consume.swift\" }, { \"comment\": 'SE-0377: \"copy is a contextual keyword, parsed as an operator if it is immediately followed by an identifier on the same line, like the consume x operator before it\"', \"match\": \"(?<!\\\\.)\\\\bcopy(?=\\\\s+`?[\\\\p{L}_])\", \"name\": \"keyword.control.copy.swift\" }] }, \"literals\": { \"patterns\": [{ \"include\": \"#literals-boolean\" }, { \"include\": \"#literals-numeric\" }, { \"include\": \"#literals-string\" }, { \"match\": \"\\\\bnil\\\\b\", \"name\": \"constant.language.nil.swift\" }, { \"comment\": 'object \"literals\" used in playgrounds', \"match\": \"\\\\B#(colorLiteral|imageLiteral|fileLiteral)\\\\b\", \"name\": \"support.function.object-literal.swift\" }, { \"match\": \"\\\\B#externalMacro\\\\b\", \"name\": \"support.function.builtin-macro.swift\" }, { \"match\": \"\\\\B#keyPath\\\\b\", \"name\": \"support.function.key-path.swift\" }, { \"begin\": \"\\\\B(#selector)(\\\\()(?:\\\\s*(getter|setter)\\\\s*(:))?\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.selector-reference.swift\" }, \"2\": { \"name\": \"punctuation.definition.arguments.begin.swift\" }, \"3\": { \"name\": \"support.variable.parameter.swift\" }, \"4\": { \"name\": \"punctuation.separator.argument-label.swift\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.swift\" } }, \"patterns\": [{ \"include\": \"#expressions\" }] }, { \"include\": \"#literals-regular-expression-literal\" }] }, \"literals-boolean\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.swift\" }, \"literals-numeric\": { \"patterns\": [{ \"comment\": \"0.1, -4_2.5, 6.022e23, 10E-5\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)\\\\d[0-9_]*(?=\\\\.\\\\d|[eE])(?:\\\\.\\\\d[0-9_]*)?(?:[eE][-+]?\\\\d[0-9_]*)?\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.float.decimal.swift\" }, { \"comment\": \"-0x1.ap2_3, 0x31p-4\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)(0x[0-9a-fA-F][0-9a-fA-F_]*)(?:\\\\.[0-9a-fA-F][0-9a-fA-F_]*)?[pP][-+]?\\\\d[0-9_]*\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.float.hexadecimal.swift\" }, { \"comment\": \"0x1p, 0x1p_2, 0x1.5pa, 0x1.1p+1f, 0x1pz\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)(0x[0-9a-fA-F][0-9a-fA-F_]*)(?:\\\\.[0-9a-fA-F][0-9a-fA-F_]*)?(?:[pP][-+]?\\\\w*)\\\\b(?!\\\\.\\\\d)\", \"name\": \"invalid.illegal.numeric.float.invalid-exponent.swift\" }, { \"comment\": \"0x1.5w (note that 0x1.f may be a valid expression)\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)(0x[0-9a-fA-F][0-9a-fA-F_]*)\\\\.\\\\d[\\\\w.]*\", \"name\": \"invalid.illegal.numeric.float.missing-exponent.swift\" }, { \"comment\": \"-.5, .2f (note that 1.-.5 may be a valid expression)\", \"match\": \"(?<=\\\\s|^)-?\\\\.\\\\d[\\\\w.]*\", \"name\": \"invalid.illegal.numeric.float.missing-leading-zero.swift\" }, { \"comment\": \"0b_0_1, 0x_1p+3q\", \"match\": \"(\\\\B-|\\\\b)0[box]_[0-9a-fA-F_]*(?:[pPeE][+-]?\\\\w+)?[\\\\w.]+\", \"name\": \"invalid.illegal.numeric.leading-underscore.swift\" }, { \"comment\": \"tuple positional member: not really a numeric literal, but not invalid\", \"match\": \"(?<=[\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)\\\\d+\\\\b\" }, { \"comment\": \"0b010, 0b1_0\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)0b[01][01_]*\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.integer.binary.swift\" }, { \"comment\": \"0o1, 0o7_3\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)0o[0-7][0-7_]*\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.integer.octal.swift\" }, { \"comment\": \"02, 3_456\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)\\\\d[0-9_]*\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.integer.decimal.swift\" }, { \"comment\": \"0x4, 0xF_7\", \"match\": \"(\\\\B-|\\\\b)(?<![\\\\[\\\\](){}\\\\p{L}_\\\\p{N}\\\\p{M}]\\\\.)0x[0-9a-fA-F][0-9a-fA-F_]*\\\\b(?!\\\\.\\\\d)\", \"name\": \"constant.numeric.integer.hexadecimal.swift\" }, { \"match\": \"(\\\\B-|\\\\b)\\\\d[\\\\w.]*\", \"name\": \"invalid.illegal.numeric.other.swift\" }] }, \"literals-regular-expression-literal\": { \"comment\": \"SE-0354 & SE-0355\", \"patterns\": [{ \"begin\": \"(#+)/\\\\n\", \"end\": \"/\\\\1\", \"name\": \"string.regexp.block.swift\", \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-regex-guts\" }, { \"include\": \"#literals-regular-expression-literal-line-comment\" }] }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-regex-guts\" }] }, \"1\": { \"name\": \"punctuation.definition.string.begin.regexp.swift\" }, \"12\": { \"name\": \"punctuation.definition.string.end.regexp.swift\" }, \"13\": { \"name\": \"invalid.illegal.returns-not-allowed.regexp\" } }, \"comment\": \"Single-line regular expression literals must be matched all in one go\\n in order to avoid ambiguities with operators, and to adhere to certain\\n parsing rules in SE-0354/SE-0355, such as:\\n - A regex literal will not be parsed if it contains an unbalanced ).\\n - A regex may end with a space only if it began with an escaped space\", \"match\": \"(((\\\\#+)?)/)(?(3)|(?!/))(?(3)|(?!\\\\s))(\\\\\\\\\\\\s)?(?<guts>(?>(?:\\\\\\\\Q(?:(?!\\\\\\\\E)(?!/\\\\2).)*+(?:\\\\\\\\E|(?(3)|(?<!\\\\s))(?=/\\\\2))|\\\\\\\\.|\\\\(\\\\?\\\\#[^)]*\\\\)|\\\\(\\\\?\\\\{(?<g1>\\\\{)?+(?<g2>\\\\{)?+(?<g3>\\\\{)?+(?<g4>\\\\{)?+(?<g5>\\\\{)?+.+?\\\\}(?(<g1>)\\\\})(?(<g2>)\\\\})(?(<g3>)\\\\})(?(<g4>)\\\\})(?(<g5>)\\\\})(?:\\\\[(?!\\\\d)\\\\w+\\\\])?[X<>]?\\\\)|(?<class>\\\\[(?:\\\\\\\\.|[^\\\\[\\\\]]|\\\\g<class>)+\\\\])|\\\\(\\\\g<guts>?+\\\\)|(?:(?!/\\\\2)[^()\\\\[\\\\\\\\])+)+))?+(?(3)|(?(5)(?<!\\\\s)))(/\\\\2)|\\\\#+/.+(\\\\n)\", \"name\": \"string.regexp.line.swift\" }] }, \"literals-regular-expression-literal-backreference-or-subpattern\": { \"comment\": \"These patterns are separated to work around issues like https://github.com/microsoft/vscode-textmate/issues/164\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"2\": { \"name\": \"variable.other.group-name.regexp\" }, \"3\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"5\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"6\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"7\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"8\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"comment\": \"'\\\\g{' NamedOrNumberRef '}'\", \"match\": \"(\\\\\\\\g\\\\{)(?:((?!\\\\d)\\\\w+)(?:([+-])(\\\\d+))?|([+-]?\\\\d+)(?:([+-])(\\\\d+))?)(\\\\})\" }, { \"captures\": { \"1\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"2\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"3\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" } }, \"comment\": \"'\\\\g' NumberRef\", \"match\": \"(\\\\\\\\g)([+-]?\\\\d+)(?:([+-])(\\\\d+))?\" }, { \"captures\": { \"1\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"3\": { \"name\": \"variable.other.group-name.regexp\" }, \"4\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"5\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"6\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"7\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"8\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"9\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"comment\": `'\\\\k<' NamedOrNumberRef '>'\n \"\\\\k'\" NamedOrNumberRef \"'\"\n '\\\\g<' NamedOrNumberRef '>'\n \"\\\\g'\" NamedOrNumberRef \"'\"`, \"match\": \"(\\\\\\\\[gk](<)|\\\\\\\\[gk]')(?:((?!\\\\d)\\\\w+)(?:([+-])(\\\\d+))?|([+-]?\\\\d+)(?:([+-])(\\\\d+))?)((?(2)>|'))\" }, { \"captures\": { \"1\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"2\": { \"name\": \"variable.other.group-name.regexp\" }, \"3\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"5\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"comment\": \"'\\\\k{' NamedRef '}'\", \"match\": \"(\\\\\\\\k\\\\{)((?!\\\\d)\\\\w+)(?:([+-])(\\\\d+))?(\\\\})\" }, { \"match\": \"\\\\\\\\[1-9]\\\\d+\", \"name\": \"keyword.other.back-reference.regexp\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.back-reference.regexp\" }, \"2\": { \"name\": \"variable.other.group-name.regexp\" }, \"3\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"5\": { \"name\": \"keyword.other.back-reference.regexp\" } }, \"comment\": \"'(?P=' NamedRef ')'\", \"match\": \"(\\\\(\\\\?(?:P[=>]|&))((?!\\\\d)\\\\w+)(?:([+-])(\\\\d+))?(\\\\))\" }, { \"match\": \"\\\\(\\\\?R\\\\)\", \"name\": \"keyword.other.back-reference.regexp\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.back-reference.regexp\" }, \"2\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"3\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"5\": { \"name\": \"keyword.other.back-reference.regexp\" } }, \"comment\": \"'(?' NumberRef ')'\", \"match\": \"(\\\\(\\\\?)([+-]?\\\\d+)(?:([+-])(\\\\d+))?(\\\\))\" }] }, \"literals-regular-expression-literal-backtracking-directive-or-global-matching-option\": { \"captures\": { \"1\": { \"name\": \"keyword.control.directive.regexp\" }, \"2\": { \"name\": \"keyword.control.directive.regexp\" }, \"3\": { \"name\": \"keyword.control.directive.regexp\" }, \"4\": { \"name\": \"variable.language.tag.regexp\" }, \"5\": { \"name\": \"keyword.control.directive.regexp\" }, \"6\": { \"name\": \"keyword.operator.assignment.regexp\" }, \"7\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"8\": { \"name\": \"keyword.control.directive.regexp\" }, \"9\": { \"name\": \"keyword.control.directive.regexp\" } }, \"match\": \"(\\\\(\\\\*)(?:(ACCEPT|FAIL|F|MARK(?=:)|(?=:)|COMMIT|PRUNE|SKIP|THEN)(?:(:)([^)]+))?|(?:(LIMIT_(?:DEPTH|HEAP|MATCH))(=)(\\\\d+))|(CRLF|CR|ANYCRLF|ANY|LF|NUL|BSR_ANYCRLF|BSR_UNICODE|NOTEMPTY_ATSTART|NOTEMPTY|NO_AUTO_POSSESS|NO_DOTSTAR_ANCHOR|NO_JIT|NO_START_OPT|UTF|UCP))(\\\\))\" }, \"literals-regular-expression-literal-callout\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"keyword.control.callout.regexp\" }, \"3\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"4\": { \"name\": \"entity.name.function.callout.regexp\" }, \"5\": { \"name\": \"entity.name.function.callout.regexp\" }, \"6\": { \"name\": \"entity.name.function.callout.regexp\" }, \"7\": { \"name\": \"entity.name.function.callout.regexp\" }, \"8\": { \"name\": \"entity.name.function.callout.regexp\" }, \"9\": { \"name\": \"entity.name.function.callout.regexp\" }, \"10\": { \"name\": \"entity.name.function.callout.regexp\" }, \"11\": { \"name\": \"entity.name.function.callout.regexp\" }, \"12\": { \"name\": \"punctuation.definition.group.regexp\" }, \"13\": { \"name\": \"punctuation.definition.group.regexp\" }, \"14\": { \"name\": \"keyword.control.callout.regexp\" }, \"15\": { \"name\": \"entity.name.function.callout.regexp\" }, \"16\": { \"name\": \"variable.language.tag-name.regexp\" }, \"17\": { \"name\": \"punctuation.definition.group.regexp\" }, \"18\": { \"name\": \"punctuation.definition.group.regexp\" }, \"19\": { \"name\": \"keyword.control.callout.regexp\" }, \"26\": { \"name\": \"variable.language.tag-name.regexp\" }, \"27\": { \"name\": \"keyword.control.callout.regexp\" }, \"28\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"match\": \"(\\\\()(?<keyw>\\\\?C)(?:(?<num>\\\\d+)|`(?<name>(?:[^`]|``)*)`|'(?<name>(?:[^']|'')*)'|\\\"(?<name>(?:[^\\\"]|\\\"\\\")*)\\\"|\\\\^(?<name>(?:[^\\\\^]|\\\\^\\\\^)*)\\\\^|%(?<name>(?:[^%]|%%)*)%|\\\\#(?<name>(?:[^#]|\\\\#\\\\#)*)\\\\#|\\\\$(?<name>(?:[^$]|\\\\$\\\\$)*)\\\\$|\\\\{(?<name>(?:[^}]|\\\\}\\\\})*)\\\\})?(\\\\))|(\\\\()(?<keyw>\\\\*)(?<name>(?!\\\\d)\\\\w+)(?:\\\\[(?<tag>(?!\\\\d)\\\\w+)\\\\])?(?:\\\\{[^,}]+(?:,[^,}]+)*\\\\})?(\\\\))|(\\\\()(?<keyw>\\\\?)(\\\\{(?<g1>\\\\{)?+(?<g2>\\\\{)?+(?<g3>\\\\{)?+(?<g4>\\\\{)?+(?<g5>\\\\{)?+).+?\\\\}(?(<g1>)\\\\})(?(<g2>)\\\\})(?(<g3>)\\\\})(?(<g4>)\\\\})(?(<g5>)\\\\})(?:\\\\[(?<tag>(?!\\\\d)\\\\w+)\\\\])?(?<keyw>[X<>]?)(\\\\))\", \"name\": \"meta.callout.regexp\" }, \"literals-regular-expression-literal-character-properties\": { \"captures\": { \"1\": { \"name\": \"support.variable.character-property.regexp\" }, \"2\": { \"name\": \"punctuation.definition.character-class.regexp\" }, \"3\": { \"name\": \"support.variable.character-property.regexp\" }, \"4\": { \"name\": \"punctuation.definition.character-class.regexp\" } }, \"match\": \"\\\\\\\\[pP]\\\\{([\\\\s\\\\w-]+(?:=[\\\\s\\\\w-]+)?)\\\\}|(\\\\[:)([\\\\s\\\\w-]+(?:=[\\\\s\\\\w-]+)?)(:\\\\])\", \"name\": \"constant.other.character-class.set.regexp\" }, \"literals-regular-expression-literal-custom-char-class\": { \"patterns\": [{ \"begin\": \"(\\\\[)(\\\\^)?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.regexp\" } }, \"name\": \"constant.other.character-class.set.regexp\", \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-custom-char-class-members\" }] }] }, \"literals-regular-expression-literal-custom-char-class-members\": { \"comment\": \"TODO: should also include atoms?\", \"patterns\": [{ \"comment\": \"\\\\b inside a character class represents a backspace\", \"match\": \"\\\\\\\\b\", \"name\": \"constant.character.escape.backslash.regexp\" }, { \"include\": \"#literals-regular-expression-literal-custom-char-class\" }, { \"include\": \"#literals-regular-expression-literal-quote\" }, { \"include\": \"#literals-regular-expression-literal-set-operators\" }, { \"include\": \"#literals-regular-expression-literal-unicode-scalars\" }, { \"include\": \"#literals-regular-expression-literal-character-properties\" }] }, \"literals-regular-expression-literal-group-option-toggle\": { \"comment\": 'A matching option sequence may be part of an \"isolated group\" which has an implicit scope that wraps the remaining elements of the current group', \"match\": \"\\\\(\\\\?(?:\\\\^(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*|(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})+|(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*-(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*)\\\\)\", \"name\": \"keyword.other.option-toggle.regexp\" }, \"literals-regular-expression-literal-group-or-conditional\": { \"patterns\": [{ \"begin\": \"(\\\\()(\\\\?~)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"keyword.control.conditional.absent.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.absent.regexp\", \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-regex-guts\" }] }, { \"begin\": \"(\\\\()(?<cond>\\\\?\\\\()(?:(?<NumberRef>(?<num>[+-]?\\\\d+)(?:(?<op>[+-])(?<num>\\\\d+))?)|(?<cond>R)\\\\g<NumberRef>?|(?<cond>R&)(?<NamedRef>(?<name>(?!\\\\d)\\\\w+)(?:(?<op>[+-])(?<num>\\\\d+))?)|(?<cond><)(?:\\\\g<NamedRef>|\\\\g<NumberRef>)(?<cond>>)|(?<cond>')(?:\\\\g<NamedRef>|\\\\g<NumberRef>)(?<cond>')|(?<cond>DEFINE)|(?<cond>VERSION)(?<compar>>?=)(?<num>\\\\d+\\\\.\\\\d+))(?<cond>\\\\))|(\\\\()(?<cond>\\\\?)(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"keyword.control.conditional.regexp\" }, \"4\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"5\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"6\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"7\": { \"name\": \"keyword.control.conditional.regexp\" }, \"8\": { \"name\": \"keyword.control.conditional.regexp\" }, \"10\": { \"name\": \"variable.other.group-name.regexp\" }, \"11\": { \"name\": \"keyword.operator.recursion-level.regexp\" }, \"12\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"13\": { \"name\": \"keyword.control.conditional.regexp\" }, \"14\": { \"name\": \"keyword.control.conditional.regexp\" }, \"15\": { \"name\": \"keyword.control.conditional.regexp\" }, \"16\": { \"name\": \"keyword.control.conditional.regexp\" }, \"17\": { \"name\": \"keyword.control.conditional.regexp\" }, \"18\": { \"name\": \"keyword.control.conditional.regexp\" }, \"19\": { \"name\": \"keyword.operator.comparison.regexp\" }, \"20\": { \"name\": \"constant.numeric.integer.decimal.regexp\" }, \"21\": { \"name\": \"keyword.control.conditional.regexp\" }, \"22\": { \"name\": \"punctuation.definition.group.regexp\" }, \"23\": { \"name\": \"keyword.control.conditional.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.conditional.regexp\", \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-regex-guts\" }] }, { \"begin\": \"(\\\\()((\\\\?)(?:([:|>=!*]|<[=!*])|P?<(?:((?!\\\\d)\\\\w+)(-))?((?!\\\\d)\\\\w+)>|'(?:((?!\\\\d)\\\\w+)(-))?((?!\\\\d)\\\\w+)'|(?:\\\\^(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*|(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})+|(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*-(?:[iJmnsUxwDPSW]|xx|y\\\\{[gw]\\\\})*):)|\\\\*(atomic|pla|positive_lookahead|nla|negative_lookahead|plb|positive_lookbehind|nlb|negative_lookbehind|napla|non_atomic_positive_lookahead|naplb|non_atomic_positive_lookbehind|sr|script_run|asr|atomic_script_run):)?+\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"keyword.other.group-options.regexp\" }, \"3\": { \"name\": \"punctuation.definition.group.regexp\" }, \"4\": { \"name\": \"punctuation.definition.group.regexp\" }, \"5\": { \"name\": \"variable.other.group-name.regexp\" }, \"6\": { \"name\": \"keyword.operator.balancing-group.regexp\" }, \"7\": { \"name\": \"variable.other.group-name.regexp\" }, \"8\": { \"name\": \"variable.other.group-name.regexp\" }, \"9\": { \"name\": \"keyword.operator.balancing-group.regexp\" }, \"10\": { \"name\": \"variable.other.group-name.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.regexp\", \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-regex-guts\" }] }] }, \"literals-regular-expression-literal-line-comment\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.regexp\" } }, \"match\": \"(\\\\#).*$\", \"name\": \"comment.line.regexp\" }, \"literals-regular-expression-literal-quote\": { \"begin\": \"\\\\\\\\Q\", \"beginCaptures\": { \"0\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"end\": \"\\\\\\\\E|(\\\\n)\", \"endCaptures\": { \"0\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"1\": { \"name\": \"invalid.illegal.returns-not-allowed.regexp\" } }, \"name\": \"string.quoted.other.regexp.swift\" }, \"literals-regular-expression-literal-regex-guts\": { \"patterns\": [{ \"include\": \"#literals-regular-expression-literal-quote\" }, { \"begin\": \"\\\\(\\\\?\\\\#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.regexp\" } }, \"name\": \"comment.block.regexp\" }, { \"begin\": \"<\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.regexp\" } }, \"end\": \"\\\\}>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.regexp\" } }, \"name\": \"meta.embedded.expression.regexp\" }, { \"include\": \"#literals-regular-expression-literal-unicode-scalars\" }, { \"include\": \"#literals-regular-expression-literal-character-properties\" }, { \"match\": \"[$^]|\\\\\\\\[AbBGyYzZ]|\\\\\\\\K\", \"name\": \"keyword.control.anchor.regexp\" }, { \"include\": \"#literals-regular-expression-literal-backtracking-directive-or-global-matching-option\" }, { \"include\": \"#literals-regular-expression-literal-callout\" }, { \"include\": \"#literals-regular-expression-literal-backreference-or-subpattern\" }, { \"match\": \"\\\\.|\\\\\\\\[CdDhHNORsSvVwWX]\", \"name\": \"constant.character.character-class.regexp\" }, { \"match\": \"\\\\\\\\c.\", \"name\": \"constant.character.entity.control-character.regexp\" }, { \"match\": \"\\\\\\\\[^c]\", \"name\": \"constant.character.escape.backslash.regexp\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.or.regexp\" }, { \"match\": \"[*+?]\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"match\": \"\\\\{\\\\s*\\\\d+\\\\s*(?:,\\\\s*\\\\d*\\\\s*)?\\\\}|\\\\{\\\\s*,\\\\s*\\\\d+\\\\s*\\\\}\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"include\": \"#literals-regular-expression-literal-custom-char-class\" }, { \"include\": \"#literals-regular-expression-literal-group-option-toggle\" }, { \"include\": \"#literals-regular-expression-literal-group-or-conditional\" }] }, \"literals-regular-expression-literal-set-operators\": { \"patterns\": [{ \"match\": \"&&\", \"name\": \"keyword.operator.intersection.regexp.swift\" }, { \"match\": \"--\", \"name\": \"keyword.operator.subtraction.regexp.swift\" }, { \"match\": \"\\\\~\\\\~\", \"name\": \"keyword.operator.symmetric-difference.regexp.swift\" }] }, \"literals-regular-expression-literal-unicode-scalars\": { \"match\": \"\\\\\\\\u\\\\{\\\\s*(?:[0-9a-fA-F]+\\\\s*)+\\\\}|\\\\\\\\u[0-9a-fA-F]{4}|\\\\\\\\x\\\\{[0-9a-fA-F]+\\\\}|\\\\\\\\x[0-9a-fA-F]{0,2}|\\\\\\\\U[0-9a-fA-F]{8}|\\\\\\\\o\\\\{[0-7]+\\\\}|\\\\\\\\0[0-7]{0,3}|\\\\\\\\N\\\\{(?:U\\\\+[0-9a-fA-F]{1,8}|[\\\\s\\\\w-]+)\\\\}\", \"name\": \"constant.character.numeric.regexp\" }, \"literals-string\": { \"patterns\": [{ \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.swift\" } }, \"comment\": \"SE-0168: Multi-Line String Literals\", \"end\": '\"\"\"(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.block.swift\", \"patterns\": [{ \"match\": '\\\\G.+(?=\"\"\")|\\\\G.+', \"name\": \"invalid.illegal.content-after-opening-delimiter.swift\" }, { \"match\": \"\\\\\\\\\\\\s*\\\\n\", \"name\": \"constant.character.escape.newline.swift\" }, { \"include\": \"#literals-string-string-guts\" }, { \"comment\": 'Allow \\\\(\"\"\"...\"\"\") to appear inside a block string', \"match\": '\\\\S((?!\\\\\\\\\\\\().)*(?=\"\"\")', \"name\": \"invalid.illegal.content-before-closing-delimiter.swift\" }] }, { \"begin\": '#\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.swift\" } }, \"end\": '\"\"\"#(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.block.raw.swift\", \"patterns\": [{ \"match\": '\\\\G.+(?=\"\"\")|\\\\G.+', \"name\": \"invalid.illegal.content-after-opening-delimiter.swift\" }, { \"match\": \"\\\\\\\\#\\\\s*\\\\n\", \"name\": \"constant.character.escape.newline.swift\" }, { \"include\": \"#literals-string-raw-string-guts\" }, { \"comment\": 'Allow \\\\(\"\"\"...\"\"\") to appear inside a block string', \"match\": '\\\\S((?!\\\\\\\\#\\\\().)*(?=\"\"\")', \"name\": \"invalid.illegal.content-before-closing-delimiter.swift\" }] }, { \"begin\": '(##+)\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.swift\" } }, \"end\": '\"\"\"\\\\1(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.block.raw.swift\", \"patterns\": [{ \"match\": '\\\\G.+(?=\"\"\")|\\\\G.+', \"name\": \"invalid.illegal.content-after-opening-delimiter.swift\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.swift\" } }, \"end\": '\"(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.single-line.swift\", \"patterns\": [{ \"match\": \"\\\\r|\\\\n\", \"name\": \"invalid.illegal.returns-not-allowed.swift\" }, { \"include\": \"#literals-string-string-guts\" }] }, { \"begin\": '(##+)\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.raw.swift\" } }, \"comment\": \"SE-0168: raw string literals (more than one #, grammar limitations prevent us from supporting escapes)\", \"end\": '\"\\\\1(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.raw.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.single-line.raw.swift\", \"patterns\": [{ \"match\": \"\\\\r|\\\\n\", \"name\": \"invalid.illegal.returns-not-allowed.swift\" }] }, { \"begin\": '#\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.raw.swift\" } }, \"comment\": \"SE-0168: raw string literals (one #, escapes supported)\", \"end\": '\"#(#*)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.raw.swift\" }, \"1\": { \"name\": \"invalid.illegal.extra-closing-delimiter.swift\" } }, \"name\": \"string.quoted.double.single-line.raw.swift\", \"patterns\": [{ \"match\": \"\\\\r|\\\\n\", \"name\": \"invalid.illegal.returns-not-allowed.swift\" }, { \"include\": \"#literals-string-raw-string-guts\" }] }] }, \"literals-string-raw-string-guts\": { \"comment\": \"the same as #string-guts but with # in escapes\", \"patterns\": [{ \"match\": `\\\\\\\\#[0\\\\\\\\tnr\"']`, \"name\": \"constant.character.escape.swift\" }, { \"match\": \"\\\\\\\\#u\\\\{[0-9a-fA-F]{1,8}\\\\}\", \"name\": \"constant.character.escape.unicode.swift\" }, { \"begin\": \"\\\\\\\\#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.swift\" } }, \"contentName\": \"source.swift\", \"end\": \"(\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.swift\" }, \"1\": { \"name\": \"source.swift\" } }, \"name\": \"meta.embedded.line.swift\", \"patterns\": [{ \"include\": \"$self\" }, { \"begin\": \"\\\\(\", \"comment\": \"Nested parens\", \"end\": \"\\\\)\" }] }, { \"match\": \"\\\\\\\\#.\", \"name\": \"invalid.illegal.escape-not-recognized\" }] }, \"literals-string-string-guts\": { \"patterns\": [{ \"match\": `\\\\\\\\[0\\\\\\\\tnr\"']`, \"name\": \"constant.character.escape.swift\" }, { \"match\": \"\\\\\\\\u\\\\{[0-9a-fA-F]{1,8}\\\\}\", \"name\": \"constant.character.escape.unicode.swift\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.swift\" } }, \"contentName\": \"source.swift\", \"end\": \"(\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.swift\" }, \"1\": { \"name\": \"source.swift\" } }, \"name\": \"meta.embedded.line.swift\", \"patterns\": [{ \"include\": \"$self\" }, { \"begin\": \"\\\\(\", \"comment\": \"Nested parens\", \"end\": \"\\\\)\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.escape-not-recognized\" }] }, \"member-reference\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.swift\" }, \"2\": { \"name\": \"punctuation.definition.identifier.swift\" }, \"3\": { \"name\": \"punctuation.definition.identifier.swift\" } }, \"match\": \"(?<=\\\\.)((?<q>`?)[\\\\p{L}_][\\\\p{L}_\\\\p{N}\\\\p{M}]*(\\\\k<q>))\" }] }, \"operators\": { \"patterns\": [{ \"comment\": \"Type casting\", \"match\": \"\\\\b(is\\\\b|as([!?]\\\\B|\\\\b))\", \"name\": \"keyword.operator.type-casting.swift\" }, { \"begin\": \"(?=(?<oph>[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}])|\\\\.(\\\\g<oph>|\\\\.|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))\", \"comment\": \"This rule helps us speed up the matching.\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"\\\\G(\\\\+\\\\+|--)$\", \"name\": \"keyword.operator.increment-or-decrement.swift\" }, { \"match\": \"\\\\G(\\\\+|-)$\", \"name\": \"keyword.operator.arithmetic.unary.swift\" }, { \"match\": \"\\\\G!$\", \"name\": \"keyword.operator.logical.not.swift\" }, { \"match\": \"\\\\G~$\", \"name\": \"keyword.operator.bitwise.not.swift\" }, { \"match\": \".+\", \"name\": \"keyword.operator.custom.prefix.swift\" }] } }, \"comment\": \"Prefix unary operator\", \"match\": \"\\\\G(?<=^|[\\\\s(\\\\[{,;:])((?!(//|/\\\\*|\\\\*/))([/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++(?![\\\\s)\\\\]},;:]|\\\\z)\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"\\\\G(\\\\+\\\\+|--)$\", \"name\": \"keyword.operator.increment-or-decrement.swift\" }, { \"match\": \"\\\\G!$\", \"name\": \"keyword.operator.increment-or-decrement.swift\" }, { \"match\": \".+\", \"name\": \"keyword.operator.custom.postfix.swift\" }] } }, \"comment\": \"Postfix unary operator\", \"match\": \"\\\\G(?<!^|[\\\\s(\\\\[{,;:])((?!(//|/\\\\*|\\\\*/))([/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++(?=[\\\\s)\\\\]},;:]|\\\\z)\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"\\\\G=$\", \"name\": \"keyword.operator.assignment.swift\" }, { \"match\": \"\\\\G(\\\\+|-|\\\\*|/|%|<<|>>|&|\\\\^|\\\\||&&|\\\\|\\\\|)=$\", \"name\": \"keyword.operator.assignment.compound.swift\" }, { \"match\": \"\\\\G(\\\\+|-|\\\\*|/)$\", \"name\": \"keyword.operator.arithmetic.swift\" }, { \"match\": \"\\\\G&(\\\\+|-|\\\\*)$\", \"name\": \"keyword.operator.arithmetic.overflow.swift\" }, { \"match\": \"\\\\G%$\", \"name\": \"keyword.operator.arithmetic.remainder.swift\" }, { \"match\": \"\\\\G(==|!=|>|<|>=|<=|~=)$\", \"name\": \"keyword.operator.comparison.swift\" }, { \"match\": \"\\\\G\\\\?\\\\?$\", \"name\": \"keyword.operator.coalescing.swift\" }, { \"match\": \"\\\\G(&&|\\\\|\\\\|)$\", \"name\": \"keyword.operator.logical.swift\" }, { \"match\": \"\\\\G(&|\\\\||\\\\^|<<|>>)$\", \"name\": \"keyword.operator.bitwise.swift\" }, { \"match\": \"\\\\G(===|!==)$\", \"name\": \"keyword.operator.bitwise.swift\" }, { \"match\": \"\\\\G\\\\?$\", \"name\": \"keyword.operator.ternary.swift\" }, { \"match\": \".+\", \"name\": \"keyword.operator.custom.infix.swift\" }] } }, \"comment\": \"Infix operator\", \"match\": \"\\\\G((?!(//|/\\\\*|\\\\*/))([/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \".+\", \"name\": \"keyword.operator.custom.prefix.dot.swift\" }] } }, \"comment\": \"Dot prefix unary operator\", \"match\": \"\\\\G(?<=^|[\\\\s(\\\\[{,;:])\\\\.((?!(//|/\\\\*|\\\\*/))(\\\\.|[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++(?![\\\\s)\\\\]},;:]|\\\\z)\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \".+\", \"name\": \"keyword.operator.custom.postfix.dot.swift\" }] } }, \"comment\": \"Dot postfix unary operator\", \"match\": \"\\\\G(?<!^|[\\\\s(\\\\[{,;:])\\\\.((?!(//|/\\\\*|\\\\*/))(\\\\.|[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++(?=[\\\\s)\\\\]},;:]|\\\\z)\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"\\\\G\\\\.\\\\.[.<]$\", \"name\": \"keyword.operator.range.swift\" }, { \"match\": \".+\", \"name\": \"keyword.operator.custom.infix.dot.swift\" }] } }, \"comment\": \"Dot infix operator\", \"match\": \"\\\\G\\\\.((?!(//|/\\\\*|\\\\*/))(\\\\.|[/=\\\\-+!*%<>&|^~?]|[\\\\x{00A1}-\\\\x{00A7}]|[\\\\x{00A9}\\\\x{00AB}]|[\\\\x{00AC}\\\\x{00AE}]|[\\\\x{00B0}-\\\\x{00B1}\\\\x{00B6}\\\\x{00BB}\\\\x{00BF}\\\\x{00D7}\\\\x{00F7}]|[\\\\x{2016}-\\\\x{2017}\\\\x{2020}-\\\\x{2027}]|[\\\\x{2030}-\\\\x{203E}]|[\\\\x{2041}-\\\\x{2053}]|[\\\\x{2055}-\\\\x{205E}]|[\\\\x{2190}-\\\\x{23FF}]|[\\\\x{2500}-\\\\x{2775}]|[\\\\x{2794}-\\\\x{2BFF}]|[\\\\x{2E00}-\\\\x{2E7F}]|[\\\\x{3001}-\\\\x{3003}]|[\\\\x{3008}-\\\\x{3030}]|[\\\\x{0300}-\\\\x{036F}]|[\\\\x{1DC0}-\\\\x{1DFF}]|[\\\\x{20D0}-\\\\x{20FF}]|[\\\\x{FE00}-\\\\x{FE0F}]|[\\\\x{FE20}-\\\\x{FE2F}]|[\\\\x{E0100}-\\\\x{E01EF}]))++\" }] }, { \"match\": \":\", \"name\": \"keyword.operator.ternary.swift\" }] }, \"root\": { \"patterns\": [{ \"include\": \"#compiler-control\" }, { \"include\": \"#declarations\" }, { \"include\": \"#expressions\" }] } }, \"scopeName\": \"source.swift\" });\nvar swift = [\n  lang\n];\n\nexport { swift as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA0E;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAyB;4BAAE;4BAAG,SAAS;wBAA2H;wBAAG;4BAAE,SAAS;4BAAmD,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,SAAS;oCAA0B,QAAQ;gCAAyB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAwC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAmD;4BAAE;4BAAG,SAAS;wBAAoE;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,SAAS;4BAA8B,QAAQ;wBAA6B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAuB,OAAO;oBAAc,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA+C;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAe;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAmH,SAAS;oBAAkQ,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAuD,SAAS;oBAAkmE,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAA4D,SAAS;oBAA2V,QAAQ;gBAAyB;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;oBAAqG,SAAS;oBAAsD,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAyC,SAAS;oBAAib,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAA8C,SAAS;oBAAoN,QAAQ;gBAAyB;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAoI,SAAS;oBAA+F,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAiD,SAAS;oBAAqmC,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAsD,SAAS;oBAA6F,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAyG,SAAS;oBAA4d,QAAQ;gBAAyB;aAAE;QAAC;QAAG,iBAAiB;YAAE,WAAW;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAuC;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAyB;aAAE;QAAC;QAAG,oCAAoC;YAAE,WAAW;YAAuB,SAAS;YAAsE,QAAQ;QAAsB;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAuD,SAAS;oBAAwC,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAoD,SAAS;oBAAe,QAAQ;gBAA+B;gBAAG;oBAAE,WAAW;oBAAiD,SAAS;oBAA8V,QAAQ;gBAAqB;gBAAG;oBAAE,WAAW;oBAAsD,SAAS;oBAA+C,QAAQ;gBAAqB;aAAE;QAAC;QAAG,uCAAuC;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAgD,SAAS;oBAA83B,QAAQ;gBAAqB;gBAAG;oBAAE,WAAW;oBAAqD,SAAS;oBAA+xB,QAAQ;gBAAqB;aAAE;QAAC;QAAG,qCAAqC;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAA8C,SAAS;oBAAg1D,QAAQ;gBAAqB;gBAAG;oBAAE,WAAW;oBAAmD,SAAS;oBAAmU,QAAQ;gBAAqB;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAkD,SAAS;oBAAymB,QAAQ;gBAAqB;gBAAG;oBAAE,WAAW;oBAAuD,SAAS;oBAAsD,QAAQ;gBAAqB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;oBAAmB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAwD;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAK,QAAQ;wBAAgD;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAK,QAAQ;wBAAgD;wBAAG;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAK,QAAQ;wBAAkC;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAQ,OAAO;YAAQ,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,eAAe;oBAAoC,OAAO;gBAAqC;gBAAG;oBAAE,SAAS;oBAA2B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,OAAO;oBAAyB,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAsB,QAAQ;wBAAkC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;gCAAG,KAAK;oCAAE,QAAQ;gCAA+C;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,SAAS;wBAA8F;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,SAAS;wBAAgH;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;gCAAG,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,SAAS;wBAAgE;wBAAG;4BAAE,SAAS;4BAAmC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,OAAO;4BAAW,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,SAAS;oCAAiC,QAAQ;gCAA8C;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAgC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,OAAO;4BAAW,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,SAAS;oCAAQ,QAAQ;gCAAoC;gCAAG;oCAAE,SAAS;oCAA0B,QAAQ;gCAAyB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAQ,QAAQ;gCAAmD;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA0C,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA2D;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAA0B,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAAwC;oCAAE;oCAAG,OAAO;oCAAW,YAAY;wCAAC;4CAAE,WAAW;wCAAY;qCAAE;gCAAC;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAAwC;wCAAG,KAAK;4CAAE,QAAQ;wCAAiC;oCAAE;oCAAG,SAAS;gCAA0B;gCAAG;oCAAE,SAAS;oCAAK,QAAQ;gCAAyC;gCAAG;oCAAE,SAAS;oCAAQ,QAAQ;gCAAmD;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAQ,QAAQ;gCAAmD;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAiE,QAAQ;gBAAyC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAyC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyB,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;oBAAE;oBAAG,WAAW;oBAAgG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAyC;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAA2C;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAgD;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgD,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;gBAAwD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAkB,SAAS;gBAAuD;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAqD;gBAAG;oBAAE,WAAW;oBAA2B,SAAS;oBAAkB,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAA2C;gBAAG;oBAAE,WAAW;gBAAgD;gBAAG;oBAAE,WAAW;gBAAwC;aAAE;QAAC;QAAG,gDAAgD;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,WAAW;YAAuD,OAAO;YAAkB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAkB,YAAY;wBAAC;4BAAE,SAAS;4BAAK,QAAQ;wBAAuD;wBAAG;4BAAE,WAAW;wBAAgC;qBAAE;gBAAC;aAAE;QAAC;QAAG,2CAA2C;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAoB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAuE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;oBAAqC,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAypB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAc,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;oBAAqE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAAkB,QAAQ;wBAAmD;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAc,QAAQ;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;oBAAqE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAA6D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAoC,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAc,QAAQ;YAA4C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,wCAAwC;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;YAAE;YAAG,OAAO;YAAkB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,yCAAyC;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAA6B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;oBAAsD,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAA8B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAiD;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,OAAO;oBAA+B,QAAQ;oBAA2C,YAAY;wBAAC;4BAAE,SAAS;4BAAO,OAAO;4BAA+B,QAAQ;4BAAsC,YAAY;gCAAC;oCAAE,WAAW;gCAAgC;gCAAG;oCAAE,WAAW;gCAA+B;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAkC,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsD;aAAE;QAAC;QAAG,sDAAsD;YAAE,SAAS;YAAa,OAAO;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,SAAS;oBAAyD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,OAAO;oBAA8B,QAAQ;oBAAyD,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,OAAO;oBAA8B,QAAQ;oBAA2D,YAAY;wBAAC;4BAAE,SAAS;4BAAW,eAAe;4BAAsC,OAAO;4BAA8B,YAAY;gCAAC;oCAAE,WAAW;gCAAgC;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAA2B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,SAAS;oBAAoF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAmB,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,SAAS;4BAA+D,QAAQ;wBAAyB;wBAAG;4BAAE,SAAS;4BAAuB,QAAQ;wBAAyB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAO,QAAQ;wCAA6C;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;4BAAgnB,QAAQ;wBAAyB;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAqC;wBAAG;4BAAE,SAAS;4BAAyB,OAAO;4BAAyB,QAAQ;wBAAmD;qBAAE;gBAAC;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAA0B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;gBAAG,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAyC,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAA8C;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAkD;wBAAG;4BAAE,WAAW;wBAA8C;wBAAG;4BAAE,WAAW;wBAA+B;qBAAE;gBAAC;aAAE;QAAC;QAAG,kDAAkD;YAAE,SAAS;YAAkB,OAAO;YAAW,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAS,OAAO;YAAoD,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkD;gBAAG;oBAAE,WAAW;gBAA8C;gBAAG;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAkF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAyB,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;oBAAqE,WAAW;gBAAqC;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAwoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,WAAW;oBAAkG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAA6C;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAmD;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,SAAS;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAAqB;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,WAAW;wBAAsC;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAA8D;QAAG,iCAAiC;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAA4B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAsE,SAAS;gBAAsH;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAqG,SAAS;gBAA+D;gBAAG;oBAAE,SAAS;oBAAgB,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAsD;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,WAAW;4BAA+B,OAAO;4BAAY,YAAY;gCAAC;oCAAE,WAAW;gCAAe;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAwF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAW,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;oCAAsC,YAAY;wCAAC;4CAAE,WAAW;wCAAsC;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;gCAAG,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,SAAS;wBAAsF;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,SAAS;wBAA0D;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkC;4BAAE;4BAAG,SAAS;wBAAkD;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAsE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;oBAA+D,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAyC;wBAAG;4BAAE,WAAW;wBAA8C;wBAAG;4BAAE,WAAW;wBAAyC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,yCAAyC;YAAE,SAAS;YAAgF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAuB,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;oBAA+D,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAqC;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAAkB,QAAQ;wBAAmD;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAuB,QAAQ;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;oBAAqE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA+D,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,yCAAyC;YAAE,SAAS;YAAypB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAuB,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;oBAAqE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA+D,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8G,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAyC;wBAAG;4BAAE,WAAW;4BAA6D,WAAW;wBAAqC;wBAAG;4BAAE,WAAW;wBAAmC;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAkE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;oBAA6D,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAA2C;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,4CAA4C;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA2H,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,WAAW;oBAAmD,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAsD;qBAAE;gBAAC;aAAE;QAAC;QAAG,oCAAoC;YAAE,SAAS;YAAyD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,WAAW;gBAA+C;aAAE;QAAC;QAAG,2CAA2C;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAqC;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAAS,OAAO;YAAsC,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAqC;aAAE;QAAC;QAAG,+CAA+C;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAyD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,SAAS;oBAAS,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAwC;qBAAE;gBAAC;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAkB,SAAS;gBAAuD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,WAAW;oBAA0C,SAAS;gBAAuD;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAA2E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAA4B,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAyC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqC;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,2CAA2C;YAAE,SAAS;YAA2F,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,sCAAsC;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAA6C,SAAS;oBAA2L,QAAQ;gBAAqB;aAAE;QAAC;QAAG,eAAe;YAAE,WAAW;YAAsE,YAAY;gBAAC;oBAAE,WAAW;gBAA8D;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAyE,SAAS;oBAAkE,QAAQ;gBAAiD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAAuD,SAAS;gBAAsE;aAAE;QAAC;QAAG,yCAAyC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA8D;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,8DAA8D;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgF;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA4E;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAqF;gBAAG;oBAAE,WAAW;gBAAgG;gBAAG;oBAAE,WAAW;gBAA8E;gBAAG;oBAAE,WAAW;gBAAmF;gBAAG;oBAAE,WAAW;gBAAuF;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAuC;aAAE;QAAC;QAAG,qFAAqF;YAAE,SAAS;YAA+B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAAwH;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,SAAS;gBAAyB;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAmD;aAAE;QAAC;QAAG,gFAAgF;YAAE,SAAS;YAAW,QAAQ;QAA4C;QAAG,4EAA4E;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS;4BAA0D,QAAQ;wBAA2C;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAAmG,SAAS;QAAqH;QAAG,sFAAsF;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA4D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAA2B,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,WAAW;oBAAmD,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;aAAE;QAAC;QAAG,gGAAgG;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,WAAW;oBAA4D,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAsF;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,WAAW;oBAAuE,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAsF;qBAAE;gBAAC;aAAE;QAAC;QAAG,8EAA8E;YAAE,SAAS;YAAsD,QAAQ;QAAoC;QAAG,uFAAuF;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,WAAW;YAAuJ,OAAO;YAAmD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAkB,QAAQ;wBAAuE;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAsF;aAAE;QAAC;QAAG,mFAAmF;YAAE,SAAS;YAAwC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAsF;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyE,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAuD,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAgD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,WAAW;oBAAyC,SAAS;gBAA+B;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA8B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAA2C;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAmC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,WAAW;oBAAqC,SAAS;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAA0C;gBAAG;oBAAE,SAAS;oBAAuD,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAiG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAA2L,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAyF,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAsE,QAAQ;gBAA0D;gBAAG;oBAAE,WAAW;oBAAyD,SAAS;oBAA0E,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA2I,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA+B;gBAAG;oBAAE,WAAW;oBAA+P,SAAS;oBAAyC,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;oBAAyK,SAAS;oBAAsC,QAAQ;gBAA6B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;oBAAyC,SAAS;oBAAkD,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAuC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAsB,QAAQ;QAAkC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAgC,SAAS;oBAAqI,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAuB,SAAS;oBAAiJ,QAAQ;gBAA2C;gBAAG;oBAAE,WAAW;oBAA2C,SAAS;oBAA+I,QAAQ;gBAAuD;gBAAG;oBAAE,WAAW;oBAAsD,SAAS;oBAA8F,QAAQ;gBAAuD;gBAAG;oBAAE,WAAW;oBAAwD,SAAS;oBAA6B,QAAQ;gBAA2D;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAA6D,QAAQ;gBAAmD;gBAAG;oBAAE,WAAW;oBAA0E,SAAS;gBAAiD;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAA8E,QAAQ;gBAAwC;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAAgF,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAa,SAAS;oBAA4E,QAAQ;gBAAyC;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAA4F,QAAQ;gBAA6C;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAsC;aAAE;QAAC;QAAG,uCAAuC;YAAE,WAAW;YAAqB,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAQ,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAkD;wBAAG;4BAAE,WAAW;wBAAoD;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkD;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,MAAM;4BAAE,QAAQ;wBAAiD;wBAAG,MAAM;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAA8U,SAAS;oBAAyc,QAAQ;gBAA2B;aAAE;QAAC;QAAG,mEAAmE;YAAE,WAAW;YAAmH,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAA+B,SAAS;gBAAiF;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,WAAW;oBAAmB,SAAS;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW,CAAC;;;4BAGxnxE,CAAC;oBAAE,SAAS;gBAAoG;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,WAAW;oBAAuB,SAAS;gBAAgD;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,WAAW;oBAAuB,SAAS;gBAAyD;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,WAAW;oBAAsB,SAAS;gBAA4C;aAAE;QAAC;QAAG,wFAAwF;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAgR;QAAG,+CAA+C;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAiC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAoC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;gBAAG,MAAM;oBAAE,QAAQ;gBAAiC;gBAAG,MAAM;oBAAE,QAAQ;gBAAoC;gBAAG,MAAM;oBAAE,QAAQ;gBAAiC;gBAAG,MAAM;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAgkB,QAAQ;QAAsB;QAAG,4DAA4D;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,SAAS;YAAuF,QAAQ;QAA4C;QAAG,yDAAyD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAA6C,YAAY;wBAAC;4BAAE,WAAW;wBAAiE;qBAAE;gBAAC;aAAE;QAAC;QAAG,iEAAiE;YAAE,WAAW;YAAoC,YAAY;gBAAC;oBAAE,WAAW;oBAAuD,SAAS;oBAAS,QAAQ;gBAA6C;gBAAG;oBAAE,WAAW;gBAAyD;gBAAG;oBAAE,WAAW;gBAA6C;gBAAG;oBAAE,WAAW;gBAAqD;gBAAG;oBAAE,WAAW;gBAAuD;gBAAG;oBAAE,WAAW;gBAA4D;aAAE;QAAC;QAAG,2DAA2D;YAAE,WAAW;YAAoJ,SAAS;YAA+J,QAAQ;QAAqC;QAAG,4DAA4D;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAkD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2Y,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAmC;wBAAG,MAAM;4BAAE,QAAQ;wBAA0C;wBAAG,MAAM;4BAAE,QAAQ;wBAA0C;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAA0C;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;wBAAG,MAAM;4BAAE,QAAQ;wBAAsC;wBAAG,MAAM;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+d,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,MAAM;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAkD;qBAAE;gBAAC;aAAE;QAAC;QAAG,oDAAoD;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAY,QAAQ;QAAsB;QAAG,6CAA6C;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAe,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;QAAmC;QAAG,kDAAkD;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA6C;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAuD;gBAAG;oBAAE,WAAW;gBAA4D;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAwF;gBAAG;oBAAE,WAAW;gBAA+C;gBAAG;oBAAE,WAAW;gBAAmE;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAA6C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAgE,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAyD;gBAAG;oBAAE,WAAW;gBAA2D;gBAAG;oBAAE,WAAW;gBAA4D;aAAE;QAAC;QAAG,qDAAqD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAA6C;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAqD;aAAE;QAAC;QAAG,uDAAuD;YAAE,SAAS;YAA+M,QAAQ;QAAoC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,WAAW;oBAAuC,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,QAAQ;wBAAwD;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAA0C;wBAAG;4BAAE,WAAW;wBAA+B;wBAAG;4BAAE,WAAW;4BAAuD,SAAS;4BAA6B,QAAQ;wBAAyD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAY,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,QAAQ;wBAAwD;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA0C;wBAAG;4BAAE,WAAW;wBAAmC;wBAAG;4BAAE,WAAW;4BAAuD,SAAS;4BAA8B,QAAQ;wBAAyD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,QAAQ;wBAAwD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAA4C;wBAAG;4BAAE,WAAW;wBAA+B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,WAAW;oBAA0G,OAAO;oBAAY,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAA4C;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,WAAW;oBAA2D,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAA4C;wBAAG;4BAAE,WAAW;wBAAmC;qBAAE;gBAAC;aAAE;QAAC;QAAG,mCAAmC;YAAE,WAAW;YAAkD,YAAY;gBAAC;oBAAE,SAAS,CAAC,iBAAiB,CAAC;oBAAE,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,eAAe;oBAAgB,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAe;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAAO,WAAW;4BAAiB,OAAO;wBAAM;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAwC;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,gBAAgB,CAAC;oBAAE,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,eAAe;oBAAgB,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAe;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAAO,WAAW;4BAAiB,OAAO;wBAAM;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAwC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAA4D;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAgB,SAAS;oBAA8B,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAA4iB,WAAW;oBAA6C,OAAO;oBAAW,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAmB,QAAQ;wCAAgD;wCAAG;4CAAE,SAAS;4CAAe,QAAQ;wCAA0C;wCAAG;4CAAE,SAAS;4CAAS,QAAQ;wCAAqC;wCAAG;4CAAE,SAAS;4CAAS,QAAQ;wCAAqC;wCAAG;4CAAE,SAAS;4CAAM,QAAQ;wCAAuC;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAAyB,SAAS;wBAAilB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAmB,QAAQ;wCAAgD;wCAAG;4CAAE,SAAS;4CAAS,QAAQ;wCAAgD;wCAAG;4CAAE,SAAS;4CAAM,QAAQ;wCAAwC;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAA0B,SAAS;wBAAilB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAS,QAAQ;wCAAoC;wCAAG;4CAAE,SAAS;4CAAkD,QAAQ;wCAA6C;wCAAG;4CAAE,SAAS;4CAAqB,QAAQ;wCAAoC;wCAAG;4CAAE,SAAS;4CAAoB,QAAQ;wCAA6C;wCAAG;4CAAE,SAAS;4CAAS,QAAQ;wCAA8C;wCAAG;4CAAE,SAAS;4CAA4B,QAAQ;wCAAoC;wCAAG;4CAAE,SAAS;4CAAc,QAAQ;wCAAoC;wCAAG;4CAAE,SAAS;4CAAmB,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAyB,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAiB,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAW,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAM,QAAQ;wCAAsC;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAAkB,SAAS;wBAAwiB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAM,QAAQ;wCAA2C;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAA6B,SAAS;wBAAwlB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAM,QAAQ;wCAA4C;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAA8B,SAAS;wBAAwlB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAkB,QAAQ;wCAA+B;wCAAG;4CAAE,SAAS;4CAAM,QAAQ;wCAA0C;qCAAE;gCAAC;4BAAE;4BAAG,WAAW;4BAAsB,SAAS;wBAA+iB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAiC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AAC56wB,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}