{"version": 3, "file": "main.d965d3c18db5ddb8c426.hot-update.js", "mappings": ";;;;;;;;;;;;;;;;;;;AACA;AAEA;AAKA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAEA;AAdA", "sources": ["webpack://storybook/../../packages/design-system/providers/theme.tsx"], "sourcesContent": ["import type { ThemeProviderProps } from 'next-themes';\nimport { ThemeProvider as NextThemeProvider } from 'next-themes';\n\nexport const ThemeProvider = ({\n  children,\n  ...properties\n}: ThemeProviderProps) => (\n  <NextThemeProvider\n    attribute=\"class\"\n    defaultTheme=\"dark\"\n    forcedTheme=\"dark\"\n    enableSystem={false}\n    disableTransitionOnChange\n    {...properties}\n  >\n    {children}\n  </NextThemeProvider>\n);\n"], "names": [], "sourceRoot": ""}