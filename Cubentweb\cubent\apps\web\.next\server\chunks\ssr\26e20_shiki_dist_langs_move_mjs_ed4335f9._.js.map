{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/move.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Move\", \"name\": \"move\", \"patterns\": [{ \"include\": \"#address\" }, { \"include\": \"#comments\" }, { \"include\": \"#module\" }, { \"include\": \"#script\" }, { \"include\": \"#annotation\" }, { \"include\": \"#comments\" }, { \"include\": \"#annotation\" }, { \"include\": \"#entry\" }, { \"include\": \"#public-scope\" }, { \"include\": \"#public\" }, { \"include\": \"#native\" }, { \"include\": \"#import\" }, { \"include\": \"#friend\" }, { \"include\": \"#const\" }, { \"include\": \"#struct\" }, { \"include\": \"#has_ability\" }, { \"include\": \"#enum\" }, { \"include\": \"#macro\" }, { \"include\": \"#fun\" }, { \"include\": \"#spec\" }], \"repository\": { \"=== DEPRECATED_BELOW ===\": {}, \"abilities\": { \"comment\": \"Ability\", \"match\": \"\\\\b(store|key|drop|copy)\\\\b\", \"name\": \"support.type.ability.move\" }, \"address\": { \"begin\": \"\\\\b(address)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.address.keyword.move\" } }, \"comment\": \"Address block\", \"end\": \"(?<=})\", \"name\": \"meta.address_block.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"(?<=address)\", \"comment\": \"Address value/const\", \"end\": \"(?=[{])\", \"name\": \"meta.address.definition.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#address_literal\" }, { \"comment\": \"Named Address\", \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.type.move\" }] }, { \"include\": \"#module\" }] }, \"annotation\": { \"begin\": \"#\\\\[\", \"end\": \"\\\\]\", \"name\": \"support.constant.annotation.move\", \"patterns\": [{ \"comment\": \"Annotation name\", \"match\": \"\\\\b(\\\\w+)\\\\s*(?==)\", \"name\": \"meta.annotation.name.move\" }, { \"begin\": \"=\", \"comment\": \"Annotation value\", \"end\": \"(?=[,\\\\]])\", \"name\": \"meta.annotation.value.move\", \"patterns\": [{ \"include\": \"#literals\" }] }] }, \"as\": { \"comment\": \"Keyword as (highlighted)\", \"match\": \"\\\\b(as)\\\\b\", \"name\": \"keyword.control.as.move\" }, \"as-import\": { \"comment\": \"Keyword as in import statement; not highlighted\", \"match\": \"\\\\b(as)\\\\b\", \"name\": \"meta.import.as.move\" }, \"block\": { \"begin\": \"{\", \"comment\": \"Block expression or definition\", \"end\": \"}\", \"name\": \"meta.block.move\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"block-comments\": { \"patterns\": [{ \"begin\": \"/\\\\*[\\\\*!](?![\\\\*/])\", \"comment\": \"Block documentation comment\", \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.move\" }, { \"begin\": \"/\\\\*\", \"comment\": \"Block comment\", \"end\": \"\\\\*/\", \"name\": \"comment.block.move\" }] }, \"capitalized\": { \"comment\": \"MyType - capitalized type name\", \"match\": \"\\\\b([A-Z][a-zA-Z_0-9]*)\\\\b\", \"name\": \"entity.name.type.use.move\" }, \"comments\": { \"name\": \"meta.comments.move\", \"patterns\": [{ \"include\": \"#doc-comments\" }, { \"include\": \"#line-comments\" }, { \"include\": \"#block-comments\" }] }, \"const\": { \"begin\": \"\\\\b(const)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.const.move\" } }, \"end\": \";\", \"name\": \"meta.const.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#primitives\" }, { \"include\": \"#literals\" }, { \"include\": \"#types\" }, { \"match\": \"\\\\b([A-Z_]+)\\\\b\", \"name\": \"constant.other.move\" }, { \"include\": \"#error_const\" }] }, \"control\": { \"comment\": \"Control flow\", \"match\": \"\\\\b(return|while|loop|if|else|break|continue|abort)\\\\b\", \"name\": \"keyword.control.move\" }, \"doc-comments\": { \"begin\": \"///\", \"comment\": \"Documentation comment\", \"end\": \"$\", \"name\": \"comment.block.documentation.move\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"markup.underline.link.move\" } }, \"comment\": \"Escaped member / link\", \"match\": \"`(\\\\w+)`\" }] }, \"entry\": { \"comment\": \"entry\", \"match\": \"\\\\b(entry)\\\\b\", \"name\": \"storage.modifier.visibility.entry.move\" }, \"enum\": { \"begin\": \"\\\\b(enum)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.enum.move\" } }, \"comment\": \"Enum syntax\", \"end\": \"(?<=})\", \"name\": \"meta.enum.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#type_param\" }, { \"comment\": \"Enum name (ident)\", \"match\": \"\\\\b[A-Z][a-zA-Z_0-9]*\\\\b\", \"name\": \"entity.name.type.enum.move\" }, { \"include\": \"#has\" }, { \"include\": \"#abilities\" }, { \"begin\": \"{\", \"end\": \"}\", \"name\": \"meta.enum.definition.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b([A-Z][A-Za-z_0-9]*)\\\\b(?=\\\\s*\\\\()\", \"name\": \"entity.name.function.enum.move\" }, { \"match\": \"\\\\b([A-Z][A-Za-z_0-9]*)\\\\b\", \"name\": \"entity.name.type.enum.move\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"name\": \"meta.enum.tuple.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expr_generic\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }] }, { \"begin\": \"{\", \"end\": \"}\", \"name\": \"meta.enum.struct.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#expr_generic\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }] }] }] }, \"error_const\": { \"match\": \"\\\\b(E[A-Z][A-Za-z_]*)\\\\b\", \"name\": \"variable.other.error.const.move\" }, \"escaped_identifier\": { \"begin\": \"`\", \"comment\": \"Escaped variable\", \"end\": \"`\", \"name\": \"variable.language.escaped.move\" }, \"expr\": { \"comment\": \"Aggregate Expression\", \"name\": \"meta.expression.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#expr_generic\" }, { \"include\": \"#packed_field\" }, { \"include\": \"#import\" }, { \"include\": \"#as\" }, { \"include\": \"#mut\" }, { \"include\": \"#let\" }, { \"include\": \"#types\" }, { \"include\": \"#literals\" }, { \"include\": \"#control\" }, { \"include\": \"#move_copy\" }, { \"include\": \"#resource_methods\" }, { \"include\": \"#self_access\" }, { \"include\": \"#module_access\" }, { \"include\": \"#label\" }, { \"include\": \"#macro_call\" }, { \"include\": \"#local_call\" }, { \"include\": \"#method_call\" }, { \"include\": \"#path_access\" }, { \"include\": \"#match_expression\" }, { \"match\": \"\\\\$(?=[a-z])\", \"name\": \"keyword.operator.macro.dollar.move\" }, { \"match\": \"(?<=[$])[a-z][A-Z_0-9a-z]*\", \"name\": \"variable.other.meta.move\" }, { \"comment\": \"ALL_CONST_CAPS\", \"match\": \"\\\\b([A-Z][A-Z_]+)\\\\b\", \"name\": \"constant.other.move\" }, { \"include\": \"#error_const\" }, { \"comment\": \"CustomType\", \"match\": \"\\\\b([A-Z][a-zA-Z_0-9]*)\\\\b\", \"name\": \"entity.name.type.move\" }, { \"include\": \"#paren\" }, { \"include\": \"#block\" }] }, \"expr_generic\": { \"begin\": \"<(?=([\\\\sa-z_,0-9A-Z<>]+>))\", \"comment\": \"< angle brackets >\", \"end\": \">\", \"name\": \"meta.expression.generic.type.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#types\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#expr_generic\" }] }, \"friend\": { \"begin\": \"\\\\b(friend)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.move\" } }, \"end\": \";\", \"name\": \"meta.friend.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#address_literal\" }, { \"comment\": \"Name of the imported module\", \"match\": \"\\\\b([a-zA-Z][A-Za-z_0-9]*)\\\\b\", \"name\": \"entity.name.type.module.move\" }] }, \"fun\": { \"patterns\": [{ \"include\": \"#fun_signature\" }, { \"include\": \"#block\" }] }, \"fun_body\": { \"begin\": \"{\", \"comment\": \"Function body\", \"end\": \"(?<=})\", \"name\": \"meta.fun_body.move\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"fun_call\": { \"begin\": \"\\\\b(\\\\w+)\\\\s*(?:<[\\\\w\\\\s,]+>)?\\\\s*[(]\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.call.move\" } }, \"comment\": \"Function call\", \"end\": \"[)]\", \"name\": \"meta.fun_call.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#resource_methods\" }, { \"include\": \"#self_access\" }, { \"include\": \"#module_access\" }, { \"include\": \"#move_copy\" }, { \"include\": \"#literals\" }, { \"include\": \"#fun_call\" }, { \"include\": \"#block\" }, { \"include\": \"#mut\" }, { \"include\": \"#as\" }] }, \"fun_signature\": { \"begin\": \"\\\\b(fun)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.fun.move\" } }, \"comment\": \"Function signature\", \"end\": \"(?=[;{])\", \"name\": \"meta.fun_signature.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#module_access\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }, { \"include\": \"#mut\" }, { \"begin\": \"(?<=\\\\bfun)\", \"comment\": \"Function name\", \"end\": \"(?=[<(])\", \"name\": \"meta.function_name.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.function.move\" }] }, { \"include\": \"#type_param\" }, { \"begin\": \"[(]\", \"comment\": \"Parentheses\", \"end\": \"[)]\", \"name\": \"meta.parentheses.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#self_access\" }, { \"include\": \"#expr_generic\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#module_access\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }, { \"include\": \"#mut\" }] }, { \"comment\": \"Keyword acquires\", \"match\": \"\\\\b(acquires)\\\\b\", \"name\": \"storage.modifier\" }] }, \"has\": { \"comment\": \"Has Abilities\", \"match\": \"\\\\b(has)\\\\b\", \"name\": \"keyword.control.ability.has.move\" }, \"has_ability\": { \"begin\": \"(?<=[})])\\\\s+(has)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.move\" } }, \"end\": \";\", \"name\": \"meta.has.ability.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#abilities\" }] }, \"ident\": { \"match\": \"\\\\b([a-zA-Z][A-Z_a-z0-9]*)\\\\b\", \"name\": \"meta.identifier.move\" }, \"import\": { \"begin\": \"\\\\b(use)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.move\" } }, \"end\": \";\", \"name\": \"meta.import.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#use_fun\" }, { \"include\": \"#address_literal\" }, { \"include\": \"#as-import\" }, { \"comment\": \"Uppercase entities\", \"match\": \"\\\\b([A-Z]\\\\w*)\\\\b\", \"name\": \"entity.name.type.move\" }, { \"begin\": \"{\", \"comment\": \"Module members\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#as-import\" }, { \"comment\": \"Uppercase entities\", \"match\": \"\\\\b([A-Z]\\\\w*)\\\\b\", \"name\": \"entity.name.type.move\" }] }, { \"comment\": \"Name of the imported module\", \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"meta.entity.name.type.module.move\" }] }, \"inline\": { \"comment\": \"inline\", \"match\": \"\\\\b(inline)\\\\b\", \"name\": \"storage.modifier.visibility.inline.move\" }, \"label\": { \"comment\": \"Label\", \"match\": \"'[a-z][a-z_0-9]*\", \"name\": \"string.quoted.single.label.move\" }, \"let\": { \"comment\": \"Keyword let\", \"match\": \"\\\\b(let)\\\\b\", \"name\": \"keyword.control.move\" }, \"line-comments\": { \"begin\": \"//\", \"comment\": \"Single-line comment\", \"end\": \"$\", \"name\": \"comment.line.double-slash.move\" }, \"literals\": { \"comment\": \"Literals supported in Move\", \"name\": \"meta.literal.move\", \"patterns\": [{ \"comment\": \"base16 address literal\", \"match\": \"@0x[A-F0-9a-f]+\", \"name\": \"support.constant.address.base16.move\" }, { \"comment\": \"named address literal @[ident]\", \"match\": \"@[a-zA-Z][a-zA-Z_0-9]*\", \"name\": \"support.constant.address.name.move\" }, { \"comment\": \"Hex literal\", \"match\": \"0x[_a-fA-F0-9]+(?:u(?:8|16|32|64|128|256))?\", \"name\": \"constant.numeric.hex.move\" }, { \"comment\": \"Numeric literal\", \"match\": \"(?<!(?:\\\\w|(?:(?<!\\\\.)\\\\.)))\\\\d[_0-9]*(?:\\\\.(?!\\\\.)(?:\\\\d[_0-9]*)?)?(?:[eE][+\\\\-]?[_0-9]+)?(?:[u](?:8|16|32|64|128|256))?\", \"name\": \"constant.numeric.move\" }, { \"begin\": '\\\\bb\"', \"comment\": \"vector ascii bytestring literal\", \"end\": '\"', \"name\": \"meta.vector.literal.ascii.move\", \"patterns\": [{ \"comment\": \"character escape\", \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.move\" }, { \"comment\": \"Special symbol escape\", \"match\": '\\\\\\\\[nrt\\\\0\"]', \"name\": \"constant.character.escape.move\" }, { \"comment\": \"HEX Escape\", \"match\": \"\\\\\\\\x[a-fA-F0-9][A-Fa-f0-9]\", \"name\": \"constant.character.escape.hex.move\" }, { \"comment\": \"ASCII Character\", \"match\": \"[\\\\x00-\\\\x7F]\", \"name\": \"string.quoted.double.raw.move\" }] }, { \"begin\": 'x\"', \"comment\": \"vector hex literal\", \"end\": '\"', \"name\": \"meta.vector.literal.hex.move\", \"patterns\": [{ \"comment\": \"vector hex literal\", \"match\": \"[A-Fa-f0-9]+\", \"name\": \"constant.character.move\" }] }, { \"comment\": \"bool literal\", \"match\": \"\\\\b(?:true|false)\\\\b\", \"name\": \"constant.language.boolean.move\" }, { \"begin\": \"vector\\\\[\", \"comment\": \"vector literal (macro?)\", \"end\": \"\\\\]\", \"name\": \"meta.vector.literal.macro.move\", \"patterns\": [{ \"include\": \"#expr\" }] }] }, \"local_call\": { \"comment\": \"call to a local / imported fun\", \"match\": \"\\\\b([a-z][_a-z0-9]*)(?=[<(])\", \"name\": \"entity.name.function.call.local.move\" }, \"macro\": { \"begin\": \"\\\\b(macro)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.macro.move\" } }, \"comment\": \"macro fun [ident] {}\", \"end\": \"(?<=})\", \"name\": \"meta.macro.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#fun\" }] }, \"macro_call\": { \"captures\": { \"2\": { \"name\": \"support.function.macro.move\" } }, \"comment\": \"Macro fun call\", \"match\": \"(\\\\b|\\\\.)([a-z][A-Za-z0-9_]*)!\", \"name\": \"meta.macro.call\" }, \"match_expression\": { \"begin\": \"\\\\b(match)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.match.move\" } }, \"comment\": \"enum pattern matching\", \"end\": \"(?<=})\", \"name\": \"meta.match.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#types\" }, { \"begin\": \"{\", \"comment\": \"Block expression or definition\", \"end\": \"}\", \"name\": \"meta.match.block.move\", \"patterns\": [{ \"comment\": \"arrow operator\", \"match\": \"\\\\b(=>)\\\\b\", \"name\": \"operator.match.move\" }, { \"include\": \"#expr\" }] }, { \"include\": \"#expr\" }] }, \"method_call\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.call.path.move\" } }, \"comment\": \"<expr>.[ident]<>?() call\", \"match\": \"\\\\.([a-z][_a-z0-9]*)(?=[<(])\", \"name\": \"meta.path.call.move\" }, \"module\": { \"begin\": \"\\\\b(module)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.move\" } }, \"comment\": \"Module definition\", \"end\": \"(?<=[;}])\", \"name\": \"meta.module.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"(?<=\\\\b(module)\\\\b)\", \"comment\": \"Module name\", \"end\": \"(?=[;{])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"begin\": \"(?<=\\\\b(module))\", \"comment\": \"Module namespace / address\", \"end\": \"(?=[(::){])\", \"name\": \"constant.other.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }] }, { \"begin\": \"(?<=::)\", \"comment\": \"Module name\", \"end\": \"(?=[\\\\s;{])\", \"name\": \"entity.name.type.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }] }] }, { \"begin\": \"{\", \"comment\": \"Module scope\", \"end\": \"}\", \"name\": \"meta.module_scope.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#annotation\" }, { \"include\": \"#entry\" }, { \"include\": \"#public-scope\" }, { \"include\": \"#public\" }, { \"include\": \"#native\" }, { \"include\": \"#import\" }, { \"include\": \"#friend\" }, { \"include\": \"#const\" }, { \"include\": \"#struct\" }, { \"include\": \"#has_ability\" }, { \"include\": \"#enum\" }, { \"include\": \"#macro\" }, { \"include\": \"#fun\" }, { \"include\": \"#spec\" }] }] }, \"module_access\": { \"captures\": { \"1\": { \"name\": \"meta.entity.name.type.accessed.module.move\" }, \"2\": { \"name\": \"entity.name.function.call.move\" } }, \"comment\": \"Use of module type or method\", \"match\": \"\\\\b(\\\\w+)::(\\\\w+)\\\\b\", \"name\": \"meta.module_access.move\" }, \"module_label\": { \"begin\": \"^\\\\s*(module)\\\\b\", \"comment\": \"Module label, inline module definition\", \"end\": \";\\\\s*$\", \"name\": \"meta.module.label.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"begin\": \"(?<=\\\\bmodule\\\\b)\", \"comment\": \"Module namespace / address\", \"end\": \"(?=[(::){])\", \"name\": \"constant.other.move\" }, { \"begin\": \"(?<=::)\", \"comment\": \"Module name\", \"end\": \"(?=[\\\\s{])\", \"name\": \"entity.name.type.move\" }] }, \"move_copy\": { \"comment\": \"Keywords move and copy\", \"match\": \"\\\\b(move|copy)\\\\b\", \"name\": \"variable.language.move\" }, \"mut\": { \"comment\": \"Mutable reference and let mut\", \"match\": \"\\\\b(mut)\\\\b\", \"name\": \"storage.modifier.mut.move\" }, \"native\": { \"comment\": \"native\", \"match\": \"\\\\b(native)\\\\b\", \"name\": \"storage.modifier.visibility.native.move\" }, \"packed_field\": { \"comment\": \"[ident]: \", \"match\": \"[a-z][a-z0-9_]+\\\\s*:\\\\s*(?=\\\\s)\", \"name\": \"meta.struct.field.move\" }, \"paren\": { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"name\": \"meta.paren.move\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"path_access\": { \"comment\": \"<expr>.[ident] access\", \"match\": \"\\\\.[a-z][_a-z0-9]*\\\\b\", \"name\": \"meta.path.access.move\" }, \"phantom\": { \"comment\": \"Keyword phantom inside type parameters\", \"match\": \"\\\\b(phantom)\\\\b\", \"name\": \"keyword.control.phantom.move\" }, \"primitives\": { \"comment\": \"Primitive types\", \"match\": \"\\\\b(u8|u16|u32|u64|u128|u256|address|bool|signer)\\\\b\", \"name\": \"support.type.primitives.move\" }, \"public\": { \"comment\": \"public\", \"match\": \"\\\\b(public)\\\\b\", \"name\": \"storage.modifier.visibility.public.move\" }, \"public-scope\": { \"begin\": \"(?<=\\\\b(public))\\\\s*\\\\(\", \"comment\": \"public (friend/script/package)\", \"end\": \"\\\\)\", \"name\": \"meta.public.scoped.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(friend|script|package)\\\\b\", \"name\": \"keyword.control.public.scope.move\" }] }, \"resource_methods\": { \"comment\": \"Methods to work with resource\", \"match\": \"\\\\b(borrow_global|borrow_global_mut|exists|move_from|move_to_sender|move_to)\\\\b\", \"name\": \"support.function.typed.move\" }, \"script\": { \"begin\": \"\\\\b(script)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.script.move\" } }, \"end\": \"(?<=})\", \"name\": \"meta.script.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"{\", \"comment\": \"Script scope\", \"end\": \"}\", \"name\": \"meta.script_scope.move\", \"patterns\": [{ \"include\": \"#const\" }, { \"include\": \"#comments\" }, { \"include\": \"#import\" }, { \"include\": \"#fun\" }] }] }, \"self_access\": { \"captures\": { \"1\": { \"name\": \"variable.language.self.move\" }, \"2\": { \"name\": \"entity.name.function.call.move\" } }, \"comment\": \"Use of Self\", \"match\": \"\\\\b(Self)::(\\\\w+)\\\\b\", \"name\": \"meta.self_access.move\" }, \"spec\": { \"begin\": \"\\\\b(spec)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.spec.move\" } }, \"end\": \"(?<=[;}])\", \"name\": \"meta.spec.move\", \"patterns\": [{ \"comment\": \"Spec target\", \"match\": \"\\\\b(module|schema|struct|fun)\", \"name\": \"storage.modifier.spec.target.move\" }, { \"comment\": \"Spec define inline\", \"match\": \"\\\\b(define)\", \"name\": \"storage.modifier.spec.define.move\" }, { \"comment\": \"Target name\", \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.function.move\" }, { \"begin\": \"{\", \"comment\": \"Spec block\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#spec_block\" }, { \"include\": \"#spec_types\" }, { \"include\": \"#spec_define\" }, { \"include\": \"#spec_keywords\" }, { \"include\": \"#control\" }, { \"include\": \"#fun_call\" }, { \"include\": \"#literals\" }, { \"include\": \"#types\" }, { \"include\": \"#let\" }] }] }, \"spec_block\": { \"begin\": \"{\", \"comment\": \"Spec block\", \"end\": \"}\", \"name\": \"meta.spec_block.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#spec_block\" }, { \"include\": \"#spec_types\" }, { \"include\": \"#fun_call\" }, { \"include\": \"#literals\" }, { \"include\": \"#control\" }, { \"include\": \"#types\" }, { \"include\": \"#let\" }] }, \"spec_define\": { \"begin\": \"\\\\b(define)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.move.spec\" } }, \"comment\": \"Spec define keyword\", \"end\": \"(?=[;{])\", \"name\": \"meta.spec_define.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#spec_types\" }, { \"include\": \"#types\" }, { \"begin\": \"(?<=\\\\bdefine)\", \"comment\": \"Function name\", \"end\": \"(?=[(])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.function.move\" }] }] }, \"spec_keywords\": { \"match\": \"\\\\b(global|pack|unpack|pragma|native|include|ensures|requires|invariant|apply|aborts_if|modifies)\\\\b\", \"name\": \"keyword.control.move.spec\" }, \"spec_types\": { \"comment\": \"Spec-only types\", \"match\": \"\\\\b(range|num|vector|bool|u8|u16|u32|u64|u128|u256|address)\\\\b\", \"name\": \"support.type.vector.move\" }, \"struct\": { \"begin\": \"\\\\b(struct)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.type.move\" } }, \"end\": \"(?<=[};)])\", \"name\": \"meta.struct.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#has\" }, { \"include\": \"#abilities\" }, { \"comment\": \"Struct name (ident)\", \"match\": \"\\\\b[A-Z][a-zA-Z_0-9]*\\\\b\", \"name\": \"entity.name.type.struct.move\" }, { \"begin\": \"\\\\(\", \"comment\": \"Positional fields\", \"end\": \"\\\\)\", \"name\": \"meta.struct.paren.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }] }, { \"include\": \"#type_param\" }, { \"begin\": \"\\\\(\", \"comment\": \"Simple struct\", \"end\": \"(?<=[)])\", \"name\": \"meta.struct.paren.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#types\" }] }, { \"begin\": \"{\", \"comment\": \"Struct body\", \"end\": \"}\", \"name\": \"meta.struct.body.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#self_access\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#module_access\" }, { \"include\": \"#expr_generic\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#types\" }] }, { \"include\": \"#has_ability\" }] }, \"struct_pack\": { \"begin\": \"(?<=[A-Za-z0-9_>])\\\\s*{\", \"comment\": \"Struct { field: value... }; identified as generic / ident followed by curly's\", \"end\": \"}\", \"name\": \"meta.struct.pack.move\", \"patterns\": [{ \"include\": \"#comments\" }] }, \"type_param\": { \"begin\": \"<\", \"comment\": \"Generic type param\", \"end\": \">\", \"name\": \"meta.generic_param.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#phantom\" }, { \"include\": \"#capitalized\" }, { \"include\": \"#module_access\" }, { \"include\": \"#abilities\" }] }, \"types\": { \"comment\": \"Built-in types + vector\", \"name\": \"meta.types.move\", \"patterns\": [{ \"include\": \"#primitives\" }, { \"include\": \"#vector\" }] }, \"use_fun\": { \"begin\": \"\\\\b(fun)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.fun.move\" } }, \"comment\": \"use { fun } internals\", \"end\": \"(?=;)\", \"name\": \"meta.import.fun.move\", \"patterns\": [{ \"include\": \"#comments\" }, { \"comment\": \"as keyword\", \"match\": \"\\\\b(as)\\\\b\", \"name\": \"keyword.control.as.move\" }, { \"comment\": \"Self keyword\", \"match\": \"\\\\b(Self)\\\\b\", \"name\": \"variable.language.self.use.fun.move\" }, { \"comment\": \"Function name\", \"match\": \"\\\\b(_______[a-z][a-z_0-9]+)\\\\b\", \"name\": \"entity.name.function.use.move\" }, { \"include\": \"#types\" }, { \"include\": \"#escaped_identifier\" }, { \"include\": \"#capitalized\" }] }, \"vector\": { \"comment\": \"vector type\", \"match\": \"\\\\b(vector)\\\\b\", \"name\": \"support.type.vector.move\" } }, \"scopeName\": \"source.move\" });\nvar move = [\n  lang\n];\n\nexport { move as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAO;QAAG;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,4BAA4B,CAAC;QAAG,aAAa;YAAE,WAAW;YAAW,SAAS;YAA+B,QAAQ;QAA4B;QAAG,WAAW;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,WAAW;YAAiB,OAAO;YAAU,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAgB,WAAW;oBAAuB,OAAO;oBAAW,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;4BAAiB,SAAS;4BAAgB,QAAQ;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAQ,OAAO;YAAO,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;oBAAmB,SAAS;oBAAsB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAoB,OAAO;oBAAc,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,MAAM;YAAE,WAAW;YAA4B,SAAS;YAAc,QAAQ;QAA0B;QAAG,aAAa;YAAE,WAAW;YAAmD,SAAS;YAAc,QAAQ;QAAsB;QAAG,SAAS;YAAE,SAAS;YAAK,WAAW;YAAkC,OAAO;YAAK,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,WAAW;oBAA+B,OAAO;oBAAQ,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,WAAW;oBAAiB,OAAO;oBAAQ,QAAQ;gBAAqB;aAAE;QAAC;QAAG,eAAe;YAAE,WAAW;YAAkC,SAAS;YAA8B,QAAQ;QAA4B;QAAG,YAAY;YAAE,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,WAAW;YAAE,WAAW;YAAgB,SAAS;YAA0D,QAAQ;QAAuB;QAAG,gBAAgB;YAAE,SAAS;YAAO,WAAW;YAAyB,OAAO;YAAK,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,WAAW;oBAAyB,SAAS;gBAAW;aAAE;QAAC;QAAG,SAAS;YAAE,WAAW;YAAS,SAAS;YAAiB,QAAQ;QAAyC;QAAG,QAAQ;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAe,OAAO;YAAU,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;oBAA4B,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAyC,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAA6B;wBAAG;4BAAE,SAAS;4BAAO,OAAO;4BAAO,QAAQ;4BAAwB,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAS;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,OAAO;4BAAK,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAsB;gCAAG;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAS;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAA4B,QAAQ;QAAkC;QAAG,sBAAsB;YAAE,SAAS;YAAK,WAAW;YAAoB,OAAO;YAAK,QAAQ;QAAiC;QAAG,QAAQ;YAAE,WAAW;YAAwB,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAM;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;oBAAkB,SAAS;oBAAwB,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAA8B,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAA+B,WAAW;YAAsB,OAAO;YAAK,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;oBAA+B,SAAS;oBAAiC,QAAQ;gBAA+B;aAAE;QAAC;QAAG,OAAO;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAK,WAAW;YAAiB,OAAO;YAAU,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,WAAW;YAAiB,OAAO;YAAO,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAM;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAsB,OAAO;YAAY,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,SAAS;oBAAe,WAAW;oBAAiB,OAAO;oBAAY,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA4B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAO,WAAW;oBAAe,OAAO;oBAAO,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAO;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAAoB,QAAQ;gBAAmB;aAAE;QAAC;QAAG,OAAO;YAAE,WAAW;YAAiB,SAAS;YAAe,QAAQ;QAAmC;QAAG,eAAe;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAiC,QAAQ;QAAuB;QAAG,UAAU;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;oBAAsB,SAAS;oBAAqB,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAkB,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;4BAAsB,SAAS;4BAAqB,QAAQ;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;oBAA+B,SAAS;oBAAgB,QAAQ;gBAAoC;aAAE;QAAC;QAAG,UAAU;YAAE,WAAW;YAAU,SAAS;YAAkB,QAAQ;QAA0C;QAAG,SAAS;YAAE,WAAW;YAAS,SAAS;YAAoB,QAAQ;QAAkC;QAAG,OAAO;YAAE,WAAW;YAAe,SAAS;YAAe,QAAQ;QAAuB;QAAG,iBAAiB;YAAE,SAAS;YAAM,WAAW;YAAuB,OAAO;YAAK,QAAQ;QAAiC;QAAG,YAAY;YAAE,WAAW;YAA8B,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;oBAA0B,SAAS;oBAAmB,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAkC,SAAS;oBAA0B,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;oBAAe,SAAS;oBAA+C,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;oBAAmB,SAAS;oBAA6H,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAS,WAAW;oBAAmC,OAAO;oBAAK,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;4BAAoB,SAAS;4BAAS,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;4BAAyB,SAAS;4BAAiB,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;4BAAc,SAAS;4BAA+B,QAAQ;wBAAqC;wBAAG;4BAAE,WAAW;4BAAmB,SAAS;4BAAiB,QAAQ;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,WAAW;oBAAsB,OAAO;oBAAK,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;4BAAsB,SAAS;4BAAgB,QAAQ;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAwB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAa,WAAW;oBAA2B,OAAO;oBAAO,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,WAAW;YAAkC,SAAS;YAAgC,QAAQ;QAAuC;QAAG,SAAS;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAwB,OAAO;YAAU,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,WAAW;YAAkB,SAAS;YAAkC,QAAQ;QAAkB;QAAG,oBAAoB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAyB,OAAO;YAAU,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAkC,OAAO;oBAAK,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;4BAAkB,SAAS;4BAAc,QAAQ;wBAAsB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,WAAW;YAA4B,SAAS;YAAgC,QAAQ;QAAsB;QAAG,UAAU;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAqB,OAAO;YAAa,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAuB,WAAW;oBAAe,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,SAAS;4BAAoB,WAAW;4BAA8B,OAAO;4BAAe,QAAQ;4BAAuB,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAW,WAAW;4BAAe,OAAO;4BAAe,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAgB,OAAO;oBAAK,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,WAAW;YAAgC,SAAS;YAAwB,QAAQ;QAA0B;QAAG,gBAAgB;YAAE,SAAS;YAAoB,WAAW;YAA0C,OAAO;YAAU,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAAqB,WAAW;oBAA8B,OAAO;oBAAe,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAW,WAAW;oBAAe,OAAO;oBAAc,QAAQ;gBAAwB;aAAE;QAAC;QAAG,aAAa;YAAE,WAAW;YAA0B,SAAS;YAAqB,QAAQ;QAAyB;QAAG,OAAO;YAAE,WAAW;YAAiC,SAAS;YAAe,QAAQ;QAA4B;QAAG,UAAU;YAAE,WAAW;YAAU,SAAS;YAAkB,QAAQ;QAA0C;QAAG,gBAAgB;YAAE,WAAW;YAAa,SAAS;YAAmC,QAAQ;QAAyB;QAAG,SAAS;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,eAAe;YAAE,WAAW;YAAyB,SAAS;YAAyB,QAAQ;QAAwB;QAAG,WAAW;YAAE,WAAW;YAA0C,SAAS;YAAmB,QAAQ;QAA+B;QAAG,cAAc;YAAE,WAAW;YAAmB,SAAS;YAAwD,QAAQ;QAA+B;QAAG,UAAU;YAAE,WAAW;YAAU,SAAS;YAAkB,QAAQ;QAA0C;QAAG,gBAAgB;YAAE,SAAS;YAA2B,WAAW;YAAkC,OAAO;YAAO,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAAoC;aAAE;QAAC;QAAG,oBAAoB;YAAE,WAAW;YAAiC,SAAS;YAAmF,QAAQ;QAA8B;QAAG,UAAU;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAgB,OAAO;oBAAK,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAO;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,WAAW;YAAe,SAAS;YAAwB,QAAQ;QAAwB;QAAG,QAAQ;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAa,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,WAAW;oBAAe,SAAS;oBAAiC,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;oBAAsB,SAAS;oBAAe,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;oBAAe,SAAS;oBAAgB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAc,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAO;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAK,WAAW;YAAc,OAAO;YAAK,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAuB,OAAO;YAAY,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAkB,WAAW;oBAAiB,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA4B;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAwG,QAAQ;QAA4B;QAAG,cAAc;YAAE,WAAW;YAAmB,SAAS;YAAkE,QAAQ;QAA2B;QAAG,UAAU;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAc,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;oBAAuB,SAAS;oBAA4B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAO,WAAW;oBAAqB,OAAO;oBAAO,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAO,WAAW;oBAAiB,OAAO;oBAAY,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,WAAW;oBAAe,OAAO;oBAAK,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAA2B,WAAW;YAAiF,OAAO;YAAK,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAK,WAAW;YAAsB,OAAO;YAAK,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,SAAS;YAAE,WAAW;YAA2B,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAyB,OAAO;YAAS,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAAc,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAgB,QAAQ;gBAAsC;gBAAG;oBAAE,WAAW;oBAAiB,SAAS;oBAAkC,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,UAAU;YAAE,WAAW;YAAe,SAAS;YAAkB,QAAQ;QAA2B;IAAE;IAAG,aAAa;AAAc;AAC16qB,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}