{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceCatchAllRoute.tsx"], "sourcesContent": ["import { isProductionEnvironment } from '@clerk/shared/utils';\nimport type { RoutingStrategy } from '@clerk/types';\nimport React from 'react';\n\nimport { useSession } from '../hooks';\nimport { usePagesRouter } from './usePagesRouter';\n\n/**\n * This ugly hook  enforces that the Clerk components are mounted in a catch-all route\n * For pages router, we can parse the pathname we get from the useRouter hook\n * For app router, there is no reliable way to do the same check right now, so we\n * fire a request to a path under window.location.href and we check whether the path\n * exists or not\n */\nexport const useEnforceCatchAllRoute = (\n  component: string,\n  path: string,\n  routing?: RoutingStrategy,\n  requireSessionBeforeCheck = true,\n) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n\n  // This check does not break the rules of hooks\n  // as the condition will remain the same for the whole app lifecycle\n  if (isProductionEnvironment()) {\n    return;\n  }\n\n  React.useEffect(() => {\n    if (!isLoaded || (routing && routing !== 'path')) {\n      return;\n    }\n\n    // For components that require an active session, like UserProfile\n    // we should not enforce the catch-all route if there is no session\n    // because these components are usually protected by the middleware\n    // and if the check runs before the session is available, it will fail\n    // even if the route is a catch-all route, as the check request will result\n    // in a 404 because of auth.protect();\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`,\n      );\n    };\n\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        // make sure to run this as soon as possible\n        // but don't run again when strict mode is enabled\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${\n            window.location.pathname\n          }/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch {\n          // no op\n        }\n        if (res?.status === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n\n    return () => {\n      // make sure to run this as soon as possible\n      // but don't run again when strict mode is enabled\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,+BAA+B;;AAExC,OAAO,WAAW;AAElB,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;;;;;;AASxB,MAAM,0BAA0B,CACrC,WACA,MACA,SACA,4BAA4B,IAAA,KACzB;IACH,MAAM,mRAAM,UAAA,CAAM,MAAA,CAAO,CAAC;IAC1B,MAAM,EAAE,WAAA,CAAY,CAAA,sTAAI,iBAAA,CAAe;IACvC,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,OAAI,uRAAA,CAAW;IAIzC,qRAAI,0BAAA,CAAwB,IAAG;QAC7B;IACF;IAEA,4QAAA,CAAA,UAAA,CAAM,SAAA;6CAAU,MAAM;YACpB,IAAI,CAAC,YAAa,WAAW,YAAY,QAAS;gBAChD;YACF;YAQA,IAAI,6BAA6B,CAAC,SAAS;gBACzC;YACF;YAEA,MAAM,KAAK,IAAI,gBAAgB;YAC/B,MAAM;2DAAQ,MAAM;oBAClB,MAAM,cAAc,cAAc,GAAG,IAAI,CAAA,iBAAA,CAAA,GAAsB,GAAG,IAAI,CAAA,qBAAA,CAAA;oBACtE,MAAM,IAAI,MACR,CAAA;YAAA,EACM,SAAS,CAAA;;QAAA,EAEb,IAAI,CAAA;mEAAA,EACuD,WAAW,CAAA,sCAAA,EAAyC,SAAS,CAAA;;QAAA,EAExH,SAAS,CAAA,oEAAA,EAAuE,IAAI,CAAA;wNAAA,EAC4H,IAAI,CAAA;AAAA,CAAA;gBAG1N;;YAEA,IAAI,aAAa;gBACf,IAAI,CAAC,YAAY,QAAA,CAAS,KAAA,CAAM,gBAAgB,GAAG;oBACjD,MAAM;gBACR;YACF,OAAO;gBACL,MAAM;+DAAQ,YAAY;wBAGxB,IAAI,OAAA;wBACJ,IAAI,IAAI,OAAA,GAAU,GAAG;4BACnB;wBACF;wBACA,IAAI;wBACJ,IAAI;4BACF,MAAM,MAAM,GAAG,OAAO,QAAA,CAAS,MAAM,GACnC,OAAO,QAAA,CAAS,QAClB,CAAA,CAAA,EAAI,SAAS,CAAA,sBAAA,EAAyB,KAAK,GAAA,CAAI,CAAC,EAAA;4BAChD,MAAM,MAAM,MAAM,KAAK;gCAAE,QAAQ,GAAG,MAAA;4BAAO,CAAC;wBAC9C,EAAA,OAAQ,CAER;wBACA,IAAA,CAAI,OAAA,OAAA,KAAA,IAAA,IAAK,MAAA,MAAW,KAAK;4BACvB,MAAM;wBACR;oBACF;;gBACA,KAAK,MAAM;YACb;YAEA;qDAAO,MAAM;oBAGX,IAAI,IAAI,OAAA,GAAU,GAAG;wBACnB,GAAG,KAAA,CAAM;oBACX;gBACF;;QACF;4CAAG;QAAC,QAAQ;KAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePathnameWithoutCatchAll.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { usePagesRouter } from './usePagesRouter';\n\nexport const usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef<string>();\n\n  const { pagesRouter } = usePagesRouter();\n\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      // in pages router things are simpler as the pathname includes the catch all route\n      // which starts with [[... and we can just remove it\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, '');\n      return pathRef.current;\n    }\n  }\n\n  // require is used to avoid importing next/navigation when the pages router is used,\n  // as it will throw an error. We cannot use dynamic import as it is async\n  // and we need the hook to be sync\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const usePathname = require('next/navigation').usePathname;\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const useParams = require('next/navigation').useParams;\n\n  // Get the pathname that includes any named or catch all params\n  // eg:\n  // the filesystem route /user/[id]/profile/[[...rest]]/page.tsx\n  // could give us the following pathname /user/123/profile/security\n  // if the user navigates to the security section of the user profile\n  const pathname = usePathname() || '';\n  const pathParts = pathname.split('/').filter(Boolean);\n  // the useParams hook returns an object with all named and catch all params\n  // for named params, the key in the returned object always contains a single value\n  // for catch all params, the key in the returned object contains an array of values\n  // we find the catch all params by checking if the value is an array\n  // and then we remove one path part for each catch all param\n  const catchAllParams = Object.values(useParams() || {})\n    .filter(v => Array.isArray(v))\n    .flat(Infinity);\n  // so we end up with the pathname where the components are mounted at\n  // eg /user/123/profile/security will return /user/123/profile as the path\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join('/')}`;\n    return pathRef.current;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,OAAO,WAAW;AAElB,SAAS,sBAAsB;;;;AAExB,MAAM,6BAA6B,MAAM;IAC9C,MAAM,uRAAU,UAAA,CAAM,MAAA,CAAe;IAErC,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,gUAAA,CAAe;IAEvC,IAAI,aAAa;QACf,IAAI,QAAQ,OAAA,EAAS;YACnB,OAAO,QAAQ,OAAA;QACjB,OAAO;YAGL,QAAQ,OAAA,GAAU,YAAY,QAAA,CAAS,OAAA,CAAQ,kBAAkB,EAAE;YACnE,OAAO,QAAQ,OAAA;QACjB;IACF;IAMA,MAAM,cAAc,QAAQ,iBAAiB,sJAAE,WAAA;IAE/C,MAAM,YAAY,QAAQ,iBAAiB,sJAAE,SAAA;IAO7C,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,YAAY,SAAS,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO;IAMpD,MAAM,iBAAiB,OAAO,MAAA,CAAO,UAAU,KAAK,CAAC,CAAC,EACnD,MAAA,CAAO,CAAA,IAAK,MAAM,OAAA,CAAQ,CAAC,CAAC,EAC5B,IAAA,CAAK,QAAQ;IAGhB,IAAI,QAAQ,OAAA,EAAS;QACnB,OAAO,QAAQ,OAAA;IACjB,OAAO;QACL,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,UAAU,KAAA,CAAM,GAAG,UAAU,MAAA,GAAS,eAAe,MAAM,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;QAC5F,OAAO,QAAQ,OAAA;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceRoutingProps.tsx"], "sourcesContent": ["import { useRoutingProps } from '@clerk/clerk-react/internal';\nimport type { RoutingOptions } from '@clerk/types';\n\nimport { useEnforceCatchAllRoute } from './useEnforceCatchAllRoute';\nimport { usePathnameWithoutCatchAll } from './usePathnameWithoutCatchAll';\n\nexport function useEnforceCorrectRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  requireSessionBeforeCheck = true,\n): T {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB;;AAGhC,SAAS,+BAA+B;AACxC,SAAS,kCAAkC;;;;;AAEpC,SAAS,8BACd,aAAA,EACA,KAAA,EACA,4BAA4B,IAAA,EACzB;IACH,MAAM,sUAAO,6BAAA,CAA2B;IACxC,MAAM,+SAAe,kBAAA,EAAgB,eAAe,OAAO;QAAE;IAAK,CAAC;IACnE,CAAA,GAAA,uTAAA,CAAA,0BAAA,EAAwB,eAAe,MAAM,aAAa,OAAA,EAAS,yBAAyB;IAC5F,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationSwitcher,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  UserButton,\n  GoogleOneTap,\n  Waitlist,\n  PricingTable,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": [], "mappings": ";;;;;;AAEA;;AAOA,OAAO,WAAW;AAElB,SAAS,qCAAqC;;;;;;;AAoBvC,MAAM,cAAsC,OAAO,MAAA,CACxD,CAAC,UAAkD;IACjD,OAAO,aAAA,GAAA,4QAAA,CAAA,UAAA,CAAA,aAAA,0RAAC,cAAA,EAAA;QAAiB,8TAAG,gCAAA,EAA8B,eAAe,KAAK,CAAA;IAAA,CAAG;AACnF,GACA;IAAE,4RAAG,cAAA;AAAgB;AAOhB,MAAM,sBAAsD,OAAO,MAAA,CACxE,CAAC,UAA0D;IACzD,OAAO,aAAA,GAAA,4QAAA,CAAA,UAAA,CAAA,aAAA,CAAC,+SAAA,EAAA;QAAyB,8TAAG,gCAAA,EAA8B,uBAAuB,KAAK,CAAA;IAAA,CAAG;AACnG,GACA;IAAE,2RAAG,uBAAA;AAAwB;AAGxB,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,4QAAA,CAAA,UAAA,CAAA,aAAA,yRAAC,UAAA,EAAA;QAAY,8TAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF;AAEO,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,4QAAA,CAAA,UAAA,CAAA,aAAA,0RAAC,SAAA,EAAA;QAAY,8TAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF", "ignoreList": [0], "debugId": null}}]}