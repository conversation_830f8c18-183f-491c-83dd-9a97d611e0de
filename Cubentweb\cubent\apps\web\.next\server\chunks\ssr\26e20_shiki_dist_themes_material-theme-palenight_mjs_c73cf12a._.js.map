{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/material-theme-palenight.mjs"], "sourcesContent": ["var materialThemePalenight = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#80CBC4\",\n    \"activityBar.background\": \"#292D3E\",\n    \"activityBar.border\": \"#292D3E60\",\n    \"activityBar.dropBackground\": \"#f0717880\",\n    \"activityBar.foreground\": \"#babed8\",\n    \"activityBarBadge.background\": \"#80CBC4\",\n    \"activityBarBadge.foreground\": \"#000000\",\n    \"badge.background\": \"#00000030\",\n    \"badge.foreground\": \"#676E95\",\n    \"breadcrumb.activeSelectionForeground\": \"#80CBC4\",\n    \"breadcrumb.background\": \"#292D3E\",\n    \"breadcrumb.focusForeground\": \"#babed8\",\n    \"breadcrumb.foreground\": \"#676E95\",\n    \"breadcrumbPicker.background\": \"#292D3E\",\n    \"button.background\": \"#717CB450\",\n    \"button.foreground\": \"#ffffff\",\n    \"debugConsole.errorForeground\": \"#f07178\",\n    \"debugConsole.infoForeground\": \"#89DDFF\",\n    \"debugConsole.warningForeground\": \"#FFCB6B\",\n    \"debugToolBar.background\": \"#292D3E\",\n    \"diffEditor.insertedTextBackground\": \"#89DDFF20\",\n    \"diffEditor.removedTextBackground\": \"#ff9cac20\",\n    \"dropdown.background\": \"#292D3E\",\n    \"dropdown.border\": \"#FFFFFF10\",\n    \"editor.background\": \"#292D3E\",\n    \"editor.findMatchBackground\": \"#000000\",\n    \"editor.findMatchBorder\": \"#80CBC4\",\n    \"editor.findMatchHighlight\": \"#babed8\",\n    \"editor.findMatchHighlightBackground\": \"#00000050\",\n    \"editor.findMatchHighlightBorder\": \"#ffffff30\",\n    \"editor.findRangeHighlightBackground\": \"#FFCB6B30\",\n    \"editor.foreground\": \"#babed8\",\n    \"editor.lineHighlightBackground\": \"#00000050\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#FFFFFF0d\",\n    \"editor.selectionBackground\": \"#717CB450\",\n    \"editor.selectionHighlightBackground\": \"#FFCC0020\",\n    \"editor.wordHighlightBackground\": \"#ff9cac30\",\n    \"editor.wordHighlightStrongBackground\": \"#C3E88D30\",\n    \"editorBracketMatch.background\": \"#292D3E\",\n    \"editorBracketMatch.border\": \"#FFCC0050\",\n    \"editorCursor.foreground\": \"#FFCC00\",\n    \"editorError.foreground\": \"#f0717870\",\n    \"editorGroup.border\": \"#00000030\",\n    \"editorGroup.dropBackground\": \"#f0717880\",\n    \"editorGroup.focusedEmptyBorder\": \"#f07178\",\n    \"editorGroupHeader.tabsBackground\": \"#292D3E\",\n    \"editorGutter.addedBackground\": \"#C3E88D60\",\n    \"editorGutter.deletedBackground\": \"#f0717860\",\n    \"editorGutter.modifiedBackground\": \"#82AAFF60\",\n    \"editorHoverWidget.background\": \"#292D3E\",\n    \"editorHoverWidget.border\": \"#FFFFFF10\",\n    \"editorIndentGuide.activeBackground\": \"#4E5579\",\n    \"editorIndentGuide.background\": \"#4E557970\",\n    \"editorInfo.foreground\": \"#82AAFF70\",\n    \"editorLineNumber.activeForeground\": \"#676E95\",\n    \"editorLineNumber.foreground\": \"#3A3F58\",\n    \"editorLink.activeForeground\": \"#babed8\",\n    \"editorMarkerNavigation.background\": \"#babed805\",\n    \"editorOverviewRuler.border\": \"#292D3E\",\n    \"editorOverviewRuler.errorForeground\": \"#f0717840\",\n    \"editorOverviewRuler.findMatchForeground\": \"#80CBC4\",\n    \"editorOverviewRuler.infoForeground\": \"#82AAFF40\",\n    \"editorOverviewRuler.warningForeground\": \"#FFCB6B40\",\n    \"editorRuler.foreground\": \"#4E5579\",\n    \"editorSuggestWidget.background\": \"#292D3E\",\n    \"editorSuggestWidget.border\": \"#FFFFFF10\",\n    \"editorSuggestWidget.foreground\": \"#babed8\",\n    \"editorSuggestWidget.highlightForeground\": \"#80CBC4\",\n    \"editorSuggestWidget.selectedBackground\": \"#00000050\",\n    \"editorWarning.foreground\": \"#FFCB6B70\",\n    \"editorWhitespace.foreground\": \"#babed840\",\n    \"editorWidget.background\": \"#292D3E\",\n    \"editorWidget.border\": \"#80CBC4\",\n    \"editorWidget.resizeBorder\": \"#80CBC4\",\n    \"extensionBadge.remoteForeground\": \"#babed8\",\n    \"extensionButton.prominentBackground\": \"#C3E88D90\",\n    \"extensionButton.prominentForeground\": \"#babed8\",\n    \"extensionButton.prominentHoverBackground\": \"#C3E88D\",\n    \"focusBorder\": \"#FFFFFF00\",\n    \"foreground\": \"#babed8\",\n    \"gitDecoration.conflictingResourceForeground\": \"#FFCB6B90\",\n    \"gitDecoration.deletedResourceForeground\": \"#f0717890\",\n    \"gitDecoration.ignoredResourceForeground\": \"#676E9590\",\n    \"gitDecoration.modifiedResourceForeground\": \"#82AAFF90\",\n    \"gitDecoration.untrackedResourceForeground\": \"#C3E88D90\",\n    \"input.background\": \"#333747\",\n    \"input.border\": \"#FFFFFF10\",\n    \"input.foreground\": \"#babed8\",\n    \"input.placeholderForeground\": \"#babed860\",\n    \"inputOption.activeBackground\": \"#babed830\",\n    \"inputOption.activeBorder\": \"#babed830\",\n    \"inputValidation.errorBorder\": \"#f07178\",\n    \"inputValidation.infoBorder\": \"#82AAFF\",\n    \"inputValidation.warningBorder\": \"#FFCB6B\",\n    \"list.activeSelectionBackground\": \"#292D3E\",\n    \"list.activeSelectionForeground\": \"#80CBC4\",\n    \"list.dropBackground\": \"#f0717880\",\n    \"list.focusBackground\": \"#babed820\",\n    \"list.focusForeground\": \"#babed8\",\n    \"list.highlightForeground\": \"#80CBC4\",\n    \"list.hoverBackground\": \"#292D3E\",\n    \"list.hoverForeground\": \"#FFFFFF\",\n    \"list.inactiveSelectionBackground\": \"#00000030\",\n    \"list.inactiveSelectionForeground\": \"#80CBC4\",\n    \"listFilterWidget.background\": \"#00000030\",\n    \"listFilterWidget.noMatchesOutline\": \"#00000030\",\n    \"listFilterWidget.outline\": \"#00000030\",\n    \"menu.background\": \"#292D3E\",\n    \"menu.foreground\": \"#babed8\",\n    \"menu.selectionBackground\": \"#00000050\",\n    \"menu.selectionBorder\": \"#00000030\",\n    \"menu.selectionForeground\": \"#80CBC4\",\n    \"menu.separatorBackground\": \"#babed8\",\n    \"menubar.selectionBackground\": \"#00000030\",\n    \"menubar.selectionBorder\": \"#00000030\",\n    \"menubar.selectionForeground\": \"#80CBC4\",\n    \"notebook.focusedCellBorder\": \"#80CBC4\",\n    \"notebook.inactiveFocusedCellBorder\": \"#80CBC450\",\n    \"notificationLink.foreground\": \"#80CBC4\",\n    \"notifications.background\": \"#292D3E\",\n    \"notifications.foreground\": \"#babed8\",\n    \"panel.background\": \"#292D3E\",\n    \"panel.border\": \"#292D3E60\",\n    \"panel.dropBackground\": \"#babed8\",\n    \"panelTitle.activeBorder\": \"#80CBC4\",\n    \"panelTitle.activeForeground\": \"#FFFFFF\",\n    \"panelTitle.inactiveForeground\": \"#babed8\",\n    \"peekView.border\": \"#00000030\",\n    \"peekViewEditor.background\": \"#333747\",\n    \"peekViewEditor.matchHighlightBackground\": \"#717CB450\",\n    \"peekViewEditorGutter.background\": \"#333747\",\n    \"peekViewResult.background\": \"#333747\",\n    \"peekViewResult.matchHighlightBackground\": \"#717CB450\",\n    \"peekViewResult.selectionBackground\": \"#676E9570\",\n    \"peekViewTitle.background\": \"#333747\",\n    \"peekViewTitleDescription.foreground\": \"#babed860\",\n    \"pickerGroup.border\": \"#FFFFFF1a\",\n    \"pickerGroup.foreground\": \"#80CBC4\",\n    \"progressBar.background\": \"#80CBC4\",\n    \"quickInput.background\": \"#292D3E\",\n    \"quickInput.foreground\": \"#676E95\",\n    \"quickInput.list.focusBackground\": \"#babed820\",\n    \"sash.hoverBorder\": \"#80CBC450\",\n    \"scrollbar.shadow\": \"#00000030\",\n    \"scrollbarSlider.activeBackground\": \"#80CBC4\",\n    \"scrollbarSlider.background\": \"#A6ACCD20\",\n    \"scrollbarSlider.hoverBackground\": \"#A6ACCD10\",\n    \"selection.background\": \"#00000080\",\n    \"settings.checkboxBackground\": \"#292D3E\",\n    \"settings.checkboxForeground\": \"#babed8\",\n    \"settings.dropdownBackground\": \"#292D3E\",\n    \"settings.dropdownForeground\": \"#babed8\",\n    \"settings.headerForeground\": \"#80CBC4\",\n    \"settings.modifiedItemIndicator\": \"#80CBC4\",\n    \"settings.numberInputBackground\": \"#292D3E\",\n    \"settings.numberInputForeground\": \"#babed8\",\n    \"settings.textInputBackground\": \"#292D3E\",\n    \"settings.textInputForeground\": \"#babed8\",\n    \"sideBar.background\": \"#292D3E\",\n    \"sideBar.border\": \"#292D3E60\",\n    \"sideBar.foreground\": \"#676E95\",\n    \"sideBarSectionHeader.background\": \"#292D3E\",\n    \"sideBarSectionHeader.border\": \"#292D3E60\",\n    \"sideBarTitle.foreground\": \"#babed8\",\n    \"statusBar.background\": \"#292D3E\",\n    \"statusBar.border\": \"#292D3E60\",\n    \"statusBar.debuggingBackground\": \"#C792EA\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.foreground\": \"#676E95\",\n    \"statusBar.noFolderBackground\": \"#292D3E\",\n    \"statusBarItem.activeBackground\": \"#f0717880\",\n    \"statusBarItem.hoverBackground\": \"#676E9520\",\n    \"statusBarItem.remoteBackground\": \"#80CBC4\",\n    \"statusBarItem.remoteForeground\": \"#000000\",\n    \"tab.activeBackground\": \"#292D3E\",\n    \"tab.activeBorder\": \"#80CBC4\",\n    \"tab.activeForeground\": \"#FFFFFF\",\n    \"tab.activeModifiedBorder\": \"#676E95\",\n    \"tab.border\": \"#292D3E\",\n    \"tab.inactiveBackground\": \"#292D3E\",\n    \"tab.inactiveForeground\": \"#676E95\",\n    \"tab.inactiveModifiedBorder\": \"#904348\",\n    \"tab.unfocusedActiveBorder\": \"#676E95\",\n    \"tab.unfocusedActiveForeground\": \"#babed8\",\n    \"tab.unfocusedActiveModifiedBorder\": \"#c05a60\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#904348\",\n    \"terminal.ansiBlack\": \"#000000\",\n    \"terminal.ansiBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightBlack\": \"#676E95\",\n    \"terminal.ansiBrightBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightCyan\": \"#89DDFF\",\n    \"terminal.ansiBrightGreen\": \"#C3E88D\",\n    \"terminal.ansiBrightMagenta\": \"#C792EA\",\n    \"terminal.ansiBrightRed\": \"#f07178\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#FFCB6B\",\n    \"terminal.ansiCyan\": \"#89DDFF\",\n    \"terminal.ansiGreen\": \"#C3E88D\",\n    \"terminal.ansiMagenta\": \"#C792EA\",\n    \"terminal.ansiRed\": \"#f07178\",\n    \"terminal.ansiWhite\": \"#ffffff\",\n    \"terminal.ansiYellow\": \"#FFCB6B\",\n    \"terminalCursor.background\": \"#000000\",\n    \"terminalCursor.foreground\": \"#FFCB6B\",\n    \"textLink.activeForeground\": \"#babed8\",\n    \"textLink.foreground\": \"#80CBC4\",\n    \"titleBar.activeBackground\": \"#292D3E\",\n    \"titleBar.activeForeground\": \"#babed8\",\n    \"titleBar.border\": \"#292D3E60\",\n    \"titleBar.inactiveBackground\": \"#292D3E\",\n    \"titleBar.inactiveForeground\": \"#676E95\",\n    \"tree.indentGuidesStroke\": \"#4E5579\",\n    \"widget.shadow\": \"#00000030\"\n  },\n  \"displayName\": \"Material Theme Palenight\",\n  \"name\": \"material-theme-palenight\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#292D3E\",\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"punctuation, constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape, text.html constant.character.entity.named\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"variable, variable.parameter, support.variable, variable.language, support.constant, meta.definition.variable entity.name.function, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"keyword, modifier, variable.language.this, support.type.object, constant.language\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, support.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"storage.type, storage.modifier, storage.control\",\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": \"support.module, support.node\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"support.type, constant.other.key\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type, entity.other.inherited-class, entity.other\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#676E95\"\n      }\n    },\n    {\n      \"scope\": \"comment punctuation.definition.comment, string.quoted.docstring\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#676E95\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name, entity.name.type.class, support.type, support.class, meta.use\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property, meta.field.declaration entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.method entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.function entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"template.expression.begin, template.expression.end, punctuation.definition.template-expression.begin, punctuation.definition.template-expression.end\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded, source.groovy.embedded, meta.template.expression\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.yaml\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.object-literal.key, meta.object-literal.key string, support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.json\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#B2CCD6\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag, punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.html\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown meta.link.inline, meta.link.reference\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown beginning.punctuation.definition.list\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold markup.italic, markup.italic markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"italic bold\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.fenced_code.block.markdown punctuation.definition.markdown\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.definition.ini\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.group-title.ini\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.class.identifier storage.type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.identifier entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method-call meta.method, source.cs entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.cs storage.type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.return-type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#676E95\"\n      }\n    },\n    {\n      \"scope\": \"source.cs entity.name.type.namespace\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"meta.jsx.children, SXNested\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"support.class.component\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cpp meta.block variable.other\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.member.access.python\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.function-call.python, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.block\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.call\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.php support.other.namespace, source.php meta.use support.class\",\n      \"settings\": {\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"settings\": {\n        \"background\": \"#292D3E\",\n        \"foreground\": \"#babed8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.format.placeholder.other.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#babed890\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#916b53\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { materialThemePalenight as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,yBAAyB,OAAO,MAAM,CAAC;IACzC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,6BAA6B;QAC7B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,kCAAkC;QAClC,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,8BAA8B;QAC9B,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,yCAAyC;QACzC,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,6BAA6B;QAC7B,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,+BAA+B;QAC/B,8BAA8B;QAC9B,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,8BAA8B;QAC9B,sCAAsC;QACtC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,oBAAoB;QACpB,gBAAgB;QAChB,wBAAwB;QACxB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,mCAAmC;QACnC,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,uCAAuC;QACvC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}