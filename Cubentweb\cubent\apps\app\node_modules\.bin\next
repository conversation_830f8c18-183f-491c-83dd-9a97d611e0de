#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/bin/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/bin/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
