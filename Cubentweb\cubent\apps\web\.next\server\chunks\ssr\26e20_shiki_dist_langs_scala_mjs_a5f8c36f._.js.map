{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/scala.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Scala\", \"fileTypes\": [\"scala\"], \"firstLineMatch\": \"^#!/.*\\\\b\\\\w*scala\\\\b\", \"foldingStartMarker\": \"/\\\\*\\\\*|\\\\{\\\\s*$\", \"foldingStopMarker\": \"\\\\*\\\\*/|^\\\\s*\\\\}\", \"name\": \"scala\", \"patterns\": [{ \"include\": \"#code\" }], \"repository\": { \"backQuotedVariable\": { \"match\": \"`[^`]+`\" }, \"block-comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.scala\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.scala\" }, { \"begin\": \"^\\\\s*(/\\\\*\\\\*)(?!/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.scala\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scala\" } }, \"name\": \"comment.block.documentation.scala\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.scaladoc.scala\" }, \"2\": { \"name\": \"variable.parameter.scala\" } }, \"match\": \"(@param)\\\\s+(\\\\S+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.scaladoc.scala\" }, \"2\": { \"name\": \"entity.name.class\" } }, \"match\": \"(@(?:tparam|throws))\\\\s+(\\\\S+)\" }, { \"match\": \"@(return|see|note|example|constructor|usecase|author|version|since|todo|deprecated|migration|define|inheritdoc)\\\\b\", \"name\": \"keyword.other.documentation.scaladoc.scala\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.documentation.link.scala\" }, \"2\": { \"name\": \"string.other.link.title.markdown\" }, \"3\": { \"name\": \"punctuation.definition.documentation.link.scala\" } }, \"match\": \"(\\\\[\\\\[)([^\\\\]]+)(\\\\]\\\\])\" }, { \"include\": \"#block-comments\" }] }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.scala\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.scala\", \"patterns\": [{ \"include\": \"#block-comments\" }] }] }, \"char-literal\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.character.begin.scala\" }, \"2\": { \"name\": \"punctuation.definition.character.end.scala\" } }, \"match\": \"(')'(')\", \"name\": \"string.quoted.other constant.character.literal.scala\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.character.begin.scala\" } }, \"end\": \"'|$\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.character.end.scala\" } }, \"name\": \"string.quoted.other constant.character.literal.scala\", \"patterns\": [{ \"match\": `\\\\\\\\(?:[btnfr\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})`, \"name\": \"constant.character.escape.scala\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-character-escape.scala\" }, { \"match\": \"[^']{2,}\", \"name\": \"invalid.illegal.character-literal-too-long\" }, { \"match\": \"(?<!')[^']\", \"name\": \"invalid.illegal.character-literal-too-long\" }] }] }, \"code\": { \"patterns\": [{ \"include\": \"#using-directive\" }, { \"include\": \"#script-header\" }, { \"include\": \"#storage-modifiers\" }, { \"include\": \"#declarations\" }, { \"include\": \"#inheritance\" }, { \"include\": \"#extension\" }, { \"include\": \"#imports\" }, { \"include\": \"#exports\" }, { \"include\": \"#comments\" }, { \"include\": \"#strings\" }, { \"include\": \"#initialization\" }, { \"include\": \"#xml-literal\" }, { \"include\": \"#keywords\" }, { \"include\": \"#using\" }, { \"include\": \"#constants\" }, { \"include\": \"#singleton-type\" }, { \"include\": \"#inline\" }, { \"include\": \"#scala-quoted-or-symbol\" }, { \"include\": \"#char-literal\" }, { \"include\": \"#empty-parentheses\" }, { \"include\": \"#parameter-list\" }, { \"include\": \"#qualifiedClassName\" }, { \"include\": \"#backQuotedVariable\" }, { \"include\": \"#curly-braces\" }, { \"include\": \"#meta-brackets\" }, { \"include\": \"#meta-bounds\" }, { \"include\": \"#meta-colons\" }] }, \"comments\": { \"patterns\": [{ \"include\": \"#block-comments\" }, { \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.scala\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scala\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-slash.scala\" }] }] }, \"constants\": { \"patterns\": [{ \"match\": \"\\\\b(false|null|true)\\\\b\", \"name\": \"constant.language.scala\" }, { \"match\": \"\\\\b(0[xX][0-9a-fA-F_]*)\\\\b\", \"name\": \"constant.numeric.scala\" }, { \"match\": \"\\\\b((\\\\d[0-9_]*(\\\\.\\\\d[0-9_]*)?)([eE](\\\\+|-)?\\\\d[0-9_]*)?|\\\\d[0-9_]*)[LlFfDd]?\\\\b\", \"name\": \"constant.numeric.scala\" }, { \"match\": \"(\\\\.\\\\d[0-9_]*)([eE](\\\\+|-)?\\\\d[0-9_]*)?[LlFfDd]?\\\\b\", \"name\": \"constant.numeric.scala\" }, { \"match\": \"\\\\b0[bB][01]([01_]*[01])?[Ll]?\\\\b\", \"name\": \"constant.numeric.scala\" }, { \"match\": \"\\\\b(this|super)\\\\b\", \"name\": \"variable.language.scala\" }] }, \"curly-braces\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.block.begin.scala\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.block.end.scala\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, \"declarations\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"entity.name.function.declaration\" } }, \"match\": \"\\\\b(def)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))?\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"entity.name.class.declaration\" } }, \"match\": \"\\\\b(trait)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))?\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"keyword.declaration.scala\" }, \"3\": { \"name\": \"entity.name.class.declaration\" } }, \"match\": \"\\\\b(?:(case)\\\\s+)?(class|object|enum)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))?\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"entity.name.type.declaration\" } }, \"match\": \"(?<!\\\\.)\\\\b(type)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))?\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.stable.scala\" }, \"2\": { \"name\": \"keyword.declaration.volatile.scala\" } }, \"match\": \"\\\\b(?:(val)|(var))\\\\b\\\\s*(?!//|/\\\\*)(?=(?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`)?\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.stable.scala\" }, \"2\": { \"name\": \"variable.stable.declaration.scala\" } }, \"match\": '\\\\b(val)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`)(?:\\\\s*,\\\\s*(?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))*)?(?!\")' }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.volatile.scala\" }, \"2\": { \"name\": \"variable.volatile.declaration.scala\" } }, \"match\": '\\\\b(var)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`)(?:\\\\s*,\\\\s*(?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))*)?(?!\")' }, { \"captures\": { \"1\": { \"name\": \"keyword.other.package.scala\" }, \"2\": { \"name\": \"keyword.declaration.scala\" }, \"3\": { \"name\": \"entity.name.class.declaration\" } }, \"match\": \"\\\\b(package)\\\\s+(object)\\\\b\\\\s*(?!//|/\\\\*)((?:(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`))?\" }, { \"begin\": \"\\\\b(package)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.package.scala\" } }, \"end\": \"(?<=[\\\\n;])\", \"name\": \"meta.package.scala\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+))\", \"name\": \"entity.name.package.scala\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.definition.package\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"entity.name.given.declaration\" } }, \"match\": \"\\\\b(given)\\\\b\\\\s*([_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|`[^`]+`)?\" }] }, \"empty-parentheses\": { \"captures\": { \"1\": { \"name\": \"meta.bracket.scala\" } }, \"match\": \"(\\\\(\\\\))\", \"name\": \"meta.parentheses.scala\" }, \"exports\": { \"begin\": \"\\\\b(export)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.export.scala\" } }, \"end\": \"(?<=[\\\\n;])\", \"name\": \"meta.export.scala\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(given)\\\\b\", \"name\": \"keyword.other.export.given.scala\" }, { \"match\": \"[A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?\", \"name\": \"entity.name.class.export.scala\" }, { \"match\": \"(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+))\", \"name\": \"entity.name.export.scala\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.definition.export\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"meta.bracket.scala\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"meta.bracket.scala\" } }, \"name\": \"meta.export.selector.scala\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.export.given.scala\" }, \"2\": { \"name\": \"entity.name.class.export.renamed-from.scala\" }, \"3\": { \"name\": \"entity.name.export.renamed-from.scala\" }, \"4\": { \"name\": \"keyword.other.arrow.scala\" }, \"5\": { \"name\": \"entity.name.class.export.renamed-to.scala\" }, \"6\": { \"name\": \"entity.name.export.renamed-to.scala\" } }, \"match\": \"(given\\\\s)?\\\\s*(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\\\\s*(=>)\\\\s*(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\\\\s*\" }, { \"match\": \"\\\\b(given)\\\\b\", \"name\": \"keyword.other.export.given.scala\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.export.given.scala\" }, \"2\": { \"name\": \"entity.name.class.export.scala\" }, \"3\": { \"name\": \"entity.name.export.scala\" } }, \"match\": \"(given\\\\s+)?(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\" }] }] }, \"extension\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" } }, \"match\": \"^\\\\s*(extension)\\\\s+(?=[\\\\[(])\" }] }, \"imports\": { \"begin\": \"\\\\b(import)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.import.scala\" } }, \"end\": \"(?<=[\\\\n;])\", \"name\": \"meta.import.scala\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(given)\\\\b\", \"name\": \"keyword.other.import.given.scala\" }, { \"match\": \"\\\\s(as)\\\\s\", \"name\": \"keyword.other.import.as.scala\" }, { \"match\": \"[A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?\", \"name\": \"entity.name.class.import.scala\" }, { \"match\": \"(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+))\", \"name\": \"entity.name.import.scala\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.definition.import\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"meta.bracket.scala\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"meta.bracket.scala\" } }, \"name\": \"meta.import.selector.scala\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.import.given.scala\" }, \"2\": { \"name\": \"entity.name.class.import.renamed-from.scala\" }, \"3\": { \"name\": \"entity.name.import.renamed-from.scala\" }, \"4\": { \"name\": \"keyword.other.arrow.scala\" }, \"5\": { \"name\": \"entity.name.class.import.renamed-to.scala\" }, \"6\": { \"name\": \"entity.name.import.renamed-to.scala\" } }, \"match\": \"(given\\\\s)?\\\\s*(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\\\\s*(=>)\\\\s*(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\\\\s*\" }, { \"match\": \"\\\\b(given)\\\\b\", \"name\": \"keyword.other.import.given.scala\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.import.given.scala\" }, \"2\": { \"name\": \"entity.name.class.import.scala\" }, \"3\": { \"name\": \"entity.name.import.scala\" } }, \"match\": \"(given\\\\s+)?(?:([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))\" }] }] }, \"inheritance\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" }, \"2\": { \"name\": \"entity.name.class\" } }, \"match\": '\\\\b(extends|with|derives)\\\\b\\\\s*([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|`[^`]+`|(?=\\\\([^)]+=>)|(?=(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+))|(?=\"))?' }] }, \"initialization\": { \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" } }, \"match\": \"\\\\b(new)\\\\b\" }, \"inline\": { \"patterns\": [{ \"match\": \"\\\\b(inline)(?=\\\\s+((?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)|`[^`]+`)\\\\s*:)\", \"name\": \"storage.modifier.other\" }, { \"match\": \"\\\\b(inline)\\\\b(?=(?:.(?!\\\\b(?:val|def|given)\\\\b))*\\\\b(if|match)\\\\b)\", \"name\": \"keyword.control.flow.scala\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(return|throw)\\\\b\", \"name\": \"keyword.control.flow.jump.scala\" }, { \"match\": \"\\\\b(classOf|isInstanceOf|asInstanceOf)\\\\b\", \"name\": \"support.function.type-of.scala\" }, { \"match\": \"\\\\b(else|if|then|do|while|for|yield|match|case)\\\\b\", \"name\": \"keyword.control.flow.scala\" }, { \"match\": \"^\\\\s*(end)\\\\s+(if|while|for|match)(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\", \"name\": \"keyword.control.flow.end.scala\" }, { \"match\": \"^\\\\s*(end)\\\\s+(val)(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\", \"name\": \"keyword.declaration.stable.end.scala\" }, { \"match\": \"^\\\\s*(end)\\\\s+(var)(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\", \"name\": \"keyword.declaration.volatile.end.scala\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.end.scala\" }, \"2\": { \"name\": \"keyword.declaration.end.scala\" }, \"3\": { \"name\": \"entity.name.type.declaration\" } }, \"match\": \"^\\\\s*(end)\\\\s+(?:(new|extension)|([A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?))(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\" }, { \"match\": \"\\\\b(catch|finally|try)\\\\b\", \"name\": \"keyword.control.exception.scala\" }, { \"match\": \"^\\\\s*(end)\\\\s+(try)(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\", \"name\": \"keyword.control.exception.end.scala\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.end.scala\" }, \"2\": { \"name\": \"entity.name.declaration\" } }, \"match\": \"^\\\\s*(end)\\\\s+(`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+))?(?=\\\\s*(//.*|/\\\\*(?!.*\\\\*/\\\\s*\\\\S.*).*)?$)\" }, { \"match\": \"(==?|!=|<=|>=|<>|<|>)\", \"name\": \"keyword.operator.comparison.scala\" }, { \"match\": \"(-|\\\\+|\\\\*|/(?![/*])|%|~)\", \"name\": \"keyword.operator.arithmetic.scala\" }, { \"match\": \"(?<![!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]|_)(!|&&|\\\\|\\\\|)(?![!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}])\", \"name\": \"keyword.operator.logical.scala\" }, { \"match\": \"(<-|\\u2190|->|\\u2192|=>|\\u21D2|\\\\?|:+|@|\\\\|)+\", \"name\": \"keyword.operator.scala\" }] }, \"meta-bounds\": { \"comment\": \"For themes: Matching view bounds\", \"match\": \"<%|=:=|<:<|<%<|>:|<:\", \"name\": \"meta.bounds.scala\" }, \"meta-brackets\": { \"comment\": \"For themes: Brackets look nice when colored.\", \"patterns\": [{ \"comment\": \"The punctuation.section.*.begin is needed for return snippet in source bundle\", \"match\": \"\\\\{\", \"name\": \"punctuation.section.block.begin.scala\" }, { \"comment\": \"The punctuation.section.*.end is needed for return snippet in source bundle\", \"match\": \"\\\\}\", \"name\": \"punctuation.section.block.end.scala\" }, { \"match\": \"{|}|\\\\(|\\\\)|\\\\[|\\\\]\", \"name\": \"meta.bracket.scala\" }] }, \"meta-colons\": { \"comment\": \"For themes: Matching type colons\", \"patterns\": [{ \"match\": \"(?<!:):(?!:)\", \"name\": \"meta.colon.scala\" }] }, \"parameter-list\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.scala\" }, \"2\": { \"name\": \"meta.colon.scala\" } }, \"match\": \"(?<=[^\\\\._$a-zA-Z0-9])(`[^`]+`|[_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)\\\\s*(:)\\\\s+\" }] }, \"qualifiedClassName\": { \"captures\": { \"1\": { \"name\": \"entity.name.class\" } }, \"match\": \"(\\\\b([A-Z][\\\\w]*)(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?)\" }, \"scala-quoted-or-symbol\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.flow.staging.scala constant.other.symbol.scala\" }, \"2\": { \"name\": \"constant.other.symbol.scala\" } }, \"match\": \"(')((?>(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)))(?!')\" }, { \"match\": \"'(?=\\\\s*\\\\{(?!'))\", \"name\": \"keyword.control.flow.staging.scala\" }, { \"match\": \"'(?=\\\\s*\\\\[(?!'))\", \"name\": \"keyword.control.flow.staging.scala\" }, { \"match\": \"\\\\$(?=\\\\s*\\\\{)\", \"name\": \"keyword.control.flow.staging.scala\" }] }, \"script-header\": { \"captures\": { \"1\": { \"name\": \"string.unquoted.shebang.scala\" } }, \"match\": \"^#!(.*)$\", \"name\": \"comment.block.shebang.scala\" }, \"singleton-type\": { \"captures\": { \"1\": { \"name\": \"keyword.type.scala\" } }, \"match\": \"\\\\.(type)(?![A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|\\\\d)\" }, \"storage-modifiers\": { \"patterns\": [{ \"match\": \"\\\\b(private\\\\[\\\\S+\\\\]|protected\\\\[\\\\S+\\\\]|private|protected)\\\\b\", \"name\": \"storage.modifier.access\" }, { \"match\": \"\\\\b(synchronized|@volatile|abstract|final|lazy|sealed|implicit|override|@transient|@native)\\\\b\", \"name\": \"storage.modifier.other\" }, { \"match\": \"(?<=^|\\\\s)\\\\b(transparent|opaque|infix|open|inline)\\\\b(?=[a-z\\\\s]*\\\\b(def|val|var|given|type|class|trait|object|enum)\\\\b)\", \"name\": \"storage.modifier.other\" }] }, \"string-interpolation\": { \"patterns\": [{ \"match\": \"\\\\$\\\\$\", \"name\": \"constant.character.escape.interpolation.scala\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.template-expression.begin.scala\" } }, \"match\": \"(\\\\$)([A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*)\", \"name\": \"meta.template.expression.scala\" }, { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.scala\" } }, \"contentName\": \"meta.embedded.line.scala\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.scala\" } }, \"name\": \"meta.template.expression.scala\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"strings\": { \"patterns\": [{ \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.scala\" } }, \"end\": '\"\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scala\" } }, \"name\": \"string.quoted.triple.scala\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\\\\\|\\\\\\\\u[0-9A-Fa-f]{4}\", \"name\": \"constant.character.escape.scala\" }] }, { \"begin\": '\\\\b(raw)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.interpolation.scala\" }, \"2\": { \"name\": \"string.quoted.triple.interpolated.scala punctuation.definition.string.begin.scala\" } }, \"end\": '(\"\"\")(?!\")|\\\\$\\n|(\\\\$[^$\"_{A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}])', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.triple.interpolated.scala punctuation.definition.string.end.scala\" }, \"2\": { \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" } }, \"patterns\": [{ \"match\": '\\\\$[$\"]', \"name\": \"constant.character.escape.scala\" }, { \"include\": \"#string-interpolation\" }, { \"match\": \".\", \"name\": \"string.quoted.triple.interpolated.scala\" }] }, { \"begin\": '\\\\b((?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?))(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.interpolation.scala\" }, \"2\": { \"name\": \"string.quoted.triple.interpolated.scala punctuation.definition.string.begin.scala\" } }, \"end\": '(\"\"\")(?!\")|\\\\$\\n|(\\\\$[^$\"_{A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}])', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.triple.interpolated.scala punctuation.definition.string.end.scala\" }, \"2\": { \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" } }, \"patterns\": [{ \"include\": \"#string-interpolation\" }, { \"match\": \"\\\\\\\\\\\\\\\\|\\\\\\\\u[0-9A-Fa-f]{4}\", \"name\": \"constant.character.escape.scala\" }, { \"match\": \".\", \"name\": \"string.quoted.triple.interpolated.scala\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.scala\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scala\" } }, \"name\": \"string.quoted.double.scala\", \"patterns\": [{ \"match\": `\\\\\\\\(?:[btnfr\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})`, \"name\": \"constant.character.escape.scala\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" }] }, { \"begin\": '\\\\b(raw)(\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.interpolation.scala\" }, \"2\": { \"name\": \"string.quoted.double.interpolated.scala punctuation.definition.string.begin.scala\" } }, \"end\": '(\")|\\\\$\\n|(\\\\$[^$\"_{A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}])', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.double.interpolated.scala punctuation.definition.string.end.scala\" }, \"2\": { \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" } }, \"patterns\": [{ \"match\": '\\\\$[$\"]', \"name\": \"constant.character.escape.scala\" }, { \"include\": \"#string-interpolation\" }, { \"match\": \".\", \"name\": \"string.quoted.double.interpolated.scala\" }] }, { \"begin\": '\\\\b((?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?))(\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.interpolation.scala\" }, \"2\": { \"name\": \"string.quoted.double.interpolated.scala punctuation.definition.string.begin.scala\" } }, \"end\": '(\")|\\\\$\\n|(\\\\$[^$\"_{A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}])', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.double.interpolated.scala punctuation.definition.string.end.scala\" }, \"2\": { \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" } }, \"patterns\": [{ \"match\": '\\\\$[$\"]', \"name\": \"constant.character.escape.scala\" }, { \"include\": \"#string-interpolation\" }, { \"match\": `\\\\\\\\(?:[btnfr\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})`, \"name\": \"constant.character.escape.scala\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.scala\" }, { \"match\": \".\", \"name\": \"string.quoted.double.interpolated.scala\" }] }] }, \"using\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.declaration.scala\" } }, \"match\": \"(?<=\\\\()\\\\s*(using)\\\\s\" }] }, \"using-directive\": { \"begin\": \"^\\\\s*(//>)\\\\s*(using)[^\\\\S\\\\n]+(?:(\\\\S+))?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.scala\" }, \"2\": { \"name\": \"keyword.other.import.scala\" }, \"3\": { \"patterns\": [{ \"match\": \"[A-Z\\\\p{Lt}\\\\p{Lu}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|`[^`]+`|(?:[A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}][A-Z\\\\p{Lt}\\\\p{Lu}_a-z$\\\\p{Lo}\\\\p{Nl}\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)?|[!#%&*+\\\\-\\\\/:<>=?@^|~\\\\p{Sm}\\\\p{So}]+)\", \"name\": \"entity.name.import.scala\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.definition.import\" }] } }, \"end\": \"\\\\n\", \"name\": \"comment.line.shebang.scala\", \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"match\": \"[^\\\\s,]+\", \"name\": \"string.quoted.double.scala\" }] }, \"xml-doublequotedString\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.double.xml\", \"patterns\": [{ \"include\": \"#xml-entity\" }] }, \"xml-embedded-content\": { \"patterns\": [{ \"begin\": \"{\", \"captures\": { \"0\": { \"name\": \"meta.bracket.scala\" } }, \"end\": \"}\", \"name\": \"meta.source.embedded.scala\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.xml\" }, \"2\": { \"name\": \"entity.other.attribute-name.xml\" }, \"3\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.xml\" } }, \"match\": \" (?:([-_a-zA-Z0-9]+)((:)))?([_a-zA-Z-]+)=\" }, { \"include\": \"#xml-doublequotedString\" }, { \"include\": \"#xml-singlequotedString\" }] }, \"xml-entity\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" }, \"3\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"match\": \"(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#\\\\d+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.xml\" }, \"xml-literal\": { \"patterns\": [{ \"begin\": \"(<)((?:([_a-zA-Z0-9][_a-zA-Z0-9]*)((:)))?([_a-zA-Z0-9][-_a-zA-Z0-9:]*))(?=(\\\\s[^>]*)?></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"3\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"4\": { \"name\": \"entity.name.tag.xml\" }, \"5\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"6\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"comment\": \"We do not allow a tag name to start with a - since this would likely conflict with the <- operator. This is not very common for tag names anyway.  Also code such as -- if (val <val2 || val> val3) will falsly be recognized as an xml tag.  The solution is to put a space on either side of the comparison operator\", \"end\": \"(>(<))/(?:([-_a-zA-Z0-9]+)((:)))?([-_a-zA-Z0-9:]*[_a-zA-Z0-9])(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"meta.scope.between-tag-pair.xml\" }, \"3\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"4\": { \"name\": \"entity.name.tag.xml\" }, \"5\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"6\": { \"name\": \"entity.name.tag.localname.xml\" }, \"7\": { \"name\": \"punctuation.definition.tag.xml\" } }, \"name\": \"meta.tag.no-content.xml\", \"patterns\": [{ \"include\": \"#xml-embedded-content\" }] }, { \"begin\": \"(</?)(?:([_a-zA-Z0-9][-_a-zA-Z0-9]*)((:)))?([_a-zA-Z0-9][-_a-zA-Z0-9:]*)(?=[^>]*?>)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(/?>)\", \"name\": \"meta.tag.xml\", \"patterns\": [{ \"include\": \"#xml-embedded-content\" }] }, { \"include\": \"#xml-entity\" }] }, \"xml-singlequotedString\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.single.xml\", \"patterns\": [{ \"include\": \"#xml-entity\" }] } }, \"scopeName\": \"source.scala\" });\nvar scala = [\n  lang\n];\n\nexport { scala as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;KAAQ;IAAE,kBAAkB;IAAyB,sBAAsB;IAAoB,qBAAqB;IAAoB,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,sBAAsB;YAAE,SAAS;QAAU;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;oBAAY,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;4BAAE;4BAAG,SAAS;wBAAqB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoB;4BAAE;4BAAG,SAAS;wBAAiC;wBAAG;4BAAE,SAAS;4BAAsH,QAAQ;wBAA6C;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkD;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAkD;4BAAE;4BAAG,SAAS;wBAA4B;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,SAAS;oBAAW,QAAQ;gBAAuD;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAAwD,YAAY;wBAAC;4BAAE,SAAS,CAAC,gDAAgD,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAsD;wBAAG;4BAAE,SAAS;4BAAY,QAAQ;wBAA6C;wBAAG;4BAAE,SAAS;4BAAc,QAAQ;wBAA6C;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAkC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAqF,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAwD,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAsO;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAwO;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAmQ;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA+O;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;gBAAqP;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAA+b;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+b;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAsP;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAe,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAwM,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAA6J;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,SAAS;YAAY,QAAQ;QAAyB;QAAG,WAAW;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAe,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAyH,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAwM,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS;wBAAkqB;wBAAG;4BAAE,SAAS;4BAAiB,QAAQ;wBAAmC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;4BAAE;4BAAG,SAAS;wBAA+U;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAiC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAe,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAyH,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAwM,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS;wBAAkqB;wBAAG;4BAAE,SAAS;4BAAiB,QAAQ;wBAAmC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;4BAAE;4BAAG,SAAS;wBAA+U;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;gBAAuX;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAc;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgO,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAuE,QAAQ;gBAA6B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA6C,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAsD,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgF,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAAsM;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAgQ;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAsG,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAiD,QAAQ;gBAAyB;aAAE;QAAC;QAAG,eAAe;YAAE,WAAW;YAAoC,SAAS;YAAwB,QAAQ;QAAoB;QAAG,iBAAiB;YAAE,WAAW;YAAgD,YAAY;gBAAC;oBAAE,WAAW;oBAAiF,SAAS;oBAAO,QAAQ;gBAAwC;gBAAG;oBAAE,WAAW;oBAA+E,SAAS;oBAAO,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAAqB;aAAE;QAAC;QAAG,eAAe;YAAE,WAAW;YAAoC,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,SAAS;gBAA4K;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;QAAsE;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiE;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAA2M;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAqC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;YAAY,QAAQ;QAA8B;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,SAAS;QAAmK;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmE,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAkG,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAA6H,QAAQ;gBAAyB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,QAAQ;gBAAgD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,SAAS;oBAAwG,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAY,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAgC,QAAQ;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAA4E,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkF;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAA0C;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAA4E,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkF;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAgC,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAA0C;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS,CAAC,gDAAgD,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAmD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAAqE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkF;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAA0C;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+J,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAAqE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkF;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAW,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS,CAAC,gDAAgD,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAmD;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAA0C;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAyB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA8C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAA4T,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAgC;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAO,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAA6B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAK,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAA4C;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2D,QAAQ;QAAgC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAA0T,OAAO;oBAAqE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuF,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAS,QAAQ;oBAAgB,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AACnl5B,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}