{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/jsonc.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON with Comments\", \"name\": \"jsonc\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json.comments\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json.comments\" } }, \"name\": \"meta.structure.array.json.comments\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json.comments\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json.comments\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.comments\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json.comments\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.comments\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json.comments\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json.comments\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json.comments\" }, \"number\": { \"match\": \"-?(?:0|[1-9]\\\\d*)(?:(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)?\", \"name\": \"constant.numeric.json.comments\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json.comments\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json.comments\" } }, \"name\": \"meta.structure.dictionary.json.comments\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json.comments\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json.comments\" } }, \"name\": \"meta.structure.dictionary.value.json.comments\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.comments\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.comments\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json.comments\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json.comments\" } }, \"name\": \"string.json.comments support.type.property-name.json.comments\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json.comments\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json.comments\" } }, \"name\": \"string.quoted.double.json.comments\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json.comments\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json.comments\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json.comments\" });\nvar jsonc = [\n  lang\n];\n\nexport { jsonc as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAsB,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAyD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAA8B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA6B,QAAQ;QAAkC;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAAiC;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;oBAAuB,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,QAAQ;oBAAiD,YAAY;wBAAC;4BAAE,WAAW;4BAAyB,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAA8D;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA8D;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAAiE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA2D;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAuB;AACr0G,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}