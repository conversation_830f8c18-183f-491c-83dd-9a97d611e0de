{"input": {"task_prompt": "Què vols que faci cubent?", "task_placeholder": "Escriu la teva tasca aquí"}, "extension": {"name": "cubent Code", "description": "Tot un equip de desenvolupadors d'IA al teu editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "Benvingut/da, {{name}}! Tens {{count}} notificacions.", "items": {"zero": "Cap element", "one": "Un element", "other": "{{count}} elements"}, "confirmation": {"reset_state": "Estàs segur que vols restablir tots els estats i emmagatzematge secret a l'extensió? Això no es pot desfer.", "delete_config_profile": "Estàs segur que vols eliminar aquest perfil de configuració?", "delete_custom_mode": "Estàs segur que vols eliminar aquest mode personalitzat?", "delete_message": "Què vols eliminar?", "just_this_message": "No<PERSON>s aquest missatge", "this_and_subsequent": "Aquest i tots els missatges posteriors"}, "errors": {"invalid_mcp_config": "Format de configuració MCP del projecte no vàlid", "invalid_mcp_settings_format": "Format JSON de configuració MCP no vàlid. Si us plau, assegura't que la teva configuració segueix el format JSON correcte.", "invalid_mcp_settings_syntax": "Format JSON de configuració MCP no vàlid. Si us plau, comprova si hi ha errors de sintaxi al teu fitxer de configuració.", "invalid_mcp_settings_validation": "Format de configuració MCP no vàlid: {{errorMessages}}", "failed_initialize_project_mcp": "Ha fallat la inicialització del servidor MCP del projecte: {{error}}", "invalid_data_uri": "Format d'URI de dades no vàlid", "checkpoint_timeout": "S'ha esgotat el temps en intentar restaurar el punt de control.", "checkpoint_failed": "Ha fallat la restauració del punt de control.", "no_workspace": "Si us plau, obre primer una carpeta de projecte", "update_support_prompt": "Ha fallat l'actualització del missatge de suport", "reset_support_prompt": "Ha fallat el restabliment del missatge de suport", "enhance_prompt": "Ha fallat la millora del missatge", "get_system_prompt": "Ha fallat l'obtenció del missatge del sistema", "search_commits": "Ha fallat la cerca de commits", "save_api_config": "Ha fallat el desament de la configuració de l'API", "create_api_config": "Ha fallat la creació de la configuració de l'API", "rename_api_config": "Ha fallat el canvi de nom de la configuració de l'API", "load_api_config": "Ha fallat la càrrega de la configuració de l'API", "delete_api_config": "Ha fallat l'eliminació de la configuració de l'API", "list_api_config": "Ha fallat l'obtenció de la llista de configuracions de l'API", "update_server_timeout": "Ha fallat l'actualització del temps d'espera del servidor", "failed_update_project_mcp": "Ha fallat l'actualització dels servidors MCP del projecte", "create_mcp_json": "Ha fallat la creació o obertura de .cubent/mcp.json: {{error}}", "hmr_not_running": "El servidor de desenvolupament local no està executant-se, l'HMR no funcionarà. Si us plau, executa 'npm run dev' abans de llançar l'extensió per habilitar l'HMR.", "retrieve_current_mode": "Error en recuperar el mode actual de l'estat.", "failed_delete_repo": "Ha fallat l'eliminació del repositori o branca associada: {{error}}", "failed_remove_directory": "Ha fallat l'eliminació del directori de tasques: {{error}}", "custom_storage_path_unusable": "La ruta d'emmagatzematge personalitzada \"{{path}}\" no és utilitzable, s'utilitzarà la ruta predeterminada", "cannot_access_path": "No es pot accedir a la ruta {{path}}: {{error}}", "settings_import_failed": "Ha fallat la importació de la configuració: {{error}}.", "mistake_limit_guidance": "Això pot indicar un error en el procés de pensament del model o la incapacitat d'utilitzar una eina correctament, que es pot mitigar amb orientació de l'usuari (p. ex. \"Prova de dividir la tasca en passos més petits\").", "violated_organization_allowlist": "Ha fallat l'execució de la tasca: el perfil actual infringeix la configuració de la teva organització", "condense_failed": "Ha fallat la condensació del context", "condense_not_enough_messages": "No hi ha prou missatges per condensar el context", "condensed_recently": "El context s'ha condensat recentment; s'omet aquest intent", "condense_handler_invalid": "El gestor de l'API per condensar el context no és vàlid", "condense_context_grew": "La mida del context ha augmentat durant la condensació; s'omet aquest intent"}, "warnings": {"no_terminal_content": "No s'ha seleccionat contingut de terminal", "missing_task_files": "Els fitxers d'aquesta tasca falten. Vols eliminar-la de la llista de tasques?"}, "info": {"no_changes": "No s'han trobat canvis.", "clipboard_copy": "Missatge del sistema copiat correctament al portapapers", "history_cleanup": "S'han netejat {{count}} tasques amb fitxers que falten de l'historial.", "mcp_server_restarting": "Reiniciant el servidor MCP {{serverName}}...", "mcp_server_connected": "Servidor MCP {{serverName}} connectat", "mcp_server_deleted": "Servidor MCP eliminat: {{serverName}}", "mcp_server_not_found": "Servidor \"{{serverName}}\" no trobat a la configuració", "custom_storage_path_set": "Ruta d'emmagatzematge personalitzada establerta: {{path}}", "default_storage_path": "S'ha reprès l'ús de la ruta d'emmagatzematge predeterminada", "settings_imported": "Configuració importada correctament."}, "answers": {"yes": "Sí", "no": "No", "cancel": "Cancel·lar", "remove": "Eliminar", "keep": "Mantenir"}, "tasks": {"canceled": "Error de tasca: Ha estat aturada i cancel·lada per l'usuari.", "deleted": "Fallada de tasca: Ha estat aturada i eliminada per l'usuari."}, "storage": {"prompt_custom_path": "Introdueix una ruta d'emmagatzematge personalitzada per a l'historial de converses o deixa-ho buit per utilitzar la ubicació predeterminada", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Introdueix una ruta completa (p. ex. D:\\RooCodeStorage o /home/<USER>/storage)", "enter_valid_path": "Introdueix una ruta vàlida"}, "settings": {"providers": {"groqApiKey": "Clau API de Groq", "getGroqApiKey": "Obté la clau API <PERSON>"}}}