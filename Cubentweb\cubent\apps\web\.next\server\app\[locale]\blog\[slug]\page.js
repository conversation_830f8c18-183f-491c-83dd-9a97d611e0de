(()=>{var e={};e.id=8414,e.ids=[8414],e.modules={1371:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),a="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;a?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(58181))},1646:(e,t,n)=>{"use strict";n.d(t,{n:()=>r});let r=n(14251).sD},2357:(e,t,n)=>{"use strict";let r,i,a;n.r(t),n.d(t,{$$RSC_SERVER_ACTION_0:()=>rk,default:()=>rw,generateMetadata:()=>rv,generateStaticParams:()=>r_});var s={};n.r(s),n.d(s,{boolean:()=>eF,booleanish:()=>eq,commaOrSpaceSeparated:()=>eJ,commaSeparated:()=>eV,number:()=>eW,overloadedBoolean:()=>eH,spaceSeparated:()=>ez});var o=n(94752),l=n(52661);n(37091);var u=n(3835),c=n(29804),h=n(71844),d=n(23055),p=n(1646),m=n(66577),g=n(23233);let f=[{id:"abap",name:"ABAP",import:()=>n.e(1594).then(n.bind(n,91594))},{id:"actionscript-3",name:"ActionScript",import:()=>n.e(9683).then(n.bind(n,49683))},{id:"ada",name:"Ada",import:()=>n.e(9824).then(n.bind(n,19824))},{id:"angular-html",name:"Angular HTML",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(768)]).then(n.bind(n,80768))},{id:"angular-ts",name:"Angular TypeScript",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6028),n.e(9755)]).then(n.bind(n,19755))},{id:"apache",name:"Apache Conf",import:()=>n.e(6866).then(n.bind(n,86866))},{id:"apex",name:"Apex",import:()=>n.e(1940).then(n.bind(n,71940))},{id:"apl",name:"APL",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(969)]).then(n.bind(n,90969))},{id:"applescript",name:"AppleScript",import:()=>n.e(5641).then(n.bind(n,65641))},{id:"ara",name:"Ara",import:()=>n.e(3706).then(n.bind(n,93706))},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(861),n.e(1767),n.e(6028),n.e(7395),n.e(2200),n.e(2204),n.e(3374),n.e(3279),n.e(7155),n.e(106),n.e(7861),n.e(4058),n.e(4131),n.e(5152),n.e(7310),n.e(6437),n.e(353),n.e(3055),n.e(2953),n.e(3914),n.e(5780),n.e(4853)]).then(n.bind(n,14853))},{id:"asm",name:"Assembly",import:()=>n.e(8317).then(n.bind(n,28317))},{id:"astro",name:"Astro",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(861),n.e(6028),n.e(7395),n.e(7350),n.e(6181),n.e(2725)]).then(n.bind(n,12725))},{id:"awk",name:"AWK",import:()=>n.e(8731).then(n.bind(n,38731))},{id:"ballerina",name:"Ballerina",import:()=>n.e(1512).then(n.bind(n,51512))},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>n.e(1449).then(n.bind(n,91449))},{id:"beancount",name:"Beancount",import:()=>n.e(8891).then(n.bind(n,88891))},{id:"berry",name:"Berry",aliases:["be"],import:()=>n.e(448).then(n.bind(n,70448))},{id:"bibtex",name:"BibTeX",import:()=>n.e(5562).then(n.bind(n,45562))},{id:"bicep",name:"Bicep",import:()=>n.e(4569).then(n.bind(n,34569))},{id:"blade",name:"Blade",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(1110)]).then(n.bind(n,41110))},{id:"c",name:"C",import:()=>n.e(4187).then(n.bind(n,14187))},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>n.e(7817).then(n.bind(n,7817))},{id:"clarity",name:"Clarity",import:()=>n.e(8432).then(n.bind(n,28432))},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>n.e(3760).then(n.bind(n,63760))},{id:"cmake",name:"CMake",import:()=>n.e(357).then(n.bind(n,357))},{id:"cobol",name:"COBOL",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(133)]).then(n.bind(n,40133))},{id:"codeowners",name:"CODEOWNERS",import:()=>n.e(4403).then(n.bind(n,4403))},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>n.e(8736).then(n.bind(n,8736))},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>Promise.all([n.e(3069),n.e(2204)]).then(n.bind(n,42204))},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>n.e(2312).then(n.bind(n,12312))},{id:"coq",name:"Coq",import:()=>n.e(1063).then(n.bind(n,21063))},{id:"cpp",name:"C++",aliases:["c++"],import:()=>Promise.all([n.e(1516),n.e(4187),n.e(3279)]).then(n.bind(n,73279))},{id:"crystal",name:"Crystal",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(1516),n.e(4187),n.e(1767),n.e(5142)]).then(n.bind(n,45142))},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>n.e(7861).then(n.bind(n,37861))},{id:"css",name:"CSS",import:()=>n.e(8013).then(n.bind(n,98013))},{id:"csv",name:"CSV",import:()=>n.e(5312).then(n.bind(n,65312))},{id:"cue",name:"CUE",import:()=>n.e(6745).then(n.bind(n,56745))},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>n.e(5301).then(n.bind(n,5301))},{id:"d",name:"D",import:()=>n.e(7984).then(n.bind(n,47984))},{id:"dart",name:"Dart",import:()=>n.e(1623).then(n.bind(n,41623))},{id:"dax",name:"DAX",import:()=>n.e(9931).then(n.bind(n,79931))},{id:"desktop",name:"Desktop",import:()=>n.e(1392).then(n.bind(n,1392))},{id:"diff",name:"Diff",import:()=>n.e(349).then(n.bind(n,90349))},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>n.e(3588).then(n.bind(n,3588))},{id:"dotenv",name:"dotEnv",import:()=>n.e(3234).then(n.bind(n,83234))},{id:"dream-maker",name:"Dream Maker",import:()=>n.e(1533).then(n.bind(n,93914))},{id:"edge",name:"Edge",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(851)]).then(n.bind(n,10851))},{id:"elixir",name:"Elixir",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(9093)]).then(n.bind(n,9093))},{id:"elm",name:"Elm",import:()=>Promise.all([n.e(4187),n.e(9056)]).then(n.bind(n,99056))},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>n.e(7210).then(n.bind(n,57210))},{id:"erb",name:"ERB",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(1767),n.e(2200),n.e(8825)]).then(n.bind(n,38825))},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>n.e(2953).then(n.bind(n,55334))},{id:"fennel",name:"Fennel",import:()=>n.e(1992).then(n.bind(n,1992))},{id:"fish",name:"Fish",import:()=>n.e(2062).then(n.bind(n,52062))},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>n.e(9238).then(n.bind(n,9238))},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>Promise.all([n.e(6704),n.e(3682)]).then(n.bind(n,13682))},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>n.e(6704).then(n.bind(n,46704))},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>Promise.all([n.e(8383),n.e(1650)]).then(n.bind(n,71650))},{id:"gdresource",name:"GDResource",import:()=>n.e(8453).then(n.bind(n,28453))},{id:"gdscript",name:"GDScript",import:()=>n.e(5932).then(n.bind(n,65932))},{id:"gdshader",name:"GDShader",import:()=>n.e(8030).then(n.bind(n,58030))},{id:"genie",name:"Genie",import:()=>n.e(2456).then(n.bind(n,62456))},{id:"gherkin",name:"Gherkin",import:()=>n.e(2246).then(n.bind(n,62246))},{id:"git-commit",name:"Git Commit Message",import:()=>n.e(6782).then(n.bind(n,56782))},{id:"git-rebase",name:"Git Rebase Message",import:()=>Promise.all([n.e(1767),n.e(4129)]).then(n.bind(n,74129))},{id:"gleam",name:"Gleam",import:()=>n.e(9284).then(n.bind(n,89284))},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(9419)]).then(n.bind(n,79419))},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(5237)]).then(n.bind(n,5237))},{id:"glsl",name:"GLSL",import:()=>Promise.all([n.e(4187),n.e(6084)]).then(n.bind(n,76084))},{id:"gnuplot",name:"Gnuplot",import:()=>n.e(9273).then(n.bind(n,9273))},{id:"go",name:"Go",import:()=>n.e(4058).then(n.bind(n,74058))},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>Promise.all([n.e(3069),n.e(861),n.e(7155),n.e(6181),n.e(4719)]).then(n.bind(n,34719))},{id:"groovy",name:"Groovy",import:()=>n.e(3914).then(n.bind(n,63914))},{id:"hack",name:"Hack",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(1516),n.e(5163)]).then(n.bind(n,65163))},{id:"haml",name:"Ruby Haml",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(1767),n.e(8383),n.e(2200),n.e(2204),n.e(510)]).then(n.bind(n,60510))},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6636)]).then(n.bind(n,86636))},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>n.e(5152).then(n.bind(n,65152))},{id:"haxe",name:"Haxe",import:()=>n.e(4828).then(n.bind(n,64828))},{id:"hcl",name:"HashiCorp HCL",import:()=>n.e(8309).then(n.bind(n,28309))},{id:"hjson",name:"Hjson",import:()=>n.e(3746).then(n.bind(n,73746))},{id:"hlsl",name:"HLSL",import:()=>n.e(8797).then(n.bind(n,28797))},{id:"html",name:"HTML",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055)]).then(n.bind(n,14055))},{id:"html-derivative",name:"HTML (Derivative)",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(7011)]).then(n.bind(n,87011))},{id:"http",name:"HTTP",import:()=>Promise.all([n.e(3069),n.e(6736),n.e(861),n.e(1767),n.e(7155),n.e(6181),n.e(8584)]).then(n.bind(n,38584))},{id:"hxml",name:"HXML",import:()=>n.e(2587).then(n.bind(n,52587))},{id:"hy",name:"Hy",import:()=>n.e(6537).then(n.bind(n,36537))},{id:"imba",name:"Imba",import:()=>Promise.all([n.e(861),n.e(4285)]).then(n.bind(n,94285))},{id:"ini",name:"INI",aliases:["properties"],import:()=>n.e(5846).then(n.bind(n,25846))},{id:"java",name:"Java",import:()=>n.e(6736).then(n.bind(n,96736))},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>n.e(3069).then(n.bind(n,63069))},{id:"jinja",name:"Jinja",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(8277)]).then(n.bind(n,38277))},{id:"jison",name:"Jison",import:()=>Promise.all([n.e(3069),n.e(7581)]).then(n.bind(n,37581))},{id:"json",name:"JSON",import:()=>n.e(3481).then(n.bind(n,85862))},{id:"json5",name:"JSON5",import:()=>n.e(4787).then(n.bind(n,64787))},{id:"jsonc",name:"JSON with Comments",import:()=>n.e(9809).then(n.bind(n,29809))},{id:"jsonl",name:"JSON Lines",import:()=>n.e(6570).then(n.bind(n,46570))},{id:"jsonnet",name:"Jsonnet",import:()=>n.e(5327).then(n.bind(n,55327))},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>n.e(4581).then(n.bind(n,94581))},{id:"jsx",name:"JSX",import:()=>n.e(7155).then(n.bind(n,27155))},{id:"julia",name:"Julia",aliases:["jl"],import:()=>Promise.all([n.e(3069),n.e(1516),n.e(4187),n.e(3374),n.e(3279),n.e(106),n.e(4131)]).then(n.bind(n,44131))},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>n.e(5003).then(n.bind(n,55003))},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>n.e(9860).then(n.bind(n,59860))},{id:"latex",name:"LaTeX",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(861),n.e(1767),n.e(2200),n.e(3374),n.e(3279),n.e(106),n.e(4131),n.e(5152),n.e(7310),n.e(2976)]).then(n.bind(n,62976))},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>n.e(9436).then(n.bind(n,19436))},{id:"less",name:"Less",import:()=>n.e(7395).then(n.bind(n,7395))},{id:"liquid",name:"Liquid",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6504)]).then(n.bind(n,6504))},{id:"log",name:"Log file",import:()=>n.e(3778).then(n.bind(n,13778))},{id:"logo",name:"Logo",import:()=>n.e(2697).then(n.bind(n,12697))},{id:"lua",name:"Lua",import:()=>Promise.all([n.e(4187),n.e(8478)]).then(n.bind(n,28478))},{id:"luau",name:"Luau",import:()=>n.e(9947).then(n.bind(n,49947))},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>n.e(8050).then(n.bind(n,58050))},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>n.e(8383).then(n.bind(n,38383))},{id:"marko",name:"Marko",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(6028),n.e(7395),n.e(1910)]).then(n.bind(n,51910))},{id:"matlab",name:"MATLAB",import:()=>n.e(5423).then(n.bind(n,35423))},{id:"mdc",name:"MDC",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(8383),n.e(8690)]).then(n.bind(n,28690))},{id:"mdx",name:"MDX",import:()=>n.e(8781).then(n.bind(n,8781))},{id:"mermaid",name:"Mermaid",import:()=>n.e(9465).then(n.bind(n,19465))},{id:"mojo",name:"Mojo",import:()=>n.e(4149).then(n.bind(n,14149))},{id:"move",name:"Move",import:()=>n.e(7239).then(n.bind(n,47239))},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>n.e(9656).then(n.bind(n,9656))},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>n.e(387).then(n.bind(n,387))},{id:"nginx",name:"Nginx",import:()=>Promise.all([n.e(4187),n.e(6420)]).then(n.bind(n,26420))},{id:"nim",name:"Nim",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(4187),n.e(8383),n.e(1020)]).then(n.bind(n,71020))},{id:"nix",name:"Nix",import:()=>n.e(3793).then(n.bind(n,93793))},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>n.e(3871).then(n.bind(n,33871))},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>n.e(6437).then(n.bind(n,56437))},{id:"objective-cpp",name:"Objective-C++",import:()=>n.e(7469).then(n.bind(n,27469))},{id:"ocaml",name:"OCaml",import:()=>n.e(5780).then(n.bind(n,93399))},{id:"pascal",name:"Pascal",import:()=>n.e(3532).then(n.bind(n,53532))},{id:"perl",name:"Perl",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(3055),n.e(1127)]).then(n.bind(n,43055))},{id:"php",name:"PHP",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(8044),n.e(3508)]).then(n.bind(n,18044))},{id:"plsql",name:"PL/SQL",import:()=>n.e(1928).then(n.bind(n,31928))},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>n.e(7955).then(n.bind(n,77955))},{id:"postcss",name:"PostCSS",import:()=>n.e(763).then(n.bind(n,763))},{id:"powerquery",name:"PowerQuery",import:()=>n.e(6085).then(n.bind(n,26085))},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>n.e(4591).then(n.bind(n,94591))},{id:"prisma",name:"Prisma",import:()=>n.e(2444).then(n.bind(n,12444))},{id:"prolog",name:"Prolog",import:()=>n.e(7433).then(n.bind(n,97433))},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>n.e(5154).then(n.bind(n,95154))},{id:"pug",name:"Pug",aliases:["jade"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6028),n.e(2204),n.e(7350),n.e(2632)]).then(n.bind(n,2632))},{id:"puppet",name:"Puppet",import:()=>n.e(4610).then(n.bind(n,14610))},{id:"purescript",name:"PureScript",import:()=>n.e(783).then(n.bind(n,90783))},{id:"python",name:"Python",aliases:["py"],import:()=>n.e(3374).then(n.bind(n,33374))},{id:"qml",name:"QML",import:()=>Promise.all([n.e(3069),n.e(1677)]).then(n.bind(n,84058))},{id:"qmldir",name:"QML Directory",import:()=>n.e(9471).then(n.bind(n,59471))},{id:"qss",name:"Qt Style Sheets",import:()=>n.e(7263).then(n.bind(n,67263))},{id:"r",name:"R",import:()=>n.e(106).then(n.bind(n,20106))},{id:"racket",name:"Racket",import:()=>n.e(7922).then(n.bind(n,77922))},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>n.e(6831).then(n.bind(n,26831))},{id:"razor",name:"ASP.NET Razor",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(7861),n.e(1080)]).then(n.bind(n,21080))},{id:"reg",name:"Windows Registry Script",import:()=>n.e(6578).then(n.bind(n,56578))},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>n.e(8831).then(n.bind(n,48831))},{id:"rel",name:"Rel",import:()=>n.e(877).then(n.bind(n,60877))},{id:"riscv",name:"RISC-V",import:()=>n.e(6265).then(n.bind(n,36265))},{id:"rst",name:"reStructuredText",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(1767),n.e(2200),n.e(3374),n.e(3279),n.e(1871)]).then(n.bind(n,1871))},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(1767),n.e(2200)]).then(n.bind(n,92200))},{id:"rust",name:"Rust",aliases:["rs"],import:()=>n.e(3034).then(n.bind(n,33034))},{id:"sas",name:"SAS",import:()=>Promise.all([n.e(1516),n.e(9575)]).then(n.bind(n,19575))},{id:"sass",name:"Sass",import:()=>n.e(1726).then(n.bind(n,91726))},{id:"scala",name:"Scala",import:()=>n.e(7310).then(n.bind(n,97310))},{id:"scheme",name:"Scheme",import:()=>n.e(6949).then(n.bind(n,66949))},{id:"scss",name:"SCSS",import:()=>Promise.all([n.e(8013),n.e(6028)]).then(n.bind(n,6028))},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>n.e(872).then(n.bind(n,10872))},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>n.e(1767).then(n.bind(n,31767))},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>Promise.all([n.e(1767),n.e(602)]).then(n.bind(n,70602))},{id:"smalltalk",name:"Smalltalk",import:()=>n.e(2883).then(n.bind(n,72883))},{id:"solidity",name:"Solidity",import:()=>n.e(1619).then(n.bind(n,1619))},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(9439)]).then(n.bind(n,89439))},{id:"sparql",name:"SPARQL",import:()=>n.e(9403).then(n.bind(n,9403))},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>n.e(7269).then(n.bind(n,67269))},{id:"sql",name:"SQL",import:()=>n.e(1516).then(n.bind(n,21516))},{id:"ssh-config",name:"SSH Config",import:()=>n.e(8401).then(n.bind(n,68401))},{id:"stata",name:"Stata",import:()=>Promise.all([n.e(1516),n.e(9171)]).then(n.bind(n,79171))},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>n.e(7350).then(n.bind(n,47350))},{id:"svelte",name:"Svelte",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(6028),n.e(8383),n.e(7395),n.e(2204),n.e(7350),n.e(2632),n.e(1725)]).then(n.bind(n,91725))},{id:"swift",name:"Swift",import:()=>n.e(353).then(n.bind(n,12734))},{id:"system-verilog",name:"SystemVerilog",import:()=>n.e(5286).then(n.bind(n,25286))},{id:"systemd",name:"Systemd Units",import:()=>n.e(8865).then(n.bind(n,8865))},{id:"tasl",name:"Tasl",import:()=>n.e(2164).then(n.bind(n,52164))},{id:"tcl",name:"Tcl",import:()=>n.e(4113).then(n.bind(n,84113))},{id:"templ",name:"Templ",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4058),n.e(1318)]).then(n.bind(n,81318))},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>n.e(8088).then(n.bind(n,48088))},{id:"tex",name:"TeX",import:()=>Promise.all([n.e(106),n.e(9119)]).then(n.bind(n,69119))},{id:"toml",name:"TOML",import:()=>n.e(9684).then(n.bind(n,69684))},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(861),n.e(6977)]).then(n.bind(n,86977))},{id:"tsv",name:"TSV",import:()=>n.e(9223).then(n.bind(n,19223))},{id:"tsx",name:"TSX",import:()=>n.e(6181).then(n.bind(n,86181))},{id:"turtle",name:"Turtle",import:()=>n.e(6722).then(n.bind(n,76722))},{id:"twig",name:"Twig",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(1767),n.e(6028),n.e(2200),n.e(3374),n.e(8044),n.e(4487)]).then(n.bind(n,54487))},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>n.e(861).then(n.bind(n,10861))},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>n.e(5465).then(n.bind(n,75465))},{id:"typst",name:"Typst",aliases:["typ"],import:()=>n.e(5862).then(n.bind(n,35862))},{id:"v",name:"V",import:()=>n.e(2358).then(n.bind(n,32358))},{id:"vala",name:"Vala",import:()=>n.e(8234).then(n.bind(n,88234))},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>n.e(4184).then(n.bind(n,34184))},{id:"verilog",name:"Verilog",import:()=>n.e(3734).then(n.bind(n,43734))},{id:"vhdl",name:"VHDL",import:()=>n.e(3078).then(n.bind(n,93078))},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>n.e(5324).then(n.bind(n,35324))},{id:"vue",name:"Vue",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(6028),n.e(8383),n.e(7395),n.e(2204),n.e(7350),n.e(7155),n.e(6181),n.e(2632),n.e(322)]).then(n.bind(n,80322))},{id:"vue-html",name:"Vue HTML",import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(861),n.e(6028),n.e(8383),n.e(7395),n.e(2204),n.e(7350),n.e(7155),n.e(6181),n.e(2632),n.e(322),n.e(5914)]).then(n.bind(n,35914))},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>n.e(6924).then(n.bind(n,6924))},{id:"wasm",name:"WebAssembly",import:()=>n.e(2368).then(n.bind(n,82368))},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>n.e(5226).then(n.bind(n,5226))},{id:"wgsl",name:"WGSL",import:()=>n.e(7775).then(n.bind(n,87775))},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>Promise.all([n.e(3069),n.e(8013),n.e(4055),n.e(6736),n.e(1516),n.e(4187),n.e(861),n.e(1767),n.e(6028),n.e(8383),n.e(7395),n.e(2200),n.e(2204),n.e(3374),n.e(7350),n.e(3279),n.e(106),n.e(2632),n.e(7861),n.e(4058),n.e(4131),n.e(5152),n.e(7310),n.e(8044),n.e(6437),n.e(353),n.e(3055),n.e(2953),n.e(3914),n.e(2976),n.e(8393)]).then(n.bind(n,18393))},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>n.e(4572).then(n.bind(n,24572))},{id:"xml",name:"XML",import:()=>Promise.all([n.e(6736),n.e(6365)]).then(n.bind(n,31127))},{id:"xsl",name:"XSL",import:()=>Promise.all([n.e(6736),n.e(4069)]).then(n.bind(n,14069))},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>n.e(5253).then(n.bind(n,25253))},{id:"zenscript",name:"ZenScript",import:()=>n.e(966).then(n.bind(n,70966))},{id:"zig",name:"Zig",import:()=>n.e(6966).then(n.bind(n,36966))}],b=Object.fromEntries(f.map(e=>[e.id,e.import])),y=Object.fromEntries(f.flatMap(e=>e.aliases?.map(t=>[t,e.import])||[])),v={...b,...y},_=async e=>n.e(4390).then(n.bind(n,84390)).then(t=>t.default(e)),k=Object.fromEntries([{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>n.e(8195).then(n.bind(n,28195))},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>n.e(444).then(n.bind(n,444))},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>n.e(9967).then(n.bind(n,39967))},{id:"catppuccin-frappe",displayName:"Catppuccin Frapp\xe9",type:"dark",import:()=>n.e(9076).then(n.bind(n,29076))},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>n.e(3376).then(n.bind(n,93376))},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>n.e(1215).then(n.bind(n,21215))},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>n.e(8576).then(n.bind(n,8576))},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>n.e(7628).then(n.bind(n,47628))},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>n.e(8255).then(n.bind(n,58255))},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>n.e(968).then(n.bind(n,50968))},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>n.e(9837).then(n.bind(n,69837))},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>n.e(1507).then(n.bind(n,1507))},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>n.e(3903).then(n.bind(n,93903))},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>n.e(3003).then(n.bind(n,33003))},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>n.e(7996).then(n.bind(n,67996))},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>n.e(7615).then(n.bind(n,27615))},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>n.e(4445).then(n.bind(n,4445))},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>n.e(7013).then(n.bind(n,47013))},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>n.e(5509).then(n.bind(n,25509))},{id:"houston",displayName:"Houston",type:"dark",import:()=>n.e(5483).then(n.bind(n,45483))},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>n.e(2231).then(n.bind(n,52231))},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>n.e(6298).then(n.bind(n,26298))},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>n.e(5712).then(n.bind(n,15712))},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>n.e(7900).then(n.bind(n,47900))},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>n.e(5958).then(n.bind(n,65958))},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>n.e(2797).then(n.bind(n,42797))},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>n.e(4237).then(n.bind(n,84237))},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>n.e(8078).then(n.bind(n,68078))},{id:"min-light",displayName:"Min Light",type:"light",import:()=>n.e(5334).then(n.bind(n,15334))},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>n.e(3135).then(n.bind(n,43135))},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>n.e(6398).then(n.bind(n,76398))},{id:"nord",displayName:"Nord",type:"dark",import:()=>n.e(8300).then(n.bind(n,78300))},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>n.e(5060).then(n.bind(n,65060))},{id:"one-light",displayName:"One Light",type:"light",import:()=>n.e(1762).then(n.bind(n,31762))},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>n.e(785).then(n.bind(n,70785))},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>n.e(6429).then(n.bind(n,16429))},{id:"red",displayName:"Red",type:"dark",import:()=>n.e(8496).then(n.bind(n,58496))},{id:"rose-pine",displayName:"Ros\xe9 Pine",type:"dark",import:()=>n.e(3173).then(n.bind(n,73173))},{id:"rose-pine-dawn",displayName:"Ros\xe9 Pine Dawn",type:"light",import:()=>n.e(36).then(n.bind(n,40036))},{id:"rose-pine-moon",displayName:"Ros\xe9 Pine Moon",type:"dark",import:()=>n.e(2681).then(n.bind(n,2681))},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>n.e(9430).then(n.bind(n,89430))},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>n.e(8733).then(n.bind(n,88733))},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>n.e(717).then(n.bind(n,20717))},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>n.e(6757).then(n.bind(n,26757))},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>n.e(475).then(n.bind(n,90475))},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>n.e(1901).then(n.bind(n,51901))},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>n.e(1578).then(n.bind(n,31578))},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>n.e(292).then(n.bind(n,60292))},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>n.e(6054).then(n.bind(n,96054))},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>n.e(8081).then(n.bind(n,28081))},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>n.e(6655).then(n.bind(n,6655))}].map(e=>[e.id,e.import]));class w extends Error{constructor(e){super(e),this.name="ShikiError"}}function x(e,...t){return t.forEach(t=>{for(let n in t)e[n]=t[n]}),e}var S=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,C=class{static hasCaptures(e){return null!==e&&(S.lastIndex=0,S.test(e))}static replaceCaptures(e,t,n){return e.replace(S,(e,r,i,a)=>{let s=n[parseInt(r||i,10)];if(!s)return e;{let e=t.substring(s.start,s.end);for(;"."===e[0];)e=e.substring(1);switch(a){case"downcase":return e.toLowerCase();case"upcase":return e.toUpperCase();default:return e}}})}};function P(e,t){return e<t?-1:+(e>t)}function N(e,t){if(null===e&&null===t)return 0;if(!e)return -1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let r=0;r<n;r++){var i,a;let n=(i=e[r],i<(a=t[r])?-1:+(i>a));if(0!==n)return n}return 0}return n-r}function A(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function L(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var T=class{constructor(e){this.fn=e,this.cache=new Map}get(e){if(this.cache.has(e))return this.cache.get(e);let t=this.fn(e);return this.cache.set(e,t),t}},E=class{constructor(e,t,n){this._colorMap=e,this._defaults=t,this._root=n,this._cachedMatchRoot=new T(e=>this._root.match(e))}static createFromRawTheme(e,t){return this.createFromParsedTheme(function(e){if(!e||!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let e=0,i=t.length;e<i;e++){let i,a=t[e];if(!a.settings)continue;if("string"==typeof a.scope){let e=a.scope;i=(e=(e=e.replace(/^[,]+/,"")).replace(/[,]+$/,"")).split(",")}else i=Array.isArray(a.scope)?a.scope:[""];let s=-1;if("string"==typeof a.settings.fontStyle){s=0;let e=a.settings.fontStyle.split(" ");for(let t=0,n=e.length;t<n;t++)switch(e[t]){case"italic":s|=1;break;case"bold":s|=2;break;case"underline":s|=4;break;case"strikethrough":s|=8}}let o=null;"string"==typeof a.settings.foreground&&A(a.settings.foreground)&&(o=a.settings.foreground);let l=null;"string"==typeof a.settings.background&&A(a.settings.background)&&(l=a.settings.background);for(let t=0,a=i.length;t<a;t++){let a=i[t].trim().split(" "),u=a[a.length-1],c=null;a.length>1&&(c=a.slice(0,a.length-1)).reverse(),n[r++]=new I(u,c,e,s,o,l)}}return n}(e),t)}static createFromParsedTheme(e,t){return function(e,t){e.sort((e,t)=>{var n,r;let i=(n=e.scope,n<(r=t.scope)?-1:+(n>r));return 0!==i||0!==(i=N(e.parentScopes,t.parentScopes))?i:e.index-t.index});let n=0,r="#000000",i="#ffffff";for(;e.length>=1&&""===e[0].scope;){let t=e.shift();-1!==t.fontStyle&&(n=t.fontStyle),null!==t.foreground&&(r=t.foreground),null!==t.background&&(i=t.background)}let a=new M(t),s=new O(n,a.getId(r),a.getId(i)),o=new G(new B(0,null,-1,0,0),[]);for(let t=0,n=e.length;t<n;t++){let n=e[t];o.insert(0,n.scope,n.parentScopes,n.fontStyle,a.getId(n.foreground),a.getId(n.background))}return new E(a,s,o)}(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(null===e)return this._defaults;let t=e.scopeName,n=this._cachedMatchRoot.get(t).find(t=>(function(e,t){if(0===t.length)return!0;for(let i=0;i<t.length;i++){var n,r;let a=t[i],s=!1;if(">"===a){if(i===t.length-1)return!1;a=t[++i],s=!0}for(;e&&(n=e.scopeName,!((r=a)===n||n.startsWith(r)&&"."===n[r.length]));){if(s)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0})(e.parent,t.parentScopes));return n?new O(n.fontStyle,n.foreground,n.background):null}},R=class e{constructor(e,t){this.parent=e,this.scopeName=t}static push(t,n){for(let r of n)t=new e(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new e(n,t[r]);return n}push(t){return new e(this,t)}getSegments(){let e=this,t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e||null!==this.parent&&this.parent.extends(e)}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}},O=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}},I=class{constructor(e,t,n,r,i,a){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=i,this.background=a}},j=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(j||{}),M=class{constructor(e){if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(null===e)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},D=Object.freeze([]),B=class e{constructor(e,t,n,r,i){this.scopeDepth=e,this.parentScopes=t||D,this.fontStyle=n,this.foreground=r,this.background=i}clone(){return new e(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,r){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,-1!==t&&(this.fontStyle=t),0!==n&&(this.foreground=n),0!==r&&(this.background=r)}},G=class e{constructor(e,t=[],n={}){this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,r=0;for(;">"===e.parentScopes[n]&&n++,">"===t.parentScopes[r]&&r++,!(n>=e.parentScopes.length)&&!(r>=t.parentScopes.length);){let i=t.parentScopes[r].length-e.parentScopes[n].length;if(0!==i)return i;n++,r++}return t.parentScopes.length-e.parentScopes.length}match(t){if(""!==t){let e,n,r=t.indexOf(".");if(-1===r?(e=t,n=""):(e=t.substring(0,r),n=t.substring(r+1)),this._children.hasOwnProperty(e))return this._children[e].match(n)}let n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(e._cmpBySpecificity),n}insert(t,n,r,i,a,s){let o,l,u;if(""===n)return void this._doInsertHere(t,r,i,a,s);let c=n.indexOf(".");-1===c?(o=n,l=""):(o=n.substring(0,c),l=n.substring(c+1)),this._children.hasOwnProperty(o)?u=this._children[o]:(u=new e(this._mainRule.clone(),B.cloneArr(this._rulesWithParentScopes)),this._children[o]=u),u.insert(t+1,l,r,i,a,s)}_doInsertHere(e,t,n,r,i){if(null===t)return void this._mainRule.acceptOverwrite(e,n,r,i);for(let a=0,s=this._rulesWithParentScopes.length;a<s;a++){let s=this._rulesWithParentScopes[a];if(0===N(s.parentScopes,t))return void s.acceptOverwrite(e,n,r,i)}-1===n&&(n=this._mainRule.fontStyle),0===r&&(r=this._mainRule.foreground),0===i&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new B(e,t,n,r,i))}},U=class e{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(t){let n=e.getLanguageId(t),r=e.getTokenType(t),i=e.getFontStyle(t);console.log({languageId:n,tokenType:r,fontStyle:i,foreground:e.getForeground(t),background:e.getBackground(t)})}static getLanguageId(e){return(255&e)>>>0}static getTokenType(e){return(768&e)>>>8}static containsBalancedBrackets(e){return(1024&e)!=0}static getFontStyle(e){return(30720&e)>>>11}static getForeground(e){return(0xff8000&e)>>>15}static getBackground(e){return(0xff000000&e)>>>24}static set(t,n,r,i,a,s,o){let l=e.getLanguageId(t),u=e.getTokenType(t),c=+!!e.containsBalancedBrackets(t),h=e.getFontStyle(t),d=e.getForeground(t),p=e.getBackground(t);return 0!==n&&(l=n),8!==r&&(u=r),null!==i&&(c=+!!i),-1!==a&&(h=a),0!==s&&(d=s),0!==o&&(p=o),(0|l|u<<8|c<<10|h<<11|d<<15|p<<24)>>>0}};function $(e,t){var n;let r,i,a=[],s=(n=e,i=(r=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g).exec(n),{next:()=>{if(!i)return null;let e=i[0];return i=r.exec(n),e}}),o=s.next();for(;null!==o;){let e=0;if(2===o.length&&":"===o.charAt(1)){switch(o.charAt(0)){case"R":e=1;break;case"L":e=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=s.next()}let t=u();if(a.push({matcher:t,priority:e}),","!==o)break;o=s.next()}return a;function l(){if("-"===o){o=s.next();let e=l();return t=>!!e&&!e(t)}if("("===o){o=s.next();let e=function(){let e=[],t=u();for(;t&&(e.push(t),"|"===o||","===o);){do o=s.next();while("|"===o||","===o);t=u()}return t=>e.some(e=>e(t))}();return")"===o&&(o=s.next()),e}if(F(o)){let e=[];do e.push(o),o=s.next();while(F(o));return n=>t(e,n)}return null}function u(){let e=[],t=l();for(;t;)e.push(t),t=l();return t=>e.every(e=>e(t))}}function F(e){return!!e&&!!e.match(/[\w\.:]+/)}var q=(e=>(e[e.None=0]="None",e[e.NotBeginString=1]="NotBeginString",e[e.NotEndString=2]="NotEndString",e[e.NotBeginPosition=4]="NotBeginPosition",e[e.DebugCall=8]="DebugCall",e))(q||{});function H(e){"function"==typeof e.dispose&&e.dispose()}var W=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},z=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},V=class{constructor(){this._references=[],this._seenReferenceKeys=new Set,this.visitedRule=new Set}get references(){return this._references}add(e){let t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},J=class{constructor(e,t){this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests=new Set,this.seenPartialScopeRequests=new Set,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new W(this.initialScopeName)]}processQueue(){let e=this.Q;this.Q=[];let t=new V;for(let n of e)!function(e,t,n,r){let i=n.lookup(e.scopeName);if(!i){if(e.scopeName===t)throw Error(`No grammar provided for <${t}>`);return}let a=n.lookup(t);e instanceof W?X({baseGrammar:a,selfGrammar:i},r):K(e.ruleName,{baseGrammar:a,selfGrammar:i,repository:i.repository},r);let s=n.injections(e.scopeName);if(s)for(let e of s)r.add(new W(e))}(n,this.initialScopeName,this.repo,t);for(let e of t.references)if(e instanceof W){if(this.seenFullScopeRequests.has(e.scopeName))continue;this.seenFullScopeRequests.add(e.scopeName),this.Q.push(e)}else{if(this.seenFullScopeRequests.has(e.scopeName)||this.seenPartialScopeRequests.has(e.toKey()))continue;this.seenPartialScopeRequests.add(e.toKey()),this.Q.push(e)}}};function K(e,t,n){t.repository&&t.repository[e]&&Y([t.repository[e]],t,n)}function X(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&Y(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&Y(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function Y(e,t,n){for(let r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);let e=r.repository?x({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&Y(r.patterns,{...t,repository:e},n);let i=r.include;if(!i)continue;let a=er(i);switch(a.kind){case 0:X({...t,selfGrammar:t.baseGrammar},n);break;case 1:X(t,n);break;case 2:K(a.ruleName,{...t,repository:e},n);break;case 3:case 4:let s=a.scopeName===t.selfGrammar.scopeName?t.selfGrammar:a.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){let r={baseGrammar:t.baseGrammar,selfGrammar:s,repository:e};4===a.kind?K(a.ruleName,r,n):X(r,n)}else 4===a.kind?n.add(new z(a.scopeName,a.ruleName)):n.add(new W(a.scopeName))}}}var Q=class{constructor(){this.kind=0}},Z=class{constructor(){this.kind=1}},ee=class{constructor(e){this.ruleName=e,this.kind=2}},et=class{constructor(e){this.scopeName=e,this.kind=3}},en=class{constructor(e,t){this.scopeName=e,this.ruleName=t,this.kind=4}};function er(e){if("$base"===e)return new Q;if("$self"===e)return new Z;let t=e.indexOf("#");return -1===t?new et(e):0===t?new ee(e.substring(1)):new en(e.substring(0,t),e.substring(t+1))}var ei=/\\(\d+)/,ea=/\\(\d+)/g;Symbol("RuleId");var es=class{constructor(e,t,n,r){this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=C.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=C.hasCaptures(this._contentName)}get debugName(){let e=this.$location?`${function e(t){let n=~t.lastIndexOf("/")||~t.lastIndexOf("\\");return 0===n?t:~n==t.length-1?e(t.substring(0,t.length-1)):t.substr(~n+1)}(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return this._nameIsCapturing&&null!==this._name&&null!==e&&null!==t?C.replaceCaptures(this._name,e,t):this._name}getContentName(e,t){return this._contentNameIsCapturing&&null!==this._contentName?C.replaceCaptures(this._contentName,e,t):this._contentName}},eo=class extends es{constructor(e,t,n,r,i){super(e,t,n,r),this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(e,t){throw Error("Not supported!")}compile(e,t){throw Error("Not supported!")}compileAG(e,t,n,r){throw Error("Not supported!")}},el=class extends es{constructor(e,t,n,r,i){super(e,t,n,null),this._match=new ep(r,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new em,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},eu=class extends es{constructor(e,t,n,r,i){super(e,t,n,r),this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(let n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new em,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},ec=class extends es{constructor(e,t,n,r,i,a,s,o,l,u){super(e,t,n,r),this._begin=new ep(i,this.id),this.beginCaptures=a,this._end=new ep(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=o,this.applyEndPatternLast=l||!1,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,r)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){for(let t of(this._cachedCompiledPatterns=new em,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},eh=class extends es{constructor(e,t,n,r,i,a,s,o,l){super(e,t,n,r),this._begin=new ep(i,this.id),this.beginCaptures=a,this.whileCaptures=o,this._while=new ep(s,-2),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=l.patterns,this.hasMissingPatterns=l.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns)for(let t of(this._cachedCompiledPatterns=new em,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,r){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,r)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new em,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},ed=class e{static createCaptureRule(e,t,n,r,i){return e.registerRule(e=>new eo(t,e,n,r,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(i=>{if(t.id=i,t.match)return new el(t.$vscodeTextmateLocation,t.id,t.name,t.match,e._compileCaptures(t.captures,n,r));if(void 0===t.begin){t.repository&&(r=x({},r,t.repository));let i=t.patterns;return void 0===i&&t.include&&(i=[{include:t.include}]),new eu(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,e._compilePatterns(i,n,r))}return t.while?new eh(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,e._compileCaptures(t.whileCaptures||t.captures,n,r),e._compilePatterns(t.patterns,n,r)):new ec(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,e._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,e._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let i=[];if(t){let a=0;for(let e in t){if("$vscodeTextmateLocation"===e)continue;let t=parseInt(e,10);t>a&&(a=t)}for(let e=0;e<=a;e++)i[e]=null;for(let a in t){if("$vscodeTextmateLocation"===a)continue;let s=parseInt(a,10),o=0;t[a].patterns&&(o=e.getCompiledRuleId(t[a],n,r)),i[s]=e.createCaptureRule(n,t[a].$vscodeTextmateLocation,t[a].name,t[a].contentName,o)}}return i}static _compilePatterns(t,n,r){let i=[];if(t)for(let a=0,s=t.length;a<s;a++){let s=t[a],o=-1;if(s.include){let t=er(s.include);switch(t.kind){case 0:case 1:o=e.getCompiledRuleId(r[s.include],n,r);break;case 2:let i=r[t.ruleName];i&&(o=e.getCompiledRuleId(i,n,r));break;case 3:case 4:let a=t.scopeName,l=4===t.kind?t.ruleName:null,u=n.getExternalGrammar(a,r);if(u)if(l){let t=u.repository[l];t&&(o=e.getCompiledRuleId(t,n,u.repository))}else o=e.getCompiledRuleId(u.repository.$self,n,u.repository)}}else o=e.getCompiledRuleId(s,n,r);if(-1!==o){let e=n.getRule(o),t=!1;if((e instanceof eu||e instanceof ec||e instanceof eh)&&e.hasMissingPatterns&&0===e.patterns.length&&(t=!0),t)continue;i.push(o)}}return{patterns:i,hasMissingPatterns:(t?t.length:0)!==i.length}}},ep=class e{constructor(e,t){if(e){let t=e.length,n=0,r=[],i=!1;for(let a=0;a<t;a++)if("\\"===e.charAt(a)&&a+1<t){let t=e.charAt(a+1);"z"===t?(r.push(e.substring(n,a)),r.push("$(?!\\n)(?<!\\n)"),n=a+2):("A"===t||"G"===t)&&(i=!0),a++}this.hasAnchor=i,0===n?this.source=e:(r.push(e.substring(n,t)),this.source=r.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,this.hasBackReferences=ei.test(this.source)}clone(){return new e(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){let n=t.map(t=>e.substring(t.start,t.end));return ea.lastIndex=0,this.source.replace(ea,(e,t)=>L(n[parseInt(t,10)]||""))}_buildAnchorCache(){let e,t,n,r,i=[],a=[],s=[],o=[];for(e=0,t=this.source.length;e<t;e++)n=this.source.charAt(e),i[e]=n,a[e]=n,s[e]=n,o[e]=n,"\\"===n&&e+1<t&&("A"===(r=this.source.charAt(e+1))?(i[e+1]="￿",a[e+1]="￿",s[e+1]="A",o[e+1]="A"):"G"===r?(i[e+1]="￿",a[e+1]="G",s[e+1]="￿",o[e+1]="G"):(i[e+1]=r,a[e+1]=r,s[e+1]=r,o[e+1]=r),e++);return{A0_G0:i.join(""),A0_G1:a.join(""),A1_G0:s.join(""),A1_G1:o.join("")}}resolveAnchors(e,t){if(!this.hasAnchor||!this._anchorCache)return this.source;if(e)if(t)return this._anchorCache.A1_G1;else return this._anchorCache.A1_G0;return t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},em=class{constructor(){this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(e=>e.source);this._cached=new eg(e,t,this._items.map(e=>e.ruleId))}return this._cached}compileAG(e,t,n){if(!this._hasAnchors)return this.compile(e);if(t)if(n)return this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1;else return this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0;return n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0)}_resolveAnchors(e,t,n){return new eg(e,this._items.map(e=>e.resolveAnchors(t,n)),this._items.map(e=>e.ruleId))}},eg=class{constructor(e,t,n){this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){"function"==typeof this.scanner.dispose&&this.scanner.dispose()}toString(){let e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join("\n")}findNextMatchSync(e,t,n){let r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},ef=class{constructor(e,t){this.languageId=e,this.tokenType=t}},eb=class e{constructor(e,t){this._getBasicScopeAttributes=new T(e=>new ef(this._scopeToLanguage(e),this._toStandardTokenType(e))),this._defaultAttributes=new ef(e,8),this._embeddedLanguagesMatcher=new ey(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return null===t?e._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(t){let n=t.match(e.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw Error("Unexpected match for standard token type!")}};eb._NULL_SCOPE_METADATA=new ef(0,0),eb.STANDARD_TOKEN_TYPE_REGEXP=/\b(comment|string|regex|meta\.embedded)\b/;var ey=class{constructor(e){if(0===e.length)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);let t=e.map(([e,t])=>L(e));t.sort(),t.reverse(),this.scopesRegExp=RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;let t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}};"undefined"!=typeof process&&process.env.VSCODE_TEXTMATE_DEBUG;var ev=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function e_(e,t,n,r,i,a,s,o){let l=t.content.length,u=!1,c=-1;if(s){let s=function(e,t,n,r,i,a){let s=i.beginRuleCapturedEOL?0:-1,o=[];for(let t=i;t;t=t.pop()){let n=t.getRule(e);n instanceof eh&&o.push({rule:n,stack:t})}for(let p=o.pop();p;p=o.pop()){var l,u,c,h,d;let{ruleScanner:o,findOptions:m}=(l=p.rule,u=e,c=p.stack.endRule,h=n,d=r===s,{ruleScanner:l.compileWhileAG(u,c,h,d),findOptions:0}),g=o.findNextMatchSync(t,r,m);if(g){if(-2!==g.ruleId){i=p.stack.pop();break}g.captureIndices&&g.captureIndices.length&&(a.produce(p.stack,g.captureIndices[0].start),ew(e,t,n,p.stack,a,p.rule.whileCaptures,g.captureIndices),a.produce(p.stack,g.captureIndices[0].end),s=g.captureIndices[0].end,g.captureIndices[0].end>r&&(r=g.captureIndices[0].end,n=!1))}else{i=p.stack.pop();break}}return{stack:i,linePos:r,anchorPosition:s,isFirstLine:n}}(e,t,n,r,i,a);i=s.stack,r=s.linePos,n=s.isFirstLine,c=s.anchorPosition}let h=Date.now();for(;!u;){if(0!==o&&Date.now()-h>o)return new ev(i,!0);!function(){let s=function(e,t,n,r,i,a){let s=function(e,t,n,r,i,a){let{ruleScanner:s,findOptions:o}=ek(i.getRule(e),e,i.endRule,n,r===a),l=s.findNextMatchSync(t,r,o);return l?{captureIndices:l.captureIndices,matchedRuleId:l.ruleId}:null}(e,t,n,r,i,a),o=e.getInjections();if(0===o.length)return s;let l=function(e,t,n,r,i,a,s){let o,l=Number.MAX_VALUE,u=null,c=0,h=a.contentNameScopesList.getScopeNames();for(let a=0,d=e.length;a<d;a++){let d=e[a];if(!d.matcher(h))continue;let{ruleScanner:p,findOptions:m}=ek(t.getRule(d.ruleId),t,null,r,i===s),g=p.findNextMatchSync(n,i,m);if(!g)continue;let f=g.captureIndices[0].start;if(!(f>=l)&&(l=f,u=g.captureIndices,o=g.ruleId,c=d.priority,l===i))break}return u?{priorityMatch:-1===c,captureIndices:u,matchedRuleId:o}:null}(o,e,t,n,r,i,a);if(!l)return s;if(!s)return l;let u=s.captureIndices[0].start,c=l.captureIndices[0].start;return c<u||l.priorityMatch&&c===u?l:s}(e,t,n,r,i,c);if(!s){a.produce(i,l),u=!0;return}let o=s.captureIndices,h=s.matchedRuleId,d=!!o&&o.length>0&&o[0].end>r;if(-1===h){let s=i.getRule(e);a.produce(i,o[0].start),i=i.withContentNameScopesList(i.nameScopesList),ew(e,t,n,i,a,s.endCaptures,o),a.produce(i,o[0].end);let h=i;if(i=i.parent,c=h.getAnchorPos(),!d&&h.getEnterPos()===r){i=h,a.produce(i,l),u=!0;return}}else{let s=e.getRule(h);a.produce(i,o[0].start);let p=i,m=s.getName(t.content,o),g=i.contentNameScopesList.pushAttributed(m,e);if(i=i.push(h,r,c,o[0].end===l,null,g,g),s instanceof ec){ew(e,t,n,i,a,s.beginCaptures,o),a.produce(i,o[0].end),c=o[0].end;let r=s.getContentName(t.content,o),h=g.pushAttributed(r,e);if(i=i.withContentNameScopesList(h),s.endHasBackReferences&&(i=i.withEndRule(s.getEndWithResolvedBackReferences(t.content,o))),!d&&p.hasSameRuleAs(i)){i=i.pop(),a.produce(i,l),u=!0;return}}else if(s instanceof eh){ew(e,t,n,i,a,s.beginCaptures,o),a.produce(i,o[0].end),c=o[0].end;let r=s.getContentName(t.content,o),h=g.pushAttributed(r,e);if(i=i.withContentNameScopesList(h),s.whileHasBackReferences&&(i=i.withEndRule(s.getWhileWithResolvedBackReferences(t.content,o))),!d&&p.hasSameRuleAs(i)){i=i.pop(),a.produce(i,l),u=!0;return}}else if(ew(e,t,n,i,a,s.captures,o),a.produce(i,o[0].end),i=i.pop(),!d){i=i.safePop(),a.produce(i,l),u=!0;return}}o[0].end>r&&(r=o[0].end,n=!1)}()}return new ev(i,!1)}function ek(e,t,n,r,i){return{ruleScanner:e.compileAG(t,n,r,i),findOptions:0}}function ew(e,t,n,r,i,a,s){if(0===a.length)return;let o=t.content,l=Math.min(a.length,s.length),u=[],c=s[0].end;for(let t=0;t<l;t++){let l=a[t];if(null===l)continue;let h=s[t];if(0===h.length)continue;if(h.start>c)break;for(;u.length>0&&u[u.length-1].endPos<=h.start;)i.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?i.produceFromScopes(u[u.length-1].scopes,h.start):i.produce(r,h.start),l.retokenizeCapturedWithRuleId){let t=l.getName(o,s),a=r.contentNameScopesList.pushAttributed(t,e),u=l.getContentName(o,s),c=a.pushAttributed(u,e),d=r.push(l.retokenizeCapturedWithRuleId,h.start,-1,!1,null,a,c),p=e.createOnigString(o.substring(0,h.end));e_(e,p,n&&0===h.start,h.start,d,i,!1,0),H(p);continue}let d=l.getName(o,s);if(null!==d){let t=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(d,e);u.push(new ex(t,h.end))}}for(;u.length>0;)i.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var ex=class{constructor(e,t){this.scopes=e,this.endPos=t}};function eS(e,t,n,r,i){let a=$(t,eC),s=ed.getCompiledRuleId(n,r,i.repository);for(let n of a)e.push({debugSelector:t,matcher:n.matcher,ruleId:s,grammar:i,priority:n.priority})}function eC(e,t){if(t.length<e.length)return!1;let n=0;return e.every(e=>{for(let r=n;r<t.length;r++)if(function(e,t){if(!e)return!1;if(e===t)return!0;let n=t.length;return e.length>n&&e.substr(0,n)===t&&"."===e[n]}(t[r],e))return n=r+1,!0;return!1})}var eP=class{constructor(e,t,n,r,i,a,s,o){if(this._rootScopeName=e,this.balancedBracketSelectors=a,this._onigLib=o,this._basicScopeAttributesProvider=new eb(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=s,this._grammar=eN(t,null),this._injections=null,this._tokenTypeMatchers=[],i)for(let e of Object.keys(i))for(let t of $(e,eC))this._tokenTypeMatchers.push({matcher:t.matcher,type:i[e]})}get themeProvider(){return this._grammarRepository}dispose(){for(let e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){let e=[],t=this._rootScopeName,n=({lookup:e=>e===this._rootScopeName?this._grammar:this.getExternalGrammar(e),injections:e=>this._grammarRepository.injections(e)}).lookup(t);if(n){let r=n.injections;if(r)for(let t in r)eS(e,t,r[t],this,n);let i=this._grammarRepository.injections(t);i&&i.forEach(t=>{let n=this.getExternalGrammar(t);if(n){let t=n.injectionSelector;t&&eS(e,t,n,this,n)}})}return e.sort((e,t)=>e.priority-t.priority),e}getInjections(){return null===this._injections&&(this._injections=this._collectInjections()),this._injections}registerRule(e){let t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){let n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=eN(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){let r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){let r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){let i;if(-1===this._rootId&&(this._rootId=ed.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections()),t&&t!==eT.NULL)i=!1,t.reset();else{let e;i=!0;let n=this._basicScopeAttributesProvider.getDefaultAttributes(),r=this.themeProvider.getDefaults(),a=U.set(0,n.languageId,n.tokenType,null,r.fontStyle,r.foregroundId,r.backgroundId),s=this.getRule(this._rootId).getName(null,null);e=s?eA.createRootAndLookUpScopeName(s,a,this):eA.createRoot("unknown",a),t=new eT(null,this._rootId,-1,-1,!1,null,e,e)}e+="\n";let a=this.createOnigString(e),s=a.content.length,o=new eR(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),l=e_(this,a,i,0,t,o,!0,r);return H(a),{lineLength:s,lineTokens:o,ruleStack:l.stack,stoppedEarly:l.stoppedEarly}}};function eN(e,t){return(e=function e(t){return Array.isArray(t)?function(t){let n=[];for(let r=0,i=t.length;r<i;r++)n[r]=e(t[r]);return n}(t):"object"==typeof t?function(t){let n={};for(let r in t)n[r]=e(t[r]);return n}(t):t}(e)).repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var eA=class e{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(t,n){let r=t,i=t?.scopePath??null;for(let t of n)r=new e(r,i=R.push(i,t.scopeNames),t.encodedTokenAttributes);return r}static createRoot(t,n){return new e(null,new R(null,t),n)}static createRootAndLookUpScopeName(t,n,r){let i=r.getMetadataForScope(t),a=new R(null,t),s=r.themeProvider.themeMatch(a),o=e.mergeAttributes(n,i,s);return new e(null,a,o)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return e.equals(this,t)}static equals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}}static mergeAttributes(e,t,n){let r=-1,i=0,a=0;return null!==n&&(r=n.fontStyle,i=n.foregroundId,a=n.backgroundId),U.set(e,t.languageId,t.tokenType,null,r,i,a)}pushAttributed(t,n){if(null===t)return this;if(-1===t.indexOf(" "))return e._pushAttributed(this,t,n);let r=t.split(/ /g),i=this;for(let t of r)i=e._pushAttributed(i,t,n);return i}static _pushAttributed(t,n,r){let i=r.getMetadataForScope(n),a=t.scopePath.push(n),s=r.themeProvider.themeMatch(a),o=e.mergeAttributes(t.tokenAttributes,i,s);return new e(t,a,o)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}},eL=class e{constructor(e,t,n,r,i,a,s,o){this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=i,this.endRule=a,this.nameScopesList=s,this.contentNameScopesList=o,this._stackElementBrand=void 0,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=r}equals(t){return null!==t&&e._equals(this,t)}static _equals(e,t){return e===t||!!this._structuralEquals(e,t)&&eA.equals(e.contentNameScopesList,t.contentNameScopesList)}static _structuralEquals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){e._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,i,a,s,o){return new e(this,t,n,r,i,a,s,o)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){let e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(t){return this.endRule===t?this:new e(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(t,n){let r=eA.fromExtension(t?.nameScopesList??null,n.nameScopesList);return new e(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,eA.fromExtension(r,n.contentNameScopesList))}};eL.NULL=new eL(null,0,0,0,!1,null,null,null);var eT=eL,eE=class{constructor(e,t){this.allowAny=!1,this.balancedBracketScopes=e.flatMap(e=>"*"===e?(this.allowAny=!0,[]):$(e,eC).map(e=>e.matcher)),this.unbalancedBracketScopes=t.flatMap(e=>$(e,eC).map(e=>e.matcher))}get matchesAlways(){return this.allowAny&&0===this.unbalancedBracketScopes.length}get matchesNever(){return 0===this.balancedBracketScopes.length&&!this.allowAny}match(e){for(let t of this.unbalancedBracketScopes)if(t(e))return!1;for(let t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},eR=class{constructor(e,t,n,r){this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let n=e?.tokenAttributes??0,r=!1;if(this.balancedBracketSelectors?.matchesAlways&&(r=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){let t=e?.getScopeNames()??[];for(let e of this._tokenTypeOverrides)e.matcher(t)&&(n=U.set(n,0,e.type,null,-1,0,0));this.balancedBracketSelectors&&(r=this.balancedBracketSelectors.match(t))}if(r&&(n=U.set(n,0,8,r,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=t;return}let n=e?.getScopeNames()??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),0===this._tokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),0===this._binaryTokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);let n=new Uint32Array(this._binaryTokens.length);for(let e=0,t=this._binaryTokens.length;e<t;e++)n[e]=this._binaryTokens[e];return n}},eO=class{constructor(e,t){this._onigLib=t,this._grammars=new Map,this._rawGrammars=new Map,this._injectionGrammars=new Map,this._theme=e}dispose(){for(let e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,i){if(!this._grammars.has(e)){let a=this._rawGrammars.get(e);if(!a)return null;this._grammars.set(e,new eP(e,a,t,n,r,i,this,this._onigLib))}return this._grammars.get(e)}},eI=class{constructor(e){this._options=e,this._syncRegistry=new eO(E.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(E.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new eE(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,r,i){let a=new J(this._syncRegistry,e);for(;a.Q.length>0;)a.Q.map(e=>this._loadSingleGrammar(e.scopeName)),a.processQueue();return this._grammarForScopeName(e,t,n,r,i)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){let t=this._options.loadGrammar(e);if(t){let n="function"==typeof this._options.getInjections?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,r=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,r)}_grammarForScopeName(e,t=0,n=null,r=null,i=null){return this._syncRegistry.grammarForScopeName(e,t,n,r,i)}},ej=eT.NULL;let eM=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class eD{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function eB(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new eD(n,r,t)}function eG(e){return e.toLowerCase()}eD.prototype.normal={},eD.prototype.property={},eD.prototype.space=void 0;class eU{constructor(e,t){this.attribute=t,this.property=e}}eU.prototype.attribute="",eU.prototype.booleanish=!1,eU.prototype.boolean=!1,eU.prototype.commaOrSpaceSeparated=!1,eU.prototype.commaSeparated=!1,eU.prototype.defined=!1,eU.prototype.mustUseProperty=!1,eU.prototype.number=!1,eU.prototype.overloadedBoolean=!1,eU.prototype.property="",eU.prototype.spaceSeparated=!1,eU.prototype.space=void 0;let e$=0,eF=eK(),eq=eK(),eH=eK(),eW=eK(),ez=eK(),eV=eK(),eJ=eK();function eK(){return 2**++e$}let eX=Object.keys(s);class eY extends eU{constructor(e,t,n,r){let i=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++i<eX.length;){let e=eX[i];!function(e,t,n){n&&(e[t]=n)}(this,eX[i],(n&s[e])===s[e])}}}function eQ(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let a=new eY(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[eG(r)]=r,n[eG(a.attribute)]=r}return new eD(t,n,e.space)}eY.prototype.defined=!0;let eZ=eQ({properties:{ariaActiveDescendant:null,ariaAtomic:eq,ariaAutoComplete:null,ariaBusy:eq,ariaChecked:eq,ariaColCount:eW,ariaColIndex:eW,ariaColSpan:eW,ariaControls:ez,ariaCurrent:null,ariaDescribedBy:ez,ariaDetails:null,ariaDisabled:eq,ariaDropEffect:ez,ariaErrorMessage:null,ariaExpanded:eq,ariaFlowTo:ez,ariaGrabbed:eq,ariaHasPopup:null,ariaHidden:eq,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ez,ariaLevel:eW,ariaLive:null,ariaModal:eq,ariaMultiLine:eq,ariaMultiSelectable:eq,ariaOrientation:null,ariaOwns:ez,ariaPlaceholder:null,ariaPosInSet:eW,ariaPressed:eq,ariaReadOnly:eq,ariaRelevant:null,ariaRequired:eq,ariaRoleDescription:ez,ariaRowCount:eW,ariaRowIndex:eW,ariaRowSpan:eW,ariaSelected:eq,ariaSetSize:eW,ariaSort:null,ariaValueMax:eW,ariaValueMin:eW,ariaValueNow:eW,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function e0(e,t){return t in e?e[t]:t}function e1(e,t){return e0(e,t.toLowerCase())}let e3=eQ({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:eV,acceptCharset:ez,accessKey:ez,action:null,allow:null,allowFullScreen:eF,allowPaymentRequest:eF,allowUserMedia:eF,alt:null,as:null,async:eF,autoCapitalize:null,autoComplete:ez,autoFocus:eF,autoPlay:eF,blocking:ez,capture:null,charSet:null,checked:eF,cite:null,className:ez,cols:eW,colSpan:null,content:null,contentEditable:eq,controls:eF,controlsList:ez,coords:eW|eV,crossOrigin:null,data:null,dateTime:null,decoding:null,default:eF,defer:eF,dir:null,dirName:null,disabled:eF,download:eH,draggable:eq,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:eF,formTarget:null,headers:ez,height:eW,hidden:eF,high:eW,href:null,hrefLang:null,htmlFor:ez,httpEquiv:ez,id:null,imageSizes:null,imageSrcSet:null,inert:eF,inputMode:null,integrity:null,is:null,isMap:eF,itemId:null,itemProp:ez,itemRef:ez,itemScope:eF,itemType:ez,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:eF,low:eW,manifest:null,max:null,maxLength:eW,media:null,method:null,min:null,minLength:eW,multiple:eF,muted:eF,name:null,nonce:null,noModule:eF,noValidate:eF,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:eF,optimum:eW,pattern:null,ping:ez,placeholder:null,playsInline:eF,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:eF,referrerPolicy:null,rel:ez,required:eF,reversed:eF,rows:eW,rowSpan:eW,sandbox:ez,scope:null,scoped:eF,seamless:eF,selected:eF,shadowRootClonable:eF,shadowRootDelegatesFocus:eF,shadowRootMode:null,shape:null,size:eW,sizes:null,slot:null,span:eW,spellCheck:eq,src:null,srcDoc:null,srcLang:null,srcSet:null,start:eW,step:null,style:null,tabIndex:eW,target:null,title:null,translate:null,type:null,typeMustMatch:eF,useMap:null,value:eq,width:eW,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ez,axis:null,background:null,bgColor:null,border:eW,borderColor:null,bottomMargin:eW,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:eF,declare:eF,event:null,face:null,frame:null,frameBorder:null,hSpace:eW,leftMargin:eW,link:null,longDesc:null,lowSrc:null,marginHeight:eW,marginWidth:eW,noResize:eF,noHref:eF,noShade:eF,noWrap:eF,object:null,profile:null,prompt:null,rev:null,rightMargin:eW,rules:null,scheme:null,scrolling:eq,standby:null,summary:null,text:null,topMargin:eW,valueType:null,version:null,vAlign:null,vLink:null,vSpace:eW,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:eF,disableRemotePlayback:eF,prefix:null,property:null,results:eW,security:null,unselectable:null},space:"html",transform:e1}),e6=eQ({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:eJ,accentHeight:eW,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:eW,amplitude:eW,arabicForm:null,ascent:eW,attributeName:null,attributeType:null,azimuth:eW,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:eW,by:null,calcMode:null,capHeight:eW,className:ez,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:eW,diffuseConstant:eW,direction:null,display:null,dur:null,divisor:eW,dominantBaseline:null,download:eF,dx:null,dy:null,edgeMode:null,editable:null,elevation:eW,enableBackground:null,end:null,event:null,exponent:eW,externalResourcesRequired:null,fill:null,fillOpacity:eW,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:eV,g2:eV,glyphName:eV,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:eW,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:eW,horizOriginX:eW,horizOriginY:eW,id:null,ideographic:eW,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:eW,k:eW,k1:eW,k2:eW,k3:eW,k4:eW,kernelMatrix:eJ,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:eW,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:eW,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:eW,overlineThickness:eW,paintOrder:null,panose1:null,path:null,pathLength:eW,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ez,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:eW,pointsAtY:eW,pointsAtZ:eW,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:eJ,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:eJ,rev:eJ,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:eJ,requiredFeatures:eJ,requiredFonts:eJ,requiredFormats:eJ,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:eW,specularExponent:eW,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:eW,strikethroughThickness:eW,string:null,stroke:null,strokeDashArray:eJ,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:eW,strokeOpacity:eW,strokeWidth:null,style:null,surfaceScale:eW,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:eJ,tabIndex:eW,tableValues:null,target:null,targetX:eW,targetY:eW,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:eJ,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:eW,underlineThickness:eW,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:eW,values:null,vAlphabetic:eW,vMathematical:eW,vectorEffect:null,vHanging:eW,vIdeographic:eW,version:null,vertAdvY:eW,vertOriginX:eW,vertOriginY:eW,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:eW,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:e0}),e2=eQ({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),e8=eQ({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:e1}),e5=eQ({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),e4=eB([eZ,e3,e2,e8,e5],"html"),e7=eB([eZ,e6,e2,e8,e5],"svg"),e9={}.hasOwnProperty,te=/["&'<>`]/g,tt=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,tn=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,tr=/[|\\{}()[\]^$+*?.]/g,ti=new WeakMap,ta=/[\dA-Fa-f]/,ts=/\d/,to=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],tl={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},tu=["cent","copy","divide","gt","lt","not","para","times"],tc={}.hasOwnProperty,th={};for(r in tl)tc.call(tl,r)&&(th[tl[r]]=r);let td=/[^\dA-Za-z]/;function tp(e,t,n){let r,i=function(e,t,n){let r="&#x"+e.toString(16).toUpperCase();return n&&t&&!ta.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);if((n.useNamedReferences||n.useShortestReferences)&&(r=function(e,t,n,r){let i=String.fromCharCode(e);if(tc.call(th,i)){let e=th[i],a="&"+e;return n&&to.includes(e)&&!tu.includes(e)&&(!r||t&&61!==t&&td.test(String.fromCharCode(t)))?a:a+";"}return""}(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!r)&&n.useShortestReferences){let r=function(e,t,n){let r="&#"+String(e);return n&&t&&!ts.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);r.length<i.length&&(i=r)}return r&&(!n.useShortestReferences||r.length<i.length)?r:i}function tm(e,t){let n;var r,i=e,a=Object.assign({format:tp},t);if(i=i.replace(a.subset?(r=a.subset,(n=ti.get(r))||(n=function(e){let t=[],n=-1;for(;++n<e.length;)t.push(e[n].replace(tr,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(r),ti.set(r,n)),n):te,s),a.subset||a.escapeOnly)return i;return i.replace(tt,function(e,t,n){return a.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,n.charCodeAt(t+2),a)}).replace(tn,s);function s(e,t,n){return a.format(e.charCodeAt(0),n.charCodeAt(t+1),a)}}let tg=/^>|^->|<!--|-->|--!>|<!-$/g,tf=[">"],tb=["<",">"];function ty(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}function tv(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}let t_=/[A-Z]/g,tk=/-[a-z]/g,tw=/^data[-\w.:]+$/i;function tx(e,t){let n=eG(t),r=t,i=eU;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&tw.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(tk,tC);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!tk.test(e)){let n=e.replace(t_,tS);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=eY}return new i(r,t)}function tS(e){return"-"+e.toLowerCase()}function tC(e){return e.charAt(1).toUpperCase()}function tP(e){return e.join(" ").trim()}let tN=/[ \t\n\f\r]/g;function tA(e){return"object"==typeof e?"text"===e.type&&tL(e.value):tL(e)}function tL(e){return""===e.replace(tN,"")}let tT=tO(1),tE=tO(-1),tR=[];function tO(e){return function(t,n,r){let i=t?t.children:tR,a=(n||0)+e,s=i[a];if(!r)for(;s&&tA(s);)a+=e,s=i[a];return s}}let tI={}.hasOwnProperty;function tj(e){return function(t,n,r){return tI.call(e,t.tagName)&&e[t.tagName](t,n,r)}}let tM=tj({body:function(e,t,n){let r=tT(n,t);return!r||"comment"!==r.type},caption:tD,colgroup:tD,dd:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName)},dt:function(e,t,n){let r=tT(n,t);return!!(r&&"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName))},head:tD,html:function(e,t,n){let r=tT(n,t);return!r||"comment"!==r.type},li:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&"li"===r.tagName},optgroup:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&"optgroup"===r.tagName},option:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&("option"===r.tagName||"optgroup"===r.tagName)},p:function(e,t,n){let r=tT(n,t);return r?"element"===r.type&&("address"===r.tagName||"article"===r.tagName||"aside"===r.tagName||"blockquote"===r.tagName||"details"===r.tagName||"div"===r.tagName||"dl"===r.tagName||"fieldset"===r.tagName||"figcaption"===r.tagName||"figure"===r.tagName||"footer"===r.tagName||"form"===r.tagName||"h1"===r.tagName||"h2"===r.tagName||"h3"===r.tagName||"h4"===r.tagName||"h5"===r.tagName||"h6"===r.tagName||"header"===r.tagName||"hgroup"===r.tagName||"hr"===r.tagName||"main"===r.tagName||"menu"===r.tagName||"nav"===r.tagName||"ol"===r.tagName||"p"===r.tagName||"pre"===r.tagName||"section"===r.tagName||"table"===r.tagName||"ul"===r.tagName):!n||"element"!==n.type||"a"!==n.tagName&&"audio"!==n.tagName&&"del"!==n.tagName&&"ins"!==n.tagName&&"map"!==n.tagName&&"noscript"!==n.tagName&&"video"!==n.tagName},rp:tB,rt:tB,tbody:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName)},td:tG,tfoot:function(e,t,n){return!tT(n,t)},th:tG,thead:function(e,t,n){let r=tT(n,t);return!!(r&&"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName))},tr:function(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&"tr"===r.tagName}});function tD(e,t,n){let r=tT(n,t,!0);return!r||"comment"!==r.type&&!("text"===r.type&&tA(r.value.charAt(0)))}function tB(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&("rp"===r.tagName||"rt"===r.tagName)}function tG(e,t,n){let r=tT(n,t);return!r||"element"===r.type&&("td"===r.tagName||"th"===r.tagName)}let tU=tj({body:function(e){let t=tT(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&tA(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,n){let r=tE(n,t),i=tT(e,-1,!0);return!(n&&r&&"element"===r.type&&"colgroup"===r.tagName&&tM(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"col"===i.tagName)},head:function(e){let t=new Set;for(let n of e.children)if("element"===n.type&&("base"===n.tagName||"title"===n.tagName)){if(t.has(n.tagName))return!1;t.add(n.tagName)}let n=e.children[0];return!n||"element"===n.type},html:function(e){let t=tT(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,n){let r=tE(n,t),i=tT(e,-1);return!(n&&r&&"element"===r.type&&("thead"===r.tagName||"tbody"===r.tagName)&&tM(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"tr"===i.tagName)}}),t$={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},tF=["<","&"];function tq(e,t,n,r){return n&&"element"===n.type&&("script"===n.tagName||"style"===n.tagName)?e.value:tm(e.value,Object.assign({},r.settings.characterReferences,{subset:tF}))}let tH=function(e,t){let n=t||{};function r(t,...n){let i=r.invalid,a=r.handlers;if(t&&e9.call(t,e)){let n=String(t[e]);i=e9.call(a,n)?a[n]:r.unknown}if(i)return i.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,n,r){return r.settings.bogusComments?"<?"+tm(e.value,Object.assign({},r.settings.characterReferences,{subset:tf}))+">":"\x3c!--"+e.value.replace(tg,function(e){return tm(e,Object.assign({},r.settings.characterReferences,{subset:tb}))})+"--\x3e"},doctype:function(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,n,r){let i,a=r.schema,s="svg"!==a.space&&r.settings.omitOptionalTags,o="svg"===a.space?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase()),l=[];"html"===a.space&&"svg"===e.tagName&&(r.schema=e7);let u=function(e,t){let n,r=[],i=-1;if(t){for(n in t)if(null!==t[n]&&void 0!==t[n]){let i=function(e,t,n){let r,i=tx(e.schema,t),a=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,s=+!e.settings.allowDangerousCharacters,o=e.quote;if(i.overloadedBoolean&&(n===i.attribute||""===n)?n=!0:(i.boolean||i.overloadedBoolean)&&("string"!=typeof n||n===i.attribute||""===n)&&(n=!!n),null==n||!1===n||"number"==typeof n&&Number.isNaN(n))return"";let l=tm(i.attribute,Object.assign({},e.settings.characterReferences,{subset:t$.name[a][s]}));return!0===n||(n=Array.isArray(n)?(i.commaSeparated?tv:tP)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?l:(e.settings.preferUnquoted&&(r=tm(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:t$.unquoted[a][s]}))),r!==n&&(e.settings.quoteSmart&&ty(n,o)>ty(n,e.alternative)&&(o=e.alternative),r=o+tm(n,Object.assign({},e.settings.characterReferences,{subset:("'"===o?t$.single:t$.double)[a][s],attribute:!0}))+o),l+(r?"="+r:r))}(e,n,t[n]);i&&r.push(i)}}for(;++i<r.length;){let t=e.settings.tightAttributes?r[i].charAt(r[i].length-1):void 0;i!==r.length-1&&'"'!==t&&"'"!==t&&(r[i]+=" ")}return r.join("")}(r,e.properties),c=r.all("html"===a.space&&"template"===e.tagName?e.content:e);return r.schema=a,c&&(o=!1),!u&&s&&tU(e,t,n)||(l.push("<",e.tagName,u?" "+u:""),o&&("svg"===a.space||r.settings.closeSelfClosing)&&(i=u.charAt(u.length-1),(!r.settings.tightSelfClosing||"/"===i||i&&'"'!==i&&"'"!==i)&&l.push(" "),l.push("/")),l.push(">")),l.push(c),o||s&&tM(e,t,n)||l.push("</"+e.tagName+">"),l.join("")},raw:function(e,t,n,r){return r.settings.allowDangerousHtml?e.value:tq(e,t,n,r)},root:function(e,t,n,r){return r.all(e)},text:tq}}),tW={},tz={},tV=[];function tJ(e,t,n){return tH(e,t,n,this)}function tK(e){let t=[],n=e&&e.children||tV,r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}class tX extends Error{constructor(e){super(e),this.name="ShikiError"}}function tY(){return"undefined"!=typeof performance?performance.now():Date.now()}let tQ=(e,t)=>e+(t-e%t)%t;async function tZ(e){let t,n,r={};function i(e){n=e,r.HEAPU8=new Uint8Array(e),r.HEAPU32=new Uint32Array(e)}let a="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function s(e,t){return e?function(e,t,n=1024){let r=t+n,i=t;for(;e[i]&&!(i>=r);)++i;if(i-t>16&&e.buffer&&a)return a.decode(e.subarray(t,i));let s="";for(;t<i;){let n=e[t++];if(!(128&n)){s+=String.fromCharCode(n);continue}let r=63&e[t++];if((224&n)==192){s+=String.fromCharCode((31&n)<<6|r);continue}let i=63&e[t++];if((n=(240&n)==224?(15&n)<<12|r<<6|i:(7&n)<<18|r<<12|i<<6|63&e[t++])<65536)s+=String.fromCharCode(n);else{let e=n-65536;s+=String.fromCharCode(55296|e>>10,56320|1023&e)}}return s}(r.HEAPU8,e,t):""}let o={emscripten_get_now:tY,emscripten_memcpy_big:function(e,t,n){r.HEAPU8.copyWithin(e,t,t+n)},emscripten_resize_heap:function(e){let a=r.HEAPU8.length;if((e>>>=0)>0x80000000)return!1;for(let r=1;r<=4;r*=2){let s=a*(1+.2/r);if(s=Math.min(s,e+0x6000000),function(e){try{return t.grow(e-n.byteLength+65535>>>16),i(t.buffer),1}catch{}}(Math.min(0x80000000,tQ(Math.max(e,s),65536))))return!0}return!1},fd_write:()=>0};async function l(){let n=await e({env:o,wasi_snapshot_preview1:o});i((t=n.memory).buffer),Object.assign(r,n),r.UTF8ToString=s}return await l(),r}let t0=null;class t1{static _utf8ByteLength(e){let t=0;for(let n=0,r=e.length;n<r;n++){let i=e.charCodeAt(n),a=i,s=!1;if(i>=55296&&i<=56319&&n+1<r){let t=e.charCodeAt(n+1);t>=56320&&t<=57343&&(a=(i-55296<<10)+65536|t-56320,s=!0)}a<=127?t+=1:a<=2047?t+=2:a<=65535?t+=3:t+=4,s&&n++}return t}utf16Length;utf8Length;utf16Value;utf8Value;utf16OffsetToUtf8;utf8OffsetToUtf16;constructor(e){let t=e.length,n=t1._utf8ByteLength(e),r=n!==t,i=r?new Uint32Array(t+1):null;r&&(i[t]=n);let a=r?new Uint32Array(n+1):null;r&&(a[n]=t);let s=new Uint8Array(n),o=0;for(let n=0;n<t;n++){let l=e.charCodeAt(n),u=l,c=!1;if(l>=55296&&l<=56319&&n+1<t){let t=e.charCodeAt(n+1);t>=56320&&t<=57343&&(u=(l-55296<<10)+65536|t-56320,c=!0)}r&&(i[n]=o,c&&(i[n+1]=o),u<=127?a[o+0]=n:u<=2047?(a[o+0]=n,a[o+1]=n):u<=65535?(a[o+0]=n,a[o+1]=n,a[o+2]=n):(a[o+0]=n,a[o+1]=n,a[o+2]=n,a[o+3]=n)),u<=127?s[o++]=u:(u<=2047?s[o++]=192|(1984&u)>>>6:(u<=65535?s[o++]=224|(61440&u)>>>12:(s[o++]=240|(1835008&u)>>>18,s[o++]=128|(258048&u)>>>12),s[o++]=128|(4032&u)>>>6),s[o++]=128|(63&u)>>>0),c&&n++}this.utf16Length=t,this.utf8Length=n,this.utf16Value=e,this.utf8Value=s,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=a}createString(e){let t=e.omalloc(this.utf8Length);return e.HEAPU8.set(this.utf8Value,t),t}}class t3{static LAST_ID=0;static _sharedPtr=0;static _sharedPtrInUse=!1;id=++t3.LAST_ID;_onigBinding;content;utf16Length;utf8Length;utf16OffsetToUtf8;utf8OffsetToUtf16;ptr;constructor(e){if(!t0)throw new tX("Must invoke loadWasm first.");this._onigBinding=t0,this.content=e;let t=new t1(e);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!t3._sharedPtrInUse?(t3._sharedPtr||(t3._sharedPtr=t0.omalloc(1e4)),t3._sharedPtrInUse=!0,t0.HEAPU8.set(t.utf8Value,t3._sharedPtr),this.ptr=t3._sharedPtr):this.ptr=t.createString(t0)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===t3._sharedPtr?t3._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}}class t6{_onigBinding;_ptr;constructor(e){if(!t0)throw new tX("Must invoke loadWasm first.");let t=[],n=[];for(let r=0,i=e.length;r<i;r++){let i=new t1(e[r]);t[r]=i.createString(t0),n[r]=i.utf8Length}let r=t0.omalloc(4*e.length);t0.HEAPU32.set(t,r/4);let i=t0.omalloc(4*e.length);t0.HEAPU32.set(n,i/4);let a=t0.createOnigScanner(r,i,e.length);for(let n=0,r=e.length;n<r;n++)t0.ofree(t[n]);t0.ofree(i),t0.ofree(r),0===a&&function(e){throw new tX(e.UTF8ToString(e.getLastOnigError()))}(t0),this._onigBinding=t0,this._ptr=a}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(e,t,n){let r=0;if("number"==typeof n&&(r=n),"string"==typeof e){e=new t3(e);let n=this._findNextMatchSync(e,t,!1,r);return e.dispose(),n}return this._findNextMatchSync(e,t,!1,r)}_findNextMatchSync(e,t,n,r){let i=this._onigBinding,a=i.findNextOnigScannerMatch(this._ptr,e.id,e.ptr,e.utf8Length,e.convertUtf16OffsetToUtf8(t),r);if(0===a)return null;let s=i.HEAPU32,o=a/4,l=s[o++],u=s[o++],c=[];for(let t=0;t<u;t++){let n=e.convertUtf8OffsetToUtf16(s[o++]),r=e.convertUtf8OffsetToUtf16(s[o++]);c[t]={start:n,end:r,length:r-n}}return{index:l,captureIndices:c}}}function t2(e){return t=>WebAssembly.instantiate(e,t)}async function t8(e){return e&&await (i||(i=async function(){t0=await tZ(async t=>{let n=e;if("function"==typeof(n=await n)&&(n=await n(t)),"function"==typeof n&&(n=await n(t)),"function"==typeof n.instantiator)n=await n.instantiator(t);else if("function"==typeof n.default)n=await n.default(t);else{var r,i,a,s;if(void 0!==n.data&&(n=n.data),r=n,"undefined"!=typeof Response&&r instanceof Response){n="function"==typeof WebAssembly.instantiateStreaming?await (a=n,e=>WebAssembly.instantiateStreaming(a,e))(t):await (s=n,async e=>{let t=await s.arrayBuffer();return WebAssembly.instantiate(t,e)})(t)}else(i=n,"undefined"!=typeof ArrayBuffer&&(i instanceof ArrayBuffer||ArrayBuffer.isView(i))||"undefined"!=typeof Buffer&&Buffer.isBuffer?.(i)||"undefined"!=typeof SharedArrayBuffer&&i instanceof SharedArrayBuffer||"undefined"!=typeof Uint32Array&&i instanceof Uint32Array||n instanceof WebAssembly.Module)?n=await t2(n)(t):"default"in n&&n.default instanceof WebAssembly.Module&&(n=await t2(n.default)(t))}return"instance"in n&&(n=n.instance),"exports"in n&&(n=n.exports),n})}())),{createScanner:e=>new t6(e),createString:e=>new t3(e)}}function t5(e,t=!1){let n=e.split(/(\r?\n)/g),r=0,i=[];for(let e=0;e<n.length;e+=2){let a=t?n[e]+(n[e+1]||""):n[e];i.push([a,r]),r+=n[e].length,r+=n[e+1]?.length||0}return i}function t4(e){return!e||["plaintext","txt","text","plain"].includes(e)}function t7(e){return"ansi"===e||t4(e)}function t9(e){return"none"===e}function ne(e,t){var n;if(!t)return e;for(let r of(e.properties||(e.properties={}),(n=e.properties).class||(n.class=[]),"string"==typeof e.properties.class&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]),Array.isArray(t)?t:t.split(/\s+/g)))r&&!e.properties.class.includes(r)&&e.properties.class.push(r);return e}async function nt(e){return Promise.resolve("function"==typeof e?e():e).then(e=>e.default||e)}function nn(e,t){let n="string"==typeof e?{}:{...e.colorReplacements},r="string"==typeof e?e:e.name;for(let[e,i]of Object.entries(t?.colorReplacements||{}))"string"==typeof i?n[e]=i:e===r&&Object.assign(n,i);return n}function nr(e,t){return e&&t?.[e?.toLowerCase()]||e}function ni(e){let t={};return e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle&&(e.fontStyle&j.Italic&&(t["font-style"]="italic"),e.fontStyle&j.Bold&&(t["font-weight"]="bold"),e.fontStyle&j.Underline&&(t["text-decoration"]="underline")),t}function na(e){return Object.entries(e).map(([e,t])=>`${e}:${t}`).join(";")}class ns extends Error{constructor(e){super(e),this.name="ShikiError"}}let no=[function(){let e=new WeakMap;function t(t){if(!e.has(t.meta)){let n=function(e){if("number"==typeof e){if(e<0||e>t.source.length)throw new ns(`Invalid decoration offset: ${e}. Code length: ${t.source.length}`);return{...r.indexToPos(e),offset:e}}{let t=r.lines[e.line];if(void 0===t)throw new ns(`Invalid decoration position ${JSON.stringify(e)}. Lines length: ${r.lines.length}`);if(e.character<0||e.character>t.length)throw new ns(`Invalid decoration position ${JSON.stringify(e)}. Line ${e.line} length: ${t.length}`);return{...e,offset:r.posToIndex(e.line,e.character)}}},r=function(e){let t=t5(e,!0).map(([e])=>e);return{lines:t,indexToPos:function(n){if(n===e.length)return{line:t.length-1,character:t[t.length-1].length};let r=n,i=0;for(let e of t){if(r<e.length)break;r-=e.length,i++}return{line:i,character:r}},posToIndex:function(e,n){let r=0;for(let n=0;n<e;n++)r+=t[n].length;return r+n}}}(t.source),i=(t.options.decorations||[]).map(e=>({...e,start:n(e.start),end:n(e.end)}));(function(e){for(let t=0;t<e.length;t++){let n=e[t];if(n.start.offset>n.end.offset)throw new ns(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){let t=e[r],i=n.start.offset<t.start.offset&&t.start.offset<n.end.offset,a=n.start.offset<t.end.offset&&t.end.offset<n.end.offset,s=t.start.offset<n.start.offset&&n.start.offset<t.end.offset,o=t.start.offset<n.end.offset&&n.end.offset<t.end.offset;if(i||a||s||o){if(a&&a||s&&o)continue;throw new ns(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(t.start)} intersect.`)}}}})(i),e.set(t.meta,{decorations:i,converter:r,source:t.source})}return e.get(t.meta)}return{name:"shiki:decorations",tokens(e){if(this.options.decorations?.length){var n=e,r=t(this).decorations.flatMap(e=>[e.start.offset,e.end.offset]);let i=Array.from(r instanceof Set?r:new Set(r)).sort((e,t)=>e-t);return i.length?n.map(e=>e.flatMap(e=>{let t=i.filter(t=>e.offset<t&&t<e.offset+e.content.length).map(t=>t-e.offset).sort((e,t)=>e-t);if(!t.length)return e;let n=0,r=[];for(let i of t)i>n&&r.push({...e,content:e.content.slice(n,i),offset:e.offset+n}),n=i;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r})):n}},code(e){if(!this.options.decorations?.length)return;let n=t(this),r=Array.from(e.children).filter(e=>"element"===e.type&&"span"===e.tagName);if(r.length!==n.converter.lines.length)throw new ns(`Number of lines in code element (${r.length}) does not match the number of lines in the source (${n.converter.lines.length}). Failed to apply decorations.`);function i(e,t,n,i){let s=r[e],o="",l=-1,u=-1;if(0===t&&(l=0),0===n&&(u=0),n===Number.POSITIVE_INFINITY&&(u=s.children.length),-1===l||-1===u)for(let e=0;e<s.children.length;e++)o+=function e(t){return"text"===t.type?t.value:"element"===t.type?t.children.map(e).join(""):""}(s.children[e]),-1===l&&o.length===t&&(l=e+1),-1===u&&o.length===n&&(u=e+1);if(-1===l)throw new ns(`Failed to find start index for decoration ${JSON.stringify(i.start)}`);if(-1===u)throw new ns(`Failed to find end index for decoration ${JSON.stringify(i.end)}`);let c=s.children.slice(l,u);if(i.alwaysWrap||c.length!==s.children.length)if(i.alwaysWrap||1!==c.length||"element"!==c[0].type){let e={type:"element",tagName:"span",properties:{},children:c};a(e,i,"wrapper"),s.children.splice(l,c.length,e)}else a(c[0],i,"token");else a(s,i,"line")}function a(e,t,n){let r=t.properties||{},i=t.transform||(e=>e);return e.tagName=t.tagName||"span",e.properties={...e.properties,...r,class:e.properties.class},t.properties?.class&&ne(e,t.properties.class),e=i(e,n)||e}let s=[];for(let e of n.decorations.sort((e,t)=>t.start.offset-e.start.offset)){let{start:t,end:n}=e;if(t.line===n.line)i(t.line,t.character,n.character,e);else if(t.line<n.line){i(t.line,t.character,Number.POSITIVE_INFINITY,e);for(let i=t.line+1;i<n.line;i++)s.unshift(()=>{var t;r[t=i]=a(r[t],e,"line")});i(n.line,0,n.character,e)}}s.forEach(e=>e())}}}()];function nl(e){return[...e.transformers||[],...no]}class nu{constructor(e,t,n){this._stack=e,this.lang=t,this.theme=n}static initial(e,t){return new nu(ej,e,t)}get scopes(){var e=this._stack;let t=[],n=new Set;return!function e(r){if(n.has(r))return;n.add(r);let i=r?.nameScopesList?.scopeName;i&&t.push(i),r.parent&&e(r.parent)}(e),t}toJSON(){return{lang:this.lang,theme:this.theme,scopes:this.scopes}}}var nc=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],nh={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function nd(e,t){let n,r=1,i=e[t+r++];if("2"===i){let i=[e[t+r++],e[t+r++],e[t+r]].map(e=>Number.parseInt(e));3!==i.length||i.some(e=>Number.isNaN(e))||(n={type:"rgb",rgb:i})}else if("5"===i){let i=Number.parseInt(e[t+r]);Number.isNaN(i)||(n={type:"table",index:Number(i)})}return[r,n]}var np={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function nm(e,t,n={}){let{lang:r="text",theme:i=e.getLoadedThemes()[0]}=n;if(t4(r)||t9(i))return t5(t).map(e=>[{content:e[0],offset:e[1]}]);let{theme:a,colorMap:s}=e.setTheme(i);if("ansi"===r)return function(e,t,n){let r,i,a,s=nn(e,n),o=t5(t),l=function(e=np){let t;function n(e){return`#${e.map(e=>Math.max(0,Math.min(e,255)).toString(16).padStart(2,"0")).join("")}`}return{value:function(r){switch(r.type){case"named":return e[r.name];case"rgb":return n(r.rgb);case"table":var i;return i=r.index,function(){if(t)return t;t=[];for(let n=0;n<nc.length;n++)t.push(e[nc[n]]);let r=[0,95,135,175,215,255];for(let e=0;e<6;e++)for(let i=0;i<6;i++)for(let a=0;a<6;a++)t.push(n([r[e],r[i],r[a]]));let i=8;for(let e=0;e<24;e++,i+=10)t.push(n([i,i,i]));return t}()[i]}}}}(Object.fromEntries(nc.map(t=>[t,e.colors?.[`terminal.ansi${t[0].toUpperCase()}${t.substring(1)}`]]))),u=(r=null,i=null,a=new Set,{parse(e){let t=[],n=0;do{let s=function(e,t){let n=e.indexOf("\x1b[",t);if(-1!==n){let t=e.indexOf("m",n);return{sequence:e.substring(n+2,t).split(";"),startPosition:n,position:t+1}}return{position:e.length}}(e,n),o=s.sequence?e.substring(n,s.startPosition):e.substring(n);if(o.length>0&&t.push({value:o,foreground:r,background:i,decorations:new Set(a)}),s.sequence){let e=function(e){let t=[];for(let n=0;n<e.length;n++){let r=Number.parseInt(e[n]);if(!Number.isNaN(r))if(0===r)t.push({type:"resetAll"});else if(r<=9)nh[r]&&t.push({type:"setDecoration",value:nh[r]});else if(r<=29){let e=nh[r-20];e&&t.push({type:"resetDecoration",value:e})}else if(r<=37)t.push({type:"setForegroundColor",value:{type:"named",name:nc[r-30]}});else if(38===r){let[r,i]=nd(e,n);i&&t.push({type:"setForegroundColor",value:i}),n+=r}else if(39===r)t.push({type:"resetForegroundColor"});else if(r<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:nc[r-40]}});else if(48===r){let[r,i]=nd(e,n);i&&t.push({type:"setBackgroundColor",value:i}),n+=r}else 49===r?t.push({type:"resetBackgroundColor"}):r>=90&&r<=97?t.push({type:"setForegroundColor",value:{type:"named",name:nc[r-90+8]}}):r>=100&&r<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:nc[r-100+8]}})}return t}(s.sequence);for(let t of e)"resetAll"===t.type?(r=null,i=null,a.clear()):"resetForegroundColor"===t.type?r=null:"resetBackgroundColor"===t.type?i=null:"resetDecoration"===t.type&&a.delete(t.value);for(let t of e)"setForegroundColor"===t.type?r=t.value:"setBackgroundColor"===t.type?i=t.value:"setDecoration"===t.type&&a.add(t.value)}n=s.position}while(n<e.length);return t}});return o.map(t=>u.parse(t[0]).map(n=>{let r,i;n.decorations.has("reverse")?(r=n.background?l.value(n.background):e.bg,i=n.foreground?l.value(n.foreground):e.fg):(r=n.foreground?l.value(n.foreground):e.fg,i=n.background?l.value(n.background):void 0),r=nr(r,s),i=nr(i,s),n.decorations.has("dim")&&(r=function(e){let t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){let e=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${e}`}else if(t[2])return`#${t[1]}${t[2]}80`;else return`#${Array.from(t[1]).map(e=>`${e}${e}`).join("")}80`;let n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}(r));let a=j.None;return n.decorations.has("bold")&&(a|=j.Bold),n.decorations.has("italic")&&(a|=j.Italic),n.decorations.has("underline")&&(a|=j.Underline),{content:n.value,offset:t[1],color:r,bgColor:i,fontStyle:a}}))}(a,t,n);let o=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==o.name)throw new w(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${o.name}"`);if(n.grammarState.theme!==i)throw new w(`Grammar state theme "${n.grammarState.theme}" does not match highlight theme "${i}"`)}return ng(t,o,a,s,n).tokens}function ng(e,t,n,r,i){let a=nn(n,i),{tokenizeMaxLineLength:s=0,tokenizeTimeLimit:o=500}=i,l=t5(e),u=i.grammarState?function(e){if(!(e instanceof nu))throw new ns("Invalid grammar state");return e._stack}(i.grammarState):null!=i.grammarContextCode?ng(i.grammarContextCode,t,n,r,{...i,grammarState:void 0,grammarContextCode:void 0}).stateStack:ej,c=[],h=[];for(let e=0,d=l.length;e<d;e++){let d,p,[m,g]=l[e];if(""===m){c=[],h.push([]);continue}if(s>0&&m.length>=s){c=[],h.push([{content:m,offset:g,color:"",fontStyle:0}]);continue}i.includeExplanation&&(d=t.tokenizeLine(m,u).tokens,p=0);let f=t.tokenizeLine2(m,u,o),b=f.tokens.length/2;for(let e=0;e<b;e++){let t=f.tokens[2*e],s=e+1<b?f.tokens[2*e+2]:m.length;if(t===s)continue;let o=f.tokens[2*e+1],l=nr(r[U.getForeground(o)],a),u=U.getFontStyle(o),h={content:m.substring(t,s),offset:g+t,color:l,fontStyle:u};if(i.includeExplanation){let e=[];if("scopeName"!==i.includeExplanation)for(let t of n.settings){let n;switch(typeof t.scope){case"string":n=t.scope.split(/,/).map(e=>e.trim());break;case"object":n=t.scope;break;default:continue}e.push({settings:t,selectors:n.map(e=>e.split(/ /))})}h.explanation=[];let r=0;for(;t+r<s;){let t=d[p],n=m.substring(t.startIndex,t.endIndex);r+=n.length,h.explanation.push({content:n,scopes:"scopeName"===i.includeExplanation?t.scopes.map(e=>({scopeName:e})):function(e,t){let n=[];for(let r=0,i=t.length;r<i;r++){let i=t[r];n[r]={scopeName:i,themeMatches:function(e,t,n){let r=[];for(let{selectors:i,settings:a}of e)for(let e of i)if(function(e,t,n){if(!nf(e[e.length-1],t))return!1;let r=e.length-2,i=n.length-1;for(;r>=0&&i>=0;)nf(e[r],n[i])&&(r-=1),i-=1;return -1===r}(e,t,n)){r.push(a);break}return r}(e,i,t.slice(0,r))}}return n}(e,t.scopes)}),p+=1}}c.push(h)}h.push(c),c=[],u=f.ruleStack}return{tokens:h,stateStack:u}}function nf(e,t){return e===t||t.substring(0,e.length)===e&&"."===t[e.length]}function nb(e,t,n){let r=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})),i=function(...e){let t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){let i=e.map(e=>e[r]),a=t.map(()=>[]);t.forEach((e,t)=>e.push(a[t]));let s=i.map(()=>0),o=i.map(e=>e[0]);for(;o.every(e=>e);){let e=Math.min(...o.map(e=>e.content.length));for(let t=0;t<n;t++){let n=o[t];n.content.length===e?(a[t].push(n),s[t]+=1,o[t]=i[t][s[t]]):(a[t].push({...n,content:n.content.slice(0,e)}),o[t]={...n,content:n.content.slice(e),offset:n.offset+e})}}}return t}(...r.map(r=>nm(e,t,{...n,theme:r.theme})));return i[0].map((e,t)=>e.map((e,a)=>{let s={content:e.content,variants:{},offset:e.offset};return"includeExplanation"in n&&n.includeExplanation&&(s.explanation=e.explanation),i.forEach((e,n)=>{let{content:i,explanation:o,offset:l,...u}=e[t][a];s.variants[r[n].color]=u}),s}))}function ny(e,t,n){let r,i,a,s,o;if("themes"in n){let{defaultColor:l="light",cssVariablePrefix:u="--shiki-"}=n,c=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})).sort((e,t)=>e.color===l?-1:+(t.color===l));if(0===c.length)throw new ns("`themes` option must not be empty");let h=nb(e,t,n);if(l&&!c.find(e=>e.color===l))throw new ns(`\`themes\` option must contain the defaultColor key \`${l}\``);let d=c.map(t=>e.getTheme(t.theme)),p=c.map(e=>e.color);a=h.map(e=>e.map(e=>(function(e,t,n,r){let i={content:e.content,explanation:e.explanation,offset:e.offset},a=t.map(t=>ni(e.variants[t])),s=new Set(a.flatMap(e=>Object.keys(e))),o=a.reduce((e,i,a)=>{for(let o of s){let s=i[o]||"inherit";if(0===a&&r)e[o]=s;else{let r="color"===o?"":"background-color"===o?"-bg":`-${o}`,i=n+t[a]+("color"===o?"":r);e[o]?e[o]+=`;${i}:${s}`:e[o]=`${i}:${s}`}}return e},{});return i.htmlStyle=r?na(o):Object.values(o).join(";"),i})(e,p,u,l)));let m=c.map(e=>nn(e.theme,n));i=c.map((e,t)=>(0===t&&l?"":`${u+e.color}:`)+(nr(d[t].fg,m[t])||"inherit")).join(";"),r=c.map((e,t)=>(0===t&&l?"":`${u+e.color}-bg:`)+(nr(d[t].bg,m[t])||"inherit")).join(";"),s=`shiki-themes ${d.map(e=>e.name).join(" ")}`,o=l?void 0:[i,r].join(";")}else if("theme"in n){let o=nn(n.theme,n);a=nm(e,t,n);let l=e.getTheme(n.theme);r=nr(l.bg,o),i=nr(l.fg,o),s=l.name}else throw new ns("Invalid options, either `theme` or `themes` must be provided");return{tokens:a,fg:i,bg:r,themeName:s,rootStyle:o}}function nv(e,t,n,r={meta:{},options:n,codeToHast:(t,n)=>nv(e,t,n),codeToTokens:(t,n)=>ny(e,t,n)}){let i=t;for(let e of nl(n))i=e.preprocess?.call(r,i,n)||i;let{tokens:a,fg:s,bg:o,themeName:l,rootStyle:u}=ny(e,i,n),{mergeWhitespaces:c=!0}=n;!0===c?a=a.map(e=>{let t=[],n="",r=0;return e.forEach((i,a)=>{let s=!(i.fontStyle&&i.fontStyle&j.Underline);s&&i.content.match(/^\s+$/)&&e[a+1]?(r||(r=i.offset),n+=i.content):n?(s?t.push({...i,offset:r,content:n+i.content}):t.push({content:n,offset:r},i),r=0,n=""):t.push(i)}),t}):"never"===c&&(a=a.map(e=>e.flatMap(e=>{if(e.content.match(/^\s+$/))return e;let t=e.content.match(/^(\s*)(.*?)(\s*)$/);if(!t)return e;let[,n,r,i]=t;if(!n&&!i)return e;let a=[{...e,offset:e.offset+n.length,content:r}];return n&&a.unshift({content:n,offset:e.offset}),i&&a.push({content:i,offset:e.offset+n.length+r.length}),a})));let h={...r,get source(){return i}};for(let e of nl(n))a=e.tokens?.call(h,a)||a;return function(e,t,n){let r=nl(t),i=[],a={type:"root",children:[]},{structure:s="classic",tabindex:o="0"}=t,l={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...!1!==o&&null!=o?{tabindex:o.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([e])=>!e.startsWith("_")))},children:[]},u={type:"element",tagName:"code",properties:{},children:i},c=[],h={...n,structure:s,addClassToHast:ne,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return a},get pre(){return l},get code(){return u},get lines(){return c}};if(e.forEach((e,t)=>{t&&("inline"===s?a.children.push({type:"element",tagName:"br",properties:{},children:[]}):"classic"===s&&i.push({type:"text",value:"\n"}));let n={type:"element",tagName:"span",properties:{class:"line"},children:[]},o=0;for(let i of e){let e={type:"element",tagName:"span",properties:{},children:[{type:"text",value:i.content}]},l=i.htmlStyle||na(ni(i));for(let i of(l&&(e.properties.style=l),r))e=i?.span?.call(h,e,t+1,o,n)||e;"inline"===s?a.children.push(e):"classic"===s&&n.children.push(e),o+=i.content.length}if("classic"===s){for(let e of r)n=e?.line?.call(h,n,t+1)||n;c.push(n),i.push(n)}}),"classic"===s){for(let e of r)u=e?.code?.call(h,u)||u;for(let e of(l.children.push(u),r))l=e?.pre?.call(h,l)||l;a.children.push(l)}let d=a;for(let e of r)d=e?.root?.call(h,d)||d;return d}(a,{...n,fg:s,bg:o,themeName:l,rootStyle:u},h)}let n_={light:"#333333",dark:"#bbbbbb"},nk={light:"#fffffe",dark:"#1e1e1e"},nw="__shiki_resolved";function nx(e){if(e?.[nw])return e;let t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||(t.type="dark"),t.colorReplacements={...t.colorReplacements},t.settings||(t.settings=[]);let{bg:n,fg:r}=t;if(!n||!r){let e=t.settings?t.settings.find(e=>!e.name&&!e.scope):void 0;e?.settings?.foreground&&(r=e.settings.foreground),e?.settings?.background&&(n=e.settings.background),!r&&t?.colors?.["editor.foreground"]&&(r=t.colors["editor.foreground"]),!n&&t?.colors?.["editor.background"]&&(n=t.colors["editor.background"]),r||(r="light"===t.type?n_.light:n_.dark),n||(n="light"===t.type?nk.light:nk.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let i=0,a=new Map;function s(e){if(a.has(e))return a.get(e);i+=1;let n=`#${i.toString(16).padStart(8,"0").toLowerCase()}`;return t.colorReplacements?.[`#${n}`]?s(e):(a.set(e,n),n)}for(let e of(t.settings=t.settings.map(e=>{let n=e.settings?.foreground&&!e.settings.foreground.startsWith("#"),r=e.settings?.background&&!e.settings.background.startsWith("#");if(!n&&!r)return e;let i={...e,settings:{...e.settings}};if(n){let n=s(e.settings.foreground);t.colorReplacements[n]=e.settings.foreground,i.settings.foreground=n}if(r){let n=s(e.settings.background);t.colorReplacements[n]=e.settings.background,i.settings.background=n}return i}),Object.keys(t.colors||{})))if(("editor.foreground"===e||"editor.background"===e||e.startsWith("terminal.ansi"))&&!t.colors[e]?.startsWith("#")){let n=s(t.colors[e]);t.colorReplacements[n]=t.colors[e],t.colors[e]=n}return Object.defineProperty(t,nw,{enumerable:!1,writable:!1,value:!0}),t}async function nS(e){return Array.from(new Set((await Promise.all(e.filter(e=>!t7(e)).map(async e=>await nt(e).then(e=>Array.isArray(e)?e:[e])))).flat()))}async function nC(e){return(await Promise.all(e.map(async e=>t9(e)?null:nx(await nt(e))))).filter(e=>!!e)}var nP=Object.defineProperty,nN=(e,t,n)=>t in e?nP(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,nA=(e,t,n)=>(nN(e,"symbol"!=typeof t?t+"":t,n),n);class nL extends eI{constructor(e,t,n,r={}){super(e),this._resolver=e,this._themes=t,this._langs=n,this._alias=r,nA(this,"_resolvedThemes",new Map),nA(this,"_resolvedGrammars",new Map),nA(this,"_langMap",new Map),nA(this,"_langGraph",new Map),nA(this,"_textmateThemeCache",new WeakMap),nA(this,"_loadedThemesCache",null),nA(this,"_loadedLanguagesCache",null),this._themes.map(e=>this.loadTheme(e)),this.loadLanguages(this._langs)}getTheme(e){return"string"==typeof e?this._resolvedThemes.get(e):this.loadTheme(e)}loadTheme(e){let t=nx(e);return t.name&&(this._resolvedThemes.set(t.name,t),this._loadedThemesCache=null),t}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(e){let t=this._textmateThemeCache.get(e);t||(t=E.createFromRawTheme(e),this._textmateThemeCache.set(e,t)),this._syncRegistry.setTheme(t)}getGrammar(e){if(this._alias[e]){let t=new Set([e]);for(;this._alias[e];){if(e=this._alias[e],t.has(e))throw new ns(`Circular alias \`${Array.from(t).join(" -> ")} -> ${e}\``);t.add(e)}}return this._resolvedGrammars.get(e)}loadLanguage(e){if(this.getGrammar(e.name))return;let t=new Set([...this._langMap.values()].filter(t=>t.embeddedLangsLazy?.includes(e.name)));this._resolver.addLanguage(e);let n={balancedBracketSelectors:e.balancedBracketSelectors||["*"],unbalancedBracketSelectors:e.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(e.scopeName,e);let r=this.loadGrammarWithConfiguration(e.scopeName,1,n);if(r.name=e.name,this._resolvedGrammars.set(e.name,r),e.aliases&&e.aliases.forEach(t=>{this._alias[t]=e.name}),this._loadedLanguagesCache=null,t.size)for(let e of t)this._resolvedGrammars.delete(e.name),this._loadedLanguagesCache=null,this._syncRegistry?._injectionGrammars?.delete(e.scopeName),this._syncRegistry?._grammars?.delete(e.scopeName),this.loadLanguage(this._langMap.get(e.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(e){for(let t of e)this.resolveEmbeddedLanguages(t);let t=Array.from(this._langGraph.entries()),n=t.filter(([e,t])=>!t);if(n.length){let e=t.filter(([e,t])=>t&&t.embeddedLangs?.some(e=>n.map(([e])=>e).includes(e))).filter(e=>!n.includes(e));throw new ns(`Missing languages ${n.map(([e])=>`\`${e}\``).join(", ")}, required by ${e.map(([e])=>`\`${e}\``).join(", ")}`)}for(let[e,n]of t)this._resolver.addLanguage(n);for(let[e,n]of t)this.loadLanguage(n)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(e){if(this._langMap.set(e.name,e),this._langGraph.set(e.name,e),e.embeddedLangs)for(let t of e.embeddedLangs)this._langGraph.set(t,this._langMap.get(t))}}var nT=Object.defineProperty,nE=(e,t,n)=>t in e?nT(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,nR=(e,t,n)=>(nE(e,"symbol"!=typeof t?t+"":t,n),n);class nO{constructor(e,t){nR(this,"_langs",new Map),nR(this,"_scopeToLang",new Map),nR(this,"_injections",new Map),nR(this,"_onigLib"),this._onigLib={createOnigScanner:t=>e.createScanner(t),createOnigString:t=>e.createString(t)},t.forEach(e=>this.addLanguage(e))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){let t=e.split("."),n=[];for(let e=1;e<=t.length;e++){let r=t.slice(0,e).join(".");n=[...n,...this._injections.get(r)||[]]}return n}}let nI=0;async function nj(e={}){let[t,n,r]=await Promise.all([nC(e.themes||[]),nS(e.langs||[]),e.engine||t8(e.loadWasm||a)]);return function(e){let t;nI+=1,!1!==e.warnings&&nI>=10&&nI%10==0&&console.warn(`[Shiki] ${nI} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let n=!1;if(!e.engine)throw new ns("`engine` option is required for synchronous mode");let r=(e.langs||[]).flat(1),i=(e.themes||[]).flat(1).map(nx),a=new nL(new nO(e.engine,r),i,r,e.langAlias);function s(e){if("none"===e)return{bg:"",fg:"",name:"none",settings:[],type:"dark"};u();let t=a.getTheme(e);if(!t)throw new ns(`Theme \`${e}\` not found, you may need to load it first`);return t}function o(...e){u(),a.loadLanguages(e.flat(1))}function l(...e){for(let t of(u(),e.flat(1)))a.loadTheme(t)}function u(){if(n)throw new ns("Shiki instance has been disposed")}function c(){n||(n=!0,a.dispose(),nI-=1)}return{setTheme:function(e){u();let n=s(e);return t!==e&&(a.setTheme(n),t=e),{theme:n,colorMap:a.getColorMap()}},getTheme:s,getLanguage:function(e){u();let t=a.getGrammar("string"==typeof e?e:e.name);if(!t)throw new ns(`Language \`${e}\` not found, you may need to load it first`);return t},getLoadedThemes:function(){return u(),a.getLoadedThemes()},getLoadedLanguages:function(){return u(),a.getLoadedLanguages()},loadLanguage:async function(...e){return o(await nS(e))},loadLanguageSync:o,loadTheme:async function(...e){return u(),l(await nC(e))},loadThemeSync:l,dispose:c,[Symbol.dispose]:c}}({...e,loadWasm:void 0,themes:t,langs:n,engine:r})}async function nM(e={}){let t=await nj(e);return{getLastGrammarState:(e,n)=>(function(e,t,n={}){let{lang:r="text",theme:i=e.getLoadedThemes()[0]}=n;if(t4(r)||t9(i))throw new w("Plain language does not have grammar state");if("ansi"===r)throw new w("ANSI language does not have grammar state");let{theme:a,colorMap:s}=e.setTheme(i),o=e.getLanguage(r);return new nu(ng(t,o,a,s,n).stateStack,o.name,a.name)})(t,e,n),codeToTokensBase:(e,n)=>nm(t,e,n),codeToTokensWithThemes:(e,n)=>nb(t,e,n),codeToTokens:(e,n)=>ny(t,e,n),codeToHast:(e,n)=>nv(t,e,n),codeToHtml:(e,n)=>(function(e,t,n){let r={meta:{},options:n,codeToHast:(t,n)=>nv(e,t,n),codeToTokens:(t,n)=>ny(e,t,n)},i=function(e,t){let n=tW.quote||'"';if('"'!==n&&"'"!==n)throw Error("Invalid quote `"+n+"`, expected `'` or `\"`");return({one:tJ,all:tK,settings:{omitOptionalTags:tW.omitOptionalTags||!1,allowParseErrors:tW.allowParseErrors||!1,allowDangerousCharacters:tW.allowDangerousCharacters||!1,quoteSmart:tW.quoteSmart||!1,preferUnquoted:tW.preferUnquoted||!1,tightAttributes:tW.tightAttributes||!1,upperDoctype:tW.upperDoctype||!1,tightDoctype:tW.tightDoctype||!1,bogusComments:tW.bogusComments||!1,tightCommaSeparatedLists:tW.tightCommaSeparatedLists||!1,tightSelfClosing:tW.tightSelfClosing||!1,collapseEmptyAttributes:tW.collapseEmptyAttributes||!1,allowDangerousHtml:tW.allowDangerousHtml||!1,voids:tW.voids||eM,characterReferences:tW.characterReferences||tz,closeSelfClosing:tW.closeSelfClosing||!1,closeEmptyElements:tW.closeEmptyElements||!1},schema:"svg"===tW.space?e7:e4,quote:n,alternative:'"'===n?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}(nv(e,t,n,r));for(let e of nl(n))i=e.postprocess?.call(r,i,n)||i;return i})(t,e,n),...t,getInternalContext:()=>t}}let{codeToHast:nD}=function(e){let t,n=async function(n={}){if(!t)return t=e({...n,themes:n.themes||[],langs:n.langs||[]});{let e=await t;return await Promise.all([e.loadTheme(...n.themes||[]),e.loadLanguage(...n.langs||[])]),e}};return{getSingletonHighlighter:e=>n(e),codeToHtml:async(e,t)=>(await n({langs:[t.lang],themes:"theme"in t?[t.theme]:Object.values(t.themes)})).codeToHtml(e,t),codeToHast:async(e,t)=>(await n({langs:[t.lang],themes:"theme"in t?[t.theme]:Object.values(t.themes)})).codeToHast(e,t),codeToTokens:async(e,t)=>(await n({langs:[t.lang],themes:"theme"in t?[t.theme]:Object.values(t.themes)})).codeToTokens(e,t),codeToTokensBase:async(e,t)=>(await n({langs:[t.lang],themes:[t.theme]})).codeToTokensBase(e,t),codeToTokensWithThemes:async(e,t)=>(await n({langs:[t.lang],themes:Object.values(t.themes).filter(Boolean)})).codeToTokensWithThemes(e,t),getLastGrammarState:async(e,t)=>(await n({langs:[t.lang],themes:[t.theme]})).getLastGrammarState(e,t)}}(function(e,t,n){let r,i,a;return r=e.langs,i=e.themes,a=e.engine,async function(e){function t(e){if("string"==typeof e){if(t7(e))return[];let t=r[e];if(!t)throw new w(`Language \`${e}\` is not included in this bundle. You may want to load it from external source.`);return t}return e}function n(e){if(t9(e))return"none";if("string"==typeof e){let t=i[e];if(!t)throw new w(`Theme \`${e}\` is not included in this bundle. You may want to load it from external source.`);return t}return e}let s=(e.themes??[]).map(e=>n(e)),o=(e.langs??[]).map(e=>t(e)),l=await nM({engine:a(),...e,themes:s,langs:o});return{...l,loadLanguage:(...e)=>l.loadLanguage(...e.map(t)),loadTheme:(...e)=>l.loadTheme(...e.map(n))}}}({langs:v,themes:k,engine:()=>t8(_)}));function nB(e,t,n,r=!1){return{name:e,code(e){let i=e.children.filter(e=>"element"===e.type),a=[];for(let s of(i.forEach((s,o)=>{let l;for(let e of s.children){if("element"!==e.type)continue;let r=e.children[0];if("text"!==r.type)continue;let a=!1;r.value=r.value.replace(t,(...t)=>n.call(this,t,s,e,i,o)?(a=!0,""):t[0]),a&&!r.value.trim()&&(l=e)}if(l&&(s.children.splice(s.children.indexOf(l),1),0===s.children.length)&&(a.push(s),r)){let t=e.children[e.children.indexOf(s)+1];t&&"text"===t.type&&"\n"===t.value&&a.push(t)}}),a))e.children.splice(e.children.indexOf(s),1)}}}function nG(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function nU(e={},t="@shikijs/transformers:notation-map"){let{classMap:n={},classActivePre:r}=e;return nB(t,RegExp(`\\s*(?://|/\\*|<!--|#|--|%{1,2}|;{1,2}|"|')\\s+\\[!code (${Object.keys(n).map(nG).join("|")})(:\\d+)?\\]\\s*(?:\\*/|-->)?\\s*$`),function([e,t,i=":1"],a,s,o,l){let u=Number.parseInt(i.slice(1),10);return o.slice(l,l+u).forEach(e=>{this.addClassToHast(e,n[t])}),r&&this.addClassToHast(this.pre,r),!0})}function n$(e,t,n,r){let i=function e(t){return"text"===t.type?t.value:"element"===t.type&&"span"===t.tagName?t.children.map(e).join(""):""}(e),a=i.indexOf(n);for(;-1!==a;)nF.call(this,e.children,t,a,n.length,r),a=i.indexOf(n,a+1)}function nF(e,t,n,r,i){let a=0;for(let l=0;l<e.length;l++){let u=e[l];if("element"!==u.type||"span"!==u.tagName||u===t)continue;let c=u.children[0];if("text"===c.type){var s,o;if(s=[a,a+c.value.length-1],s[0]<=(o=[n,n+r])[1]&&s[1]>=o[0]){let t=Math.max(0,n-a),s=r-Math.max(0,a-n);if(0===s)continue;let o=function(e,t,n,r){let i=t.value,a=t=>{var n,r;return n=e,r={children:[{type:"text",value:t}]},{...n,properties:{...n.properties},...r}};return[n>0?a(i.slice(0,n)):void 0,a(i.slice(n,n+r)),n+r<i.length?a(i.slice(n+r)):void 0]}(u,c,t,s);this.addClassToHast(o[1],i);let h=o.filter(Boolean);e.splice(l,1,...h),l+=h.length-1}a+=c.value.length}}}Symbol("highlighted-lines");let nq=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,nH=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,nW={};function nz(e,t){return((t||nW).jsx?nH:nq).test(e)}let nV={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var nJ=n(80427);nX("end");let nK=nX("start");function nX(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function nY(e){return nZ(e&&e.line)+":"+nZ(e&&e.column)}function nQ(e){return nY(e&&e.start)+"-"+nY(e&&e.end)}function nZ(e){return e&&"number"==typeof e?e:1}class n0 extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},a=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(a=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let s=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=s?s.line:void 0,this.name=function(e){return e&&"object"==typeof e?"position"in e||"type"in e?nQ(e.position):"start"in e||"end"in e?nQ(e):"line"in e||"column"in e?nY(e):"":""}(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=a&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}n0.prototype.file="",n0.prototype.name="",n0.prototype.reason="",n0.prototype.message="",n0.prototype.stack="",n0.prototype.column=void 0,n0.prototype.line=void 0,n0.prototype.ancestors=void 0,n0.prototype.cause=void 0,n0.prototype.fatal=void 0,n0.prototype.place=void 0,n0.prototype.ruleId=void 0,n0.prototype.source=void 0;let n1={}.hasOwnProperty,n3=new Map,n6=/[A-Z]/g,n2=new Set(["table","tbody","thead","tfoot","tr"]),n8=new Set(["td","th"]),n5="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function n4(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=e7),e.ancestors.push(t);let a=rt(e,t.tagName,!1),s=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&n1.call(t.properties,r)){let a=function(e,t,n){let r=tx(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?tv(n):tP(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return nJ(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new n0("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=n5+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)n1.call(e,t)&&(n[function(e){let t=e.replace(n6,rr);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?nV[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(a){let[r,s]=a;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof s&&n8.has(t.tagName)?n=s:i[r]=s}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),o=re(e,t);return n2.has(t.tagName)&&(o=o.filter(function(e){return"string"!=typeof e||!tA(e)})),n7(e,s,a,t),n9(s,o),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}rn(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(e.schema=e7),e.ancestors.push(t);let a=null===t.name?e.Fragment:rt(e,t.name,!0),s=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let i=t.expression;i.type;let a=i.properties[0];a.type,Object.assign(n,e.evaluater.evaluateExpression(a.argument))}else rn(e,t.position);else{let i,a=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else rn(e,t.position);else i=null===r.value||r.value;n[a]=i}return n}(e,t),o=re(e,t);return n7(e,s,a,t),n9(s,o),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);rn(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return n9(r,re(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function n7(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function n9(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function re(e,t){let n=[],r=-1,i=e.passKeys?new Map:n3;for(;++r<t.children.length;){let a,s=t.children[r];if(e.passKeys){let e="element"===s.type?s.tagName:"mdxJsxFlowElement"===s.type||"mdxJsxTextElement"===s.type?s.name:void 0;if(e){let t=i.get(e)||0;a=e+"-"+t,i.set(e,t+1)}}let o=n4(e,s,a);void 0!==o&&n.push(o)}return n}function rt(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),i=-1;for(;++i<n.length;){let t=nz(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=nz(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return n1.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);rn(e)}function rn(e,t){let n=new n0("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=n5+"#cannot-handle-mdx-estrees-without-createevaluater",n}function rr(e){return"-"+e.toLowerCase()}var ri={Fragment:o.Fragment,jsx:o.jsx,jsxs:o.jsxs},ra=e=>(0,m.BU)(void 0,[e],function*({id:e,children:t,lang:n,theme:r,components:i,extraTransformers:a,startHidden:s,groupId:l,lineNumbers:u}){var c;let h=function(e,t){var n,r,i,a,s;let o;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let l=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=l,r=t.jsxDEV,o=function(e,t,i,a){let s=Array.isArray(i.children),o=nK(e);return r(t,i,a,s,{columnNumber:o?o.column-1:void 0,fileName:n,lineNumber:o?o.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,a=t.jsx,s=t.jsxs,o=function(e,t,n,r){let i=Array.isArray(n.children)?s:a;return r?i(t,n,r):i(t,n)}}let u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:o,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:l,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?e7:e4,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=n4(u,e,void 0);return c&&"string"!=typeof c?c:u.create(e,u.Fragment,{children:c||void 0},void 0)}((yield nD(t,{lang:n,theme:r,transformers:[function(e={}){let{classLineAdd:t="diff add",classLineRemove:n="diff remove",classActivePre:r="has-diff"}=e;return nU({classMap:{"++":t,"--":n},classActivePre:r},"@shikijs/transformers:notation-diff")}(),function(e={}){let{classMap:t={error:["highlighted","error"],warning:["highlighted","warning"]},classActivePre:n="has-highlighted"}=e;return nU({classMap:t,classActivePre:n},"@shikijs/transformers:notation-error-level")}(),function(e={}){let{classActiveLine:t="highlighted",classActivePre:n="has-highlighted"}=e;return nU({classMap:{highlight:t,hl:t},classActivePre:n},"@shikijs/transformers:notation-highlight")}(),function(e={}){let{classActiveWord:t="highlighted-word",classActivePre:n}=e;return nB("@shikijs/transformers:notation-highlight-word",/^\s*(?:\/\/|\/\*|<!--|#)\s+\[!code word:((?:\\.|[^:\]])+)(:\d+)?\]\s*(?:\*\/|-->)?/,function([e,r,i],a,s,o,l){let u=i?Number.parseInt(i.slice(1),10):o.length;return r=r.replace(/\\(.)/g,"$1"),o.slice(l+1,l+1+u).forEach(e=>n$.call(this,e,s,r,t)),n&&this.addClassToHast(this.pre,n),!0},!0)}(),...u?[{line(e,t){e.children=[{type:"element",tagName:"span",properties:{class:!0===u?"line-number":u.className,style:"user-select: none; pointer-events: none; text-align: right; display: inline-block;"},children:[{type:"text",value:t.toString()}]},...e.children]}}]:[],...null!=a?a:[]]})),(0,m.ko)((0,m.IA)({},ri),{components:i})),d=null!=(c=null==i?void 0:i.div)?c:"div";return(0,o.jsx)(d,{"data-snippet-group-id":l,"data-snippet-id":e,style:{display:s?"none":"block"},children:h})}),rs=(0,g.lazy)(()=>Promise.resolve().then(n.bind(n,22311))),ro=({snippets:e,theme:t,childrenTop:n,childrenBottom:r,components:i,disableLocalStorageSelection:a,extraTransformers:s,lineNumbers:l})=>{let u=(0,g.useId)(),c=e.map((e,t)=>(0,m.ko)((0,m.IA)({},e),{id:u+"-snippet-"+t}));return(0,o.jsxs)(rs,{snippets:c.map(e=>({id:e.id,language:e.language,label:e.label})),storeSnippetSelection:!a,groupId:u,children:[n,c.map((e,n)=>(0,o.jsx)(ra,{lang:rl(e.language),theme:t,id:e.id,components:i,startHidden:n>0,groupId:u,extraTransformers:s,lineNumbers:l,children:e.code},e.id)),r]})},rl=e=>"plainText"===e?"text":e,ru=n(17218),rc=n(6043),rh=n(34962),rd=n(64018),rp=n(56450),rm=n(49499),rg=n.n(rm),rf=n(62923);let rb=c._.VERCEL_PROJECT_PRODUCTION_URL?.startsWith("https")?"https":"http",ry=new URL(`${rb}://${c._.VERCEL_PROJECT_PRODUCTION_URL}`),rv=async({params:e})=>{let{slug:t}=await e,n=await d.h.getPost(t);return n?(0,rp.w)({title:n._title,description:n.description,image:n.image.url}):{}},r_=async()=>(await d.h.getPosts()).map(({_slug:e})=>({slug:e})),rk=async function([e]){let t=e.blog.posts.item;return t||(0,rf.notFound)(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(rd.Z,{code:{"@type":"BlogPosting","@context":"https://schema.org",datePublished:t.date,description:t.description,mainEntityOfPage:{"@type":"WebPage","@id":new URL(`/blog/${t._slug}`,ry).toString()},headline:t._title,image:t.image.url,dateModified:t.date,author:t.authors.at(0)?._title,isAccessibleForFree:!0}}),(0,o.jsxs)("div",{className:"container mx-auto px-8 sm:px-12 lg:px-16 xl:px-24 pt-32 pb-16",style:{paddingTop:"60px"},children:[(0,o.jsxs)(rg(),{className:"mb-4 inline-flex items-center gap-1 text-muted-foreground text-sm focus:underline focus:outline-none",href:"/blog",children:[(0,o.jsx)(h.A60,{className:"h-4 w-4"}),"Back to Blog"]}),(0,o.jsxs)("div",{className:"mt-16 flex flex-col items-start gap-8 sm:flex-row",children:[(0,o.jsx)("div",{className:"sm:flex-1",children:(0,o.jsxs)("div",{className:"prose prose-neutral dark:prose-invert max-w-none",children:[(0,o.jsx)("h1",{className:"scroll-m-20 text-balance font-extrabold text-4xl tracking-tight lg:text-5xl",children:t._title}),(0,o.jsx)("p",{className:"text-balance leading-7 [&:not(:first-child)]:mt-6",children:t.description}),t.image?(0,o.jsx)(rc.BaseHubImage,{src:t.image.url,width:t.image.width,height:t.image.height,alt:t.image.alt??"",className:"my-16 h-full w-full rounded-xl",priority:!0}):void 0,(0,o.jsx)("div",{className:"mx-auto max-w-prose",children:(0,o.jsx)(p.n,{content:t.body.json.content,components:{pre:({code:e,language:t})=>(0,o.jsx)(ro,{theme:"vesper",snippets:[{code:e,language:t}]})}})})]})}),(0,o.jsx)("div",{className:"sticky top-24 hidden shrink-0 md:block",children:(0,o.jsx)(u.B,{toc:(0,o.jsx)(rh.M,{data:t.body.json.toc}),readingTime:`${t.body.readingTime} min read`,date:new Date(t.date)})})]})]})]})},rw=async({params:e})=>{let{slug:t}=await e;return(0,o.jsx)(ru.J,{queries:[d.h.postQuery(t)],children:(0,l.A)(rk,"408e024b02e769f94907fb0f8bac7b2c67cf980e8b",null)})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3835:(e,t,n)=>{"use strict";n.d(t,{B:()=>a});var r=n(94752),i=n(48384);let a=async({date:e,readingTime:t,tags:n,toc:a})=>(0,r.jsxs)("div",{className:"col-span-4 flex w-72 flex-col items-start gap-8 border-foreground/10 border-l px-6 lg:col-span-2",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Published"}),(0,r.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric",timeZone:"America/New_York"}).format(e)})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Reading Time"}),(0,r.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:t})]}),n&&(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Tags"}),(0,r.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:n.map(i.Z).join(", ")})]}),a?(0,r.jsx)("div",{className:"-mx-2",children:(0,r.jsxs)("div",{className:"grid gap-2 p-2",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Sections"}),a]})}):void 0]})},6043:(e,t,n)=>{"use strict";n.r(t),n.d(t,{BaseHubImage:()=>i,basehubImageLoader:()=>a});var r=n(6340);let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call BaseHubImage() from the server but BaseHubImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\node_modules\\basehub\\dist\\next-image.js","BaseHubImage"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call basehubImageLoader() from the server but basehubImageLoader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\node_modules\\basehub\\dist\\next-image.js","basehubImageLoader")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22311:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\node_modules\\\\.pnpm\\\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\\\node_modules\\\\basehub\\\\dist\\\\client-BDEKBT54.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\node_modules\\basehub\\dist\\client-BDEKBT54.js","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34497:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>d,tree:()=>u});var r=n(57864),i=n(94327),a=n(73391),s=n.n(a),o=n(17984),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let u={children:["",{children:["[locale]",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,2357)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\blog\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(n.bind(n,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(n.bind(n,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(n.bind(n,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(n.t.bind(n,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\blog\\[slug]\\page.tsx"],h={require:n,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/blog/[slug]/page",pathname:"/[locale]/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},34962:(e,t,n)=>{"use strict";n.d(t,{M:()=>a});var r=n(94752),i=n(14251);let a=({data:e,...t})=>(0,r.jsx)("div",{children:(0,r.jsx)(i.sD,{components:{ol:({children:e})=>(0,r.jsx)("ol",{className:"flex list-none flex-col gap-2 text-sm",children:e}),ul:({children:e})=>(0,r.jsx)("ul",{className:"flex list-none flex-col gap-2 text-sm",children:e}),li:({children:e})=>(0,r.jsx)("li",{className:"pl-3",children:e}),a:({children:e,href:t})=>(0,r.jsx)("a",{className:"line-clamp-3 flex rounded-sm text-foreground text-sm underline decoration-foreground/0 transition-colors hover:decoration-foreground/50",href:`#${t?.split("#").at(1)}`,children:e})},...t,children:e})})},36028:(e,t)=>{},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56450:(e,t,n)=>{"use strict";n.d(t,{w:()=>l});var r=n(81121),i=n.n(r);let a="next-forge",s={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:n,...r})=>{let l=`${e} | ${a}`,u={title:l,description:t,applicationName:a,metadataBase:o?new URL(`https://${o}`):void 0,authors:[s],creator:s.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:a,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=i()(u,r);return n&&c.openGraph&&(c.openGraph.images=[{url:n,width:1200,height:630,alt:e}]),c}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58181:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,l=/^\s+|\s+$/g;function u(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var c=1,h=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function p(){var e={line:c,column:h};return function(t){return t.position=new m(e),b(r),t}}function m(e){this.start=e,this.end={line:c,column:h},this.source=l.source}m.prototype.content=e;var g=[];function f(t){var n=Error(l.source+":"+c+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=c,n.column=h,n.source=e,l.silent)g.push(n);else throw n}function b(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function y(e){var t;for(e=e||[];t=v();)!1!==t&&e.push(t);return e}function v(){var t=p();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return f("End of comment missing");var r=e.slice(2,n-2);return h+=2,d(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}b(r);var _,k=[];for(y(k);_=function(){var e=p(),n=b(i);if(n){if(v(),!b(a))return f("property missing ':'");var r=b(s),l=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return b(o),l}}();)!1!==_&&(k.push(_),y(k));return k}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64018:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(94752);n(36028);let i=({code:e})=>(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}})},71271:(e,t,n)=>{"use strict";n.r(t),n.d(t,{"00a93fb4bd742ae20ac4f2efc682a9dd8b873c54ce":()=>r.z2,"408e024b02e769f94907fb0f8bac7b2c67cf980e8b":()=>a.$$RSC_SERVER_ACTION_0,"40f08c3d29a2f32c36fa37e5a9ba2c32897f96adb3":()=>i.x,"600ae2011570e2df1d46454ad223098fc4baa55559":()=>r.qr,"60800baff73db42f3be2da01face57da531e4ef986":()=>r.q2,"608b2f8eca36791b674ce9740d60d899091a017080":()=>r.xK});var r=n(22589),i=n(18362),a=n(2357)},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78255:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});var r=n(35371),i=r.createContext(void 0),a=n(99730),s=({children:e,snippets:t,storeSnippetSelection:n,groupId:s})=>{let o=1===t.length,[l,u]=r.useState(t[0]);r.useEffect(()=>{document.querySelectorAll(`[data-snippet-group-id="${s}"]`).forEach(e=>{e.getAttribute("data-snippet-id")===(null==l?void 0:l.id)?(e.style.display="block",e.setAttribute("data-active","true")):(e.style.display="none",e.setAttribute("data-active","false"))})},[l,s]);let c=!o&&n?`__bshb-active-snippet-for-${t.map(e=>e.label||e.id).sort((e,t)=>e.localeCompare(t)).join("-")}`:null;r.useEffect(()=>{var e;if(c){try{let n=null==(e=window.localStorage)?void 0:e.getItem(c);if(n){let e=t.find(e=>e.label===n||e.id===n);e&&u(e)}}catch(e){}return window.addEventListener("__bshb-snippet-change",n),()=>{window.removeEventListener("__bshb-snippet-change",n)}}function n(e){if(e.detail.key!==c)return;let n=t.find(t=>t.label===e.detail.snippet.label||t.id===e.detail.snippet.id);n&&u(n)}},[c,t]);let h=r.useCallback(e=>{var t;if(u(e),!c)return;try{null==(t=window.localStorage)||t.setItem(c,e.label||e.id)}catch(e){}let n=new CustomEvent("__bshb-snippet-change",{detail:{key:c,snippet:e}});window.dispatchEvent(n)},[c]);return(0,a.jsx)(i.Provider,{value:{snippets:t,activeSnippet:l,selectSnippet:h,groupId:s},children:e})}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80427:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(1371)),i=n(96699);function a(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}a.default=a,e.exports=a},80481:e=>{"use strict";e.exports=require("node:readline")},83183:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,21034,23)),Promise.resolve().then(n.bind(n,22311)),Promise.resolve().then(n.bind(n,6043)),Promise.resolve().then(n.t.bind(n,49499,23)),Promise.resolve().then(n.bind(n,93665)),Promise.resolve().then(n.bind(n,40356))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91431:(e,t,n)=>{Promise.resolve().then(n.bind(n,86332)),Promise.resolve().then(n.bind(n,78255)),Promise.resolve().then(n.bind(n,96860)),Promise.resolve().then(n.t.bind(n,41265,23)),Promise.resolve().then(n.bind(n,22683)),Promise.resolve().then(n.bind(n,38327))},94735:e=>{"use strict";e.exports=require("events")},96699:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,o=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||i.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(a,l)).replace(r,o))}},96860:(e,t,n)=>{"use strict";n.r(t),n.d(t,{BaseHubImage:()=>y,basehubImageLoader:()=>b});var r=Object.defineProperty,i=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,u=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,c=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&u(e,n,t[n]);if(s)for(var n of s(t))l.call(t,n)&&u(e,n,t[n]);return e},h=(e,t)=>i(e,a(t)),d=n(48473),p=n(35371),m=n(99730),g="https://basehub.earth",f="https://assets.basehub.com",b=({src:e,width:t,quality:n})=>{let r;try{r=new URL(e)}catch(t){throw Error(`Invalid BaseHub Image URL: ${e}

Expected origin to be one of:
- ${g} (deprecated)
- ${f}
`)}let i=[`width=${t}`,`quality=${n||90}`];if(r.href.includes(g))if(r.pathname.startsWith("/cdn-cgi/image/")){let[e,t,n,a="",...s]=r.pathname.split("/"),o=[...a.split(",").filter(e=>!e.startsWith("width=")&&!e.startsWith("quality=")&&!e.startsWith("w=")&&!e.startsWith("q=")&&!e.startsWith("h=")&&!e.startsWith("height=")),...i].join(",");!1===o.includes("format=")&&(o+=",format=auto"),r.pathname=`/cdn-cgi/image/${o}/${s.join("/")}`}else i.push("format=auto"),r.pathname=`/cdn-cgi/image/${i.join(",")}${r.pathname}`;else r.href.includes(f)&&(i.forEach(e=>{let[t,n]=e.split("=");t&&n&&r.searchParams.set(t,n)}),!1===r.searchParams.has("format")&&r.searchParams.set("format","auto"),r.searchParams.delete("height"),r.searchParams.delete("h"));let a=new URL(f);if(r.href.includes(g))if(r.pathname.startsWith("/cdn-cgi/image/")){let[e,t,n,i="",...s]=r.pathname.split("/");a.pathname=s.join("/"),a.search=i.split(",").join("&")}else a.pathname=r.pathname,a.search=r.search;else{if(!r.href.includes(f))return e;a.pathname=r.pathname,a.search=r.search}return a.toString()},y=(0,p.forwardRef)((e,t)=>{var n,r,i;let a=null!=(i=null!=(r=e.unoptimized)?r:null==(n=e.src.toString().split("?")[0])?void 0:n.endsWith(".svg"))?i:void 0;return(0,m.jsx)(d.default,h(c({},e),{placeholder:e.placeholder,loader:b,unoptimized:a,ref:t}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[5319,3396,415,2644,365,1121,5954,9752,6270],()=>n(34497));module.exports=r})();