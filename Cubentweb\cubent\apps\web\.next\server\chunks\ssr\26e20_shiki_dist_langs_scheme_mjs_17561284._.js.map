{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/scheme.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Scheme\", \"fileTypes\": [\"scm\", \"ss\", \"sch\", \"rkt\"], \"name\": \"scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#block-comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#string\" }, { \"include\": \"#language-functions\" }, { \"include\": \"#quote\" }, { \"include\": \"#illegal\" }], \"repository\": { \"block-comment\": { \"begin\": \"\\\\#\\\\|\", \"contentName\": \"comment\", \"end\": \"\\\\|\\\\#\", \"name\": \"comment\", \"patterns\": [{ \"include\": \"#block-comment\", \"name\": \"comment\" }] }, \"comment\": { \"begin\": \"(^[ \\\\t]+)?(?=;)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.scheme\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \";\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scheme\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.semicolon.scheme\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"#[t|f]\", \"name\": \"constant.language.boolean.scheme\" }, { \"match\": `(?<=[(\\\\s])((#e|#i)?\\\\d+(\\\\.\\\\d+)?|(#x)[0-9a-fA-F]+|(#o)[0-7]+|(#b)[01]+)(?=[\\\\s;()'\",\\\\[\\\\]])`, \"name\": \"constant.numeric.scheme\" }] }, \"illegal\": { \"match\": \"[()\\\\[\\\\]]\", \"name\": \"invalid.illegal.parenthesis.scheme\" }, \"language-functions\": { \"patterns\": [{ \"match\": \"(?<=(\\\\s|\\\\(|\\\\[))(do|or|and|else|quasiquote|begin|if|case|set!|cond|let|unquote|define|let\\\\*|unquote-splicing|delay|letrec)(?=(\\\\s|\\\\())\", \"name\": \"keyword.control.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions run a test, and return a boolean\\n\t\t\t\t\t\tanswer.\\n\t\t\t\t\t\", \"match\": \"(?<=(\\\\s|\\\\())(char-alphabetic|char-lower-case|char-numeric|char-ready|char-upper-case|char-whitespace|(?:char|string)(?:-ci)?(?:=|<=?|>=?)|atom|boolean|bound-identifier=|char|complex|identifier|integer|symbol|free-identifier=|inexact|eof-object|exact|list|(?:input|output)-port|pair|real|rational|zero|vector|negative|odd|null|string|eq|equal|eqv|even|number|positive|procedure)(\\\\?)(?=(\\\\s|\\\\())\", \"name\": \"support.function.boolean-test.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions change one type into another.\\n\t\t\t\t\t\", \"match\": \"(?<=(\\\\s|\\\\())(char->integer|exact->inexact|inexact->exact|integer->char|symbol->string|list->vector|list->string|identifier->symbol|vector->list|string->list|string->number|string->symbol|number->string)(?=(\\\\s|\\\\())\", \"name\": \"support.function.convert-type.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions are potentially dangerous because\\n\t\t\t\t\t\tthey have side-effects which could affect other\\n\t\t\t\t\t\tparts of the program.\\n\t\t\t\t\t\", \"match\": \"(?<=(\\\\s|\\\\())(set-(?:car|cdr)|(?:vector|string)-(?:fill|set))(!)(?=(\\\\s|\\\\())\", \"name\": \"support.function.with-side-effects.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\t+, -, *, /, =, >, etc. \\n\t\t\t\t\t\", \"match\": \"(?<=(\\\\s|\\\\())(>=?|<=?|=|[*/+-])(?=(\\\\s|\\\\())\", \"name\": \"keyword.operator.arithmetic.scheme\" }, { \"match\": \"(?<=(\\\\s|\\\\())(append|apply|approximate|call-with-current-continuation|call/cc|catch|construct-identifier|define-syntax|display|foo|for-each|force|format|cd|gen-counter|gen-loser|generate-identifier|last-pair|length|let-syntax|letrec-syntax|list|list-ref|list-tail|load|log|macro|magnitude|map|map-streams|max|member|memq|memv|min|newline|nil|not|peek-char|rationalize|read|read-char|return|reverse|sequence|substring|syntax|syntax-rules|transcript-off|transcript-on|truncate|unwrap-syntax|values-list|write|write-char|cons|c(a|d){1,4}r|abs|acos|angle|asin|assoc|assq|assv|atan|ceiling|cos|floor|round|sin|sqrt|tan|(?:real|imag)-part|numerator|denominatormodulo|exp|expt|remainder|quotient|lcm|call-with-(?:input|output)-file|(?:close|current)-(?:input|output)-port|with-(?:input|output)-from-file|open-(?:input|output)-file|char-(?:downcase|upcase|ready)|make-(?:polar|promise|rectangular|string|vector)string(?:-(?:append|copy|length|ref))?|vector(?:-length|-ref))(?=(\\\\s|\\\\())\", \"name\": \"support.function.general.scheme\" }] }, \"quote\": { \"comment\": \"\\n\t\t\t\tWe need to be able to quote any kind of item, which creates\\n\t\t\t\ta tiny bit of complexity in our grammar.  It is hopefully\\n\t\t\t\tnot overwhelming complexity.\\n\t\t\t\t\\n\t\t\t\tNote: the first two matches are special cases.  quoted\\n\t\t\t\tsymbols, and quoted empty lists are considered constant.other\\n\t\t\t\t\\n\t\t\t\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.section.quoted.symbol.scheme\" } }, \"match\": \"(')\\\\s*([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*)\", \"name\": \"constant.other.symbol.scheme\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.quoted.empty-list.scheme\" }, \"2\": { \"name\": \"meta.expression.scheme\" }, \"3\": { \"name\": \"punctuation.section.expression.begin.scheme\" }, \"4\": { \"name\": \"punctuation.section.expression.end.scheme\" } }, \"match\": \"(')\\\\s*((\\\\()\\\\s*(\\\\)))\", \"name\": \"constant.other.empty-list.schem\" }, { \"begin\": \"(')\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.quoted.scheme\" } }, \"comment\": \"quoted double-quoted string or s-expression\", \"end\": \"(?=[\\\\s()])|(?<=\\\\n)\", \"name\": \"string.other.quoted-object.scheme\", \"patterns\": [{ \"include\": \"#quoted\" }] }] }, \"quote-sexp\": { \"begin\": \"(?<=\\\\()\\\\s*(quote)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.quote.scheme\" } }, \"comment\": \"\\n\t\t\t\tSomething quoted with (quote \\xABthing\\xBB).  In this case \\xABthing\\xBB\\n\t\t\t\twill not be evaluated, so we are considering it a string.\\n\t\t\t\", \"contentName\": \"string.other.quote.scheme\", \"end\": \"(?=[\\\\s)])|(?<=\\\\n)\", \"patterns\": [{ \"include\": \"#quoted\" }] }, \"quoted\": { \"patterns\": [{ \"include\": \"#string\" }, { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.scheme\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.scheme\" } }, \"name\": \"meta.expression.scheme\", \"patterns\": [{ \"include\": \"#quoted\" }] }, { \"include\": \"#quote\" }, { \"include\": \"#illegal\" }] }, \"sexp\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.scheme\" } }, \"end\": \"(\\\\))(\\\\n)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.scheme\" }, \"2\": { \"name\": \"meta.after-expression.scheme\" } }, \"name\": \"meta.expression.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<=\\\\()(define)\\\\s+(\\\\()([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*)((\\\\s+([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*|[._]))*)\\\\s*(\\\\))\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"punctuation.definition.function.scheme\" }, \"3\": { \"name\": \"entity.name.function.scheme\" }, \"4\": { \"name\": \"variable.parameter.function.scheme\" }, \"7\": { \"name\": \"punctuation.definition.function.scheme\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.procedure.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"begin\": \"(?<=\\\\()(lambda)\\\\s+(\\\\()((?:([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*|[._])\\\\s+)*(?:([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*|[._]))?)(\\\\))\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"punctuation.definition.variable.scheme\" }, \"3\": { \"name\": \"variable.parameter.scheme\" }, \"6\": { \"name\": \"punctuation.definition.variable.scheme\" } }, \"comment\": \"\\n\t\t\t\t\t\tNot sure this one is quite correct.  That \\\\s* is\\n\t\t\t\t\t\tparticularly troubling\\n\t\t\t\t\t\", \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.procedure.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"begin\": \"(?<=\\\\()(define)\\\\s([0-9A-Za-z][0-9A-Za-z!$%&*+-./:<=>?@^_~]*)\\\\s*.*?\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"variable.other.scheme\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.variable.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"include\": \"#quote-sexp\" }, { \"include\": \"#quote\" }, { \"include\": \"#language-functions\" }, { \"include\": \"#string\" }, { \"include\": \"#constants\" }, { \"match\": \"(?<=[(\\\\s])(#\\\\\\\\)(space|newline|tab)(?=[\\\\s)])\", \"name\": \"constant.character.named.scheme\" }, { \"match\": \"(?<=[(\\\\s])(#\\\\\\\\)x[0-9A-F]{2,4}(?=[\\\\s)])\", \"name\": \"constant.character.hex-literal.scheme\" }, { \"match\": \"(?<=[(\\\\s])(#\\\\\\\\).(?=[\\\\s)])\", \"name\": \"constant.character.escape.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tthe . in (a . b) which conses together two elements\\n\t\t\t\t\t\ta and b. (a b c) == (a . (b . (c . nil)))\\n\t\t\t\t\t\", \"match\": \"(?<=[ ()])\\\\.(?=[ ()])\", \"name\": \"punctuation.separator.cons.scheme\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, \"string\": { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.scheme\" } }, \"end\": '(\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.scheme\" } }, \"name\": \"string.quoted.double.scheme\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.scheme\" }] } }, \"scopeName\": \"source.scheme\" });\nvar scheme = [\n  lang\n];\n\nexport { scheme as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;QAAO;QAAM;QAAO;KAAM;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAsB;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAU,eAAe;YAAW,OAAO;YAAU,QAAQ;YAAW,YAAY;gBAAC;oBAAE,WAAW;oBAAkB,QAAQ;gBAAU;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAAgC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS,CAAC,8FAA8F,CAAC;oBAAE,QAAQ;gBAA0B;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAc,QAAQ;QAAqC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8I,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAkF,SAAS;oBAAiZ,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAgE,SAAS;oBAA6N,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAwJ,SAAS;oBAAkF,QAAQ;gBAA4C;gBAAG;oBAAE,WAAW;oBAA0C,SAAS;oBAAiD,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAu9B,QAAQ;gBAAkC;aAAE;QAAC;QAAG,SAAS;YAAE,WAAW;YAAsT,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,SAAS;oBAAsD,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAA2B,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,WAAW;oBAA+C,OAAO;oBAAwB,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA2B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,WAAW;YAAsJ,eAAe;YAA6B,OAAO;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAe,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA0I,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkJ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,WAAW;oBAAkG,OAAO;oBAAW,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA8C,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAuH,SAAS;oBAA0B,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAmC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAgB;AAC/gR,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}