(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[200,9456],{6160:(e,t,r)=>{"use strict";r.r(t),r.d(t,{KeylessCookieSync:()=>a});var n=r(35062),i=r(50628),o=r(37791);function a(e){var t;let a=(null==(t=(0,n.useSelectedLayoutSegments)()[0])?void 0:t.startsWith("/_not-found"))||!1;return(0,i.useEffect)(()=>{o.I&&!a&&r.e(1990).then(r.bind(r,11990)).then(t=>t.syncKeylessConfigAction({...e,returnUrl:window.location.href}))},[a]),e.children}},12056:(e,t,r)=>{"use strict";r.d(t,{useAuth:()=>i.d,useClerk:()=>n.ho,useEmailLink:()=>n.ui,useOrganization:()=>n.Z5,useOrganizationList:()=>n.D_,useReverification:()=>n.Wp,useSession:()=>n.wV,useSessionList:()=>n.g7,useSignIn:()=>n.go,useSignUp:()=>n.yC,useUser:()=>n.Jd});var n=r(12503);r(13319);var i=r(90148)},33026:(e,t,r)=>{"use strict";r.d(t,{CreateOrganization:()=>n.ul,GoogleOneTap:()=>n.PQ,OrganizationList:()=>n.oE,OrganizationProfile:()=>h,OrganizationSwitcher:()=>n.NC,PricingTable:()=>n.nm,SignIn:()=>d,SignInButton:()=>n.hZ,SignInWithMetamaskButton:()=>n.M_,SignOutButton:()=>n.ct,SignUp:()=>g,SignUpButton:()=>n.Ny,UserButton:()=>n.uF,UserProfile:()=>f,Waitlist:()=>n.cP});var n=r(12503),i=r(50628),o=r(17379),a=r(66598),s=r(85007);let u=(e,t,r,o=!0)=>{let u=i.useRef(0),{pagesRouter:l}=(0,s.r)(),{session:c,isLoaded:f}=(0,n.wV)();(0,a.Fj)()||i.useEffect(()=>{if(!f||r&&"path"!==r||o&&!c)return;let n=new AbortController,i=()=>{let r=l?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${r}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return l?l.pathname.match(/\[\[\.\.\..+]]/)||i():(async()=>{let t;if(u.current++,!(u.current>1)){try{let r=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(r,{signal:n.signal})}catch{}(null==t?void 0:t.status)===404&&i()}})(),()=>{u.current>1&&n.abort()}},[f])},l=()=>{let e=i.useRef(),{pagesRouter:t}=(0,s.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let n=r(35062).usePathname,o=r(35062).useParams,a=(n()||"").split("/").filter(Boolean),u=Object.values(o()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${a.slice(0,a.length-u.length).join("/")}`),e.current};function c(e,t,r=!0){let n=l(),i=(0,o.yC)(e,t,{path:n});return u(e,n,i.routing,r),i}let f=Object.assign(e=>i.createElement(n.Fv,{...c("UserProfile",e)}),{...n.Fv}),h=Object.assign(e=>i.createElement(n.nC,{...c("OrganizationProfile",e)}),{...n.nC}),d=e=>i.createElement(n.Ls,{...c("SignIn",e,!1)}),g=e=>i.createElement(n.Hx,{...c("SignUp",e,!1)})},35062:(e,t,r)=>{"use strict";r.r(t);var n=r(43914),i={};for(let e in n)"default"!==e&&(i[e]=()=>n[e]);r.d(t,i)},39499:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(70988);let i=(0,n.createServerReference)("7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},61372:(e,t,r)=>{"use strict";r.d(t,{j:()=>o});var n=Object.prototype.hasOwnProperty;function i(e,t,r){for(r of e.keys())if(o(r,t))return r}function o(e,t){var r,a,s;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((a=e.length)===t.length)for(;a--&&o(e[a],t[a]););return -1===a}if(r===Set){if(e.size!==t.size)return!1;for(a of e)if((s=a)&&"object"==typeof s&&!(s=i(t,s))||!t.has(s))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(a of e)if((s=a[0])&&"object"==typeof s&&!(s=i(t,s))||!o(a[1],t.get(s)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((a=e.byteLength)===t.byteLength)for(;a--&&e.getInt8(a)===t.getInt8(a););return -1===a}if(ArrayBuffer.isView(e)){if((a=e.byteLength)===t.byteLength)for(;a--&&e[a]===t[a];);return -1===a}if(!r||"object"==typeof e){for(r in a=0,e)if(n.call(e,r)&&++a&&!n.call(t,r)||!(r in t)||!o(e[r],t[r]))return!1;return Object.keys(t).length===a}}return e!=e&&t!=t}},86739:(e,t,r)=>{"use strict";r.d(t,{AuthenticateWithRedirectCallback:()=>n.B$,ClerkDegraded:()=>n.wF,ClerkFailed:()=>n.lT,ClerkLoaded:()=>n.z0,ClerkLoading:()=>n.A0,RedirectToCreateOrganization:()=>n.rm,RedirectToOrganizationProfile:()=>n.m2,RedirectToSignIn:()=>n.W5,RedirectToSignUp:()=>n.mO,RedirectToUserProfile:()=>n.eG});var n=r(12503);r(17379)},90148:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>u,d:()=>l});var n=r(12503),i=r(17379),o=r(51702),a=r(50628);let s=a.createContext(null);function u(e){let{authPromise:t,children:r}=e;return a.createElement(s.Provider,{value:t},r)}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,o.useRouter)(),r=a.useContext(s),u=r;return(r&&"then"in r&&(u=a.use(r)),"undefined"!=typeof window)?(0,n.As)({...u,...e}):t?(0,n.As)(e):(0,i.hP)({...u,...e})}},98177:(e,t,r)=>{Promise.resolve().then(r.bind(r,12535)),Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,86739)),Promise.resolve().then(r.bind(r,12056)),Promise.resolve().then(r.bind(r,90148)),Promise.resolve().then(r.bind(r,33026)),Promise.resolve().then(r.bind(r,5659)),Promise.resolve().then(r.bind(r,60006))}},e=>{var t=t=>e(e.s=t);e.O(0,[2503,3594,2913,4499,7358],()=>t(98177)),_N_E=e.O()}]);