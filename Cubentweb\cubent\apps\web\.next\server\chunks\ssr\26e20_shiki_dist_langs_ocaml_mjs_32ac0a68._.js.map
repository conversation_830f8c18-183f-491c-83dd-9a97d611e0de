{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/ocaml.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"OCaml\", \"fileTypes\": [\".ml\", \".mli\"], \"name\": \"ocaml\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }], \"repository\": { \"attribute\": { \"begin\": \"(\\\\[)[\\\\s]*((?<![#\\\\-:!?.@*/&%^+<=>|~$])@{1,3}(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"beginCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"patterns\": [{ \"include\": \"#attributePayload\" }] }, \"attributeIdentifier\": { \"captures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"match\": `((?<![#\\\\-:!?.@*/&%^+<=>|~$])%(?![#\\\\-:!?.@*/&%^+<=>|~$]))((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))` }, \"attributePayload\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]%|^%))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"((?<![#\\\\-:!?.@*/&%^+<=>|~$])[:?](?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<=[\\\\s])|(?=\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pathModuleExtended\" }, { \"include\": \"#pathRecord\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#signature\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])|\\\\bwhen\\\\b\", \"endCaptures\": { \"1\": {} }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]when|^when))(?![\\\\w]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#term\" }] }] }, { \"include\": \"#term\" }] }, \"bindClassTerm\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]class|^class|[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]class|^class|[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": `(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)[\\\\s]*,|[^\\\\sa-z%])|(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)|(?=\\\\btype\\\\b)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"bindClassType\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]class|^class|[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]class|^class|[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": `(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)[\\\\s]*,|[^\\\\sa-z%])|(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)|(?=\\\\btype\\\\b)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }] }, \"bindConstructor\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]exception|^exception))(?![\\\\w]))|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\+=|^\\\\+=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\||^\\\\|))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(:)|(\\\\bof\\\\b)|((?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" }, \"3\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"match\": \"\\\\.\\\\.\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"match\": \"\\\\b(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\\\\b(?![\\\\s]*(?:\\\\.|\\\\([^\\\\*]))\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\\\w]of|^of))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"bindSignature\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModuleExtended\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }] }, \"bindStructure\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and))(?![\\\\w]))|(?=[A-Z])\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:(?!=))|(:?=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"\\\\bmodule\\\\b\", \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, { \"match\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\", \"name\": \"entity.name.function strong emphasis\" }, { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#signature\" }] }, { \"include\": \"#variableModule\" }] }, { \"include\": \"#literalUnit\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(and)\\\\b|((?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:=|^:=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(?:(and)|(with))\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#structure\" }] }] }, \"bindTerm\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]external|^external|[^\\\\w]let|^let|[^\\\\w]method|^method|[^\\\\w]val|^val))(?![\\\\w]))\", \"end\": \"(\\\\bmodule\\\\b)|(\\\\bopen\\\\b)|(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|((?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$]))(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"4\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]external|^external|[^\\\\w]let|^let|[^\\\\w]method|^method|[^\\\\w]val|^val))(?![\\\\w]))\", \"end\": `(?=\\\\b(?:module|open)\\\\b)|(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)[\\\\s]*,|[^\\\\sa-z%])|(\\\\brec\\\\b)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))`, \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]rec|^rec))(?![\\\\w]))\", \"end\": `((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))|(?=[^\\\\sA-Za-z])`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#bindTermArgs\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]module|^module))(?![\\\\w]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#declModule\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]open|^open))(?![\\\\w]))\", \"end\": \"(?=\\\\bin\\\\b)|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#pathModuleSimple\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\btype\\\\b|(?=[^\\\\s])\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } } }, { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"bindTermArgs\": { \"patterns\": [{ \"applyEndPatternLast\": true, \"begin\": \"~|\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \":|(?=[^\\\\s])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]~|^~|[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)|(?<=\\\\))`, \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\((?!\\\\*)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"begin\": \"(?<=\\\\()\", \"end\": \":|=\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }] }, { \"begin\": \"(?<=:)\", \"end\": \"=|(?=\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }] }, { \"include\": \"#pattern\" }] }, \"bindType\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\+=|=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#pathType\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"entity.name.function strong\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\+=|^\\\\+=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#bindConstructor\" }] }] }, \"comment\": { \"patterns\": [{ \"include\": \"#attribute\" }, { \"include\": \"#extension\" }, { \"include\": \"#commentBlock\" }, { \"include\": \"#commentDoc\" }] }, \"commentBlock\": { \"begin\": \"\\\\(\\\\*(?!\\\\*[^)])\", \"contentName\": \"emphasis\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment constant.regexp meta.separator.markdown\", \"patterns\": [{ \"include\": \"#commentBlock\" }, { \"include\": \"#commentDoc\" }] }, \"commentDoc\": { \"begin\": \"\\\\(\\\\*\\\\*\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment constant.regexp meta.separator.markdown\", \"patterns\": [{ \"match\": \"\\\\*\" }, { \"include\": \"#comment\" }] }, \"decl\": { \"patterns\": [{ \"include\": \"#declClass\" }, { \"include\": \"#declException\" }, { \"include\": \"#declInclude\" }, { \"include\": \"#declModule\" }, { \"include\": \"#declOpen\" }, { \"include\": \"#declTerm\" }, { \"include\": \"#declType\" }] }, \"declClass\": { \"begin\": \"\\\\bclass\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"begin\": \"(?:(?<=(?:[^\\\\w]class|^class))(?![\\\\w]))\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric markup.underline\" } }, \"end\": \"\\\\btype\\\\b|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#bindClassTerm\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindClassType\" }] }] }, \"declException\": { \"begin\": \"\\\\bexception\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindConstructor\" }] }, \"declInclude\": { \"begin\": \"\\\\binclude\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#signature\" }] }, \"declModule\": { \"begin\": \"(?:(?<=(?:[^\\\\w]module|^module))(?![\\\\w]))|\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"begin\": \"(?:(?<=(?:[^\\\\w]module|^module))(?![\\\\w]))\", \"end\": \"(\\\\btype\\\\b)|(?=[A-Z])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"match\": \"\\\\brec\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindSignature\" }] }, { \"begin\": \"(?=[A-Z])\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindStructure\" }] }] }, \"declOpen\": { \"begin\": \"\\\\bopen\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#pathModuleExtended\" }] }, \"declTerm\": { \"begin\": \"\\\\b(?:(external|val)|(method)|(let))\\\\b(!?)\", \"beginCaptures\": { \"1\": { \"name\": \"support.type markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" }, \"3\": { \"name\": \"keyword.control markup.underline\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindTerm\" }] }, \"declType\": { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))|\\\\btype\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindType\" }] }, \"extension\": { \"begin\": \"(\\\\[)((?<![#\\\\-:!?.@*/&%^+<=>|~$])%{1,3}(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"beginCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"patterns\": [{ \"include\": \"#attributePayload\" }] }, \"literal\": { \"patterns\": [{ \"include\": \"#termConstructor\" }, { \"include\": \"#literalArray\" }, { \"include\": \"#literalBoolean\" }, { \"include\": \"#literalCharacter\" }, { \"include\": \"#literalList\" }, { \"include\": \"#literalNumber\" }, { \"include\": \"#literalObjectTerm\" }, { \"include\": \"#literalString\" }, { \"include\": \"#literalRecord\" }, { \"include\": \"#literalUnit\" }] }, \"literalArray\": { \"begin\": \"\\\\[\\\\|\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\|\\\\]\", \"patterns\": [{ \"include\": \"#term\" }] }, \"literalBoolean\": { \"match\": \"\\\\bfalse|true\\\\b\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"literalCharacter\": { \"begin\": \"(?<![\\\\w])'\", \"end\": \"'\", \"name\": \"markup.punctuation.quote.beginning\", \"patterns\": [{ \"include\": \"#literalCharacterEscape\" }] }, \"literalCharacterEscape\": { \"match\": `\\\\\\\\(?:[\\\\\\\\\"'ntbr]|[\\\\d][\\\\d][\\\\d]|x[0-9A-Fa-f][0-9A-Fa-f]|o[0-3][0-7][0-7])` }, \"literalClassType\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\bobject\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"begin\": \"\\\\bas\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#variablePattern\" }] }, { \"include\": \"#type\" }] }, { \"include\": \"#pattern\" }, { \"include\": \"#declTerm\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\" }] }, \"literalList\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#term\" }] }] }, \"literalNumber\": { \"match\": \"(?<![A-Za-z])[\\\\d][\\\\d]*(\\\\.[\\\\d][\\\\d]*)?\", \"name\": \"constant.numeric\" }, \"literalObjectTerm\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\bobject\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"begin\": \"\\\\bas\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#variablePattern\" }] }, { \"include\": \"#term\" }] }, { \"include\": \"#pattern\" }, { \"include\": \"#declTerm\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\" }] }, \"literalRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]with|^with))(?![\\\\w]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"literalString\": { \"patterns\": [{ \"begin\": '\"', \"end\": '\"', \"name\": \"string beginning.punctuation.definition.quote.markdown\", \"patterns\": [{ \"include\": \"#literalStringEscape\" }] }, { \"begin\": \"(\\\\{)([_a-z]*?)(\\\\|)\", \"end\": \"(\\\\|)(\\\\2)(\\\\})\", \"name\": \"string beginning.punctuation.definition.quote.markdown\", \"patterns\": [{ \"include\": \"#literalStringEscape\" }] }] }, \"literalStringEscape\": { \"match\": '\\\\\\\\(?:[\\\\\\\\\"ntbr]|[\\\\d][\\\\d][\\\\d]|x[0-9A-Fa-f][0-9A-Fa-f]|o[0-3][0-7][0-7])' }, \"literalUnit\": { \"match\": \"\\\\(\\\\)\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"pathModuleExtended\": { \"patterns\": [{ \"include\": \"#pathModulePrefixExtended\" }, { \"match\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\", \"name\": \"entity.name.class constant.numeric\" }] }, \"pathModulePrefixExtended\": { \"begin\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\.|$|\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric\" } }, \"end\": \"(?![\\\\s\\\\.]|$|\\\\()\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \"((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\)))\", \"name\": \"string.other.link variable.language variable.parameter emphasis\" }, { \"include\": \"#structure\" }] }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": \"((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\.|$))|((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*(?:$|\\\\()))|((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\)))|(?![\\\\s\\\\.A-Z]|$|\\\\()\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.class constant.numeric\" }, \"2\": { \"name\": \"entity.name.function strong\" }, \"3\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } } }] }, \"pathModulePrefixExtendedParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \"((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\)))\", \"name\": \"string.other.link variable.language variable.parameter emphasis\" }, { \"include\": \"#structure\" }] }, \"pathModulePrefixSimple\": { \"begin\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\.)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric\" } }, \"end\": \"(?![\\\\s\\\\.])\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": \"((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*\\\\.))|((?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)(?=[\\\\s]*))|(?![\\\\s\\\\.A-Z])\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.class constant.numeric\" }, \"2\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } } }] }, \"pathModuleSimple\": { \"patterns\": [{ \"include\": \"#pathModulePrefixSimple\" }, { \"match\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\", \"name\": \"entity.name.class constant.numeric\" }] }, \"pathRecord\": { \"patterns\": [{ \"begin\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"end\": \"(?=[^\\\\s\\\\.])(?!\\\\(\\\\*)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\.|^\\\\.))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": `((?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$]))|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|mutable|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))|(?<=\\\\))|(?<=\\\\])`, \"endCaptures\": { \"1\": { \"name\": \"keyword strong\" }, \"2\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"begin\": \"\\\\((?!\\\\*)\", \"captures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }] }] }] }, \"pattern\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#patternArray\" }, { \"include\": \"#patternLazy\" }, { \"include\": \"#patternList\" }, { \"include\": \"#patternMisc\" }, { \"include\": \"#patternModule\" }, { \"include\": \"#patternRecord\" }, { \"include\": \"#literal\" }, { \"include\": \"#patternParens\" }, { \"include\": \"#patternType\" }, { \"include\": \"#variablePattern\" }, { \"include\": \"#termOperator\" }] }, \"patternArray\": { \"begin\": \"\\\\[\\\\|\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\|\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }, \"patternLazy\": { \"match\": \"lazy\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"patternList\": { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }, \"patternMisc\": { \"captures\": { \"1\": { \"name\": \"string.regexp strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"match\": \"((?<![#\\\\-:!?.@*/&%^+<=>|~$]),(?![#\\\\-:!?.@*/&%^+<=>|~$]))|([#\\\\-:!?.@*/&%^+<=>|~$]+)|\\\\b(as)\\\\b\" }, \"patternModule\": { \"begin\": \"\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declModule\" }] }, \"patternParens\": { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#pattern\" }] }, \"patternRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]with|^with))(?![\\\\w]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }] }, \"patternType\": { \"begin\": \"\\\\btype\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declType\" }] }, \"pragma\": { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])#(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#literalNumber\" }, { \"include\": \"#literalString\" }] }, \"signature\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#signatureLiteral\" }, { \"include\": \"#signatureFunctor\" }, { \"include\": \"#pathModuleExtended\" }, { \"include\": \"#signatureParens\" }, { \"include\": \"#signatureRecovered\" }, { \"include\": \"#signatureConstraints\" }] }, \"signatureConstraints\": { \"begin\": \"\\\\bwith\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"end\": \"(?=\\\\))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]with|^with))(?![\\\\w]))\", \"end\": \"\\\\b(?:(module)|(type))\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, \"2\": { \"name\": \"keyword\" } } }, { \"include\": \"#declModule\" }, { \"include\": \"#declType\" }] }, \"signatureFunctor\": { \"patterns\": [{ \"begin\": \"\\\\bfunctor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]functor|^functor))(?![\\\\w]))\", \"end\": \"(\\\\(\\\\))|(\\\\((?!\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?<=\\\\()\", \"end\": \"(:)|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#variableModule\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(\\\\()|((?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag\" }, \"2\": { \"name\": \"support.type strong\" } } }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#signature\" }] }] }, { \"match\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"name\": \"support.type strong\" }] }, \"signatureLiteral\": { \"begin\": \"\\\\bsig\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }] }, \"signatureParens\": { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#signature\" }] }, { \"include\": \"#signature\" }] }, \"signatureRecovered\": { \"patterns\": [{ \"begin\": \"\\\\(|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:|[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^\\\\w]include|^include|[^\\\\w]open|^open))(?![\\\\w]))\", \"end\": \"\\\\bmodule\\\\b|(?!$|[\\\\s]|\\\\bmodule\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } } }, { \"begin\": \"(?:(?<=(?:[^\\\\w]module|^module))(?![\\\\w]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]module|^module))(?![\\\\w]))\", \"end\": \"\\\\btype\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } } }, { \"begin\": \"(?:(?<=(?:[^\\\\w]type|^type))(?![\\\\w]))\", \"end\": \"\\\\bof\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?:(?<=(?:[^\\\\w]of|^of))(?![\\\\w]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#signature\" }] }] }] }, \"structure\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#structureLiteral\" }, { \"include\": \"#structureFunctor\" }, { \"include\": \"#pathModuleExtended\" }, { \"include\": \"#structureParens\" }] }, \"structureFunctor\": { \"patterns\": [{ \"begin\": \"\\\\bfunctor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]functor|^functor))(?![\\\\w]))\", \"end\": \"(\\\\(\\\\))|(\\\\((?!\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?<=\\\\()\", \"end\": \"(:)|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#variableModule\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(\\\\()|((?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag\" }, \"2\": { \"name\": \"support.type strong\" } } }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#structure\" }] }] }, { \"match\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"name\": \"support.type strong\" }] }, \"structureLiteral\": { \"begin\": \"\\\\bstruct\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }] }, \"structureParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#structureUnpack\" }, { \"include\": \"#structure\" }] }, \"structureUnpack\": { \"begin\": \"\\\\bval\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"(?=\\\\))\" }, \"term\": { \"patterns\": [{ \"include\": \"#termLet\" }, { \"include\": \"#termAtomic\" }] }, \"termAtomic\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#termConditional\" }, { \"include\": \"#termConstructor\" }, { \"include\": \"#termDelim\" }, { \"include\": \"#termFor\" }, { \"include\": \"#termFunction\" }, { \"include\": \"#literal\" }, { \"include\": \"#termMatch\" }, { \"include\": \"#termMatchRule\" }, { \"include\": \"#termPun\" }, { \"include\": \"#termOperator\" }, { \"include\": \"#termTry\" }, { \"include\": \"#termWhile\" }, { \"include\": \"#pathRecord\" }] }, \"termConditional\": { \"match\": \"\\\\b(?:if|then|else)\\\\b\", \"name\": \"keyword.control\" }, \"termConstructor\": { \"patterns\": [{ \"include\": \"#pathModulePrefixSimple\" }, { \"match\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }] }, \"termDelim\": { \"patterns\": [{ \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"\\\\bbegin\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#term\" }] }] }, \"termFor\": { \"patterns\": [{ \"begin\": \"\\\\bfor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bdone\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]for|^for))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(?:downto|to)\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]to|^to))(?![\\\\w]))\", \"end\": \"\\\\bdo\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]do|^do))(?![\\\\w]))\", \"end\": \"(?=\\\\bdone\\\\b)\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"termFunction\": { \"captures\": { \"1\": { \"name\": \"storage.type\" }, \"2\": { \"name\": \"storage.type\" } }, \"match\": \"\\\\b(?:(fun)|(function))\\\\b\" }, \"termLet\": { \"patterns\": [{ \"begin\": \"(?:(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<=;|\\\\())(?=[\\\\s]|\\\\blet\\\\b)|(?:(?<=(?:[^\\\\w]begin|^begin|[^\\\\w]do|^do|[^\\\\w]else|^else|[^\\\\w]in|^in|[^\\\\w]struct|^struct|[^\\\\w]then|^then|[^\\\\w]try|^try))(?![\\\\w]))|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]@@|^@@))(?![#\\\\-:!?.@*/&%^+<=>|~$]))[\\\\s]+\", \"end\": \"\\\\b(?:(and)|(let))\\\\b|(?=[^\\\\s])(?!\\\\(\\\\*)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" } }, \"patterns\": [{ \"include\": \"#comment\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]and|^and|[^\\\\w]let|^let))(?![\\\\w]))|(let)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type markup.underline\" } }, \"end\": \"\\\\b(?:(and)|(in))\\\\b|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|class|exception|external|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" } }, \"patterns\": [{ \"include\": \"#bindTerm\" }] }] }, \"termMatch\": { \"begin\": \"\\\\bmatch\\\\b\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bwith\\\\b\", \"patterns\": [{ \"include\": \"#term\" }] }, \"termMatchRule\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]fun|^fun|[^\\\\w]function|^function|[^\\\\w]with|^with))(?![\\\\w]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(\\\\|)|(->)(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"1\": { \"name\": \"support.type strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attributeIdentifier\" }, { \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\[#\\\\-:!?.@*/&%^+<=>|~$]\\\\||^\\\\|))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(\\\\|)|(->)(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"1\": { \"name\": \"support.type strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#pattern\" }, { \"begin\": \"\\\\bwhen\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"(?=(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"termOperator\": { \"patterns\": [{ \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])#(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function\" } } }, { \"captures\": { \"0\": { \"name\": \"keyword.control strong\" } }, \"match\": \"<-\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"match\": \"(,|[#\\\\-:!?.@*/&%^+<=>|~$]+)|(;)\" }, { \"match\": \"\\\\b(?:and|assert|asr|land|lazy|lsr|lxor|mod|new|or)\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }] }, \"termPun\": { \"applyEndPatternLast\": true, \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\?|~(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \":|(?=[^\\\\s:])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?|[^#\\\\-:!?.@*/&%^+<=>|~$]~|^~))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } } }] }, \"termTry\": { \"begin\": \"\\\\btry\\\\b\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bwith\\\\b\", \"patterns\": [{ \"include\": \"#term\" }] }, \"termWhile\": { \"patterns\": [{ \"begin\": \"\\\\bwhile\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bdone\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^\\\\w]while|^while))(?![\\\\w]))\", \"end\": \"\\\\bdo\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]do|^do))(?![\\\\w]))\", \"end\": \"(?=\\\\bdone\\\\b)\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"type\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"\\\\bnonrec\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"include\": \"#pathModulePrefixExtended\" }, { \"include\": \"#typeLabel\" }, { \"include\": \"#typeObject\" }, { \"include\": \"#typeOperator\" }, { \"include\": \"#typeParens\" }, { \"include\": \"#typePolymorphicVariant\" }, { \"include\": \"#typeRecord\" }, { \"include\": \"#typeConstructor\" }] }, \"typeConstructor\": { \"patterns\": [{ \"begin\": `(_)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))|(')((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))|(?<=[^\\\\*]\\\\)|\\\\])`, \"beginCaptures\": { \"1\": { \"name\": \"comment constant.regexp meta.separator.markdown\" }, \"3\": { \"name\": \"string.other.link variable.language variable.parameter emphasis strong emphasis\" }, \"4\": { \"name\": \"keyword.control emphasis\" } }, \"end\": `(?=\\\\((?!\\\\*)|\\\\*|:|,|=|\\\\.|>|-|\\\\{|\\\\[|\\\\+|\\\\}|\\\\)|\\\\]|;|\\\\|)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))[\\\\s]*(?!\\\\(\\\\*|[\\\\w])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)`, \"endCaptures\": { \"1\": { \"name\": \"entity.name.function strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixExtended\" }] }] }, \"typeLabel\": { \"patterns\": [{ \"begin\": `(\\\\??)((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))[\\\\s]*((?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$]))`, \"captures\": { \"1\": { \"name\": \"keyword strong emphasis\" }, \"2\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }, \"3\": { \"name\": \"keyword\" } }, \"end\": \"(?=(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"patterns\": [{ \"include\": \"#type\" }] }] }, \"typeModule\": { \"begin\": \"\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#pathModuleExtended\" }, { \"include\": \"#signatureConstraints\" }] }, \"typeObject\": { \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \">\", \"patterns\": [{ \"begin\": \"(?<=<|;)\", \"end\": \"(:)|(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"typeOperator\": { \"patterns\": [{ \"match\": \",|;|[#\\\\-:!?.@*/&%^+<=>|~$]+\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }] }, \"typeParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \",\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"include\": \"#typeModule\" }, { \"include\": \"#type\" }] }, \"typePolymorphicVariant\": { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [] }, \"typeRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\w]with|^with))(?![\\\\w]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"variableModule\": { \"captures\": { \"0\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } }, \"match\": \"(?:\\\\b(?=[A-Z])[A-Za-z_][\\\\w']*)\" }, \"variablePattern\": { \"captures\": { \"1\": { \"name\": \"comment constant.regexp meta.separator.markdown\" }, \"2\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } }, \"match\": `(\\\\b_\\\\b)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[a-z_])[A-Za-z_][\\\\w']*))` } }, \"scopeName\": \"source.ocaml\" });\nvar ocaml = [\n  lang\n];\n\nexport { ocaml as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;QAAO;KAAO;IAAE,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,aAAa;YAAE,SAAS;YAA8E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;gBAAG,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoF;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,SAAS,CAAC,2fAA2f,CAAC;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwE,OAAO;oBAAoF,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4E,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAA4E,OAAO;4BAAsB,eAAe;gCAAE,KAAK,CAAC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA0C,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4E,OAAO;oBAA+L,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA4E,OAAO,CAAC,o6BAAo6B,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAuB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAO,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAAO,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAiL,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAA0I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4E,OAAO;oBAA+L,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA4E,OAAO,CAAC,o6BAAo6B,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAuB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAO,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAAO,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAiL,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAA0I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6L,OAAO;oBAA4M,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAA6E;wBAAG;4BAAE,SAAS;4BAAqE,QAAQ;wBAA+E;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2G,OAAO;oBAA2L,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAA4D,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAA0I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAkD,OAAO;oBAA4L,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAAyF;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAAuC;wBAAG;4BAAE,SAAS;4BAAc,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAAO,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAA4D,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAoF;oCAAE;oCAAG,OAAO;oCAAW,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAuM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuG,OAAO;oBAAuJ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;wBAAG,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyL,OAAO;oBAAkR,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyF;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAyL,OAAO,CAAC,67BAA67B,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6E;gCAAG,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAuB;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAwC,OAAO,CAAC,kdAAkd,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAA0I,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAyL,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAwE,OAAO;4BAAyB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAA0C,OAAO;4BAA8D,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6E;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAA0I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,uBAAuB;oBAAM,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,OAAO;oBAAgB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAyG,OAAO,CAAC,wcAAwc,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyF;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAAc,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6B;oCAAE;oCAAG,OAAO;oCAAO,YAAY;wCAAC;4CAAE,SAAS;4CAAY,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAU;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,SAAS,CAAC,+bAA+b,CAAC;oDAAE,QAAQ;gDAAyF;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAU,OAAO;4CAAa,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAU;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAwE,OAAO;4CAAW,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyD,OAAO;oBAA8L,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2G,OAAO;oBAA0I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAqB,eAAe;YAAY,OAAO;YAAU,QAAQ;YAAmD,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAa,OAAO;YAAU,QAAQ;YAAmD,YAAY;gBAAC;oBAAE,SAAS;gBAAM;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,OAAO;oBAAmI,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA2D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0G;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA8C,OAAO;oBAA0B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAa,QAAQ;wBAA6E;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA+C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAqD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAgI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAwE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;gBAAG,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAU,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;YAAE;YAAG,OAAO;YAAU,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAoB,QAAQ;QAA+E;QAAG,oBAAoB;YAAE,SAAS;YAAe,OAAO;YAAK,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS,CAAC,6EAA6E,CAAC;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,SAAS;4BAAiB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6E;4BAAE;4BAAG,OAAO;4BAAgI,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,SAAS;oCAAY,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA6E;oCAAE;oCAAG,OAAO;oCAAgI,YAAY;wCAAC;4CAAE,WAAW;wCAAmB;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;gBAAM;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+E;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA6C,QAAQ;QAAmB;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,SAAS;4BAAiB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6E;4BAAE;4BAAG,OAAO;4BAAgI,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,SAAS;oCAAY,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA6E;oCAAE;oCAAG,OAAO;oCAAgI,YAAY;wCAAC;4CAAE,WAAW;wCAAmB;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;gBAAM;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsF;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAc,OAAO;oBAA8B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAAuB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA0D,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwB,OAAO;oBAAmB,QAAQ;oBAA0D,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;QAA+E;QAAG,eAAe;YAAE,SAAS;YAAU,QAAQ;QAA+E;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAAqC;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAuD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAO,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,SAAS;4BAAmD,QAAQ;wBAAkE;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiB;oBAAE;oBAAG,OAAO;oBAAiL,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkE;oBAAE;gBAAE;aAAE;QAAC;QAAG,kCAAkC;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAmD,QAAQ;gBAAkE;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiB;oBAAE;oBAAG,OAAO;oBAAgH,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+E;oBAAE;gBAAE;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAAqC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,+bAA+b,CAAC;oBAAE,OAAO;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAuI,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiB;4BAAE;4BAAG,OAAO,CAAC,qgBAAqgB,CAAC;4BAAE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAiB;gCAAG,KAAK;oCAAE,QAAQ;gCAAyF;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAA0B;gCAAG;oCAAE,SAAS;oCAAc,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6E;oCAAE;oCAAG,OAAO;oCAAO,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAO,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6E;oCAAE;oCAAG,OAAO;oCAAO,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAU,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;YAAE;YAAG,OAAO;YAAU,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAQ,QAAQ;QAA6E;QAAG,eAAe;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+E;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6E;gBAAG,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,SAAS;QAAmG;QAAG,iBAAiB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyF;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAc,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA4D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsF;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAc,OAAO;oBAA8B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAAuB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAU;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA4D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAgI,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8F;YAAE;YAAG,OAAO;YAAwI,YAAY;gBAAC;oBAAE,SAAS;oBAA0C,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyF;wBAAG,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;gBAAE;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,SAAS;4BAAgD,OAAO;4BAAyB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA+E;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAa,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoF;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAwE,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAqE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAA0E,OAAO;4BAAgI,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAAsB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAa,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAc,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA4D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyK,OAAO;oBAAyC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyF;oBAAE;gBAAE;gBAAG;oBAAE,SAAS;oBAA8C,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,SAAS;4BAA8C,OAAO;4BAAc,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAU;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAA0C,OAAO;4BAAY,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAAsC,OAAO;4BAAgI,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,OAAO;oBAAgI,YAAY;wBAAC;4BAAE,SAAS;4BAAgD,OAAO;4BAAyB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA+E;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAa,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoF;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAwE,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAqE,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;wBAAE;wBAAG;4BAAE,SAAS;4BAA0E,OAAO;4BAAgI,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAAsB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAgB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;QAAU;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA0B,QAAQ;QAAkB;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAA+E;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAwC,OAAO;4BAA4D,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAwE,OAAO;4BAAuB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAsC,OAAO;4BAAY,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAsC,OAAO;4BAAkB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAe;gBAAG,KAAK;oBAAE,QAAQ;gBAAe;YAAE;YAAG,SAAS;QAA6B;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+V,OAAO;oBAA8C,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA2I,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8F;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAe,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkF,OAAO;oBAAqE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0I,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAqE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAc,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6E;4BAAE;4BAAG,OAAO;4BAAiE,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,OAAO,CAAC,+bAA+b,CAAC;oBAAE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;gBAAE;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAAK;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,SAAS;gBAAmC;gBAAG;oBAAE,SAAS;oBAA0D,QAAQ;gBAA6E;aAAE;QAAC;QAAG,WAAW;YAAE,uBAAuB;YAAM,SAAS;YAAgE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6E;YAAE;YAAG,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAU;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAyG,OAAO,CAAC,+bAA+b,CAAC;oBAAE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyF;oBAAE;gBAAE;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAa,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA4C,OAAO;4BAAY,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAsC,OAAO;4BAAkB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAA6E;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,65BAA65B,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkF;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO,CAAC,mpBAAmpB,CAAC;oBAAE,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA4B;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,ugBAAugB,CAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkG;wBAAG,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,OAAO;oBAAiE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyF;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAK,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsF;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgC,QAAQ;gBAAoF;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAA6E;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAO,OAAO;YAAO,YAAY,EAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsF;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAc,OAAO;oBAA8B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,OAAO;oBAAuB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoF;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS,CAAC,+bAA+b,CAAC;4BAAE,QAAQ;wBAAkG;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwE,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6E;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkE;YAAE;YAAG,SAAS;QAAmC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAAkE;YAAE;YAAG,SAAS,CAAC,2cAA2c,CAAC;QAAC;IAAE;IAAG,aAAa;AAAe;AAC/8gE,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}