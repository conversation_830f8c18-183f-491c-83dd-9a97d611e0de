{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AnimatedTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatedTitle() from the server but AnimatedTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/animated-title.tsx <module evaluation>\",\n    \"AnimatedTitle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4FACA", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AnimatedTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatedTitle() from the server but AnimatedTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/animated-title.tsx\",\n    \"AnimatedTitle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wEACA", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/hero.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { blog } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, MoveRight, PhoneCall } from 'lucide-react';\nimport Link from 'next/link';\nimport { AnimatedTitle } from './animated-title';\n\ntype HeroProps = {\n  dictionary: Dictionary;\n};\n\n// Hero component for the homepage\nexport const Hero = async ({ dictionary }: HeroProps) => (\n  <div className=\"w-full relative overflow-hidden -mt-20 pt-20\">\n    {/* Static coding languages and symbols background */}\n    <div className=\"absolute inset-0 -top-20 pointer-events-none overflow-hidden opacity-15 grayscale\">\n      {/* JavaScript Logo */}\n      <div className=\"absolute w-8 h-8 text-yellow-400\" style={{ left: '5%', top: '15%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.***********.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Python Logo */}\n      <div className=\"absolute w-6 h-6 text-blue-400\" style={{ left: '85%', top: '12%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.***********.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.***********.***********.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* TypeScript Logo */}\n      <div className=\"absolute w-8 h-8 text-blue-500\" style={{ left: '15%', top: '25%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* React Logo */}\n      <div className=\"absolute w-6 h-6 text-cyan-400\" style={{ left: '75%', top: '28%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44a23.476 23.476 0 0 0-3.107-.534A23.892 23.892 0 0 0 12.769 4.7c1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442a22.73 22.73 0 0 0-3.113.538 15.02 15.02 0 0 1-.254-1.42c-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.36-.034-.471 0-.92.014-1.36.034.44-.572.895-1.096 1.36-1.564zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87a25.64 25.64 0 0 1-4.412.005 26.64 26.64 0 0 1-1.183-1.86c-.372-.64-.71-1.29-1.018-1.946a25.17 25.17 0 0 1 1.013-1.954c.38-.66.773-1.286 1.18-1.868A25.245 25.245 0 0 1 12 8.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933a25.952 25.952 0 0 0-1.345-2.32zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493a23.966 23.966 0 0 0-1.1-2.98c.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98a23.142 23.142 0 0 0-1.086 2.964c-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39a25.819 25.819 0 0 0 1.341-2.338zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143a22.005 22.005 0 0 1-2.006-.386c.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295a1.185 1.185 0 0 1-.553-.132c-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.36.034.47 0 .92-.014 1.36-.034-.44.572-.895 1.095-1.36 1.565-.455-.47-.91-.993-1.36-1.565z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* HTML5 Logo */}\n      <div className=\"absolute w-8 h-8 text-orange-500\" style={{ left: '8%', top: '35%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M1.5 0h21l-1.91 21.563L11.977 24l-8.564-2.438L1.5 0zm7.031 9.75l-.232-2.718 10.059.003.23-2.622L5.412 4.41l.698 8.01h9.126l-.326 3.426-2.91.804-2.955-.81-.188-2.11H6.248l.33 4.171L12 19.351l5.379-1.443.744-8.157H8.531z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* CSS3 Logo */}\n      <div className=\"absolute w-6 h-6 text-blue-600\" style={{ left: '88%', top: '38%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M1.5 0h21l-1.91 21.563L11.977 24l-8.565-2.438L1.5 0zm17.09 4.413L5.41 4.41l.213 2.622 10.125.002-.255 2.716h-6.64l.24 2.573h6.182l-.366 3.523-2.91.804-2.956-.81-.188-2.11h-2.61l.29 3.855L12 19.288l5.373-1.53L18.59 4.414z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Node.js Logo */}\n      <div className=\"absolute w-8 h-8 text-green-500\" style={{ left: '12%', top: '45%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M11.998,24c-0.321,0-0.641-0.084-0.922-0.247l-2.936-1.737c-0.438-0.245-0.224-0.332-0.08-0.383 c0.585-0.203,0.703-0.25,1.328-0.604c0.065-0.037,0.151-0.023,0.218,0.017l2.256,1.339c0.082,0.045,0.197,0.045,0.272,0l8.795-5.076 c0.082-0.047,0.134-0.141,0.134-0.238V6.921c0-0.099-0.053-0.192-0.137-0.242l-8.791-5.072c-0.081-0.047-0.189-0.047-0.271,0 L3.075,6.68C2.99,6.729,2.936,6.825,2.936,6.921v10.15c0,0.097,0.054,0.189,0.139,0.235l2.409,1.392 c1.307,0.654,2.108-0.116,2.108-0.89V7.787c0-0.142,0.114-0.253,0.256-0.253h1.115c0.139,0,0.255,0.112,0.255,0.253v10.021 c0,1.745-0.95,2.745-2.604,2.745c-0.508,0-0.909,0-2.026-0.551L2.28,18.675c-0.57-0.329-0.922-0.945-0.922-1.604V6.921 c0-0.659,0.353-1.275,0.922-1.603l8.795-5.082c0.557-0.315,1.296-0.315,1.848,0l8.794,5.082c0.570,0.329,0.924,0.944,0.924,1.603 v10.15c0,0.659-0.354,1.273-0.924,1.604l-8.794,5.078C12.643,23.916,12.324,24,11.998,24z M19.099,13.993 c0-1.9-1.284-2.406-3.987-2.763c-2.731-0.361-3.009-0.548-3.009-1.187c0-0.528,0.235-1.233,2.258-1.233 c1.807,0,2.473,0.389,2.747,1.607c0.024,0.115,0.129,0.199,0.247,0.199h1.141c0.071,0,0.138-0.031,0.186-0.081 c0.048-0.054,0.074-0.123,0.067-0.196c-0.177-2.098-1.571-3.076-4.388-3.076c-2.508,0-4.004,1.058-4.004,2.833 c0,1.925,1.488,2.457,3.895,2.695c2.88,0.282,3.103,0.703,3.103,1.269c0,0.983-0.789,1.402-2.642,1.402 c-2.327,0-2.839-0.584-3.011-1.742c-0.02-0.124-0.126-0.215-0.253-0.215h-1.137c-0.141,0-0.254,0.112-0.254,0.253 c0,1.482,0.806,3.248,4.655,3.248C17.501,17.007,19.099,15.91,19.099,13.993z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Vue.js Logo */}\n      <div className=\"absolute w-6 h-6 text-green-400\" style={{ left: '82%', top: '48%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M24,1.61H14.06L12,5.16,9.94,1.61H0L12,22.39ZM12,14.08,5.16,2.23H9.59L12,6.41l2.41-4.18h4.43Z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Angular Logo */}\n      <div className=\"absolute w-8 h-8 text-red-500\" style={{ left: '6%', top: '55%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M9.93 12.645h4.134L12 7.98l-2.07 4.665zm.002-9.644L0 2.508 1.731 20.6l10.267 3.4 10.267-3.4L24 2.508 9.932 1.001zM18.535 18.239h-2.766l-1.429-3.576H9.641l-1.429 3.576H5.465L11.999 5.1l6.536 13.139z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Docker Logo */}\n      <div className=\"absolute w-6 h-6 text-blue-400\" style={{ left: '86%', top: '58%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M13.983 11.078h2.119a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.119a.185.185 0 00-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 00.186-.186V3.574a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.186m0 2.716h2.118a.187.187 0 00.186-.186V6.29a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.887c0 .102.082.185.185.186m-2.93 0h2.12a.186.186 0 00.184-.186V6.29a.185.185 0 00-.185-.185H8.1a.185.185 0 00-.185.185v1.887c0 .102.083.185.185.186m-2.964 0h2.119a.186.186 0 00.185-.186V6.29a.185.185 0 00-.185-.185H5.136a.186.186 0 00-.186.185v1.887c0 .102.084.185.186.186m5.893 2.715h2.118a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 00.185-.185V9.006a.185.185 0 00-.184-.186h-2.12a.186.186 0 00-.186.186v1.887c0 .102.084.185.186.185m-2.92 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.082.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338 0-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 00-.75.748 11.376 11.376 0 00.692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983 0 1.938-.089 2.835-.266a11.192 11.192 0 003.090-.142c.94-.4 1.818-.99 2.604-1.75.677-.656 1.248-1.44 1.696-2.33a4.73 4.73 0 00.415-.988h.317c1.237 0 1.996-.409 2.394-.801.262-.262.45-.6.559-.991l.099-.291z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Rust Logo */}\n      <div className=\"absolute w-8 h-8 text-orange-600\" style={{ left: '10%', top: '88%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M23.8346 11.7033l-1.0073-.6236a13.7268 13.7268 0 00-.0283-.2936l.8656-.8069a.3483.3483 0 00-.1154-.5938l-1.3108-.5154a13.7759 13.7759 0 00-.0857-.2857l.7107-.9395a.3462.3462 0 00-.2051-.5644l-1.3669-.2935a14.6027 14.6027 0 00-.1417-.2714l.5458-1.0415a.3486.3486 0 00-.2929-.5338l-1.4119-.0648c-.0622-.0890-.1258-.1769-.1911-.2636l.3749-1.1212a.3462.3462 0 00-.3764-.4951l-1.4119.1622a15.5295 15.5295 0 00-.2357-.2357l.1622-1.4119a.3462.3462 0 00-.4951-.3764l-1.1212.3749c-.0867-.0653-.1746-.1289-.2636-.1911l-.0648-1.4119a.3486.3486 0 00-.5338-.2929L14.6027 2.57a14.6027 14.6027 0 00-.2714-.1417l-.2935-1.3669a.3462.3462 0 00-.5644-.2051l-.9395.7107a13.7759 13.7759 0 00-.2857-.0857L11.7033.1654a.3483.3483 0 00-.5938-.1154l-.8069.8656a13.7268 13.7268 0 00-.2936-.0283L9.3854.8346a.3483.3483 0 00-.5938.1154l-.5154 1.3108a13.7759 13.7759 0 00-.2857.0857l-.9395-.7107a.3462.3462 0 00-.5644.2051l-.2935 1.3669a14.6027 14.6027 0 00-.2714.1417L4.57 2.0346a.3486.3486 0 00-.5338.2929l-.0648 1.4119c-.0890.0622-.1769.1258-.2636.1911l-1.1212-.3749a.3462.3462 0 00-.4951.3764l.1622 1.4119a15.5295 15.5295 0 00-.2357.2357L.6236 5.2967a.3462.3462 0 00-.3764.4951l.3749 1.1212c-.0653.0867-.1289.1746-.1911.2636l-1.4119.0648a.3486.3486 0 00-.2929.5338l.5458 1.0415a14.6027 14.6027 0 00-.1417.2714L.8346 9.3854a.3462.3462 0 00-.2051.5644l.7107.9395a13.7759 13.7759 0 00-.0857.2857L.1654 12.2967a.3483.3483 0 00-.1154.5938l.8656.8069a13.7268 13.7268 0 00-.0283.2936L.1654 14.7033a.3483.3483 0 00.1154.5938l1.3108.5154a13.7759 13.7759 0 00.0857.2857l-.7107.9395a.3462.3462 0 00.2051.5644l1.3669.2935a14.6027 14.6027 0 00.1417.2714l-.5458 1.0415a.3486.3486 0 00.2929.5338l1.4119.0648c.0622.0890.1258.1769.1911.2636l-.3749 1.1212a.3462.3462 0 00.3764.4951l1.4119-.1622a15.5295 15.5295 0 00.2357.2357l-.1622 1.4119a.3462.3462 0 00.4951.3764l1.1212-.3749c.0867.0653.1746.1289.2636.1911l.0648 1.4119a.3486.3486 0 00.5338.2929l1.0415-.5458a14.6027 14.6027 0 00.2714.1417l.2935 1.3669a.3462.3462 0 00.5644.2051l.9395-.7107a13.7759 13.7759 0 00.2857.0857l.5154 1.3108a.3483.3483 0 00.5938.1154l.8069-.8656a13.7268 13.7268 0 00.2936.0283l.6236 1.0073a.3483.3483 0 00.5938-.1154l.5154-1.3108a13.7759 13.7759 0 00.2857-.0857l.9395.7107a.3462.3462 0 00.5644-.2051l.2935-1.3669a14.6027 14.6027 0 00.2714-.1417l1.0415.5458a.3486.3486 0 00.5338-.2929l.0648-1.4119c.0890-.0622.1769-.1258.2636-.1911l1.1212.3749a.3462.3462 0 00.4951-.3764l-.1622-1.4119a15.5295 15.5295 0 00.2357-.2357l1.4119.1622a.3462.3462 0 00.3764-.4951l-.3749-1.1212c.0653-.0867.1289-.1746.1911-.2636l1.4119-.0648a.3486.3486 0 00.2929-.5338l-.5458-1.0415a14.6027 14.6027 0 00.1417-.2714l1.3669-.2935a.3462.3462 0 00.2051-.5644l-.7107-.9395a13.7759 13.7759 0 00.0857-.2857l1.3108-.5154a.3483.3483 0 00.1154-.5938l-.8656-.8069a13.7268 13.7268 0 00.0283-.2936l1.0073-.6236a.3483.3483 0 000-.5938zM12 18.0234c-3.3281 0-6.0234-2.6953-6.0234-6.0234S8.6719 5.9766 12 5.9766s6.0234 2.6953 6.0234 6.0234S15.3281 18.0234 12 18.0234z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Go Logo */}\n      <div className=\"absolute w-6 h-6 text-cyan-500\" style={{ left: '80%', top: '92%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M1.811 10.231c-.047 0-.058-.023-.035-.059l.246-.315c.023-.035.081-.058.128-.058h4.172c.046 0 .058.035.035.07l-.199.303c-.023.036-.082.07-.117.07zM.047 11.306c-.047 0-.059-.023-.035-.058l.245-.316c.023-.035.082-.058.129-.058h5.328c.047 0 .07.035.058.07l-.093.28c-.012.047-.058.07-.105.070zM2.828 12.394c-.047 0-.059-.035-.035-.07l.163-.292c.023-.035.070-.07.117-.07h2.337c.047 0 .070.035.070.082l-.023.28c0 .047-.047.082-.082.082zm8.967-2.307c-1.225 0-1.956.547-1.956 1.4 0 .878.729 1.365 1.933 1.365.808 0 1.538-.187 2.26-.457l-.315-1.108c-.512.222-.99.316-1.421.316-.573 0-.878-.187-.878-.55 0-.362.293-.550.878-.550.421 0 .909.117 1.421.339l.315-1.108c-.73-.292-1.538-.457-2.237-.457zm7.549 0c-1.225 0-1.956.547-1.956 1.4 0 .878.729 1.365 1.933 1.365.808 0 1.538-.187 2.26-.457l-.315-1.108c-.512.222-.99.316-1.421.316-.573 0-.878-.187-.878-.55 0-.362.293-.550.878-.550.421 0 .909.117 1.421.339l.315-1.108c-.73-.292-1.538-.457-2.237-.457zm-4.154.023c-.023 0-.047.023-.047.047v3.315c0 .023.024.047.047.047h1.26c.024 0 .047-.024.047-.047V10.11c0-.024-.023-.047-.047-.047zm-.245-1.4c-.024 0-.047.024-.047.047v.902c0 .024.023.047.047.047h1.26c.024 0 .047-.023.047-.047v-.902c0-.023-.023-.047-.047-.047z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n\n      {/* Coding Symbols */}\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '25%', top: '18%' }}>{ }</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '65%', top: '22%' }}>[ ]</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '35%', top: '32%' }}>( )</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '55%', top: '35%' }}>&lt; &gt;</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '45%', top: '42%' }}>;</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '25%', top: '52%' }}>:</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '65%', top: '55%' }}>→</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '35%', top: '62%' }}>===</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '55%', top: '68%' }}>!=</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '15%', top: '75%' }}>&&</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '75%', top: '78%' }}>||</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '45%', top: '85%' }}>++</div>\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '25%', top: '92%' }}>--</div>\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '65%', top: '88%' }}>?:</div>\n\n      {/* Additional Programming Language Icons */}\n      {/* Java Logo */}\n      <div className=\"absolute w-5 h-5 text-red-600\" style={{ left: '40%', top: '15%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M8.851 18.56s-.917.534.653.714c1.902.218 2.874.187 4.969-.211 0 0 .552.346 1.321.646-4.699 2.013-10.633-.118-6.943-1.149M8.276 15.933s-1.028.761.542.924c2.032.209 3.636.227 6.413-.308 0 0 .384.389.987.602-5.679 1.661-12.007.13-7.942-1.218M13.116 11.475c1.158 1.333-.304 2.533-.304 2.533s2.939-1.518 1.589-3.418c-1.261-1.772-2.228-2.652 3.007-5.688 0-.001-8.216 2.051-4.292 6.573M19.33 20.504s.679.559-.747.991c-2.712.822-11.288 1.069-13.669.033-.856-.373.75-.89 1.254-.998.527-.114.828-.093.828-.093-.953-.671-6.156 1.317-2.643 1.887 9.58 1.553 17.462-.7 14.977-1.82M9.292 13.21s-4.362 1.036-1.544 1.412c1.189.159 3.561.123 5.77-.062 1.806-.152 3.618-.477 3.618-.477s-.637.272-1.098.587c-4.429 1.165-12.986.623-10.522-.568 2.082-1.006 3.776-.892 3.776-.892M17.116 17.584c4.503-2.34 2.421-4.589.968-4.285-.355.074-.515.138-.515.138s.132-.207.385-.297c2.875-1.011 5.086 2.981-.928 4.562 0-.001.07-.062.09-.118M14.401 0s2.494 2.494-2.365 6.33c-3.896 3.077-.888 4.832-.001 6.836-2.274-2.053-3.943-3.858-2.824-5.539 1.644-2.469 6.197-3.665 5.19-7.627M9.734 23.924c4.322.277 10.959-.153 11.116-2.198 0 0-.302.775-3.572 1.391-3.688.694-8.239.613-10.937.168 0-.001.553.457 3.393.639\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* C++ Logo */}\n      <div className=\"absolute w-5 h-5 text-blue-700\" style={{ left: '60%', top: '25%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M22.394 6c-.167-.29-.398-.543-.652-.69L12.926.22c-.509-.294-1.34-.294-1.848 0L2.26 5.31c-.508.293-.923 1.013-.923 1.6v10.18c0 .294.104.62.271.91.167.29.398.543.652.69l8.816 5.09c.508.293 1.34.293 1.848 0l8.816-5.09c.254-.147.485-.4.652-.69.167-.29.27-.616.27-.91V6.91c.003-.294-.1-.62-.268-.91zM12 19.11c-3.92 0-7.109-3.19-7.109-7.11 0-3.92 3.19-7.11 7.109-7.11a7.133 7.133 0 016.156 3.553l-3.076 1.78a3.567 3.567 0 00-3.08-1.78A3.56 3.56 0 008.444 12 3.56 3.56 0 0012 15.555a3.57 3.57 0 003.08-1.778l3.078 1.78A7.135 7.135 0 0112 19.11zm7.11-6.715h-.79V11.61h-.79v.785h-.79v.79h.79v.785h.79v-.785h.79zm2.962 0h-.79V11.61h-.79v.785h-.79v.79h.79v.785h.79v-.785h.79z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* PHP Logo */}\n      <div className=\"absolute w-5 h-5 text-purple-600\" style={{ left: '30%', top: '65%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M7.01 10.207h-.944l-.515 2.648h.838c.556 0 .982-.122 1.292-.391.313-.27.503-.644.572-1.115.05-.33-.044-.611-.292-.92-.248-.308-.646-.459-1.191-.459l-.76.237zm12.39 0h-.944l-.515 2.648h.838c.556 0 .982-.122 1.292-.391.313-.27.503-.644.572-1.115.05-.33-.044-.611-.292-.92-.248-.308-.646-.459-1.191-.459l-.76.237zM24 12c0 6.627-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0s12 5.373 12 12zM5.394 10.123c.191-.731.295-1.283.295-1.692 0-.484-.169-.854-.498-1.119-.329-.264-.784-.396-1.365-.396H2.3l-.8 4.102h1.308l.157-.806h.994c.642 0 1.099-.075 1.374-.225.275-.15.478-.398.61-.744l-.549.88zm3.604 1.595c-.225-.36-.337-.94-.337-1.736 0-.767.064-1.351.192-1.754.128-.402.34-.732.635-.99.295-.258.688-.421 1.18-.488.492-.067 1.046-.1 1.662-.1h1.08l-.8 4.102h-1.308l.157-.806h-.994c-.642 0-1.099.075-1.374.225-.275.15-.478.398-.61.744l.517-.197zm7.04-1.595c.191-.731.295-1.283.295-1.692 0-.484-.169-.854-.498-1.119-.329-.264-.784-.396-1.365-.396h-1.526l-.8 4.102h1.308l.157-.806h.994c.642 0 1.099-.075 1.374-.225.275-.15.478-.398.61-.744l-.549.88zm3.604 1.595c-.225-.36-.337-.94-.337-1.736 0-.767.064-1.351.192-1.754.128-.402.34-.732.635-.99.295-.258.688-.421 1.18-.488.492-.067 1.046-.1 1.662-.1h1.08l-.8 4.102h-1.308l.157-.806h-.994c-.642 0-1.099.075-1.374.225-.275.15-.478.398-.61.744l.517-.197z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Swift Logo */}\n      <div className=\"absolute w-5 h-5 text-orange-500\" style={{ left: '70%', top: '72%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M7.508 0c-.287 0-.573 0-.86.002-.86.01-1.717.04-2.573.09C2.718.17 1.365.32.458 1.227c-.907.907-1.057 2.26-1.135 3.617C-.737 5.7-.737 6.557-.737 7.415v9.17c0 .858 0 1.715.06 2.572.078 1.357.228 2.71 1.135 3.617.907.907 2.26 1.057 3.617 1.135.856.06 1.713.06 2.571.06h9.17c.858 0 1.715 0 2.572-.06 1.357-.078 2.71-.228 3.617-1.135.907-.907 1.057-2.26 1.135-3.617.06-.857.06-1.714.06-2.572v-9.17c0-.858 0-1.715-.06-2.572-.078-1.357-.228-2.71-1.135-3.617C20.553.32 19.2.17 17.843.092 16.987.032 16.13.032 15.272.032H7.508zm-.787 4.227c.04-.04.1-.06.16-.06.787 0 1.574.32 2.361.96 1.574 1.28 3.148 3.148 4.722 5.803-1.574-1.574-3.148-2.854-4.722-3.841-.787-.493-1.574-.787-2.361-.787-.06 0-.12-.02-.16-.06-.04-.04-.06-.1-.06-.16s.02-.12.06-.16zm8.525 1.574c.787.787 1.574 1.574 2.361 2.361-2.361-1.574-4.722-2.361-7.083-2.361-.787 0-1.574.04-2.361.12 1.574-.787 3.148-1.28 4.722-1.28 1.574 0 2.361.787 2.361 1.16zm-8.525 3.148c.787 0 1.574.293 2.361.787 1.574.987 3.148 2.267 4.722 3.841-1.574-2.655-3.148-4.523-4.722-5.803-.787-.64-1.574-.96-2.361-.96-.06 0-.12.02-.16.06-.04.04-.06.1-.06.16s.02.12.06.16c.04.04.1.06.16.06z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Kotlin Logo */}\n      <div className=\"absolute w-5 h-5 text-purple-500\" style={{ left: '20%', top: '82%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M24 24H0V0h24L12 12 24 24z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      {/* Git Logo */}\n      <div className=\"absolute w-5 h-5 text-orange-600\" style={{ left: '50%', top: '95%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M23.546 10.93L13.067.452c-.604-.603-1.582-.603-2.188 0L8.708 2.627l2.76 2.76c.645-.215 1.379-.07 1.889.441.516.515.658 1.258.438 1.9l2.658 2.66c.645-.223 1.387-.078 1.9.435.721.72.721 1.884 0 2.604-.719.719-1.881.719-2.6 0-.539-.541-.674-1.337-.404-1.996L12.86 8.955v6.525c.176.086.342.203.488.348.713.721.713 1.883 0 2.6-.719.721-1.889.721-2.609 0-.719-.719-.719-1.879 0-2.598.182-.18.387-.316.605-.406V8.835c-.217-.091-.424-.222-.6-.401-.545-.545-.676-1.342-.396-2.009L7.636 3.7.45 10.881c-.6.605-.6 1.584 0 2.189l10.48 10.477c.604.604 1.582.604 2.186 0l10.43-10.43c.605-.603.605-1.582 0-2.187\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n    </div>\n\n    {/* Grid background extending behind header */}\n    <div\n      className=\"absolute inset-0 -top-20 opacity-20\"\n      style={{\n        backgroundImage: `\n          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '40px 40px'\n      }}\n    />\n    {/* Evenly spaced dashed grid lines that frame content without conflicting */}\n    <div className=\"absolute inset-0 -top-20 pointer-events-none z-10\">\n      {/* Left vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          left: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)',\n          maskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)',\n          WebkitMaskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)'\n        }}\n      />\n\n      {/* Right vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          right: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)',\n          maskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)',\n          WebkitMaskImage: 'linear-gradient(to bottom, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 100%)'\n        }}\n      />\n\n      {/* Top horizontal dashed line - above content */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '20px',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Middle horizontal line - between announcement and headline */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '20%',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Bottom horizontal line - below buttons */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          bottom: '20px',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n    </div>\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"flex flex-col items-center justify-center gap-8 pt-6 pb-8 lg:pt-12 lg:pb-12\">\n        <div>\n          <Feed queries={[blog.latestPostQuery]}>\n            {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n            {async ([data]: [any]) => {\n              'use server';\n\n              return (\n                <Button variant=\"secondary\" size=\"sm\" className=\"gap-4 bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/20 hover:from-orange-500/20 hover:to-orange-600/20 hover:border-orange-500/30 text-orange-400 hover:text-orange-300\" asChild>\n                  <Link href={`/blog/${data.blog.posts.item?._slug}`}>\n                    {dictionary.web.home.hero.announcement}{' '}\n                    <MoveRight className=\"h-4 w-4 text-orange-400\" />\n                  </Link>\n                </Button>\n              );\n            }}\n          </Feed>\n        </div>\n        <div className=\"flex flex-col gap-6 relative\">\n          {/* Natural flowing white gradient background extending behind header */}\n          <div className=\"absolute inset-0 -top-32 bg-gradient-to-b from-white/8 via-white/8 to-transparent blur-3xl -z-10 scale-150\" />\n          <div className=\"absolute inset-0 -top-24 bg-gradient-radial from-white/8 via-white/8 to-transparent blur-2xl -z-10 scale-125\" />\n          <AnimatedTitle />\n          <p className=\"max-w-3xl mx-auto text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl relative z-10\">\n            Meet Cubent.Dev, your AI coding partner inside the editor. Generate code, solve bugs, document faster, and build smarter with simple language.\n          </p>\n        </div>\n        <div className=\"flex flex-col sm:flex-row gap-4 mt-8\">\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n              </svg>\n              VS Code\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://plugins.jetbrains.com/plugin/cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\" fill=\"currentColor\"/>\n                <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" fill=\"white\"/>\n                <text x=\"3\" y=\"8\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">JET</text>\n                <text x=\"3\" y=\"15\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">BRAINS</text>\n                <rect x=\"3\" y=\"18\" width=\"10\" height=\"1.5\" fill=\"black\"/>\n              </svg>\n              JetBrains\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AACA;;;;;;;;;;MAqNa,wEAAO,CAAC,KAAY;;IAGnB,qBACE,6VAAC,2JAAA,CAAA,SAAM;QAAC,SAAQ;QAAY,MAAK;QAAK,WAAU;QAAmM,OAAO;kBACxP,cAAA,6VAAC,2QAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO;;;gBACR;8BACxC,6VAAC,oSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B;AAzNL,MAAM,OAAO,OAAO,EAAE,UAAU,EAAa,iBAClD,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAA<PERSON>,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAChF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA+1B,MAAK;;;;;;;;;;;;;;;;kCAIh3B,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAm4C,MAAK;;;;;;;;;;;;;;;;kCAIp5C,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA+pC,MAAK;;;;;;;;;;;;;;;;kCAIhrC,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAorF,MAAK;;;;;;;;;;;;;;;;kCAIrsF,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAChF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA6N,MAAK;;;;;;;;;;;;;;;;kCAI9O,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA+N,MAAK;;;;;;;;;;;;;;;;kCAIhP,6VAAC;wBAAI,WAAU;wBAAkC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAChF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA69C,MAAK;;;;;;;;;;;;;;;;kCAI9+C,6VAAC;wBAAI,WAAU;wBAAkC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAChF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA+F,MAAK;;;;;;;;;;;;;;;;kCAIhH,6VAAC;wBAAI,WAAU;wBAAgC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAC7E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAwM,MAAK;;;;;;;;;;;;;;;;kCAIzN,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAsoD,MAAK;;;;;;;;;;;;;;;;kCAIvpD,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAo5F,MAAK;;;;;;;;;;;;;;;;kCAIr6F,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAkrC,MAAK;;;;;;;;;;;;;;;;kCAKnsC,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;;;;;;kCAC5F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAI9F,6VAAC;wBAAI,WAAU;wBAAgC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC9E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAypC,MAAK;;;;;;;;;;;;;;;;kCAI1qC,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA2pB,MAAK;;;;;;;;;;;;;;;;kCAI5qB,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA0wC,MAAK;;;;;;;;;;;;;;;;kCAI3xC,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAkmC,MAAK;;;;;;;;;;;;;;;;kCAInnC,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAA6B,MAAK;;;;;;;;;;;;;;;;kCAI9C,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAslB,MAAK;;;;;;;;;;;;;;;;;;;;;;0BAMzmB,6VAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;QAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAGF,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM;4BACN,YAAY;4BACZ,WAAW;4BACX,iBAAiB;wBACnB;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,YAAY;4BACZ,WAAW;4BACX,iBAAiB;wBACnB;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;;;;;;;0BAIJ,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;sCACC,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,eAAe;iCAAC;0CAElC,8VAAA;;;;;;;;;;;sCAcL,6VAAC;4BAAI,WAAU;;8CAEb,6VAAC;oCAAI,WAAU;;;;;;8CACf,6VAAC;oCAAI,WAAU;;;;;;8CACf,6VAAC,gLAAA,CAAA,gBAAa;;;;;8CACd,6VAAC;oCAAE,WAAU;8CAAsH;;;;;;;;;;;;sCAIrI,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,2JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoE,QAAO;wCAAS,KAAI;wCAAsB,WAAU;;0DACjI,6VAAC;gDAAI,WAAU;gDAAwB,SAAQ;gDAAY,MAAK;0DAC9D,cAAA,6VAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;0DAEN,6VAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,6VAAC,2JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAA8C,QAAO;wCAAS,KAAI;wCAAsB,WAAU;;0DAC3G,6VAAC;gDAAI,WAAU;gDAAwB,SAAQ;gDAAY,MAAK;;kEAC9D,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;;;;;;kEAC9C,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;;;;;;kEAC9C,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,UAAS;wDAAI,YAAW;wDAAO,MAAK;wDAAQ,YAAW;kEAAoB;;;;;;kEAC7F,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAK,UAAS;wDAAI,YAAW;wDAAO,MAAK;wDAAQ,YAAW;kEAAoB;;;;;;kEAC9F,6VAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAK,OAAM;wDAAK,QAAO;wDAAM,MAAK;;;;;;;;;;;;4CAC5C;0DAEN,6VAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'Cubent';\nconst author: Metadata['authors'] = {\n  name: '<PERSON><PERSON><PERSON>',\n  url: 'https://cubent.dev/',\n};\nconst publisher = 'Cubent';\nconst twitterHandle = '@cubent';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx <module evaluation>\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uFACA", "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx <module evaluation>\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sFACA", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oFACA", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gEACA", "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/model-providers.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ModelProviders = () => {\n  return (\n    <div className=\"w-full py-20 lg:py-32\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex flex-col items-center justify-center gap-12\">\n          {/* Header */}\n          <div className=\"flex flex-col gap-4 text-center max-w-4xl mx-auto\">\n            <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n              First-class support for every major model provider\n            </h2>\n            <p className=\"max-w-2xl mx-auto text-lg text-muted-foreground leading-relaxed tracking-tight\">\n              Connect with the AI models you trust. <PERSON><PERSON><PERSON> works seamlessly with all leading providers.\n            </p>\n          </div>\n\n          {/* Model Provider Logos */}\n          <div className=\"w-full max-w-6xl mx-auto\">\n            <div className=\"flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16\">\n\n              {/* OpenAI */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png\"\n                    alt=\"OpenAI\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenAI</span>\n              </div>\n\n              {/* Anthropic */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png\"\n                    alt=\"Anthropic\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Anthropic</span>\n              </div>\n\n              {/* Google */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/gemini-color.png\"\n                    alt=\"Google Gemini\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Google</span>\n              </div>\n\n              {/* Cohere */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/cohere-color.png\"\n                    alt=\"Cohere\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Cohere</span>\n              </div>\n\n              {/* Mistral */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral-color.png\"\n                    alt=\"Mistral\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Mistral</span>\n              </div>\n\n              {/* OpenRouter */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openrouter.png\"\n                    alt=\"OpenRouter\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenRouter</span>\n              </div>\n\n            </div>\n          </div>\n\n\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAAiB;IAC5B,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6VAAC;gCAAE,WAAU;0CAAiF;;;;;;;;;;;;kCAMhG,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CAGb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E", "debugId": null}}, {"offset": {"line": 1792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx <module evaluation>\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/page.tsx"], "sourcesContent": ["import { showBetaFeature } from '@repo/feature-flags';\nimport { getDictionary } from '@repo/internationalization';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport { Community } from './components/community';\nimport { CTA } from './components/cta';\nimport { Download } from './components/download';\nimport { FAQ } from './components/faq';\n\nimport { Hero } from './components/hero';\nimport { Mockup } from './components/mockup';\nimport { ModelProviders } from './components/model-providers';\nimport { Stats } from './components/stats';\nimport { Testimonials } from './components/testimonials';\nimport { TrustedBy } from './components/trusted-by';\n\ntype HomeProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: HomeProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return createMetadata(dictionary.web.home.meta);\n};\n\nconst Home = async ({ params }: HomeProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n  const betaFeature = await showBetaFeature();\n\n  return (\n    <>\n      {betaFeature && (\n        <div className=\"w-full bg-black py-2 text-center text-white\">\n          Beta feature now available\n        </div>\n      )}\n      <Hero dictionary={dictionary} />\n      <TrustedBy dictionary={dictionary} />\n      <Mockup />\n      <ModelProviders />\n      <Community dictionary={dictionary} />\n      <Download />\n    </>\n  );\n};\n\nexport default Home;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AAGA;;;;;;;;;;;AAQO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;AAChD;AAEA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAa;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,qBACE;;YACG,6BACC,6VAAC;gBAAI,WAAU;0BAA8C;;;;;;0BAI/D,6VAAC,mKAAA,CAAA,OAAI;gBAAC,YAAY;;;;;;0BAClB,6VAAC,4KAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,qKAAA,CAAA,SAAM;;;;;0BACP,6VAAC,iLAAA,CAAA,iBAAc;;;;;0BACf,6VAAC,wKAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,uKAAA,CAAA,WAAQ;;;;;;;AAGf;uCAEe", "debugId": null}}]}