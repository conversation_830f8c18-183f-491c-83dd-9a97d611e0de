{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/prisma.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Prisma\", \"fileTypes\": [\"prisma\"], \"name\": \"prisma\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#model_block_definition\" }, { \"include\": \"#config_block_definition\" }, { \"include\": \"#enum_block_definition\" }, { \"include\": \"#type_definition\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.array\", \"patterns\": [{ \"include\": \"#value\" }] }, \"assignment\": { \"patterns\": [{ \"begin\": \"^\\\\s*(\\\\w+)\\\\s*(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" }, \"2\": { \"name\": \"keyword.operator.terraform\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"include\": \"#value\" }, { \"include\": \"#double_comment_inline\" }] }] }, \"attribute\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.attribute.prisma\" } }, \"match\": \"(@@?[\\\\w\\\\.]+)\", \"name\": \"source.prisma.attribute\" }, \"attribute_with_arguments\": { \"begin\": \"(@@?[\\\\w\\\\.]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.attribute.prisma\" }, \"2\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.attribute.with_arguments\", \"patterns\": [{ \"include\": \"#named_argument\" }, { \"include\": \"#value\" }] }, \"boolean\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.prisma\" }, \"config_block_definition\": { \"begin\": \"^\\\\s*(generator|datasource)\\\\s+([A-Za-z][\\\\w]*)\\\\s+({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.config.prisma\" }, \"2\": { \"name\": \"entity.name.type.config.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#assignment\" }] }, \"double_comment\": { \"begin\": \"//\", \"end\": \"$\\\\n?\", \"name\": \"comment.prisma\" }, \"double_comment_inline\": { \"match\": \"//[^\\\\n]*\", \"name\": \"comment.prisma\" }, \"double_quoted_string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"string.quoted.double.start.prisma\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.end.prisma\" } }, \"name\": \"unnamed\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"match\": \"([\\\\w\\\\-\\\\/\\\\._\\\\\\\\%@:?=]+)\", \"name\": \"string.quoted.double.prisma\" }] }, \"enum_block_definition\": { \"begin\": \"^\\\\s*(enum)\\\\s+([A-Za-z][\\\\w]*)\\\\s+({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.enum.prisma\" }, \"2\": { \"name\": \"entity.name.type.enum.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#enum_value_definition\" }] }, \"enum_value_definition\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" } }, \"match\": \"^\\\\s*(\\\\w+)\\\\s*\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"field_definition\": { \"name\": \"scalar.field\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" }, \"2\": { \"name\": \"invalid.illegal.colon.prisma\" }, \"3\": { \"name\": \"variable.language.relations.prisma\" }, \"4\": { \"name\": \"support.type.primitive.prisma\" }, \"5\": { \"name\": \"keyword.operator.list_type.prisma\" }, \"6\": { \"name\": \"keyword.operator.optional_type.prisma\" }, \"7\": { \"name\": \"invalid.illegal.required_type.prisma\" } }, \"match\": \"^\\\\s*(\\\\w+)(\\\\s*:)?\\\\s+((?!(?:Int|BigInt|String|DateTime|Bytes|Decimal|Float|Json|Boolean)\\\\b)\\\\b\\\\w+)?(Int|BigInt|String|DateTime|Bytes|Decimal|Float|Json|Boolean)?(\\\\[\\\\])?(\\\\?)?(!)?\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"functional\": { \"begin\": \"(\\\\w+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.functional.prisma\" }, \"2\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.functional\", \"patterns\": [{ \"include\": \"#value\" }] }, \"identifier\": { \"patterns\": [{ \"match\": \"\\\\b(\\\\w)+\\\\b\", \"name\": \"support.constant.constant.prisma\" }] }, \"literal\": { \"name\": \"source.prisma.literal\", \"patterns\": [{ \"include\": \"#boolean\" }, { \"include\": \"#number\" }, { \"include\": \"#double_quoted_string\" }, { \"include\": \"#identifier\" }] }, \"map_key\": { \"name\": \"source.prisma.key\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.key.prisma\" }, \"2\": { \"name\": \"punctuation.definition.separator.key-value.prisma\" } }, \"match\": \"(\\\\w+)\\\\s*(:)\\\\s*\" }] }, \"model_block_definition\": { \"begin\": \"^\\\\s*(model|type|view)\\\\s+([A-Za-z][\\\\w]*)\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.model.prisma\" }, \"2\": { \"name\": \"entity.name.type.model.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#field_definition\" }] }, \"named_argument\": { \"name\": \"source.prisma.named_argument\", \"patterns\": [{ \"include\": \"#map_key\" }, { \"include\": \"#value\" }] }, \"number\": { \"match\": \"((0(x|X)[0-9a-fA-F]*)|(\\\\+|-)?\\\\b((\\\\d+\\\\.?\\\\d*)|(\\\\.\\\\d+))((e|E)(\\\\+|-)?\\\\d+)?)([LlFfUuDdg]|UL|ul)?\\\\b\", \"name\": \"constant.numeric.prisma\" }, \"string_interpolation\": { \"patterns\": [{ \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.interpolation.start.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.interpolation.end.prisma\" } }, \"name\": \"source.tag.embedded.source.prisma\", \"patterns\": [{ \"include\": \"#value\" }] }] }, \"triple_comment\": { \"begin\": \"///\", \"end\": \"$\\\\n?\", \"name\": \"comment.prisma\" }, \"type_definition\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.type.prisma\" }, \"2\": { \"name\": \"entity.name.type.type.prisma\" }, \"3\": { \"name\": \"support.type.primitive.prisma\" } }, \"match\": \"^\\\\s*(type)\\\\s+(\\\\w+)\\\\s*=\\\\s*(\\\\w+)\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"value\": { \"name\": \"source.prisma.value\", \"patterns\": [{ \"include\": \"#array\" }, { \"include\": \"#functional\" }, { \"include\": \"#literal\" }] } }, \"scopeName\": \"source.prisma\" });\nvar prisma = [\n  lang\n];\n\nexport { prisma as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;KAAS;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAA0B;QAAG;YAAE,WAAW;QAA2B;QAAG;YAAE,WAAW;QAAyB;QAAG;YAAE,WAAW;QAAmB;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAAkB,QAAQ;QAA0B;QAAG,4BAA4B;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAsB,QAAQ;QAAmC;QAAG,2BAA2B;YAAE,SAAS;YAA0D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAM,OAAO;YAAS,QAAQ;QAAiB;QAAG,yBAAyB;YAAE,SAAS;YAAa,QAAQ;QAAiB;QAAG,wBAAwB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA8B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAkB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,oBAAoB;YAAE,QAAQ;YAAgB,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;gBAA2L;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAAmC;aAAE;QAAC;QAAG,WAAW;YAAE,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,WAAW;YAAE,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,SAAS;gBAAoB;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAqD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,kBAAkB;YAAE,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA2G,QAAQ;QAA0B;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,OAAO;YAAS,QAAQ;QAAiB;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAuC;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,SAAS;YAAE,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;IAAE;IAAG,aAAa;AAAgB;AACh8M,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}