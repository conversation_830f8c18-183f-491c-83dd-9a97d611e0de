{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/json.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON\", \"name\": \"json\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json\" } }, \"name\": \"meta.structure.array.json\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json\" }, \"number\": { \"match\": \"-?(?:0|[1-9]\\\\d*)(?:(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)?\", \"name\": \"constant.numeric.json\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json\" } }, \"name\": \"meta.structure.dictionary.json\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json\" } }, \"name\": \"meta.structure.dictionary.value.json\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json\" } }, \"name\": \"string.json support.type.property-name.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json\" } }, \"name\": \"string.quoted.double.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json\" });\nvar json = [\n  lang\n];\n\nexport { json as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAgD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA6B,QAAQ;QAAyB;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAAwB;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;oBAAuB,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;4BAAyB,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAAqD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAqD;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAA+C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAkD;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AACxiG,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}