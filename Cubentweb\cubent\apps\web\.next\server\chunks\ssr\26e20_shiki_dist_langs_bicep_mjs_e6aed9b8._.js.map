{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/bicep.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Bicep\", \"fileTypes\": [\".bicep\"], \"name\": \"bicep\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comments\" }], \"repository\": { \"array-literal\": { \"begin\": \"\\\\[(?!(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\bfor\\\\b)\", \"end\": \"]\", \"name\": \"meta.array-literal.bicep\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comments\" }] }, \"block-comment\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.bicep\" }, \"comments\": { \"patterns\": [{ \"include\": \"#line-comment\" }, { \"include\": \"#block-comment\" }] }, \"decorator\": { \"begin\": \"@(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*(?=\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b)\", \"end\": \"\", \"name\": \"meta.decorator.bicep\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comments\" }] }, \"directive\": { \"begin\": \"#\\\\b[_a-zA-Z-0-9]+\\\\b\", \"end\": \"$\", \"name\": \"meta.directive.bicep\", \"patterns\": [{ \"include\": \"#directive-variable\" }, { \"include\": \"#comments\" }] }, \"directive-variable\": { \"match\": \"\\\\b[_a-zA-Z-0-9]+\\\\b\", \"name\": \"keyword.control.declaration.bicep\" }, \"escape-character\": { \"match\": \"\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\${)\", \"name\": \"constant.character.escape.bicep\" }, \"expression\": { \"patterns\": [{ \"include\": \"#string-literal\" }, { \"include\": \"#string-verbatim\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#named-literal\" }, { \"include\": \"#object-literal\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#keyword\" }, { \"include\": \"#identifier\" }, { \"include\": \"#function-call\" }, { \"include\": \"#decorator\" }, { \"include\": \"#lambda-start\" }, { \"include\": \"#directive\" }] }, \"function-call\": { \"begin\": \"(\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b)(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\(\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.bicep\" } }, \"end\": \"\\\\)\", \"name\": \"meta.function-call.bicep\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comments\" }] }, \"identifier\": { \"match\": \"\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b(?!(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\()\", \"name\": \"variable.other.readwrite.bicep\" }, \"keyword\": { \"match\": \"\\\\b(metadata|targetScope|resource|module|param|var|output|for|in|if|existing|import|as|type|with|using|extends|func|assert|extension)\\\\b\", \"name\": \"keyword.control.declaration.bicep\" }, \"lambda-start\": { \"begin\": \"(\\\\((?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*(,(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*)*\\\\)|\\\\((?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\)|(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*)(?=(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*=>)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.undefined.bicep\", \"patterns\": [{ \"include\": \"#identifier\" }, { \"include\": \"#comments\" }] } }, \"end\": \"(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*=>\", \"name\": \"meta.lambda-start.bicep\" }, \"line-comment\": { \"match\": \"//.*(?=$)\", \"name\": \"comment.line.double-slash.bicep\" }, \"named-literal\": { \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.bicep\" }, \"numeric-literal\": { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.bicep\" }, \"object-literal\": { \"begin\": \"{\", \"end\": \"}\", \"name\": \"meta.object-literal.bicep\", \"patterns\": [{ \"include\": \"#object-property-key\" }, { \"include\": \"#expression\" }, { \"include\": \"#comments\" }] }, \"object-property-key\": { \"match\": \"\\\\b[_$A-Za-z][_$0-9A-Za-z]*\\\\b(?=(?:[ \\\\t\\\\r\\\\n]|\\\\/\\\\*(?:\\\\*(?!\\\\/)|[^*])*\\\\*\\\\/)*:)\", \"name\": \"variable.other.property.bicep\" }, \"string-literal\": { \"begin\": \"'(?!'')\", \"end\": \"'\", \"name\": \"string.quoted.single.bicep\", \"patterns\": [{ \"include\": \"#escape-character\" }, { \"include\": \"#string-literal-subst\" }] }, \"string-literal-subst\": { \"begin\": \"(?<!\\\\\\\\)(\\\\${)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.template-expression.begin.bicep\" } }, \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.template-expression.end.bicep\" } }, \"name\": \"meta.string-literal-subst.bicep\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comments\" }] }, \"string-verbatim\": { \"begin\": \"'''\", \"end\": \"'''(?!')\", \"name\": \"string.quoted.multi.bicep\", \"patterns\": [] } }, \"scopeName\": \"source.bicep\" });\nvar bicep = [\n  lang\n];\n\nexport { bicep as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;KAAS;IAAE,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAsE,OAAO;YAAK,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;QAAsB;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAyF,OAAO;YAAI,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAyB,OAAO;YAAK,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAwB,QAAQ;QAAoC;QAAG,oBAAoB;YAAE,SAAS;YAA2C,QAAQ;QAAkC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAyF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA2F,QAAQ;QAAiC;QAAG,WAAW;YAAE,SAAS;YAA4I,QAAQ;QAAoC;QAAG,gBAAgB;YAAE,SAAS;YAAwgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAwD,QAAQ;QAA0B;QAAG,gBAAgB;YAAE,SAAS;YAAa,QAAQ;QAAkC;QAAG,iBAAiB;YAAE,SAAS;YAA2B,QAAQ;QAA0B;QAAG,mBAAmB;YAAE,SAAS;YAAQ,QAAQ;QAAyB;QAAG,kBAAkB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAyF,QAAQ;QAAgC;QAAG,kBAAkB;YAAE,SAAS;YAAW,OAAO;YAAK,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,OAAO;YAAY,QAAQ;YAA6B,YAAY,EAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AAC1yI,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}