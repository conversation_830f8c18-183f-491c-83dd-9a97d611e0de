{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/tsv.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"TSV\", \"fileTypes\": [\"tsv\", \"tab\"], \"name\": \"tsv\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"rainbow1\" }, \"2\": { \"name\": \"keyword.rainbow2\" }, \"3\": { \"name\": \"entity.name.function.rainbow3\" }, \"4\": { \"name\": \"comment.rainbow4\" }, \"5\": { \"name\": \"string.rainbow5\" }, \"6\": { \"name\": \"variable.parameter.rainbow6\" }, \"7\": { \"name\": \"constant.numeric.rainbow7\" }, \"8\": { \"name\": \"entity.name.type.rainbow8\" }, \"9\": { \"name\": \"markup.bold.rainbow9\" }, \"10\": { \"name\": \"invalid.rainbow10\" } }, \"match\": \"([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)([^\\\\t]*\\\\t?)\", \"name\": \"rainbowgroup\" }], \"scopeName\": \"text.tsv\" });\nvar tsv = [\n  lang\n];\n\nexport { tsv as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;QAAO;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAW;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmB;gBAAG,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,MAAM;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAsI,QAAQ;QAAe;KAAE;IAAE,aAAa;AAAW;AACztB,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}