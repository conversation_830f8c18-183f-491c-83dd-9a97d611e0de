module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/java.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>java)
});
const lang = Object.freeze({
    "displayName": "Java",
    "name": "java",
    "patterns": [
        {
            "begin": "\\b(package)\\b\\s*",
            "beginCaptures": {
                "1": {
                    "name": "keyword.other.package.java"
                }
            },
            "contentName": "storage.modifier.package.java",
            "end": "\\s*(;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.terminator.java"
                }
            },
            "name": "meta.package.java",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "match": "(?<=\\.)\\s*\\.|\\.(?=\\s*;)",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!_)_(?=\\s*(\\.|;))|\\b\\d+|-+",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "[A-Z]+",
                    "name": "invalid.deprecated.package_name_not_lowercase.java"
                },
                {
                    "match": "\\b(?<!\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\b",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.java"
                }
            ]
        },
        {
            "begin": "\\b(import)\\b\\s*\\b(static)?\\b\\s",
            "beginCaptures": {
                "1": {
                    "name": "keyword.other.import.java"
                },
                "2": {
                    "name": "storage.modifier.java"
                }
            },
            "contentName": "storage.modifier.import.java",
            "end": "\\s*(;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.terminator.java"
                }
            },
            "name": "meta.import.java",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "match": "(?<=\\.)\\s*\\.|\\.(?=\\s*;)",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!\\.)\\s*\\*",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "(?<!_)_(?=\\s*(\\.|;))|\\b\\d+|-+",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\b(?<!\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\b",
                    "name": "invalid.illegal.character_not_allowed_here.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.java"
                },
                {
                    "match": "\\*",
                    "name": "variable.language.wildcard.java"
                }
            ]
        },
        {
            "include": "#comments-javadoc"
        },
        {
            "include": "#code"
        },
        {
            "include": "#module"
        }
    ],
    "repository": {
        "all-types": {
            "patterns": [
                {
                    "include": "#primitive-arrays"
                },
                {
                    "include": "#primitive-types"
                },
                {
                    "include": "#object-types"
                }
            ]
        },
        "annotations": {
            "patterns": [
                {
                    "begin": "((@)\\s*([^\\s(]+))(\\()",
                    "beginCaptures": {
                        "2": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "3": {
                            "name": "storage.type.annotation.java"
                        },
                        "4": {
                            "name": "punctuation.definition.annotation-arguments.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.annotation-arguments.end.bracket.round.java"
                        }
                    },
                    "name": "meta.declaration.annotation.java",
                    "patterns": [
                        {
                            "captures": {
                                "1": {
                                    "name": "constant.other.key.java"
                                },
                                "2": {
                                    "name": "keyword.operator.assignment.java"
                                }
                            },
                            "match": "(\\w*)\\s*(=)"
                        },
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "2": {
                            "name": "storage.modifier.java"
                        },
                        "3": {
                            "name": "storage.type.annotation.java"
                        },
                        "5": {
                            "name": "punctuation.definition.annotation.java"
                        },
                        "6": {
                            "name": "storage.type.annotation.java"
                        }
                    },
                    "match": "(@)(interface)\\s+(\\w*)|((@)\\s*(\\w+))",
                    "name": "meta.declaration.annotation.java"
                }
            ]
        },
        "anonymous-block-and-instance-initializer": {
            "begin": "{",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.block.begin.bracket.curly.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.block.end.bracket.curly.java"
                }
            },
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "anonymous-classes-and-new": {
            "begin": "\\bnew\\b",
            "beginCaptures": {
                "0": {
                    "name": "keyword.control.new.java"
                }
            },
            "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
            "patterns": [
                {
                    "include": "#comments"
                },
                {
                    "include": "#function-call"
                },
                {
                    "include": "#all-types"
                },
                {
                    "begin": "(?<=\\))",
                    "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.inner-class.begin.bracket.curly.java"
                                }
                            },
                            "end": "}",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.inner-class.end.bracket.curly.java"
                                }
                            },
                            "name": "meta.inner-class.java",
                            "patterns": [
                                {
                                    "include": "#class-body"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "(?<=\\])",
                    "end": "(?=;|\\)|\\]|\\.|,|\\?|:|}|\\+|-|\\*|\\/(?!\\/|\\*)|%|!|&|\\||\\^|=)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.array-initializer.begin.bracket.curly.java"
                                }
                            },
                            "end": "}",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.array-initializer.end.bracket.curly.java"
                                }
                            },
                            "name": "meta.array-initializer.java",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "include": "#parens"
                }
            ]
        },
        "assertions": {
            "patterns": [
                {
                    "begin": "\\b(assert)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.assert.java"
                        }
                    },
                    "end": "$",
                    "name": "meta.declaration.assertion.java",
                    "patterns": [
                        {
                            "match": ":",
                            "name": "keyword.operator.assert.expression-separator.java"
                        },
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "class": {
            "begin": "(?=\\w?[\\w\\s-]*\\b(?:class|(?<!@)interface|enum)\\s+[\\w$]+)",
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.class.end.bracket.curly.java"
                }
            },
            "name": "meta.class.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.modifier.java"
                        },
                        "2": {
                            "name": "entity.name.type.class.java"
                        }
                    },
                    "match": "(class|(?<!@)interface|enum)\\s+([\\w$]+)",
                    "name": "meta.class.identifier.java"
                },
                {
                    "begin": "extends",
                    "beginCaptures": {
                        "0": {
                            "name": "storage.modifier.extends.java"
                        }
                    },
                    "end": "(?={|implements|permits)",
                    "name": "meta.definition.class.inherited.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "(implements)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?=\\s*extends|permits|\\{)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "(permits)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.permits.java"
                        }
                    },
                    "end": "(?=\\s*extends|implements|\\{)",
                    "name": "meta.definition.class.permits.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.class.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.class.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#class-body"
                        }
                    ]
                }
            ]
        },
        "class-body": {
            "patterns": [
                {
                    "include": "#comments-javadoc"
                },
                {
                    "include": "#comments"
                },
                {
                    "include": "#enums"
                },
                {
                    "include": "#class"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#static-initializer"
                },
                {
                    "include": "#class-fields-and-methods"
                },
                {
                    "include": "#annotations"
                },
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#member-variables"
                },
                {
                    "include": "#code"
                }
            ]
        },
        "class-fields-and-methods": {
            "patterns": [
                {
                    "begin": "(?==)",
                    "end": "(?=;)",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "include": "#methods"
                }
            ]
        },
        "code": {
            "patterns": [
                {
                    "include": "#annotations"
                },
                {
                    "include": "#comments"
                },
                {
                    "include": "#enums"
                },
                {
                    "include": "#class"
                },
                {
                    "include": "#record"
                },
                {
                    "include": "#anonymous-block-and-instance-initializer"
                },
                {
                    "include": "#try-catch-finally"
                },
                {
                    "include": "#assertions"
                },
                {
                    "include": "#parens"
                },
                {
                    "include": "#constants-and-special-vars"
                },
                {
                    "include": "#numbers"
                },
                {
                    "include": "#anonymous-classes-and-new"
                },
                {
                    "include": "#lambda-expression"
                },
                {
                    "include": "#keywords"
                },
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#method-call"
                },
                {
                    "include": "#function-call"
                },
                {
                    "include": "#variables"
                },
                {
                    "include": "#variables-local"
                },
                {
                    "include": "#objects"
                },
                {
                    "include": "#properties"
                },
                {
                    "include": "#strings"
                },
                {
                    "include": "#all-types"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.period.java"
                },
                {
                    "match": ";",
                    "name": "punctuation.terminator.java"
                }
            ]
        },
        "comments": {
            "patterns": [
                {
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "match": "/\\*\\*/",
                    "name": "comment.block.empty.java"
                },
                {
                    "include": "#comments-inline"
                }
            ]
        },
        "comments-inline": {
            "patterns": [
                {
                    "begin": "/\\*",
                    "captures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "end": "\\*/",
                    "name": "comment.block.java"
                },
                {
                    "begin": "(^[ \\t]+)?(?=//)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.whitespace.comment.leading.java"
                        }
                    },
                    "end": "(?!\\G)",
                    "patterns": [
                        {
                            "begin": "//",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.comment.java"
                                }
                            },
                            "end": "\\n",
                            "name": "comment.line.double-slash.java"
                        }
                    ]
                }
            ]
        },
        "comments-javadoc": {
            "patterns": [
                {
                    "begin": "^\\s*(/\\*\\*)(?!/)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "end": "\\*/",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.comment.java"
                        }
                    },
                    "name": "comment.block.javadoc.java",
                    "patterns": [
                        {
                            "match": "@(author|deprecated|return|see|serial|since|version)\\b",
                            "name": "keyword.other.documentation.javadoc.java"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "variable.parameter.java"
                                }
                            },
                            "match": "(@param)\\s+(\\S+)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "entity.name.type.class.java"
                                }
                            },
                            "match": "(@(?:exception|throws))\\s+(\\S+)"
                        },
                        {
                            "captures": {
                                "1": {
                                    "name": "keyword.other.documentation.javadoc.java"
                                },
                                "2": {
                                    "name": "entity.name.type.class.java"
                                },
                                "3": {
                                    "name": "variable.parameter.java"
                                }
                            },
                            "match": "{(@link)\\s+(\\S+)?#([\\w$]+\\s*\\([^()]*\\)).*?}"
                        }
                    ]
                }
            ]
        },
        "constants-and-special-vars": {
            "patterns": [
                {
                    "match": "\\b(true|false|null)\\b",
                    "name": "constant.language.java"
                },
                {
                    "match": "\\bthis\\b",
                    "name": "variable.language.this.java"
                },
                {
                    "match": "\\bsuper\\b",
                    "name": "variable.language.java"
                }
            ]
        },
        "enums": {
            "begin": "^\\s*([\\w\\s]*)(enum)\\s+(\\w+)",
            "beginCaptures": {
                "1": {
                    "patterns": [
                        {
                            "include": "#storage-modifiers"
                        }
                    ]
                },
                "2": {
                    "name": "storage.modifier.java"
                },
                "3": {
                    "name": "entity.name.type.enum.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.enum.end.bracket.curly.java"
                }
            },
            "name": "meta.enum.java",
            "patterns": [
                {
                    "begin": "\\b(extends)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.extends.java"
                        }
                    },
                    "end": "(?={|\\bimplements\\b)",
                    "name": "meta.definition.class.inherited.classes.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "\\b(implements)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?={|\\bextends\\b)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.enum.begin.bracket.curly.java"
                        }
                    },
                    "end": "(?=})",
                    "patterns": [
                        {
                            "begin": "(?<={)",
                            "end": "(?=;|})",
                            "patterns": [
                                {
                                    "include": "#comments-javadoc"
                                },
                                {
                                    "include": "#comments"
                                },
                                {
                                    "begin": "\\b(\\w+)\\b",
                                    "beginCaptures": {
                                        "1": {
                                            "name": "constant.other.enum.java"
                                        }
                                    },
                                    "end": "(,)|(?=;|})",
                                    "endCaptures": {
                                        "1": {
                                            "name": "punctuation.separator.delimiter.java"
                                        }
                                    },
                                    "patterns": [
                                        {
                                            "include": "#comments-javadoc"
                                        },
                                        {
                                            "include": "#comments"
                                        },
                                        {
                                            "begin": "\\(",
                                            "beginCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.round.java"
                                                }
                                            },
                                            "end": "\\)",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.round.java"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "include": "#code"
                                                }
                                            ]
                                        },
                                        {
                                            "begin": "{",
                                            "beginCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.curly.java"
                                                }
                                            },
                                            "end": "}",
                                            "endCaptures": {
                                                "0": {
                                                    "name": "punctuation.bracket.curly.java"
                                                }
                                            },
                                            "patterns": [
                                                {
                                                    "include": "#class-body"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "include": "#class-body"
                        }
                    ]
                }
            ]
        },
        "function-call": {
            "begin": "([A-Za-z_$][\\w$]*)\\s*(\\()",
            "beginCaptures": {
                "1": {
                    "name": "entity.name.function.java"
                },
                "2": {
                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                }
            },
            "end": "\\)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                }
            },
            "name": "meta.function-call.java",
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "generics": {
            "begin": "<",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.bracket.angle.java"
                }
            },
            "end": ">",
            "endCaptures": {
                "0": {
                    "name": "punctuation.bracket.angle.java"
                }
            },
            "patterns": [
                {
                    "match": "\\b(extends|super)\\b",
                    "name": "storage.modifier.$1.java"
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.type.java"
                        }
                    },
                    "match": "(?<!\\.)([a-zA-Z$_][a-zA-Z0-9$_]*)(?=\\s*<)"
                },
                {
                    "include": "#primitive-arrays"
                },
                {
                    "match": "[a-zA-Z$_][a-zA-Z0-9$_]*",
                    "name": "storage.type.generic.java"
                },
                {
                    "match": "\\?",
                    "name": "storage.type.generic.wildcard.java"
                },
                {
                    "match": "&",
                    "name": "punctuation.separator.types.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.",
                    "name": "punctuation.separator.period.java"
                },
                {
                    "include": "#parens"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "keywords": {
            "patterns": [
                {
                    "match": "\\bthrow\\b",
                    "name": "keyword.control.throw.java"
                },
                {
                    "match": "\\?|:",
                    "name": "keyword.control.ternary.java"
                },
                {
                    "match": "\\b(return|yield|break|case|continue|default|do|while|for|switch|if|else)\\b",
                    "name": "keyword.control.java"
                },
                {
                    "match": "\\b(instanceof)\\b",
                    "name": "keyword.operator.instanceof.java"
                },
                {
                    "match": "(<<|>>>?|~|\\^)",
                    "name": "keyword.operator.bitwise.java"
                },
                {
                    "match": "((&|\\^|\\||<<|>>>?)=)",
                    "name": "keyword.operator.assignment.bitwise.java"
                },
                {
                    "match": "(===?|!=|<=|>=|<>|<|>)",
                    "name": "keyword.operator.comparison.java"
                },
                {
                    "match": "([+*/%-]=)",
                    "name": "keyword.operator.assignment.arithmetic.java"
                },
                {
                    "match": "(=)",
                    "name": "keyword.operator.assignment.java"
                },
                {
                    "match": "(--|\\+\\+)",
                    "name": "keyword.operator.increment-decrement.java"
                },
                {
                    "match": "(-|\\+|\\*|\\/|%)",
                    "name": "keyword.operator.arithmetic.java"
                },
                {
                    "match": "(!|&&|\\|\\|)",
                    "name": "keyword.operator.logical.java"
                },
                {
                    "match": "(\\||&)",
                    "name": "keyword.operator.bitwise.java"
                },
                {
                    "match": "\\b(const|goto)\\b",
                    "name": "keyword.reserved.java"
                }
            ]
        },
        "lambda-expression": {
            "patterns": [
                {
                    "match": "->",
                    "name": "storage.type.function.arrow.java"
                }
            ]
        },
        "member-variables": {
            "begin": "(?=private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)",
            "end": "(?==|;)",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#variables"
                },
                {
                    "include": "#primitive-arrays"
                },
                {
                    "include": "#object-types"
                }
            ]
        },
        "method-call": {
            "begin": "(\\.)\\s*([A-Za-z_$][\\w$]*)\\s*(\\()",
            "beginCaptures": {
                "1": {
                    "name": "punctuation.separator.period.java"
                },
                "2": {
                    "name": "entity.name.function.java"
                },
                "3": {
                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                }
            },
            "end": "\\)",
            "endCaptures": {
                "0": {
                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                }
            },
            "name": "meta.method-call.java",
            "patterns": [
                {
                    "include": "#code"
                }
            ]
        },
        "methods": {
            "begin": "(?!new)(?=[\\w<].*\\s+)(?=([^=/]|/(?!/))+\\()",
            "end": "(})|(?=;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.section.method.end.bracket.curly.java"
                }
            },
            "name": "meta.method.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "begin": "(\\w+)\\s*(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "entity.name.function.java"
                        },
                        "2": {
                            "name": "punctuation.definition.parameters.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.parameters.end.bracket.round.java"
                        }
                    },
                    "name": "meta.method.identifier.java",
                    "patterns": [
                        {
                            "include": "#parameters"
                        },
                        {
                            "include": "#parens"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#generics"
                },
                {
                    "begin": "(?=\\w.*\\s+\\w+\\s*\\()",
                    "end": "(?=\\s+\\w+\\s*\\()",
                    "name": "meta.method.return-type.java",
                    "patterns": [
                        {
                            "include": "#all-types"
                        },
                        {
                            "include": "#parens"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#throws"
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.method.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.method.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "module": {
            "begin": "((open)\\s)?(module)\\s+(\\w+)",
            "beginCaptures": {
                "1": {
                    "name": "storage.modifier.java"
                },
                "3": {
                    "name": "storage.modifier.java"
                },
                "4": {
                    "name": "entity.name.type.module.java"
                }
            },
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.module.end.bracket.curly.java"
                }
            },
            "name": "meta.module.java",
            "patterns": [
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.module.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.module.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#comments-javadoc"
                        },
                        {
                            "match": "\\b(requires|transitive|exports|opens|to|uses|provides|with)\\b",
                            "name": "keyword.module.java"
                        }
                    ]
                }
            ]
        },
        "numbers": {
            "patterns": [
                {
                    "match": "\\b(?<!\\$)0(x|X)((?<!\\.)[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?[Ll]?(?!\\.)|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?\\.?|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)?\\.[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)[Pp][+-]?\\d([0-9_]*\\d)?[FfDd]?)\\b(?!\\$)",
                    "name": "constant.numeric.hex.java"
                },
                {
                    "match": "\\b(?<!\\$)0(b|B)[01]([01_]*[01])?[Ll]?\\b(?!\\$)",
                    "name": "constant.numeric.binary.java"
                },
                {
                    "match": "\\b(?<!\\$)0[0-7]([0-7_]*[0-7])?[Ll]?\\b(?!\\$)",
                    "name": "constant.numeric.octal.java"
                },
                {
                    "match": "(?<!\\$)(\\b\\d([0-9_]*\\d)?\\.\\B(?!\\.)|\\b\\d([0-9_]*\\d)?\\.([Ee][+-]?\\d([0-9_]*\\d)?)[FfDd]?\\b|\\b\\d([0-9_]*\\d)?\\.([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]\\b|\\b\\d([0-9_]*\\d)?\\.(\\d([0-9_]*\\d)?)([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]?\\b|(?<!\\.)\\B\\.\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]?\\b|\\b\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)[FfDd]?\\b|\\b\\d([0-9_]*\\d)?([Ee][+-]?\\d([0-9_]*\\d)?)?[FfDd]\\b|\\b(0|[1-9]([0-9_]*\\d)?)(?!\\.)[Ll]?\\b)(?!\\$)",
                    "name": "constant.numeric.decimal.java"
                }
            ]
        },
        "object-types": {
            "patterns": [
                {
                    "include": "#generics"
                },
                {
                    "begin": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*)([A-Z_]\\w*)\\s*(?=\\[)",
                    "beginCaptures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        },
                        "2": {
                            "name": "storage.type.object.array.java"
                        }
                    },
                    "end": "(?!\\s*\\[)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#parens"
                        }
                    ]
                },
                {
                    "captures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        }
                    },
                    "match": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*[A-Z_]\\w*)\\s*(?=<)"
                },
                {
                    "captures": {
                        "1": {
                            "patterns": [
                                {
                                    "match": "[A-Za-z_]\\w*",
                                    "name": "storage.type.java"
                                },
                                {
                                    "match": "\\.",
                                    "name": "punctuation.separator.period.java"
                                }
                            ]
                        }
                    },
                    "match": "\\b((?:[A-Za-z_]\\w*\\s*\\.\\s*)*[A-Z_]\\w*)\\b((?=\\s*[A-Za-z$_\\n])|(?=\\s*\\.\\.\\.))"
                }
            ]
        },
        "object-types-inherited": {
            "patterns": [
                {
                    "include": "#generics"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        }
                    },
                    "match": "\\b(?:[A-Z]\\w*\\s*(\\.)\\s*)*[A-Z]\\w*\\b",
                    "name": "entity.other.inherited-class.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                }
            ]
        },
        "objects": {
            "match": "(?<![\\w$])[a-zA-Z_$][\\w$]*(?=\\s*\\.\\s*[\\w$]+)",
            "name": "variable.other.object.java"
        },
        "parameters": {
            "patterns": [
                {
                    "match": "\\bfinal\\b",
                    "name": "storage.modifier.java"
                },
                {
                    "include": "#annotations"
                },
                {
                    "include": "#all-types"
                },
                {
                    "include": "#strings"
                },
                {
                    "match": "\\w+",
                    "name": "variable.parameter.java"
                },
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "\\.\\.\\.",
                    "name": "punctuation.definition.parameters.varargs.java"
                }
            ]
        },
        "parens": {
            "patterns": [
                {
                    "begin": "\\(",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.round.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "\\[",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.square.java"
                        }
                    },
                    "end": "\\]",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.square.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.bracket.curly.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.bracket.curly.java"
                        }
                    },
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "primitive-arrays": {
            "patterns": [
                {
                    "begin": "\\b(void|boolean|byte|char|short|int|float|long|double)\\b\\s*(?=\\[)",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.type.primitive.array.java"
                        }
                    },
                    "end": "(?!\\s*\\[)",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "include": "#parens"
                        }
                    ]
                }
            ]
        },
        "primitive-types": {
            "match": "\\b(void|boolean|byte|char|short|int|float|long|double)\\b",
            "name": "storage.type.primitive.java"
        },
        "properties": {
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "keyword.control.new.java"
                        }
                    },
                    "match": "(\\.)\\s*(new)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "variable.other.object.property.java"
                        }
                    },
                    "match": "(\\.)\\s*([a-zA-Z_$][\\w$]*)(?=\\s*\\.\\s*[a-zA-Z_$][\\w$]*)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "variable.other.object.property.java"
                        }
                    },
                    "match": "(\\.)\\s*([a-zA-Z_$][\\w$]*)"
                },
                {
                    "captures": {
                        "1": {
                            "name": "punctuation.separator.period.java"
                        },
                        "2": {
                            "name": "invalid.illegal.identifier.java"
                        }
                    },
                    "match": "(\\.)\\s*(\\d[\\w$]*)"
                }
            ]
        },
        "record": {
            "begin": "(?=\\w?[\\w\\s]*\\b(?:record)\\s+[\\w$]+)",
            "end": "}",
            "endCaptures": {
                "0": {
                    "name": "punctuation.section.class.end.bracket.curly.java"
                }
            },
            "name": "meta.record.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "include": "#generics"
                },
                {
                    "include": "#comments"
                },
                {
                    "begin": "(record)\\s+([\\w$]+)(<[\\w$]+>)?(\\()",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.java"
                        },
                        "2": {
                            "name": "entity.name.type.record.java"
                        },
                        "3": {
                            "patterns": [
                                {
                                    "include": "#generics"
                                }
                            ]
                        },
                        "4": {
                            "name": "punctuation.definition.parameters.begin.bracket.round.java"
                        }
                    },
                    "end": "\\)",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.parameters.end.bracket.round.java"
                        }
                    },
                    "name": "meta.record.identifier.java",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                },
                {
                    "begin": "(implements)\\s",
                    "beginCaptures": {
                        "1": {
                            "name": "storage.modifier.implements.java"
                        }
                    },
                    "end": "(?=\\s*\\{)",
                    "name": "meta.definition.class.implemented.interfaces.java",
                    "patterns": [
                        {
                            "include": "#object-types-inherited"
                        },
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#record-body"
                }
            ]
        },
        "record-body": {
            "begin": "{",
            "beginCaptures": {
                "0": {
                    "name": "punctuation.section.class.begin.bracket.curly.java"
                }
            },
            "end": "(?=})",
            "name": "meta.record.body.java",
            "patterns": [
                {
                    "include": "#record-constructor"
                },
                {
                    "include": "#class-body"
                }
            ]
        },
        "record-constructor": {
            "begin": "(?!new)(?=[\\w<].*\\s+)(?=([^(=/]|/(?!/))+(?={))",
            "end": "(})|(?=;)",
            "endCaptures": {
                "1": {
                    "name": "punctuation.section.method.end.bracket.curly.java"
                }
            },
            "name": "meta.method.java",
            "patterns": [
                {
                    "include": "#storage-modifiers"
                },
                {
                    "begin": "(\\w+)",
                    "beginCaptures": {
                        "1": {
                            "name": "entity.name.function.java"
                        }
                    },
                    "end": "(?=\\s*{)",
                    "name": "meta.method.identifier.java",
                    "patterns": [
                        {
                            "include": "#comments"
                        }
                    ]
                },
                {
                    "include": "#comments"
                },
                {
                    "begin": "{",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.section.method.begin.bracket.curly.java"
                        }
                    },
                    "contentName": "meta.method.body.java",
                    "end": "(?=})",
                    "patterns": [
                        {
                            "include": "#code"
                        }
                    ]
                }
            ]
        },
        "static-initializer": {
            "patterns": [
                {
                    "include": "#anonymous-block-and-instance-initializer"
                },
                {
                    "match": "static",
                    "name": "storage.modifier.java"
                }
            ]
        },
        "storage-modifiers": {
            "match": "\\b(public|private|protected|static|final|native|synchronized|abstract|threadsafe|transient|volatile|default|strictfp|sealed|non-sealed)\\b",
            "name": "storage.modifier.java"
        },
        "strings": {
            "patterns": [
                {
                    "begin": '"""',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": '"""',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.triple.java",
                    "patterns": [
                        {
                            "match": '(\\\\""")(?!")|(\\\\.)',
                            "name": "constant.character.escape.java"
                        }
                    ]
                },
                {
                    "begin": '"',
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": '"',
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.double.java",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character.escape.java"
                        }
                    ]
                },
                {
                    "begin": "'",
                    "beginCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.begin.java"
                        }
                    },
                    "end": "'",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.definition.string.end.java"
                        }
                    },
                    "name": "string.quoted.single.java",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character.escape.java"
                        }
                    ]
                }
            ]
        },
        "throws": {
            "begin": "throws",
            "beginCaptures": {
                "0": {
                    "name": "storage.modifier.java"
                }
            },
            "end": "(?={|;)",
            "name": "meta.throwables.java",
            "patterns": [
                {
                    "match": ",",
                    "name": "punctuation.separator.delimiter.java"
                },
                {
                    "match": "[a-zA-Z$_][\\.a-zA-Z0-9$_]*",
                    "name": "storage.type.java"
                },
                {
                    "include": "#comments"
                }
            ]
        },
        "try-catch-finally": {
            "patterns": [
                {
                    "begin": "\\btry\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control.try.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.try.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.try.java",
                    "patterns": [
                        {
                            "begin": "\\(",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.resources.begin.bracket.round.java"
                                }
                            },
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.resources.end.bracket.round.java"
                                }
                            },
                            "name": "meta.try.resources.java",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.try.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.try.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "\\b(catch)\\b",
                    "beginCaptures": {
                        "1": {
                            "name": "keyword.control.catch.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.catch.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.catch.java",
                    "patterns": [
                        {
                            "include": "#comments"
                        },
                        {
                            "begin": "\\(",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.definition.parameters.begin.bracket.round.java"
                                }
                            },
                            "contentName": "meta.catch.parameters.java",
                            "end": "\\)",
                            "endCaptures": {
                                "0": {
                                    "name": "punctuation.definition.parameters.end.bracket.round.java"
                                }
                            },
                            "patterns": [
                                {
                                    "include": "#comments"
                                },
                                {
                                    "include": "#storage-modifiers"
                                },
                                {
                                    "begin": "[a-zA-Z$_][\\.a-zA-Z0-9$_]*",
                                    "beginCaptures": {
                                        "0": {
                                            "name": "storage.type.java"
                                        }
                                    },
                                    "end": "(\\|)|(?=\\))",
                                    "endCaptures": {
                                        "1": {
                                            "name": "punctuation.catch.separator.java"
                                        }
                                    },
                                    "patterns": [
                                        {
                                            "include": "#comments"
                                        },
                                        {
                                            "captures": {
                                                "0": {
                                                    "name": "variable.parameter.java"
                                                }
                                            },
                                            "match": "\\w+"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.catch.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.catch.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                },
                {
                    "begin": "\\bfinally\\b",
                    "beginCaptures": {
                        "0": {
                            "name": "keyword.control.finally.java"
                        }
                    },
                    "end": "}",
                    "endCaptures": {
                        "0": {
                            "name": "punctuation.section.finally.end.bracket.curly.java"
                        }
                    },
                    "name": "meta.finally.java",
                    "patterns": [
                        {
                            "begin": "{",
                            "beginCaptures": {
                                "0": {
                                    "name": "punctuation.section.finally.begin.bracket.curly.java"
                                }
                            },
                            "contentName": "meta.finally.body.java",
                            "end": "(?=})",
                            "patterns": [
                                {
                                    "include": "#code"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "variables": {
            "begin": "(?=\\b((void|boolean|byte|char|short|int|float|long|double)|(?>(\\w+\\.)*[A-Z_]+\\w*))\\b\\s*(<[\\w<>,\\.?\\s\\[\\]]*>)?\\s*((\\[\\])*)?\\s+[A-Za-z_$][\\w$]*([\\w\\[\\],$][\\w\\[\\],\\s]*)?\\s*(=|:|;))",
            "end": "(?==|:|;)",
            "name": "meta.definition.variable.java",
            "patterns": [
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.definition.java"
                        }
                    },
                    "match": "([A-Za-z$_][\\w$]*)(?=\\s*(\\[\\])*\\s*(;|:|=|,))"
                },
                {
                    "include": "#all-types"
                },
                {
                    "include": "#code"
                }
            ]
        },
        "variables-local": {
            "begin": "(?=\\b(var)\\b\\s+[A-Za-z_$][\\w$]*\\s*(=|:|;))",
            "end": "(?==|:|;)",
            "name": "meta.definition.variable.local.java",
            "patterns": [
                {
                    "match": "\\bvar\\b",
                    "name": "storage.type.local.java"
                },
                {
                    "captures": {
                        "1": {
                            "name": "variable.other.definition.java"
                        }
                    },
                    "match": "([A-Za-z$_][\\w$]*)(?=\\s*(\\[\\])*\\s*(=|:|;))"
                },
                {
                    "include": "#code"
                }
            ]
        }
    },
    "scopeName": "source.java"
});
var java = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_java_mjs_488c0877._.js.map