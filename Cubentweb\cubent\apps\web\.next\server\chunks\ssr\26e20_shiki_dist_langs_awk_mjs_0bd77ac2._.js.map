{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/awk.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"AWK\", \"fileTypes\": [\"awk\"], \"name\": \"awk\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#procedure\" }, { \"include\": \"#pattern\" }], \"repository\": { \"builtin-pattern\": { \"match\": \"\\\\b(BEGINFILE|BEGIN|ENDFILE|END)\\\\b\", \"name\": \"constant.language.awk\" }, \"command\": { \"patterns\": [{ \"match\": \"\\\\b(?:next|print|printf)\\\\b\", \"name\": \"keyword.other.command.awk\" }, { \"match\": \"\\\\b(?:close|getline|delete|system)\\\\b\", \"name\": \"keyword.other.command.nawk\" }, { \"match\": \"\\\\b(?:fflush|nextfile)\\\\b\", \"name\": \"keyword.other.command.bell-awk\" }] }, \"comment\": { \"match\": \"#.*\", \"name\": \"comment.line.number-sign.awk\" }, \"constant\": { \"patterns\": [{ \"include\": \"#numeric-constant\" }, { \"include\": \"#string-constant\" }] }, \"escaped-char\": { \"match\": '\\\\\\\\(?:[\\\\\\\\abfnrtv/\"]|x[0-9A-Fa-f]{2}|[0-7]{3})', \"name\": \"constant.character.escape.awk\" }, \"expression\": { \"patterns\": [{ \"include\": \"#command\" }, { \"include\": \"#function\" }, { \"include\": \"#constant\" }, { \"include\": \"#variable\" }, { \"include\": \"#regexp-in-expression\" }, { \"include\": \"#operator\" }, { \"include\": \"#groupings\" }] }, \"function\": { \"patterns\": [{ \"match\": \"\\\\b(?:exp|int|log|sqrt|index|length|split|sprintf|substr)\\\\b\", \"name\": \"support.function.awk\" }, { \"match\": \"\\\\b(?:atan2|cos|rand|sin|srand|gsub|match|sub|tolower|toupper)\\\\b\", \"name\": \"support.function.nawk\" }, { \"match\": \"\\\\b(?:gensub|strftime|systime)\\\\b\", \"name\": \"support.function.gawk\" }] }, \"function-definition\": { \"begin\": \"\\\\b(function)\\\\s+(\\\\w+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.awk\" }, \"2\": { \"name\": \"entity.name.function.awk\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.awk\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.awk\" } }, \"patterns\": [{ \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"variable.parameter.function.awk\" }, { \"match\": \"\\\\b(,)\\\\b\", \"name\": \"punctuation.separator.parameters.awk\" }] }, \"groupings\": { \"patterns\": [{ \"match\": \"\\\\(\", \"name\": \"meta.brace.round.awk\" }, { \"match\": \"\\\\)\", \"name\": \"meta.brace.round.awk\" }, { \"match\": \"\\\\,\", \"name\": \"punctuation.separator.parameters.awk\" }] }, \"keyword\": { \"match\": \"\\\\b(?:break|continue|do|while|exit|for|if|else|return)\\\\b\", \"name\": \"keyword.control.awk\" }, \"numeric-constant\": { \"match\": \"\\\\b\\\\d+(?:\\\\.\\\\d+)?(?:e[+-]\\\\d+)?\\\\b\", \"name\": \"constant.numeric.awk\" }, \"operator\": { \"patterns\": [{ \"match\": \"(!?~|[=<>!]=|[<>])\", \"name\": \"keyword.operator.comparison.awk\" }, { \"match\": \"\\\\b(in)\\\\b\", \"name\": \"keyword.operator.comparison.awk\" }, { \"match\": \"([+\\\\-*/%^]=|\\\\+\\\\+|--|>>|=)\", \"name\": \"keyword.operator.assignment.awk\" }, { \"match\": \"(\\\\|\\\\||&&|!)\", \"name\": \"keyword.operator.boolean.awk\" }, { \"match\": \"([+\\\\-*/%^])\", \"name\": \"keyword.operator.arithmetic.awk\" }, { \"match\": \"([?:])\", \"name\": \"keyword.operator.trinary.awk\" }, { \"match\": \"(\\\\[|\\\\])\", \"name\": \"keyword.operator.index.awk\" }] }, \"pattern\": { \"patterns\": [{ \"include\": \"#regexp-as-pattern\" }, { \"include\": \"#function-definition\" }, { \"include\": \"#builtin-pattern\" }, { \"include\": \"#expression\" }] }, \"procedure\": { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#procedure\" }, { \"include\": \"#keyword\" }, { \"include\": \"#expression\" }] }, \"regex-as-assignment\": { \"begin\": \"([^=<>!+\\\\-*/%^]=)\\\\s*(/)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.awk\" }, \"2\": { \"name\": \"punctuation.definition.regex.begin.awk\" } }, \"contentName\": \"string.regexp\", \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.end.awk\" } }, \"patterns\": [{ \"include\": \"source.regexp\" }] }, \"regex-as-comparison\": { \"begin\": \"(!?~)\\\\s*(/)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.comparison.awk\" }, \"2\": { \"name\": \"punctuation.definition.regex.begin.awk\" } }, \"contentName\": \"string.regexp\", \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.end.awk\" } }, \"patterns\": [{ \"include\": \"source.regexp\" }] }, \"regex-as-first-argument\": { \"begin\": \"(\\\\()\\\\s*(/)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.awk\" }, \"2\": { \"name\": \"punctuation.definition.regex.begin.awk\" } }, \"contentName\": \"string.regexp\", \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.end.awk\" } }, \"patterns\": [{ \"include\": \"source.regexp\" }] }, \"regex-as-nth-argument\": { \"begin\": \"(,)\\\\s*(/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.parameters.awk\" }, \"2\": { \"name\": \"punctuation.definition.regex.begin.awk\" } }, \"contentName\": \"string.regexp\", \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.end.awk\" } }, \"patterns\": [{ \"include\": \"source.regexp\" }] }, \"regexp-as-pattern\": { \"begin\": \"/\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.begin.awk\" } }, \"contentName\": \"string.regexp\", \"end\": \"/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.regex.end.awk\" } }, \"patterns\": [{ \"include\": \"source.regexp\" }] }, \"regexp-in-expression\": { \"patterns\": [{ \"include\": \"#regex-as-assignment\" }, { \"include\": \"#regex-as-comparison\" }, { \"include\": \"#regex-as-first-argument\" }, { \"include\": \"#regex-as-nth-argument\" }] }, \"string-constant\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.awk\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.awk\" } }, \"name\": \"string.quoted.double.awk\", \"patterns\": [{ \"include\": \"#escaped-char\" }] }, \"variable\": { \"patterns\": [{ \"match\": \"\\\\$\\\\d+\", \"name\": \"variable.language.awk\" }, { \"match\": \"\\\\b(?:FILENAME|FS|NF|NR|OFMT|OFS|ORS|RS)\\\\b\", \"name\": \"variable.language.awk\" }, { \"match\": \"\\\\b(?:ARGC|ARGV|CONVFMT|ENVIRON|FNR|RLENGTH|RSTART|SUBSEP)\\\\b\", \"name\": \"variable.language.nawk\" }, { \"match\": \"\\\\b(?:ARGIND|ERRNO|FIELDWIDTHS|IGNORECASE|RT)\\\\b\", \"name\": \"variable.language.gawk\" }] } }, \"scopeName\": \"source.awk\" });\nvar awk = [\n  lang\n];\n\nexport { awk as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,SAAS;YAAuC,QAAQ;QAAwB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+B,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAiC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAO,QAAQ;QAA+B;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAoD,QAAQ;QAAgC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgE,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAqE,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAwB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAgC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAuC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAA6D,QAAQ;QAAsB;QAAG,oBAAoB;YAAE,SAAS;YAAwC,QAAQ;QAAuB;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAA6B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAA+C,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAoD,QAAQ;gBAAyB;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AAChvL,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}