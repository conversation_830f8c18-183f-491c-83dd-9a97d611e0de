{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [], "dynamicRoutes": [{"page": "/sign-in/[[...sign-in]]", "regex": "^/sign\\-in(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignin": "nxtPsign-in"}, "namedRegex": "^/sign\\-in(?:/(?<nxtPsignin>.+?))?(?:/)?$"}, {"page": "/sign-up/[[...sign-up]]", "regex": "^/sign\\-up(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignup": "nxtPsign-up"}, "namedRegex": "^/sign\\-up(?:/(?<nxtPsignup>.+?))?(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/.well-known/vercel/flags", "regex": "^/\\.well\\-known/vercel/flags(?:/)?$", "routeKeys": {}, "namedRegex": "^/\\.well\\-known/vercel/flags(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/apple-icon.png", "regex": "^/apple\\-icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/apple\\-icon\\.png(?:/)?$"}, {"page": "/auth-success", "regex": "^/auth\\-success(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth\\-success(?:/)?$"}, {"page": "/debug-auth", "regex": "^/debug\\-auth(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-auth(?:/)?$"}, {"page": "/icon.png", "regex": "^/icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.png(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/opengraph-image.png", "regex": "^/opengraph\\-image\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/opengraph\\-image\\.png(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/profile/extension", "regex": "^/profile/extension(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/extension(?:/)?$"}, {"page": "/profile/settings", "regex": "^/profile/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/settings(?:/)?$"}, {"page": "/profile/usage", "regex": "^/profile/usage(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/usage(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/webhooks", "regex": "^/webhooks(?:/)?$", "routeKeys": {}, "namedRegex": "^/webhooks(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/ingest/static/:path*", "destination": "https://us-assets.i.posthog.com/static/:path*", "regex": "^/ingest/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/ingest/:path*", "destination": "https://us.i.posthog.com/:path*", "regex": "^/ingest(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/ingest/decide", "destination": "https://us.i.posthog.com/decide", "regex": "^/ingest/decide(?:/)?$"}]}