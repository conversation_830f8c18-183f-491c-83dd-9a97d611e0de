{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/jsonl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON Lines\", \"name\": \"jsonl\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json.lines\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json.lines\" } }, \"name\": \"meta.structure.array.json.lines\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json.lines\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json.lines\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json.lines\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json.lines\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json.lines\" }, \"number\": { \"match\": \"-?(?:0|[1-9]\\\\d*)(?:(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)?\", \"name\": \"constant.numeric.json.lines\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json.lines\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json.lines\" } }, \"name\": \"meta.structure.dictionary.json.lines\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json.lines\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json.lines\" } }, \"name\": \"meta.structure.dictionary.value.json.lines\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.lines\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.lines\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json.lines\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json.lines\" } }, \"name\": \"string.json.lines support.type.property-name.json.lines\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json.lines\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json.lines\" } }, \"name\": \"string.quoted.double.json.lines\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json.lines\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json.lines\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json.lines\" });\nvar jsonl = [\n  lang\n];\n\nexport { jsonl as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAsD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA6B,QAAQ;QAA+B;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAA8B;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;oBAAuB,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;4BAAyB,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAA2D;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA2D;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwD;YAAE;YAAG,QAAQ;YAA2D,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAwD;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAoB;AACnuG,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}