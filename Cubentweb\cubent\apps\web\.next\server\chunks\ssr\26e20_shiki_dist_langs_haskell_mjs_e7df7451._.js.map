{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/haskell.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Haskell\", \"fileTypes\": [\"hs\", \"hs-boot\", \"hsig\"], \"name\": \"haskell\", \"patterns\": [{ \"include\": \"#liquid_haskell\" }, { \"include\": \"#comment_like\" }, { \"include\": \"#numeric_literals\" }, { \"include\": \"#string_literal\" }, { \"include\": \"#char_literal\" }, { \"match\": \"(?<!@|#)-\\\\}\", \"name\": \"invalid\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()\\\\s*(\\\\))\", \"name\": \"constant.language.unit.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" }, \"3\": { \"name\": \"keyword.operator.hash.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()(#)\\\\s*(#)(\\\\))\", \"name\": \"constant.language.unit.unboxed.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()\\\\s*,[\\\\s,]*(\\\\))\", \"name\": \"support.constant.tuple.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" }, \"3\": { \"name\": \"keyword.operator.hash.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()(#)\\\\s*,[\\\\s,]*(#)(\\\\))\", \"name\": \"support.constant.tuple.unboxed.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.bracket.haskell\" }, \"2\": { \"name\": \"punctuation.bracket.haskell\" } }, \"match\": \"(\\\\[)\\\\s*(\\\\])\", \"name\": \"constant.language.empty-list.haskell\" }, { \"begin\": \"(\\\\b(?<!')(module)|^(signature))(\\\\b(?!'))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.module.haskell\" }, \"3\": { \"name\": \"keyword.other.signature.haskell\" } }, \"end\": \"(?=\\\\b(?<!')where\\\\b(?!'))\", \"name\": \"meta.declaration.module.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }, { \"match\": \"[a-z]+\", \"name\": \"invalid\" }] }, { \"include\": \"#ffi\" }, { \"begin\": \"^(\\\\s*)(class)(\\\\b(?!'))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.class.haskell\" } }, \"end\": \"(?=(?<!')\\\\bwhere\\\\b(?!'))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.class.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#where\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(data|newtype)(?:\\\\s+(instance))?\\\\s+((?:(?!(?:(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:=|--+)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))|(?:\\\\b(?<!')(?:where|deriving)\\\\b(?!'))|{-).)*)(?=\\\\b(?<!'')where\\\\b(?!''))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.$2.haskell\" }, \"3\": { \"name\": \"keyword.other.instance.haskell\" }, \"4\": { \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"end\": \"(?=(?<!')\\\\bderiving\\\\b(?!'))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.$2.generalized.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"begin\": \"(?<!')\\\\b(where)\\\\s*(\\\\{)(?!-)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.where.haskell\" }, \"2\": { \"name\": \"punctuation.brace.haskell\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#gadt_constructor\" }, { \"match\": \";\", \"name\": \"punctuation.semicolon.haskell\" }] }, { \"match\": \"\\\\b(?<!')(where)\\\\b(?!')\", \"name\": \"keyword.other.where.haskell\" }, { \"include\": \"#deriving\" }, { \"include\": \"#gadt_constructor\" }] }, { \"include\": \"#role_annotation\" }, { \"begin\": \"^(\\\\s*)(pattern)\\\\s+(.*?)\\\\s+(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.pattern.haskell\" }, \"3\": { \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#data_constructor\" }] }, \"4\": { \"name\": \"keyword.operator.double-colon.haskell\" } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.pattern.type.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^\\\\s*(pattern)\\\\b(?!')\", \"captures\": { \"1\": { \"name\": \"keyword.other.pattern.haskell\" } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.pattern.haskell\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"^(\\\\s*)(data|newtype)(?:\\\\s+(family|instance))?\\\\s+(((?!(?:(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:=|--+)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))|(?:\\\\b(?<!')(?:where|deriving)\\\\b(?!'))|{-).)*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.$2.haskell\" }, \"3\": { \"name\": \"keyword.other.$3.haskell\" }, \"4\": { \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.$2.algebraic.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#deriving\" }, { \"include\": \"#forall\" }, { \"include\": \"#adt_constructor\" }, { \"include\": \"#context\" }, { \"include\": \"#record_decl\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(type)\\\\s+(family)\\\\b(?!')(((?!(?:(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:=|--+)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))|\\\\b(?<!')where\\\\b(?!')|{-).)*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.type.haskell\" }, \"3\": { \"name\": \"keyword.other.family.haskell\" }, \"4\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#where\" }, { \"include\": \"#type_signature\" }] } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.type.family.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#where\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(type)(?:\\\\s+(instance))?\\\\s+(((?!(?:(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:=|--+|::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))|{-).)*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.type.haskell\" }, \"3\": { \"name\": \"keyword.other.instance.haskell\" }, \"4\": { \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.type.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(instance)(\\\\b(?!'))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.instance.haskell\" } }, \"end\": \"(?=\\\\b(?<!')(where)\\\\b(?!'))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.declaration.instance.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#where\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(import)(\\\\b(?!'))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.import.haskell\" } }, \"end\": \"(?=\\\\b(?<!')(where)\\\\b(?!'))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.import.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#where\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.$1.haskell\" } }, \"match\": \"(qualified|as|hiding)\" }, { \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }] }, { \"include\": \"#deriving\" }, { \"include\": \"#layout_herald\" }, { \"include\": \"#keyword\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.$1.haskell\" }, \"2\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#integer_literals\" }, { \"include\": \"#infix_op\" }] } }, \"match\": \"^\\\\s*(infix[lr]?)\\\\s+(.*)\", \"name\": \"meta.fixity-declaration.haskell\" }, { \"include\": \"#overloaded_label\" }, { \"include\": \"#type_application\" }, { \"include\": \"#reserved_symbol\" }, { \"include\": \"#fun_decl\" }, { \"include\": \"#qualifier\" }, { \"include\": \"#data_constructor\" }, { \"include\": \"#start_type_signature\" }, { \"include\": \"#prefix_op\" }, { \"include\": \"#infix_op\" }, { \"begin\": \"(\\\\()(#)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" } }, \"end\": \"(#)(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.hash.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"$self\" }] }, { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"$self\" }] }, { \"include\": \"#quasi_quote\" }, { \"begin\": \"(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.bracket.haskell\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.bracket.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"$self\" }] }, { \"include\": \"#record\" }], \"repository\": { \"adt_constructor\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"begin\": \"(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:(=)|(\\\\|))(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.eq.haskell\" }, \"2\": { \"name\": \"keyword.operator.pipe.haskell\" } }, \"end\": \"(?:\\\\G|^)\\\\s*(?:(?:(?<!')\\\\b((?:[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'\\\\.])+)|('?(?<paren>\\\\((?:[^()]*|\\\\g<paren>)*\\\\)))|('?(?<brac>\\\\((?:[^\\\\[\\\\]]*|\\\\g<brac>)*\\\\])))\\\\s*(?:(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(:[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]*)|(`)([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(`)))|(?:(?<!')\\\\b([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*))|(\\\\()\\\\s*(:[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]*)\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type_signature\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type_signature\" }] }, \"4\": { \"patterns\": [{ \"include\": \"#type_signature\" }] }, \"6\": { \"name\": \"constant.other.operator.haskell\" }, \"7\": { \"name\": \"punctuation.backtick.haskell\" }, \"8\": { \"name\": \"constant.other.haskell\" }, \"9\": { \"name\": \"punctuation.backtick.haskell\" }, \"10\": { \"name\": \"constant.other.haskell\" }, \"11\": { \"name\": \"punctuation.paren.haskell\" }, \"12\": { \"name\": \"constant.other.operator.haskell\" }, \"13\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#deriving\" }, { \"include\": \"#record_decl\" }, { \"include\": \"#forall\" }, { \"include\": \"#context\" }] }] }, \"block_comment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.haskell\" } }, \"end\": \"-\\\\}\", \"name\": \"comment.block.haskell\", \"patterns\": [{ \"include\": \"#block_comment\" }] }, \"char_literal\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.haskell\" }, \"2\": { \"name\": \"constant.character.escape.haskell\" }, \"3\": { \"name\": \"constant.character.escape.octal.haskell\" }, \"4\": { \"name\": \"constant.character.escape.hexadecimal.haskell\" }, \"5\": { \"name\": \"constant.character.escape.control.haskell\" }, \"6\": { \"name\": \"punctuation.definition.string.end.haskell\" } }, \"match\": `(?<![\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'])(')(?:[ -\\\\[\\\\]-~]|(\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\\\\\&]))|(\\\\\\\\o[0-7]+)|(\\\\\\\\x[0-9A-Fa-f]+)|(\\\\\\\\\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_]))(')`, \"name\": \"string.quoted.single.haskell\" }, \"comma\": { \"match\": \",\", \"name\": \"punctuation.separator.comma.haskell\" }, \"comment_like\": { \"patterns\": [{ \"include\": \"#cpp\" }, { \"include\": \"#pragma\" }, { \"include\": \"#comments\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)(--\\\\s[\\\\|$])\", \"beginCaptures\": { \"2\": { \"name\": \"punctuation.whitespace.comment.leading.haskell\" } }, \"end\": \"(?=^(?!\\\\1--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])))\", \"name\": \"comment.block.documentation.haskell\" }, { \"begin\": \"(^[ \\\\t]+)?(--\\\\s[\\\\^\\\\*])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.haskell\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.documentation.haskell\" }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\\\\s?[\\\\|$\\\\*\\\\^]\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.haskell\" } }, \"end\": \"-\\\\}\", \"name\": \"comment.block.documentation.haskell\", \"patterns\": [{ \"include\": \"#block_comment\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.haskell\" } }, \"comment\": \"Operators may begin with '--' as long as they are not entirely composed of '-' characters. This means comments can't be immediately followed by an allowable operator character.\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.haskell\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.haskell\" }] }, { \"include\": \"#block_comment\" }] }, \"context\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#type_signature\" }] }, \"2\": { \"name\": \"keyword.operator.big-arrow.haskell\" } }, \"match\": \"(.*)(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(=>|\\u21D2)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\" }, \"cpp\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.preprocessor.c\" } }, \"comment\": `In addition to Haskell's \"native\" syntax, GHC permits the C preprocessor to be run on a source file.`, \"match\": \"^(#).*$\", \"name\": \"meta.preprocessor.c\" }, \"data_constructor\": { \"match\": \"\\\\b(?<!')[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?![\\\\.'\\\\w])\", \"name\": \"constant.other.haskell\" }, \"deriving\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)(deriving)\\\\s+(?:(via|stock|newtype|anyclass)\\\\s+)?\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.deriving.haskell\" }, \"3\": { \"name\": \"keyword.other.deriving.strategy.$3.haskell\" } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.deriving.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"match\": \"(?<!')\\\\b(instance)\\\\b(?!')\", \"name\": \"keyword.other.instance.haskell\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.deriving.strategy.$1.haskell\" } }, \"match\": \"(?<!')\\\\b(via|stock|newtype|anyclass)\\\\b(?!')\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"(deriving)(?:\\\\s+(stock|newtype|anyclass))?\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.deriving.haskell\" }, \"2\": { \"name\": \"keyword.other.deriving.strategy.$2.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" } }, \"name\": \"meta.deriving.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.other.deriving.haskell\" }, \"2\": { \"name\": \"keyword.other.deriving.strategy.$2.haskell\" }, \"3\": { \"patterns\": [{ \"include\": \"#type_signature\" }] }, \"5\": { \"name\": \"keyword.other.deriving.strategy.via.haskell\" }, \"6\": { \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"match\": \"(deriving)(?:\\\\s+(stock|newtype|anyclass))?\\\\s+([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(\\\\s+(via)\\\\s+(.*)$)?\", \"name\": \"meta.deriving.haskell\" }, { \"match\": \"(?<!')\\\\b(via)\\\\b(?!')\", \"name\": \"keyword.other.deriving.strategy.via.haskell\" }] }, \"double_colon\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.double-colon.haskell\" } }, \"match\": \"\\\\s*(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\\\\s*\" }, \"export_constructs\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"begin\": \"\\\\b(?<!')(pattern)\\\\b(?!')\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.pattern.haskell\" } }, \"end\": \"([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)|(\\\\()\\\\s*(:[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"constant.other.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"constant.other.operator.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }] }, { \"begin\": \"\\\\b(?<!')(type)\\\\b(?!')\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.type.haskell\" } }, \"end\": \"([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)|(\\\\()\\\\s*([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"storage.type.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"storage.type.operator.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }] }, { \"match\": \"(?<!')\\\\b[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"entity.name.function.haskell\" }, { \"match\": \"(?<!')\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"storage.type.haskell\" }, { \"include\": \"#record_wildcard\" }, { \"include\": \"#reserved_symbol\" }, { \"include\": \"#prefix_op\" }] }, \"ffi\": { \"begin\": \"^(\\\\s*)(foreign)\\\\s+(import|export)\\\\s+\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.foreign.haskell\" }, \"3\": { \"name\": \"keyword.other.$3.haskell\" } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.$3.foreign.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.calling-convention.$1.haskell\" } }, \"match\": \"\\\\b(?<!')(ccall|cplusplus|dotnet|jvm|stdcall|prim|capi)\\\\s+\" }, { \"begin\": `(?=\")|(?=\\\\b(?<!')([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\b(?!'))`, \"end\": \"(?=(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.safety.$1.haskell\" }, \"2\": { \"name\": \"entity.name.foreign.haskell\", \"patterns\": [{ \"include\": \"#string_literal\" }] }, \"3\": { \"name\": \"entity.name.function.haskell\" }, \"4\": { \"name\": \"entity.name.function.infix.haskell\" } }, \"match\": `\\\\b(?<!')(safe|unsafe|interruptible)\\\\b(?!')\\\\s*(\"(?:\\\\\\\\\"|[^\"])*\")?\\\\s*(?:(?:\\\\b(?<!'')([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\b(?!'))|(?:\\\\(\\\\s*(?!--+\\\\))([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]\\`{}_\"']]+)\\\\s*\\\\)))` }, { \"captures\": { \"1\": { \"name\": \"keyword.other.safety.$1.haskell\" }, \"2\": { \"name\": \"entity.name.foreign.haskell\", \"patterns\": [{ \"include\": \"#string_literal\" }] } }, \"match\": `\\\\b(?<!')(safe|unsafe|interruptible)\\\\b(?!')\\\\s*(\"(?:\\\\\\\\\"|[^\"])*\")?\\\\s*$` }, { \"captures\": { \"0\": { \"name\": \"entity.name.foreign.haskell\", \"patterns\": [{ \"include\": \"#string_literal\" }] } }, \"match\": '\"(?:\\\\\\\\\"|[^\"])*\"' }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"entity.name.function.infix.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(?:\\\\b(?<!'')([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\b(?!'))|(?:(\\\\()\\\\s*(?!--+\\\\))([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\)))\" }] }, { \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }] }, \"float_literals\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.floating.decimal.haskell\" }, \"2\": { \"name\": \"constant.numeric.floating.hexadecimal.haskell\" } }, \"comment\": \"Floats are decimal or hexadecimal\", \"match\": \"\\\\b(?<!')(?:(\\\\d[_0-9]*\\\\.\\\\d[_0-9]*(?:[eE][-+]?\\\\d[_0-9]*)?|\\\\d[_0-9]*[eE][-+]?\\\\d[_0-9]*)|(0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*\\\\.[0-9a-fA-F][_0-9a-fA-F]*(?:[pP][-+]?\\\\d[_0-9]*)?|0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*[pP][-+]?\\\\d[_0-9]*))\\\\b(?!')\" }, \"forall\": { \"begin\": \"\\\\b(?<!')(forall|\\u2200)\\\\b(?!')\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.forall.haskell\" } }, \"end\": \"(\\\\.)|(->|\\u2192)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.period.haskell\" }, \"2\": { \"name\": \"keyword.operator.arrow.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#type_variable\" }, { \"include\": \"#type_signature\" }] }, \"fun_decl\": { \"begin\": \"^(\\\\s*)(?<fn>(?:[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\#*|\\\\(\\\\s*(?!--+\\\\))[\\\\p{S}\\\\p{P}&&[^(),:;\\\\[\\\\]`{}_\\\"']][\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]*\\\\s*\\\\))(?:\\\\s*,\\\\s*\\\\g<fn>)?)\\\\s*(?<![\\\\p{S}\\\\p{P}&&[^),;\\\\]`}_\\\"']])(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{_\\\"']])\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.function.haskell\", \"patterns\": [{ \"include\": \"#reserved_symbol\" }, { \"include\": \"#prefix_op\" }] }, \"3\": { \"name\": \"keyword.operator.double-colon.haskell\" } }, \"end\": \"(?=(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])((<-|\\u2190)|(=)|(-<|\\u21A2)|(-<<|\\u291B))([(),;\\\\[\\\\]`{}_\\\"']|[^\\\\p{S}\\\\p{P}]))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.function.type-declaration.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, \"gadt_constructor\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)(?:(\\\\b(?<!')[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)|(\\\\()\\\\s*(:[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]*)\\\\s*(\\\\)))\", \"beginCaptures\": { \"2\": { \"name\": \"constant.other.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" }, \"4\": { \"name\": \"constant.other.operator.haskell\" }, \"5\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(?=\\\\b(?<!'')deriving\\\\b(?!'))|(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#deriving\" }, { \"include\": \"#double_colon\" }, { \"include\": \"#record_decl\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"(\\\\b(?<!')[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}]*)|(\\\\()\\\\s*(:[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]*)\\\\s*(\\\\))\", \"beginCaptures\": { \"1\": { \"name\": \"constant.other.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"constant.other.operator.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#deriving\" }, { \"include\": \"#double_colon\" }, { \"include\": \"#record_decl\" }, { \"include\": \"#type_signature\" }] }] }, \"infix_op\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"entity.name.namespace.haskell\" }, \"3\": { \"name\": \"keyword.operator.infix.haskell\" } }, \"comment\": \"In case this regex seems overly general, note that Haskell permits  the definition of new operators which can be nearly any string of  punctuation characters, such as $%^&*.\\n\", \"match\": \"((?:(?<!'')('')?[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'']*\\\\.)*)(\\\\#+|[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+(?<!\\\\#))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.backtick.haskell\" }, \"2\": { \"name\": \"entity.name.namespace.haskell\" }, \"3\": { \"patterns\": [{ \"include\": \"#data_constructor\" }] }, \"4\": { \"name\": \"punctuation.backtick.haskell\" } }, \"comment\": \"In case this regex seems unusual for an infix operator, note that Haskell\\nallows any ordinary function application (elem 4 [1..10]) to be rewritten\\nas an infix expression (4 `elem` [1..10]).\\n\", \"match\": \"(`)((?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'']*\\\\.)*)([\\\\p{Ll}\\\\p{Lu}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'']*)(`)\", \"name\": \"keyword.operator.function.infix.haskell\" }] }, \"inline_phase\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.haskell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.haskell\" } }, \"name\": \"meta.inlining-phase.haskell\", \"patterns\": [{ \"match\": \"~\", \"name\": \"punctuation.tilde.haskell\" }, { \"include\": \"#integer_literals\" }, { \"match\": \"\\\\w*\", \"name\": \"invalid\" }] }, \"integer_literals\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.integral.decimal.haskell\" }, \"2\": { \"name\": \"constant.numeric.integral.hexadecimal.haskell\" }, \"3\": { \"name\": \"constant.numeric.integral.octal.haskell\" }, \"4\": { \"name\": \"constant.numeric.integral.binary.haskell\" } }, \"match\": \"\\\\b(?<!')(?:(\\\\d[_0-9]*)|(0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*)|(0[oO]_*[0-7][_0-7]*)|(0[bB]_*[01][_01]*))\\\\b(?!')\" }, \"keyword\": { \"captures\": { \"1\": { \"name\": \"keyword.other.$1.haskell\" }, \"2\": { \"name\": \"keyword.control.$2.haskell\" } }, \"match\": \"\\\\b(?<!')(?:(where|let|in|default)|(m?do|if|then|else|case|of|proc|rec))\\\\b(?!')\" }, \"layout_herald\": { \"begin\": \"(?<!')\\\\b(?:(where|let|m?do)|(of))\\\\s*(\\\\{)(?!-)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.$1.haskell\" }, \"2\": { \"name\": \"keyword.control.of.haskell\" }, \"3\": { \"name\": \"punctuation.brace.haskell\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"patterns\": [{ \"include\": \"$self\" }, { \"match\": \";\", \"name\": \"punctuation.semicolon.haskell\" }] }, \"liquid_haskell\": { \"begin\": \"\\\\{-@\", \"end\": \"@-\\\\}\", \"name\": \"block.liquidhaskell.haskell\", \"patterns\": [{ \"include\": \"$self\" }] }, \"module_exports\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.paren.haskell\" } }, \"name\": \"meta.declaration.exports.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.module.haskell\" } }, \"match\": \"\\\\b(?<!')(module)\\\\b(?!')\" }, { \"include\": \"#comma\" }, { \"include\": \"#export_constructs\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#record_wildcard\" }, { \"include\": \"#export_constructs\" }, { \"include\": \"#comma\" }] }] }, \"module_name\": { \"match\": \"(?<conid>[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(\\\\.\\\\g<conid>)?)\", \"name\": \"entity.name.namespace.haskell\" }, \"numeric_literals\": { \"patterns\": [{ \"include\": \"#float_literals\" }, { \"include\": \"#integer_literals\" }] }, \"overloaded_label\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.prefix.hash.haskell\" }, \"2\": { \"patterns\": [{ \"include\": \"#string_literal\" }] } }, \"match\": '(?<![\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{]])(\\\\#)(?:(\"(?:\\\\\\\\\"|[^\"])*\")|[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\'\\\\.]+)', \"name\": \"entity.name.label.haskell\" }] }, \"pragma\": { \"begin\": \"\\\\{-#\", \"end\": \"#-\\\\}\", \"name\": \"meta.preprocessor.haskell\", \"patterns\": [{ \"begin\": \"(?i)\\\\b(?<!')(LANGUAGE)\\\\b(?!')\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preprocessor.pragma.haskell\" } }, \"end\": \"(?=#-\\\\})\", \"patterns\": [{ \"match\": \"(?:No)?(?:AutoDeriveTypeable|DatatypeContexts|DoRec|IncoherentInstances|MonadFailDesugaring|MonoPatBinds|NullaryTypeClasses|OverlappingInstances|PatternSignatures|RecordPuns|RelaxedPolyRec)\", \"name\": \"invalid.deprecated\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.preprocessor.extension.haskell\" } }, \"match\": \"((?:No)?(?:AllowAmbiguousTypes|AlternativeLayoutRule|AlternativeLayoutRuleTransitional|Arrows|BangPatterns|BinaryLiterals|CApiFFI|CPP|CUSKs|ConstrainedClassMethods|ConstraintKinds|DataKinds|DefaultSignatures|DeriveAnyClass|DeriveDataTypeable|DeriveFoldable|DeriveFunctor|DeriveGeneric|DeriveLift|DeriveTraversable|DerivingStrategies|DerivingVia|DisambiguateRecordFields|DoAndIfThenElse|BlockArguments|DuplicateRecordFields|EmptyCase|EmptyDataDecls|EmptyDataDeriving|ExistentialQuantification|ExplicitForAll|ExplicitNamespaces|ExtendedDefaultRules|FlexibleContexts|FlexibleInstances|ForeignFunctionInterface|FunctionalDependencies|GADTSyntax|GADTs|GHCForeignImportPrim|Generali(?:s|z)edNewtypeDeriving|ImplicitParams|ImplicitPrelude|ImportQualifiedPost|ImpredicativeTypes|TypeFamilyDependencies|InstanceSigs|ApplicativeDo|InterruptibleFFI|JavaScriptFFI|KindSignatures|LambdaCase|LiberalTypeSynonyms|MagicHash|MonadComprehensions|MonoLocalBinds|MonomorphismRestriction|MultiParamTypeClasses|MultiWayIf|NumericUnderscores|NPlusKPatterns|NamedFieldPuns|NamedWildCards|NegativeLiterals|HexFloatLiterals|NondecreasingIndentation|NumDecimals|OverloadedLabels|OverloadedLists|OverloadedStrings|PackageImports|ParallelArrays|ParallelListComp|PartialTypeSignatures|PatternGuards|PatternSynonyms|PolyKinds|PolymorphicComponents|QuantifiedConstraints|PostfixOperators|QuasiQuotes|Rank2Types|RankNTypes|RebindableSyntax|RecordWildCards|RecursiveDo|RelaxedLayout|RoleAnnotations|ScopedTypeVariables|StandaloneDeriving|StarIsType|StaticPointers|Strict|StrictData|TemplateHaskell|TemplateHaskellQuotes|StandaloneKindSignatures|TraditionalRecordSyntax|TransformListComp|TupleSections|TypeApplications|TypeInType|TypeFamilies|TypeOperators|TypeSynonymInstances|UnboxedTuples|UnboxedSums|UndecidableInstances|UndecidableSuperClasses|UnicodeSyntax|UnliftedFFITypes|UnliftedNewtypes|ViewPatterns))\" }, { \"include\": \"#comma\" }] }, { \"begin\": \"(?i)\\\\b(?<!')(SPECIALI(?:S|Z)E)(?:\\\\s*(\\\\[[^\\\\[\\\\]]*\\\\])?\\\\s*|\\\\s+)(instance)\\\\b(?!')\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preprocessor.pragma.haskell\" }, \"2\": { \"patterns\": [{ \"include\": \"#inline_phase\" }] }, \"3\": { \"name\": \"keyword.other.instance.haskell\" } }, \"end\": \"(?=#-\\\\})\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"(?i)\\\\b(?<!')(SPECIALI(?:S|Z)E)\\\\b(?!')(?:\\\\s+(INLINE)\\\\b(?!'))?(?:\\\\s*(\\\\[[^\\\\[\\\\]]*\\\\])?)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preprocessor.pragma.haskell\" }, \"2\": { \"name\": \"keyword.other.preprocessor.pragma.haskell\" }, \"3\": { \"patterns\": [{ \"include\": \"#inline_phase\" }] } }, \"end\": \"(?=#-\\\\})\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"(?i)\\\\b(?<!')(LANGUAGE|OPTIONS_GHC|INCLUDE|MINIMAL|UNPACK|OVERLAPS|INCOHERENT|NOUNPACK|SOURCE|OVERLAPPING|OVERLAPPABLE|INLINE|NOINLINE|INLINE?ABLE|CONLIKE|LINE|COLUMN|RULES|COMPLETE)\\\\b(?!')\", \"name\": \"keyword.other.preprocessor.haskell\" }, { \"begin\": \"(?i)\\\\b(DEPRECATED|WARNING)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preprocessor.pragma.haskell\" } }, \"end\": \"(?=#-\\\\})\", \"patterns\": [{ \"include\": \"#string_literal\" }] }] }, \"prefix_op\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"entity.name.function.infix.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" } }, \"comment\": \"An operator cannot be composed entirely of '-' characters;  instead, it should be matched as a comment.\\n\", \"match\": \"(\\\\()\\\\s*(?!(?:--+|\\\\.\\\\.)\\\\))(\\\\#+|[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+(?<!\\\\#))\\\\s*(\\\\))\" }] }, \"qualifier\": { \"match\": \"\\\\b(?<!')[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\.\", \"name\": \"entity.name.namespace.haskell\" }, \"quasi_quote\": { \"patterns\": [{ \"begin\": \"(\\\\[)(e|d|p)?(\\\\|\\\\|?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" }, \"2\": { \"name\": \"entity.name.quasi-quoter.haskell\" }, \"3\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" } }, \"end\": \"\\\\3\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.quasi-quotation.end.haskell\" } }, \"name\": \"meta.quasi-quotation.haskell\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"(\\\\[)(t)(\\\\|\\\\|?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" }, \"2\": { \"name\": \"entity.name.quasi-quoter.haskell\" }, \"3\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" } }, \"end\": \"\\\\3\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.quasi-quotation.end.haskell\" } }, \"name\": \"meta.quasi-quotation.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"(\\\\[)(?:(\\\\$\\\\$)|(\\\\$))?((?:[^\\\\s\\\\p{S}\\\\p{P}]|[\\\\.'_])*)(\\\\|\\\\|?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" }, \"2\": { \"name\": \"keyword.operator.prefix.double-dollar.haskell\" }, \"3\": { \"name\": \"keyword.operator.prefix.dollar.haskell\" }, \"4\": { \"name\": \"entity.name.quasi-quoter.haskell\", \"patterns\": [{ \"include\": \"#qualifier\" }] }, \"5\": { \"name\": \"keyword.operator.quasi-quotation.begin.haskell\" } }, \"end\": \"\\\\5\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.quasi-quotation.end.haskell\" } }, \"name\": \"meta.quasi-quotation.haskell\" }] }, \"record\": { \"begin\": \"({)(?!-)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"end\": \"(?<!-)(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"name\": \"meta.record.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#record_field\" }] }, \"record_decl\": { \"begin\": \"({)(?!-)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"end\": \"(?<!-)(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"name\": \"meta.record.definition.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#record_decl_field\" }] }, \"record_decl_field\": { \"begin\": \"(?:([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)|(\\\\()\\\\s*([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\)))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.member.definition.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"variable.other.member.definition.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(,)|(?=})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.comma.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#comma\" }, { \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }, { \"include\": \"#record_decl_field\" }] }, \"record_field\": { \"patterns\": [{ \"begin\": \"(?:([\\\\p{Ll}\\\\p{Lu}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\\\.']*)|(\\\\()\\\\s*([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\)))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.member.haskell\", \"patterns\": [{ \"include\": \"#qualifier\" }] }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"variable.other.member.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(,)|(?=})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.comma.haskell\" } }, \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#comma\" }, { \"include\": \"$self\" }] }, { \"include\": \"#record_wildcard\" }] }, \"record_wildcard\": { \"captures\": { \"1\": { \"name\": \"variable.other.member.wildcard.haskell\" } }, \"match\": \"(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(\\\\.\\\\.)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\" }, \"reserved_symbol\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.double-dot.haskell\" }, \"2\": { \"name\": \"keyword.operator.colon.haskell\" }, \"3\": { \"name\": \"keyword.operator.eq.haskell\" }, \"4\": { \"name\": \"keyword.operator.lambda.haskell\" }, \"5\": { \"name\": \"keyword.operator.pipe.haskell\" }, \"6\": { \"name\": \"keyword.operator.arrow.left.haskell\" }, \"7\": { \"name\": \"keyword.operator.arrow.haskell\" }, \"8\": { \"name\": \"keyword.operator.arrow.left.tail.haskell\" }, \"9\": { \"name\": \"keyword.operator.arrow.left.tail.double.haskell\" }, \"10\": { \"name\": \"keyword.operator.arrow.tail.haskell\" }, \"11\": { \"name\": \"keyword.operator.arrow.tail.double.haskell\" }, \"12\": { \"name\": \"keyword.other.forall.haskell\" } }, \"match\": \"(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"'']])(?:(\\\\.\\\\.)|(:)|(=)|(\\\\\\\\)|(\\\\|)|(<-|\\u2190)|(->|\\u2192)|(-<|\\u21A2)|(-<<|\\u291B)|(>-|\\u291A)|(>>-|\\u291C)|(\\u2200))(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"'']])\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.postfix.hash.haskell\" } }, \"match\": \"(?<=[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\\\p{S}\\\\p{P}&&[^#,;\\\\[`{]])(\\\\#+)(?![\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\\\p{S}\\\\p{P}&&[^),;\\\\]`}]])\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.infix.tight.at.haskell\" } }, \"match\": \"(?<=[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd})}\\\\]])(@)(?=[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}(\\\\[{])\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.prefix.tilde.haskell\" }, \"2\": { \"name\": \"keyword.operator.prefix.bang.haskell\" }, \"3\": { \"name\": \"keyword.operator.prefix.minus.haskell\" }, \"4\": { \"name\": \"keyword.operator.prefix.dollar.haskell\" }, \"5\": { \"name\": \"keyword.operator.prefix.double-dollar.haskell\" } }, \"match\": \"(?<![\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{]])(?:(~)|(!)|(-)|(\\\\$)|(\\\\$\\\\$))(?=[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}({\\\\[])\" }] }, \"role_annotation\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)(type)\\\\s+(role)\\\\b(?!')\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.type.haskell\" }, \"3\": { \"name\": \"keyword.other.role.haskell\" } }, \"end\": \"(?=\\\\}|;)|^(?!\\\\1\\\\s+\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$))\", \"name\": \"meta.role-annotation.haskell\", \"patterns\": [{ \"include\": \"#comment_like\" }, { \"include\": \"#type_constructor\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.role.$1.haskell\" } }, \"match\": \"\\\\b(?<!')(nominal|representational|phantom)\\\\b(?!')\" }] }] }, \"start_type_signature\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{_\\\"']])\\\\s*\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.operator.double-colon.haskell\" } }, \"end\": \"(?=\\\\#?\\\\)|\\\\]|,|(?<!')\\\\b(in|then|else|of)\\\\b(?!')|(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:(\\\\\\\\|\\u03BB)|(<-|\\u2190)|(=)|(-<|\\u21A2)|(-<<|\\u291B))([(),;\\\\[\\\\]`{}_\\\"']|[^\\\\p{S}\\\\p{P}])|(\\\\#|@)-\\\\}|(?=\\\\}|;)|^(?!\\\\1\\\\s*\\\\S|\\\\s*(?:$|\\\\{-[^@]|--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]{}`_\\\"']]).*$)))\", \"name\": \"meta.type-declaration.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"(?<![\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{_\\\"']])(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(,;\\\\[`{_\\\"']])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.double-colon.haskell\" } }, \"end\": \"(?=\\\\#?\\\\)|\\\\]|,|\\\\b(?<!')(in|then|else|of)\\\\b(?!')|(\\\\#|@)-\\\\}|(?<![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])(?:(\\\\\\\\|\\u03BB)|(<-|\\u2190)|(=)|(-<|\\u21A2)|(-<<|\\u291B))([(),;\\\\[\\\\]`{}_\\\"']|[^\\\\p{S}\\\\p{P}])|(?=\\\\}|;)|$)\", \"patterns\": [{ \"include\": \"#type_signature\" }] }] }, \"string_literal\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.haskell\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.haskell\" } }, \"name\": \"string.quoted.double.haskell\", \"patterns\": [{ \"match\": `\\\\\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\&])`, \"name\": \"constant.character.escape.haskell\" }, { \"match\": \"\\\\\\\\o[0-7]+|\\\\\\\\x[0-9A-Fa-f]+|\\\\\\\\\\\\d+\", \"name\": \"constant.character.escape.octal.haskell\" }, { \"match\": \"\\\\\\\\\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_]\", \"name\": \"constant.character.escape.control.haskell\" }, { \"begin\": \"\\\\\\\\\\\\s\", \"beginCaptures\": { \"0\": { \"name\": \"constant.character.escape.begin.haskell\" } }, \"end\": \"\\\\\\\\\", \"endCaptures\": { \"0\": { \"name\": \"constant.character.escape.end.haskell\" } }, \"patterns\": [{ \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.haskell\" }] }] }, \"type_application\": { \"patterns\": [{ \"begin\": `(?<=[\\\\s,;\\\\[\\\\]{}\"])(@)(')?(\\\\()`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.prefix.at.haskell\" }, \"2\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.paren.haskell\" } }, \"name\": \"meta.type-application.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": `(?<=[\\\\s,;\\\\[\\\\]{}\"])(@)(')?(\\\\[)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.prefix.at.haskell\" }, \"2\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"3\": { \"name\": \"punctuation.bracket.haskell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.haskell\" } }, \"name\": \"meta.type-application.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": '(?<=[\\\\s,;\\\\[\\\\]{}\"])(@)(?=\\\\\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.prefix.at.haskell\" } }, \"end\": '(?<=\\\\\")', \"name\": \"meta.type-application.haskell\", \"patterns\": [{ \"include\": \"#string_literal\" }] }, { \"begin\": `(?<=[\\\\s,;\\\\[\\\\]{}\"])(@)(?=[\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'])`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.prefix.at.haskell\" } }, \"end\": \"(?![\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}'])\", \"name\": \"meta.type-application.haskell\", \"patterns\": [{ \"include\": \"#type_signature\" }] }] }, \"type_constructor\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"entity.name.namespace.haskell\" }, \"3\": { \"name\": \"storage.type.haskell\" } }, \"match\": \"(')?((?:\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\.)*)(\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"entity.name.namespace.haskell\" }, \"4\": { \"name\": \"storage.type.operator.haskell\" }, \"5\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(')?(\\\\()\\\\s*((?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\.)*)([\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+)\\\\s*(\\\\))\" }] }, \"type_operator\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"entity.name.namespace.haskell\" }, \"3\": { \"name\": \"storage.type.operator.infix.haskell\" } }, \"match\": \"(?:(?<!')('))?((?:\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\.)*)(?![#@]?-})(\\\\#+|[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+(?<!\\\\#))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.backtick.haskell\" }, \"3\": { \"name\": \"entity.name.namespace.haskell\" }, \"4\": { \"name\": \"storage.type.infix.haskell\" }, \"5\": { \"name\": \"punctuation.backtick.haskell\" } }, \"match\": \"(')?(\\\\`)((?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\\\\.)*)([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(`)\" }] }, \"type_signature\": { \"patterns\": [{ \"include\": \"#comment_like\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(')?(\\\\()\\\\s*(\\\\))\", \"name\": \"support.constant.unit.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" }, \"3\": { \"name\": \"keyword.operator.hash.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()(#)\\\\s*(#)(\\\\))\", \"name\": \"support.constant.unit.unboxed.haskell\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" }, \"3\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(')?(\\\\()\\\\s*,[\\\\s,]*(\\\\))\", \"name\": \"support.constant.tuple.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" }, \"3\": { \"name\": \"keyword.operator.hash.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()(#)\\\\s*(#)(\\\\))\", \"name\": \"support.constant.unit.unboxed.haskell\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" }, \"3\": { \"name\": \"keyword.operator.hash.haskell\" }, \"4\": { \"name\": \"punctuation.paren.haskell\" } }, \"match\": \"(\\\\()(#)\\\\s*,[\\\\s,]*(#)(\\\\))\", \"name\": \"support.constant.tuple.unboxed.haskell\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.bracket.haskell\" }, \"3\": { \"name\": \"punctuation.bracket.haskell\" } }, \"match\": \"(')?(\\\\[)\\\\s*(\\\\])\", \"name\": \"support.constant.empty-list.haskell\" }, { \"include\": \"#integer_literals\" }, { \"match\": \"(::|\\u2237)(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']])\", \"name\": \"keyword.operator.double-colon.haskell\" }, { \"include\": \"#forall\" }, { \"match\": \"=>|\\u21D2\", \"name\": \"keyword.operator.big-arrow.haskell\" }, { \"include\": \"#string_literal\" }, { \"match\": \"'[^']'\", \"name\": \"invalid\" }, { \"include\": \"#type_application\" }, { \"include\": \"#reserved_symbol\" }, { \"include\": \"#type_operator\" }, { \"include\": \"#type_constructor\" }, { \"begin\": \"(\\\\()(#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" }, \"2\": { \"name\": \"keyword.operator.hash.haskell\" } }, \"end\": \"(#)(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.hash.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"(')?(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.paren.haskell\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.paren.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"(')?(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.promotion.haskell\" }, \"2\": { \"name\": \"punctuation.bracket.haskell\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.bracket.haskell\" } }, \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#type_signature\" }] }, { \"include\": \"#type_variable\" }] }, \"type_variable\": { \"match\": \"\\\\b(?<!')(?!(?:forall|deriving)\\\\b(?!'))[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"variable.other.generic-type.haskell\" }, \"where\": { \"patterns\": [{ \"begin\": \"(?<!')\\\\b(where)\\\\s*(\\\\{)(?!-)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.where.haskell\" }, \"2\": { \"name\": \"punctuation.brace.haskell\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brace.haskell\" } }, \"patterns\": [{ \"include\": \"$self\" }, { \"match\": \";\", \"name\": \"punctuation.semicolon.haskell\" }] }, { \"match\": \"\\\\b(?<!')(where)\\\\b(?!')\", \"name\": \"keyword.other.where.haskell\" }] } }, \"scopeName\": \"source.haskell\", \"aliases\": [\"hs\"] });\nvar haskell = [\n  lang\n];\n\nexport { haskell as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAM;QAAW;KAAO;IAAE,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,SAAS;YAAgB,QAAQ;QAAU;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAAkB,QAAQ;QAAiC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAAwB,QAAQ;QAAyC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAA0B,QAAQ;QAAiC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAAgC,QAAQ;QAAyC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;YAAkB,QAAQ;QAAuC;QAAG;YAAE,SAAS;YAA8C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAA8B,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAU;aAAE;QAAC;QAAG;YAAE,WAAW;QAAO;QAAG;YAAE,SAAS;YAA4B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAA0H,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA6H,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,SAAS;YAAoF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA0B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG;YAAE,SAAS;YAA0M,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAuK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6J,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA+B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAA4H,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAA4H,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAwB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAA6B,QAAQ;QAAkC;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAY,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG;YAAE,WAAW;QAAU;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAkG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAmb,eAAe;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,MAAM;4BAAE,QAAQ;wBAAyB;wBAAG,MAAM;4BAAE,QAAQ;wBAA4B;wBAAG,MAAM;4BAAE,QAAQ;wBAAkC;wBAAG,MAAM;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,uBAAuB;YAAG,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAQ,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,SAAS,CAAC,sRAAsR,CAAC;YAAE,QAAQ;QAA+B;QAAG,SAAS;YAAE,SAAS;YAAK,QAAQ;QAAsC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAA2D,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAAqC;gBAAG;oBAAE,uBAAuB;oBAAG,SAAS;oBAAwB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,WAAW;oBAAoL,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAmC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;QAAmG;QAAG,OAAO;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,WAAW,CAAC,oGAAoG,CAAC;YAAE,SAAS;YAAW,QAAQ;QAAsB;QAAG,oBAAoB;YAAE,SAAS;YAA2E,QAAQ;QAAyB;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAA+F,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,SAAS;4BAA+B,QAAQ;wBAAiC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,SAAS;wBAAgD;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA2H,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAA8C;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;QAA8D;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAkH,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAiH,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwD,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA8D,QAAQ;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,OAAO;YAAE,SAAS;YAA2C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAA+F,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,SAAS;gBAA8D;gBAAG;oBAAE,SAAS,CAAC,wEAAwE,CAAC;oBAAE,OAAO;oBAA2D,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;oCAA+B,YAAY;wCAAC;4CAAE,WAAW;wCAAkB;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,SAAS,CAAC,mNAAmN,CAAC;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;oCAA+B,YAAY;wCAAC;4CAAE,WAAW;wCAAkB;qCAAE;gCAAC;4BAAE;4BAAG,SAAS,CAAC,yEAAyE,CAAC;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAA+B,YAAY;wCAAC;4CAAE,WAAW;wCAAkB;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAAoB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,SAAS;wBAA8I;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,WAAW;YAAqC,SAAS;QAA+O;QAAG,UAAU;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAqB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAmR,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAA4N,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsI,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAA8H,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0H,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,WAAW;oBAAmL,SAAS;gBAA+H;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,WAAW;oBAAsM,SAAS;oBAA2H,QAAQ;gBAA0C;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAU;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,SAAS;QAAgH;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,SAAS;QAAmF;QAAG,iBAAiB;YAAE,SAAS;YAAoD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAgC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAS,OAAO;YAAS,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,uBAAuB;YAAG,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA4B;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAA+E,QAAQ;QAAgC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAmI,QAAQ;gBAA4B;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAS,OAAO;YAAS,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,SAAS;4BAAiM,QAAQ;wBAAqB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA+C;4BAAE;4BAAG,SAAS;wBAAu1D;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkM,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,WAAW;oBAA6G,SAAS;gBAA8F;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAiE,QAAQ;QAAgC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;gBAA+B;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA+G,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,SAAS;QAA4F;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,MAAM;4BAAE,QAAQ;wBAAsC;wBAAG,MAAM;4BAAE,QAAQ;wBAA6C;wBAAG,MAAM;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA0M;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;gBAAkI;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAuF;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,SAAS;gBAAsI;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA+F,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;4BAAE;4BAAG,SAAS;wBAAsD;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAyS,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAyN,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,SAAS,CAAC,oJAAoJ,CAAC;oBAAE,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAA0C,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAQ,QAAQ;wBAAqD;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,iCAAiC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,iCAAiC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,4DAA4D,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAwC,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAA2H;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAA2H;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA8I;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA6H;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAAsB,QAAQ;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAAwB,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAA8B,QAAQ;gBAAiC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAAwB,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAAgC,QAAQ;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;oBAAsB,QAAQ;gBAAsC;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAAuD,QAAQ;gBAAwC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAU;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAY,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAuF,QAAQ;QAAsC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;aAAE;QAAC;IAAE;IAAG,aAAa;IAAkB,WAAW;QAAC;KAAK;AAAC;AACz22C,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}