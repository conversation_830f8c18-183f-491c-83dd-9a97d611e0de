{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/csharp.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"C#\", \"name\": \"csharp\", \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#directives\" }, { \"include\": \"#declarations\" }, { \"include\": \"#script-top-level\" }], \"repository\": { \"accessor-getter\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"contentName\": \"meta.accessor.getter.cs\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#statement\" }] }, { \"include\": \"#accessor-getter-expression\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"accessor-getter-expression\": { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.arrow.cs\" } }, \"contentName\": \"meta.accessor.getter.cs\", \"end\": \"(?=;|\\\\})\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, \"accessor-setter\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"contentName\": \"meta.accessor.setter.cs\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#statement\" }] }, { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.arrow.cs\" } }, \"contentName\": \"meta.accessor.setter.cs\", \"end\": \"(?=;|\\\\})\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#punctuation-semicolon\" }] }, \"anonymous-method-expression\": { \"patterns\": [{ \"begin\": \"((?:\\\\b(?:async|static)\\\\b\\\\s*)*)(?:(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b|(\\\\()(?<tuple>(?:[^()]|\\\\(\\\\g<tuple>\\\\))*)(\\\\)))\\\\s*(=>)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"async|static\", \"name\": \"storage.modifier.$0.cs\" }] }, \"2\": { \"name\": \"entity.name.variable.parameter.cs\" }, \"3\": { \"name\": \"punctuation.parenthesis.open.cs\" }, \"4\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#explicit-anonymous-function-parameter\" }, { \"include\": \"#implicit-anonymous-function-parameter\" }, { \"include\": \"#default-argument\" }, { \"include\": \"#punctuation-comma\" }] }, \"5\": { \"name\": \"punctuation.parenthesis.close.cs\" }, \"6\": { \"name\": \"keyword.operator.arrow.cs\" } }, \"end\": \"(?=[,;)}])\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"(?={)\", \"end\": \"(?=[,;)}])\", \"patterns\": [{ \"include\": \"#block\" }, { \"include\": \"#intrusive\" }] }, { \"begin\": \"\\\\b(ref)\\\\b|(?=\\\\S)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.ref.cs\" } }, \"end\": \"(?=[,;)}])\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, { \"begin\": \"((?:\\\\b(?:async|static)\\\\b\\\\s*)*)\\\\b(delegate)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"async|static\", \"name\": \"storage.modifier.$0.cs\" }] }, \"2\": { \"name\": \"storage.type.delegate.cs\" } }, \"end\": \"(?<=})|(?=[,;)}])\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#explicit-anonymous-function-parameter\" }, { \"include\": \"#punctuation-comma\" }] }, { \"include\": \"#block\" }] }] }, \"anonymous-object-creation-expression\": { \"begin\": \"\\\\b(new)\\\\b\\\\s*(?=\\\\{|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.new.cs\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#initializer-expression\" }] }, \"argument\": { \"patterns\": [{ \"match\": \"\\\\b(ref|in)\\\\b\", \"name\": \"storage.modifier.$1.cs\" }, { \"begin\": \"\\\\b(out)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.out.cs\" } }, \"end\": \"(?=,|\\\\)|\\\\])\", \"patterns\": [{ \"include\": \"#declaration-expression-local\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#expression\" }] }, \"argument-list\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#named-argument\" }, { \"include\": \"#argument\" }, { \"include\": \"#punctuation-comma\" }] }, \"array-creation-expression\": { \"begin\": \"\\\\b(new|stackalloc)\\\\b\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))?\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.$1.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] } }, \"end\": \"(?<=\\\\])\", \"patterns\": [{ \"include\": \"#bracketed-argument-list\" }] }, \"as-expression\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.as.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] } }, \"match\": \"(?<!\\\\.)\\\\b(as)\\\\b\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?(?!\\\\?))?(?:\\\\s*\\\\[\\\\s*(?:,\\\\s*)*\\\\](?:\\\\s*\\\\?(?!\\\\?))?)*))?\" }, \"assignment-expression\": { \"begin\": \"(?:\\\\*|/|%|\\\\+|-|\\\\?\\\\?|\\\\&|\\\\^|<<|>>>?|\\\\|)?=(?!=|>)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#assignment-operators\" }] } }, \"end\": \"(?=[,)\\\\];}])\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, \"assignment-operators\": { \"patterns\": [{ \"match\": \"\\\\*=|/=|%=|\\\\+=|-=|\\\\?\\\\?=\", \"name\": \"keyword.operator.assignment.compound.cs\" }, { \"match\": \"\\\\&=|\\\\^=|<<=|>>>?=|\\\\|=\", \"name\": \"keyword.operator.assignment.compound.bitwise.cs\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.cs\" }] }, \"attribute\": { \"patterns\": [{ \"include\": \"#type-name\" }, { \"include\": \"#type-arguments\" }, { \"include\": \"#attribute-arguments\" }] }, \"attribute-arguments\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#attribute-named-argument\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"attribute-named-argument\": { \"begin\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(?==)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.variable.property.cs\" } }, \"end\": \"(?=(,|\\\\)))\", \"patterns\": [{ \"include\": \"#operator-assignment\" }, { \"include\": \"#expression\" }] }, \"attribute-section\": { \"begin\": \"(\\\\[)(assembly|module|field|event|method|param|property|return|type)?(:)?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.open.cs\" }, \"2\": { \"name\": \"keyword.other.attribute-specifier.cs\" }, \"3\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attribute\" }, { \"include\": \"#punctuation-comma\" }] }, \"await-expression\": { \"match\": \"(?<!\\\\.\\\\s*)\\\\b(await)\\\\b\", \"name\": \"keyword.operator.expression.await.cs\" }, \"await-statement\": { \"begin\": \"(?<!\\\\.\\\\s*)\\\\b(await)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.await.cs\" } }, \"end\": \"(?<=})|(?=;|})\", \"patterns\": [{ \"include\": \"#foreach-statement\" }, { \"include\": \"#using-statement\" }, { \"include\": \"#expression\" }] }, \"base-types\": { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(?=\\\\{|where|;)\", \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#preprocessor\" }] }, \"block\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#statement\" }] }, \"boolean-literal\": { \"patterns\": [{ \"match\": \"(?<!\\\\.)\\\\btrue\\\\b\", \"name\": \"constant.language.boolean.true.cs\" }, { \"match\": \"(?<!\\\\.)\\\\bfalse\\\\b\", \"name\": \"constant.language.boolean.false.cs\" }] }, \"bracketed-argument-list\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.open.cs\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"patterns\": [{ \"include\": \"#named-argument\" }, { \"include\": \"#argument\" }, { \"include\": \"#punctuation-comma\" }] }, \"bracketed-parameter-list\": { \"begin\": \"(?=(\\\\[))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.open.cs\" } }, \"end\": \"(?=(\\\\]))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"patterns\": [{ \"begin\": \"(?<=\\\\[)\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#parameter\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#variable-initializer\" }] }] }, \"break-or-continue-statement\": { \"match\": \"(?<!\\\\.)\\\\b(break|continue)\\\\b\", \"name\": \"keyword.control.flow.$1.cs\" }, \"case-guard\": { \"patterns\": [{ \"include\": \"#parenthesized-expression\" }, { \"include\": \"#expression\" }] }, \"cast-expression\": { \"captures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"match\": \"(\\\\()\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*(\\\\))(?=\\\\s*-*!*@?[_0-9A-Za-z(])\" }, \"casted-constant-pattern\": { \"begin\": `(\\\\()([\\\\s.:@_0-9A-Za-z]+)(\\\\))(?=[\\\\s+\\\\-!~]*@?[_0-9A-Za-z('\"]+)`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-builtin\" }, { \"include\": \"#type-name\" }] }, \"3\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#casted-constant-pattern\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#constant-pattern\" }] }, { \"include\": \"#constant-pattern\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.alias.cs\" }, \"2\": { \"name\": \"punctuation.separator.coloncolon.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(::)\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\.)\" }, { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"variable.other.constant.cs\" }] }, \"catch-clause\": { \"begin\": \"(?<!\\\\.)\\\\b(catch)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.exception.catch.cs\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"6\": { \"name\": \"entity.name.variable.local.cs\" } }, \"match\": \"(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*(?:(\\\\g<identifier>)\\\\b)?\" }] }, { \"include\": \"#when-clause\" }, { \"include\": \"#comment\" }, { \"include\": \"#block\" }] }, \"char-character-escape\": { \"match\": \"\\\\\\\\(x[0-9a-fA-F]{1,4}|u[0-9a-fA-F]{4}|.)\", \"name\": \"constant.character.escape.cs\" }, \"char-literal\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.char.begin.cs\" } }, \"end\": \"(\\\\')|((?:[^\\\\\\\\\\\\n])$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.char.end.cs\" }, \"2\": { \"name\": \"invalid.illegal.newline.cs\" } }, \"name\": \"string.quoted.single.cs\", \"patterns\": [{ \"include\": \"#char-character-escape\" }] }, \"class-declaration\": { \"begin\": \"(?=(\\\\brecord\\\\b\\\\s+)?\\\\bclass\\\\b)\", \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"begin\": \"(\\\\b(record)\\\\b\\\\s+)?\\\\b(class)\\\\b\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.record.cs\" }, \"3\": { \"name\": \"storage.type.class.cs\" }, \"4\": { \"name\": \"entity.name.type.class.cs\" } }, \"end\": \"(?=\\\\{)|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameter-list\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#base-types\" }, { \"include\": \"#generic-constraints\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#class-or-struct-members\" }] }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"class-or-struct-members\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#storage-modifier\" }, { \"include\": \"#type-declarations\" }, { \"include\": \"#property-declaration\" }, { \"include\": \"#field-declaration\" }, { \"include\": \"#event-declaration\" }, { \"include\": \"#indexer-declaration\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#constructor-declaration\" }, { \"include\": \"#destructor-declaration\" }, { \"include\": \"#operator-declaration\" }, { \"include\": \"#conversion-operator-declaration\" }, { \"include\": \"#method-declaration\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"combinator-pattern\": { \"match\": \"\\\\b(and|or|not)\\\\b\", \"name\": \"keyword.operator.expression.pattern.combinator.$1.cs\" }, \"comment\": { \"patterns\": [{ \"begin\": \"(^\\\\s+)?(///)(?!/)\", \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.cs\" }, \"2\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"name\": \"comment.block.documentation.cs\", \"patterns\": [{ \"include\": \"#xml-doc-comment\" }], \"while\": \"^(\\\\s*)(///)(?!/)\" }, { \"begin\": \"(^\\\\s+)?(/\\\\*\\\\*)(?!/)\", \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.cs\" }, \"2\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"end\": \"(^\\\\s+)?(\\\\*/)\", \"name\": \"comment.block.documentation.cs\", \"patterns\": [{ \"begin\": \"\\\\G(?=(?~\\\\*/)$)\", \"patterns\": [{ \"include\": \"#xml-doc-comment\" }], \"while\": \"^(\\\\s*+)(\\\\*(?!/))?(?=(?~\\\\*/)$)\", \"whileCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.cs\" }, \"2\": { \"name\": \"punctuation.definition.comment.cs\" } } }, { \"include\": \"#xml-doc-comment\" }] }, { \"begin\": \"(^\\\\s+)?(//).*$\", \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.cs\" }, \"2\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"name\": \"comment.line.double-slash.cs\", \"while\": \"^(\\\\s*)(//).*$\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.cs\" }] }, \"conditional-operator\": { \"patterns\": [{ \"match\": \"\\\\?(?!\\\\?|\\\\s*[.\\\\[])\", \"name\": \"keyword.operator.conditional.question-mark.cs\" }, { \"match\": \":\", \"name\": \"keyword.operator.conditional.colon.cs\" }] }, \"constant-pattern\": { \"patterns\": [{ \"include\": \"#boolean-literal\" }, { \"include\": \"#null-literal\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#char-literal\" }, { \"include\": \"#string-literal\" }, { \"include\": \"#raw-string-literal\" }, { \"include\": \"#verbatim-string-literal\" }, { \"include\": \"#type-operator-expression\" }, { \"include\": \"#expression-operator-expression\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#casted-constant-pattern\" }] }, \"constructor-declaration\": { \"begin\": \"(?=@?[_A-Za-z][_0-9A-Za-z]*\\\\s*\\\\()\", \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\" }, { \"begin\": \"(:)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(?=\\\\{|=>)\", \"patterns\": [{ \"include\": \"#constructor-initializer\" }] }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"constructor-initializer\": { \"begin\": \"\\\\b(base|this)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.language.$1.cs\" } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#argument-list\" }] }, \"context-control-paren-statement\": { \"patterns\": [{ \"include\": \"#fixed-statement\" }, { \"include\": \"#lock-statement\" }, { \"include\": \"#using-statement\" }] }, \"context-control-statement\": { \"match\": \"\\\\b(checked|unchecked|unsafe)\\\\b(?!\\\\s*[@_A-Za-z(])\", \"name\": \"keyword.control.context.$1.cs\" }, \"conversion-operator-declaration\": { \"begin\": \"(?<explicit_or_implicit_keyword>(?:\\\\b(?:explicit|implicit)))\\\\s*(?<operator_keyword>(?:\\\\b(?:operator)))\\\\s*(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.modifier.explicit.cs\" } }, \"match\": \"\\\\b(explicit)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.implicit.cs\" } }, \"match\": \"\\\\b(implicit)\\\\b\" }] }, \"2\": { \"name\": \"storage.type.operator.cs\" }, \"3\": { \"patterns\": [{ \"include\": \"#type\" }] } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"declaration-expression-local\": { \"captures\": { \"1\": { \"name\": \"storage.type.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.local.cs\" } }, \"match\": \"(?:\\\\b(var)\\\\b|(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*)))\\\\s+(\\\\g<identifier>)\\\\b\\\\s*(?=[,)\\\\]])\" }, \"declaration-expression-tuple\": { \"captures\": { \"1\": { \"name\": \"storage.type.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.tuple-element.cs\" } }, \"match\": \"(?:\\\\b(var)\\\\b|(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*)))\\\\s+(\\\\g<identifier>)\\\\b\\\\s*(?=[,)])\" }, \"declarations\": { \"patterns\": [{ \"include\": \"#namespace-declaration\" }, { \"include\": \"#type-declarations\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"default-argument\": { \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.assignment.cs\" } }, \"end\": \"(?=,|\\\\))\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"default-literal-expression\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.default.cs\" } }, \"match\": \"\\\\b(default)\\\\b\" }, \"delegate-declaration\": { \"begin\": \"(?:\\\\b(delegate)\\\\b)\\\\s+(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+(\\\\g<identifier>)\\\\s*(<([^<>]+)>)?\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.delegate.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.type.delegate.cs\" }, \"8\": { \"patterns\": [{ \"include\": \"#type-parameter-list\" }] } }, \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#generic-constraints\" }] }, \"designation-pattern\": { \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#punctuation-comma\" }, { \"include\": \"#designation-pattern\" }] }, { \"include\": \"#simple-designation-pattern\" }] }, \"destructor-declaration\": { \"begin\": \"(~)(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.tilde.cs\" }, \"2\": { \"name\": \"entity.name.function.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"directives\": { \"patterns\": [{ \"include\": \"#extern-alias-directive\" }, { \"include\": \"#using-directive\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"discard-pattern\": { \"match\": \"_(?![_0-9A-Za-z])\", \"name\": \"variable.language.discard.cs\" }, \"do-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(do)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.loop.do.cs\" } }, \"end\": \"(?=;|})\", \"patterns\": [{ \"include\": \"#statement\" }] }, \"double-raw-interpolation\": { \"begin\": \"(?<=[^{][^{]|^)((?:\\\\{)*)(\\\\{\\\\{)(?=[^{])\", \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.cs\" }, \"2\": { \"name\": \"punctuation.definition.interpolation.begin.cs\" } }, \"end\": \"\\\\}\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.interpolation.end.cs\" } }, \"name\": \"meta.interpolation.cs\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"element-access-expression\": { \"begin\": \"(?:(?:(\\\\?)\\\\s*)?(\\\\.)\\\\s*|(->)\\\\s*)?(?:(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*)?(?:(\\\\?)\\\\s*)?(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.null-conditional.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" }, \"3\": { \"name\": \"punctuation.accessor.pointer.cs\" }, \"4\": { \"name\": \"variable.other.object.property.cs\" }, \"5\": { \"name\": \"keyword.operator.null-conditional.cs\" } }, \"end\": \"(?<=\\\\])(?!\\\\s*\\\\[)\", \"patterns\": [{ \"include\": \"#bracketed-argument-list\" }] }, \"else-part\": { \"begin\": \"(?<!\\\\.)\\\\b(else)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.else.cs\" } }, \"end\": \"(?<=})|(?=;)\", \"patterns\": [{ \"include\": \"#statement\" }] }, \"enum-declaration\": { \"begin\": \"(?=\\\\benum\\\\b)\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"begin\": \"(?=enum)\", \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.enum.cs\" }, \"2\": { \"name\": \"entity.name.type.enum.cs\" } }, \"match\": \"(enum)\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#type\" }] }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#punctuation-comma\" }, { \"begin\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.variable.enum-member.cs\" } }, \"end\": \"(?=(,|\\\\}))\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }] }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"event-accessors\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attribute-section\" }, { \"begin\": \"\\\\b(add|remove)\\\\b\\\\s*(?=\\\\{|;|=>|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.accessor.$1.cs\" } }, \"end\": \"(?<=\\\\}|;)|(?=\\\\})\", \"patterns\": [{ \"include\": \"#accessor-setter\" }] }] }, \"event-declaration\": { \"begin\": \"\\\\b(event)\\\\b\\\\s*(?<return_type>(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+)(?<interface_name>\\\\g<type_name>\\\\s*\\\\.\\\\s*)?(\\\\g<identifier>)\\\\s*(?=\\\\{|;|,|=|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.event.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"8\": { \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-accessor\" }] }, \"9\": { \"name\": \"entity.name.variable.event.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#event-accessors\" }, { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.variable.event.cs\" }, { \"include\": \"#punctuation-comma\" }, { \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.assignment.cs\" } }, \"end\": \"(?<=,)|(?=;)\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }] }, \"explicit-anonymous-function-parameter\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.parameter.cs\" } }, \"match\": \"(?:\\\\b(ref|params|out|in)\\\\b\\\\s*)?(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args><(?:[^<>]|\\\\g<type_args>)*>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)*\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*\\\\b(\\\\g<identifier>)\\\\b\" }, \"expression\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#expression-operator-expression\" }, { \"include\": \"#type-operator-expression\" }, { \"include\": \"#default-literal-expression\" }, { \"include\": \"#throw-expression\" }, { \"include\": \"#raw-interpolated-string\" }, { \"include\": \"#interpolated-string\" }, { \"include\": \"#verbatim-interpolated-string\" }, { \"include\": \"#type-builtin\" }, { \"include\": \"#language-variable\" }, { \"include\": \"#switch-statement-or-expression\" }, { \"include\": \"#with-expression\" }, { \"include\": \"#conditional-operator\" }, { \"include\": \"#assignment-expression\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#await-expression\" }, { \"include\": \"#query-expression\" }, { \"include\": \"#as-expression\" }, { \"include\": \"#is-expression\" }, { \"include\": \"#anonymous-method-expression\" }, { \"include\": \"#object-creation-expression\" }, { \"include\": \"#array-creation-expression\" }, { \"include\": \"#anonymous-object-creation-expression\" }, { \"include\": \"#invocation-expression\" }, { \"include\": \"#member-access-expression\" }, { \"include\": \"#element-access-expression\" }, { \"include\": \"#cast-expression\" }, { \"include\": \"#literal\" }, { \"include\": \"#parenthesized-expression\" }, { \"include\": \"#tuple-deconstruction-assignment\" }, { \"include\": \"#initializer-expression\" }, { \"include\": \"#identifier\" }] }, \"expression-body\": { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.arrow.cs\" } }, \"end\": \"(?=[,);}])\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, \"expression-operator-expression\": { \"begin\": \"\\\\b(checked|unchecked|nameof)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.$1.cs\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"expression-operators\": { \"patterns\": [{ \"match\": \"<<|>>>?\", \"name\": \"keyword.operator.bitwise.shift.cs\" }, { \"match\": \"==|!=\", \"name\": \"keyword.operator.comparison.cs\" }, { \"match\": \"<=|>=|<|>\", \"name\": \"keyword.operator.relational.cs\" }, { \"match\": \"!|&&|\\\\|\\\\|\", \"name\": \"keyword.operator.logical.cs\" }, { \"match\": \"\\\\&|~|\\\\^|\\\\|\", \"name\": \"keyword.operator.bitwise.cs\" }, { \"match\": \"--\", \"name\": \"keyword.operator.decrement.cs\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.cs\" }, { \"match\": \"\\\\+|-(?!>)|\\\\*|/|%\", \"name\": \"keyword.operator.arithmetic.cs\" }, { \"match\": \"\\\\?\\\\?\", \"name\": \"keyword.operator.null-coalescing.cs\" }, { \"match\": \"\\\\.\\\\.\", \"name\": \"keyword.operator.range.cs\" }] }, \"extern-alias-directive\": { \"begin\": \"\\\\b(extern)\\\\s+(alias)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.directive.extern.cs\" }, \"2\": { \"name\": \"keyword.other.directive.alias.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"variable.other.alias.cs\" }] }, \"field-declaration\": { \"begin\": \"(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+(\\\\g<identifier>)\\\\s*(?!=>|==)(?=,|;|=|$)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"6\": { \"name\": \"entity.name.variable.field.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.variable.field.cs\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#class-or-struct-members\" }] }, \"finally-clause\": { \"begin\": \"(?<!\\\\.)\\\\b(finally)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.exception.finally.cs\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#block\" }] }, \"fixed-statement\": { \"begin\": \"\\\\b(fixed)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.context.fixed.cs\" } }, \"end\": \"(?<=\\\\))|(?=;|})\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#local-variable-declaration\" }] }] }, \"for-statement\": { \"begin\": \"\\\\b(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.loop.for.cs\" } }, \"end\": \"(?<=\\\\))|(?=;|})\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"begin\": \"(?=[^;)])\", \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#local-variable-declaration\" }] }, { \"begin\": \"(?=;)\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-semicolon\" }] }] }] }, \"foreach-statement\": { \"begin\": \"\\\\b(foreach)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.loop.foreach.cs\" } }, \"end\": \"(?<=\\\\))|(?=;|})\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.ref.cs\" }, \"2\": { \"name\": \"storage.type.var.cs\" }, \"3\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"8\": { \"name\": \"entity.name.variable.local.cs\" }, \"9\": { \"name\": \"keyword.control.loop.in.cs\" } }, \"match\": \"(?:(?:(\\\\bref)\\\\s+)?(\\\\bvar\\\\b)|(?<type_name>(?:(?:ref\\\\s+)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*)))\\\\s+(\\\\g<identifier>)\\\\s+\\\\b(in)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#tuple-declaration-deconstruction-element-list\" }] }, \"3\": { \"name\": \"keyword.control.loop.in.cs\" } }, \"match\": \"(?:\\\\b(var)\\\\b\\\\s*)?(?<tuple>\\\\((?:[^()]|\\\\g<tuple>)+\\\\))\\\\s+\\\\b(in)\\\\b\" }, { \"include\": \"#expression\" }] }] }, \"generic-constraints\": { \"begin\": \"(where)\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.where.cs\" }, \"2\": { \"name\": \"entity.name.type.type-parameter.cs\" }, \"3\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(?=\\\\{|where|;|=>)\", \"patterns\": [{ \"match\": \"\\\\bclass\\\\b\", \"name\": \"storage.type.class.cs\" }, { \"match\": \"\\\\bstruct\\\\b\", \"name\": \"storage.type.struct.cs\" }, { \"match\": \"\\\\bdefault\\\\b\", \"name\": \"keyword.other.constraint.default.cs\" }, { \"match\": \"\\\\bnotnull\\\\b\", \"name\": \"keyword.other.constraint.notnull.cs\" }, { \"match\": \"\\\\bunmanaged\\\\b\", \"name\": \"keyword.other.constraint.unmanaged.cs\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.new.cs\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.cs\" }, \"3\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"match\": \"(new)\\\\s*(\\\\()\\\\s*(\\\\))\" }, { \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#generic-constraints\" }] }, \"goto-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(goto)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.goto.cs\" } }, \"end\": \"(?=[;}])\", \"patterns\": [{ \"begin\": \"\\\\b(case)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.case.cs\" } }, \"end\": \"(?=[;}])\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.conditional.default.cs\" } }, \"match\": \"\\\\b(default)\\\\b\" }, { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.label.cs\" }] }, \"group-by\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.by.cs\" } }, \"match\": \"\\\\b(by)\\\\b\\\\s*\" }, \"group-clause\": { \"begin\": \"\\\\b(group)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.group.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#group-by\" }, { \"include\": \"#group-into\" }, { \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"group-into\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.into.cs\" }, \"2\": { \"name\": \"entity.name.variable.range-variable.cs\" } }, \"match\": \"\\\\b(into)\\\\b\\\\s*(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\\\\s*\" }, \"identifier\": { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"variable.other.readwrite.cs\" }, \"if-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(if)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.if.cs\" } }, \"end\": \"(?<=})|(?=;)\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"include\": \"#statement\" }] }, \"implicit-anonymous-function-parameter\": { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\\\\b\", \"name\": \"entity.name.variable.parameter.cs\" }, \"indexer-declaration\": { \"begin\": \"(?<return_type>(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+)(?<interface_name>\\\\g<type_name>\\\\s*\\\\.\\\\s*)?(?<indexer_name>this)\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-accessor\" }] }, \"8\": { \"name\": \"variable.language.this.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#bracketed-parameter-list\" }, { \"include\": \"#property-accessors\" }, { \"include\": \"#accessor-getter-expression\" }, { \"include\": \"#variable-initializer\" }] }, \"initializer-expression\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"interface-declaration\": { \"begin\": \"(?=\\\\binterface\\\\b)\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"begin\": \"(interface)\\\\b\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.interface.cs\" }, \"2\": { \"name\": \"entity.name.type.interface.cs\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameter-list\" }, { \"include\": \"#base-types\" }, { \"include\": \"#generic-constraints\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#interface-members\" }] }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"interface-members\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#storage-modifier\" }, { \"include\": \"#property-declaration\" }, { \"include\": \"#event-declaration\" }, { \"include\": \"#indexer-declaration\" }, { \"include\": \"#method-declaration\" }, { \"include\": \"#operator-declaration\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"interpolated-string\": { \"begin\": '\\\\$\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '(\")|((?:[^\\\\\\\\\\\\n])$)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.cs\" }, \"2\": { \"name\": \"invalid.illegal.newline.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#string-character-escape\" }, { \"include\": \"#interpolation\" }] }, \"interpolation\": { \"begin\": \"(?<=[^{]|^)((?:\\\\{\\\\{)*)(\\\\{)(?=[^{])\", \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.cs\" }, \"2\": { \"name\": \"punctuation.definition.interpolation.begin.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.interpolation.end.cs\" } }, \"name\": \"meta.interpolation.cs\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"intrusive\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"invocation-expression\": { \"begin\": \"(?:(?:(\\\\?)\\\\s*)?(\\\\.)\\\\s*|(->)\\\\s*)?(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(<(?<type_args>[^<>()]++|<\\\\g<type_args>*+>|\\\\(\\\\g<type_args>*+\\\\))*+>\\\\s*)?(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.null-conditional.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" }, \"3\": { \"name\": \"punctuation.accessor.pointer.cs\" }, \"4\": { \"name\": \"entity.name.function.cs\" }, \"5\": { \"patterns\": [{ \"include\": \"#type-arguments\" }] } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#argument-list\" }] }, \"is-expression\": { \"begin\": \"(?<!\\\\.)\\\\b(is)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.pattern.is.cs\" } }, \"end\": \"(?=[)}\\\\],;:?=&|^]|!=)\", \"patterns\": [{ \"include\": \"#pattern\" }] }, \"join-clause\": { \"begin\": \"\\\\b(join)\\\\b\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))?\\\\s+(\\\\g<identifier>)\\\\b\\\\s*\\\\b(in)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.join.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.range-variable.cs\" }, \"8\": { \"name\": \"keyword.operator.expression.query.in.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#join-on\" }, { \"include\": \"#join-equals\" }, { \"include\": \"#join-into\" }, { \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"join-equals\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.equals.cs\" } }, \"match\": \"\\\\b(equals)\\\\b\\\\s*\" }, \"join-into\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.into.cs\" }, \"2\": { \"name\": \"entity.name.variable.range-variable.cs\" } }, \"match\": \"\\\\b(into)\\\\b\\\\s*(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\\\\s*\" }, \"join-on\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.on.cs\" } }, \"match\": \"\\\\b(on)\\\\b\\\\s*\" }, \"labeled-statement\": { \"captures\": { \"1\": { \"name\": \"entity.name.label.cs\" }, \"2\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\" }, \"language-variable\": { \"patterns\": [{ \"match\": \"\\\\b(base|this)\\\\b\", \"name\": \"variable.language.$1.cs\" }, { \"match\": \"\\\\b(value)\\\\b\", \"name\": \"variable.other.$1.cs\" }] }, \"let-clause\": { \"begin\": \"\\\\b(let)\\\\b\\\\s*(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\\\\s*(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.let.cs\" }, \"2\": { \"name\": \"entity.name.variable.range-variable.cs\" }, \"3\": { \"name\": \"keyword.operator.assignment.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"list-pattern\": { \"begin\": \"(?=\\\\[)\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.open.cs\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"patterns\": [{ \"include\": \"#pattern\" }, { \"include\": \"#punctuation-comma\" }] }, { \"begin\": \"(?<=\\\\])\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#simple-designation-pattern\" }] }] }, \"literal\": { \"patterns\": [{ \"include\": \"#boolean-literal\" }, { \"include\": \"#null-literal\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#char-literal\" }, { \"include\": \"#raw-string-literal\" }, { \"include\": \"#string-literal\" }, { \"include\": \"#verbatim-string-literal\" }, { \"include\": \"#tuple-literal\" }] }, \"local-constant-declaration\": { \"begin\": \"(?<const_keyword>\\\\b(?:const)\\\\b)\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+(\\\\g<identifier>)\\\\s*(?=,|;|=)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.const.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.local.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.variable.local.cs\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, \"local-declaration\": { \"patterns\": [{ \"include\": \"#local-constant-declaration\" }, { \"include\": \"#local-variable-declaration\" }, { \"include\": \"#local-function-declaration\" }, { \"include\": \"#local-tuple-var-deconstruction\" }] }, \"local-function-declaration\": { \"begin\": \"\\\\b((?:(?:async|unsafe|static|extern)\\\\s+)*)(?<type_name>(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?)?(?:\\\\s*\\\\[\\\\s*(?:,\\\\s*)*\\\\](?:\\\\s*\\\\?)?)*)\\\\s+(\\\\g<identifier>)\\\\s*(<[^<>]+>)?\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#storage-modifier\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.function.cs\" }, \"8\": { \"patterns\": [{ \"include\": \"#type-parameter-list\" }] } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#generic-constraints\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"local-tuple-var-deconstruction\": { \"begin\": \"(?:\\\\b(var)\\\\b\\\\s*)(?<tuple>\\\\((?:[^()]|\\\\g<tuple>)+\\\\))\\\\s*(?=;|=|\\\\))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#tuple-declaration-deconstruction-element-list\" }] } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, \"local-variable-declaration\": { \"begin\": \"(?:(?:(\\\\bref)\\\\s+(?:(\\\\breadonly)\\\\s+)?)?(\\\\bvar\\\\b)|(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*[?*]\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*)))\\\\s+(\\\\g<identifier>)\\\\s*(?!=>)(?=,|;|=|\\\\))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.ref.cs\" }, \"2\": { \"name\": \"storage.modifier.readonly.cs\" }, \"3\": { \"name\": \"storage.type.var.cs\" }, \"4\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"9\": { \"name\": \"entity.name.variable.local.cs\" } }, \"end\": \"(?=[;)}])\", \"patterns\": [{ \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.variable.local.cs\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, \"lock-statement\": { \"begin\": \"\\\\b(lock)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.context.lock.cs\" } }, \"end\": \"(?<=\\\\))|(?=;|})\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#expression\" }] }] }, \"member-access-expression\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.null-conditional.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" }, \"3\": { \"name\": \"punctuation.accessor.pointer.cs\" }, \"4\": { \"name\": \"variable.other.object.property.cs\" } }, \"match\": \"(?:(?:(\\\\?)\\\\s*)?(\\\\.)\\\\s*|(->)\\\\s*)(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(?![_0-9A-Za-z]|\\\\(|(\\\\?)?\\\\[|<)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.cs\" }, \"2\": { \"name\": \"variable.other.object.cs\" }, \"3\": { \"patterns\": [{ \"include\": \"#type-arguments\" }] } }, \"match\": \"(\\\\.)?\\\\s*(@?[_A-Za-z][_0-9A-Za-z]*)(?<type_params>\\\\s*<([^<>]|\\\\g<type_params>)+>\\\\s*)(?=(\\\\s*\\\\?)?\\\\s*\\\\.\\\\s*@?[_A-Za-z][_0-9A-Za-z]*)\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.object.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*(?:(?:\\\\?\\\\s*)?\\\\.|->)\\\\s*@?[_A-Za-z][_0-9A-Za-z]*)\" }] }, \"method-declaration\": { \"begin\": \"(?<return_type>(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+)(?<interface_name>\\\\g<type_name>\\\\s*\\\\.\\\\s*)?(\\\\g<identifier>)\\\\s*(<([^<>]+)>)?\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-accessor\" }] }, \"8\": { \"name\": \"entity.name.function.cs\" }, \"9\": { \"patterns\": [{ \"include\": \"#type-parameter-list\" }] } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#generic-constraints\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"named-argument\": { \"begin\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.variable.parameter.cs\" }, \"2\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"end\": \"(?=(,|\\\\)|\\\\]))\", \"patterns\": [{ \"include\": \"#argument\" }] }, \"namespace-declaration\": { \"begin\": \"\\\\b(namespace)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.namespace.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.namespace.cs\" }, { \"include\": \"#punctuation-accessor\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#declarations\" }, { \"include\": \"#using-directive\" }, { \"include\": \"#punctuation-semicolon\" }] }] }, \"null-literal\": { \"match\": \"(?<!\\\\.)\\\\bnull\\\\b\", \"name\": \"constant.language.null.cs\" }, \"numeric-literal\": { \"captures\": { \"0\": { \"patterns\": [{ \"begin\": \"(?=.)\", \"end\": \"$\", \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"constant.numeric.decimal.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"3\": { \"name\": \"constant.numeric.other.separator.thousands.cs\" }, \"4\": { \"name\": \"constant.numeric.other.separator.decimals.cs\" }, \"5\": { \"name\": \"constant.numeric.decimal.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"6\": { \"name\": \"constant.numeric.other.separator.thousands.cs\" }, \"8\": { \"name\": \"constant.numeric.other.exponent.cs\" }, \"9\": { \"name\": \"keyword.operator.arithmetic.cs\" }, \"10\": { \"name\": \"keyword.operator.arithmetic.cs\" }, \"11\": { \"name\": \"constant.numeric.decimal.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"12\": { \"name\": \"constant.numeric.other.suffix.cs\" } }, \"match\": \"(\\\\G(?=[0-9.])(?!0[xXbB]))(\\\\d(?:\\\\d|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?((?:(?<=\\\\d)|\\\\.(?=\\\\d)))(\\\\d(?:\\\\d|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?((?<!_)([eE])(\\\\+?)(-?)((?:\\\\d(?:\\\\d|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?([fFdDmM](?!\\\\w))?$\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.other.preffix.binary.cs\" }, \"2\": { \"name\": \"constant.numeric.binary.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"3\": { \"name\": \"constant.numeric.other.separator.thousands.cs\" }, \"4\": { \"name\": \"constant.numeric.other.suffix.cs\" } }, \"match\": \"(\\\\G0[bB])([01_](?:[01_]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?:(?:(?:(?:[uU]|[uU]l)|[uU]L)|l[uU]?)|L[uU]?)|[fFdDmM])(?!\\\\w))?$\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.other.preffix.hex.cs\" }, \"2\": { \"name\": \"constant.numeric.hex.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"3\": { \"name\": \"constant.numeric.other.separator.thousands.cs\" }, \"4\": { \"name\": \"constant.numeric.other.suffix.cs\" } }, \"match\": \"(\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?:(?:(?:(?:[uU]|[uU]l)|[uU]L)|l[uU]?)|L[uU]?)|[fFdDmM])(?!\\\\w))?$\" }, { \"captures\": { \"2\": { \"name\": \"constant.numeric.decimal.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"3\": { \"name\": \"constant.numeric.other.separator.thousands.cs\" }, \"5\": { \"name\": \"constant.numeric.other.exponent.cs\" }, \"6\": { \"name\": \"keyword.operator.arithmetic.cs\" }, \"7\": { \"name\": \"keyword.operator.arithmetic.cs\" }, \"8\": { \"name\": \"constant.numeric.decimal.cs\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"constant.numeric.other.separator.thousands.cs\" }] }, \"9\": { \"name\": \"constant.numeric.other.suffix.cs\" } }, \"match\": \"(\\\\G(?=[0-9.])(?!0[xXbB]))(\\\\d(?:\\\\d|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?<!_)([eE])(\\\\+?)(-?)((?:\\\\d(?:\\\\d|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?((?:(?:(?:(?:(?:[uU]|[uU]l)|[uU]L)|l[uU]?)|L[uU]?)|[fFdDmM])(?!\\\\w))?$\" }, { \"match\": \"(?:(?:[0-9a-zA-Z_]|_)|(?<=[eE])[+-]|\\\\.\\\\d)+\", \"name\": \"invalid.illegal.constant.numeric.cs\" }] }] } }, \"match\": \"(?<!\\\\w)\\\\.?\\\\d(?:(?:[0-9a-zA-Z_]|_)|(?<=[eE])[+-]|\\\\.\\\\d)*\" }, \"object-creation-expression\": { \"patterns\": [{ \"include\": \"#object-creation-expression-with-parameters\" }, { \"include\": \"#object-creation-expression-with-no-parameters\" }] }, \"object-creation-expression-with-no-parameters\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.new.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] } }, \"match\": \"(new)\\\\s+(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*(?=\\\\{|//|/\\\\*|$)\" }, \"object-creation-expression-with-parameters\": { \"begin\": \"(new)(?:\\\\s+(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*)))?\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.new.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#argument-list\" }] }, \"operator-assignment\": { \"match\": \"(?<!=|!)(=)(?!=)\", \"name\": \"keyword.operator.assignment.cs\" }, \"operator-declaration\": { \"begin\": \"(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s*\\\\b(?<operator_keyword>operator)\\\\b\\\\s*(?<operator>[+\\\\-*/%&|\\\\^!=~<>]+|true|false)\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"6\": { \"name\": \"storage.type.operator.cs\" }, \"7\": { \"name\": \"entity.name.function.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#expression-body\" }, { \"include\": \"#block\" }] }, \"orderby-clause\": { \"begin\": \"\\\\b(orderby)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.orderby.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#ordering-direction\" }, { \"include\": \"#query-body\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"ordering-direction\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.query.$1.cs\" } }, \"match\": \"\\\\b(ascending|descending)\\\\b\" }, \"parameter\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.parameter.cs\" } }, \"match\": \"(?:(?:\\\\b(ref|params|out|in|this)\\\\b)\\\\s+)?(?<type_name>(?:(?:ref\\\\s+)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+(\\\\g<identifier>)\" }, \"parenthesized-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"parenthesized-parameter-list\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#parameter\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#variable-initializer\" }] }, \"pattern\": { \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#combinator-pattern\" }, { \"include\": \"#discard-pattern\" }, { \"include\": \"#constant-pattern\" }, { \"include\": \"#relational-pattern\" }, { \"include\": \"#var-pattern\" }, { \"include\": \"#type-pattern\" }, { \"include\": \"#positional-pattern\" }, { \"include\": \"#property-pattern\" }, { \"include\": \"#list-pattern\" }, { \"include\": \"#slice-pattern\" }] }, \"positional-pattern\": { \"begin\": \"(?=\\\\()\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#subpattern\" }, { \"include\": \"#punctuation-comma\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#property-pattern\" }, { \"include\": \"#simple-designation-pattern\" }] }] }, \"preprocessor\": { \"begin\": \"^\\\\s*(\\\\#)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.hash.cs\" } }, \"end\": \"(?<=$)\", \"name\": \"meta.preprocessor.cs\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#preprocessor-define-or-undef\" }, { \"include\": \"#preprocessor-if-or-elif\" }, { \"include\": \"#preprocessor-else-or-endif\" }, { \"include\": \"#preprocessor-warning-or-error\" }, { \"include\": \"#preprocessor-region\" }, { \"include\": \"#preprocessor-endregion\" }, { \"include\": \"#preprocessor-load\" }, { \"include\": \"#preprocessor-r\" }, { \"include\": \"#preprocessor-line\" }, { \"include\": \"#preprocessor-pragma-warning\" }, { \"include\": \"#preprocessor-pragma-checksum\" }] }, \"preprocessor-define-or-undef\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.define.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.undef.cs\" }, \"3\": { \"name\": \"entity.name.variable.preprocessor.symbol.cs\" } }, \"match\": \"\\\\b(?:(define)|(undef))\\\\b\\\\s*\\\\b([_A-Za-z][_0-9A-Za-z]*)\\\\b\" }, \"preprocessor-else-or-endif\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.else.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.endif.cs\" } }, \"match\": \"\\\\b(?:(else)|(endif))\\\\b\" }, \"preprocessor-endregion\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.endregion.cs\" } }, \"match\": \"\\\\b(endregion)\\\\b\" }, \"preprocessor-expression\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#preprocessor-expression\" }] }, { \"captures\": { \"1\": { \"name\": \"constant.language.boolean.true.cs\" }, \"2\": { \"name\": \"constant.language.boolean.false.cs\" }, \"3\": { \"name\": \"entity.name.variable.preprocessor.symbol.cs\" } }, \"match\": \"\\\\b(?:(true)|(false)|([_A-Za-z][_0-9A-Za-z]*))\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.comparison.cs\" }, \"2\": { \"name\": \"keyword.operator.logical.cs\" } }, \"match\": \"(==|!=)|(!|&&|\\\\|\\\\|)\" }] }, \"preprocessor-if-or-elif\": { \"begin\": \"\\\\b(?:(if)|(elif))\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.preprocessor.if.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.elif.cs\" } }, \"end\": \"(?=$)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#preprocessor-expression\" }] }, \"preprocessor-line\": { \"begin\": \"\\\\b(line)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.preprocessor.line.cs\" } }, \"end\": \"(?=$)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.default.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.hidden.cs\" } }, \"match\": \"\\\\b(?:(default|hidden))\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.cs\" } }, \"match\": \"\\\\d+\" }, { \"captures\": { \"0\": { \"name\": \"string.quoted.double.cs\" } }, \"match\": '\\\\\"[^\"]*\\\\\"' }] }, \"preprocessor-load\": { \"begin\": \"\\\\b(load)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.preprocessor.load.cs\" } }, \"end\": \"(?=$)\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"string.quoted.double.cs\" } }, \"match\": '\\\\\"[^\"]*\\\\\"' }] }, \"preprocessor-pragma-checksum\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.pragma.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.checksum.cs\" }, \"3\": { \"name\": \"string.quoted.double.cs\" }, \"4\": { \"name\": \"string.quoted.double.cs\" }, \"5\": { \"name\": \"string.quoted.double.cs\" } }, \"match\": '\\\\b(pragma)\\\\b\\\\s*\\\\b(checksum)\\\\b\\\\s*(\\\\\"[^\"]*\\\\\")\\\\s*(\\\\\"[^\"]*\\\\\")\\\\s*(\\\\\"[^\"]*\\\\\")' }, \"preprocessor-pragma-warning\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.pragma.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.warning.cs\" }, \"3\": { \"name\": \"keyword.preprocessor.disable.cs\" }, \"4\": { \"name\": \"keyword.preprocessor.restore.cs\" }, \"5\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.cs\" } }, \"match\": \"\\\\d+\" }, { \"include\": \"#punctuation-comma\" }] } }, \"match\": \"\\\\b(pragma)\\\\b\\\\s*\\\\b(warning)\\\\b\\\\s*\\\\b(?:(disable)|(restore))\\\\b(\\\\s*\\\\d+(?:\\\\s*,\\\\s*\\\\d+)?)?\" }, \"preprocessor-r\": { \"begin\": \"\\\\b(r)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.preprocessor.r.cs\" } }, \"end\": \"(?=$)\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"string.quoted.double.cs\" } }, \"match\": '\\\\\"[^\"]*\\\\\"' }] }, \"preprocessor-region\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.region.cs\" }, \"2\": { \"name\": \"string.unquoted.preprocessor.message.cs\" } }, \"match\": \"\\\\b(region)\\\\b\\\\s*(.*)(?=$)\" }, \"preprocessor-warning-or-error\": { \"captures\": { \"1\": { \"name\": \"keyword.preprocessor.warning.cs\" }, \"2\": { \"name\": \"keyword.preprocessor.error.cs\" }, \"3\": { \"name\": \"string.unquoted.preprocessor.message.cs\" } }, \"match\": \"\\\\b(?:(warning)|(error))\\\\b\\\\s*(.*)(?=$)\" }, \"property-accessors\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attribute-section\" }, { \"match\": \"\\\\b(private|protected|internal)\\\\b\", \"name\": \"storage.modifier.$1.cs\" }, { \"begin\": \"\\\\b(get)\\\\b\\\\s*(?=\\\\{|;|=>|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.accessor.$1.cs\" } }, \"end\": \"(?<=\\\\}|;)|(?=\\\\})\", \"patterns\": [{ \"include\": \"#accessor-getter\" }] }, { \"begin\": \"\\\\b(set|init)\\\\b\\\\s*(?=\\\\{|;|=>|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.accessor.$1.cs\" } }, \"end\": \"(?<=\\\\}|;)|(?=\\\\})\", \"patterns\": [{ \"include\": \"#accessor-setter\" }] }] }, \"property-declaration\": { \"begin\": \"(?![\\\\w\\\\s]*\\\\b(?:class|interface|struct|enum|event)\\\\b)(?<return_type>(?<type_name>(?:(?:ref\\\\s+(?:readonly\\\\s+)?)?(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))\\\\s+)(?<interface_name>\\\\g<type_name>\\\\s*\\\\.\\\\s*)?(?<property_name>\\\\g<identifier>)\\\\s*(?=\\\\{|=>|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-accessor\" }] }, \"8\": { \"name\": \"entity.name.variable.property.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#property-accessors\" }, { \"include\": \"#accessor-getter-expression\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#class-or-struct-members\" }] }, \"property-pattern\": { \"begin\": \"(?={)\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#subpattern\" }, { \"include\": \"#punctuation-comma\" }] }, { \"begin\": \"(?<=\\\\})\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#simple-designation-pattern\" }] }] }, \"punctuation-accessor\": { \"match\": \"\\\\.\", \"name\": \"punctuation.accessor.cs\" }, \"punctuation-comma\": { \"match\": \",\", \"name\": \"punctuation.separator.comma.cs\" }, \"punctuation-semicolon\": { \"match\": \";\", \"name\": \"punctuation.terminator.statement.cs\" }, \"query-body\": { \"patterns\": [{ \"include\": \"#let-clause\" }, { \"include\": \"#where-clause\" }, { \"include\": \"#join-clause\" }, { \"include\": \"#orderby-clause\" }, { \"include\": \"#select-clause\" }, { \"include\": \"#group-clause\" }] }, \"query-expression\": { \"begin\": \"\\\\b(from)\\\\b\\\\s*(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))?\\\\s+(\\\\g<identifier>)\\\\b\\\\s*\\\\b(in)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.from.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"7\": { \"name\": \"entity.name.variable.range-variable.cs\" }, \"8\": { \"name\": \"keyword.operator.expression.query.in.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"raw-interpolated-string\": { \"patterns\": [{ \"include\": \"#raw-interpolated-string-five-or-more-quote-one-or-more-interpolation\" }, { \"include\": \"#raw-interpolated-string-three-or-more-quote-three-or-more-interpolation\" }, { \"include\": \"#raw-interpolated-string-quadruple-quote-double-interpolation\" }, { \"include\": \"#raw-interpolated-string-quadruple-quote-single-interpolation\" }, { \"include\": \"#raw-interpolated-string-triple-quote-double-interpolation\" }, { \"include\": \"#raw-interpolated-string-triple-quote-single-interpolation\" }] }, \"raw-interpolated-string-five-or-more-quote-one-or-more-interpolation\": { \"begin\": '\\\\$+\"\"\"\"\"+', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"\"\"+', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\" }, \"raw-interpolated-string-quadruple-quote-double-interpolation\": { \"begin\": '\\\\$\\\\$\"\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#double-raw-interpolation\" }] }, \"raw-interpolated-string-quadruple-quote-single-interpolation\": { \"begin\": '\\\\$\"\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#raw-interpolation\" }] }, \"raw-interpolated-string-three-or-more-quote-three-or-more-interpolation\": { \"begin\": '\\\\$\\\\$\\\\$+\"\"\"+', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"+', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\" }, \"raw-interpolated-string-triple-quote-double-interpolation\": { \"begin\": '\\\\$\\\\$\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#double-raw-interpolation\" }] }, \"raw-interpolated-string-triple-quote-single-interpolation\": { \"begin\": '\\\\$\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#raw-interpolation\" }] }, \"raw-interpolation\": { \"begin\": \"(?<=[^{]|^)((?:\\\\{)*)(\\\\{)(?=[^{])\", \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.cs\" }, \"2\": { \"name\": \"punctuation.definition.interpolation.begin.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.interpolation.end.cs\" } }, \"name\": \"meta.interpolation.cs\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"raw-string-literal\": { \"patterns\": [{ \"include\": \"#raw-string-literal-more\" }, { \"include\": \"#raw-string-literal-quadruple\" }, { \"include\": \"#raw-string-literal-triple\" }] }, \"raw-string-literal-more\": { \"begin\": '\"\"\"\"\"+', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"\"\"+', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\" }, \"raw-string-literal-quadruple\": { \"begin\": '\"\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\" }, \"raw-string-literal-triple\": { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\" }, \"readonly-modifier\": { \"match\": \"\\\\breadonly\\\\b\", \"name\": \"storage.modifier.readonly.cs\" }, \"record-declaration\": { \"begin\": \"(?=\\\\brecord\\\\b)\", \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"begin\": \"(record)\\\\b\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.record.cs\" }, \"2\": { \"name\": \"entity.name.type.class.cs\" } }, \"end\": \"(?=\\\\{)|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameter-list\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#base-types\" }, { \"include\": \"#generic-constraints\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#class-or-struct-members\" }] }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"ref-modifier\": { \"match\": \"\\\\bref\\\\b\", \"name\": \"storage.modifier.ref.cs\" }, \"relational-pattern\": { \"begin\": \"<=?|>=?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.relational.cs\" } }, \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"return-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(return)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.return.cs\" } }, \"end\": \"(?=[;}])\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, \"script-top-level\": { \"patterns\": [{ \"include\": \"#statement\" }, { \"include\": \"#method-declaration\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"select-clause\": { \"begin\": \"\\\\b(select)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.select.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"simple-designation-pattern\": { \"patterns\": [{ \"include\": \"#discard-pattern\" }, { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.variable.local.cs\" }] }, \"slice-pattern\": { \"match\": \"\\\\.\\\\.\", \"name\": \"keyword.operator.range.cs\" }, \"statement\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#while-statement\" }, { \"include\": \"#do-statement\" }, { \"include\": \"#for-statement\" }, { \"include\": \"#foreach-statement\" }, { \"include\": \"#if-statement\" }, { \"include\": \"#else-part\" }, { \"include\": \"#goto-statement\" }, { \"include\": \"#return-statement\" }, { \"include\": \"#break-or-continue-statement\" }, { \"include\": \"#throw-statement\" }, { \"include\": \"#yield-statement\" }, { \"include\": \"#await-statement\" }, { \"include\": \"#try-statement\" }, { \"include\": \"#expression-operator-expression\" }, { \"include\": \"#context-control-statement\" }, { \"include\": \"#context-control-paren-statement\" }, { \"include\": \"#labeled-statement\" }, { \"include\": \"#object-creation-expression\" }, { \"include\": \"#array-creation-expression\" }, { \"include\": \"#anonymous-object-creation-expression\" }, { \"include\": \"#local-declaration\" }, { \"include\": \"#block\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"storage-modifier\": { \"match\": \"(?<!\\\\.)\\\\b(new|public|protected|internal|private|abstract|virtual|override|sealed|static|partial|readonly|volatile|const|extern|async|unsafe|ref|required|file)\\\\b\", \"name\": \"storage.modifier.$1.cs\" }, \"string-character-escape\": { \"match\": \"\\\\\\\\(x[0-9a-fA-F]{1,4}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|.)\", \"name\": \"constant.character.escape.cs\" }, \"string-literal\": { \"begin\": '(?<!@)\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '(\")|((?:[^\\\\\\\\\\\\n])$)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.cs\" }, \"2\": { \"name\": \"invalid.illegal.newline.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"struct-declaration\": { \"begin\": \"(?=(\\\\brecord\\\\b\\\\s+)?\\\\bstruct\\\\b)\", \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"begin\": \"(\\\\b(record)\\\\b\\\\s+)?(struct)\\\\b\\\\s+(@?[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.record.cs\" }, \"3\": { \"name\": \"storage.type.struct.cs\" }, \"4\": { \"name\": \"entity.name.type.struct.cs\" } }, \"end\": \"(?=\\\\{)|(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameter-list\" }, { \"include\": \"#parenthesized-parameter-list\" }, { \"include\": \"#base-types\" }, { \"include\": \"#generic-constraints\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#class-or-struct-members\" }] }, { \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }] }, \"subpattern\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"variable.other.object.property.cs\" }, { \"include\": \"#punctuation-accessor\" }] }, \"2\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*(?:\\\\s*\\\\.\\\\s*@?[_A-Za-z][_0-9A-Za-z]*)*)\\\\s*(:)\" }, { \"include\": \"#pattern\" }] }, \"switch-expression\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#punctuation-comma\" }, { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.arrow.cs\" } }, \"end\": \"(?=,|})\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"\\\\b(when)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.when.cs\" } }, \"end\": \"(?==>|,|})\", \"patterns\": [{ \"include\": \"#case-guard\" }] }, { \"begin\": \"(?!\\\\s)\", \"end\": \"(?=\\\\bwhen\\\\b|=>|,|})\", \"patterns\": [{ \"include\": \"#pattern\" }] }] }, \"switch-label\": { \"begin\": \"\\\\b(case|default)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.$1.cs\" } }, \"end\": \"(:)|(?=})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.colon.cs\" } }, \"patterns\": [{ \"begin\": \"\\\\b(when)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.when.cs\" } }, \"end\": \"(?=:|})\", \"patterns\": [{ \"include\": \"#case-guard\" }] }, { \"begin\": \"(?!\\\\s)\", \"end\": \"(?=\\\\bwhen\\\\b|:|})\", \"patterns\": [{ \"include\": \"#pattern\" }] }] }, \"switch-statement\": { \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"patterns\": [{ \"include\": \"#switch-label\" }, { \"include\": \"#statement\" }] }] }, \"switch-statement-or-expression\": { \"begin\": \"(?<!\\\\.)\\\\b(switch)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.switch.cs\" } }, \"end\": \"(?<=})|(?=})\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"(?=\\\\()\", \"end\": \"(?<=\\\\})|(?=\\\\})\", \"patterns\": [{ \"include\": \"#switch-statement\" }] }, { \"begin\": \"(?=\\\\{)\", \"end\": \"(?<=\\\\})|(?=\\\\})\", \"patterns\": [{ \"include\": \"#switch-expression\" }] }] }, \"throw-expression\": { \"captures\": { \"1\": { \"name\": \"keyword.control.flow.throw.cs\" } }, \"match\": \"\\\\b(throw)\\\\b\" }, \"throw-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(throw)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.throw.cs\" } }, \"end\": \"(?=[;}])\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"try-block\": { \"begin\": \"(?<!\\\\.)\\\\b(try)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.exception.try.cs\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#block\" }] }, \"try-statement\": { \"patterns\": [{ \"include\": \"#try-block\" }, { \"include\": \"#catch-clause\" }, { \"include\": \"#finally-clause\" }] }, \"tuple-declaration-deconstruction-element-list\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#tuple-declaration-deconstruction-element-list\" }, { \"include\": \"#declaration-expression-tuple\" }, { \"include\": \"#punctuation-comma\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.variable.tuple-element.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\\\\s*(?=[,)])\" }] }, \"tuple-deconstruction-assignment\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#tuple-deconstruction-element-list\" }] } }, \"match\": \"(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\))\\\\s*(?!=>|==)(?==)\" }, \"tuple-deconstruction-element-list\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#tuple-deconstruction-element-list\" }, { \"include\": \"#declaration-expression-tuple\" }, { \"include\": \"#punctuation-comma\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.readwrite.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\\\\s*(?=[,)])\" }] }, \"tuple-element\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type\" }] }, \"6\": { \"name\": \"entity.name.variable.tuple-element.cs\" } }, \"match\": \"(?<type_name>(?:(?:(?:(?<identifier>@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*::\\\\s*)?(?<name_and_type_args>\\\\g<identifier>\\\\s*(?<type_args>\\\\s*<(?:[^<>]|\\\\g<type_args>)+>\\\\s*)?)(?:\\\\s*\\\\.\\\\s*\\\\g<name_and_type_args>)*|(?<tuple>\\\\s*\\\\((?:[^()]|\\\\g<tuple>)+\\\\)))(?:\\\\s*\\\\?\\\\s*)?(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*(?:\\\\?)?\\\\s*)*))(?:(?<tuple_name>\\\\g<identifier>)\\\\b)?\" }, \"tuple-literal\": { \"begin\": \"(\\\\()(?=.*[:,])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#tuple-literal-element\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"tuple-literal-element\": { \"begin\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(?=:)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.variable.tuple-element.cs\" } }, \"end\": \"(:)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.colon.cs\" } } }, \"tuple-type\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#tuple-element\" }, { \"include\": \"#punctuation-comma\" }] }, \"type\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#ref-modifier\" }, { \"include\": \"#readonly-modifier\" }, { \"include\": \"#tuple-type\" }, { \"include\": \"#type-builtin\" }, { \"include\": \"#type-name\" }, { \"include\": \"#type-arguments\" }, { \"include\": \"#type-array-suffix\" }, { \"include\": \"#type-nullable-suffix\" }, { \"include\": \"#type-pointer-suffix\" }] }, \"type-arguments\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.cs\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.cs\" } }, \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-array-suffix\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.open.cs\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-builtin\": { \"captures\": { \"1\": { \"name\": \"keyword.type.$1.cs\" } }, \"match\": \"\\\\b(bool|s?byte|u?short|n?u?int|u?long|float|double|decimal|char|string|object|void|dynamic)\\\\b\" }, \"type-declarations\": { \"patterns\": [{ \"include\": \"#preprocessor\" }, { \"include\": \"#comment\" }, { \"include\": \"#storage-modifier\" }, { \"include\": \"#class-declaration\" }, { \"include\": \"#delegate-declaration\" }, { \"include\": \"#enum-declaration\" }, { \"include\": \"#interface-declaration\" }, { \"include\": \"#struct-declaration\" }, { \"include\": \"#record-declaration\" }, { \"include\": \"#attribute-section\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"type-name\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.type.alias.cs\" }, \"2\": { \"name\": \"punctuation.separator.coloncolon.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(::)\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" } }, \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\.)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.cs\" }, \"2\": { \"name\": \"entity.name.type.cs\" } }, \"match\": \"(\\\\.)\\\\s*(@?[_A-Za-z][_0-9A-Za-z]*)\" }, { \"match\": \"@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.cs\" }] }, \"type-nullable-suffix\": { \"match\": \"\\\\?\", \"name\": \"punctuation.separator.question-mark.cs\" }, \"type-operator-expression\": { \"begin\": \"\\\\b(default|sizeof|typeof)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.$1.cs\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, \"type-parameter-list\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.cs\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.cs\" } }, \"patterns\": [{ \"match\": \"\\\\b(in|out)\\\\b\", \"name\": \"storage.modifier.$1.cs\" }, { \"match\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\b\", \"name\": \"entity.name.type.type-parameter.cs\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#attribute-section\" }] }, \"type-pattern\": { \"begin\": \"(?=@?[_A-Za-z][_0-9A-Za-z]*)\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?!\\\\G[@_A-Za-z])(?=[({@_A-Za-z)}\\\\],;:=&|^]|(?:\\\\s|^)\\\\?|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#type-subpattern\" }] }, { \"begin\": \"(?=[({@_A-Za-z])\", \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#positional-pattern\" }, { \"include\": \"#property-pattern\" }, { \"include\": \"#simple-designation-pattern\" }] }] }, \"type-pointer-suffix\": { \"match\": \"\\\\*\", \"name\": \"punctuation.separator.asterisk.cs\" }, \"type-subpattern\": { \"patterns\": [{ \"include\": \"#type-builtin\" }, { \"begin\": \"(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(::)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.type.alias.cs\" }, \"2\": { \"name\": \"punctuation.separator.coloncolon.cs\" } }, \"end\": \"(?<=[_0-9A-Za-z])|(?=[.<\\\\[({)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.cs\" }] }, { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.cs\" }, { \"begin\": \"\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.accessor.cs\" } }, \"end\": \"(?<=[_0-9A-Za-z])|(?=[<\\\\[({)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.cs\" }] }, { \"include\": \"#type-arguments\" }, { \"include\": \"#type-array-suffix\" }, { \"match\": \"(?<!\\\\s)\\\\?\", \"name\": \"punctuation.separator.question-mark.cs\" }] }, \"using-directive\": { \"patterns\": [{ \"begin\": \"\\\\b(?:(global)\\\\s+)?(using)\\\\s+(static)\\\\b\\\\s*(?:(unsafe)\\\\b\\\\s*)?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.directive.global.cs\" }, \"2\": { \"name\": \"keyword.other.directive.using.cs\" }, \"3\": { \"name\": \"keyword.other.directive.static.cs\" }, \"4\": { \"name\": \"storage.modifier.unsafe.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"\\\\b(?:(global)\\\\s+)?(using)\\\\b\\\\s*(?:(unsafe)\\\\b\\\\s*)?(@?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.directive.global.cs\" }, \"2\": { \"name\": \"keyword.other.directive.using.cs\" }, \"3\": { \"name\": \"storage.modifier.unsafe.cs\" }, \"4\": { \"name\": \"entity.name.type.alias.cs\" }, \"5\": { \"name\": \"keyword.operator.assignment.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type\" }] }, { \"begin\": \"\\\\b(?:(global)\\\\s+)?(using)\\\\b\\\\s*+(?!\\\\(|var\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.directive.global.cs\" }, \"2\": { \"name\": \"keyword.other.directive.using.cs\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"\\\\@?[_A-Za-z][_0-9A-Za-z]*\", \"name\": \"entity.name.type.namespace.cs\" }, { \"include\": \"#punctuation-accessor\" }, { \"include\": \"#operator-assignment\" }] }] }, \"using-statement\": { \"begin\": \"\\\\b(using)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.context.using.cs\" } }, \"end\": \"(?<=\\\\))|(?=;|})\", \"patterns\": [{ \"include\": \"#intrusive\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#intrusive\" }, { \"include\": \"#await-expression\" }, { \"include\": \"#local-variable-declaration\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#local-variable-declaration\" }] }, \"var-pattern\": { \"begin\": \"\\\\b(var)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.var.cs\" } }, \"end\": \"(?=[)}\\\\],;:?=&|^]|!=|\\\\b(and|or|when)\\\\b)\", \"patterns\": [{ \"include\": \"#designation-pattern\" }] }, \"variable-initializer\": { \"begin\": \"(?<!=|!)(=)(?!=|>)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.cs\" } }, \"end\": \"(?=[,)\\\\];}])\", \"patterns\": [{ \"include\": \"#ref-modifier\" }, { \"include\": \"#expression\" }] }, \"verbatim-interpolated-string\": { \"begin\": '(?:\\\\$@|@\\\\$)\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"(?=[^\"])', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#verbatim-string-character-escape\" }, { \"include\": \"#interpolation\" }] }, \"verbatim-string-character-escape\": { \"match\": '\"\"', \"name\": \"constant.character.escape.cs\" }, \"verbatim-string-literal\": { \"begin\": '@\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\"(?=[^\"])', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#verbatim-string-character-escape\" }] }, \"when-clause\": { \"begin\": \"(?<!\\\\.)\\\\b(when)\\\\b\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.exception.when.cs\" }, \"2\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#comment\" }] }, \"where-clause\": { \"begin\": \"\\\\b(where)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.query.where.cs\" } }, \"end\": \"(?=;|\\\\))\", \"patterns\": [{ \"include\": \"#query-body\" }, { \"include\": \"#expression\" }] }, \"while-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(while)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.loop.while.cs\" } }, \"end\": \"(?<=\\\\})|(?=;)\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"include\": \"#statement\" }] }, \"with-expression\": { \"begin\": \"(?<!\\\\.)\\\\b(with)\\\\b\\\\s*(?=\\\\{|//|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.with.cs\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#initializer-expression\" }] }, \"xml-attribute\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.cs\" }, \"2\": { \"name\": \"entity.other.attribute-name.namespace.cs\" }, \"3\": { \"name\": \"punctuation.separator.colon.cs\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.cs\" }, \"5\": { \"name\": \"punctuation.separator.equals.cs\" } }, \"match\": \"(?:^|\\\\s+)((?:([-_0-9A-Za-z]+)(:))?([-_0-9A-Za-z]+))(=)\" }, { \"include\": \"#xml-string\" }] }, \"xml-cdata\": { \"begin\": \"<!\\\\[CDATA\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": \"\\\\]\\\\]>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.unquoted.cdata.cs\" }, \"xml-character-entity\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.cs\" }, \"3\": { \"name\": \"punctuation.definition.constant.cs\" } }, \"match\": \"(&)((?:[A-Za-z:_][0-9A-Za-z:_.-]*)|(?:\\\\#[\\\\d]+)|(?:\\\\#x[0-9A-Fa-f]+))(;)\", \"name\": \"constant.character.entity.cs\" }, { \"match\": \"&\", \"name\": \"invalid.illegal.bad-ampersand.cs\" }] }, \"xml-comment\": { \"begin\": \"<!--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"end\": \"-->\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.cs\" } }, \"name\": \"comment.block.cs\" }, \"xml-doc-comment\": { \"patterns\": [{ \"include\": \"#xml-comment\" }, { \"include\": \"#xml-character-entity\" }, { \"include\": \"#xml-cdata\" }, { \"include\": \"#xml-tag\" }] }, \"xml-string\": { \"patterns\": [{ \"begin\": \"\\\\'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": \"\\\\'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.single.cs\", \"patterns\": [{ \"include\": \"#xml-character-entity\" }] }, { \"begin\": '\\\\\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.cs\" } }, \"end\": '\\\\\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.cs\" } }, \"name\": \"string.quoted.double.cs\", \"patterns\": [{ \"include\": \"#xml-character-entity\" }] }] }, \"xml-tag\": { \"begin\": \"(</?)((?:([-_0-9A-Za-z]+)(:))?([-_0-9A-Za-z]+))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.cs\" }, \"2\": { \"name\": \"entity.name.tag.cs\" }, \"3\": { \"name\": \"entity.name.tag.namespace.cs\" }, \"4\": { \"name\": \"punctuation.separator.colon.cs\" }, \"5\": { \"name\": \"entity.name.tag.localname.cs\" } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.cs\" } }, \"name\": \"meta.tag.cs\", \"patterns\": [{ \"include\": \"#xml-attribute\" }] }, \"yield-break-statement\": { \"captures\": { \"1\": { \"name\": \"keyword.control.flow.yield.cs\" }, \"2\": { \"name\": \"keyword.control.flow.break.cs\" } }, \"match\": \"(?<!\\\\.)\\\\b(yield)\\\\b\\\\s*\\\\b(break)\\\\b\" }, \"yield-return-statement\": { \"begin\": \"(?<!\\\\.)\\\\b(yield)\\\\b\\\\s*\\\\b(return)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.yield.cs\" }, \"2\": { \"name\": \"keyword.control.flow.return.cs\" } }, \"end\": \"(?=[;}])\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"yield-statement\": { \"patterns\": [{ \"include\": \"#yield-return-statement\" }, { \"include\": \"#yield-break-statement\" }] } }, \"scopeName\": \"source.cs\", \"aliases\": [\"c#\", \"cs\"] });\nvar csharp = [\n  lang\n];\n\nexport { csharp as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAM,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAoB;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,eAAe;YAA2B,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8H,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAgB,QAAQ;gCAAyB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAyC;gCAAG;oCAAE,WAAW;gCAAyC;gCAAG;oCAAE,WAAW;gCAAoB;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAc,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAAS,OAAO;4BAAc,YAAY;gCAAC;oCAAE,WAAW;gCAAS;gCAAG;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,OAAO;4BAAc,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyD,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAgB,QAAQ;gCAAyB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAyC;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,wCAAwC;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAgW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAsV;QAAG,yBAAyB;YAAE,SAAS;YAAyD,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAiC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAe,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA6B,QAAQ;QAAuC;QAAG,mBAAmB;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAAqC;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAAkC,QAAQ;QAA6B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAsW;QAAG,2BAA2B;YAAE,SAAS,CAAC,iEAAiE,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAqC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA6B;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgC;4BAAE;4BAAG,SAAS;wBAAsV;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAA6C,QAAQ;QAA+B;QAAG,gBAAgB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAsC,OAAO;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAAwE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAsB,QAAQ;QAAuD;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;oBAAE,SAAS;gBAAoB;gBAAG;oBAAE,SAAS;oBAA0B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAkB,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;4BAAE,SAAS;4BAAoC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;wBAAE;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAgC,SAAS;gBAAiB;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAmB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,QAAQ;gBAAgD;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAuC,OAAO;YAAkB,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAgC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAc,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAgC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAuD,QAAQ;QAAgC;QAAG,mCAAmC;YAAE,SAAS;YAA+c,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;4BAAE;4BAAG,SAAS;wBAAmB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;4BAAE;4BAAG,SAAS;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAgX;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;QAA6W;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,SAAS;QAAkB;QAAG,wBAAwB;YAAE,SAAS;YAAga,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAA4C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAqB,QAAQ;QAA+B;QAAG,gBAAgB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAA6C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAiG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAkB,OAAO;YAAY,YAAY;gBAAC;oBAAE,SAAS;oBAAY,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;4BAAE;4BAAG,SAAS;wBAAuC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,SAAS;4BAA4B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAwb,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAgB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,yCAAyC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAkX;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,kCAAkC;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA4B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,QAAQ;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAuW,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA2B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAoB,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAa,OAAO;4BAAa,YAAY;gCAAC;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAA8B;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAa;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAQ;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,SAAS;wBAAyY;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAiD;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,SAAS;wBAA0E;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAgD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAsB,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAA0B;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;gBAAkB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAuB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAAiB;QAAG,gBAAgB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,SAAS;QAAoD;QAAG,cAAc;YAAE,SAAS;YAA4B,QAAQ;QAA8B;QAAG,gBAAgB;YAAE,SAAS;YAAiC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAgB,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,yCAAyC;YAAE,SAAS;YAAiC,QAAQ;QAAoC;QAAG,uBAAuB;YAAE,SAAS;YAAwb,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAuB,OAAO;YAAY,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAA0J,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAqX,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,SAAS;QAAqB;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,SAAS;QAAoD;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAAiB;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAoC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAuB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA2D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAW,OAAO;YAA8C,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAiY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAkC;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAsa,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kCAAkC;YAAE,SAAS;YAA2E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiD;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAA2b,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAqG;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAkB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAA2I;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAuF;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAqc,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAsB,QAAQ;QAA4B;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAS,OAAO;4BAAK,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;4CAA+B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgD;wCAAG,KAAK;4CAAE,QAAQ;wCAA+C;wCAAG,KAAK;4CAAE,QAAQ;4CAA+B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgD;wCAAG,KAAK;4CAAE,QAAQ;wCAAqC;wCAAG,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,MAAM;4CAAE,QAAQ;wCAAiC;wCAAG,MAAM;4CAAE,QAAQ;4CAA+B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,MAAM;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,SAAS;gCAA0P;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA2C;wCAAG,KAAK;4CAAE,QAAQ;4CAA8B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgD;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,SAAS;gCAAuI;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAwC;wCAAG,KAAK;4CAAE,QAAQ;4CAA2B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgD;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,SAAS;gCAAmJ;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;4CAA+B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAgD;wCAAG,KAAK;4CAAE,QAAQ;wCAAqC;wCAAG,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;wCAAiC;wCAAG,KAAK;4CAAE,QAAQ;4CAA+B,YAAY;gDAAC;oDAAE,SAAS;oDAAoC,QAAQ;gDAAgD;6CAAE;wCAAC;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,SAAS;gCAAkO;gCAAG;oCAAE,SAAS;oCAAgD,QAAQ;gCAAsC;6BAAE;wBAAC;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAA8D;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA8C;gBAAG;oBAAE,WAAW;gBAAiD;aAAE;QAAC;QAAG,iDAAiD;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAuV;QAAG,8CAA8C;YAAE,SAAS;YAAmV,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAoB,QAAQ;QAAiC;QAAG,wBAAwB;YAAE,SAAS;YAAyb,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAA+B;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAqY;QAAG,4BAA4B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAS,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAW,OAAO;YAA8C,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,SAAS;QAA+D;QAAG,8BAA8B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAA2B;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAoB;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAAwB;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAAO;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAc;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAc;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;QAAwF;QAAG,+BAA+B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;4BAAE;4BAAG,SAAS;wBAAO;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAkG;QAAG,kBAAkB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAc;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAA8B;QAAG,iCAAiC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAA2C;QAAG,sBAAsB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAsC,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAyC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAygB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAS,OAAO;YAA8C,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAO,QAAQ;QAA0B;QAAG,qBAAqB;YAAE,SAAS;YAAK,QAAQ;QAAiC;QAAG,yBAAyB;YAAE,SAAS;YAAK,QAAQ;QAAsC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAqX,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwE;gBAAG;oBAAE,WAAW;gBAA2E;gBAAG;oBAAE,WAAW;gBAAgE;gBAAG;oBAAE,WAAW;gBAAgE;gBAAG;oBAAE,WAAW;gBAA6D;gBAAG;oBAAE,WAAW;gBAA6D;aAAE;QAAC;QAAG,wEAAwE;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA0B;QAAG,gEAAgE;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,gEAAgE;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,2EAA2E;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA0B;QAAG,6DAA6D;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,6DAA6D;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAsC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA0B;QAAG,gCAAgC;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA0B;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA0B;QAAG,qBAAqB;YAAE,SAAS;YAAkB,QAAQ;QAA+B;QAAG,sBAAsB;YAAE,SAAS;YAAoB,OAAO;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAA6C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAa,QAAQ;QAA0B;QAAG,sBAAsB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA0B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAgC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAU,QAAQ;QAA4B;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAuK,QAAQ;QAAyB;QAAG,2BAA2B;YAAE,SAAS;YAA6D,QAAQ;QAA+B;QAAG,kBAAkB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAuC,OAAO;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAAkE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAA8B,QAAQ;gCAAoC;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA4E;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAc,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,kCAAkC;YAAE,SAAS;YAA0B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAgB;QAAG,mBAAmB;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,iDAAiD;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiD;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;gBAA4C;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqC;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAA8D;QAAG,qCAAqC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAA4C;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;QAA+V;QAAG,iBAAiB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;QAAE;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,SAAS;QAAkG;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAqC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAsB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAO,QAAQ;QAAyC;QAAG,4BAA4B;YAAE,SAAS;YAAuC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAgC,OAAO;YAA8C,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAqF,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,OAAO;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAO,QAAQ;QAAoC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAuE,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAsE,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAyC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAAgC;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAA8B;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,oCAAoC;YAAE,SAAS;YAAM,QAAQ;QAA+B;QAAG,2BAA2B;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAoC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAiC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA6C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA0D;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;QAA2B;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAA6E,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;QAAmB;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAmD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,QAAQ;YAAe,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,SAAS;QAAyC;QAAG,0BAA0B;YAAE,SAAS;YAA2C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAa,WAAW;QAAC;QAAM;KAAK;AAAC;AAChyxF,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}