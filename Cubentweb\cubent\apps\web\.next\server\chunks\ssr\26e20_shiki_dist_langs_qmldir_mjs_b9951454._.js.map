{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/qmldir.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"QML Directory\", \"name\": \"qmldir\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#keywords\" }, { \"include\": \"#version\" }, { \"include\": \"#names\" }], \"repository\": { \"comment\": { \"patterns\": [{ \"begin\": \"#\", \"end\": \"$\", \"name\": \"comment.line.number-sign.qmldir\" }] }, \"file-name\": { \"patterns\": [{ \"match\": \"\\\\b\\\\w+\\\\.(qmltypes|qml|js)\\\\b\", \"name\": \"string.unquoted.qmldir\" }] }, \"identifier\": { \"patterns\": [{ \"match\": \"\\\\b\\\\w+\\\\b\", \"name\": \"variable.parameter.qmldir\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\\\b\", \"name\": \"keyword.other.qmldir\" }] }, \"module-name\": { \"patterns\": [{ \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"entity.name.type.qmldir\" }] }, \"names\": { \"patterns\": [{ \"include\": \"#file-name\" }, { \"include\": \"#module-name\" }, { \"include\": \"#identifier\" }] }, \"version\": { \"patterns\": [{ \"match\": \"\\\\b\\\\d+\\\\.\\\\d+\\\\b\", \"name\": \"constant.numeric.qml\" }] } }, \"scopeName\": \"source.qmldir\" });\nvar qmldir = [\n  lang\n];\n\nexport { qmldir as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAiB,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;gBAAkC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkC,QAAQ;gBAAyB;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAA4B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyF,QAAQ;gBAAuB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,QAAQ;gBAAuB;aAAE;QAAC;IAAE;IAAG,aAAa;AAAgB;AACzgC,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}