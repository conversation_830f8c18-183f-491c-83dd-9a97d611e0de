{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/reg.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Windows Registry Script\", \"fileTypes\": [\"reg\", \"REG\"], \"name\": \"reg\", \"patterns\": [{ \"match\": \"Windows Registry Editor Version 5\\\\.00|REGEDIT4\", \"name\": \"keyword.control.import.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.reg\" } }, \"match\": \"(;).*$\", \"name\": \"comment.line.semicolon.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.reg\" }, \"2\": { \"name\": \"entity.section.reg\" }, \"3\": { \"name\": \"punctuation.definition.section.reg\" } }, \"match\": \"^\\\\s*(\\\\[(?!-))(.*?)(\\\\])\", \"name\": \"entity.name.function.section.add.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.reg\" }, \"2\": { \"name\": \"entity.section.reg\" }, \"3\": { \"name\": \"punctuation.definition.section.reg\" } }, \"match\": \"^\\\\s*(\\\\[-)(.*?)(\\\\])\", \"name\": \"entity.name.function.section.delete.reg\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.definition.quote.reg\" }, \"3\": { \"name\": \"support.function.regname.ini\" }, \"4\": { \"name\": \"punctuation.definition.quote.reg\" }, \"5\": { \"name\": \"punctuation.definition.equals.reg\" }, \"7\": { \"name\": \"keyword.operator.arithmetic.minus.reg\" }, \"9\": { \"name\": \"punctuation.definition.quote.reg\" }, \"10\": { \"name\": \"string.name.regdata.reg\" }, \"11\": { \"name\": \"punctuation.definition.quote.reg\" }, \"13\": { \"name\": \"support.type.dword.reg\" }, \"14\": { \"name\": \"keyword.operator.arithmetic.colon.reg\" }, \"15\": { \"name\": \"constant.numeric.dword.reg\" }, \"17\": { \"name\": \"support.type.dword.reg\" }, \"18\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"19\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"20\": { \"name\": \"constant.numeric.hex.size.reg\" }, \"21\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"22\": { \"name\": \"keyword.operator.arithmetic.colon.reg\" }, \"23\": { \"name\": \"constant.numeric.hex.reg\" }, \"24\": { \"name\": \"keyword.operator.arithmetic.linecontinuation.reg\" }, \"25\": { \"name\": \"comment.declarationline.semicolon.reg\" } }, \"match\": `^(\\\\s*([\"']?)(.+?)([\"']?)\\\\s*(=))?\\\\s*((-)|(([\"'])(.*?)([\"']))|(((?i:dword))(:)\\\\s*([\\\\dabcdefABCDEF]{1,8}))|(((?i:hex))((\\\\()([\\\\d]*)(\\\\)))?(:)(.*?)(\\\\\\\\?)))\\\\s*(;.*)?$`, \"name\": \"meta.declaration.reg\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.reg\" }, { \"match\": \"[a-fA-F]+\", \"name\": \"constant.numeric.hex.reg\" }, { \"match\": \",+\", \"name\": \"constant.numeric.hex.comma.reg\" }, { \"match\": \"\\\\\\\\\", \"name\": \"keyword.operator.arithmetic.linecontinuation.reg\" }], \"scopeName\": \"source.reg\" });\nvar reg = [\n  lang\n];\n\nexport { reg as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAA2B,aAAa;QAAC;QAAO;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAmD,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;YAAU,QAAQ;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;YAA6B,QAAQ;QAAuC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;YAAyB,QAAQ;QAA0C;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,MAAM;oBAAE,QAAQ;gBAA0B;gBAAG,MAAM;oBAAE,QAAQ;gBAAmC;gBAAG,MAAM;oBAAE,QAAQ;gBAAyB;gBAAG,MAAM;oBAAE,QAAQ;gBAAwC;gBAAG,MAAM;oBAAE,QAAQ;gBAA6B;gBAAG,MAAM;oBAAE,QAAQ;gBAAyB;gBAAG,MAAM;oBAAE,QAAQ;gBAA8C;gBAAG,MAAM;oBAAE,QAAQ;gBAA8C;gBAAG,MAAM;oBAAE,QAAQ;gBAAgC;gBAAG,MAAM;oBAAE,QAAQ;gBAA8C;gBAAG,MAAM;oBAAE,QAAQ;gBAAwC;gBAAG,MAAM;oBAAE,QAAQ;gBAA2B;gBAAG,MAAM;oBAAE,QAAQ;gBAAmD;gBAAG,MAAM;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS,CAAC,yKAAyK,CAAC;YAAE,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAQ,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAa,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAM,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAQ,QAAQ;QAAmD;KAAE;IAAE,aAAa;AAAa;AACn7E,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}