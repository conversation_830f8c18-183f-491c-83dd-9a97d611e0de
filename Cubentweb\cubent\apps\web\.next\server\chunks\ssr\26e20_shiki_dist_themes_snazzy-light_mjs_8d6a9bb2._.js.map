{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/snazzy-light.mjs"], "sourcesContent": ["var snazzyLight = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#E7E8E6\",\n    \"activityBar.foreground\": \"#2DAE58\",\n    \"activityBar.inactiveForeground\": \"#68696888\",\n    \"activityBarBadge.background\": \"#09A1ED\",\n    \"badge.background\": \"#09A1ED\",\n    \"badge.foreground\": \"#ffffff\",\n    \"button.background\": \"#2DAE58\",\n    \"debugExceptionWidget.background\": \"#FFAEAC33\",\n    \"debugExceptionWidget.border\": \"#FF5C57\",\n    \"debugToolBar.border\": \"#E9EAEB\",\n    \"diffEditor.insertedTextBackground\": \"#2DAE5824\",\n    \"diffEditor.removedTextBackground\": \"#FFAEAC44\",\n    \"dropdown.border\": \"#E9EAEB\",\n    \"editor.background\": \"#FAFBFC\",\n    \"editor.findMatchBackground\": \"#00E6E06A\",\n    \"editor.findMatchHighlightBackground\": \"#00E6E02A\",\n    \"editor.findRangeHighlightBackground\": \"#F5B90011\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#2DAE5822\",\n    \"editor.foreground\": \"#565869\",\n    \"editor.hoverHighlightBackground\": \"#00E6E018\",\n    \"editor.rangeHighlightBackground\": \"#F5B90033\",\n    \"editor.selectionBackground\": \"#2DAE5822\",\n    \"editor.snippetTabstopHighlightBackground\": \"#ADB1C23A\",\n    \"editor.stackFrameHighlightBackground\": \"#F5B90033\",\n    \"editor.wordHighlightBackground\": \"#ADB1C23A\",\n    \"editorError.foreground\": \"#FF5C56\",\n    \"editorGroup.emptyBackground\": \"#F3F4F5\",\n    \"editorGutter.addedBackground\": \"#2DAE58\",\n    \"editorGutter.deletedBackground\": \"#FF5C57\",\n    \"editorGutter.modifiedBackground\": \"#00A39FAA\",\n    \"editorInlayHint.background\": \"#E9EAEB\",\n    \"editorInlayHint.foreground\": \"#565869\",\n    \"editorLineNumber.activeForeground\": \"#35CF68\",\n    \"editorLineNumber.foreground\": \"#9194A2aa\",\n    \"editorLink.activeForeground\": \"#35CF68\",\n    \"editorOverviewRuler.addedForeground\": \"#2DAE58\",\n    \"editorOverviewRuler.deletedForeground\": \"#FF5C57\",\n    \"editorOverviewRuler.errorForeground\": \"#FF5C56\",\n    \"editorOverviewRuler.findMatchForeground\": \"#13BBB7AA\",\n    \"editorOverviewRuler.modifiedForeground\": \"#00A39FAA\",\n    \"editorOverviewRuler.warningForeground\": \"#CF9C00\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#ADB1C288\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#35CF68\",\n    \"editorWarning.foreground\": \"#CF9C00\",\n    \"editorWhitespace.foreground\": \"#ADB1C255\",\n    \"extensionButton.prominentBackground\": \"#2DAE58\",\n    \"extensionButton.prominentHoverBackground\": \"#238744\",\n    \"focusBorder\": \"#09A1ED\",\n    \"foreground\": \"#686968\",\n    \"gitDecoration.modifiedResourceForeground\": \"#00A39F\",\n    \"gitDecoration.untrackedResourceForeground\": \"#2DAE58\",\n    \"input.border\": \"#E9EAEB\",\n    \"list.activeSelectionBackground\": \"#09A1ED\",\n    \"list.activeSelectionForeground\": \"#ffffff\",\n    \"list.errorForeground\": \"#FF5C56\",\n    \"list.focusBackground\": \"#BCE7FC99\",\n    \"list.focusForeground\": \"#11658F\",\n    \"list.hoverBackground\": \"#E9EAEB\",\n    \"list.inactiveSelectionBackground\": \"#89B5CB33\",\n    \"list.warningForeground\": \"#B38700\",\n    \"menu.background\": \"#FAFBFC\",\n    \"menu.selectionBackground\": \"#E9EAEB\",\n    \"menu.selectionForeground\": \"#686968\",\n    \"menubar.selectionBackground\": \"#E9EAEB\",\n    \"menubar.selectionForeground\": \"#686968\",\n    \"merge.currentContentBackground\": \"#35CF6833\",\n    \"merge.currentHeaderBackground\": \"#35CF6866\",\n    \"merge.incomingContentBackground\": \"#14B1FF33\",\n    \"merge.incomingHeaderBackground\": \"#14B1FF77\",\n    \"peekView.border\": \"#09A1ED\",\n    \"peekViewEditor.background\": \"#14B1FF08\",\n    \"peekViewEditor.matchHighlightBackground\": \"#F5B90088\",\n    \"peekViewEditor.matchHighlightBorder\": \"#F5B900\",\n    \"peekViewEditorStickyScroll.background\": \"#EDF4FB\",\n    \"peekViewResult.matchHighlightBackground\": \"#F5B90088\",\n    \"peekViewResult.selectionBackground\": \"#09A1ED\",\n    \"peekViewResult.selectionForeground\": \"#FFFFFF\",\n    \"peekViewTitle.background\": \"#09A1ED11\",\n    \"selection.background\": \"#2DAE5844\",\n    \"settings.modifiedItemIndicator\": \"#13BBB7\",\n    \"sideBar.background\": \"#F3F4F5\",\n    \"sideBar.border\": \"#DEDFE0\",\n    \"sideBarSectionHeader.background\": \"#E9EAEB\",\n    \"sideBarSectionHeader.border\": \"#DEDFE0\",\n    \"statusBar.background\": \"#2DAE58\",\n    \"statusBar.debuggingBackground\": \"#13BBB7\",\n    \"statusBar.debuggingBorder\": \"#00A39F\",\n    \"statusBar.noFolderBackground\": \"#565869\",\n    \"statusBarItem.remoteBackground\": \"#238744\",\n    \"tab.activeBorderTop\": \"#2DAE58\",\n    \"terminal.ansiBlack\": \"#565869\",\n    \"terminal.ansiBlue\": \"#09A1ED\",\n    \"terminal.ansiBrightBlack\": \"#75798F\",\n    \"terminal.ansiBrightBlue\": \"#14B1FF\",\n    \"terminal.ansiBrightCyan\": \"#13BBB7\",\n    \"terminal.ansiBrightGreen\": \"#35CF68\",\n    \"terminal.ansiBrightMagenta\": \"#FF94D2\",\n    \"terminal.ansiBrightRed\": \"#FFAEAC\",\n    \"terminal.ansiBrightWhite\": \"#FFFFFF\",\n    \"terminal.ansiBrightYellow\": \"#F5B900\",\n    \"terminal.ansiCyan\": \"#13BBB7\",\n    \"terminal.ansiGreen\": \"#2DAE58\",\n    \"terminal.ansiMagenta\": \"#F767BB\",\n    \"terminal.ansiRed\": \"#FF5C57\",\n    \"terminal.ansiWhite\": \"#FAFBF9\",\n    \"terminal.ansiYellow\": \"#CF9C00\",\n    \"titleBar.activeBackground\": \"#F3F4F5\"\n  },\n  \"displayName\": \"Snazzy Light\",\n  \"name\": \"snazzy-light\",\n  \"tokenColors\": [\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.object-literal.key\",\n        \"meta.object-literal.key constant.character.escape\",\n        \"meta.object-literal string\",\n        \"meta.object-literal string constant.character.escape\",\n        \"support.type.property-name\",\n        \"support.type.property-name constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"storage\",\n        \"meta.class storage.type\",\n        \"keyword.operator.expression.import\",\n        \"keyword.operator.new\",\n        \"keyword.operator.expression.delete\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type\",\n        \"meta.type.annotation entity.name.type\",\n        \"new.expr meta.type.parameters entity.name.type\",\n        \"storage.type.primitive\",\n        \"storage.type.built-in.primitive\",\n        \"meta.function.parameter storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#FF5C57CC\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"support.constant\",\n        \"variable.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"support.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.this\",\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"support.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.decorator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.class entity.name.type\",\n        \"new.expr entity.name.type\",\n        \"entity.other.inherited-class\",\n        \"support.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.preprocessor.pragma\",\n        \"keyword.control.directive.include\",\n        \"keyword.other.preprocessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.exception\",\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section\",\n      \"settings\": {}\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5C57\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#F5B900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"string.regexp constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.quantifier.regexp\",\n        \"keyword.operator.negation.regexp\",\n        \"keyword.operator.or.regexp\",\n        \"string.regexp punctuation\",\n        \"string.regexp keyword\",\n        \"string.regexp keyword.control\",\n        \"string.regexp constant\",\n        \"variable.other.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00A39F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp keyword.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00A39F88\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.preprocessor\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#9194A2\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.documentation entity.name.type\",\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation storage\",\n        \"comment.block.documentation keyword.other\",\n        \"meta.class comment.block.documentation storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9194A2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"keyword.other.arrow\",\n        \"keyword.control.@\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag.metadata.doctype.html entity.name.tag\",\n        \"meta.tag.metadata.doctype.html entity.other.attribute-name.html\",\n        \"meta.tag.sgml.doctype\",\n        \"meta.tag.sgml.doctype string\",\n        \"meta.tag.sgml.doctype entity.name.tag\",\n        \"meta.tag.sgml punctuation.definition.tag.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9194A2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag\",\n        \"punctuation.definition.tag.html\",\n        \"punctuation.definition.tag.begin.html\",\n        \"punctuation.definition.tag.end.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag entity.other.attribute-name\",\n        \"entity.other.attribute-name.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF8380\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.entity\",\n        \"punctuation.definition.entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selector\",\n        \"meta.selector entity\",\n        \"meta.selector entity punctuation\",\n        \"source.css entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.at-rule\",\n        \"keyword.control.at-rule punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": \"source.css variable\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-name\",\n        \"source.css support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.type.vendored.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869AA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property-value\",\n        \"support.constant.property-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.entity.css\",\n        \"keyword.operator.combinator.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF82CBBB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.important\",\n      \"settings\": {\n        \"foreground\": \"#238744\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss entity.other.attribute-name.class.css\",\n        \"source.css.scss entity.other.attribute-name.id.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag.reference.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss meta.at-rule keyword\",\n        \"source.css.scss meta.at-rule keyword punctuation\",\n        \"source.css.scss meta.at-rule operator.logical\",\n        \"keyword.control.content.scss\",\n        \"keyword.control.return.scss\",\n        \"keyword.control.return.scss punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.at-rule.mixin.scss\",\n        \"meta.at-rule.include.scss\",\n        \"source.css.scss meta.at-rule.if\",\n        \"source.css.scss meta.at-rule.else\",\n        \"source.css.scss meta.at-rule.each\",\n        \"source.css.scss meta.at-rule variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.less entity.other.attribute-name.class.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": \"source.stylus meta.brace.curly.css\",\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.stylus entity.other.attribute-name.class\",\n        \"source.stylus entity.other.attribute-name.id\",\n        \"source.stylus entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.stylus support.type.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.stylus variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"foreground\": \"#888888\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#888888\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.error\",\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#888888\"\n      }\n    },\n    {\n      \"scope\": \"meta.link\",\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown\",\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.output\",\n        \"markup.raw\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#999999\"\n      }\n    },\n    {\n      \"scope\": \"markup.prompt\",\n      \"settings\": {\n        \"foreground\": \"#999999\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.traceback\",\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline\",\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#777985\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.brace.round\",\n        \"meta.brace.square\",\n        \"storage.type.function.arrow\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.import-export-all\",\n        \"meta.import keyword.control.default\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.js\",\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.super\",\n        \"support.type.object.module.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": \"meta.jsx.children\",\n      \"settings\": {\n        \"foreground\": \"#686968\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.yaml\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.alias.yaml\",\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded.begin.php\",\n        \"punctuation.section.embedded.end.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#75798F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.use.php entity.other.alias.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.php support.function.construct\",\n        \"source.php support.function.var\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.extends.php\",\n        \"source.php keyword.other\",\n        \"storage.modifier.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.class.body.php storage.type.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.php\",\n        \"meta.class.body.php meta.function-call.php storage.type.php\",\n        \"meta.class.body.php meta.function.php storage.type.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.php keyword.other.DML\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D94E4A\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.sql.embedded.php keyword.operator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ini keyword\",\n        \"source.toml keyword\",\n        \"source.env variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ini entity.name.section\",\n        \"source.toml entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.import.go\",\n        \"keyword.package.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.reason variable.language string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.reason support.type\",\n        \"source.reason constant.language\",\n        \"source.reason constant.language constant.numeric\",\n        \"source.reason support.type string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.reason keyword.operator keyword.control\",\n        \"source.reason keyword.control.less\",\n        \"source.reason keyword.control.flow\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.reason string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.reason support.property-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.rust support.function.core.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.rust storage.type.core.rust\",\n        \"source.rust storage.class.std\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.rust entity.name.type.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.function.coffee\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.type.cs\",\n        \"storage.type.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.namespace.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF5C56\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.range\",\n        \"meta.diff.index\",\n        \"meta.separator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": \"source.makefile variable\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.protocol-specification.objc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.parens storage.type.objc\",\n        \"meta.return-type.objc support.class\",\n        \"meta.return-type.objc storage.type.objc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.sql keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.special-method.dockerfile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.elixir\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.elm\",\n        \"support.module.elm\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elm keyword.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.erlang entity.name.type.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.field.erlang\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.erlang constant.other.symbol\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.declaration.class.haskell storage.type.haskell\",\n        \"meta.declaration.instance.haskell storage.type.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.preprocessor.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#75798F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.haskell keyword.control\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"tag.end.latte\",\n        \"tag.begin.latte\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": \"source.po keyword.control\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"source.po storage.type\",\n      \"settings\": {\n        \"foreground\": \"#9194A2\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.po\",\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": \"meta.header.po string\",\n      \"settings\": {\n        \"foreground\": \"#FF8380\"\n      }\n    },\n    {\n      \"scope\": \"source.po meta.header.po\",\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml punctuation.definition.tag emphasis\",\n        \"source.ocaml entity.name.class constant.numeric\",\n        \"source.ocaml support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml constant.numeric entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml comment meta.separator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml support.type strong\",\n        \"source.ocaml keyword.control strong\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ocaml support.constant.property-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.scala entity.name.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.bracket.scala\",\n        \"meta.colon.scala\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.metadata.simple.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.metadata.simple.clojure meta.symbol\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.r keyword.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.svelte meta.block.ts entity.name.label\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.word.applescript\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F767BB\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.livescript\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#09A1ED\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.self.lua\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.class.swift\",\n        \"meta.inheritance-clause.swift\",\n        \"meta.import.swift entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.swift punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B38700\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.swift entity.name.function.swift\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.twig\",\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.tag-string.django\",\n      \"settings\": {\n        \"foreground\": \"#565869\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.tag.tagbraces.django\",\n        \"entity.tag.filter-pipe.django\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.section.attributes.haml constant.language\",\n        \"meta.section.attributes.plain.haml constant.other.symbol\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FF8380\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.prolog.haml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9194A2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.handlebars\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": \"text.log log.constant\",\n      \"settings\": {\n        \"foreground\": \"#C25193\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.c string constant.other.placeholder\",\n        \"source.cpp string constant.other.placeholder\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B38700\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.key.groovy\",\n      \"settings\": {\n        \"foreground\": \"#11658F\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.groovy\",\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.variable.groovy storage.type.groovy\",\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.groovy\",\n      \"settings\": {\n        \"foreground\": \"#CF9C00\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.class.pug\",\n        \"entity.other.attribute-name.id.pug\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.name.attribute.tag.pug\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ADB1C2\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.style.html\",\n      \"settings\": {\n        \"foreground\": \"#13BBB7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.wasm\",\n      \"settings\": {\n        \"foreground\": \"#2DAE58\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { snazzyLight as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc,OAAO,MAAM,CAAC;IAC9B,UAAU;QACR,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,mCAAmC;QACnC,+BAA+B;QAC/B,uBAAuB;QACvB,qCAAqC;QACrC,oCAAoC;QACpC,mBAAmB;QACnB,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,+CAA+C;QAC/C,qBAAqB;QACrB,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,4CAA4C;QAC5C,wCAAwC;QACxC,kCAAkC;QAClC,0BAA0B;QAC1B,+BAA+B;QAC/B,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,8BAA8B;QAC9B,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,uCAAuC;QACvC,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,0CAA0C;QAC1C,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,4BAA4B;QAC5B,+BAA+B;QAC/B,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,4CAA4C;QAC5C,6CAA6C;QAC7C,gBAAgB;QAChB,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,0BAA0B;QAC1B,mBAAmB;QACnB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,yCAAyC;QACzC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,wBAAwB;QACxB,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,mCAAmC;QACnC,+BAA+B;QAC/B,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,gCAAgC;QAChC,kCAAkC;QAClC,uBAAuB;QACvB,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;IAC/B;IACA,eAAe;IACf,QAAQ;IACR,eAAe;QACb;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY,CAAC;QACf;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}