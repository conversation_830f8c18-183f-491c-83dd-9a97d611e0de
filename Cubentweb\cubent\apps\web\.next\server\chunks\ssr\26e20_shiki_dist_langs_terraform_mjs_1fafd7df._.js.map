{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/terraform.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Terraform\", \"fileTypes\": [\"tf\", \"tfvars\"], \"name\": \"terraform\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#attribute_definition\" }, { \"include\": \"#block\" }, { \"include\": \"#expressions\" }], \"repository\": { \"attribute_access\": { \"begin\": \"\\\\.(?!\\\\*)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.accessor.hcl\" } }, \"comment\": \"Matches traversal attribute access such as .attr\", \"end\": \"[A-Za-z][\\\\w-]*|\\\\d*\", \"endCaptures\": { \"0\": { \"patterns\": [{ \"comment\": \"Attribute name\", \"match\": \"(?!null|false|true)[A-Za-z][\\\\w-]*\", \"name\": \"variable.other.member.hcl\" }, { \"comment\": \"Optional attribute index\", \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.hcl\" }] } } }, \"attribute_definition\": { \"captures\": { \"1\": { \"name\": \"punctuation.section.parens.begin.hcl\" }, \"2\": { \"name\": \"variable.other.readwrite.hcl\" }, \"3\": { \"name\": \"punctuation.section.parens.end.hcl\" }, \"4\": { \"name\": \"keyword.operator.assignment.hcl\" } }, \"comment\": 'Identifier \"=\" with optional parens', \"match\": \"(\\\\()?(\\\\b(?!null\\\\b|false\\\\b|true\\\\b)[A-Za-z][0-9A-Za-z_-]*)(\\\\))?\\\\s*(=(?!=|>))\\\\s*\", \"name\": \"variable.declaration.hcl\" }, \"attribute_splat\": { \"begin\": \"\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.accessor.hcl\" } }, \"comment\": \"Legacy attribute-only splat\", \"end\": \"\\\\*\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.splat.hcl\" } } }, \"block\": { \"begin\": '([\\\\w][\\\\-\\\\w]*)([\\\\s\\\\\"\\\\-\\\\w]*)(\\\\{)', \"beginCaptures\": { \"1\": { \"patterns\": [{ \"comment\": \"Known block type\", \"match\": \"\\\\bdata|check|import|locals|module|output|provider|resource|terraform|variable\\\\b\", \"name\": \"entity.name.type.terraform\" }, { \"comment\": \"Unknown block type\", \"match\": \"\\\\b(?!null|false|true)[A-Za-z][0-9A-Za-z_-]*\\\\b\", \"name\": \"entity.name.type.hcl\" }] }, \"2\": { \"patterns\": [{ \"comment\": \"Block label\", \"match\": '[\\\\\"\\\\-\\\\w]+', \"name\": \"variable.other.enummember.hcl\" }] }, \"3\": { \"name\": \"punctuation.section.block.begin.hcl\" } }, \"comment\": 'This will match Terraform blocks like `resource \"aws_instance\" \"web\" {` or `module {`', \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.block.end.hcl\" } }, \"name\": \"meta.block.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#attribute_definition\" }, { \"include\": \"#block\" }, { \"include\": \"#expressions\" }] }, \"block_inline_comments\": { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Inline comments start with the /* sequence and end with the */ sequence, and may have any characters within except the ending sequence. An inline comment is considered equivalent to a whitespace sequence\", \"end\": \"\\\\*/\", \"name\": \"comment.block.hcl\" }, \"brackets\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin.hcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.hcl\" } }, \"patterns\": [{ \"comment\": \"Splat operator\", \"match\": \"\\\\*\", \"name\": \"keyword.operator.splat.hcl\" }, { \"include\": \"#comma\" }, { \"include\": \"#comments\" }, { \"include\": \"#inline_for_expression\" }, { \"include\": \"#inline_if_expression\" }, { \"include\": \"#expressions\" }, { \"include\": \"#local_identifiers\" }] }, \"char_escapes\": { \"comment\": \"Character Escapes\", \"match\": '\\\\\\\\[nrt\"\\\\\\\\]|\\\\\\\\u(\\\\h{8}|\\\\h{4})', \"name\": \"constant.character.escape.hcl\" }, \"comma\": { \"comment\": \"Commas - used in certain expressions\", \"match\": \"\\\\,\", \"name\": \"punctuation.separator.hcl\" }, \"comments\": { \"patterns\": [{ \"include\": \"#hash_line_comments\" }, { \"include\": \"#double_slash_line_comments\" }, { \"include\": \"#block_inline_comments\" }] }, \"double_slash_line_comments\": { \"begin\": \"//\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Line comments start with // sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.double-slash.hcl\" }, \"expressions\": { \"patterns\": [{ \"include\": \"#literal_values\" }, { \"include\": \"#operators\" }, { \"include\": \"#tuple_for_expression\" }, { \"include\": \"#object_for_expression\" }, { \"include\": \"#brackets\" }, { \"include\": \"#objects\" }, { \"include\": \"#attribute_access\" }, { \"include\": \"#attribute_splat\" }, { \"include\": \"#functions\" }, { \"include\": \"#parens\" }] }, \"for_expression_body\": { \"patterns\": [{ \"comment\": \"in keyword\", \"match\": \"\\\\bin\\\\b\", \"name\": \"keyword.operator.word.hcl\" }, { \"comment\": \"if keyword\", \"match\": \"\\\\bif\\\\b\", \"name\": \"keyword.control.conditional.hcl\" }, { \"match\": \":\", \"name\": \"keyword.operator.hcl\" }, { \"include\": \"#expressions\" }, { \"include\": \"#comments\" }, { \"include\": \"#comma\" }, { \"include\": \"#local_identifiers\" }] }, \"functions\": { \"begin\": \"([:\\\\-\\\\w]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\b(core::)?(abs|abspath|alltrue|anytrue|base64decode|base64encode|base64gzip|base64sha256|base64sha512|basename|bcrypt|can|ceil|chomp|chunklist|cidrhost|cidrnetmask|cidrsubnet|cidrsubnets|coalesce|coalescelist|compact|concat|contains|csvdecode|dirname|distinct|element|endswith|file|filebase64|filebase64sha256|filebase64sha512|fileexists|filemd5|fileset|filesha1|filesha256|filesha512|flatten|floor|format|formatdate|formatlist|indent|index|join|jsondecode|jsonencode|keys|length|log|lookup|lower|matchkeys|max|md5|merge|min|nonsensitive|one|parseint|pathexpand|plantimestamp|pow|range|regex|regexall|replace|reverse|rsadecrypt|sensitive|setintersection|setproduct|setsubtract|setunion|sha1|sha256|sha512|signum|slice|sort|split|startswith|strcontains|strrev|substr|sum|templatefile|textdecodebase64|textencodebase64|timeadd|timecmp|timestamp|title|tobool|tolist|tomap|tonumber|toset|tostring|transpose|trim|trimprefix|trimspace|trimsuffix|try|upper|urlencode|uuid|uuidv5|values|yamldecode|yamlencode|zipmap)\\\\b\", \"name\": \"support.function.builtin.terraform\" }, { \"match\": \"\\\\bprovider::[A-Za-z][\\\\w_-]*::[A-Za-z][\\\\w_-]*\\\\b\", \"name\": \"support.function.provider.terraform\" }] }, \"2\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Built-in function calls\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.hcl\" } }, \"name\": \"meta.function-call.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expressions\" }, { \"include\": \"#comma\" }] }, \"hash_line_comments\": { \"begin\": \"#\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Line comments start with # sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.number-sign.hcl\" }, \"hcl_type_keywords\": { \"comment\": \"Type keywords known to HCL.\", \"match\": \"\\\\b(any|string|number|bool|list|set|map|tuple|object)\\\\b\", \"name\": \"storage.type.hcl\" }, \"heredoc\": { \"begin\": \"(<<-?)\\\\s*(\\\\w+)\\\\s*$\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.hcl\" }, \"2\": { \"name\": \"keyword.control.heredoc.hcl\" } }, \"comment\": \"String Heredoc\", \"end\": \"^\\\\s*\\\\2\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.heredoc.hcl\" } }, \"name\": \"string.unquoted.heredoc.hcl\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }, \"inline_for_expression\": { \"captures\": { \"1\": { \"name\": \"keyword.control.hcl\" }, \"2\": { \"patterns\": [{ \"match\": \"=>\", \"name\": \"storage.type.function.hcl\" }, { \"include\": \"#for_expression_body\" }] } }, \"match\": \"(for)\\\\b(.*)\\\\n\" }, \"inline_if_expression\": { \"begin\": \"(if)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.hcl\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"include\": \"#expressions\" }, { \"include\": \"#comments\" }, { \"include\": \"#comma\" }, { \"include\": \"#local_identifiers\" }] }, \"language_constants\": { \"comment\": \"Language Constants\", \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.hcl\" }, \"literal_values\": { \"patterns\": [{ \"include\": \"#numeric_literals\" }, { \"include\": \"#language_constants\" }, { \"include\": \"#string_literals\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#hcl_type_keywords\" }, { \"include\": \"#named_value_references\" }] }, \"local_identifiers\": { \"comment\": \"Local Identifiers\", \"match\": \"\\\\b(?!null|false|true)[A-Za-z][0-9A-Za-z_-]*\\\\b\", \"name\": \"variable.other.readwrite.hcl\" }, \"named_value_references\": { \"comment\": \"Constant values available only to Terraform.\", \"match\": \"\\\\b(var|local|module|data|path|terraform)\\\\b\", \"name\": \"variable.other.readwrite.terraform\" }, \"numeric_literals\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.exponent.hcl\" } }, \"comment\": \"Integer, no fraction, optional exponent\", \"match\": \"\\\\b\\\\d+([Ee][+-]?)\\\\d+\\\\b\", \"name\": \"constant.numeric.float.hcl\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.decimal.hcl\" }, \"2\": { \"name\": \"punctuation.separator.exponent.hcl\" } }, \"comment\": \"Integer, fraction, optional exponent\", \"match\": \"\\\\b\\\\d+(\\\\.)\\\\d+(?:([Ee][+-]?)\\\\d+)?\\\\b\", \"name\": \"constant.numeric.float.hcl\" }, { \"comment\": \"Integers\", \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.integer.hcl\" }] }, \"object_for_expression\": { \"begin\": \"(\\\\{)\\\\s?(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.braces.begin.hcl\" }, \"2\": { \"name\": \"keyword.control.hcl\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.hcl\" } }, \"patterns\": [{ \"match\": \"=>\", \"name\": \"storage.type.function.hcl\" }, { \"include\": \"#for_expression_body\" }] }, \"object_key_values\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#literal_values\" }, { \"include\": \"#operators\" }, { \"include\": \"#tuple_for_expression\" }, { \"include\": \"#object_for_expression\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#functions\" }] }, \"objects\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.begin.hcl\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.hcl\" } }, \"name\": \"meta.braces.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#objects\" }, { \"include\": \"#inline_for_expression\" }, { \"include\": \"#inline_if_expression\" }, { \"captures\": { \"1\": { \"name\": \"meta.mapping.key.hcl variable.other.readwrite.hcl\" }, \"2\": { \"name\": \"keyword.operator.assignment.hcl\", \"patterns\": [{ \"match\": \"=>\", \"name\": \"storage.type.function.hcl\" }] } }, \"comment\": \"Literal, named object key\", \"match\": \"\\\\b((?!null|false|true)[A-Za-z][0-9A-Za-z_-]*)\\\\s*(=>?)\\\\s*\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#named_value_references\" }] }, \"1\": { \"name\": \"meta.mapping.key.hcl string.quoted.double.hcl\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.hcl\" }, \"3\": { \"name\": \"punctuation.definition.string.end.hcl\" }, \"4\": { \"name\": \"keyword.operator.hcl\" } }, \"comment\": \"String object key\", \"match\": '\\\\b((\").*(\"))\\\\s*(=)\\\\s*' }, { \"begin\": \"^\\\\s*\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Computed object key (any expression between parens)\", \"end\": \"(\\\\))\\\\s*(=|:)\\\\s*\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.parens.end.hcl\" }, \"2\": { \"name\": \"keyword.operator.hcl\" } }, \"name\": \"meta.mapping.key.hcl\", \"patterns\": [{ \"include\": \"#named_value_references\" }, { \"include\": \"#attribute_access\" }] }, { \"include\": \"#object_key_values\" }] }, \"operators\": { \"patterns\": [{ \"match\": \">=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"<=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"==\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"!=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\+\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"-\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\*\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\/\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\%\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\&\\\\&\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \"\\\\|\\\\|\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \"!\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \">\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"<\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \":\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"=>\", \"name\": \"keyword.operator.hcl\" }] }, \"parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Parens - matched *after* function syntax\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.hcl\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expressions\" }] }, \"string_interpolation\": { \"begin\": \"(?<![%$])([%$]{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.interpolation.begin.hcl\" } }, \"comment\": \"String interpolation\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.interpolation.end.hcl\" } }, \"name\": \"meta.interpolation.hcl\", \"patterns\": [{ \"comment\": \"Trim left whitespace\", \"match\": \"\\\\~\\\\s\", \"name\": \"keyword.operator.template.left.trim.hcl\" }, { \"comment\": \"Trim right whitespace\", \"match\": \"\\\\s\\\\~\", \"name\": \"keyword.operator.template.right.trim.hcl\" }, { \"comment\": \"if/else/endif and for/in/endfor directives\", \"match\": \"\\\\b(if|else|endif|for|in|endfor)\\\\b\", \"name\": \"keyword.control.hcl\" }, { \"include\": \"#expressions\" }, { \"include\": \"#local_identifiers\" }] }, \"string_literals\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hcl\" } }, \"comment\": \"Strings\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.hcl\" } }, \"name\": \"string.quoted.double.hcl\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"include\": \"#char_escapes\" }] }, \"tuple_for_expression\": { \"begin\": \"(\\\\[)\\\\s?(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.brackets.begin.hcl\" }, \"2\": { \"name\": \"keyword.control.hcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.hcl\" } }, \"patterns\": [{ \"include\": \"#for_expression_body\" }] } }, \"scopeName\": \"source.hcl.terraform\", \"aliases\": [\"tf\", \"tfvars\"] });\nvar terraform = [\n  lang\n];\n\nexport { terraform as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAa,aAAa;QAAC;QAAM;KAAS;IAAE,QAAQ;IAAa,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAe;KAAE;IAAE,cAAc;QAAE,oBAAoB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,WAAW;YAAoD,OAAO;YAAwB,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;4BAAkB,SAAS;4BAAsC,QAAQ;wBAA4B;wBAAG;4BAAE,WAAW;4BAA4B,SAAS;4BAAQ,QAAQ;wBAA+B;qBAAE;gBAAC;YAAE;QAAE;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,WAAW;YAAuC,SAAS;YAAyF,QAAQ;QAA2B;QAAG,mBAAmB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,WAAW;YAA+B,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;QAAE;QAAG,SAAS;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;4BAAoB,SAAS;4BAAqF,QAAQ;wBAA6B;wBAAG;4BAAE,WAAW;4BAAsB,SAAS;4BAAmD,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;4BAAe,SAAS;4BAAgB,QAAQ;wBAAgC;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,WAAW;YAAyF,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,WAAW;YAA+M,OAAO;YAAQ,QAAQ;QAAoB;QAAG,YAAY;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;oBAAkB,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gBAAgB;YAAE,WAAW;YAAqB,SAAS;YAAuC,QAAQ;QAAgC;QAAG,SAAS;YAAE,WAAW;YAAwC,SAAS;YAAO,QAAQ;QAA4B;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAM,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,WAAW;YAA8I,OAAO;YAAS,QAAQ;QAAgC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAc,SAAS;oBAAY,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAAY,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAy/B,QAAQ;wBAAqC;wBAAG;4BAAE,SAAS;4BAAsD,QAAQ;wBAAsC;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,WAAW;YAA2B,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAK,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,WAAW;YAA6I,OAAO;YAAS,QAAQ;QAA+B;QAAG,qBAAqB;YAAE,WAAW;YAA+B,SAAS;YAA4D,QAAQ;QAAmB;QAAG,WAAW;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,WAAW;YAAkB,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAM,QAAQ;wBAA4B;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAkB;QAAG,wBAAwB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,WAAW;YAAsB,SAAS;YAA2B,QAAQ;QAAwB;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,WAAW;YAAqB,SAAS;YAAmD,QAAQ;QAA+B;QAAG,0BAA0B;YAAE,WAAW;YAAgD,SAAS;YAAgD,QAAQ;QAAqC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,WAAW;oBAA2C,SAAS;oBAA6B,QAAQ;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,WAAW;oBAAwC,SAAS;oBAA2C,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;oBAAY,SAAS;oBAAc,QAAQ;gBAA+B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,SAAS;oCAAM,QAAQ;gCAA4B;6BAAE;wBAAC;oBAAE;oBAAG,WAAW;oBAA6B,SAAS;gBAA8D;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAA0B;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,WAAW;oBAAqB,SAAS;gBAA2B;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,WAAW;oBAAuD,OAAO;oBAAsB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAuB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,WAAW;YAA4C,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,WAAW;YAAwB,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;oBAAwB,SAAS;oBAAU,QAAQ;gBAA0C;gBAAG;oBAAE,WAAW;oBAAyB,SAAS;oBAAU,QAAQ;gBAA2C;gBAAG;oBAAE,WAAW;oBAA8C,SAAS;oBAAuC,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,WAAW;YAAW,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAwB,WAAW;QAAC;QAAM;KAAS;AAAC;AACvtb,IAAI,YAAY;IACd;CACD", "ignoreList": [0], "debugId": null}}]}