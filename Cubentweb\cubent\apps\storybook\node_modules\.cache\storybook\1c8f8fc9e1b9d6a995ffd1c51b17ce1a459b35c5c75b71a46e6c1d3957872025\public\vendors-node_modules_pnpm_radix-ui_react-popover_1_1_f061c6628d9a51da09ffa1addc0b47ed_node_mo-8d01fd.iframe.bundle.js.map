{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-popover_1_1_f061c6628d9a51da09ffa1addc0b47ed_node_mo-8d01fd.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-popover@1.1_f061c6628d9a51da09ffa1addc0b47ed/node_modules/@radix-ui/react-popover/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/popover.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { jsx } from \"react/jsx-runtime\";\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = createContextScope(POPOVER_NAME, [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props) => {\n  const {\n    __scopePopover,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = false\n  } = props;\n  const popperScope = usePopperScope(__scopePopover);\n  const triggerRef = React.useRef(null);\n  const [hasCustomAnchor, setHasCustomAnchor] = React.useState(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: POPOVER_NAME\n  });\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    PopoverProvider,\n    {\n      scope: __scopePopover,\n      contentId: useId(),\n      triggerRef,\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      hasCustomAnchor,\n      onCustomAnchorAdd: React.useCallback(() => setHasCustomAnchor(true), []),\n      onCustomAnchorRemove: React.useCallback(() => setHasCustomAnchor(false), []),\n      modal,\n      children\n    }\n  ) });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    React.useEffect(() => {\n      onCustomAnchorAdd();\n      return () => onCustomAnchorRemove();\n    }, [onCustomAnchorAdd, onCustomAnchorRemove]);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { ...popperScope, ...anchorProps, ref: forwardedRef });\n  }\n);\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: trigger });\n  }\n);\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar PopoverPortal = (props) => {\n  const { __scopePopover, forceMount, children, container } = props;\n  const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopePopover, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.modal ? /* @__PURE__ */ jsx(PopoverContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(PopoverContentNonModal, { ...contentProps, ref: forwardedRef }) });\n  }\n);\nPopoverContent.displayName = CONTENT_NAME;\nvar Slot = createSlot(\"PopoverContent.RemoveScroll\");\nvar PopoverContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const isRightClickOutsideRef = React.useRef(false);\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ jsx(\n      PopoverContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: composeEventHandlers(\n          props.onPointerDownOutside,\n          (event) => {\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            isRightClickOutsideRef.current = isRightClick;\n          },\n          { checkForDefaultPrevented: false }\n        ),\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )\n      }\n    ) });\n  }\n);\nvar PopoverContentNonModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      PopoverContentImpl,\n      {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === \"pointerdown\") {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n          const target = event.target;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n          if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }\n      }\n    );\n  }\n);\nvar PopoverContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopover,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      ...contentProps\n    } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    useFocusGuards();\n    return /* @__PURE__ */ jsx(\n      FocusScope,\n      {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ jsx(\n          DismissableLayer,\n          {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: () => context.onOpenChange(false),\n            children: /* @__PURE__ */ jsx(\n              PopperPrimitive.Content,\n              {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                  ...contentProps.style,\n                  // re-namespace exposed content custom properties\n                  ...{\n                    \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                  }\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, () => context.onOpenChange(false))\n      }\n    );\n  }\n);\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\nexport {\n  Anchor2 as Anchor,\n  Arrow2 as Arrow,\n  Close,\n  Content2 as Content,\n  Popover,\n  PopoverAnchor,\n  PopoverArrow,\n  PopoverClose,\n  PopoverContent,\n  PopoverPortal,\n  PopoverTrigger,\n  Portal,\n  Root2 as Root,\n  Trigger,\n  createPopoverScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}