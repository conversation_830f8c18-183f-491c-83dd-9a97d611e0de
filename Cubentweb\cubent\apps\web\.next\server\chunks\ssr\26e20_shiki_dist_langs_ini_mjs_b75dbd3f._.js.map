{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/ini.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"INI\", \"name\": \"ini\", \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.ini\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.ini\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.ini\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=;)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.ini\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \";\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.ini\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.semicolon.ini\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.other.definition.ini\" }, \"2\": { \"name\": \"punctuation.separator.key-value.ini\" } }, \"match\": \"\\\\b([a-zA-Z0-9_.-]+)\\\\b\\\\s*(=)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.ini\" }, \"3\": { \"name\": \"punctuation.definition.entity.ini\" } }, \"match\": \"^(\\\\[)(.*?)(\\\\])\", \"name\": \"entity.name.section.group-title.ini\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ini\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ini\" } }, \"name\": \"string.quoted.single.ini\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.ini\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ini\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ini\" } }, \"name\": \"string.quoted.double.ini\" }], \"scopeName\": \"source.ini\", \"aliases\": [\"properties\"] });\nvar ini = [\n  lang\n];\n\nexport { ini as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAA+B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAoB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAA6B;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;QAAiC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAoB,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA2B;KAAE;IAAE,aAAa;IAAc,WAAW;QAAC;KAAa;AAAC;AACtlD,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}