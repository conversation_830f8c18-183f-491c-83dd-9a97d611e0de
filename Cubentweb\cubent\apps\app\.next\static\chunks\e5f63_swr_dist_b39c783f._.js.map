{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/_internal/events.mjs"], "sourcesContent": ["const FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nexport { ERROR_REVALIDATE_EVENT, FOCUS_EVENT, MUTATE_EVENT, RECONNECT_EVENT };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,cAAc;AACpB,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs"], "sourcesContent": ["'use client';\nimport React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement } from 'react';\nimport * as revalidateEvents from './events.mjs';\nimport { dequal } from 'dequal/lite';\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](revalidateEvents.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nexport { noop as A, isPromiseLike as B, IS_REACT_LEGACY as I, OBJECT as O, SWRConfigContext as S, UNDEFINED as U, isFunction as a, SWRGlobalState as b, cache as c, defaultConfig as d, isUndefined as e, mergeConfigs as f, SWRConfig as g, initCache as h, isWindowDefined as i, mutate as j, compare as k, stableHash as l, mergeObjects as m, internalMutate as n, getTimestamp as o, preset as p, defaultConfigOptions as q, IS_SERVER as r, serialize as s, rAF as t, useIsomorphicLayoutEffect as u, slowConnection as v, isDocumentDefined as w, isLegacyDeno as x, hasRequestAnimationFrame as y, createCacheHelper as z };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AAHA;;;;AAKA,gEAAgE;AAChE,MAAM,iBAAiB,IAAI;AAE3B,+DAA+D;AAC/D,MAAM,OAAO,KAAK;AAClB,mEAAmE;AACnE,8EAA8E;AAC9E,8DAA8D;AAC9D,kBAAkB;AAClB,MAAM,YAAY,eAAe,GAAG;AACpC,MAAM,SAAS;AACf,MAAM,cAAc,CAAC,IAAI,MAAM;AAC/B,MAAM,aAAa,CAAC,IAAI,OAAO,KAAK;AACpC,MAAM,eAAe,CAAC,GAAG,IAAI,CAAC;QACtB,GAAG,CAAC;QACJ,GAAG,CAAC;IACR,CAAC;AACL,MAAM,gBAAgB,CAAC,IAAI,WAAW,EAAE,IAAI;AAE5C,MAAM,cAAc,CAAC;AACrB,MAAM,gBAAgB,CAAC;AACvB,MAAM,gBAAgB;AACtB,kGAAkG;AAClG,MAAM,kBAAkB,OAAO,UAAU;AACzC,MAAM,oBAAoB,OAAO,YAAY;AAC7C,MAAM,eAAe,mBAAmB,UAAU;AAClD,MAAM,2BAA2B,IAAI,mBAAmB,OAAO,MAAM,CAAC,wBAAwB,IAAI;AAClG,MAAM,oBAAoB,CAAC,OAAO;IAC9B,MAAM,QAAQ,eAAe,GAAG,CAAC;IACjC,OAAO;QACH,SAAS;QACT,IAAI,CAAC,YAAY,QAAQ,MAAM,GAAG,CAAC,QAAQ;QAC3C,SAAS;QACT,CAAC;YACG,IAAI,CAAC,YAAY,MAAM;gBACnB,MAAM,OAAO,MAAM,GAAG,CAAC;gBACvB,sEAAsE;gBACtE,yBAAyB;gBACzB,IAAI,CAAC,CAAC,OAAO,aAAa,GAAG;oBACzB,aAAa,CAAC,IAAI,GAAG;gBACzB;gBACA,KAAK,CAAC,EAAE,CAAC,KAAK,aAAa,MAAM,OAAO,QAAQ;YACpD;QACJ;QACA,aAAa;QACb,KAAK,CAAC,EAAE;QACR,4BAA4B;QAC5B;YACI,IAAI,CAAC,YAAY,MAAM;gBACnB,8EAA8E;gBAC9E,IAAI,OAAO,eAAe,OAAO,aAAa,CAAC,IAAI;YACvD;YACA,2EAA2E;YAC3E,OAAO,CAAC,YAAY,QAAQ,MAAM,GAAG,CAAC,QAAQ;QAClD;KACH;AACL,EAAE,qFAAqF;;AAGvF;;;;;;CAMC,GAAG,IAAI,SAAS;AACjB,MAAM,WAAW,IAAI;AACrB,gFAAgF;AAChF,MAAM,CAAC,eAAe,eAAe,GAAG,mBAAmB,OAAO,gBAAgB,GAAG;IACjF,OAAO,gBAAgB,CAAC,IAAI,CAAC;IAC7B,OAAO,mBAAmB,CAAC,IAAI,CAAC;CACnC,GAAG;IACA;IACA;CACH;AACD,MAAM,YAAY;IACd,MAAM,kBAAkB,qBAAqB,SAAS,eAAe;IACrE,OAAO,YAAY,oBAAoB,oBAAoB;AAC/D;AACA,MAAM,YAAY,CAAC;IACf,mBAAmB;IACnB,IAAI,mBAAmB;QACnB,SAAS,gBAAgB,CAAC,oBAAoB;IAClD;IACA,cAAc,SAAS;IACvB,OAAO;QACH,IAAI,mBAAmB;YACnB,SAAS,mBAAmB,CAAC,oBAAoB;QACrD;QACA,eAAe,SAAS;IAC5B;AACJ;AACA,MAAM,gBAAgB,CAAC;IACnB,4BAA4B;IAC5B,MAAM,WAAW;QACb,SAAS;QACT;IACJ;IACA,gDAAgD;IAChD,MAAM,YAAY;QACd,SAAS;IACb;IACA,cAAc,UAAU;IACxB,cAAc,WAAW;IACzB,OAAO;QACH,eAAe,UAAU;QACzB,eAAe,WAAW;IAC9B;AACJ;AACA,MAAM,SAAS;IACX;IACA;AACJ;AACA,MAAM,uBAAuB;IACzB;IACA;AACJ;AAEA,MAAM,kBAAkB,CAAC,4QAAA,CAAA,UAAK,CAAC,KAAK;AACpC,MAAM,YAAY,CAAC,mBAAmB;AACtC,iCAAiC;AACjC,MAAM,MAAM,CAAC,IAAI,6BAA6B,MAAM,CAAC,wBAAwB,CAAC,KAAK,WAAW,GAAG;AACjG,6EAA6E;AAC7E,6EAA6E;AAC7E,kCAAkC;AAClC,MAAM,4BAA4B,YAAY,4QAAA,CAAA,YAAS,GAAG,4QAAA,CAAA,kBAAe;AACzE,wEAAwE;AACxE,MAAM,sBAAsB,OAAO,cAAc,eAAe,UAAU,UAAU;AACpF,iEAAiE;AACjE,MAAM,iBAAiB,CAAC,aAAa,uBAAuB,CAAC;IACzD;IACA;CACH,CAAC,QAAQ,CAAC,oBAAoB,aAAa,KAAK,oBAAoB,QAAQ;AAE7E,+CAA+C;AAC/C,2CAA2C;AAC3C,yDAAyD;AACzD,6BAA6B;AAC7B,MAAM,QAAQ,IAAI;AAClB,MAAM,eAAe,CAAC,OAAO,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChG,qBAAqB;AACrB,IAAI,UAAU;AACd,8CAA8C;AAC9C,4CAA4C;AAC5C,kCAAkC;AAClC,gCAAgC;AAChC,4BAA4B;AAC5B,EAAE;AACF,+EAA+E;AAC/E,YAAY;AACZ,MAAM,aAAa,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,MAAM,SAAS,aAAa,KAAK;IACjC,MAAM,UAAU,aAAa,KAAK;IAClC,MAAM,gBAAgB,aAAa,KAAK;IACxC,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,SAAS;QAC5C,4EAA4E;QAC5E,sDAAsD;QACtD,SAAS,MAAM,GAAG,CAAC;QACnB,IAAI,QAAQ,OAAO;QACnB,4EAA4E;QAC5E,gCAAgC;QAChC,2EAA2E;QAC3E,SAAS,EAAE,UAAU;QACrB,MAAM,GAAG,CAAC,KAAK;QACf,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,SAAS;YACT,SAAS;YACT,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,QAAQ;gBACvC,UAAU,WAAW,GAAG,CAAC,MAAM,IAAI;YACvC;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;QACA,IAAI,eAAe;YACf,qBAAqB;YACrB,SAAS;YACT,MAAM,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;YAClC,MAAM,CAAC,YAAY,QAAQ,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG;oBAC1B,UAAU,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI;gBACrD;YACJ;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;IACJ,OAAO;QACH,SAAS,SAAS,IAAI,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,KAAK;IACvH;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,IAAI,WAAW,MAAM;QACjB,IAAI;YACA,MAAM;QACV,EAAE,OAAO,KAAK;YACV,yBAAyB;YACzB,MAAM;QACV;IACJ;IACA,8EAA8E;IAC9E,mBAAmB;IACnB,MAAM,OAAO;IACb,uDAAuD;IACvD,MAAM,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,OAAO;IACjG,OAAO;QACH;QACA;KACH;AACL;AAEA,oBAAoB;AACpB,IAAI,cAAc;AAClB,MAAM,eAAe,IAAI,EAAE;AAE3B,eAAe,eAAe,GAAG,IAAI;IACjC,MAAM,CAAC,OAAO,MAAM,OAAO,MAAM,GAAG;IACpC,oEAAoE;IACpE,gBAAgB;IAChB,MAAM,UAAU,aAAa;QACzB,eAAe;QACf,cAAc;IAClB,GAAG,OAAO,UAAU,YAAY;QAC5B,YAAY;IAChB,IAAI,SAAS,CAAC;IACd,IAAI,gBAAgB,QAAQ,aAAa;IACzC,MAAM,wBAAwB,QAAQ,eAAe;IACrD,IAAI,iBAAiB,QAAQ,cAAc;IAC3C,MAAM,kBAAkB,CAAC;QACrB,OAAO,OAAO,0BAA0B,aAAa,sBAAsB,SAAS,0BAA0B;IAClH;IACA,MAAM,eAAe,QAAQ,YAAY;IACzC,8EAA8E;IAC9E,iBAAiB;IACjB,IAAI,WAAW,OAAO;QAClB,MAAM,YAAY;QAClB,MAAM,cAAc,EAAE;QACtB,MAAM,KAAK,MAAM,IAAI;QACrB,KAAK,MAAM,OAAO,GAAG;YACjB,IACA,CAAC,iBAAiB,IAAI,CAAC,QAAQ,UAAU,MAAM,GAAG,CAAC,KAAK,EAAE,GAAG;gBACzD,YAAY,IAAI,CAAC;YACrB;QACJ;QACA,OAAO,QAAQ,GAAG,CAAC,YAAY,GAAG,CAAC;IACvC;IACA,OAAO,YAAY;;IACnB,eAAe,YAAY,EAAE;QACzB,gBAAgB;QAChB,MAAM,CAAC,IAAI,GAAG,UAAU;QACxB,IAAI,CAAC,KAAK;QACV,MAAM,CAAC,KAAK,IAAI,GAAG,kBAAkB,OAAO;QAC5C,MAAM,CAAC,oBAAoB,UAAU,OAAO,QAAQ,GAAG,eAAe,GAAG,CAAC;QAC1E,MAAM,kBAAkB;YACpB,MAAM,eAAe,kBAAkB,CAAC,IAAI;YAC5C,MAAM,aAAa,WAAW,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,IAAI,EAAE,MAAM,QAAQ,UAAU,KAAK;YAChH,IAAI,YAAY;gBACZ,uEAAuE;gBACvE,gCAAgC;gBAChC,OAAO,KAAK,CAAC,IAAI;gBACjB,OAAO,OAAO,CAAC,IAAI;gBACnB,IAAI,gBAAgB,YAAY,CAAC,EAAE,EAAE;oBACjC,OAAO,YAAY,CAAC,EAAE,CAAC,4NAAA,CAAA,eAA6B,EAAE,IAAI,CAAC,IAAI,MAAM,IAAI;gBAC7E;YACJ;YACA,OAAO,MAAM,IAAI;QACrB;QACA,2EAA2E;QAC3E,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,kCAAkC;YAClC,OAAO;QACX;QACA,IAAI,OAAO;QACX,IAAI;QACJ,4BAA4B;QAC5B,MAAM,mBAAmB;QACzB,QAAQ,CAAC,IAAI,GAAG;YACZ;YACA;SACH;QACD,MAAM,oBAAoB,CAAC,YAAY;QACvC,MAAM,QAAQ;QACd,mFAAmF;QACnF,6EAA6E;QAC7E,8EAA8E;QAC9E,MAAM,gBAAgB,MAAM,IAAI;QAChC,MAAM,cAAc,MAAM,EAAE;QAC5B,MAAM,gBAAgB,YAAY,eAAe,gBAAgB;QACjE,6BAA6B;QAC7B,IAAI,mBAAmB;YACnB,iBAAiB,WAAW,kBAAkB,eAAe,eAAe,iBAAiB;YAC7F,8EAA8E;YAC9E,IAAI;gBACA,MAAM;gBACN,IAAI;YACR;QACJ;QACA,IAAI,WAAW,OAAO;YAClB,6DAA6D;YAC7D,IAAI;gBACA,OAAO,KAAK;YAChB,EAAE,OAAO,KAAK;gBACV,sEAAsE;gBACtE,QAAQ;YACZ;QACJ;QACA,8DAA8D;QAC9D,IAAI,QAAQ,cAAc,OAAO;YAC7B,wEAAwE;YACxE,yBAAyB;YACzB,OAAO,MAAM,KAAK,KAAK,CAAC,CAAC;gBACrB,QAAQ;YACZ;YACA,4EAA4E;YAC5E,mEAAmE;YACnE,wBAAwB;YACxB,IAAI,qBAAqB,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;gBACvC,IAAI,OAAO,MAAM;gBACjB,OAAO;YACX,OAAO,IAAI,SAAS,qBAAqB,gBAAgB,QAAQ;gBAC7D,+DAA+D;gBAC/D,yBAAyB;gBACzB,gBAAgB;gBAChB,wEAAwE;gBACxE,IAAI;oBACA,MAAM;oBACN,IAAI;gBACR;YACJ;QACJ;QACA,mDAAmD;QACnD,IAAI,eAAe;YACf,IAAI,CAAC,OAAO;gBACR,kCAAkC;gBAClC,IAAI,WAAW,gBAAgB;oBAC3B,MAAM,qBAAqB,cAAc,MAAM;oBAC/C,IAAI;wBACA,MAAM;wBACN,OAAO;wBACP,IAAI;oBACR;gBACJ,OAAO;oBACH,iGAAiG;oBACjG,IAAI;wBACA;wBACA,OAAO;wBACP,IAAI;oBACR;gBACJ;YACJ;QACJ;QACA,sDAAsD;QACtD,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG;QACnB,8CAA8C;QAC9C,QAAQ,OAAO,CAAC,mBAAmB,IAAI,CAAC;YACpC,6EAA6E;YAC7E,mCAAmC;YACnC,IAAI;gBACA,IAAI;YACR;QACJ;QACA,6BAA6B;QAC7B,IAAI,OAAO;YACP,IAAI,cAAc,MAAM;YACxB;QACJ;QACA,OAAO;IACX;AACJ;AAEA,MAAM,oBAAoB,CAAC,cAAc;IACrC,IAAI,MAAM,OAAO,aAAa;QAC1B,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;IACnD;AACJ;AACA,MAAM,YAAY,CAAC,UAAU;IACzB,uEAAuE;IACvE,8EAA8E;IAC9E,aAAa;IACb,iFAAiF;IACjF,mDAAmD;IACnD,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;QAC/B,MAAM,OAAO,aAAa,sBAAsB;QAChD,8EAA8E;QAC9E,uBAAuB;QACvB,MAAM,qBAAqB,OAAO,MAAM,CAAC;QACzC,MAAM,SAAS,eAAe,IAAI,CAAC,WAAW;QAC9C,IAAI,UAAU;QACd,MAAM,gBAAgB,OAAO,MAAM,CAAC;QACpC,MAAM,YAAY,CAAC,KAAK;YACpB,MAAM,OAAO,aAAa,CAAC,IAAI,IAAI,EAAE;YACrC,aAAa,CAAC,IAAI,GAAG;YACrB,KAAK,IAAI,CAAC;YACV,OAAO,IAAI,KAAK,MAAM,CAAC,KAAK,OAAO,CAAC,WAAW;QACnD;QACA,MAAM,SAAS,CAAC,KAAK,OAAO;YACxB,SAAS,GAAG,CAAC,KAAK;YAClB,MAAM,OAAO,aAAa,CAAC,IAAI;YAC/B,IAAI,MAAM;gBACN,KAAK,MAAM,MAAM,KAAK;oBAClB,GAAG,OAAO;gBACd;YACJ;QACJ;QACA,MAAM,eAAe;YACjB,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBAC/B,sEAAsE;gBACtE,eAAe,GAAG,CAAC,UAAU;oBACzB;oBACA,OAAO,MAAM,CAAC;oBACd,OAAO,MAAM,CAAC;oBACd,OAAO,MAAM,CAAC;oBACd;oBACA;oBACA;iBACH;gBACD,IAAI,CAAC,WAAW;oBACZ,8DAA8D;oBAC9D,uEAAuE;oBACvE,4DAA4D;oBAC5D,yBAAyB;oBACzB,qDAAqD;oBACrD,6CAA6C;oBAC7C,MAAM,eAAe,KAAK,SAAS,CAAC,WAAW,IAAI,CAAC,WAAW,kBAAkB,IAAI,CAAC,WAAW,oBAAoB,4NAAA,CAAA,cAA4B;oBACjJ,MAAM,mBAAmB,KAAK,aAAa,CAAC,WAAW,IAAI,CAAC,WAAW,kBAAkB,IAAI,CAAC,WAAW,oBAAoB,4NAAA,CAAA,kBAAgC;oBAC7J,UAAU;wBACN,gBAAgB;wBAChB,oBAAoB;wBACpB,wEAAwE;wBACxE,yEAAyE;wBACzE,8CAA8C;wBAC9C,eAAe,MAAM,CAAC;oBAC1B;gBACJ;YACJ;QACJ;QACA;QACA,wEAAwE;QACxE,iDAAiD;QACjD,6EAA6E;QAC7E,2CAA2C;QAC3C,kEAAkE;QAClE,OAAO;YACH;YACA;YACA;YACA;SACH;IACL;IACA,OAAO;QACH;QACA,eAAe,GAAG,CAAC,SAAS,CAAC,EAAE;KAClC;AACL;AAEA,cAAc;AACd,MAAM,eAAe,CAAC,GAAG,IAAI,QAAQ,YAAY;IAC7C,MAAM,gBAAgB,OAAO,eAAe;IAC5C,MAAM,oBAAoB,KAAK,UAAU;IACzC,sBAAsB;IACtB,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,CAAC,CAAC,IAAI,OAAO,kBAAkB;IAC9H,IAAI,CAAC,YAAY,kBAAkB,oBAAoB,eAAe;QAClE;IACJ;IACA,WAAW,YAAY,SAAS;AACpC;AACA,MAAM,UAAU,8LAAA,CAAA,SAAM;AACtB,yBAAyB;AACzB,MAAM,CAAC,OAAO,OAAO,GAAG,UAAU,IAAI;AACtC,iBAAiB;AACjB,MAAM,gBAAgB,aAAa;IAC/B,SAAS;IACT,eAAe;IACf,WAAW;IACX,SAAS;IACT;IACA,aAAa;IACb,WAAW;IACX,mBAAmB;IACnB,uBAAuB;IACvB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,oBAAoB,iBAAiB,QAAQ;IAC7C,uBAAuB,IAAI;IAC3B,kBAAkB,IAAI;IACtB,gBAAgB,iBAAiB,OAAO;IACxC,YAAY;IACZ;IACA,UAAU,IAAI;IACd;IACA;IACA,UAAU,CAAC;AACf,GACA;AAEA,MAAM,eAAe,CAAC,GAAG;IACrB,mEAAmE;IACnE,MAAM,IAAI,aAAa,GAAG;IAC1B,yEAAyE;IACzE,IAAI,GAAG;QACH,MAAM,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG;QAClC,MAAM,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG;QAClC,IAAI,MAAM,IAAI;YACV,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;QACtB;QACA,IAAI,MAAM,IAAI;YACV,EAAE,QAAQ,GAAG,aAAa,IAAI;QAClC;IACJ;IACA,OAAO;AACX;AAEA,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,CAAC;AACxC,MAAM,YAAY,CAAC;IACf,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,aAAU,AAAD,EAAE;IAChC,MAAM,qBAAqB,WAAW;IACtC,MAAM,SAAS,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;qCAAE,IAAI,qBAAqB,MAAM,gBAAgB;oCAAO;QACzE;QACA;QACA;KACH;IACD,+CAA+C;IAC/C,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;6CAAE,IAAI,qBAAqB,SAAS,aAAa,cAAc;4CAAS;QACjG;QACA;QACA;KACH;IACD,yCAAyC;IACzC,MAAM,WAAW,UAAU,OAAO,QAAQ;IAC1C,6CAA6C;IAC7C,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,YAAY,CAAC,gBAAgB,OAAO,EAAE;QACtC,gBAAgB,OAAO,GAAG,UAAU,SAAS,eAAe,KAAK,IAAI,QAAQ;IACjF;IACA,MAAM,eAAe,gBAAgB,OAAO;IAC5C,iDAAiD;IACjD,IAAI,cAAc;QACd,eAAe,KAAK,GAAG,YAAY,CAAC,EAAE;QACtC,eAAe,MAAM,GAAG,YAAY,CAAC,EAAE;IAC3C;IACA,sBAAsB;IACtB;+CAA0B;YACtB,IAAI,cAAc;gBACd,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,EAAE;gBAClC,OAAO,YAAY,CAAC,EAAE;YAC1B;QACJ;8CAAG,EAAE;IACL,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,QAAQ,EAAE,aAAa,OAAO;QAChE,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/_internal/constants.mjs"], "sourcesContent": ["const INFINITE_PREFIX = '$inf$';\n\nexport { INFINITE_PREFIX };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/_internal/index.mjs"], "sourcesContent": ["import { i as isWindowDefined, a as isFunction, m as mergeObjects, S as SWRConfigContext, d as defaultConfig, s as serialize, b as SWRGlobalState, c as cache, e as isUndefined, f as mergeConfigs } from './config-context-client-v7VOFo66.mjs';\nexport { I as IS_REACT_LEGACY, r as IS_SERVER, O as OBJECT, g as SWRConfig, U as UNDEFINED, k as compare, z as createCacheHelper, q as defaultConfigOptions, o as getTimestamp, y as hasRequestAnimationFrame, h as initCache, n as internalMutate, w as isDocumentDefined, x as isLegacyDeno, B as isPromiseLike, j as mutate, A as noop, p as preset, t as rAF, v as slowConnection, l as stableHash, u as useIsomorphicLayoutEffect } from './config-context-client-v7VOFo66.mjs';\nimport * as revalidateEvents from './events.mjs';\nexport { revalidateEvents };\nimport { INFINITE_PREFIX } from './constants.mjs';\nexport { INFINITE_PREFIX } from './constants.mjs';\nimport React, { useContext } from 'react';\nexport * from './types.mjs';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { SWRGlobalState, cache, defaultConfig, isFunction, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, normalize, preload, serialize, subscribeCallback, useSWRConfig, withArgs, withMiddleware };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAIA;AAEA;;;;;;;;;AAGA,mBAAmB;AACnB,MAAM,iBAAiB,6PAAA,CAAA,IAAe,IAAI,OAAO,oBAAoB;AACrE,MAAM,MAAM,iBAAiB,OAAO,oBAAoB,GAAG,EAAE;AAC7D,MAAM,gBAAgB;IAClB,IAAI,gBAAgB;QAChB,mBAAmB;QACnB,OAAO,sBAAsB,GAAG,4QAAA,CAAA,UAAK;IACzC;AACJ;AAEA,MAAM,YAAY,CAAC;IACf,OAAO,CAAA,GAAA,6PAAA,CAAA,IAAU,AAAD,EAAE,IAAI,CAAC,EAAE,IAAI;QACzB,IAAI,CAAC,EAAE;QACP,IAAI,CAAC,EAAE;QACP,IAAI,CAAC,EAAE,IAAI,CAAC;KACf,GAAG;QACA,IAAI,CAAC,EAAE;QACP;QACA,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC;KAC9C;AACL;AAEA,MAAM,eAAe;IACjB,OAAO,CAAA,GAAA,6PAAA,CAAA,IAAY,AAAD,EAAE,6PAAA,CAAA,IAAa,EAAE,CAAA,GAAA,4QAAA,CAAA,aAAU,AAAD,EAAE,6PAAA,CAAA,IAAgB;AAClE;AAEA,MAAM,UAAU,CAAC,MAAM;IACnB,MAAM,CAAC,KAAK,MAAM,GAAG,CAAA,GAAA,6PAAA,CAAA,IAAS,AAAD,EAAE;IAC/B,MAAM,OAAO,QAAQ,GAAG,6PAAA,CAAA,IAAc,CAAC,GAAG,CAAC,6PAAA,CAAA,IAAK;IAChD,2DAA2D;IAC3D,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC,IAAI;IACrC,MAAM,MAAM,QAAQ;IACpB,OAAO,CAAC,IAAI,GAAG;IACf,OAAO;AACX;AACA,MAAM,aAAa,CAAC,aAAa,CAAC,MAAM,UAAU;QAC1C,4EAA4E;QAC5E,MAAM,UAAU,YAAY,CAAC,CAAC,GAAG;YAC7B,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,6PAAA,CAAA,IAAS,AAAD,EAAE;YACxB,MAAM,OAAO,QAAQ,GAAG,6PAAA,CAAA,IAAc,CAAC,GAAG,CAAC,6PAAA,CAAA,IAAK;YAChD,IAAI,IAAI,UAAU,CAAC,+NAAA,CAAA,kBAAe,GAAG;gBACjC,6CAA6C;gBAC7C,+CAA+C;gBAC/C,OAAO,YAAY;YACvB;YACA,MAAM,MAAM,OAAO,CAAC,IAAI;YACxB,IAAI,CAAA,GAAA,6PAAA,CAAA,IAAW,AAAD,EAAE,MAAM,OAAO,YAAY;YACzC,OAAO,OAAO,CAAC,IAAI;YACnB,OAAO;QACX,CAAC;QACD,OAAO,WAAW,MAAM,SAAS;IACrC;AAEJ,MAAM,sBAAsB,IAAI,MAAM,CAAC;AAEvC,gFAAgF;AAChF,kBAAkB;AAClB,MAAM,WAAW,CAAC;IACd,OAAO,SAAS,WAAW,GAAG,IAAI;QAC9B,+CAA+C;QAC/C,MAAM,iBAAiB;QACvB,uBAAuB;QACvB,MAAM,CAAC,KAAK,IAAI,QAAQ,GAAG,UAAU;QACrC,wBAAwB;QACxB,MAAM,SAAS,CAAA,GAAA,6PAAA,CAAA,IAAY,AAAD,EAAE,gBAAgB;QAC5C,mBAAmB;QACnB,IAAI,OAAO;QACX,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,MAAM,aAAa,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC;QACtC,IAAI,IAAI,IAAI,WAAW,MAAM,EAAE,KAAK;YAChC,OAAO,UAAU,CAAC,EAAE,CAAC;QACzB;QACA,OAAO,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,MAAM;IACnD;AACJ;AAEA,2EAA2E;AAC3E,4BAA4B;AAC5B,MAAM,oBAAoB,CAAC,KAAK,WAAW;IACvC,MAAM,oBAAoB,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE;IAChE,kBAAkB,IAAI,CAAC;IACvB,OAAO;QACH,MAAM,QAAQ,kBAAkB,OAAO,CAAC;QACxC,IAAI,SAAS,GAAG;YACZ,2BAA2B;YAC3B,iBAAiB,CAAC,MAAM,GAAG,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;YAC1E,kBAAkB,GAAG;QACzB;IACJ;AACJ;AAEA,yCAAyC;AACzC,MAAM,iBAAiB,CAAC,QAAQ;IAC5B,OAAO,CAAC,GAAG;QACP,MAAM,CAAC,KAAK,IAAI,OAAO,GAAG,UAAU;QACpC,MAAM,OAAO,CAAC,OAAO,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC;QACvC,OAAO,OAAO,KAAK,IAAI;YACnB,GAAG,MAAM;YACT,KAAK;QACT;IACJ;AACJ;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/index/index.mjs"], "sourcesContent": ["import React, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { OBJECT as OBJECT$1, SWRConfig as SWRConfig$1, defaultConfig, with<PERSON>rgs, SWRGlobalState, serialize as serialize$1, createCacheHel<PERSON>, isUndefined as isUndefined$1, UNDEFINED as UNDEFINED$1, isPromiseLike, getTimestamp, isFunction as isFunction$1, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from '../_internal/index.mjs';\nexport { mutate, preload, useSWRConfig } from '../_internal/index.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = React.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize$1(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = isUndefined$1(fallbackData) ? isUndefined$1(config.fallback) ? UNDEFINED$1 : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined$1(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined$1(cachedData) ? fallback && isPromiseLike(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined$1(cachedData) ? isUndefined$1(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined$1(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined$1(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined$1(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined$1(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined$1(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined$1(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined$1(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED$1;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined$1(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction$1(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined$1(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED$1, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if (isUndefined$1(data) || IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                rAF(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction$1(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined$1(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined$1(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined$1(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined$1(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = OBJECT$1.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAGA,+DAA+D;AAC/D,MAAM,OAAO,KAAK;AAClB,mEAAmE;AACnE,8EAA8E;AAC9E,8DAA8D;AAC9D,kBAAkB;AAClB,MAAM,YAAY,eAAe,GAAG;AACpC,MAAM,SAAS;AACf,MAAM,cAAc,CAAC,IAAI,MAAM;AAC/B,MAAM,aAAa,CAAC,IAAI,OAAO,KAAK;AAEpC,+CAA+C;AAC/C,2CAA2C;AAC3C,yDAAyD;AACzD,6BAA6B;AAC7B,MAAM,QAAQ,IAAI;AAClB,MAAM,eAAe,CAAC,OAAO,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChG,qBAAqB;AACrB,IAAI,UAAU;AACd,8CAA8C;AAC9C,4CAA4C;AAC5C,kCAAkC;AAClC,gCAAgC;AAChC,4BAA4B;AAC5B,EAAE;AACF,+EAA+E;AAC/E,YAAY;AACZ,MAAM,aAAa,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,MAAM,SAAS,aAAa,KAAK;IACjC,MAAM,UAAU,aAAa,KAAK;IAClC,MAAM,gBAAgB,aAAa,KAAK;IACxC,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,SAAS;QAC5C,4EAA4E;QAC5E,sDAAsD;QACtD,SAAS,MAAM,GAAG,CAAC;QACnB,IAAI,QAAQ,OAAO;QACnB,4EAA4E;QAC5E,gCAAgC;QAChC,2EAA2E;QAC3E,SAAS,EAAE,UAAU;QACrB,MAAM,GAAG,CAAC,KAAK;QACf,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,SAAS;YACT,SAAS;YACT,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,QAAQ;gBACvC,UAAU,WAAW,GAAG,CAAC,MAAM,IAAI;YACvC;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;QACA,IAAI,eAAe;YACf,qBAAqB;YACrB,SAAS;YACT,MAAM,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;YAClC,MAAM,CAAC,YAAY,QAAQ,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG;oBAC1B,UAAU,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI;gBACrD;YACJ;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;IACJ,OAAO;QACH,SAAS,SAAS,IAAI,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,KAAK;IACvH;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,IAAI,WAAW,MAAM;QACjB,IAAI;YACA,MAAM;QACV,EAAE,OAAO,KAAK;YACV,yBAAyB;YACzB,MAAM;QACV;IACJ;IACA,8EAA8E;IAC9E,mBAAmB;IACnB,MAAM,OAAO;IACb,uDAAuD;IACvD,MAAM,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,OAAO;IACjG,OAAO;QACH;QACA;KACH;AACL;AAEA,MAAM,qBAAqB,CAAC,MAAM,UAAU,IAAI,CAAC,EAAE;AAEnD,4CAA4C;AAC5C,MAAM,MAAM,4QAAA,CAAA,UAAK,CAAC,GAAG,IAAI,gFAAgF;AACzG,yBAAyB;AACzB,gFAAgF;AAChF,qIAAqI;AACrI,CAAC,CAAC;IACE,OAAO,SAAS,MAAM;QAClB,KAAK;YACD,MAAM;QACV,KAAK;YACD,OAAO,SAAS,KAAK;QACzB,KAAK;YACD,MAAM,SAAS,MAAM;QACzB;YACI,SAAS,MAAM,GAAG;YAClB,SAAS,IAAI,CAAC,CAAC;gBACX,SAAS,MAAM,GAAG;gBAClB,SAAS,KAAK,GAAG;YACrB,GAAG,CAAC;gBACA,SAAS,MAAM,GAAG;gBAClB,SAAS,MAAM,GAAG;YACtB;YACA,MAAM;IACd;AACJ,CAAC;AACD,MAAM,cAAc;IAChB,QAAQ;AACZ;AACA,MAAM,gBAAgB,CAAC,MAAM,SAAS;IAClC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG;IACnK,MAAM,CAAC,oBAAoB,UAAU,OAAO,QAAQ,GAAG,oSAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;IAC1E,qDAAqD;IACrD,8EAA8E;IAC9E,kBAAkB;IAClB,uCAAuC;IACvC,MAAM,CAAC,KAAK,MAAM,GAAG,CAAA,GAAA,+RAAA,CAAA,YAAW,AAAD,EAAE;IACjC,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IACjC,8EAA8E;IAC9E,iCAAiC;IACjC,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,mCAAmC;IACnC,MAAM,SAAS,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,IAAI,UAAU,OAAO;IACvC,MAAM,WAAW,IAAI,YAAY,SAAS,MAAM,YAAY,QAAQ;IACpE,MAAM,CAAC,UAAU,UAAU,gBAAgB,gBAAgB,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IACvF,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;IAC5C,mFAAmF;IACnF,wEAAwE;IACxE,MAAM,WAAW,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,gBAAgB,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,OAAO,QAAQ,IAAI,+RAAA,CAAA,YAAW,GAAG,OAAO,QAAQ,CAAC,IAAI,GAAG;IACrH,MAAM,UAAU,CAAC,MAAM;QACnB,IAAI,MAAM,KAAK,kBAAkB;YAC7B,MAAM,IAAI;YACV,IAAI,MAAM,QAAQ;gBACd,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG;oBAC/B,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;wBACzB,OAAO;oBACX;oBACA,IAAI,CAAC,QAAQ,cAAc,OAAO,CAAC,EAAE,GAAG;wBACpC,OAAO;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;oBACxB,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;8CAAE;YACxB,MAAM,qBAAqB;yEAAC;oBACxB,IAAI,CAAC,KAAK,OAAO;oBACjB,IAAI,CAAC,SAAS,OAAO;oBACrB,6DAA6D;oBAC7D,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,oBAAoB,OAAO;oBAC9C,wCAAwC;oBACxC,IAAI,YAAY,QAAQ,IAAI,OAAO;oBACnC,IAAI,UAAU,OAAO;oBACrB,OAAO,sBAAsB;gBACjC;aAAC;YACD,mDAAmD;YACnD,MAAM;uEAAmB,CAAC;oBACtB,mDAAmD;oBACnD,MAAM,WAAW,CAAA,GAAA,kSAAA,CAAA,eAAY,AAAD,EAAE;oBAC9B,OAAO,SAAS,EAAE;oBAClB,IAAI,CAAC,oBAAoB;wBACrB,OAAO;oBACX;oBACA,OAAO;wBACH,cAAc;wBACd,WAAW;wBACX,GAAG,QAAQ;oBACf;gBACJ;;YACA,MAAM,aAAa;YACnB,MAAM,cAAc;YACpB,MAAM,iBAAiB,iBAAiB;YACxC,MAAM,iBAAiB,eAAe,cAAc,iBAAiB,iBAAiB;YACtF,wEAAwE;YACxE,qEAAqE;YACrE,sDAAsD;YACtD,IAAI,oBAAoB;YACxB,OAAO;;0DACH;wBACI,MAAM,cAAc,iBAAiB;wBACrC,MAAM,gBAAgB,QAAQ,aAAa;wBAC3C,IAAI,eAAe;4BACf,iEAAiE;4BACjE,0DAA0D;4BAC1D,uEAAuE;4BACvE,+DAA+D;4BAC/D,uCAAuC;4BACvC,kEAAkE;4BAClE,gEAAgE;4BAChE,oEAAoE;4BACpE,mEAAmE;4BACnE,mCAAmC;4BACnC,kBAAkB,IAAI,GAAG,YAAY,IAAI;4BACzC,kBAAkB,SAAS,GAAG,YAAY,SAAS;4BACnD,kBAAkB,YAAY,GAAG,YAAY,YAAY;4BACzD,kBAAkB,KAAK,GAAG,YAAY,KAAK;4BAC3C,OAAO;wBACX,OAAO;4BACH,oBAAoB;4BACpB,OAAO;wBACX;oBACJ;;;0DACA,IAAI;;aACP;QACL,uDAAuD;QACvD;6CAAG;QACC;QACA;KACH;IACD,gDAAgD;IAChD,MAAM,SAAS,CAAA,GAAA,uQAAA,CAAA,uBAAoB,AAAD,EAAE,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,WAAW,eAAe;8DAAK,CAAC,SAAS;oBAClF,IAAI,CAAC,QAAQ,MAAM,UAAU;gBACjC;;qDACJ;QACI;QACA;KACH,GAAG,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;IAClC,MAAM,iBAAiB,CAAC,kBAAkB,OAAO;IACjD,MAAM,iBAAiB,kBAAkB,CAAC,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG;IACnF,MAAM,aAAa,OAAO,IAAI;IAC9B,MAAM,OAAO,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,cAAc,YAAY,CAAA,GAAA,mSAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,IAAI,YAAY,WAAW;IAC1G,MAAM,QAAQ,OAAO,KAAK;IAC1B,0FAA0F;IAC1F,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,eAAe,mBAAmB,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,cAAc,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,aAAa,OAAO,IAAI,OAAO,aAAa,OAAO,GAAG,aAAa;IACrJ,iEAAiE;IACjE,wFAAwF;IACxF,8DAA8D;IAC9D,MAAM,8BAA8B,CAAC;QACjC,2FAA2F;QAC3F,IAAI,kBAAkB,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,OAAO;QACpD,6DAA6D;QAC7D,IAAI,kBAAkB,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,oBAAoB,OAAO;QAChE,wCAAwC;QACxC,IAAI,YAAY,QAAQ,IAAI,OAAO;QACnC,qEAAqE;QACrE,kEAAkE;QAClE,kEAAkE;QAClE,IAAI,UAAU,OAAO,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,QAAQ;QACnD,+DAA+D;QAC/D,oEAAoE;QACpE,OAAO,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,SAAS;IAClC,CAAC;IACD,wCAAwC;IACxC,oFAAoF;IACpF,MAAM,yBAAyB,CAAC,CAAC,CAAC,OAAO,WAAW,kBAAkB,2BAA2B;IACjG,MAAM,eAAe,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,OAAO,YAAY,IAAI,yBAAyB,OAAO,YAAY;IACtG,MAAM,YAAY,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,OAAO,SAAS,IAAI,yBAAyB,OAAO,SAAS;IAC7F,2EAA2E;IAC3E,sDAAsD;IACtD,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YAClC,MAAM,iBAAiB,WAAW,OAAO;YACzC,IAAI,CAAC,OAAO,CAAC,kBAAkB,aAAa,OAAO,IAAI,YAAY,QAAQ,IAAI;gBAC3E,OAAO;YACX;YACA,IAAI;YACJ,IAAI;YACJ,IAAI,UAAU;YACd,MAAM,OAAO,kBAAkB,CAAC;YAChC,uEAAuE;YACvE,mCAAmC;YACnC,MAAM,wBAAwB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,MAAM;YACzD;;;;;;;;;;MAUF,GAAG,MAAM;2EAAoB;oBACvB,IAAI,qSAAA,CAAA,kBAAe,EAAE;wBACjB,OAAO,CAAC,aAAa,OAAO,IAAI,QAAQ,OAAO,OAAO,IAAI,kBAAkB,OAAO;oBACvF;oBACA,OAAO,QAAQ,OAAO,OAAO;gBACjC;;YACA,oDAAoD;YACpD,MAAM,aAAa;gBACf,cAAc;gBACd,WAAW;YACf;YACA,MAAM;qFAA8B;oBAChC,SAAS;gBACb;;YACA,MAAM;sEAAe;oBACjB,2DAA2D;oBAC3D,MAAM,cAAc,KAAK,CAAC,IAAI;oBAC9B,IAAI,eAAe,WAAW,CAAC,EAAE,KAAK,SAAS;wBAC3C,OAAO,KAAK,CAAC,IAAI;oBACrB;gBACJ;;YACA,qEAAqE;YACrE,MAAM,eAAe;gBACjB,cAAc;YAClB;YACA,0EAA0E;YAC1E,8CAA8C;YAC9C,IAAI,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,WAAW,IAAI,GAAG;gBAChC,aAAa,SAAS,GAAG;YAC7B;YACA,IAAI;gBACA,IAAI,uBAAuB;oBACvB,SAAS;oBACT,mEAAmE;oBACnE,qCAAqC;oBACrC,IAAI,OAAO,cAAc,IAAI,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,WAAW,IAAI,GAAG;wBACzD;qEAAW;gCACP,IAAI,WAAW,qBAAqB;oCAChC,YAAY,aAAa,CAAC,KAAK;gCACnC;4BACJ;oEAAG,OAAO,cAAc;oBAC5B;oBACA,4CAA4C;oBAC5C,uCAAuC;oBACvC,KAAK,CAAC,IAAI,GAAG;wBACT,eAAe;wBACf,CAAA,GAAA,kSAAA,CAAA,eAAY,AAAD;qBACd;gBACL;gBACA,gEAAgE;gBAChE,mBAAmB;;gBAEnB,CAAC,SAAS,QAAQ,GAAG,KAAK,CAAC,IAAI;gBAC/B,UAAU,MAAM;gBAChB,IAAI,uBAAuB;oBACvB,0DAA0D;oBAC1D,0BAA0B;oBAC1B,WAAW,cAAc,OAAO,gBAAgB;gBACpD;gBACA,uEAAuE;gBACvE,uEAAuE;gBACvE,qDAAqD;gBACrD,mCAAmC;gBACnC,oDAAoD;gBACpD,iDAAiD;gBACjD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS;oBAC1C,IAAI,uBAAuB;wBACvB,IAAI,qBAAqB;4BACrB,YAAY,WAAW,CAAC;wBAC5B;oBACJ;oBACA,OAAO;gBACX;gBACA,eAAe;gBACf,WAAW,KAAK,GAAG,+RAAA,CAAA,YAAW;gBAC9B,iFAAiF;gBACjF,UAAU;gBACV,8BAA8B;gBAC9B,yBAAyB;gBACzB,UAAU;gBACV,8BAA8B;gBAC9B,qBAAqB;gBACrB,UAAU;gBACV,8BAA8B;gBAC9B,oCAAoC;gBACpC,gFAAgF;gBAChF,4EAA4E;gBAC5E,MAAM,eAAe,QAAQ,CAAC,IAAI;gBAClC,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,iBAAiB,SAAS;gBAC7C,CAAC,WAAW,YAAY,CAAC,EAAE,IAAI,SAAS;gBACxC,WAAW,YAAY,CAAC,EAAE,IAAI,SAAS;gBACvC,YAAY,CAAC,EAAE,KAAK,CAAC,GAAG;oBACpB;oBACA,IAAI,uBAAuB;wBACvB,IAAI,qBAAqB;4BACrB,YAAY,WAAW,CAAC;wBAC5B;oBACJ;oBACA,OAAO;gBACX;gBACA,gEAAgE;gBAChE,uCAAuC;gBACvC,MAAM,YAAY,WAAW,IAAI;gBACjC,0CAA0C;gBAC1C,8EAA8E;gBAC9E,WAAW,IAAI,GAAG,QAAQ,WAAW,WAAW,YAAY;gBAC5D,gEAAgE;gBAChE,IAAI,uBAAuB;oBACvB,IAAI,qBAAqB;wBACrB,YAAY,SAAS,CAAC,SAAS,KAAK;oBACxC;gBACJ;YACJ,EAAE,OAAO,KAAK;gBACV;gBACA,MAAM,gBAAgB;gBACtB,MAAM,EAAE,kBAAkB,EAAE,GAAG;gBAC/B,qEAAqE;gBACrE,IAAI,CAAC,cAAc,QAAQ,IAAI;oBAC3B,yDAAyD;oBACzD,WAAW,KAAK,GAAG;oBACnB,gEAAgE;oBAChE,gBAAgB;oBAChB,IAAI,yBAAyB,qBAAqB;wBAC9C,cAAc,OAAO,CAAC,KAAK,KAAK;wBAChC,IAAI,uBAAuB,QAAQ,CAAA,GAAA,gSAAA,CAAA,aAAY,AAAD,EAAE,uBAAuB,mBAAmB,MAAM;4BAC5F,IAAI,CAAC,YAAY,iBAAiB,IAAI,CAAC,YAAY,qBAAqB,IAAI,YAAY;gCACpF,uDAAuD;gCACvD,8BAA8B;gCAC9B,kDAAkD;gCAClD,cAAc,YAAY,CAAC,KAAK,KAAK;6EAAe,CAAC;wCACjD,MAAM,eAAe,kBAAkB,CAAC,IAAI;wCAC5C,IAAI,gBAAgB,YAAY,CAAC,EAAE,EAAE;4CACjC,YAAY,CAAC,EAAE,CAAC,wQAAA,CAAA,mBAAgB,CAAC,sBAAsB,EAAE;wCAC7D;oCACJ;4EAAG;oCACC,YAAY,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI;oCACrC,QAAQ;gCACZ;4BACJ;wBACJ;oBACJ;gBACJ;YACJ;YACA,2BAA2B;YAC3B,UAAU;YACV,mCAAmC;YACnC;YACA,OAAO;QACX;gDACA,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,SAAS;IACT,2DAA2D;IAC3D,oDAAoD;IACpD,4DAA4D;IAC5D,4CAA4C;IAC5C,mDAAmD;IACnD,uDAAuD;IACvD;QACI;QACA;KACH;IACD,uEAAuE;IACvE,wDAAwD;IACxD,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;kDAC9B,CAAC,GAAG;YACA,OAAO,CAAA,GAAA,oSAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,OAAO,KAAK;QACpD;iDACA,EAAE;IACF,+BAA+B;IAC/B,CAAA,GAAA,+SAAA,CAAA,4BAAyB,AAAD;mDAAE;YACtB,WAAW,OAAO,GAAG;YACrB,UAAU,OAAO,GAAG;YACpB,wEAAwE;YACxE,kCAAkC;YAClC,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,aAAa;gBAC5B,aAAa,OAAO,GAAG;YAC3B;QACJ;;IACA,gCAAgC;IAChC,CAAA,GAAA,+SAAA,CAAA,4BAAyB,AAAD;mDAAE;YACtB,IAAI,CAAC,KAAK;YACV,MAAM,iBAAiB,WAAW,IAAI,CAAC,+RAAA,CAAA,YAAW,EAAE;YACpD,IAAI,yBAAyB;YAC7B,IAAI,YAAY,iBAAiB,EAAE;gBAC/B,MAAM,UAAU,KAAK,GAAG;gBACxB,yBAAyB,UAAU,YAAY,qBAAqB;YACxE;YACA,mEAAmE;YACnE,iCAAiC;YACjC,MAAM;wEAAe,CAAC,MAAM,OAAO,CAAC,CAAC;oBACjC,IAAI,QAAQ,wQAAA,CAAA,mBAAgB,CAAC,WAAW,EAAE;wBACtC,MAAM,MAAM,KAAK,GAAG;wBACpB,IAAI,YAAY,iBAAiB,IAAI,MAAM,0BAA0B,YAAY;4BAC7E,yBAAyB,MAAM,YAAY,qBAAqB;4BAChE;wBACJ;oBACJ,OAAO,IAAI,QAAQ,wQAAA,CAAA,mBAAgB,CAAC,eAAe,EAAE;wBACjD,IAAI,YAAY,qBAAqB,IAAI,YAAY;4BACjD;wBACJ;oBACJ,OAAO,IAAI,QAAQ,wQAAA,CAAA,mBAAgB,CAAC,YAAY,EAAE;wBAC9C,OAAO;oBACX,OAAO,IAAI,QAAQ,wQAAA,CAAA,mBAAgB,CAAC,sBAAsB,EAAE;wBACxD,OAAO,WAAW;oBACtB;oBACA;gBACJ;;YACA,MAAM,cAAc,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,oBAAoB;YAC/D,+DAA+D;YAC/D,aAAa,OAAO,GAAG;YACvB,OAAO,OAAO,GAAG;YACjB,kBAAkB,OAAO,GAAG;YAC5B,sCAAsC;YACtC,SAAS;gBACL,IAAI;YACR;YACA,yBAAyB;YACzB,IAAI,6BAA6B;gBAC7B,IAAI,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,SAAS,+RAAA,CAAA,YAAS,EAAE;oBAClC,0BAA0B;oBAC1B;gBACJ,OAAO;oBACH,mEAAmE;oBACnE,aAAa;oBACb,CAAA,GAAA,yRAAA,CAAA,MAAG,AAAD,EAAE;gBACR;YACJ;YACA;2DAAO;oBACH,wBAAwB;oBACxB,aAAa,OAAO,GAAG;oBACvB;gBACJ;;QACJ;kDAAG;QACC;KACH;IACD,UAAU;IACV,CAAA,GAAA,+SAAA,CAAA,4BAAyB,AAAD;mDAAE;YACtB,IAAI;YACJ,SAAS;gBACL,0BAA0B;gBAC1B,sEAAsE;gBACtE,MAAM,WAAW,CAAA,GAAA,gSAAA,CAAA,aAAY,AAAD,EAAE,mBAAmB,gBAAgB,WAAW,IAAI,IAAI;gBACpF,sEAAsE;gBACtE,mDAAmD;gBACnD,gEAAgE;gBAChE,IAAI,YAAY,UAAU,CAAC,GAAG;oBAC1B,QAAQ,WAAW,SAAS;gBAChC;YACJ;YACA,SAAS;gBACL,+BAA+B;gBAC/B,qEAAqE;gBACrE,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,qBAAqB,YAAY,SAAS,EAAE,KAAK,CAAC,sBAAsB,YAAY,QAAQ,EAAE,GAAG;oBACvH,WAAW,aAAa,IAAI,CAAC;gBACjC,OAAO;oBACH,6CAA6C;oBAC7C;gBACJ;YACJ;YACA;YACA;2DAAO;oBACH,IAAI,OAAO;wBACP,aAAa;wBACb,QAAQ,CAAC;oBACb;gBACJ;;QACJ;kDAAG;QACC;QACA;QACA;QACA;KACH;IACD,wCAAwC;IACxC,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE;IACd,4DAA4D;IAC5D,gFAAgF;IAChF,2EAA2E;IAC3E,yBAAyB;IACzB,IAAI,YAAY,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,SAAS,KAAK;QACxC,4EAA4E;QAC5E,0EAA0E;QAC1E,4CAA4C;QAC5C,IAAI,CAAC,qSAAA,CAAA,kBAAe,IAAI,+RAAA,CAAA,YAAS,EAAE;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,qEAAqE;QACrE,WAAW,OAAO,GAAG;QACrB,UAAU,OAAO,GAAG;QACpB,aAAa,OAAO,GAAG;QACvB,MAAM,MAAM,OAAO,CAAC,IAAI;QACxB,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,MAAM;YACrB,MAAM,UAAU,YAAY;YAC5B,IAAI;QACR;QACA,IAAI,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,QAAQ;YACtB,MAAM,UAAU,WAAW;YAC3B,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,eAAe;gBAC9B,QAAQ,MAAM,GAAG;gBACjB,QAAQ,KAAK,GAAG;YACpB;YACA,IAAI;QACR,OAAO;YACH,MAAM;QACV;IACJ;IACA,MAAM,cAAc;QAChB,QAAQ;QACR,IAAI,QAAQ;YACR,kBAAkB,IAAI,GAAG;YACzB,OAAO;QACX;QACA,IAAI,SAAS;YACT,kBAAkB,KAAK,GAAG;YAC1B,OAAO;QACX;QACA,IAAI,gBAAgB;YAChB,kBAAkB,YAAY,GAAG;YACjC,OAAO;QACX;QACA,IAAI,aAAa;YACb,kBAAkB,SAAS,GAAG;YAC9B,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM,YAAY,4RAAA,CAAA,SAAQ,CAAC,cAAc,CAAC,+RAAA,CAAA,YAAW,EAAE,gBAAgB;IACnE,OAAO,mSAAA,CAAA,gBAAa;AACxB;AACA;;;;;;;;;;;;;;CAcC,GAAG,MAAM,SAAS,CAAA,GAAA,2OAAA,CAAA,WAAQ,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/swr%402.3.3_react%4019.1.0/node_modules/swr/dist/infinite/index.mjs"], "sourcesContent": ["import { useRef, useCallback } from 'react';\nimport useS<PERSON> from '../index/index.mjs';\nimport { withMiddleware, SWRGlobalState, cache, INFINITE_PREFIX as INFINITE_PREFIX$1, createCacheHelper, isUndefined as isUndefined$1, useIsomorphicLayoutEffect, UNDEFINED as UNDEFINED$1, serialize as serialize$1, isFunction as isFunction$1 } from '../_internal/index.mjs';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { INFINITE_PREFIX } from '../_internal/constants.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\nconst EMPTY_PROMISE = Promise.resolve();\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = useRef(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = SWRGlobalState.get(cache);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = INFINITE_PREFIX$1 + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = createCacheHelper(cache$1, infiniteKey);\n        const getSnapshot = useCallback(()=>{\n            const size = isUndefined$1(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        useSyncExternalStore(useCallback((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = useCallback(()=>{\n            const cachedPageSize = get()._l;\n            return isUndefined$1(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = useRef(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        useIsomorphicLayoutEffect(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: UNDEFINED$1\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = createCacheHelper(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = serialize$1(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = createCacheHelper(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || isUndefined$1(pageData) || revalidateFirstPage && !i && !isUndefined$1(cacheData) || shouldRevalidateOnMount || cacheData && !isUndefined$1(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: UNDEFINED$1\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = useCallback(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!isUndefined$1(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = useCallback((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = createCacheHelper(cache$1, infiniteKey);\n            let size;\n            if (isFunction$1(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = createCacheHelper(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = serialize$1(getKey(i, previousPageData));\n                const [getCache] = createCacheHelper(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : UNDEFINED$1;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if (isUndefined$1(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = withMiddleware(useSWR, infinite);\n\nexport { useSWRInfinite as default, infinite, unstable_serialize };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAGA,+DAA+D;AAC/D,MAAM,OAAO,KAAK;AAClB,mEAAmE;AACnE,8EAA8E;AAC9E,8DAA8D;AAC9D,kBAAkB;AAClB,MAAM,YAAY,eAAe,GAAG;AACpC,MAAM,SAAS;AACf,MAAM,cAAc,CAAC,IAAI,MAAM;AAC/B,MAAM,aAAa,CAAC,IAAI,OAAO,KAAK;AAEpC,+CAA+C;AAC/C,2CAA2C;AAC3C,yDAAyD;AACzD,6BAA6B;AAC7B,MAAM,QAAQ,IAAI;AAClB,MAAM,eAAe,CAAC,OAAO,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChG,qBAAqB;AACrB,IAAI,UAAU;AACd,8CAA8C;AAC9C,4CAA4C;AAC5C,kCAAkC;AAClC,gCAAgC;AAChC,4BAA4B;AAC5B,EAAE;AACF,+EAA+E;AAC/E,YAAY;AACZ,MAAM,aAAa,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,MAAM,SAAS,aAAa,KAAK;IACjC,MAAM,UAAU,aAAa,KAAK;IAClC,MAAM,gBAAgB,aAAa,KAAK;IACxC,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,SAAS;QAC5C,4EAA4E;QAC5E,sDAAsD;QACtD,SAAS,MAAM,GAAG,CAAC;QACnB,IAAI,QAAQ,OAAO;QACnB,4EAA4E;QAC5E,gCAAgC;QAChC,2EAA2E;QAC3E,SAAS,EAAE,UAAU;QACrB,MAAM,GAAG,CAAC,KAAK;QACf,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,SAAS;YACT,SAAS;YACT,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,QAAQ;gBACvC,UAAU,WAAW,GAAG,CAAC,MAAM,IAAI;YACvC;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;QACA,IAAI,eAAe;YACf,qBAAqB;YACrB,SAAS;YACT,MAAM,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;YAClC,MAAM,CAAC,YAAY,QAAQ,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG;oBAC1B,UAAU,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI;gBACrD;YACJ;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;IACJ,OAAO;QACH,SAAS,SAAS,IAAI,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,KAAK;IACvH;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,IAAI,WAAW,MAAM;QACjB,IAAI;YACA,MAAM;QACV,EAAE,OAAO,KAAK;YACV,yBAAyB;YACzB,MAAM;QACV;IACJ;IACA,8EAA8E;IAC9E,mBAAmB;IACnB,MAAM,OAAO;IACb,uDAAuD;IACvD,MAAM,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,OAAO;IACjG,OAAO;QACH;QACA;KACH;AACL;AAEA,MAAM,kBAAkB,CAAC;IACrB,OAAO,UAAU,SAAS,OAAO,GAAG,QAAQ,KAAK,CAAC,EAAE;AACxD;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,+NAAA,CAAA,kBAAe,GAAG,gBAAgB;AAC7C;AAEA,8EAA8E;AAC9E,2EAA2E;AAC3E,MAAM,gBAAgB,QAAQ,OAAO;AACrC,MAAM,WAAW,CAAC,aAAa,CAAC,QAAQ,IAAI;QACpC,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;QAC3B,MAAM,EAAE,OAAO,OAAO,EAAE,cAAc,CAAC,EAAE,gBAAgB,KAAK,EAAE,cAAc,KAAK,EAAE,sBAAsB,IAAI,EAAE,oBAAoB,KAAK,EAAE,WAAW,KAAK,EAAE,GAAG;QACjK,MAAM,OAAO,QAAQ,GAAG,oSAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,2RAAA,CAAA,QAAK;QAChD,uEAAuE;QACvE,sCAAsC;QACtC,IAAI;QACJ,IAAI;YACA,cAAc,gBAAgB;YAC9B,IAAI,aAAa,cAAc,+NAAA,CAAA,kBAAiB,GAAG;QACvD,EAAE,OAAO,KAAK;QACd,iBAAiB;QACjB;QACA,MAAM,CAAC,KAAK,KAAK,eAAe,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;QAC9D,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;iDAAE;gBAC5B,MAAM,OAAO,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,MAAM,EAAE,IAAI,cAAc,MAAM,EAAE;gBAC7D,OAAO;YACX,uDAAuD;YACvD;gDAAG;YACC;YACA;YACA;SACH;QACD,CAAA,GAAA,uQAAA,CAAA,uBAAoB,AAAD,EAAE,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;gBAC9B,IAAI,aAAa,OAAO,eAAe;iEAAa;wBAChD;oBACJ;;gBACA;iEAAO,KAAK;;YAChB;wDACA;YACI;YACA;SACH,GAAG,aAAa;QACjB,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;qDAAE;gBAChC,MAAM,iBAAiB,MAAM,EAAE;gBAC/B,OAAO,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,kBAAkB,cAAc;YACzD,uDAAuD;YACvD,uDAAuD;YACvD;oDAAG;YACC;YACA;SACH;QACD,oEAAoE;QACpE,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;QAC/B,0EAA0E;QAC1E,CAAA,GAAA,+SAAA,CAAA,4BAAyB,AAAD;kDAAE;gBACtB,IAAI,CAAC,YAAY,OAAO,EAAE;oBACtB,YAAY,OAAO,GAAG;oBACtB;gBACJ;gBACA,IAAI,aAAa;oBACb,uFAAuF;oBACvF,uDAAuD;oBACvD,IAAI;wBACA,IAAI,cAAc,gBAAgB,OAAO,GAAG;oBAChD;gBACJ;YACJ,6DAA6D;YAC7D,uDAAuD;YACvD;iDAAG;YACC;YACA;SACH;QACD,iEAAiE;QACjE,MAAM,0BAA0B,qBAAqB,CAAC,YAAY,OAAO;QACzE,oDAAoD;QACpD,MAAM,MAAM,WAAW;wCAAa,OAAO;gBACvC,6BAA6B;gBAC7B,MAAM,qBAAqB,MAAM,EAAE;gBACnC,MAAM,uBAAuB,MAAM,EAAE;gBACrC,IAAI;oBACA,IAAI,+RAAA,CAAA,YAAW;gBACnB;gBACA,+BAA+B;gBAC/B,MAAM,OAAO,EAAE;gBACf,MAAM,WAAW;gBACjB,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;gBAC9C,MAAM,YAAY,WAAW,IAAI;gBACjC,MAAM,eAAe,EAAE;gBACvB,IAAI,mBAAmB;gBACvB,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,EAAE;oBAC7B,MAAM,CAAC,SAAS,QAAQ,GAAG,CAAA,GAAA,+RAAA,CAAA,YAAW,AAAD,EAAE,OAAO,GAAG,WAAW,OAAO;oBACnE,IAAI,CAAC,SAAS;wBACV;oBACJ;oBACA,MAAM,CAAC,aAAa,YAAY,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;oBAC9D,4BAA4B;oBAC5B,IAAI,WAAW,cAAc,IAAI;oBACjC,mCAAmC;oBACnC,+BAA+B;oBAC/B,sBAAsB;oBACtB,yBAAyB;oBACzB,wDAAwD;oBACxD,qDAAqD;oBACrD,oCAAoC;oBACpC,MAAM,kBAAkB,iBAAiB,sBAAsB,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,aAAa,uBAAuB,CAAC,KAAK,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,cAAc,2BAA2B,aAAa,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE;oBAC1P,IAAI,MAAM,CAAC,OAAO,yBAAyB,aAAa,qBAAqB,UAAU,WAAW,eAAe,GAAG;wBAChH,MAAM;mEAAa;gCACf,MAAM,sBAAsB,WAAW;gCACvC,IAAI,CAAC,qBAAqB;oCACtB,WAAW,MAAM,GAAG;gCACxB,OAAO;oCACH,MAAM,MAAM,OAAO,CAAC,QAAQ;oCAC5B,mDAAmD;oCACnD,2BAA2B;oCAC3B,OAAO,OAAO,CAAC,QAAQ;oCACvB,2CAA2C;oCAC3C,WAAW,MAAM;gCACrB;gCACA,YAAY;oCACR,MAAM;oCACN,IAAI;gCACR;gCACA,IAAI,CAAC,EAAE,GAAG;4BACd;;wBACA,IAAI,UAAU;4BACV,aAAa,IAAI,CAAC;wBACtB,OAAO;4BACH,MAAM;wBACV;oBACJ,OAAO;wBACH,IAAI,CAAC,EAAE,GAAG;oBACd;oBACA,IAAI,CAAC,UAAU;wBACX,mBAAmB;oBACvB;gBACJ;gBACA,uCAAuC;gBACvC,IAAI,UAAU;oBACV,MAAM,QAAQ,GAAG,CAAC,aAAa,GAAG;oDAAC,CAAC,IAAI;;gBAC5C;gBACA,6EAA6E;gBAC7E,IAAI;oBACA,IAAI,+RAAA,CAAA,YAAW;gBACnB;gBACA,kBAAkB;gBAClB,OAAO;YACX;uCAAG;QACH,MAAM,SAAS,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;4CACzB,SAAS,IAAI,EAAE,IAAI;gBACf,oEAAoE;gBACpE,gBAAgB;gBAChB,MAAM,UAAU,OAAO,SAAS,YAAY;oBACxC,YAAY;gBAChB,IAAI,QAAQ,CAAC;gBACb,mBAAmB;gBACnB,MAAM,mBAAmB,QAAQ,UAAU,KAAK;gBAChD,8CAA8C;gBAC9C,IAAI,CAAC,aAAa,OAAO;gBACzB,IAAI,kBAAkB;oBAClB,IAAI,CAAC,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,OAAO;wBACtB,gDAAgD;wBAChD,IAAI;4BACA,IAAI;4BACJ,IAAI,QAAQ,UAAU;wBAC1B;oBACJ,OAAO;wBACH,8CAA8C;wBAC9C,IAAI;4BACA,IAAI;4BACJ,IAAI,QAAQ,UAAU;wBAC1B;oBACJ;gBACJ;gBACA,OAAO,UAAU,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM;oBACvC,GAAG,OAAO;oBACV,YAAY;gBAChB,KAAK,IAAI,MAAM;YACnB;2CACA,uDAAuD;QACvD;YACI;YACA;SACH;QACD,qBAAqB;QACrB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;gBACzB,8CAA8C;gBAC9C,IAAI,CAAC,aAAa,OAAO;gBACzB,MAAM,GAAG,WAAW,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;gBAClD,IAAI;gBACJ,IAAI,CAAA,GAAA,gSAAA,CAAA,aAAY,AAAD,EAAE,MAAM;oBACnB,OAAO,IAAI;gBACf,OAAO,IAAI,OAAO,OAAO,UAAU;oBAC/B,OAAO;gBACX;gBACA,IAAI,OAAO,QAAQ,UAAU,OAAO;gBACpC,WAAW;oBACP,IAAI;gBACR;gBACA,gBAAgB,OAAO,GAAG;gBAC1B,iDAAiD;gBACjD,MAAM,OAAO,EAAE;gBACf,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;gBACtD,IAAI,mBAAmB;gBACvB,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAE;oBACzB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,+RAAA,CAAA,YAAW,AAAD,EAAE,OAAO,GAAG;oBACxC,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,uSAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;oBAC9C,4BAA4B;oBAC5B,MAAM,WAAW,UAAU,WAAW,IAAI,GAAG,+RAAA,CAAA,YAAW;oBACxD,gFAAgF;oBAChF,IAAI,CAAA,GAAA,iSAAA,CAAA,cAAa,AAAD,EAAE,WAAW;wBACzB,OAAO,OAAO,mBAAmB,IAAI;oBACzC;oBACA,KAAK,IAAI,CAAC;oBACV,mBAAmB;gBACvB;gBACA,OAAO,OAAO;YAClB;4CACA,uDAAuD;QACvD;YACI;YACA;YACA;YACA;SACH;QACD,4EAA4E;QAC5E,8CAA8C;QAC9C,OAAO;YACH,MAAM;YACN;YACA;YACA,IAAI,QAAQ;gBACR,OAAO,IAAI,IAAI;YACnB;YACA,IAAI,SAAS;gBACT,OAAO,IAAI,KAAK;YACpB;YACA,IAAI,gBAAgB;gBAChB,OAAO,IAAI,YAAY;YAC3B;YACA,IAAI,aAAa;gBACb,OAAO,IAAI,SAAS;YACxB;QACJ;IACJ;AACJ,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,iBAAc,AAAD,EAAE,uOAAA,CAAA,UAAM,EAAE", "ignoreList": [0], "debugId": null}}]}