{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/systemd.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Systemd Units\", \"name\": \"systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"^\\\\s*(InaccessableDirectories|InaccessibleDirectories|ReadOnlyDirectories|ReadWriteDirectories|Capabilities|TableId|UseDomainName|IPv6AcceptRouterAdvertisements|SysVStartPriority|StartLimitInterval|RequiresOverridable|RequisiteOverridable|PropagateReloadTo|PropagateReloadFrom|OnFailureIsolate|BindTo)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#variables\" }, { \"include\": \"#quotedString\" }, { \"include\": \"#booleans\" }, { \"include\": \"#timeSpans\" }, { \"include\": \"#sizes\" }, { \"include\": \"#numbers\" }] }, { \"begin\": \"^\\\\s*(Environment)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"match\": `(?<=\\\\G|[\\\\s\"'])([A-Za-z0-9_]+)(=)(?=[^\\\\s\"'])` }, { \"include\": \"#variables\" }, { \"include\": \"#booleans\" }, { \"include\": \"#numbers\" }] }, { \"begin\": \"^\\\\s*(OnCalendar)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#variables\" }, { \"include\": \"#calendarShorthands\" }, { \"include\": \"#numbers\" }] }, { \"begin\": \"^\\\\s*(CapabilityBoundingSet|AmbientCapabilities|AddCapability|DropCapability)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#capabilities\" }] }, { \"begin\": \"^\\\\s*(Restart)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#variables\" }, { \"include\": \"#restartOptions\" }] }, { \"begin\": \"^\\\\s*(Type)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#variables\" }, { \"include\": \"#typeOptions\" }] }, { \"begin\": \"^\\\\s*(Exec(?:Start(?:Pre|Post)?|Reload|Stop(?:Post)?))\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#executablePrefixes\" }, { \"include\": \"#variables\" }, { \"include\": \"#quotedString\" }, { \"include\": \"#booleans\" }, { \"include\": \"#numbers\" }] }, { \"begin\": \"^\\\\s*([\\\\w\\\\-\\\\.]+)\\\\s*(=)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag\" }, \"2\": { \"name\": \"keyword.operator.assignment\" } }, \"end\": \"(?<!\\\\\\\\)\\\\n\", \"name\": \"meta.config-entry.systemd\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#variables\" }, { \"include\": \"#quotedString\" }, { \"include\": \"#booleans\" }, { \"include\": \"#timeSpans\" }, { \"include\": \"#sizes\" }, { \"include\": \"#numbers\" }] }, { \"include\": \"#sections\" }], \"repository\": { \"booleans\": { \"patterns\": [{ \"match\": \"\\\\b(?<![-\\\\/\\\\.])(true|false|on|off|yes|no)(?![-\\\\/\\\\.])\\\\b\", \"name\": \"constant.language\" }] }, \"calendarShorthands\": { \"patterns\": [{ \"match\": \"\\\\b(?:minute|hour|dai|month|week|quarter|semiannual)ly\\\\b\", \"name\": \"constant.language\" }] }, \"capabilities\": { \"patterns\": [{ \"match\": \"\\\\b(?:CAP_(?:AUDIT_CONTROL|AUDIT_READ|AUDIT_WRITE|BLOCK_SUSPEND|BPF|CHECKPOINT_RESTORE|CHOWN|DAC_OVERRIDE|DAC_READ_SEARCH|FOWNER|FSETID|IPC_LOCK|IPC_OWNER|KILL|LEASE|LINUX_IMMUTABLE|MAC_ADMIN|MAC_OVERRIDE|MKNOD|NET_ADMIN|NET_BIND_SERVICE|NET_BROADCAST|NET_RAW|PERFMON|SETFCAP|SETGID|SETPCAP|SETUID|SYS_ADMIN|SYS_BOOT|SYS_CHROOT|SYS_MODULE|SYS_NICE|SYS_PACCT|SYS_PTRACE|SYS_RAWIO|SYS_RESOURCE|SYS_TIME|SYS_TTY_CONFIG|SYSLOG|WAKE_ALARM))\\\\b\", \"name\": \"constant.other.systemd\" }] }, \"comments\": { \"patterns\": [{ \"match\": \"^\\\\s*[#;].*\\\\n\", \"name\": \"comment.line.number-sign\" }] }, \"executablePrefixes\": { \"patterns\": [{ \"match\": \"\\\\G([@\\\\-:]+(?:\\\\+|!!?)?|(?:\\\\+|!!?)[@\\\\-:]*)\", \"name\": \"keyword.operator.prefix.systemd\" }] }, \"numbers\": { \"patterns\": [{ \"match\": \"(?<=\\\\s|=)\\\\d+(?:\\\\.\\\\d+)?(?=[\\\\s:]|$)\", \"name\": \"constant.numeric\" }] }, \"quotedString\": { \"patterns\": [{ \"begin\": \"(?<=\\\\G|\\\\s)'\", \"end\": \"['\\\\n]\", \"name\": \"string.quoted.single\", \"patterns\": [{ \"match\": `\\\\\\\\(?:[abfnrtvs\\\\\\\\\"'\\\\n]|x[0-9A-Fa-f]{2}|[0-8]{3}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})`, \"name\": \"constant.character.escape\" }] }, { \"begin\": '(?<=\\\\G|\\\\s)\"', \"end\": '[\"\\\\n]', \"name\": \"string.quoted.double\", \"patterns\": [{ \"match\": `\\\\\\\\(?:[abfnrtvs\\\\\\\\\"'\\\\n]|x[0-9A-Fa-f]{2}|[0-8]{3}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})`, \"name\": \"constant.character.escape\" }] }] }, \"restartOptions\": { \"patterns\": [{ \"match\": \"\\\\b(no|always|on-(?:success|failure|abnormal|abort|watchdog))\\\\b\", \"name\": \"constant.language\" }] }, \"sections\": { \"patterns\": [{ \"match\": \"^\\\\s*\\\\[(Address|Automount|BFIFO|BareUDP|BatmanAdvanced|Bond|Bridge|BridgeFDB|BridgeMDB|BridgeVLAN|CAKE|CAN|Container|Content|ControlledDelay|Coredump|D-BUS Service|DHCP|DHCPPrefixDelegation|DHCPServer|DHCPServerStaticLease|DHCPv4|DHCPv6|DHCPv6PrefixDelegation|DeficitRoundRobinScheduler|DeficitRoundRobinSchedulerClass|Distribution|EnhancedTransmissionSelection|Exec|FairQueueing|FairQueueingControlledDelay|Files|FlowQueuePIE|FooOverUDP|GENEVE|GenericRandomEarlyDetection|HeavyHitterFilter|HierarchyTokenBucket|HierarchyTokenBucketClass|Home|IOCost|IPVLAN|IPVTAP|IPoIB|IPv6AcceptRA|IPv6AddressLabel|IPv6PREF64Prefix|IPv6Prefix|IPv6PrefixDelegation|IPv6RoutePrefix|IPv6SendRA|Image|Install|Journal|Kube|L2TP|L2TPSession|LLDP|Link|Login|MACVLAN|MACVTAP|MACsec|MACsecReceiveAssociation|MACsecReceiveChannel|MACsecTransmitAssociation|Manager|Match|Mount|Neighbor|NetDev|Network|NetworkEmulator|NextHop|OOM|Output|PFIFO|PFIFOFast|PFIFOHeadDrop|PIE|PStore|Packages|Partition|Path|Peer|Pod|QDisc|QuickFairQueueing|QuickFairQueueingClass|Remote|Resolve|Route|RoutingPolicyRule|SR-IOV|Scope|Service|Sleep|Socket|Source|StochasticFairBlue|StochasticFairnessQueueing|Swap|Tap|Target|Time|Timer|TokenBucketFilter|TrafficControlQueueingDiscipline|Transfer|TrivialLinkEqualizer|Tun|Tunnel|UKI|Unit|Upload|VLAN|VRF|VXCAN|VXLAN|Volume|WLAN|WireGuard|WireGuardPeer|Xfrm)\\\\]\", \"name\": \"entity.name.section\" }, { \"match\": \"\\\\s*\\\\[[\\\\w-]+\\\\]\", \"name\": \"entity.name.unknown-section\" }] }, \"sizes\": { \"patterns\": [{ \"match\": \"(?<=\\\\s|=)\\\\d+(?:\\\\.\\\\d+)?[KMGT](?=[\\\\s:]|$)\", \"name\": \"constant.numeric\" }, { \"match\": \"(?<==)infinity(?=[\\\\s:]|$)\", \"name\": \"constant.numeric\" }] }, \"timeSpans\": { \"patterns\": [{ \"match\": \"\\\\b(?:\\\\d+(?:[u\\u03BC]s(?:ec)?|ms(?:ec)?|s(?:ec|econds?)?|m(?:in|inutes?)?|h(?:r|ours?)?|d(?:ays?)?|w(?:eeks)?|M|months?|y(?:ears?)?)){1,}\\\\b\", \"name\": \"constant.numeric\" }] }, \"typeOptions\": { \"patterns\": [{ \"match\": \"\\\\b(?:simple|exec|forking|oneshot|dbus|notify(?:-reload)?|idle|unicast|local|broadcast|anycast|multicast|blackhole|unreachable|prohibit|throw|nat|xresolve|blackhole|unreachable|prohibit|ad-hoc|station|ap(?:-vlan)?|wds|monitor|mesh-point|p2p-(?:client|go|device)|ocb|nan)\\\\b\", \"name\": \"constant.language\" }] }, \"variables\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.systemd\" }, \"2\": { \"name\": \"variable.other\" } }, \"match\": \"(\\\\$)([A-Za-z0-9_]+)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.systemd\" }, \"2\": { \"name\": \"variable.other\" }, \"3\": { \"name\": \"punctuation.definition.variable.systemd\" } }, \"match\": \"(\\\\$\\\\{)([A-Za-z0-9_]+)(\\\\})\" }, { \"match\": \"%%\", \"name\": \"constant.other.placeholder\" }, { \"match\": \"%[aAbBCEfgGhHiIjJlLmMnNopPsStTuUvVwW]\\\\b\", \"name\": \"constant.other.placeholder\" }] } }, \"scopeName\": \"source.systemd\" });\nvar systemd = [\n  lang\n];\n\nexport { systemd as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAiB,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAA+T,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS,CAAC,8CAA8C,CAAC;gBAAC;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAA+F,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAgC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAwE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAgB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+D,QAAQ;gBAAoB;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,QAAQ;gBAAoB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0b,QAAQ;gBAAyB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,QAAQ;gBAA2B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiD,QAAQ;gBAAkC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0C,QAAQ;gBAAmB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,OAAO;oBAAU,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS,CAAC,oFAAoF,CAAC;4BAAE,QAAQ;wBAA4B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,OAAO;oBAAU,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS,CAAC,oFAAoF,CAAC;4BAAE,QAAQ;wBAA4B;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoE,QAAQ;gBAAoB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAk1C,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA8B;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAmB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiJ,QAAQ;gBAAmB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqR,QAAQ;gBAAoB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiB;oBAAE;oBAAG,SAAS;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAA6B;aAAE;QAAC;IAAE;IAAG,aAAa;AAAiB;AACx/P,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}