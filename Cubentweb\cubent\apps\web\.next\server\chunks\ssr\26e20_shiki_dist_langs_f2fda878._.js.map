{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/json.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON\", \"name\": \"json\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json\" } }, \"name\": \"meta.structure.array.json\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json\" }, \"number\": { \"match\": \"-?(?:0|[1-9]\\\\d*)(?:(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)?\", \"name\": \"constant.numeric.json\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json\" } }, \"name\": \"meta.structure.dictionary.json\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json\" } }, \"name\": \"meta.structure.dictionary.value.json\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json\" } }, \"name\": \"string.json support.type.property-name.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json\" } }, \"name\": \"string.quoted.double.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json\" });\nvar json = [\n  lang\n];\n\nexport { json as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAgD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA6B,QAAQ;QAAyB;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAAwB;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;oBAAuB,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;4BAAyB,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAAqD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAqD;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAA+C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAkD;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AACxiG,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/stylus.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Stylus\", \"fileTypes\": [\"styl\", \"stylus\", \"css.styl\", \"css.stylus\"], \"name\": \"stylus\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#at_rule\" }, { \"include\": \"#language_keywords\" }, { \"include\": \"#language_constants\" }, { \"include\": \"#variable_declaration\" }, { \"include\": \"#function\" }, { \"include\": \"#selector\" }, { \"include\": \"#declaration\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.property-list.begin.css\" }, \"2\": { \"name\": \"punctuation.section.property-list.end.css\" } }, \"match\": \"(\\\\{)(\\\\})\", \"name\": \"meta.brace.curly.css\" }, { \"match\": \"\\\\{|\\\\}\", \"name\": \"meta.brace.curly.css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#string\" }, { \"include\": \"#operator\" }], \"repository\": { \"at_rule\": { \"patterns\": [{ \"begin\": \"\\\\s*((@)(import|require))\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.import.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"end\": \"\\\\s*((?=;|$|\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.import.css\", \"patterns\": [{ \"include\": \"#string\" }] }, { \"begin\": \"\\\\s*((@)(extend[s]?)\\\\b)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.extend.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"end\": \"\\\\s*((?=;|$|\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.extend.css\", \"patterns\": [{ \"include\": \"#selector\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.fontface.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"match\": \"^\\\\s*((@)font-face)\\\\b\", \"name\": \"meta.at-rule.fontface.stylus\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.css.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"match\": \"^\\\\s*((@)css)\\\\b\", \"name\": \"meta.at-rule.css.stylus\" }, { \"begin\": \"\\\\s*((@)charset)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.charset.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"end\": \"\\\\s*((?=;|$|\\\\n))\", \"name\": \"meta.at-rule.charset.stylus\", \"patterns\": [{ \"include\": \"#string\" }] }, { \"begin\": \"\\\\s*((@)keyframes)\\\\b\\\\s+([a-zA-Z_-][a-zA-Z0-9_-]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.keyframes.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" }, \"3\": { \"name\": \"entity.name.function.keyframe.stylus\" } }, \"end\": \"\\\\s*((?=\\\\{|$|\\\\n))\", \"name\": \"meta.at-rule.keyframes.stylus\" }, { \"begin\": \"(?=(\\\\b(\\\\d+%|from\\\\b|to\\\\b)))\", \"end\": \"(?=(\\\\{|\\\\n))\", \"name\": \"meta.at-rule.keyframes.stylus\", \"patterns\": [{ \"match\": \"(\\\\b(\\\\d+%|from\\\\b|to\\\\b))\", \"name\": \"entity.other.attribute-name.stylus\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.media.stylus\" }, \"2\": { \"name\": \"punctuation.definition.keyword.stylus\" } }, \"match\": \"^\\\\s*((@)media)\\\\b\", \"name\": \"meta.at-rule.media.stylus\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(width|scan|resolution|orientation|monochrome|min-width|min-resolution|min-monochrome|min-height|min-device-width|min-device-height|min-device-aspect-ratio|min-color-index|min-color|min-aspect-ratio|max-width|max-resolution|max-monochrome|max-height|max-device-width|max-device-height|max-device-aspect-ratio|max-color-index|max-color|max-aspect-ratio|height|grid|device-width|device-height|device-aspect-ratio|color-index|color|aspect-ratio)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"support.type.property-name.media-feature.media.css\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(tv|tty|screen|projection|print|handheld|embossed|braille|aural|all)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"support.constant.media-type.media.css\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(portrait|landscape)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"support.constant.property-value.media-property.media.css\" }] }, \"char_escape\": { \"match\": \"\\\\\\\\(.)\", \"name\": \"constant.character.escape.stylus\" }, \"color\": { \"patterns\": [{ \"begin\": \"\\\\b(rgb|rgba|hsl|hsla)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.color.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.css\" } }, \"name\": \"meta.function.color.css\", \"patterns\": [{ \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#property_variable\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.css\" } }, \"match\": \"(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\\\\b\", \"name\": \"constant.other.color.rgb-value.css\" }, { \"comment\": \"http://www.w3.org/TR/CSS21/syndata.html#value-def-color\", \"match\": \"\\\\b(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)\\\\b\", \"name\": \"support.constant.color.w3c-standard-color-name.css\" }, { \"comment\": \"http://www.w3.org/TR/css3-color/#svg-color\", \"match\": \"\\\\b(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|turquoise|violet|wheat|whitesmoke|yellowgreen)\\\\b\", \"name\": \"support.constant.color.w3c-extended-color-name.css\" }] }, \"comment\": { \"patterns\": [{ \"include\": \"#comment_block\" }, { \"include\": \"#comment_line\" }] }, \"comment_block\": { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.css\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.css\" } }, \"name\": \"comment.block.css\" }, \"comment_line\": { \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.stylus\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.stylus\" } }, \"end\": \"(?=\\\\n)\", \"name\": \"comment.line.double-slash.stylus\" }] }, \"declaration\": { \"begin\": \"((?<=^)[^\\\\S\\\\n]+)|((?<=;)[^\\\\S\\\\n]*)|((?<=\\\\{)[^\\\\S\\\\n]*)\", \"end\": \"(?=\\\\n)|(;)|(?=\\\\})|(\\\\n)\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.property-list.css\", \"patterns\": [{ \"match\": \"(?<![\\\\w-])--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.css\" }, { \"include\": \"#language_keywords\" }, { \"include\": \"#language_constants\" }, { \"match\": \"(?:(?<=^)[^\\\\S\\\\n]+(\\\\n))\" }, { \"captures\": { \"1\": { \"name\": \"support.type.property-name.css\" }, \"2\": { \"name\": \"punctuation.separator.key-value.css\" }, \"3\": { \"name\": \"variable.section.css\" } }, \"match\": \"\\\\G\\\\s*(counter-reset|counter-increment)(?:(:)|[^\\\\S\\\\n])[^\\\\S\\\\n]*([a-zA-Z_-][a-zA-Z0-9_-]*)\", \"name\": \"meta.property.counter.css\" }, { \"begin\": \"\\\\G\\\\s*(filter)(?:(:)|[^\\\\S\\\\n])[^\\\\S\\\\n]*\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.property-name.css\" }, \"2\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"end\": \"(?=\\\\n|;|\\\\}|$)\", \"name\": \"meta.property.filter.css\", \"patterns\": [{ \"include\": \"#function\" }, { \"include\": \"#property_values\" }] }, { \"include\": \"#property\" }, { \"include\": \"#interpolation\" }, { \"include\": \"$self\" }] }, \"font_name\": { \"match\": \"(\\\\b(?i:arial|century|comic|courier|cursive|fantasy|futura|garamond|georgia|helvetica|impact|lucida|monospace|symbol|system|tahoma|times|trebuchet|utopia|verdana|webdings|sans-serif|serif)\\\\b)\", \"name\": \"support.constant.font-name.css\" }, \"function\": { \"begin\": \"(?=[a-zA-Z_-][a-zA-Z0-9_-]*\\\\()\", \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.css\" } }, \"patterns\": [{ \"begin\": \"(format|url|local)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.misc.css\", \"patterns\": [{ \"match\": \"(?<=\\\\()[^)\\\\s]*(?=\\\\))\", \"name\": \"string.css\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#operator\" }, { \"match\": \"\\\\s*\" }] }, { \"captures\": { \"1\": { \"name\": \"support.function.misc.counter.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" }, \"3\": { \"name\": \"variable.section.css\" } }, \"match\": \"(counter)(\\\\()([a-zA-Z_-][a-zA-Z0-9_-]*)(?=\\\\))\", \"name\": \"meta.function.misc.counter.css\" }, { \"begin\": \"(counters)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.counters.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.misc.counters.css\", \"patterns\": [{ \"match\": \"\\\\G[a-zA-Z_-][a-zA-Z0-9_-]*\", \"name\": \"variable.section.css\" }, { \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(attr)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.attr.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.misc.attr.css\", \"patterns\": [{ \"match\": \"\\\\G[a-zA-Z_-][a-zA-Z0-9_-]*\", \"name\": \"entity.other.attribute-name.attribute.css\" }, { \"match\": \"(?<=[a-zA-Z0-9_-])\\\\s*\\\\b(string|color|url|integer|number|length|em|ex|px|rem|vw|vh|vmin|vmax|mm|cm|in|pt|pc|angle|deg|grad|rad|time|s|ms|frequency|Hz|kHz|%)\\\\b\", \"name\": \"support.type.attr.css\" }, { \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(calc)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.calc.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.misc.calc.css\", \"patterns\": [{ \"include\": \"#property_values\" }] }, { \"begin\": \"(cubic-bezier)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.timing.cubic-bezier.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.timing.cubic-bezier.css\", \"patterns\": [{ \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(steps)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.timing.steps.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.timing.steps.css\", \"patterns\": [{ \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#numeric\" }, { \"match\": \"\\\\b(start|end)\\\\b\", \"name\": \"support.constant.timing.steps.direction.css\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(linear-gradient|radial-gradient|repeating-linear-gradient|repeating-radial-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.gradient.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.gradient.css\", \"patterns\": [{ \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#color\" }, { \"match\": \"\\\\b(to|bottom|right|left|top|circle|ellipse|center|closest-side|closest-corner|farthest-side|farthest-corner|at)\\\\b\", \"name\": \"support.constant.gradient.css\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(blur|brightness|contrast|grayscale|hue-rotate|invert|opacity|saturate|sepia)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.filter.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.filter.css\", \"patterns\": [{ \"include\": \"#numeric\" }, { \"include\": \"#property_variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(drop-shadow)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.filter.drop-shadow.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.filter.drop-shadow.css\", \"patterns\": [{ \"include\": \"#numeric\" }, { \"include\": \"#color\" }, { \"include\": \"#property_variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(matrix|matrix3d|perspective|rotate|rotate3d|rotate[Xx]|rotate[yY]|rotate[zZ]|scale|scale3d|scale[xX]|scale[yY]|scale[zZ]|skew|skew[xX]|skew[yY]|translate|translate3d|translate[xX]|translate[yY]|translate[zZ])(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.transform.css\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.transform.css\", \"patterns\": [{ \"include\": \"#numeric\" }, { \"include\": \"#property_variable\" }, { \"include\": \"#interpolation\" }] }, { \"match\": \"(url|local|format|counter|counters|attr|calc)(?=\\\\()\", \"name\": \"support.function.misc.css\" }, { \"match\": \"(cubic-bezier|steps)(?=\\\\()\", \"name\": \"support.function.timing.css\" }, { \"match\": \"(linear-gradient|radial-gradient|repeating-linear-gradient|repeating-radial-gradient)(?=\\\\()\", \"name\": \"support.function.gradient.css\" }, { \"match\": \"(blur|brightness|contrast|drop-shadow|grayscale|hue-rotate|invert|opacity|saturate|sepia)(?=\\\\()\", \"name\": \"support.function.filter.css\" }, { \"match\": \"(matrix|matrix3d|perspective|rotate|rotate3d|rotate[Xx]|rotate[yY]|rotate[zZ]|scale|scale3d|scale[xX]|scale[yY]|scale[zZ]|skew|skew[xX]|skew[yY]|translate|translate3d|translate[xX]|translate[yY]|translate[zZ])(?=\\\\()\", \"name\": \"support.function.transform.css\" }, { \"begin\": \"([a-zA-Z_-][a-zA-Z0-9_-]*)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.stylus\" }, \"2\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.function.stylus\", \"patterns\": [{ \"match\": \"--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.argument.stylus\" }, { \"match\": \"\\\\s*(,)\\\\s*\", \"name\": \"punctuation.separator.parameter.css\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#property_values\" }] }, { \"match\": \"\\\\(\", \"name\": \"punctuation.section.function.css\" }] }, \"interpolation\": { \"begin\": \"(?:(\\\\{)[^\\\\S\\\\n]*)(?=[^;=]*[^\\\\S\\\\n]*\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.curly\" } }, \"end\": \"(?:[^\\\\S\\\\n]*(\\\\}))|\\\\n|$\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.curly\" } }, \"name\": \"meta.interpolation.stylus\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#string\" }, { \"include\": \"#operator\" }] }, \"language_constants\": { \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.stylus\" }, \"language_keywords\": { \"patterns\": [{ \"match\": \"(\\\\b|\\\\s)(return|else|for|unless|if|else)\\\\b\", \"name\": \"keyword.control.stylus\" }, { \"match\": \"(\\\\b|\\\\s)(!important|in|is defined|is a)\\\\b\", \"name\": \"keyword.other.stylus\" }, { \"match\": \"\\\\barguments\\\\b\", \"name\": \"variable.language.stylus\" }] }, \"numeric\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.unit.css\" } }, \"match\": \"(?<!\\\\w|-)(?:(?:-|\\\\+)?(?:\\\\d+(?:\\\\.\\\\d+)?)|(?:\\\\.\\\\d+))((?:px|pt|ch|cm|mm|in|r?em|ex|pc|deg|g?rad|dpi|dpcm|dppx|fr|ms|s|turn|vh|vmax|vmin|vw)\\\\b|%)?\", \"name\": \"constant.numeric.css\" }] }, \"operator\": { \"patterns\": [{ \"match\": \"((?:\\\\?|:|!|~|\\\\+|(\\\\s-\\\\s)|(?:\\\\*)?\\\\*|\\\\/|%|(\\\\.)?\\\\.\\\\.|<|>|(?:=|:|\\\\?|\\\\+|-|\\\\*|\\\\/|%|<|>)?=|!=)|\\\\b(?:in|is(?:nt)?|(?<!:)not|or|and)\\\\b)\", \"name\": \"keyword.operator.stylus\" }, { \"include\": \"#char_escape\" }] }, \"property\": { \"begin\": \"(?:\\\\G\\\\s*(?:(-webkit-[-A-Za-z]+|-moz-[-A-Za-z]+|-o-[-A-Za-z]+|-ms-[-A-Za-z]+|-khtml-[-A-Za-z]+|zoom|z-index|y|x|wrap|word-wrap|word-spacing|word-break|word|width|widows|white-space-collapse|white-space|white|weight|volume|voice-volume|voice-stress|voice-rate|voice-pitch-range|voice-pitch|voice-family|voice-duration|voice-balance|voice|visibility|vertical-align|variant|user-select|up|unicode-bidi|unicode-range|unicode|trim|transition-timing-function|transition-property|transition-duration|transition-delay|transition|transform|touch-action|top-width|top-style|top-right-radius|top-left-radius|top-color|top|timing-function|text-wrap|text-transform|text-shadow|text-replace|text-rendering|text-overflow|text-outline|text-justify|text-indent|text-height|text-emphasis|text-decoration|text-align-last|text-align|text|target-position|target-new|target-name|target|table-layout|tab-size|style-type|style-position|style-image|style|string-set|stretch|stress|stacking-strategy|stacking-shift|stacking-ruby|stacking|src|speed|speech-rate|speech|speak-punctuation|speak-numeral|speak-header|speak|span|spacing|space-collapse|space|sizing|size-adjust|size|shadow|respond-to|rule-width|rule-style|rule-color|rule|ruby-span|ruby-position|ruby-overhang|ruby-align|ruby|rows|rotation-point|rotation|role|right-width|right-style|right-color|right|richness|rest-before|rest-after|rest|resource|resize|reset|replace|repeat|rendering-intent|rate|radius|quotes|punctuation-trim|punctuation|property|profile|presentation-level|presentation|position|pointer-events|point|play-state|play-during|play-count|pitch-range|pitch|phonemes|pause-before|pause-after|pause|page-policy|page-break-inside|page-break-before|page-break-after|page|padding-top|padding-right|padding-left|padding-bottom|padding|pack|overhang|overflow-y|overflow-x|overflow-style|overflow|outline-width|outline-style|outline-offset|outline-color|outline|orphans|origin|orientation|orient|ordinal-group|order|opacity|offset|numeral|new|nav-up|nav-right|nav-left|nav-index|nav-down|nav|name|move-to|model|mix-blend-mode|min-width|min-height|min|max-width|max-height|max|marquee-style|marquee-speed|marquee-play-count|marquee-direction|marquee|marks|mark-before|mark-after|mark|margin-top|margin-right|margin-left|margin-bottom|margin|mask-image|list-style-type|list-style-position|list-style-image|list-style|list|lines|line-stacking-strategy|line-stacking-shift|line-stacking-ruby|line-stacking|line-height|line-break|level|letter-spacing|length|left-width|left-style|left-color|left|label|justify-content|justify|iteration-count|inline-box-align|initial-value|initial-size|initial-before-align|initial-before-adjust|initial-after-align|initial-after-adjust|index|indent|increment|image-resolution|image-orientation|image|icon|hyphens|hyphenate-resource|hyphenate-lines|hyphenate-character|hyphenate-before|hyphenate-after|hyphenate|height|header|hanging-punctuation|gap|grid|grid-area|grid-auto-columns|grid-auto-flow|grid-auto-rows|grid-column|grid-column-end|grid-column-start|grid-row|grid-row-end|grid-row-start|grid-template|grid-template-areas|grid-template-columns|grid-template-rows|row-gap|gap|font-kerning|font-language-override|font-weight|font-variant-caps|font-variant|font-style|font-synthesis|font-stretch|font-size-adjust|font-size|font-family|font|float-offset|float|flex-wrap|flex-shrink|flex-grow|flex-group|flex-flow|flex-direction|flex-basis|flex|fit-position|fit|fill|filter|family|empty-cells|emphasis|elevation|duration|drop-initial-value|drop-initial-size|drop-initial-before-align|drop-initial-before-adjust|drop-initial-after-align|drop-initial-after-adjust|drop|down|dominant-baseline|display-role|display-model|display|direction|delay|decoration-break|decoration|cursor|cue-before|cue-after|cue|crop|counter-reset|counter-increment|counter|count|content|columns|column-width|column-span|column-rule-width|column-rule-style|column-rule-color|column-rule|column-gap|column-fill|column-count|column-break-before|column-break-after|column|color-profile|color|collapse|clip|clear|character|caption-side|break-inside|break-before|break-after|break|box-sizing|box-shadow|box-pack|box-orient|box-ordinal-group|box-lines|box-flex-group|box-flex|box-direction|box-decoration-break|box-align|box|bottom-width|bottom-style|bottom-right-radius|bottom-left-radius|bottom-color|bottom|border-width|border-top-width|border-top-style|border-top-right-radius|border-top-left-radius|border-top-color|border-top|border-style|border-spacing|border-right-width|border-right-style|border-right-color|border-right|border-radius|border-length|border-left-width|border-left-style|border-left-color|border-left|border-image|border-color|border-collapse|border-bottom-width|border-bottom-style|border-bottom-right-radius|border-bottom-left-radius|border-bottom-color|border-bottom|border|bookmark-target|bookmark-level|bookmark-label|bookmark|binding|bidi|before|baseline-shift|baseline|balance|background-blend-mode|background-size|background-repeat|background-position|background-origin|background-image|background-color|background-clip|background-break|background-attachment|background|azimuth|attachment|appearance|animation-timing-function|animation-play-state|animation-name|animation-iteration-count|animation-duration|animation-direction|animation-delay|animation-fill-mode|animation|alignment-baseline|alignment-adjust|alignment|align-self|align-last|align-items|align-content|align|after|adjust|will-change)|(writing-mode|text-anchor|stroke-width|stroke-opacity|stroke-miterlimit|stroke-linejoin|stroke-linecap|stroke-dashoffset|stroke-dasharray|stroke|stop-opacity|stop-color|shape-rendering|marker-start|marker-mid|marker-end|lighting-color|kerning|image-rendering|glyph-orientation-vertical|glyph-orientation-horizontal|flood-opacity|flood-color|fill-rule|fill-opacity|fill|enable-background|color-rendering|color-interpolation-filters|color-interpolation|clip-rule|clip-path)|([a-zA-Z_-][a-zA-Z0-9_-]*))(?!([^\\\\S\\\\n]*&)|([^\\\\S\\\\n]*\\\\{))(?=:|([^\\\\S\\\\n]+[^\\\\s])))\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.property-name.css\" }, \"2\": { \"name\": \"support.type.property-name.svg.css\" }, \"3\": { \"name\": \"support.function.mixin.stylus\" } }, \"end\": \"(;)|(?=\\\\n|\\\\}|$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"patterns\": [{ \"include\": \"#property_value\" }] }, \"property_value\": { \"begin\": \"\\\\G(?:(:)|(\\\\s))(\\\\s*)(?!&)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.css\" }, \"2\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"end\": \"(?=\\\\n|;|\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.property-value.css\", \"patterns\": [{ \"include\": \"#property_values\" }, { \"match\": \"[^\\\\n]+?\" }] }, \"property_values\": { \"patterns\": [{ \"include\": \"#function\" }, { \"include\": \"#comment\" }, { \"include\": \"#language_keywords\" }, { \"include\": \"#language_constants\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(wrap-reverse|wrap|whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|unicase|underline|ultra-expanded|ultra-condensed|transparent|transform|top|titling-caps|thin|thick|text-top|text-bottom|text|tb-rl|table-row-group|table-row|table-header-group|table-footer-group|table-column-group|table-column|table-cell|table|sw-resize|super|strict|stretch|step-start|step-end|static|square|space-between|space-around|space|solid|soft-light|small-caps|separate|semi-expanded|semi-condensed|se-resize|scroll|screen|saturation|s-resize|running|rtl|row-reverse|row-resize|row|round|right|ridge|reverse|repeat-y|repeat-x|repeat|relative|progressive|progress|pre-wrap|pre-line|pre|pointer|petite-caps|paused|pan-x|pan-left|pan-right|pan-y|pan-up|pan-down|padding-box|overline|overlay|outside|outset|optimizeSpeed|optimizeLegibility|opacity|oblique|nw-resize|nowrap|not-allowed|normal|none|no-repeat|no-drop|newspaper|ne-resize|n-resize|multiply|move|middle|medium|max-height|manipulation|main-size|luminosity|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|local|list-item|linear(?!-)|line-through|line-edge|line|lighter|lighten|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline-block|inline|inherit|infinite|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|hue|horizontal|hidden|help|hard-light|hand|groove|geometricPrecision|forwards|flex-start|flex-end|flex|fixed|extra-expanded|extra-condensed|expanded|exclusion|ellipsis|ease-out|ease-in-out|ease-in|ease|e-resize|double|dotted|distribute-space|distribute-letter|distribute-all-lines|distribute|disc|disabled|difference|default|decimal|dashed|darken|currentColor|crosshair|cover|content-box|contain|condensed|column-reverse|column|color-dodge|color-burn|color|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|border-box|bolder|bold|block|bidi-override|below|baseline|balance|backwards|auto|antialiased|always|alternate-reverse|alternate|all-small-caps|all-scroll|all-petite-caps|all|absolute)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"support.constant.property-value.css\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(start|sRGB|square|round|optimizeSpeed|optimizeQuality|nonzero|miter|middle|linearRGB|geometricPrecision |evenodd |end |crispEdges|butt|bevel)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"support.constant.property-value.svg.css\" }, { \"include\": \"#font_name\" }, { \"include\": \"#numeric\" }, { \"include\": \"#color\" }, { \"include\": \"#string\" }, { \"match\": \"!\\\\s*important\", \"name\": \"keyword.other.important.css\" }, { \"include\": \"#operator\" }, { \"include\": \"#stylus_keywords\" }, { \"include\": \"#property_variable\" }] }, \"property_variable\": { \"patterns\": [{ \"include\": \"#variable\" }, { \"match\": \"(?<!^)(\\\\@[a-zA-Z_-][a-zA-Z0-9_-]*)\", \"name\": \"variable.property.stylus\" }] }, \"selector\": { \"patterns\": [{ \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(a|abbr|acronym|address|area|article|aside|audio|b|base|bdi|bdo|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|data|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|keygen|label|legend|li|link|main|map|mark|math|menu|menuitem|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|rb|rp|rt|rtc|ruby|s|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|svg|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr|track|tt|u|ul|var|video|wbr)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"entity.name.tag.css\" }, { \"match\": \"(?:(?=\\\\w)(?<![\\\\w-]))(vkern|view|use|tspan|tref|title|textPath|text|symbol|switch|svg|style|stop|set|script|rect|radialGradient|polyline|polygon|pattern|path|mpath|missing-glyph|metadata|mask|marker|linearGradient|line|image|hkern|glyphRef|glyph|g|foreignObject|font-face-uri|font-face-src|font-face-name|font-face-format|font-face|font|filter|feTurbulence|feTile|feSpotLight|feSpecularLighting|fePointLight|feOffset|feMorphology|feMergeNode|feMerge|feImage|feGaussianBlur|feFuncR|feFuncG|feFuncB|feFuncA|feFlood|feDistantLight|feDisplacementMap|feDiffuseLighting|feConvolveMatrix|feComposite|feComponentTransfer|feColorMatrix|feBlend|ellipse|desc|defs|cursor|color-profile|clipPath|circle|animateTransform|animateMotion|animateColor|animate|altGlyphItem|altGlyphDef|altGlyph|a)(?:(?<=\\\\w)(?![\\\\w-]))\", \"name\": \"entity.name.tag.svg.css\" }, { \"match\": \"\\\\s*(\\\\,)\\\\s*\", \"name\": \"meta.selector.stylus\" }, { \"match\": \"\\\\*\", \"name\": \"meta.selector.stylus\" }, { \"captures\": { \"2\": { \"name\": \"entity.other.attribute-name.parent-selector-suffix.stylus\" } }, \"match\": \"\\\\s*(\\\\&)([a-zA-Z0-9_-]+)\\\\s*\", \"name\": \"meta.selector.stylus\" }, { \"match\": \"\\\\s*(\\\\&)\\\\s*\", \"name\": \"meta.selector.stylus\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(\\\\.)[a-zA-Z0-9_-]+\", \"name\": \"entity.other.attribute-name.class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(#)[a-zA-Z][a-zA-Z0-9_-]*\", \"name\": \"entity.other.attribute-name.id.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(:+)(after|before|content|first-letter|first-line|host|(-(moz|webkit|ms)-)?selection)\\\\b\", \"name\": \"entity.other.attribute-name.pseudo-element.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(:)((first|last)-child|(first|last|only)-of-type|empty|root|target|first|left|right)\\\\b\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(:)(checked|enabled|default|disabled|indeterminate|invalid|optional|required|valid)\\\\b\", \"name\": \"entity.other.attribute-name.pseudo-class.ui-state.css\" }, { \"begin\": \"((:)not)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.css\" } }, \"patterns\": [{ \"include\": \"#selector\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.css\" }, \"4\": { \"name\": \"constant.numeric.css\" }, \"5\": { \"name\": \"punctuation.section.function.css\" } }, \"match\": \"((:)nth-(?:(?:last-)?child|(?:last-)?of-type))(\\\\()(-?(?:\\\\d+n?|n)(?:\\\\+\\\\d+)?|even|odd)(\\\\))\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"puncutation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.css\" }, \"4\": { \"name\": \"constant.language.css\" }, \"5\": { \"name\": \"punctuation.section.function.css\" } }, \"match\": \"((:)dir)\\\\s*(?:(\\\\()(ltr|rtl)?(\\\\)))?\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"puncutation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.css\" }, \"4\": { \"name\": \"constant.language.css\" }, \"6\": { \"name\": \"punctuation.section.function.css\" } }, \"match\": \"((:)lang)\\\\s*(?:(\\\\()(\\\\w+(-\\\\w+)?)?(\\\\)))?\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(:)(active|hover|link|visited|focus)\\\\b\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(::)(shadow)\\\\b\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"entity.other.attribute-name.attribute.css\" }, \"3\": { \"name\": \"punctuation.separator.operator.css\" }, \"4\": { \"name\": \"string.unquoted.attribute-value.css\" }, \"5\": { \"name\": \"string.quoted.double.attribute-value.css\" }, \"6\": { \"name\": \"punctuation.definition.string.begin.css\" }, \"7\": { \"name\": \"punctuation.definition.string.end.css\" }, \"8\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": `(?i)(\\\\[)\\\\s*(-?[_a-z\\\\\\\\[^\\0-\\x7F]][_a-z0-9\\\\-\\\\\\\\[^\\0-\\x7F]]*)(?:\\\\s*([~|^$*]?=)\\\\s*(?:(-?[_a-z\\\\\\\\[^\\0-\\x7F]][_a-z0-9\\\\-\\\\\\\\[^\\0-\\x7F]]*)|((?>(['\"])(?:[^\\\\\\\\]|\\\\\\\\.)*?(\\\\6)))))?\\\\s*(\\\\])`, \"name\": \"meta.attribute-selector.css\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#variable\" }] }, \"string\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"match\": \"\\\\\\\\([a-fA-F0-9]{1,6}|.)\", \"name\": \"constant.character.escape.css\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.single.css\", \"patterns\": [{ \"match\": \"\\\\\\\\([a-fA-F0-9]{1,6}|.)\", \"name\": \"constant.character.escape.css\" }] }] }, \"variable\": { \"match\": \"(\\\\$[a-zA-Z_-][a-zA-Z0-9_-]*)\", \"name\": \"variable.stylus\" }, \"variable_declaration\": { \"begin\": \"^[^\\\\S\\\\n]*(\\\\$?[a-zA-Z_-][a-zA-Z0-9_-]*)[^\\\\S\\\\n]*(=|\\\\?=|:=)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.stylus\" }, \"2\": { \"name\": \"keyword.operator.stylus\" } }, \"end\": \"(\\\\n)|(;)|(?=\\\\})\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"patterns\": [{ \"include\": \"#property_values\" }] } }, \"scopeName\": \"source.stylus\", \"aliases\": [\"styl\"] });\nvar stylus = [\n  lang\n];\n\nexport { stylus as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,aAAa;QAAC;QAAQ;QAAU;QAAY;KAAa;IAAE,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAsB;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,SAAS;YAAc,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAW,QAAQ;QAAuB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;oBAA0B,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;oBAAoB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA2B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAqB,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAuB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAkC,OAAO;oBAAiB,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,SAAS;4BAA8B,QAAQ;wBAAqC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;oBAAsB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAA0e,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAoH,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAoE,QAAQ;gBAA2D;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAW,QAAQ;QAAmC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAyC,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;oBAA2D,SAAS;oBAAgH,QAAQ;gBAAqD;gBAAG;oBAAE,WAAW;oBAA8C,SAAS;oBAAs1C,QAAQ;gBAAqD;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;QAAoB;QAAG,gBAAgB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;gBAAmC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAA8D,OAAO;YAA6B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,SAAS;oBAA0G,QAAQ;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;gBAA4B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;oBAAiG,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAmB,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAoM,QAAQ;QAAiC;QAAG,YAAY;YAAE,SAAS;YAAmC,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAA2B,QAAQ;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;wBAAO;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;oBAAmD,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAA+B,QAAQ;wBAAuB;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,SAAS;4BAA+B,QAAQ;wBAA4C;wBAAG;4BAAE,SAAS;4BAAoK,QAAQ;wBAAwB;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAyC,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAqB,QAAQ;wBAA8C;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAuH,QAAQ;wBAAgC;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0N,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwD,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAgG,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAA4N,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS;4BAA+F,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA8C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,OAAO;YAA6B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAA2B,QAAQ;QAA2B;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAA+C,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAA2B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;oBAAyJ,QAAQ;gBAAuB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiJ,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA84L,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAqB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA+B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;gBAAW;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAA2kE,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAA8L,QAAQ;gBAA0C;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAA2B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAitB,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAqyB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,SAAS;oBAAiC,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAuB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAAuB,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA6B,QAAQ;gBAAqC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA4F,QAAQ;gBAAiD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA2F,QAAQ;gBAA+C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA0F,QAAQ;gBAAwD;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAgG;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAA8C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA2C,QAAQ;gBAA+C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAAmB,QAAQ;gBAA+C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS,CAAC,6LAA6L,CAAC;oBAAE,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAA4B,QAAQ;wBAAgC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAA4B,QAAQ;wBAAgC;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAiC,QAAQ;QAAkB;QAAG,wBAAwB;YAAE,SAAS;YAAkE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAqB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAiB,WAAW;QAAC;KAAO;AAAC;AAC92/B,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/sass.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Sass\", \"fileTypes\": [\"sass\"], \"foldingStartMarker\": \"/\\\\*|^#|^\\\\*|^\\\\b|*#?region|^\\\\.\", \"foldingStopMarker\": \"\\\\*/|*#?endregion|^\\\\s*$\", \"name\": \"sass\", \"patterns\": [{ \"begin\": \"^(\\\\s*)(/\\\\*)\", \"end\": \"(\\\\*/)|^(?!\\\\s\\\\1)\", \"name\": \"comment.block.sass\", \"patterns\": [{ \"include\": \"#comment-tag\" }, { \"include\": \"#comment-param\" }] }, { \"match\": \"^[\\\\t ]*/?//[\\\\t ]*[SRI][\\\\t ]*$\", \"name\": \"keyword.other.sass.formatter.action\" }, { \"begin\": \"^[\\\\t ]*//[\\\\t ]*(import)[\\\\t ]*(css-variables)[\\\\t ]*(from)\", \"captures\": { \"1\": { \"name\": \"keyword.control\" }, \"2\": { \"name\": \"variable\" }, \"3\": { \"name\": \"keyword.control\" } }, \"end\": \"$\\\\n?\", \"name\": \"comment.import.css.variables\", \"patterns\": [{ \"include\": \"#import-quotes\" }] }, { \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#curly-brackets\" }, { \"include\": \"#placeholder-selector\" }, { \"begin\": \"\\\\$[a-zA-Z0-9_-]+(?=:)\", \"captures\": { \"0\": { \"name\": \"variable.other.name\" } }, \"end\": \"$\\\\n?|(?=\\\\)\\\\s\\\\)|\\\\)\\\\n)\", \"name\": \"sass.script.maps\", \"patterns\": [{ \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#variable\" }, { \"include\": \"#rgb-value\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#comma\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"include\": \"#operator\" }, { \"include\": \"#reserved-words\" }, { \"include\": \"#parent-selector\" }, { \"include\": \"#property-value\" }, { \"include\": \"#semicolon\" }, { \"include\": \"#dotdotdot\" }] }, { \"include\": \"#variable-root\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#comma\" }, { \"include\": \"#semicolon\" }, { \"include\": \"#dotdotdot\" }, { \"begin\": \"@include|\\\\+(?!\\\\W|\\\\d)\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.sass\" } }, \"end\": \"(?=\\\\n|\\\\()\", \"name\": \"support.function.name.sass.library\" }, { \"begin\": \"^(@use)\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.sass.use\" } }, \"end\": \"(?=\\\\n)\", \"name\": \"sass.use\", \"patterns\": [{ \"match\": \"as|with\", \"name\": \"support.type.css.sass\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#variable-root\" }, { \"include\": \"#rgb-value\" }, { \"include\": \"#comma\" }, { \"include\": \"#parenthesis-open\" }, { \"include\": \"#parenthesis-close\" }, { \"include\": \"#colon\" }, { \"include\": \"#import-quotes\" }] }, { \"begin\": \"^@import(.*?)( as.*)?$\", \"captures\": { \"1\": { \"name\": \"constant.character.css.sass\" }, \"2\": { \"name\": \"invalid\" } }, \"end\": \"(?=\\\\n)\", \"name\": \"keyword.control.at-rule.use\" }, { \"begin\": \"@mixin|^[\\\\t ]*=|@function\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.sass\" } }, \"end\": \"$\\\\n?|(?=\\\\()\", \"name\": \"support.function.name.sass\", \"patterns\": [{ \"match\": \"[\\\\w-]+\", \"name\": \"entity.name.function\" }] }, { \"begin\": \"@\", \"end\": \"$\\\\n?|\\\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\\\s|,))\", \"name\": \"keyword.control.at-rule.css.sass\" }, { \"begin\": \"(?<!-|\\\\()\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|slot)\\\\b(?!-|\\\\)|:\\\\s)|&\", \"end\": \"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\.|\\\\#|\\\\[|>|-|_)\", \"name\": \"entity.name.tag.css.sass.symbol\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"#\", \"end\": \"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\.|\\\\[|>)\", \"name\": \"entity.other.attribute-name.id.css.sass\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\.|(?<=&)(-|_)\", \"end\": \"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\[|>)\", \"name\": \"entity.other.attribute-name.class.css.sass\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"name\": \"entity.other.attribute-selector.sass\", \"patterns\": [{ \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"match\": \"\\\\^|\\\\$|\\\\*|~\", \"name\": \"keyword.other.regex.sass\" }] }, { \"match\": \"^((?<=\\\\]|\\\\)|not\\\\(|\\\\*|>|>\\\\s)|\\n*):[a-z:-]+|(::|:-)[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.sass\" }, { \"include\": \"#module\" }, { \"match\": \"[\\\\w-]*\\\\(\", \"name\": \"entity.name.function\" }, { \"match\": \"\\\\)\", \"name\": \"entity.name.function.close\" }, { \"begin\": \":\", \"end\": \"$\\\\n?|(?=\\\\s\\\\(|and\\\\(|\\\\),)\", \"name\": \"meta.property-list.css.sass.prop\", \"patterns\": [{ \"match\": \"(?<=:)[a-z-]+\\\\s\", \"name\": \"support.type.property-name.css.sass.prop.name\" }, { \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#curly-brackets\" }, { \"include\": \"#variable\" }, { \"include\": \"#rgb-value\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#module\" }, { \"match\": \"--.+?(?=\\\\))\", \"name\": \"variable.css\" }, { \"match\": \"[\\\\w-]*\\\\(\", \"name\": \"entity.name.function\" }, { \"match\": \"\\\\)\", \"name\": \"entity.name.function.close\" }, { \"include\": \"#flag\" }, { \"include\": \"#comma\" }, { \"include\": \"#semicolon\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"include\": \"#operator\" }, { \"include\": \"#parent-selector\" }, { \"include\": \"#property-value\" }] }, { \"include\": \"#rgb-value\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"begin\": \"(?<=})(?!\\\\n|\\\\(|\\\\)|[a-zA-Z0-9_-]+:)\", \"end\": \"\\\\s|(?=,|\\\\.|\\\\[|\\\\)|\\\\n)\", \"name\": \"entity.name.tag.css.sass\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"include\": \"#operator\" }, { \"match\": \"[a-z-]+((?=:|#{))\", \"name\": \"support.type.property-name.css.sass.prop.name\" }, { \"include\": \"#reserved-words\" }, { \"include\": \"#property-value\" }], \"repository\": { \"colon\": { \"match\": \":\", \"name\": \"meta.property-list.css.sass.colon\" }, \"comma\": { \"match\": \"\\\\band\\\\b|\\\\bor\\\\b|,\", \"name\": \"comment.punctuation.comma.sass\" }, \"comment-param\": { \"match\": \"\\\\@(\\\\w+)\", \"name\": \"storage.type.class.jsdoc\" }, \"comment-tag\": { \"begin\": \"(?<={{)\", \"end\": \"(?=}})\", \"name\": \"comment.tag.sass\" }, \"curly-brackets\": { \"match\": \"{|}\", \"name\": \"invalid\" }, \"dotdotdot\": { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"variable.other\" }, \"double-quoted\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.css.sass\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"double-slash\": { \"begin\": \"//\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.sass\", \"patterns\": [{ \"include\": \"#comment-tag\" }] }, \"flag\": { \"match\": \"!(important|default|optional|global)\", \"name\": \"keyword.other.important.css.sass\" }, \"function\": { \"match\": \"(?<=[\\\\s|(|,|:])(?!url|format|attr)[a-zA-Z0-9_-][\\\\w-]*(?=\\\\()\", \"name\": \"support.function.name.sass\" }, \"function-content\": { \"begin\": \"(?<=url\\\\(|format\\\\(|attr\\\\()\", \"end\": \".(?=\\\\))\", \"name\": \"string.quoted.double.css.sass\" }, \"import-quotes\": { \"match\": `[\"']?\\\\.{0,2}[\\\\w/]+[\"']?`, \"name\": \"constant.character.css.sass\" }, \"interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.sass\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }, { \"include\": \"#comma\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }] }, \"module\": { \"captures\": { \"1\": { \"name\": \"constant.character.module.name\" }, \"2\": { \"name\": \"constant.numeric.module.dot\" } }, \"match\": \"([\\\\w-]+?)(\\\\.)\", \"name\": \"constant.character.module\" }, \"numeric\": { \"match\": \"(-|\\\\.)?\\\\d+(\\\\.\\\\d+)?\", \"name\": \"constant.numeric.css.sass\" }, \"operator\": { \"match\": \"\\\\+|\\\\s-\\\\s|\\\\s-(?=\\\\$)|(?<=\\\\()-(?=\\\\$)|\\\\s-(?=\\\\()|\\\\*|/|%|=|!|<|>|~\", \"name\": \"keyword.operator.sass\" }, \"parent-selector\": { \"match\": \"&\", \"name\": \"entity.name.tag.css.sass\" }, \"parenthesis-close\": { \"match\": \"\\\\)\", \"name\": \"entity.name.function.parenthesis.close\" }, \"parenthesis-open\": { \"match\": \"\\\\(\", \"name\": \"entity.name.function.parenthesis.open\" }, \"placeholder-selector\": { \"begin\": \"(?<!\\\\d)%(?!\\\\d)\", \"end\": \"$\\\\n?|\\\\s\", \"name\": \"entity.other.inherited-class.placeholder-selector.css.sass\" }, \"property-value\": { \"match\": \"[a-zA-Z0-9_-]+\", \"name\": \"meta.property-value.css.sass support.constant.property-value.css.sass\" }, \"pseudo-class\": { \"match\": \":[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.sass\" }, \"quoted-interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.sass\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }, { \"include\": \"#comma\" }] }, \"reserved-words\": { \"match\": \"\\\\b(false|from|in|not|null|through|to|true)\\\\b\", \"name\": \"support.type.property-name.css.sass\" }, \"rgb-value\": { \"match\": \"(#)([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\b\", \"name\": \"constant.language.color.rgb-value.css.sass\" }, \"semicolon\": { \"match\": \";\", \"name\": \"invalid\" }, \"single-quoted\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.css.sass\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"unit\": { \"match\": \"(?<=[\\\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|fr|%)\", \"name\": \"keyword.control.unit.css.sass\" }, \"variable\": { \"match\": \"\\\\$[a-zA-Z0-9_-]+\", \"name\": \"variable.other.value\" }, \"variable-root\": { \"match\": \"\\\\$[a-zA-Z0-9_-]+\", \"name\": \"variable.other.root\" } }, \"scopeName\": \"source.sass\" });\nvar sass = [\n  lang\n];\n\nexport { sass as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;KAAO;IAAE,sBAAsB;IAAoC,qBAAqB;IAA4B,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAiB,OAAO;YAAsB,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAoC,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAgE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkB;gBAAG,KAAK;oBAAE,QAAQ;gBAAW;gBAAG,KAAK;oBAAE,QAAQ;gBAAkB;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,SAAS;YAA0B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAA8B,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,SAAS;YAA2B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAe,QAAQ;QAAqC;QAAG;YAAE,SAAS;YAAW,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAW,QAAQ;YAAY,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA0B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAU;YAAE;YAAG,OAAO;YAAW,QAAQ;QAA8B;QAAG;YAAE,SAAS;YAA8B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAiB,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAuB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAK,OAAO;YAAyG,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAwsB,OAAO;YAA6C,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAK,OAAO;YAAqC,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmB,OAAO;YAAiC,QAAQ;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAAwC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA2B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAkE,QAAQ;QAAoD;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,SAAS;YAAc,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAO,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAAK,OAAO;YAAgC,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAgD;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,SAAS;YAAyC,OAAO;YAA6B,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAgD;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAkB;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAK,QAAQ;QAAoC;QAAG,SAAS;YAAE,SAAS;YAAwB,QAAQ;QAAiC;QAAG,iBAAiB;YAAE,SAAS;YAAa,QAAQ;QAA2B;QAAG,eAAe;YAAE,SAAS;YAAW,OAAO;YAAU,QAAQ;QAAmB;QAAG,kBAAkB;YAAE,SAAS;YAAO,QAAQ;QAAU;QAAG,aAAa;YAAE,SAAS;YAAa,QAAQ;QAAiB;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAM,OAAO;YAAS,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAwC,QAAQ;QAAmC;QAAG,YAAY;YAAE,SAAS;YAAkE,QAAQ;QAA6B;QAAG,oBAAoB;YAAE,SAAS;YAAiC,OAAO;YAAY,QAAQ;QAAgC;QAAG,iBAAiB;YAAE,SAAS,CAAC,yBAAyB,CAAC;YAAE,QAAQ;QAA8B;QAAG,iBAAiB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,SAAS;YAAmB,QAAQ;QAA4B;QAAG,WAAW;YAAE,SAAS;YAA0B,QAAQ;QAA4B;QAAG,YAAY;YAAE,SAAS;YAA0E,QAAQ;QAAwB;QAAG,mBAAmB;YAAE,SAAS;YAAK,QAAQ;QAA2B;QAAG,qBAAqB;YAAE,SAAS;YAAO,QAAQ;QAAyC;QAAG,oBAAoB;YAAE,SAAS;YAAO,QAAQ;QAAwC;QAAG,wBAAwB;YAAE,SAAS;YAAoB,OAAO;YAAa,QAAQ;QAA6D;QAAG,kBAAkB;YAAE,SAAS;YAAkB,QAAQ;QAAwE;QAAG,gBAAgB;YAAE,SAAS;YAAa,QAAQ;QAAoD;QAAG,wBAAwB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAkD,QAAQ;QAAsC;QAAG,aAAa;YAAE,SAAS;YAA0D,QAAQ;QAA6C;QAAG,aAAa;YAAE,SAAS;YAAK,QAAQ;QAAU;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAiH,QAAQ;QAAgC;QAAG,YAAY;YAAE,SAAS;YAAqB,QAAQ;QAAuB;QAAG,iBAAiB;YAAE,SAAS;YAAqB,QAAQ;QAAsB;IAAE;IAAG,aAAa;AAAc;AACnuT,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/css.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"CSS\", \"name\": \"css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#combinators\" }, { \"include\": \"#selector\" }, { \"include\": \"#at-rules\" }, { \"include\": \"#rule-list\" }], \"repository\": { \"at-rules\": { \"patterns\": [{ \"begin\": \"\\\\A(?:\\\\xEF\\\\xBB\\\\xBF)?(?i:(?=\\\\s*@charset\\\\b))\", \"end\": \";|(?=$)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.charset.css\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.illegal.not-lowercase.charset.css\" }, \"2\": { \"name\": \"invalid.illegal.leading-whitespace.charset.css\" }, \"3\": { \"name\": \"invalid.illegal.no-whitespace.charset.css\" }, \"4\": { \"name\": \"invalid.illegal.whitespace.charset.css\" }, \"5\": { \"name\": \"invalid.illegal.not-double-quoted.charset.css\" }, \"6\": { \"name\": \"invalid.illegal.unclosed-string.charset.css\" }, \"7\": { \"name\": \"invalid.illegal.unexpected-characters.charset.css\" } }, \"match\": '\\\\G((?!@charset)@\\\\w+)|\\\\G(\\\\s+)|(@charset\\\\S[^;]*)|(?<=@charset)(\\\\x20{2,}|\\\\t+)|(?<=@charset\\\\x20)([^\";]+)|(\"[^\"]+$)|(?<=\")([^;]+)' }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.charset.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"match\": \"((@)charset)(?=\\\\s)\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"|$', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"begin\": '(?:\\\\G|^)(?=(?:[^\"])+$)', \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\" }] }] }, { \"begin\": `(?i)((@)import)(?:\\\\s+|$|(?=['\"]|/\\\\*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.import.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.import.css\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*(?=/\\\\*)\", \"end\": \"(?<=\\\\*/)\\\\s*\", \"patterns\": [{ \"include\": \"#comment-block\" }] }, { \"include\": \"#string\" }, { \"include\": \"#url\" }, { \"include\": \"#media-query-list\" }] }, { \"begin\": \"(?i)((@)font-face)(?=\\\\s*|{|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.font-face.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?!\\\\G)\", \"name\": \"meta.at-rule.font-face.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#rule-list\" }] }, { \"begin\": \"(?i)(@)page(?=[\\\\s:{]|/\\\\*|$)\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.page.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*($|[:{;]))\", \"name\": \"meta.at-rule.page.css\", \"patterns\": [{ \"include\": \"#rule-list\" }] }, { \"begin\": \"(?i)(?=@media(\\\\s|\\\\(|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)media\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.media.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.media.header.css\", \"patterns\": [{ \"include\": \"#media-query-list\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.media.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.media.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.media.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)(?=@counter-style([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)counter-style\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.counter-style.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.at-rule.counter-style.header.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.parameter.style-name.css\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.counter-style.body.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#rule-list-innards\" }] }] }, { \"begin\": `(?i)(?=@document([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)document\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.document.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.document.header.css\", \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(url-prefix|domain|regexp)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.document-rule.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.document-rule.css\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"match\": `[^'\")\\\\s]+`, \"name\": \"variable.parameter.document-rule.css\" }] }, { \"include\": \"#url\" }, { \"include\": \"#commas\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.document.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.document.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.document.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)(?=@(?:-(?:webkit|moz|o|ms)-)?keyframes([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)(?:-(?:webkit|moz|o|ms)-)?keyframes\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.keyframes.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.at-rule.keyframes.header.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.parameter.keyframe-list.css\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.keyframes.body.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.keyframe-offset.css\" }, \"2\": { \"name\": \"entity.other.keyframe-offset.percentage.css\" } }, \"match\": \"(?i)(?<![\\\\w-])(from|to)(?![\\\\w-])|([-+]?(?:\\\\d+(?:\\\\.\\\\d+)?|\\\\.\\\\d+)%)\" }, { \"include\": \"#rule-list\" }] }] }, { \"begin\": \"(?i)(?=@supports(\\\\s|\\\\(|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)|(?=;)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)supports\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.supports.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.supports.header.css\", \"patterns\": [{ \"include\": \"#feature-query-operators\" }, { \"include\": \"#feature-query\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.supports.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.supports.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.supports.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)((@)(-(ms|o)-)?viewport)(?=[\\\\s'\"{;]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.viewport.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[@{;])\", \"name\": \"meta.at-rule.viewport.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": `(?i)((@)font-feature-values)(?=[\\\\s'\"{;]|/\\\\*|$)\\\\s*`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.font-feature-values.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"contentName\": \"variable.parameter.font-name.css\", \"end\": \"(?=\\\\s*[@{;])\", \"name\": \"meta.at-rule.font-features.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"include\": \"#font-features\" }, { \"begin\": `(?i)((@)namespace)(?=[\\\\s'\";]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.namespace.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";|(?=[@{])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.namespace.css\", \"patterns\": [{ \"include\": \"#url\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#comment-block\" }] }, \"2\": { \"name\": \"entity.name.function.namespace-prefix.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?i)(?:\\\\G|^|(?<=\\\\s))(?=(?<=\\\\s|^)(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])|\\\\s*/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)(.*?)((?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*)\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?i)(?=@[\\\\w-]+[^;]+;s*$)\", \"end\": \"(?<=;)(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)[\\\\w-]+\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.header.css\" }] }, { \"begin\": \"(?i)(?=@[\\\\w-]+(\\\\s|\\\\(|{|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)[\\\\w-]+\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.header.css\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }] }, \"color-keywords\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-standard-color-name.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-extended-color-name.css\" }, { \"match\": \"(?i)(?<![\\\\w-])currentColor(?![\\\\w-])\", \"name\": \"support.constant.color.current.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText)(?![\\\\w-])\", \"name\": \"invalid.deprecated.color.system.css\" }] }, \"combinators\": { \"patterns\": [{ \"match\": \"/deep/|>>>\", \"name\": \"invalid.deprecated.combinator.css\" }, { \"match\": \">>|>|\\\\+|~\", \"name\": \"keyword.operator.combinator.css\" }] }, \"commas\": { \"match\": \",\", \"name\": \"punctuation.separator.list.comma.css\" }, \"comment-block\": { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.css\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.css\" } }, \"name\": \"comment.block.css\" }, \"escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[0-9a-fA-F]{1,6}\", \"name\": \"constant.character.escape.codepoint.css\" }, { \"begin\": \"\\\\\\\\$\\\\s*\", \"end\": \"^(?<!\\\\G)\", \"name\": \"constant.character.escape.newline.css\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.css\" }] }, \"feature-query\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.condition.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.condition.end.bracket.round.css\" } }, \"name\": \"meta.feature-query.css\", \"patterns\": [{ \"include\": \"#feature-query-operators\" }, { \"include\": \"#feature-query\" }] }, \"feature-query-operators\": { \"patterns\": [{ \"match\": \"(?i)(?<=[\\\\s()]|^|\\\\*/)(and|not|or)(?=[\\\\s()]|/\\\\*|$)\", \"name\": \"keyword.operator.logical.feature.$1.css\" }, { \"include\": \"#rule-list-innards\" }] }, \"font-features\": { \"begin\": `(?i)((@)(annotation|character-variant|ornaments|styleset|stylistic|swash))(?=[\\\\s@'\"{;]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.${3:/downcase}.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?<=})\", \"name\": \"meta.at-rule.${3:/downcase}.css\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.property-list.font-feature.css\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.font-feature.css\" }, { \"include\": \"#rule-list-innards\" }] }] }, \"functional-pseudo-classes\": { \"patterns\": [{ \"begin\": \"(?i)((:)dir)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"match\": \"(?i)(?<![\\\\w-])(ltr|rtl)(?![\\\\w-])\", \"name\": \"support.constant.text-direction.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)((:)lang)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"match\": \"(?<=[(,\\\\s])[a-zA-Z]+(-[a-zA-Z0-9]*|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?=[),\\\\s])\", \"name\": \"support.constant.language-range.css\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"include\": \"#escapes\" }, { \"match\": '(?<=[\"\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=[\"\\\\s])', \"name\": \"support.constant.language-range.css\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.single.css\", \"patterns\": [{ \"include\": \"#escapes\" }, { \"match\": \"(?<=['\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=['\\\\s])\", \"name\": \"support.constant.language-range.css\" }] }, { \"include\": \"#commas\" }] }, { \"begin\": \"(?i)((:)(?:not|has|matches|where|is))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#selector-innards\" }] }, { \"begin\": \"(?i)((:)nth-(?:last-)?(?:child|of-type))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"match\": \"(?i)[+-]?(\\\\d+n?|n)(\\\\s*[+-]\\\\s*\\\\d+)?\", \"name\": \"constant.numeric.css\" }, { \"match\": \"(?i)even|odd\", \"name\": \"support.constant.parity.css\" }] }] }, \"functions\": { \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(calc)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.calc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.calc.css\", \"patterns\": [{ \"match\": \"[*/]|(?<=\\\\s|^)[-+](?=\\\\s|$)\", \"name\": \"keyword.operator.arithmetic.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(rgba?|rgb|hsla?|hsl|hwb|lab|oklab|lch|oklch|color)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.color.css\", \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])((?:-webkit-|-moz-|-o-)?(?:repeating-)?(?:linear|radial|conic)-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.gradient.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(from|to|at|in|hue)(?![\\\\w-])\", \"name\": \"keyword.operator.gradient.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(-webkit-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.gradient.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.invalid.deprecated.gradient.css\", \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(from|to|color-stop)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(annotation|attr|blur|brightness|character-variant|clamp|contrast|counters?|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate|color-mix|image-set|invert|local|max|min|minmax|opacity|ornaments|repeat|saturate|sepia|styleset|stylistic|swash|symbols|cos|sin|tan|acos|asin|atan|atan2|hypot|sqrt|pow|log|exp|abs|sign)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.misc.css\", \"patterns\": [{ \"match\": `(?i)(?<=[,\\\\s\"]|\\\\*/|^)\\\\d+x(?=[\\\\s,\"')]|/\\\\*|$)`, \"name\": \"constant.numeric.other.density.css\" }, { \"include\": \"#property-values\" }, { \"match\": `[^'\"),\\\\s]+`, \"name\": \"variable.parameter.misc.css\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(circle|ellipse|inset|polygon|rect)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.shape.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.shape.css\", \"patterns\": [{ \"match\": \"(?i)(?<=\\\\s|^|\\\\*/)(at|round)(?=\\\\s|/\\\\*|$)\", \"name\": \"keyword.operator.shape.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(cubic-bezier|steps)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.timing-function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.timing-function.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(start|end)(?=\\\\s*\\\\)|$)\", \"name\": \"support.constant.step-direction.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])((?:translate|scale|rotate)(?:[XYZ]|3D)?|matrix(?:3D)?|skew[XY]?|perspective)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.transform.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"include\": \"#url\" }, { \"begin\": \"(?i)(?<![\\\\w-])(var)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.variable.css\", \"patterns\": [{ \"match\": \"--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.argument.css\" }, { \"include\": \"#property-values\" }] }] }, \"media-feature-keywords\": { \"match\": \"(?i)(?<=^|\\\\s|:|\\\\*/)(?:portrait|landscape|progressive|interlace|fullscreen|standalone|minimal-ui|browser|hover)(?=\\\\s|\\\\)|$)\", \"name\": \"support.constant.property-value.css\" }, \"media-features\": { \"captures\": { \"1\": { \"name\": \"support.type.property-name.media.css\" }, \"2\": { \"name\": \"support.type.property-name.media.css\" }, \"3\": { \"name\": \"support.type.vendored.property-name.media.css\" } }, \"match\": \"(?i)(?<=^|\\\\s|\\\\(|\\\\*/)(?:((?:min-|max-)?(?:height|width|aspect-ratio|color|color-index|monochrome|resolution)|grid|scan|orientation|display-mode|hover)|((?:min-|max-)?device-(?:height|width|aspect-ratio))|((?:[-_](?:webkit|apple|khtml|epub|moz|ms|o|xv|ah|rim|atsc|hp|tc|wap|ro)|(?:mso|prince))-[\\\\w-]+(?=\\\\s*(?:/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)?\\\\s*[:)])))(?=\\\\s|$|[><:=]|\\\\)|/\\\\*)\" }, \"media-query\": { \"begin\": \"\\\\G\", \"end\": \"(?=\\\\s*[{;])\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#media-types\" }, { \"match\": \"(?i)(?<=\\\\s|^|,|\\\\*/)(only|not)(?=\\\\s|{|/\\\\*|$)\", \"name\": \"keyword.operator.logical.$1.media.css\" }, { \"match\": \"(?i)(?<=\\\\s|^|\\\\*/|\\\\))and(?=\\\\s|/\\\\*|$)\", \"name\": \"keyword.operator.logical.and.media.css\" }, { \"match\": \",(?:(?:\\\\s*,)+|(?=\\\\s*[;){]))\", \"name\": \"invalid.illegal.comma.css\" }, { \"include\": \"#commas\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#media-features\" }, { \"include\": \"#media-feature-keywords\" }, { \"match\": \":\", \"name\": \"punctuation.separator.key-value.css\" }, { \"match\": \">=|<=|=|<|>\", \"name\": \"keyword.operator.comparison.css\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.css\" }, \"2\": { \"name\": \"keyword.operator.arithmetic.css\" }, \"3\": { \"name\": \"constant.numeric.css\" } }, \"match\": \"(\\\\d+)\\\\s*(/)\\\\s*(\\\\d+)\", \"name\": \"meta.ratio.css\" }, { \"include\": \"#numeric-values\" }, { \"include\": \"#comment-block\" }] }] }, \"media-query-list\": { \"begin\": \"(?=\\\\s*[^{;])\", \"end\": \"(?=\\\\s*[{;])\", \"patterns\": [{ \"include\": \"#media-query\" }] }, \"media-types\": { \"captures\": { \"1\": { \"name\": \"support.constant.media.css\" }, \"2\": { \"name\": \"invalid.deprecated.constant.media.css\" } }, \"match\": \"(?i)(?<=^|\\\\s|,|\\\\*/)(?:(all|print|screen|speech)|(aural|braille|embossed|handheld|projection|tty|tv))(?=$|[{,\\\\s;]|/\\\\*)\" }, \"numeric-values\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.css\" } }, \"match\": \"(#)(?:[0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\b\", \"name\": \"constant.other.color.rgb-value.hex.css\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.unit.percentage.css\" }, \"2\": { \"name\": \"keyword.other.unit.${2:/downcase}.css\" } }, \"match\": \"(?i)(?<![\\\\w-])[-+]?(?:\\\\d+(?:\\\\.\\\\d+)?|\\\\.\\\\d+)(?:(?<=\\\\d)E[-+]?\\\\d+)?(?:(%)|(deg|grad|rad|turn|Hz|kHz|ch|cm|em|ex|fr|in|mm|mozmm|pc|pt|px|q|rem|rch|rex|rlh|ic|ric|rcap|vh|vw|vb|vi|svh|svw|svb|svi|dvh|dvw|dvb|dvi|lvh|lvw|lvb|lvi|vmax|vmin|cqw|cqi|cqh|cqb|cqmin|cqmax|dpi|dpcm|dppx|s|ms)\\\\b)?\", \"name\": \"constant.numeric.css\" }] }, \"property-keywords\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(above|absolute|active|add|additive|after-edge|alias|all|all-petite-caps|all-scroll|all-small-caps|alpha|alphabetic|alternate|alternate-reverse|always|antialiased|auto|auto-fill|auto-fit|auto-pos|available|avoid|avoid-column|avoid-page|avoid-region|backwards|balance|baseline|before-edge|below|bevel|bidi-override|blink|block|block-axis|block-start|block-end|bold|bolder|border|border-box|both|bottom|bottom-outside|break-all|break-word|bullets|butt|capitalize|caption|cell|center|central|char|circle|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color|color-burn|color-dodge|column|column-reverse|common-ligatures|compact|condensed|contain|content|content-box|contents|context-menu|contextual|copy|cover|crisp-edges|crispEdges|crosshair|cyclic|dark|darken|dashed|decimal|default|dense|diagonal-fractions|difference|digits|disabled|disc|discretionary-ligatures|distribute|distribute-all-lines|distribute-letter|distribute-space|dot|dotted|double|double-circle|downleft|downright|e-resize|each-line|ease|ease-in|ease-in-out|ease-out|economy|ellipse|ellipsis|embed|end|evenodd|ew-resize|exact|exclude|exclusion|expanded|extends|extra-condensed|extra-expanded|fallback|farthest-corner|farthest-side|fill|fill-available|fill-box|filled|fit-content|fixed|flat|flex|flex-end|flex-start|flip|flow-root|forwards|freeze|from-image|full-width|geometricPrecision|georgian|grab|grabbing|grayscale|grid|groove|hand|hanging|hard-light|help|hidden|hide|historical-forms|historical-ligatures|horizontal|horizontal-tb|hue|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|ideographic|inactive|infinite|inherit|initial|inline|inline-axis|inline-block|inline-end|inline-flex|inline-grid|inline-list-item|inline-start|inline-table|inset|inside|inter-character|inter-ideograph|inter-word|intersect|invert|isolate|isolate-override|italic|jis04|jis78|jis83|jis90|justify|justify-all|kannada|keep-all|landscape|large|larger|left|light|lighten|lighter|line|line-edge|line-through|linear|linearRGB|lining-nums|list-item|local|loose|lowercase|lr|lr-tb|ltr|luminance|luminosity|main-size|mandatory|manipulation|manual|margin-box|match-parent|match-source|mathematical|max-content|medium|menu|message-box|middle|min-content|miter|mixed|move|multiply|n-resize|narrower|ne-resize|nearest-neighbor|nesw-resize|newspaper|no-change|no-clip|no-close-quote|no-common-ligatures|no-contextual|no-discretionary-ligatures|no-drop|no-historical-ligatures|no-open-quote|no-repeat|none|nonzero|normal|not-allowed|nowrap|ns-resize|numbers|numeric|nw-resize|nwse-resize|oblique|oldstyle-nums|open|open-quote|optimizeLegibility|optimizeQuality|optimizeSpeed|optional|ordinal|outset|outside|over|overlay|overline|padding|padding-box|page|painted|pan-down|pan-left|pan-right|pan-up|pan-x|pan-y|paused|petite-caps|pixelated|plaintext|pointer|portrait|pre|pre-line|pre-wrap|preserve-3d|progress|progressive|proportional-nums|proportional-width|proximity|radial|recto|region|relative|remove|repeat|repeat-[xy]|reset-size|reverse|revert|ridge|right|rl|rl-tb|round|row|row-resize|row-reverse|row-severse|rtl|ruby|ruby-base|ruby-base-container|ruby-text|ruby-text-container|run-in|running|s-resize|saturation|scale-down|screen|scroll|scroll-position|se-resize|semi-condensed|semi-expanded|separate|sesame|show|sideways|sideways-left|sideways-lr|sideways-right|sideways-rl|simplified|slashed-zero|slice|small|small-caps|small-caption|smaller|smooth|soft-light|solid|space|space-around|space-between|space-evenly|spell-out|square|sRGB|stacked-fractions|start|static|status-bar|swap|step-end|step-start|sticky|stretch|strict|stroke|stroke-box|style|sub|subgrid|subpixel-antialiased|subtract|super|sw-resize|symbolic|table|table-caption|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|tabular-nums|tb|tb-rl|text|text-after-edge|text-before-edge|text-bottom|text-top|thick|thin|titling-caps|top|top-outside|touch|traditional|transparent|triangle|ultra-condensed|ultra-expanded|under|underline|unicase|unset|upleft|uppercase|upright|use-glyph-orientation|use-script|verso|vertical|vertical-ideographic|vertical-lr|vertical-rl|vertical-text|view-box|visible|visibleFill|visiblePainted|visibleStroke|w-resize|wait|wavy|weight|whitespace|wider|words|wrap|wrap-reverse|x|x-large|x-small|xx-large|xx-small|y|zero|zoom-in|zoom-out)(?![\\\\w-])\", \"name\": \"support.constant.property-value.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|cjk-ideographic|decimal|decimal-leading-zero|devanagari|disc|disclosure-closed|disclosure-open|ethiopic-halehame-am|ethiopic-halehame-ti-e[rt]|ethiopic-numeric|georgian|gujarati|gurmukhi|hangul|hangul-consonant|hebrew|hiragana|hiragana-iroha|japanese-formal|japanese-informal|kannada|katakana|katakana-iroha|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman|urdu)(?![\\\\w-])\", \"name\": \"support.constant.property-value.list-style-type.css\" }, { \"match\": \"(?<![\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\", \"name\": \"support.constant.vendored.property-value.css\" }, { \"match\": \"(?<![\\\\w-])(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system-ui|system|tahoma|times|trebuchet|ui-monospace|ui-rounded|ui-sans-serif|ui-serif|utopia|verdana|webdings|sans-serif|serif|monospace)(?![\\\\w-])\", \"name\": \"support.constant.font-name.css\" }] }, \"property-names\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(?:accent-color|additive-symbols|align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration|animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|backdrop-filter|backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image|background-origin|background-position|background-position-[xy]|background-repeat|background-size|bleed|block-size|border|border-block-end|border-block-end-color|border-block-end-style|border-block-end-width|border-block-start|border-block-start-color|border-block-start-style|border-block-start-width|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius|border-bottom-style|border-bottom-width|border-collapse|border-color|border-end-end-radius|border-end-start-radius|border-image|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-inline-end|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-start|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-left|border-left-color|border-left-style|border-left-width|border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-start-end-radius|border-start-start-radius|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style|border-top-width|border-width|bottom|box-decoration-break|box-shadow|box-sizing|break-after|break-before|break-inside|caption-side|caret-color|clear|clip|clip-path|clip-rule|color|color-adjust|color-interpolation-filters|color-scheme|column-count|column-fill|column-gap|column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|contain|container|container-name|container-type|content|counter-increment|counter-reset|cursor|direction|display|empty-cells|enable-background|fallback|fill|fill-opacity|fill-rule|filter|flex|flex-basis|flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|flood-color|flood-opacity|font|font-display|font-family|font-feature-settings|font-kerning|font-language-override|font-optical-sizing|font-size|font-size-adjust|font-stretch|font-style|font-synthesis|font-variant|font-variant-alternates|font-variant-caps|font-variant-east-asian|font-variant-ligatures|font-variant-numeric|font-variant-position|font-variation-settings|font-weight|gap|glyph-orientation-horizontal|glyph-orientation-vertical|grid|grid-area|grid-auto-columns|grid-auto-flow|grid-auto-rows|grid-column|grid-column-end|grid-column-gap|grid-column-start|grid-gap|grid-row|grid-row-end|grid-row-gap|grid-row-start|grid-template|grid-template-areas|grid-template-columns|grid-template-rows|hanging-punctuation|height|hyphens|image-orientation|image-rendering|image-resolution|ime-mode|initial-letter|initial-letter-align|inline-size|inset|inset-block|inset-block-end|inset-block-start|inset-inline|inset-inline-end|inset-inline-start|isolation|justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-break|line-clamp|line-height|list-style|list-style-image|list-style-position|list-style-type|margin|margin-block|margin-block-end|margin-block-start|margin-bottom|margin-inline|margin-inline-end|margin-inline-start|margin-left|margin-right|margin-top|marker-end|marker-mid|marker-start|marks|mask|mask-border|mask-border-mode|mask-border-outset|mask-border-repeat|mask-border-slice|mask-border-source|mask-border-width|mask-clip|mask-composite|mask-image|mask-mode|mask-origin|mask-position|mask-repeat|mask-size|mask-type|max-block-size|max-height|max-inline-size|max-lines|max-width|max-zoom|min-block-size|min-height|min-inline-size|min-width|min-zoom|mix-blend-mode|negative|object-fit|object-position|offset|offset-anchor|offset-distance|offset-path|offset-position|offset-rotation|opacity|order|orientation|orphans|outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-anchor|overflow-block|overflow-inline|overflow-wrap|overflow-[xy]|overscroll-behavior|overscroll-behavior-block|overscroll-behavior-inline|overscroll-behavior-[xy]|pad|padding|padding-block|padding-block-end|padding-block-start|padding-bottom|padding-inline|padding-inline-end|padding-inline-start|padding-left|padding-right|padding-top|page-break-after|page-break-before|page-break-inside|paint-order|perspective|perspective-origin|place-content|place-items|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|row-gap|ruby-align|ruby-merge|ruby-position|scale|scroll-behavior|scroll-margin|scroll-margin-block|scroll-margin-block-end|scroll-margin-block-start|scroll-margin-bottom|scroll-margin-inline|scroll-margin-inline-end|scroll-margin-inline-start|scroll-margin-left|scroll-margin-right|scroll-margin-top|scroll-padding|scroll-padding-block|scroll-padding-block-end|scroll-padding-block-start|scroll-padding-bottom|scroll-padding-inline|scroll-padding-inline-end|scroll-padding-inline-start|scroll-padding-left|scroll-padding-right|scroll-padding-top|scroll-snap-align|scroll-snap-coordinate|scroll-snap-destination|scroll-snap-stop|scroll-snap-type|scrollbar-color|scrollbar-gutter|scrollbar-width|shape-image-threshold|shape-margin|shape-outside|shape-rendering|size|speak-as|src|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|suffix|symbols|system|tab-size|table-layout|text-align|text-align-last|text-anchor|text-combine-upright|text-decoration|text-decoration-color|text-decoration-line|text-decoration-skip|text-decoration-skip-ink|text-decoration-style|text-decoration-thickness|text-emphasis|text-emphasis-color|text-emphasis-position|text-emphasis-style|text-indent|text-justify|text-orientation|text-overflow|text-rendering|text-shadow|text-size-adjust|text-transform|text-underline-offset|text-underline-position|top|touch-action|transform|transform-box|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function|translate|unicode-bidi|unicode-range|user-select|user-zoom|vertical-align|visibility|white-space|widows|width|will-change|word-break|word-spacing|word-wrap|writing-mode|z-index|zoom|alignment-baseline|baseline-shift|clip-rule|color-interpolation|color-interpolation-filters|color-profile|color-rendering|cx|cy|dominant-baseline|enable-background|fill|fill-opacity|fill-rule|flood-color|flood-opacity|glyph-orientation-horizontal|glyph-orientation-vertical|height|kerning|lighting-color|marker-end|marker-mid|marker-start|r|rx|ry|shape-rendering|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|text-anchor|width|x|y|adjust|after|align|align-last|alignment|alignment-adjust|appearance|attachment|azimuth|background-break|balance|baseline|before|bidi|binding|bookmark|bookmark-label|bookmark-level|bookmark-target|border-length|bottom-color|bottom-left-radius|bottom-right-radius|bottom-style|bottom-width|box|box-align|box-direction|box-flex|box-flex-group|box-lines|box-ordinal-group|box-orient|box-pack|break|character|collapse|column|column-break-after|column-break-before|count|counter|crop|cue|cue-after|cue-before|decoration|decoration-break|delay|display-model|display-role|down|drop|drop-initial-after-adjust|drop-initial-after-align|drop-initial-before-adjust|drop-initial-before-align|drop-initial-size|drop-initial-value|duration|elevation|emphasis|family|fit|fit-position|flex-group|float-offset|gap|grid-columns|grid-rows|hanging-punctuation|header|hyphenate|hyphenate-after|hyphenate-before|hyphenate-character|hyphenate-lines|hyphenate-resource|icon|image|increment|indent|index|initial-after-adjust|initial-after-align|initial-before-adjust|initial-before-align|initial-size|initial-value|inline-box-align|iteration-count|justify|label|left-color|left-style|left-width|length|level|line|line-stacking|line-stacking-ruby|line-stacking-shift|line-stacking-strategy|lines|list|mark|mark-after|mark-before|marks|marquee|marquee-direction|marquee-play-count|marquee-speed|marquee-style|max|min|model|move-to|name|nav|nav-down|nav-index|nav-left|nav-right|nav-up|new|numeral|offset|ordinal-group|orient|origin|overflow-style|overhang|pack|page|page-policy|pause|pause-after|pause-before|phonemes|pitch|pitch-range|play-count|play-during|play-state|point|presentation|presentation-level|profile|property|punctuation|punctuation-trim|radius|rate|rendering-intent|repeat|replace|reset|resolution|resource|respond-to|rest|rest-after|rest-before|richness|right-color|right-style|right-width|role|rotation|rotation-point|rows|ruby|ruby-overhang|ruby-span|rule|rule-color|rule-style|rule-width|shadow|size|size-adjust|sizing|space|space-collapse|spacing|span|speak|speak-header|speak-numeral|speak-punctuation|speech|speech-rate|speed|stacking|stacking-ruby|stacking-shift|stacking-strategy|stress|stretch|string-set|style|style-image|style-position|style-type|target|target-name|target-new|target-position|text|text-height|text-justify|text-outline|text-replace|text-wrap|timing-function|top-color|top-left-radius|top-right-radius|top-style|top-width|trim|unicode|up|user-select|variant|voice|voice-balance|voice-duration|voice-family|voice-pitch|voice-pitch-range|voice-rate|voice-stress|voice-volume|volume|weight|white|white-space-collapse|word|wrap)(?![\\\\w-])\", \"name\": \"support.type.property-name.css\" }, { \"match\": \"(?<![\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\", \"name\": \"support.type.vendored.property-name.css\" }] }, \"property-values\": { \"patterns\": [{ \"include\": \"#commas\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#functions\" }, { \"include\": \"#property-keywords\" }, { \"include\": \"#unicode-range\" }, { \"include\": \"#numeric-values\" }, { \"include\": \"#color-keywords\" }, { \"include\": \"#string\" }, { \"match\": \"!\\\\s*important(?![\\\\w-])\", \"name\": \"keyword.other.important.css\" }] }, \"pseudo-classes\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"invalid.illegal.colon.css\" } }, \"match\": \"(?i)(:)(:*)(?:active|any-link|checked|default|disabled|empty|enabled|first|(?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within|fullscreen|host|hover|in-range|indeterminate|invalid|left|link|optional|out-of-range|read-only|read-write|required|right|root|scope|target|unresolved|valid|visited)(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"pseudo-elements\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(?i)(?:(::?)(?:after|before|first-letter|first-line|(?:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-z-]+)|(::)(?:backdrop|content|grammar-error|marker|placeholder|selection|shadow|spelling-error))(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-element.css\" }, \"rule-list\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.property-list.css\", \"patterns\": [{ \"include\": \"#rule-list-innards\" }] }, \"rule-list-innards\": { \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#font-features\" }, { \"match\": \"(?<![\\\\w-])--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.css\" }, { \"begin\": \"(?<![-a-zA-Z])(?=[-a-zA-Z])\", \"end\": \"$|(?![-a-zA-Z])\", \"name\": \"meta.property-name.css\", \"patterns\": [{ \"include\": \"#property-names\" }] }, { \"begin\": \"(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"contentName\": \"meta.property-value.css\", \"end\": \"\\\\s*(;)|\\\\s*(?=}|\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#property-values\" }] }, { \"match\": \";\", \"name\": \"punctuation.terminator.rule.css\" }] }, \"selector\": { \"begin\": \"(?=(?:\\\\|)?(?:[-\\\\[:.*#a-zA-Z_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)))\", \"end\": \"(?=\\\\s*[/@{)])\", \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#selector-innards\" }] }, \"selector-innards\": { \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#commas\" }, { \"include\": \"#escapes\" }, { \"include\": \"#combinators\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.namespace-prefix.css\" }, \"2\": { \"name\": \"punctuation.separator.css\" } }, \"match\": \"(?:^|(?<=[\\\\s,(};]))(?![-\\\\w*]+\\\\|(?![-\\\\[:.*#a-zA-Z_]|[^\\\\x00-\\\\x7F]))((?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*|\\\\*)?(\\\\|)\" }, { \"include\": \"#tag-names\" }, { \"match\": \"\\\\*\", \"name\": \"entity.name.tag.wildcard.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?<![@\\\\w-])([.#])((?:-?\\\\d|-(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)|(?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?:[!\\\"'%&(*;<?@^`|\\\\]}]|/(?!\\\\*))+)(?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*)\", \"name\": \"invalid.illegal.bad-identifier.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(\\\\.)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)\", \"name\": \"entity.other.attribute-name.class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(\\\\#)(-?(?!\\\\d)(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)\", \"name\": \"entity.other.attribute-name.id.css\" }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.begin.bracket.square.css\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.end.bracket.square.css\" } }, \"name\": \"meta.attribute-selector.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#string\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.ignore-case.css\" } }, \"match\": `(?<=[\"'\\\\s]|^|\\\\*/)\\\\s*([iI])\\\\s*(?=[\\\\s\\\\]]|/\\\\*|$)` }, { \"captures\": { \"1\": { \"name\": \"string.unquoted.attribute-value.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": `(?<==)\\\\s*((?!/\\\\*)(?:[^\\\\\\\\\"'\\\\s\\\\]]|\\\\\\\\.)+)` }, { \"include\": \"#escapes\" }, { \"match\": \"[~|^$*]?=\", \"name\": \"keyword.operator.pattern.css\" }, { \"match\": \"\\\\|\", \"name\": \"punctuation.separator.css\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.namespace-prefix.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(-?(?!\\\\d)(?:[\\\\w-]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+|\\\\*)(?=\\\\|(?!\\\\s|=|$|\\\\])(?:-?(?!\\\\d)|[\\\\\\\\\\\\w-]|[^\\\\x00-\\\\x7F]))\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(-?(?!\\\\d)(?>[\\\\w-]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)\\\\s*(?=[~|^\\\\]$*=]|/\\\\*)\" }] }, { \"include\": \"#pseudo-classes\" }, { \"include\": \"#pseudo-elements\" }, { \"include\": \"#functional-pseudo-classes\" }, { \"match\": \"(?<![@\\\\w-])(?=[a-z]\\\\w*-)(?:(?![A-Z])[\\\\w-])+(?![(\\\\w-])\", \"name\": \"entity.name.tag.custom.css\" }] }, \"string\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"|(?<!\\\\\\\\)(?=$|\\\\n)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"begin\": '(?:\\\\G|^)(?=(?:[^\\\\\\\\\"]|\\\\\\\\.)+$)', \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\", \"patterns\": [{ \"include\": \"#escapes\" }] }, { \"include\": \"#escapes\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": \"'|(?<!\\\\\\\\)(?=$|\\\\n)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.single.css\", \"patterns\": [{ \"begin\": \"(?:\\\\G|^)(?=(?:[^\\\\\\\\']|\\\\\\\\.)+$)\", \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\", \"patterns\": [{ \"include\": \"#escapes\" }] }, { \"include\": \"#escapes\" }] }] }, \"tag-names\": { \"match\": \"(?i)(?<![\\\\w:-])(?:a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|bgsound|big|blink|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command|content|data|datalist|dd|del|details|dfn|dialog|dir|div|dl|dt|element|em|embed|fieldset|figcaption|figure|font|footer|form|frame|frameset|h[1-6]|head|header|hgroup|hr|html|i|iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|link|listing|main|map|mark|marquee|math|menu|menuitem|meta|meter|multicol|nav|nextid|nobr|noembed|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|plaintext|pre|progress|q|rb|rp|rt|rtc|ruby|s|samp|script|section|select|shadow|slot|small|source|spacer|span|strike|strong|style|sub|summary|sup|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr|track|tt|u|ul|var|video|wbr|xmp|altGlyph|altGlyphDef|altGlyphItem|animate|animateColor|animateMotion|animateTransform|circle|clipPath|color-profile|cursor|defs|desc|discard|ellipse|feBlend|feColorMatrix|feComponentTransfer|feComposite|feConvolveMatrix|feDiffuseLighting|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feImage|feMerge|feMergeNode|feMorphology|feOffset|fePointLight|feSpecularLighting|feSpotLight|feTile|feTurbulence|filter|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|foreignObject|g|glyph|glyphRef|hatch|hatchpath|hkern|line|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|metadata|missing-glyph|mpath|path|pattern|polygon|polyline|radialGradient|rect|set|solidcolor|stop|svg|switch|symbol|text|textPath|tref|tspan|use|view|vkern|annotation|annotation-xml|maction|maligngroup|malignmark|math|menclose|merror|mfenced|mfrac|mglyph|mi|mlabeledtr|mlongdiv|mmultiscripts|mn|mo|mover|mpadded|mphantom|mroot|mrow|ms|mscarries|mscarry|msgroup|msline|mspace|msqrt|msrow|mstack|mstyle|msub|msubsup|msup|mtable|mtd|mtext|mtr|munder|munderover|semantics)(?=[+~>\\\\s,.#|){:\\\\[]|/\\\\*|$)\", \"name\": \"entity.name.tag.css\" }, \"unicode-range\": { \"captures\": { \"0\": { \"name\": \"constant.other.unicode-range.css\" }, \"1\": { \"name\": \"punctuation.separator.dash.unicode-range.css\" } }, \"match\": \"(?<![\\\\w-])[Uu]\\\\+[0-9A-Fa-f?]{1,6}(?:(-)[0-9A-Fa-f]{1,6})?(?![\\\\w-])\" }, \"url\": { \"begin\": \"(?i)(?<![\\\\w@-])(url)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.url.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.url.css\", \"patterns\": [{ \"match\": `[^'\")\\\\s]+`, \"name\": \"variable.parameter.url.css\" }, { \"include\": \"#string\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] } }, \"scopeName\": \"source.css\" });\nvar css = [\n  lang\n];\n\nexport { css as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmD,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAiD;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAyC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,SAAS;wBAAuI;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,SAAS;wBAAsB;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,SAAS;oCAA2B,OAAO;oCAAK,QAAQ;gCAAsC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,uCAAuC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAAmB,OAAO;4BAAiB,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAqB,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkC,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAmB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkD;4BAAE;4BAAG,QAAQ;4BAA+B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,wCAAwC,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAA2B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAa,QAAQ;4BAAyC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;oCAA6F,QAAQ;gCAAoC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4D;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA0D;4BAAE;4BAAG,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,mCAAmC,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,SAAS;oCAAkD,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAqC;wCAAG,KAAK;4CAAE,QAAQ;wCAAuD;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAqD;oCAAE;oCAAG,QAAQ;oCAAmC,YAAY;wCAAC;4CAAE,WAAW;wCAAU;wCAAG;4CAAE,WAAW;wCAAiB;wCAAG;4CAAE,WAAW;wCAAW;wCAAG;4CAAE,SAAS,CAAC,UAAU,CAAC;4CAAE,QAAQ;wCAAuC;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAO;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,8DAA8D,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAiD,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAa,QAAQ;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;oCAA6F,QAAQ;gCAAuC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsD;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA8C;oCAAE;oCAAG,SAAS;gCAA0E;gCAAG;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqC,OAAO;oBAAuB,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,WAAW;gCAA2B;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,gDAAgD,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAiB,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,oDAAoD,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,eAAe;oBAAoC,OAAO;oBAAiB,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS,CAAC,qCAAqC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAiB;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;oCAA6C,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAAkM;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6B,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAqB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;4BAAE;4BAAG,QAAQ;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAqB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;wBAA0B;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;4BAAE;4BAAG,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmI,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAm4C,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA6X,QAAQ;gBAAsC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAkC;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,QAAQ;QAAuC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;QAAoB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAa,OAAO;oBAAa,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyD,QAAQ;gBAA0C;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS,CAAC,+FAA+F,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;4BAA6F,QAAQ;wBAA4B;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAsC,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA+E,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAAoD,QAAQ;gCAAsC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAAoD,QAAQ;gCAAsC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA0C,QAAQ;wBAAuB;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAAgC,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2E,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAgD,QAAQ;wBAAgC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0D,YAAY;wBAAC;4BAAE,SAAS;4BAA4C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS,CAAC,gDAAgD,CAAC;4BAAE,QAAQ;wBAAqC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,SAAS,CAAC,WAAW,CAAC;4BAAE,QAAQ;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAA+C,QAAQ;wBAA6B;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAA2C,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAA+F,QAAQ;wBAAwB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAiI,QAAQ;QAAsC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,SAAS;QAA0X;QAAG,eAAe;YAAE,SAAS;YAAO,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAkC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;4BAA2B,QAAQ;wBAAiB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAiB,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;QAA4H;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAA4D,QAAQ;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;oBAAwS,QAAQ;gBAAuB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqzI,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAixB,QAAQ;gBAAsD;gBAAG;oBAAE,SAAS;oBAA+G,QAAQ;gBAA+C;gBAAG;oBAAE,SAAS;oBAAmP,QAAQ;gBAAiC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAw3S,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA+G,QAAQ;gBAA0C;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAA6U,QAAQ;QAA+C;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAmQ,QAAQ;QAAiD;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAA0G,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAA+B,OAAO;oBAAmB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAyB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAkC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA+E,OAAO;YAAkB,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAA+K;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA4N,QAAQ;gBAAqC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAoG,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA6G,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS,CAAC,oDAAoD,CAAC;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAuC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS,CAAC,8CAA8C,CAAC;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAa,QAAQ;wBAA+B;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAA4B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAAoI;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAmC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAA2F;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAA6B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,OAAO;4BAAK,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,OAAO;4BAAK,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAo8D,QAAQ;QAAsB;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,SAAS;QAAwE;QAAG,OAAO;YAAE,SAAS;YAA8B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,SAAS,CAAC,UAAU,CAAC;oBAAE,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AACvwjD,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/scss.mjs"], "sourcesContent": ["import css from './css.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"SCSS\", \"name\": \"scss\", \"patterns\": [{ \"include\": \"#variable_setting\" }, { \"include\": \"#at_rule_forward\" }, { \"include\": \"#at_rule_use\" }, { \"include\": \"#at_rule_include\" }, { \"include\": \"#at_rule_import\" }, { \"include\": \"#general\" }, { \"include\": \"#flow_control\" }, { \"include\": \"#rules\" }, { \"include\": \"#property_list\" }, { \"include\": \"#at_rule_mixin\" }, { \"include\": \"#at_rule_media\" }, { \"include\": \"#at_rule_function\" }, { \"include\": \"#at_rule_charset\" }, { \"include\": \"#at_rule_option\" }, { \"include\": \"#at_rule_namespace\" }, { \"include\": \"#at_rule_fontface\" }, { \"include\": \"#at_rule_page\" }, { \"include\": \"#at_rule_keyframes\" }, { \"include\": \"#at_rule_at_root\" }, { \"include\": \"#at_rule_supports\" }, { \"match\": \";\", \"name\": \"punctuation.terminator.rule.css\" }], \"repository\": { \"at_rule_at_root\": { \"begin\": \"\\\\s*((@)(at-root))(\\\\s+|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.at-root.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.at-root.scss\", \"patterns\": [{ \"include\": \"#function_attributes\" }, { \"include\": \"#functions\" }, { \"include\": \"#selectors\" }] }, \"at_rule_charset\": { \"begin\": \"\\\\s*((@)charset\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.charset.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*((?=;|$))\", \"name\": \"meta.at-rule.charset.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }] }, \"at_rule_content\": { \"begin\": \"\\\\s*((@)content\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.content.scss\" } }, \"end\": \"\\\\s*((?=;))\", \"name\": \"meta.content.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#selectors\" }, { \"include\": \"#property_values\" }] }, \"at_rule_each\": { \"begin\": \"\\\\s*((@)each\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.each.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*((?=}))\", \"name\": \"meta.at-rule.each.scss\", \"patterns\": [{ \"match\": \"\\\\b(in|,)\\\\b\", \"name\": \"keyword.control.operator\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }, { \"include\": \"$self\" }] }, \"at_rule_else\": { \"begin\": \"\\\\s*((@)else(\\\\s*(if)?))\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.else.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.else.scss\", \"patterns\": [{ \"include\": \"#conditional_operators\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }] }, \"at_rule_extend\": { \"begin\": \"\\\\s*((@)extend\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.extend.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?=;)\", \"name\": \"meta.at-rule.extend.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#selectors\" }, { \"include\": \"#property_values\" }] }, \"at_rule_fontface\": { \"patterns\": [{ \"begin\": \"^\\\\s*((@)font-face\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.fontface.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.fontface.scss\", \"patterns\": [{ \"include\": \"#function_attributes\" }] }] }, \"at_rule_for\": { \"begin\": \"\\\\s*((@)for\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.for.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.for.scss\", \"patterns\": [{ \"match\": \"(==|!=|<=|>=|<|>|from|to|through)\", \"name\": \"keyword.control.operator\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }, { \"include\": \"$self\" }] }, \"at_rule_forward\": { \"begin\": \"\\\\s*((@)forward\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.forward.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?=;)\", \"name\": \"meta.at-rule.forward.scss\", \"patterns\": [{ \"match\": \"\\\\b(as|hide|show)\\\\b\", \"name\": \"keyword.control.operator\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.module.scss\" }, \"2\": { \"name\": \"punctuation.definition.wildcard.scss\" } }, \"match\": \"\\\\b([\\\\w-]+)(\\\\*)\" }, { \"match\": \"\\\\b[\\\\w-]+\\\\b\", \"name\": \"entity.name.function.scss\" }, { \"include\": \"#variable\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }, { \"include\": \"#comment_line\" }, { \"include\": \"#comment_block\" }] }, \"at_rule_function\": { \"patterns\": [{ \"begin\": \"\\\\s*((@)function\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.function.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" }, \"3\": { \"name\": \"entity.name.function.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.function.scss\", \"patterns\": [{ \"include\": \"#function_attributes\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.function.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" }, \"3\": { \"name\": \"entity.name.function.scss\" } }, \"match\": \"\\\\s*((@)function\\\\b)\\\\s*\", \"name\": \"meta.at-rule.function.scss\" }] }, \"at_rule_if\": { \"begin\": \"\\\\s*((@)if\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.if.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.if.scss\", \"patterns\": [{ \"include\": \"#conditional_operators\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }] }, \"at_rule_import\": { \"begin\": \"\\\\s*((@)import\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.import.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*((?=;)|(?=}))\", \"name\": \"meta.at-rule.import.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }, { \"include\": \"#functions\" }, { \"include\": \"#comment_line\" }] }, \"at_rule_include\": { \"patterns\": [{ \"begin\": \"(?<=@include)\\\\s+(?:([\\\\w-]+)\\\\s*(\\\\.))?([\\\\w-]+)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.scss\" }, \"2\": { \"name\": \"punctuation.access.module.scss\" }, \"3\": { \"name\": \"entity.name.function.scss\" }, \"4\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.scss\" } }, \"name\": \"meta.at-rule.include.scss\", \"patterns\": [{ \"include\": \"#function_attributes\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.at-rule.include.scss\" }, \"1\": { \"name\": \"variable.scss\" }, \"2\": { \"name\": \"punctuation.access.module.scss\" }, \"3\": { \"name\": \"entity.name.function.scss\" } }, \"match\": \"(?<=@include)\\\\s+(?:([\\\\w-]+)\\\\s*(\\\\.))?([\\\\w-]+)\" }, { \"captures\": { \"0\": { \"name\": \"meta.at-rule.include.scss\" }, \"1\": { \"name\": \"keyword.control.at-rule.include.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"match\": \"((@)include)\\\\b\" }] }, \"at_rule_keyframes\": { \"begin\": \"(?<=^|\\\\s)(@)(?:-(?:webkit|moz)-)?keyframes\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.keyframes.scss\" }, \"1\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"(?<=})\", \"name\": \"meta.at-rule.keyframes.scss\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.scss\" } }, \"match\": \"(?<=@keyframes)\\\\s+((?:[_A-Za-z][-\\\\w]|-[_A-Za-z])[-\\\\w]*)\" }, { \"begin\": '(?<=@keyframes)\\\\s+(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.scss\" } }, \"contentName\": \"entity.name.function.scss\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scss\" } }, \"name\": \"string.quoted.double.scss\", \"patterns\": [{ \"match\": \"\\\\\\\\(\\\\h{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(?<=@keyframes)\\\\s+(')\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.scss\" } }, \"contentName\": \"entity.name.function.scss\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scss\" } }, \"name\": \"string.quoted.single.scss\", \"patterns\": [{ \"match\": \"\\\\\\\\(\\\\h{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.begin.scss\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.end.scss\" } }, \"patterns\": [{ \"match\": \"\\\\b(?:(?:100|[1-9]\\\\d|\\\\d)%|from|to)(?=\\\\s*{)\", \"name\": \"entity.other.attribute-name.scss\" }, { \"include\": \"#flow_control\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#property_list\" }, { \"include\": \"#rules\" }] }] }, \"at_rule_media\": { \"patterns\": [{ \"begin\": \"^\\\\s*((@)media)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.media.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.media.scss\", \"patterns\": [{ \"include\": \"#comment_docblock\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#comment_line\" }, { \"match\": \"\\\\b(only)\\\\b\", \"name\": \"keyword.control.operator.css.scss\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.media-query.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.media-query.end.bracket.round.scss\" } }, \"name\": \"meta.property-list.media-query.scss\", \"patterns\": [{ \"begin\": \"(?<![-a-z])(?=[-a-z])\", \"end\": \"$|(?![-a-z])\", \"name\": \"meta.property-name.media-query.scss\", \"patterns\": [{ \"include\": \"source.css#media-features\" }, { \"include\": \"source.css#property-names\" }] }, { \"begin\": \"(:)\\\\s*(?!(\\\\s*{))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.scss\" } }, \"contentName\": \"meta.property-value.media-query.scss\", \"end\": \"\\\\s*(;|(?=}|\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.scss\" } }, \"patterns\": [{ \"include\": \"#general\" }, { \"include\": \"#property_values\" }] }] }, { \"include\": \"#variable\" }, { \"include\": \"#conditional_operators\" }, { \"include\": \"source.css#media-types\" }] }] }, \"at_rule_mixin\": { \"patterns\": [{ \"begin\": \"(?<=@mixin)\\\\s+([\\\\w-]+)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.scss\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.scss\" } }, \"name\": \"meta.at-rule.mixin.scss\", \"patterns\": [{ \"include\": \"#function_attributes\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.scss\" } }, \"match\": \"(?<=@mixin)\\\\s+([\\\\w-]+)\", \"name\": \"meta.at-rule.mixin.scss\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.mixin.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"match\": \"((@)mixin)\\\\b\", \"name\": \"meta.at-rule.mixin.scss\" }] }, \"at_rule_namespace\": { \"patterns\": [{ \"begin\": \"(?<=@namespace)\\\\s+(?=url)\", \"end\": \"(?=;|$)\", \"name\": \"meta.at-rule.namespace.scss\", \"patterns\": [{ \"include\": \"#property_values\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }] }, { \"begin\": \"(?<=@namespace)\\\\s+([\\\\w-]*)\", \"captures\": { \"1\": { \"name\": \"entity.name.namespace-prefix.scss\" } }, \"end\": \"(?=;|$)\", \"name\": \"meta.at-rule.namespace.scss\", \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#property_values\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.namespace.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"match\": \"((@)namespace)\\\\b\", \"name\": \"meta.at-rule.namespace.scss\" }] }, \"at_rule_option\": { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.charset.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"match\": \"^\\\\s*((@)option\\\\b)\\\\s*\", \"name\": \"meta.at-rule.option.scss\" }, \"at_rule_page\": { \"patterns\": [{ \"begin\": \"^\\\\s*((@)page)(?=:|\\\\s)\\\\s*([-:\\\\w]*)\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.page.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" }, \"3\": { \"name\": \"entity.name.function.scss\" } }, \"end\": \"\\\\s*(?={)\", \"name\": \"meta.at-rule.page.scss\" }] }, \"at_rule_return\": { \"begin\": \"\\\\s*((@)(return)\\\\b)\", \"captures\": { \"1\": { \"name\": \"keyword.control.return.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*((?=;))\", \"name\": \"meta.at-rule.return.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#property_values\" }] }, \"at_rule_supports\": { \"begin\": \"(?<=^|\\\\s)(@)supports\\\\b\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.supports.scss\" }, \"1\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"(?={)|$\", \"name\": \"meta.at-rule.supports.scss\", \"patterns\": [{ \"include\": \"#logical_operators\" }, { \"include\": \"#properties\" }, { \"match\": \"\\\\(\", \"name\": \"punctuation.definition.condition.begin.bracket.round.scss\" }, { \"match\": \"\\\\)\", \"name\": \"punctuation.definition.condition.end.bracket.round.scss\" }] }, \"at_rule_use\": { \"begin\": \"\\\\s*((@)use\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.use.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?=;)\", \"name\": \"meta.at-rule.use.scss\", \"patterns\": [{ \"match\": \"\\\\b(as|with)\\\\b\", \"name\": \"keyword.control.operator\" }, { \"match\": \"\\\\b[\\\\w-]+\\\\b\", \"name\": \"variable.scss\" }, { \"match\": \"\\\\*\", \"name\": \"variable.language.expanded-namespace.scss\" }, { \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }, { \"include\": \"#comment_line\" }, { \"include\": \"#comment_block\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.scss\" } }, \"patterns\": [{ \"include\": \"#function_attributes\" }] }] }, \"at_rule_warn\": { \"begin\": \"\\\\s*((@)(warn|debug|error)\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.warn.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?=;)\", \"name\": \"meta.at-rule.warn.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#string_double\" }, { \"include\": \"#string_single\" }] }, \"at_rule_while\": { \"begin\": \"\\\\s*((@)while\\\\b)\\\\s*\", \"captures\": { \"1\": { \"name\": \"keyword.control.while.scss\" }, \"2\": { \"name\": \"punctuation.definition.keyword.scss\" } }, \"end\": \"\\\\s*(?=})\", \"name\": \"meta.at-rule.while.scss\", \"patterns\": [{ \"include\": \"#conditional_operators\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }, { \"include\": \"$self\" }] }, \"comment_block\": { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scss\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scss\" } }, \"name\": \"comment.block.scss\" }, \"comment_docblock\": { \"begin\": \"///\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scss\" } }, \"end\": \"(?=$)\", \"name\": \"comment.block.documentation.scss\", \"patterns\": [{ \"include\": \"source.sassdoc\" }] }, \"comment_line\": { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scss\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.scss\" }, \"comparison_operators\": { \"match\": \"==|!=|<=|>=|<|>\", \"name\": \"keyword.operator.comparison.scss\" }, \"conditional_operators\": { \"patterns\": [{ \"include\": \"#comparison_operators\" }, { \"include\": \"#logical_operators\" }] }, \"constant_default\": { \"match\": \"!default\", \"name\": \"keyword.other.default.scss\" }, \"constant_functions\": { \"begin\": \"(?:([\\\\w-]+)(\\\\.))?([\\\\w-]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.scss\" }, \"2\": { \"name\": \"punctuation.access.module.scss\" }, \"3\": { \"name\": \"support.function.misc.scss\" }, \"4\": { \"name\": \"punctuation.section.function.scss\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.scss\" } }, \"patterns\": [{ \"include\": \"#parameters\" }] }, \"constant_important\": { \"match\": \"!important\", \"name\": \"keyword.other.important.scss\" }, \"constant_mathematical_symbols\": { \"match\": \"\\\\b(\\\\+|-|\\\\*|/)\\\\b\", \"name\": \"support.constant.mathematical-symbols.scss\" }, \"constant_optional\": { \"match\": \"!optional\", \"name\": \"keyword.other.optional.scss\" }, \"constant_sass_functions\": { \"begin\": \"(headings|stylesheet-url|rgba?|hsla?|ie-hex-str|red|green|blue|alpha|opacity|hue|saturation|lightness|prefixed|prefix|-moz|-svg|-css2|-pie|-webkit|-ms|font-(?:files|url)|grid-image|image-(?:width|height|url|color)|sprites?|sprite-(?:map|map-name|file|url|position)|inline-(?:font-files|image)|opposite-position|grad-point|grad-end-position|color-stops|color-stops-in-percentages|grad-color-stops|(?:radial|linear)-(?:gradient|svg-gradient)|opacify|fade-?in|transparentize|fade-?out|lighten|darken|saturate|desaturate|grayscale|adjust-(?:hue|lightness|saturation|color)|scale-(?:lightness|saturation|color)|change-color|spin|complement|invert|mix|-compass-(?:list|space-list|slice|nth|list-size)|blank|compact|nth|first-value-of|join|length|append|nest|append-selector|headers|enumerate|range|percentage|unitless|unit|if|type-of|comparable|elements-of-type|quote|unquote|escape|e|sin|cos|tan|abs|round|ceil|floor|pi|translate(?:X|Y))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.scss\" }, \"2\": { \"name\": \"punctuation.section.function.scss\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.scss\" } }, \"patterns\": [{ \"include\": \"#parameters\" }] }, \"flow_control\": { \"patterns\": [{ \"include\": \"#at_rule_if\" }, { \"include\": \"#at_rule_else\" }, { \"include\": \"#at_rule_warn\" }, { \"include\": \"#at_rule_for\" }, { \"include\": \"#at_rule_while\" }, { \"include\": \"#at_rule_each\" }, { \"include\": \"#at_rule_return\" }] }, \"function_attributes\": { \"patterns\": [{ \"match\": \":\", \"name\": \"punctuation.separator.key-value.scss\" }, { \"include\": \"#general\" }, { \"include\": \"#property_values\" }, { \"match\": \"[={}?;@]\", \"name\": \"invalid.illegal.scss\" }] }, \"functions\": { \"patterns\": [{ \"begin\": \"([\\\\w-]{1,})(\\\\()\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.scss\" }, \"2\": { \"name\": \"punctuation.section.function.scss\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.scss\" } }, \"patterns\": [{ \"include\": \"#parameters\" }] }, { \"match\": \"([\\\\w-]{1,})\", \"name\": \"support.function.misc.scss\" }] }, \"general\": { \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#comment_docblock\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#comment_line\" }] }, \"interpolation\": { \"begin\": \"#{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.interpolation.begin.bracket.curly.scss\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.interpolation.end.bracket.curly.scss\" } }, \"name\": \"variable.interpolation.scss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#property_values\" }] }, \"logical_operators\": { \"match\": \"\\\\b(not|or|and)\\\\b\", \"name\": \"keyword.operator.logical.scss\" }, \"map\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.map.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.map.end.bracket.round.scss\" } }, \"name\": \"meta.definition.variable.map.scss\", \"patterns\": [{ \"include\": \"#comment_docblock\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#comment_line\" }, { \"captures\": { \"1\": { \"name\": \"support.type.map.key.scss\" }, \"2\": { \"name\": \"punctuation.separator.key-value.scss\" } }, \"match\": \"\\\\b([\\\\w-]+)\\\\s*(:)\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.scss\" }, { \"include\": \"#map\" }, { \"include\": \"#variable\" }, { \"include\": \"#property_values\" }] }, \"operators\": { \"match\": \"[-+*/](?!\\\\s*[-+*/])\", \"name\": \"keyword.operator.css\" }, \"parameters\": { \"patterns\": [{ \"include\": \"#variable\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.scss\" } }, \"patterns\": [{ \"include\": \"#function_attributes\" }] }, { \"include\": \"#property_values\" }, { \"include\": \"#comment_block\" }, { \"match\": `[^'\",) \\\\t]+`, \"name\": \"variable.parameter.url.scss\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.scss\" }] }, \"parent_selector_suffix\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.identifier.scss\" }] } }, \"match\": \"(?<=&)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\$|})+)(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)\", \"name\": \"entity.other.attribute-name.parent-selector-suffix.css\" }, \"properties\": { \"patterns\": [{ \"begin\": \"(?<![-a-z])(?=[-a-z])\", \"end\": \"$|(?![-a-z])\", \"name\": \"meta.property-name.scss\", \"patterns\": [{ \"include\": \"source.css#property-names\" }, { \"include\": \"#at_rule_include\" }] }, { \"begin\": \"(:)\\\\s*(?!(\\\\s*{))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.scss\" } }, \"contentName\": \"meta.property-value.scss\", \"end\": \"\\\\s*(;|(?=}|\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.scss\" } }, \"patterns\": [{ \"include\": \"#general\" }, { \"include\": \"#property_values\" }] }] }, \"property_list\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.scss\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.scss\" } }, \"name\": \"meta.property-list.scss\", \"patterns\": [{ \"include\": \"#flow_control\" }, { \"include\": \"#rules\" }, { \"include\": \"#properties\" }, { \"include\": \"$self\" }] }, \"property_values\": { \"patterns\": [{ \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }, { \"include\": \"#constant_functions\" }, { \"include\": \"#constant_sass_functions\" }, { \"include\": \"#constant_important\" }, { \"include\": \"#constant_default\" }, { \"include\": \"#constant_optional\" }, { \"include\": \"source.css#numeric-values\" }, { \"include\": \"source.css#property-keywords\" }, { \"include\": \"source.css#color-keywords\" }, { \"include\": \"source.css#property-names\" }, { \"include\": \"#constant_mathematical_symbols\" }, { \"include\": \"#operators\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.scss\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.scss\" } }, \"patterns\": [{ \"include\": \"#general\" }, { \"include\": \"#property_values\" }] }] }, \"rules\": { \"patterns\": [{ \"include\": \"#general\" }, { \"include\": \"#at_rule_extend\" }, { \"include\": \"#at_rule_content\" }, { \"include\": \"#at_rule_include\" }, { \"include\": \"#at_rule_media\" }, { \"include\": \"#selectors\" }] }, \"selector_attribute\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.attribute-selector.begin.bracket.square.scss\" }, \"2\": { \"name\": \"entity.other.attribute-name.attribute.scss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.scss\" }] }, \"3\": { \"name\": \"keyword.operator.scss\" }, \"4\": { \"name\": \"string.unquoted.attribute-value.scss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.scss\" }] }, \"5\": { \"name\": \"string.quoted.double.attribute-value.scss\" }, \"6\": { \"name\": \"punctuation.definition.string.begin.scss\" }, \"7\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.scss\" }] }, \"8\": { \"name\": \"punctuation.definition.string.end.scss\" }, \"9\": { \"name\": \"string.quoted.single.attribute-value.scss\" }, \"10\": { \"name\": \"punctuation.definition.string.begin.scss\" }, \"11\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.scss\" }] }, \"12\": { \"name\": \"punctuation.definition.string.end.scss\" }, \"13\": { \"name\": \"punctuation.definition.attribute-selector.end.bracket.square.scss\" } }, \"match\": `(?i)(\\\\[)\\\\s*((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\.?\\\\$|})+?)(?:\\\\s*([~|^$*]?=)\\\\s*(?:((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\.?\\\\$|})+)|((\")(.*?)(\"))|((')(.*?)('))))?\\\\s*(\\\\])`, \"name\": \"meta.attribute-selector.scss\" }, \"selector_class\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.scss\" }] } }, \"match\": \"(\\\\.)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\.?\\\\$|})+)(?=$|[\\\\s,#)\\\\[:{>+~|]|\\\\.[^$]|/\\\\*|;)\", \"name\": \"entity.other.attribute-name.class.css\" }, \"selector_custom\": { \"match\": \"\\\\b([a-zA-Z0-9]+(-[a-zA-Z0-9]+)+)(?=\\\\.|\\\\s++[^:]|\\\\s*[,\\\\[{]|:(link|visited|hover|active|focus|target|lang|disabled|enabled|checked|indeterminate|root|nth-(child|last-child|of-type|last-of-type)|first-child|last-child|first-of-type|last-of-type|only-child|only-of-type|empty|not|valid|invalid)(\\\\([0-9A-Za-z]*\\\\))?)\", \"name\": \"entity.name.tag.custom.scss\" }, \"selector_id\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.identifier.scss\" }] } }, \"match\": \"(\\\\#)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\.?\\\\$|})+)(?=$|[\\\\s,#)\\\\[:{>+~|]|\\\\.[^$]|/\\\\*)\", \"name\": \"entity.other.attribute-name.id.css\" }, \"selector_placeholder\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\\\\\([0-9a-fA-F]{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"match\": \"\\\\$|}\", \"name\": \"invalid.illegal.identifier.scss\" }] } }, \"match\": \"(%)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\#\\\\{|\\\\.\\\\$|\\\\$|})+)(?=;|$|[\\\\s,#)\\\\[:{>+~|]|\\\\.[^$]|/\\\\*)\", \"name\": \"entity.other.attribute-name.placeholder.css\" }, \"selector_pseudo_class\": { \"patterns\": [{ \"begin\": \"((:)\\\\bnth-(?:child|last-child|of-type|last-of-type))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.definition.pseudo-class.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.pseudo-class.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.css\" }, { \"match\": \"(?<=\\\\d)n\\\\b|\\\\b(n|even|odd)\\\\b\", \"name\": \"constant.other.scss\" }, { \"match\": \"\\\\w+\", \"name\": \"invalid.illegal.scss\" }] }, { \"include\": \"source.css#pseudo-classes\" }, { \"include\": \"source.css#pseudo-elements\" }, { \"include\": \"source.css#functional-pseudo-classes\" }] }, \"selectors\": { \"patterns\": [{ \"include\": \"source.css#tag-names\" }, { \"include\": \"#selector_custom\" }, { \"include\": \"#selector_class\" }, { \"include\": \"#selector_id\" }, { \"include\": \"#selector_pseudo_class\" }, { \"include\": \"#tag_wildcard\" }, { \"include\": \"#tag_parent_reference\" }, { \"include\": \"source.css#pseudo-elements\" }, { \"include\": \"#selector_attribute\" }, { \"include\": \"#selector_placeholder\" }, { \"include\": \"#parent_selector_suffix\" }] }, \"string_double\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.scss\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scss\" } }, \"name\": \"string.quoted.double.scss\", \"patterns\": [{ \"match\": \"\\\\\\\\(\\\\h{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"include\": \"#interpolation\" }] }, \"string_single\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.scss\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.scss\" } }, \"name\": \"string.quoted.single.scss\", \"patterns\": [{ \"match\": \"\\\\\\\\(\\\\h{1,6}|.)\", \"name\": \"constant.character.escape.scss\" }, { \"include\": \"#interpolation\" }] }, \"tag_parent_reference\": { \"match\": \"&\", \"name\": \"entity.name.tag.reference.scss\" }, \"tag_wildcard\": { \"match\": \"\\\\*\", \"name\": \"entity.name.tag.wildcard.scss\" }, \"variable\": { \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#interpolation\" }] }, \"variable_setting\": { \"begin\": \"(?=\\\\$[\\\\w-]+\\\\s*:)\", \"contentName\": \"meta.definition.variable.scss\", \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.scss\" } }, \"patterns\": [{ \"match\": \"\\\\$[\\\\w-]+(?=\\\\s*:)\", \"name\": \"variable.scss\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.scss\" } }, \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#comment_docblock\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#comment_line\" }, { \"include\": \"#map\" }, { \"include\": \"#property_values\" }, { \"include\": \"#variable\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.scss\" }] }] }, \"variables\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.scss\" }, \"2\": { \"name\": \"punctuation.access.module.scss\" }, \"3\": { \"name\": \"variable.scss\" } }, \"match\": \"\\\\b([\\\\w-]+)(\\\\.)(\\\\$[\\\\w-]+)\\\\b\" }, { \"match\": \"(\\\\$|--)[A-Za-z0-9_-]+\\\\b\", \"name\": \"variable.scss\" }] } }, \"scopeName\": \"source.css.scss\", \"embeddedLangs\": [\"css\"] });\nvar scss = [\n  ...css,\n  lang\n];\n\nexport { scss as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAmB;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,SAAS;YAAK,QAAQ;QAAkC;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,SAAS;YAA8B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA2B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAiB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA2B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAe,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAwB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAe,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAgC,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA0B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAuB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,SAAS;oBAAqC,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA2B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;gBAAoB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAA4B,QAAQ;gBAA6B;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAsB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAA0B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAqB,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAkB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAkD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAU,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAA6D;gBAAG;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,eAAe;oBAA6B,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,eAAe;oBAA6B,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAiD,QAAQ;wBAAmC;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAAoC;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8D;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA4D;4BAAE;4BAAG,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,SAAS;oCAAyB,OAAO;oCAAgB,QAAQ;oCAAuC,YAAY;wCAAC;4CAAE,WAAW;wCAA4B;wCAAG;4CAAE,WAAW;wCAA4B;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAsB,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAuC;oCAAE;oCAAG,eAAe;oCAAwC,OAAO;oCAAqB,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAAW;wCAAG;4CAAE,WAAW;wCAAmB;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;oBAA4B,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAiB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,OAAO;oBAAW,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAqB,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2B,QAAQ;QAA2B;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;gBAAyB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAwB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAe,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA4B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAW,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4D;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA0D;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAuB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4C;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAsC,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAyB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAa,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;QAAqB;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAO,QAAQ;QAAoB;QAAG,wBAAwB;YAAE,SAAS;YAAmB,QAAQ;QAAmC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAY,QAAQ;QAA6B;QAAG,sBAAsB;YAAE,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgB;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAc,QAAQ;QAA+B;QAAG,iCAAiC;YAAE,SAAS;YAAuB,QAAQ;QAA6C;QAAG,qBAAqB;YAAE,SAAS;YAAa,QAAQ;QAA8B;QAAG,2BAA2B;YAAE,SAAS;YAA66B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAuB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAA6B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgE;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8D;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAsB,QAAQ;QAAgC;QAAG,OAAO;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,SAAS;gBAAsB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAwB,QAAQ;QAAuB;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS,CAAC,YAAY,CAAC;oBAAE,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkC;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAAkH,QAAQ;QAAyD;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,OAAO;oBAAgB,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAqB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsE;gBAAG,KAAK;oBAAE,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,MAAM;oBAAE,QAAQ;gBAA2C;gBAAG,MAAM;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG,MAAM;oBAAE,QAAQ;gBAAyC;gBAAG,MAAM;oBAAE,QAAQ;gBAAoE;YAAE;YAAG,SAAS,CAAC,6OAA6O,CAAC;YAAE,QAAQ;QAA+B;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAuB;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAA8H,QAAQ;QAAwC;QAAG,mBAAmB;YAAE,SAAS;YAAgU,QAAQ;QAA8B;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkC;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAA4H,QAAQ;QAAqC;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkC;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAA+H,QAAQ;QAA8C;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAAQ,QAAQ;wBAAuB;wBAAG;4BAAE,SAAS;4BAAmC,QAAQ;wBAAsB;wBAAG;4BAAE,SAAS;4BAAQ,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAuC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAiC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAiC;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAK,QAAQ;QAAiC;QAAG,gBAAgB;YAAE,SAAS;YAAO,QAAQ;QAAgC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAuB,eAAe;YAAiC,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAAgB;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgB;oBAAE;oBAAG,SAAS;gBAAmC;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAgB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAmB,iBAAiB;QAAC;KAAM;AAAC;AAC5o5B,IAAI,OAAO;OACN,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/postcss.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PostCSS\", \"fileTypes\": [\"pcss\", \"postcss\"], \"foldingStartMarker\": \"/\\\\*|^#|^\\\\*|^\\\\b|^\\\\.\", \"foldingStopMarker\": \"\\\\*/|^\\\\s*$\", \"name\": \"postcss\", \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.postcss\", \"patterns\": [{ \"include\": \"#comment-tag\" }] }, { \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#placeholder-selector\" }, { \"include\": \"#variable\" }, { \"include\": \"#variable-root-css\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#dotdotdot\" }, { \"begin\": \"@include\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.postcss\" } }, \"end\": \"(?=\\\\n|\\\\(|{|;)\", \"name\": \"support.function.name.postcss.library\" }, { \"begin\": \"@mixin|@function\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.css.postcss\" } }, \"end\": \"$\\\\n?|(?=\\\\(|{)\", \"name\": \"support.function.name.postcss.no-completions\", \"patterns\": [{ \"match\": \"[\\\\w-]+\", \"name\": \"entity.name.function\" }] }, { \"match\": \"(?<=@import)\\\\s[\\\\w/.*-]+\", \"name\": \"string.quoted.double.css.postcss\" }, { \"begin\": \"@\", \"end\": \"$\\\\n?|\\\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\\\s|,))|(?=;)\", \"name\": \"keyword.control.at-rule.css.postcss\" }, { \"begin\": \"#\", \"end\": \"$\\\\n?|(?=\\\\s|,|;|\\\\(|\\\\)|\\\\.|\\\\[|{|>)\", \"name\": \"entity.other.attribute-name.id.css.postcss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\.|(?<=&)(-|_)\", \"end\": \"$\\\\n?|(?=\\\\s|,|;|\\\\(|\\\\)|\\\\[|{|>)\", \"name\": \"entity.other.attribute-name.class.css.postcss\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"name\": \"entity.other.attribute-selector.postcss\", \"patterns\": [{ \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"match\": \"\\\\^|\\\\$|\\\\*|~\", \"name\": \"keyword.other.regex.postcss\" }] }, { \"match\": \"(?<=\\\\]|\\\\)|not\\\\(|\\\\*|>|>\\\\s):[a-z:-]+|(::|:-)[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.postcss\" }, { \"begin\": \":\", \"end\": \"$\\\\n?|(?=;|\\\\s\\\\(|and\\\\(|{|}|\\\\),)\", \"name\": \"meta.property-list.css.postcss\", \"patterns\": [{ \"include\": \"#double-slash\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#variable\" }, { \"include\": \"#rgb-value\" }, { \"include\": \"#numeric\" }, { \"include\": \"#unit\" }, { \"include\": \"#flag\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"include\": \"#function-content-var\" }, { \"include\": \"#operator\" }, { \"include\": \"#parent-selector\" }, { \"include\": \"#property-value\" }] }, { \"include\": \"#rgb-value\" }, { \"include\": \"#function\" }, { \"include\": \"#function-content\" }, { \"begin\": \"(?<!-|\\\\()\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|x)\\\\b(?!-|\\\\)|:\\\\s)|&\", \"end\": \"(?=\\\\s|,|;|\\\\(|\\\\)|\\\\.|\\\\[|{|>|-|_)\", \"name\": \"entity.name.tag.css.postcss.symbol\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#pseudo-class\" }] }, { \"include\": \"#operator\" }, { \"match\": \"[a-z-]+((?=:|#{))\", \"name\": \"support.type.property-name.css.postcss\" }, { \"include\": \"#reserved-words\" }, { \"include\": \"#property-value\" }], \"repository\": { \"comment-tag\": { \"begin\": \"{{\", \"end\": \"}}\", \"name\": \"comment.tags.postcss\", \"patterns\": [{ \"match\": \"[\\\\w-]+\", \"name\": \"comment.tag.postcss\" }] }, \"dotdotdot\": { \"match\": \"\\\\.{3}\", \"name\": \"variable.other\" }, \"double-quoted\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.css.postcss\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"double-slash\": { \"begin\": \"//\", \"end\": \"$\", \"name\": \"comment.line.postcss\", \"patterns\": [{ \"include\": \"#comment-tag\" }] }, \"flag\": { \"match\": \"!(important|default|optional|global)\", \"name\": \"keyword.other.important.css.postcss\" }, \"function\": { \"match\": \"(?<=[\\\\s|(|,|:])(?!url|format|attr)[\\\\w-][\\\\w-]*(?=\\\\()\", \"name\": \"support.function.name.postcss\" }, \"function-content\": { \"match\": \"(?<=url\\\\(|format\\\\(|attr\\\\().+?(?=\\\\))\", \"name\": \"string.quoted.double.css.postcss\" }, \"function-content-var\": { \"match\": \"(?<=var\\\\()[\\\\w-]+(?=\\\\))\", \"name\": \"variable.parameter.postcss\" }, \"interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.postcss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }, { \"include\": \"#double-quoted\" }, { \"include\": \"#single-quoted\" }] }, \"numeric\": { \"match\": \"(-|\\\\.)?\\\\d+(\\\\.\\\\d+)?\", \"name\": \"constant.numeric.css.postcss\" }, \"operator\": { \"match\": \"\\\\+|\\\\s-\\\\s|\\\\s-(?=\\\\$)|(?<=\\\\()-(?=\\\\$)|\\\\s-(?=\\\\()|\\\\*|/|%|=|!|<|>|~\", \"name\": \"keyword.operator.postcss\" }, \"parent-selector\": { \"match\": \"&\", \"name\": \"entity.name.tag.css.postcss\" }, \"placeholder-selector\": { \"begin\": \"(?<!\\\\d)%(?!\\\\d)\", \"end\": \"$\\\\n?|\\\\s|(?=;|{)\", \"name\": \"entity.other.attribute-name.placeholder-selector.postcss\" }, \"property-value\": { \"match\": \"[\\\\w-]+\", \"name\": \"meta.property-value.css.postcss, support.constant.property-value.css.postcss\" }, \"pseudo-class\": { \"match\": \":[a-z:-]+\", \"name\": \"entity.other.attribute-name.pseudo-class.css.postcss\" }, \"quoted-interpolation\": { \"begin\": \"#{\", \"end\": \"}\", \"name\": \"support.function.interpolation.postcss\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#numeric\" }, { \"include\": \"#operator\" }, { \"include\": \"#unit\" }] }, \"reserved-words\": { \"match\": \"\\\\b(false|from|in|not|null|through|to|true)\\\\b\", \"name\": \"support.type.property-name.css.postcss\" }, \"rgb-value\": { \"match\": \"(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\\\\b\", \"name\": \"constant.other.color.rgb-value.css.postcss\" }, \"single-quoted\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.css.postcss\", \"patterns\": [{ \"include\": \"#quoted-interpolation\" }] }, \"unit\": { \"match\": \"(?<=[\\\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|%)\", \"name\": \"keyword.other.unit.css.postcss\" }, \"variable\": { \"match\": \"\\\\$[\\\\w-]+\", \"name\": \"variable.parameter.postcss\" }, \"variable-root-css\": { \"match\": \"(?<!&)--[\\\\w-]+\", \"name\": \"variable.parameter.postcss\" } }, \"scopeName\": \"source.css.postcss\" });\nvar postcss = [\n  lang\n];\n\nexport { postcss as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAQ;KAAU;IAAE,sBAAsB;IAA0B,qBAAqB;IAAe,QAAQ;IAAW,YAAY;QAAC;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,SAAS;YAAY,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAmB,QAAQ;QAAwC;QAAG;YAAE,SAAS;YAAoB,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAmB,QAAQ;YAAgD,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAuB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAAmC;QAAG;YAAE,SAAS;YAAK,OAAO;YAA+G,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAK,OAAO;YAAyC,QAAQ;YAA8C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmB,OAAO;YAAqC,QAAQ;YAAiD,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA8B;aAAE;QAAC;QAAG;YAAE,SAAS;YAA2D,QAAQ;QAAuD;QAAG;YAAE,SAAS;YAAK,OAAO;YAAsC,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,SAAS;YAAqsB,OAAO;YAAuC,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAyC;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAkB;KAAE;IAAE,cAAc;QAAE,eAAe;YAAE,SAAS;YAAM,OAAO;YAAM,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAAsB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAU,QAAQ;QAAiB;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAAwC,QAAQ;QAAsC;QAAG,YAAY;YAAE,SAAS;YAA2D,QAAQ;QAAgC;QAAG,oBAAoB;YAAE,SAAS;YAA2C,QAAQ;QAAmC;QAAG,wBAAwB;YAAE,SAAS;YAA6B,QAAQ;QAA6B;QAAG,iBAAiB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAA0B,QAAQ;QAA+B;QAAG,YAAY;YAAE,SAAS;YAA0E,QAAQ;QAA2B;QAAG,mBAAmB;YAAE,SAAS;YAAK,QAAQ;QAA8B;QAAG,wBAAwB;YAAE,SAAS;YAAoB,OAAO;YAAqB,QAAQ;QAA2D;QAAG,kBAAkB;YAAE,SAAS;YAAW,QAAQ;QAA+E;QAAG,gBAAgB;YAAE,SAAS;YAAa,QAAQ;QAAuD;QAAG,wBAAwB;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAkD,QAAQ;QAAyC;QAAG,aAAa;YAAE,SAAS;YAAyC,QAAQ;QAA6C;QAAG,iBAAiB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,QAAQ;YAAE,SAAS;YAA8G,QAAQ;QAAiC;QAAG,YAAY;YAAE,SAAS;YAAc,QAAQ;QAA6B;QAAG,qBAAqB;YAAE,SAAS;YAAmB,QAAQ;QAA6B;IAAE;IAAG,aAAa;AAAqB;AAC/pN,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/astro.mjs"], "sourcesContent": ["import json from './json.mjs';\nimport javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport stylus from './stylus.mjs';\nimport sass from './sass.mjs';\nimport css from './css.mjs';\nimport scss from './scss.mjs';\nimport less from './less.mjs';\nimport postcss from './postcss.mjs';\nimport tsx from './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Astro\", \"fileTypes\": [\"astro\"], \"injections\": { \"L:(meta.script.astro) (meta.lang.js | meta.lang.javascript | meta.lang.partytown | meta.lang.node) - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.js\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"L:(meta.script.astro) (meta.lang.json) - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.json\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.json\" }] }] }, \"L:(meta.script.astro) (meta.lang.ts | meta.lang.typescript) - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.ts\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, \"L:meta.script.astro - meta.lang - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.js\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"L:meta.style.astro - meta.lang - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, \"L:meta.style.astro meta.lang.css - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, \"L:meta.style.astro meta.lang.less - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.less\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.css.less\" }] }] }, \"L:meta.style.astro meta.lang.postcss - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.postcss\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.css.postcss\" }] }] }, \"L:meta.style.astro meta.lang.sass - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.sass\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.sass\" }] }] }, \"L:meta.style.astro meta.lang.scss - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.scss\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.css.scss\" }] }] }, \"L:meta.style.astro meta.lang.stylus - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.stylus\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.astro\", \"patterns\": [{ \"include\": \"source.stylus\" }] }] } }, \"name\": \"astro\", \"patterns\": [{ \"include\": \"#scope\" }, { \"include\": \"#frontmatter\" }, { \"include\": \"#text\" }], \"repository\": { \"attribute-literal\": { \"begin\": \"(`)\", \"end\": \"\\\\1\", \"name\": \"string.template.astro\", \"patterns\": [{ \"include\": \"source.tsx#template-substitution-element\" }, { \"include\": \"source.tsx#string-character-escape\" }] }, \"attributes\": { \"patterns\": [{ \"include\": \"#attributes-events\" }, { \"include\": \"#attributes-keyvalue\" }, { \"include\": \"#attributes-interpolated\" }] }, \"attributes-events\": { \"begin\": \"(on(s(croll|t(orage|alled)|u(spend|bmit)|e(curitypolicyviolation|ek(ing|ed)|lect))|hashchange|c(hange|o(ntextmenu|py)|u(t|echange)|l(ick|ose)|an(cel|play(through)?))|t(imeupdate|oggle)|in(put|valid)|o(nline|ffline)|d(urationchange|r(op|ag(start|over|e(n(ter|d)|xit)|leave)?)|blclick)|un(handledrejection|load)|p(opstate|lay(ing)?|a(ste|use|ge(show|hide))|rogress)|e(nded|rror|mptied)|volumechange|key(down|up|press)|focus|w(heel|aiting)|l(oad(start|e(nd|d(data|metadata)))?|anguagechange)|a(uxclick|fterprint|bort)|r(e(s(ize|et)|jectionhandled)|atechange)|m(ouse(o(ut|ver)|down|up|enter|leave|move)|essage(error)?)|b(efore(unload|print)|lur)))(?![\\\\\\\\w:-])\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"match\": \".*\", \"name\": \"entity.other.attribute-name.astro\" }] } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.astro\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.astro\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#attribute-literal\" }, { \"begin\": \"(?=[^\\\\s=<>`/]|/(?!>))\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.line.js\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"source.js\" }, \"1\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": \"(([^\\\\s\\\\\\\"'=<>`/]|/(?!>))+)\", \"name\": \"string.unquoted.astro\" }, { \"begin\": '([\"])', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.astro\" } }, \"end\": \"\\\\1\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.astro\" } }, \"name\": \"string.quoted.astro\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": '([^\\\\n\\\\\"/]|/(?![/*]))+' }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": '(?=\\\\\")|\\\\n', \"name\": \"comment.line.double-slash.js\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.js\" } }, \"end\": '(?=\\\\\")|\\\\*/', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.js\" } }, \"name\": \"comment.block.js\" }] }, { \"begin\": \"(['])\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.astro\" } }, \"end\": \"\\\\1\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.astro\" } }, \"name\": \"string.quoted.astro\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": \"([^\\\\n\\\\'/]|/(?![/*]))+\" }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"(?=\\\\')|\\\\n\", \"name\": \"comment.line.double-slash.js\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.js\" } }, \"end\": \"(?=\\\\')|\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.js\" } }, \"name\": \"comment.block.js\" }] }] }] }] }, \"attributes-interpolated\": { \"begin\": \"(?<!:|=)\\\\s*({)\", \"contentName\": \"meta.embedded.expression.astro source.tsx\", \"end\": \"(\\\\})\", \"patterns\": [{ \"include\": \"source.tsx\" }] }, \"attributes-keyvalue\": { \"begin\": \"([_@$A-Za-z][:._\\\\-$0-9A-Za-z]*)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"match\": \".*\", \"name\": \"entity.other.attribute-name.astro\" }] } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.astro\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.astro\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"include\": \"#attributes-value\" }] }] }, \"attributes-value\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"match\": \"([^\\\\s\\\"'=<>`/]|/(?!>))+\", \"name\": \"string.unquoted.astro\" }, { \"begin\": `(['\"])`, \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.astro\" } }, \"end\": \"\\\\1\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.astro\" } }, \"name\": \"string.quoted.astro\" }, { \"include\": \"#attribute-literal\" }] }, \"comments\": { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.astro\" } }, \"end\": \"-->\", \"name\": \"comment.block.astro\", \"patterns\": [{ \"match\": \"\\\\G-?>|<!--(?!>)|<!-(?=-->)|--!>\", \"name\": \"invalid.illegal.characters-not-allowed-here.astro\" }] }, \"entities\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.astro\" }, \"912\": { \"name\": \"punctuation.definition.entity.astro\" } }, \"match\": \"(&)(?=[a-zA-Z])((a(s(ymp(eq)?|cr|t)|n(d(slope|d|v|and)?|g(s(t|ph)|zarr|e|le|rt(vb(d)?)?|msd(a(h|c|d|e|f|a|g|b))?)?)|c(y|irc|d|ute|E)?|tilde|o(pf|gon)|uml|p(id|os|prox(eq)?|e|E|acir)?|elig|f(r)?|w(conint|int)|l(pha|e(ph|fsym))|acute|ring|grave|m(p|a(cr|lg))|breve)|A(s(sign|cr)|nd|MP|c(y|irc)|tilde|o(pf|gon)|uml|pplyFunction|fr|Elig|lpha|acute|ring|grave|macr|breve))|(B(scr|cy|opf|umpeq|e(cause|ta|rnoullis)|fr|a(ckslash|r(v|wed))|reve)|b(s(cr|im(e)?|ol(hsub|b)?|emi)|n(ot|e(quiv)?)|c(y|ong)|ig(s(tar|qcup)|c(irc|up|ap)|triangle(down|up)|o(times|dot|plus)|uplus|vee|wedge)|o(t(tom)?|pf|wtie|x(h(d|u|D|U)?|times|H(d|u|D|U)?|d(R|l|r|L)|u(R|l|r|L)|plus|D(R|l|r|L)|v(R|h|H|l|r|L)?|U(R|l|r|L)|V(R|h|H|l|r|L)?|minus|box))|Not|dquo|u(ll(et)?|mp(e(q)?|E)?)|prime|e(caus(e)?|t(h|ween|a)|psi|rnou|mptyv)|karow|fr|l(ock|k(1(2|4)|34)|a(nk|ck(square|triangle(down|left|right)?|lozenge)))|a(ck(sim(eq)?|cong|prime|epsilon)|r(vee|wed(ge)?))|r(eve|vbar)|brk(tbrk)?))|(c(s(cr|u(p(e)?|b(e)?))|h(cy|i|eck(mark)?)|ylcty|c(irc|ups(sm)?|edil|a(ps|ron))|tdot|ir(scir|c(eq|le(d(R|circ|S|dash|ast)|arrow(left|right)))?|e|fnint|E|mid)?|o(n(int|g(dot)?)|p(y(sr)?|f|rod)|lon(e(q)?)?|m(p(fn|le(xes|ment))?|ma(t)?))|dot|u(darr(l|r)|p(s|c(up|ap)|or|dot|brcap)?|e(sc|pr)|vee|wed|larr(p)?|r(vearrow(left|right)|ly(eq(succ|prec)|vee|wedge)|arr(m)?|ren))|e(nt(erdot)?|dil|mptyv)|fr|w(conint|int)|lubs(uit)?|a(cute|p(s|c(up|ap)|dot|and|brcup)?|r(on|et))|r(oss|arr))|C(scr|hi|c(irc|onint|edil|aron)|ircle(Minus|Times|Dot|Plus)|Hcy|o(n(tourIntegral|int|gruent)|unterClockwiseContourIntegral|p(f|roduct)|lon(e)?)|dot|up(Cap)?|OPY|e(nterDot|dilla)|fr|lo(seCurly(DoubleQuote|Quote)|ckwiseContourIntegral)|a(yleys|cute|p(italDifferentialD)?)|ross))|(d(s(c(y|r)|trok|ol)|har(l|r)|c(y|aron)|t(dot|ri(f)?)|i(sin|e|v(ide(ontimes)?|onx)?|am(s|ond(suit)?)?|gamma)|Har|z(cy|igrarr)|o(t(square|plus|eq(dot)?|minus)?|ublebarwedge|pf|wn(harpoon(left|right)|downarrows|arrow)|llar)|d(otseq|a(rr|gger))?|u(har|arr)|jcy|e(lta|g|mptyv)|f(isht|r)|wangle|lc(orn|rop)|a(sh(v)?|leth|rr|gger)|r(c(orn|rop)|bkarow)|b(karow|lac)|Arr)|D(s(cr|trok)|c(y|aron)|Scy|i(fferentialD|a(critical(Grave|Tilde|Do(t|ubleAcute)|Acute)|mond))|o(t(Dot|Equal)?|uble(Right(Tee|Arrow)|ContourIntegral|Do(t|wnArrow)|Up(DownArrow|Arrow)|VerticalBar|L(ong(RightArrow|Left(RightArrow|Arrow))|eft(RightArrow|Tee|Arrow)))|pf|wn(Right(TeeVector|Vector(Bar)?)|Breve|Tee(Arrow)?|arrow|Left(RightVector|TeeVector|Vector(Bar)?)|Arrow(Bar|UpArrow)?))|Zcy|el(ta)?|D(otrahd)?|Jcy|fr|a(shv|rr|gger)))|(e(s(cr|im|dot)|n(sp|g)|c(y|ir(c)?|olon|aron)|t(h|a)|o(pf|gon)|dot|u(ro|ml)|p(si(v|lon)?|lus|ar(sl)?)|e|D(ot|Dot)|q(s(im|lant(less|gtr))|c(irc|olon)|u(iv(DD)?|est|als)|vparsl)|f(Dot|r)|l(s(dot)?|inters|l)?|a(ster|cute)|r(Dot|arr)|g(s(dot)?|rave)?|x(cl|ist|p(onentiale|ectation))|m(sp(1(3|4))?|pty(set|v)?|acr))|E(s(cr|im)|c(y|irc|aron)|ta|o(pf|gon)|NG|dot|uml|TH|psilon|qu(ilibrium|al(Tilde)?)|fr|lement|acute|grave|x(ists|ponentialE)|m(pty(SmallSquare|VerySmallSquare)|acr)))|(f(scr|nof|cy|ilig|o(pf|r(k(v)?|all))|jlig|partint|emale|f(ilig|l(ig|lig)|r)|l(tns|lig|at)|allingdotseq|r(own|a(sl|c(1(2|8|3|4|5|6)|78|2(3|5)|3(8|4|5)|45|5(8|6)))))|F(scr|cy|illed(SmallSquare|VerySmallSquare)|o(uriertrf|pf|rAll)|fr))|(G(scr|c(y|irc|edil)|t|opf|dot|T|Jcy|fr|amma(d)?|reater(Greater|SlantEqual|Tilde|Equal(Less)?|FullEqual|Less)|g|breve)|g(s(cr|im(e|l)?)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|irc)|t(c(c|ir)|dot|quest|lPar|r(sim|dot|eq(qless|less)|less|a(pprox|rr)))?|imel|opf|dot|jcy|e(s(cc|dot(o(l)?)?|l(es)?)?|q(slant|q)?|l)?|v(nE|ertneqq)|fr|E(l)?|l(j|E|a)?|a(cute|p|mma(d)?)|rave|g(g)?|breve))|(h(s(cr|trok|lash)|y(phen|bull)|circ|o(ok(leftarrow|rightarrow)|pf|arr|rbar|mtht)|e(llip|arts(uit)?|rcon)|ks(earow|warow)|fr|a(irsp|lf|r(dcy|r(cir|w)?)|milt)|bar|Arr)|H(s(cr|trok)|circ|ilbertSpace|o(pf|rizontalLine)|ump(DownHump|Equal)|fr|a(cek|t)|ARDcy))|(i(s(cr|in(s(v)?|dot|v|E)?)|n(care|t(cal|prod|e(rcal|gers)|larhk)?|odot|fin(tie)?)?|c(y|irc)?|t(ilde)?|i(nfin|i(nt|int)|ota)?|o(cy|ta|pf|gon)|u(kcy|ml)|jlig|prod|e(cy|xcl)|quest|f(f|r)|acute|grave|m(of|ped|a(cr|th|g(part|e|line))))|I(scr|n(t(e(rsection|gral))?|visible(Comma|Times))|c(y|irc)|tilde|o(ta|pf|gon)|dot|u(kcy|ml)|Ocy|Jlig|fr|Ecy|acute|grave|m(plies|a(cr|ginaryI))?))|(j(s(cr|ercy)|c(y|irc)|opf|ukcy|fr|math)|J(s(cr|ercy)|c(y|irc)|opf|ukcy|fr))|(k(scr|hcy|c(y|edil)|opf|jcy|fr|appa(v)?|green)|K(scr|c(y|edil)|Hcy|opf|Jcy|fr|appa))|(l(s(h|cr|trok|im(e|g)?|q(uo(r)?|b)|aquo)|h(ar(d|u(l)?)|blk)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|ub|e(il|dil)|aron)|Barr|t(hree|c(c|ir)|imes|dot|quest|larr|r(i(e|f)?|Par))?|Har|o(ng(left(arrow|rightarrow)|rightarrow|mapsto)|times|z(enge|f)?|oparrow(left|right)|p(f|lus|ar)|w(ast|bar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|r(dhar|ushar))|ur(dshar|uhar)|jcy|par(lt)?|e(s(s(sim|dot|eq(qgtr|gtr)|approx|gtr)|cc|dot(o(r)?)?|g(es)?)?|q(slant|q)?|ft(harpoon(down|up)|threetimes|leftarrows|arrow(tail)?|right(squigarrow|harpoons|arrow(s)?))|g)?|v(nE|ertneqq)|f(isht|loor|r)|E(g)?|l(hard|corner|tri|arr)?|a(ng(d|le)?|cute|t(e(s)?|ail)?|p|emptyv|quo|rr(sim|hk|tl|pl|fs|lp|b(fs)?)?|gran|mbda)|r(har(d)?|corner|tri|arr|m)|g(E)?|m(idot|oust(ache)?)|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr))|L(s(h|cr|trok)|c(y|edil|aron)|t|o(ng(RightArrow|left(arrow|rightarrow)|rightarrow|Left(RightArrow|Arrow))|pf|wer(RightArrow|LeftArrow))|T|e(ss(Greater|SlantEqual|Tilde|EqualGreater|FullEqual|Less)|ft(Right(Vector|Arrow)|Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|rightarrow|Floor|A(ngleBracket|rrow(RightArrow|Bar)?)))|Jcy|fr|l(eftarrow)?|a(ng|cute|placetrf|rr|mbda)|midot))|(M(scr|cy|inusPlus|opf|u|e(diumSpace|llintrf)|fr|ap)|m(s(cr|tpos)|ho|nplus|c(y|omma)|i(nus(d(u)?|b)?|cro|d(cir|dot|ast)?)|o(dels|pf)|dash|u(ltimap|map)?|p|easuredangle|DDot|fr|l(cp|dr)|a(cr|p(sto(down|up|left)?)?|l(t(ese)?|e)|rker)))|(n(s(hort(parallel|mid)|c(cue|e|r)?|im(e(q)?)?|u(cc(eq)?|p(set(eq(q)?)?|e|E)?|b(set(eq(q)?)?|e|E)?)|par|qsu(pe|be)|mid)|Rightarrow|h(par|arr|Arr)|G(t(v)?|g)|c(y|ong(dot)?|up|edil|a(p|ron))|t(ilde|lg|riangle(left(eq)?|right(eq)?)|gl)|i(s(d)?|v)?|o(t(ni(v(c|a|b))?|in(dot|v(c|a|b)|E)?)?|pf)|dash|u(m(sp|ero)?)?|jcy|p(olint|ar(sl|t|allel)?|r(cue|e(c(eq)?)?)?)|e(s(im|ear)|dot|quiv|ar(hk|r(ow)?)|xist(s)?|Arr)?|v(sim|infin|Harr|dash|Dash|l(t(rie)?|e|Arr)|ap|r(trie|Arr)|g(t|e))|fr|w(near|ar(hk|r(ow)?)|Arr)|V(dash|Dash)|l(sim|t(ri(e)?)?|dr|e(s(s)?|q(slant|q)?|ft(arrow|rightarrow))?|E|arr|Arr)|a(ng|cute|tur(al(s)?)?|p(id|os|prox|E)?|bla)|r(tri(e)?|ightarrow|arr(c|w)?|Arr)|g(sim|t(r)?|e(s|q(slant|q)?)?|E)|mid|L(t(v)?|eft(arrow|rightarrow)|l)|b(sp|ump(e)?))|N(scr|c(y|edil|aron)|tilde|o(nBreakingSpace|Break|t(R(ightTriangle(Bar|Equal)?|everseElement)|Greater(Greater|SlantEqual|Tilde|Equal|FullEqual|Less)?|S(u(cceeds(SlantEqual|Tilde|Equal)?|perset(Equal)?|bset(Equal)?)|quareSu(perset(Equal)?|bset(Equal)?))|Hump(DownHump|Equal)|Nested(GreaterGreater|LessLess)|C(ongruent|upCap)|Tilde(Tilde|Equal|FullEqual)?|DoubleVerticalBar|Precedes(SlantEqual|Equal)?|E(qual(Tilde)?|lement|xists)|VerticalBar|Le(ss(Greater|SlantEqual|Tilde|Equal|Less)?|ftTriangle(Bar|Equal)?))?|pf)|u|e(sted(GreaterGreater|LessLess)|wLine|gative(MediumSpace|Thi(nSpace|ckSpace)|VeryThinSpace))|Jcy|fr|acute))|(o(s(cr|ol|lash)|h(m|bar)|c(y|ir(c)?)|ti(lde|mes(as)?)|S|int|opf|d(sold|iv|ot|ash|blac)|uml|p(erp|lus|ar)|elig|vbar|f(cir|r)|l(c(ir|ross)|t|ine|arr)|a(st|cute)|r(slope|igof|or|d(er(of)?|f|m)?|v|arr)?|g(t|on|rave)|m(i(nus|cron|d)|ega|acr))|O(s(cr|lash)|c(y|irc)|ti(lde|mes)|opf|dblac|uml|penCurly(DoubleQuote|Quote)|ver(B(ar|rac(e|ket))|Parenthesis)|fr|Elig|acute|r|grave|m(icron|ega|acr)))|(p(s(cr|i)|h(i(v)?|one|mmat)|cy|i(tchfork|v)?|o(intint|und|pf)|uncsp|er(cnt|tenk|iod|p|mil)|fr|l(us(sim|cir|two|d(o|u)|e|acir|mn|b)?|an(ck(h)?|kv))|ar(s(im|l)|t|a(llel)?)?|r(sim|n(sim|E|ap)|cue|ime(s)?|o(d|p(to)?|f(surf|line|alar))|urel|e(c(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?)?|E|ap)?|m)|P(s(cr|i)|hi|cy|i|o(incareplane|pf)|fr|lusMinus|artialD|r(ime|o(duct|portion(al)?)|ecedes(SlantEqual|Tilde|Equal)?)?))|(q(scr|int|opf|u(ot|est(eq)?|at(int|ernions))|prime|fr)|Q(scr|opf|UOT|fr))|(R(s(h|cr)|ho|c(y|edil|aron)|Barr|ight(Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|Floor|A(ngleBracket|rrow(Bar|LeftArrow)?))|o(undImplies|pf)|uleDelayed|e(verse(UpEquilibrium|E(quilibrium|lement)))?|fr|EG|a(ng|cute|rr(tl)?)|rightarrow)|r(s(h|cr|q(uo(r)?|b)|aquo)|h(o(v)?|ar(d|u(l)?))|nmid|c(y|ub|e(il|dil)|aron)|Barr|t(hree|imes|ri(e|f|ltri)?)|i(singdotseq|ng|ght(squigarrow|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(tail)?|rightarrows))|Har|o(times|p(f|lus|ar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|ldhar)|uluhar|p(polint|ar(gt)?)|e(ct|al(s|ine|part)?|g)|f(isht|loor|r)|l(har|arr|m)|a(ng(d|e|le)?|c(ute|e)|t(io(nals)?|ail)|dic|emptyv|quo|rr(sim|hk|c|tl|pl|fs|w|lp|ap|b(fs)?)?)|rarr|x|moust(ache)?|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr)))|(s(s(cr|tarf|etmn|mile)|h(y|c(hcy|y)|ort(parallel|mid)|arp)|c(sim|y|n(sim|E|ap)|cue|irc|polint|e(dil)?|E|a(p|ron))?|t(ar(f)?|r(ns|aight(phi|epsilon)))|i(gma(v|f)?|m(ne|dot|plus|e(q)?|l(E)?|rarr|g(E)?)?)|zlig|o(pf|ftcy|l(b(ar)?)?)|dot(e|b)?|u(ng|cc(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?|p(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|hs(ol|ub)|1|n(e|E)|2|d(sub|ot)|3|plus|e(dot)?|E|larr|mult)?|m|b(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|n(e|E)|dot|plus|e(dot)?|E|rarr|mult)?)|pa(des(uit)?|r)|e(swar|ct|tm(n|inus)|ar(hk|r(ow)?)|xt|mi|Arr)|q(su(p(set(eq)?|e)?|b(set(eq)?|e)?)|c(up(s)?|ap(s)?)|u(f|ar(e|f))?)|fr(own)?|w(nwar|ar(hk|r(ow)?)|Arr)|larr|acute|rarr|m(t(e(s)?)?|i(d|le)|eparsl|a(shp|llsetminus))|bquo)|S(scr|hort(RightArrow|DownArrow|UpArrow|LeftArrow)|c(y|irc|edil|aron)?|tar|igma|H(cy|CHcy)|opf|u(c(hThat|ceeds(SlantEqual|Tilde|Equal)?)|p(set|erset(Equal)?)?|m|b(set(Equal)?)?)|OFTcy|q(uare(Su(perset(Equal)?|bset(Equal)?)|Intersection|Union)?|rt)|fr|acute|mallCircle))|(t(s(hcy|c(y|r)|trok)|h(i(nsp|ck(sim|approx))|orn|e(ta(sym|v)?|re(4|fore))|k(sim|ap))|c(y|edil|aron)|i(nt|lde|mes(d|b(ar)?)?)|o(sa|p(cir|f(ork)?|bot)?|ea)|dot|prime|elrec|fr|w(ixt|ohead(leftarrow|rightarrow))|a(u|rget)|r(i(sb|time|dot|plus|e|angle(down|q|left(eq)?|right(eq)?)?|minus)|pezium|ade)|brk)|T(s(cr|trok)|RADE|h(i(nSpace|ckSpace)|e(ta|refore))|c(y|edil|aron)|S(cy|Hcy)|ilde(Tilde|Equal|FullEqual)?|HORN|opf|fr|a(u|b)|ripleDot))|(u(scr|h(ar(l|r)|blk)|c(y|irc)|t(ilde|dot|ri(f)?)|Har|o(pf|gon)|d(har|arr|blac)|u(arr|ml)|p(si(h|lon)?|harpoon(left|right)|downarrow|uparrows|lus|arrow)|f(isht|r)|wangle|l(c(orn(er)?|rop)|tri)|a(cute|rr)|r(c(orn(er)?|rop)|tri|ing)|grave|m(l|acr)|br(cy|eve)|Arr)|U(scr|n(ion(Plus)?|der(B(ar|rac(e|ket))|Parenthesis))|c(y|irc)|tilde|o(pf|gon)|dblac|uml|p(si(lon)?|downarrow|Tee(Arrow)?|per(RightArrow|LeftArrow)|DownArrow|Equilibrium|arrow|Arrow(Bar|DownArrow)?)|fr|a(cute|rr(ocir)?)|ring|grave|macr|br(cy|eve)))|(v(s(cr|u(pn(e|E)|bn(e|E)))|nsu(p|b)|cy|Bar(v)?|zigzag|opf|dash|prop|e(e(eq|bar)?|llip|r(t|bar))|Dash|fr|ltri|a(ngrt|r(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|t(heta|riangle(left|right))|p(hi|i|ropto)|epsilon|kappa|r(ho)?))|rtri|Arr)|V(scr|cy|opf|dash(l)?|e(e|r(yThinSpace|t(ical(Bar|Separator|Tilde|Line))?|bar))|Dash|vdash|fr|bar))|(w(scr|circ|opf|p|e(ierp|d(ge(q)?|bar))|fr|r(eath)?)|W(scr|circ|opf|edge|fr))|(X(scr|i|opf|fr)|x(s(cr|qcup)|h(arr|Arr)|nis|c(irc|up|ap)|i|o(time|dot|p(f|lus))|dtri|u(tri|plus)|vee|fr|wedge|l(arr|Arr)|r(arr|Arr)|map))|(y(scr|c(y|irc)|icy|opf|u(cy|ml)|en|fr|ac(y|ute))|Y(scr|c(y|irc)|opf|uml|Icy|Ucy|fr|acute|Acy))|(z(scr|hcy|c(y|aron)|igrarr|opf|dot|e(ta|etrf)|fr|w(nj|j)|acute)|Z(scr|c(y|aron)|Hcy|opf|dot|e(ta|roWidthSpace)|fr|acute)))(;)\", \"name\": \"constant.character.entity.named.$2.astro\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.astro\" }, \"3\": { \"name\": \"punctuation.definition.entity.astro\" } }, \"match\": \"(&)#\\\\d+(;)\", \"name\": \"constant.character.entity.numeric.decimal.astro\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.astro\" }, \"3\": { \"name\": \"punctuation.definition.entity.astro\" } }, \"match\": \"(&)#[xX][0-9a-fA-F]+(;)\", \"name\": \"constant.character.entity.numeric.hexadecimal.astro\" }, { \"match\": \"&(?=[a-zA-Z0-9]+;)\", \"name\": \"invalid.illegal.ambiguous-ampersand.astro\" }] }, \"frontmatter\": { \"begin\": \"\\\\A(-{3})\\\\s*$\", \"beginCaptures\": { \"1\": { \"name\": \"comment\" } }, \"contentName\": \"source.ts\", \"end\": \"(^|\\\\G)(-{3})|\\\\.{3}\\\\s*$\", \"endCaptures\": { \"2\": { \"name\": \"comment\" } }, \"patterns\": [{ \"include\": \"source.ts\" }] }, \"interpolation\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.astro\" } }, \"contentName\": \"meta.embedded.expression.astro source.tsx\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.astro\" } }, \"patterns\": [{ \"begin\": \"\\\\G\\\\s*(?={)\", \"end\": \"(?<=})\", \"patterns\": [{ \"include\": \"source.tsx#object-literal\" }] }, { \"include\": \"source.tsx\" }] }] }, \"scope\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#tags\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#entities\" }] }, \"tags\": { \"patterns\": [{ \"include\": \"#tags-raw\" }, { \"include\": \"#tags-lang\" }, { \"include\": \"#tags-void\" }, { \"include\": \"#tags-general-end\" }, { \"include\": \"#tags-general-start\" }] }, \"tags-end-node\": { \"captures\": { \"1\": { \"name\": \"meta.tag.end.astro punctuation.definition.tag.begin.astro\" }, \"2\": { \"name\": \"meta.tag.end.astro\", \"patterns\": [{ \"include\": \"#tags-name\" }] }, \"3\": { \"name\": \"meta.tag.end.astro punctuation.definition.tag.end.astro\" }, \"4\": { \"name\": \"meta.tag.start.astro punctuation.definition.tag.end.astro\" } }, \"match\": \"(</)(.*?)\\\\s*(>)|(/>)\" }, \"tags-general-end\": { \"begin\": \"(</)([^/\\\\s>]*)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.tag.end.astro punctuation.definition.tag.begin.astro\" }, \"2\": { \"name\": \"meta.tag.end.astro\", \"patterns\": [{ \"include\": \"#tags-name\" }] } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.tag.end.astro punctuation.definition.tag.end.astro\" } }, \"name\": \"meta.scope.tag.$2.astro\" }, \"tags-general-start\": { \"begin\": \"(<)([^/\\\\s>/]*)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-start-node\" }] } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.tag.start.astro punctuation.definition.tag.end.astro\" } }, \"name\": \"meta.scope.tag.$2.astro\", \"patterns\": [{ \"include\": \"#tags-start-attributes\" }] }, \"tags-lang\": { \"begin\": \"<(script|style)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-start-node\" }] } }, \"end\": \"</\\\\1\\\\s*>|/>\", \"endCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-end-node\" }] } }, \"name\": \"meta.scope.tag.$1.astro meta.$1.astro\", \"patterns\": [{ \"begin\": `\\\\G(?=\\\\s*[^>]*?(type|lang)\\\\s*=\\\\s*(['\"]|)(?:text\\\\/)?(application\\\\/ld\\\\+json)\\\\2)`, \"end\": \"(?=</|/>)\", \"name\": \"meta.lang.json.astro\", \"patterns\": [{ \"include\": \"#tags-lang-start-attributes\" }] }, { \"begin\": `\\\\G(?=\\\\s*[^>]*?(type|lang)\\\\s*=\\\\s*(['\"]|)(module)\\\\2)`, \"end\": \"(?=</|/>)\", \"name\": \"meta.lang.javascript.astro\", \"patterns\": [{ \"include\": \"#tags-lang-start-attributes\" }] }, { \"begin\": `\\\\G(?=\\\\s*[^>]*?(type|lang)\\\\s*=\\\\s*(['\"]|)(?:text/|application/)?([\\\\w\\\\/+]+)\\\\2)`, \"end\": \"(?=</|/>)\", \"name\": \"meta.lang.$3.astro\", \"patterns\": [{ \"include\": \"#tags-lang-start-attributes\" }] }, { \"include\": \"#tags-lang-start-attributes\" }] }, \"tags-lang-start-attributes\": { \"begin\": \"\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.astro\" } }, \"name\": \"meta.tag.start.astro\", \"patterns\": [{ \"include\": \"#attributes\" }] }, \"tags-name\": { \"patterns\": [{ \"match\": \"[A-Z]\\\\w*\", \"name\": \"support.class.component.astro\" }, { \"match\": \"[a-z][\\\\w0-9:]*-[\\\\w0-9:-]*\", \"name\": \"meta.tag.custom.astro entity.name.tag.astro\" }, { \"match\": \"[a-z][\\\\w0-9:-]*\", \"name\": \"entity.name.tag.astro\" }] }, \"tags-raw\": { \"begin\": \"<([^/?!\\\\s<>]+)(?=[^>]+is:raw).*?\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-start-node\" }] } }, \"contentName\": \"source.unknown\", \"end\": \"</\\\\1\\\\s*>|/>\", \"endCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-end-node\" }] } }, \"name\": \"meta.scope.tag.$1.astro meta.raw.astro\", \"patterns\": [{ \"include\": \"#tags-lang-start-attributes\" }] }, \"tags-start-attributes\": { \"begin\": \"\\\\G\", \"end\": \"(?=/?>)\", \"name\": \"meta.tag.start.astro\", \"patterns\": [{ \"include\": \"#attributes\" }] }, \"tags-start-node\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.astro\" }, \"2\": { \"patterns\": [{ \"include\": \"#tags-name\" }] } }, \"match\": \"(<)([^/\\\\s>/]*)\", \"name\": \"meta.tag.start.astro\" }, \"tags-void\": { \"begin\": \"(<)(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.astro\" }, \"2\": { \"name\": \"entity.name.tag.astro\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.astro\" } }, \"name\": \"meta.tag.void.astro\", \"patterns\": [{ \"include\": \"#attributes\" }] }, \"text\": { \"patterns\": [{ \"begin\": \"(?<=^|---|>|})\", \"end\": \"(?=<|{|$)\", \"name\": \"text.astro\", \"patterns\": [{ \"include\": \"#entities\" }] }] } }, \"scopeName\": \"source.astro\", \"embeddedLangs\": [\"json\", \"javascript\", \"typescript\", \"stylus\", \"sass\", \"css\", \"scss\", \"less\", \"postcss\", \"tsx\"] });\nvar astro = [\n  ...json,\n  ...javascript,\n  ...typescript,\n  ...stylus,\n  ...sass,\n  ...css,\n  ...scss,\n  ...less,\n  ...postcss,\n  ...tsx,\n  lang\n];\n\nexport { astro as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;KAAQ;IAAE,cAAc;QAAE,sHAAsH;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAa,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,0DAA0D;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAe,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,+EAA+E;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAa,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,mDAAmD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAa,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,kDAAkD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAc,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,oDAAoD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAc,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,qDAAqD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAmB,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,wDAAwD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAsB,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,qDAAqD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAe,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,qDAAqD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAmB,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uDAAuD;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,eAAe;oBAAiB,OAAO;oBAAU,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,qBAAqB;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAA2C;gBAAG;oBAAE,WAAW;gBAAqC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAopB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAM,QAAQ;wBAAoC;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAoB,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,SAAS;4BAA0B,OAAO;4BAAW,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAY;wCAAG,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;oCAAgC,QAAQ;gCAAwB;gCAAG;oCAAE,SAAS;oCAAS,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA4C;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,QAAQ;oCAAuB,YAAY;wCAAC;4CAAE,YAAY;gDAAE,KAAK;oDAAE,YAAY;wDAAC;4DAAE,WAAW;wDAAY;qDAAE;gDAAC;4CAAE;4CAAG,SAAS;wCAA0B;wCAAG;4CAAE,SAAS;4CAAM,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAoC;4CAAE;4CAAG,OAAO;4CAAe,QAAQ;wCAA+B;wCAAG;4CAAE,SAAS;4CAAQ,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA0C;4CAAE;4CAAG,OAAO;4CAAgB,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAwC;4CAAE;4CAAG,QAAQ;wCAAmB;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAS,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA4C;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAA0C;oCAAE;oCAAG,QAAQ;oCAAuB,YAAY;wCAAC;4CAAE,YAAY;gDAAE,KAAK;oDAAE,YAAY;wDAAC;4DAAE,WAAW;wDAAY;qDAAE;gDAAC;4CAAE;4CAAG,SAAS;wCAA0B;wCAAG;4CAAE,SAAS;4CAAM,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAoC;4CAAE;4CAAG,OAAO;4CAAe,QAAQ;wCAA+B;wCAAG;4CAAE,SAAS;4CAAQ,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA0C;4CAAE;4CAAG,OAAO;4CAAgB,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAwC;4CAAE;4CAAG,QAAQ;wCAAmB;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAmB,eAAe;YAA6C,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAM,QAAQ;wBAAoC;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAoB,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS,CAAC,MAAM,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAO,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,SAAS;oBAAoC,QAAQ;gBAAoD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,OAAO;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAA86W,QAAQ;gBAA2C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAAkD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAA2B,QAAQ;gBAAsD;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA4C;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAU;YAAE;YAAG,eAAe;YAAa,OAAO;YAA6B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAU;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,eAAe;oBAA6C,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,OAAO;4BAAU,YAAY;gCAAC;oCAAE,WAAW;gCAA4B;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;gBAAG,KAAK;oBAAE,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0D;gBAAG,KAAK;oBAAE,QAAQ;gBAA4D;YAAE;YAAG,SAAS;QAAwB;QAAG,oBAAoB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;gBAAG,KAAK;oBAAE,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,QAAQ;QAA0B;QAAG,sBAAsB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;YAAE;YAAG,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,SAAS,CAAC,oFAAoF,CAAC;oBAAE,OAAO;oBAAa,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,uDAAuD,CAAC;oBAAE,OAAO;oBAAa,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,kFAAkF,CAAC;oBAAE,OAAO;oBAAa,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAO,OAAO;YAAY,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA8C;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAqC,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,eAAe;YAAkB,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAO,OAAO;YAAW,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAAmB,QAAQ;QAAuB;QAAG,aAAa;YAAE,SAAS;YAAwF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,OAAO;oBAAa,QAAQ;oBAAc,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,iBAAiB;QAAC;QAAQ;QAAc;QAAc;QAAU;QAAQ;QAAO;QAAQ;QAAQ;QAAW;KAAM;AAAC;AACvswB,IAAI,QAAQ;OACP,kMAAA,CAAA,UAAI;OACJ,wMAAA,CAAA,UAAU;OACV,wMAAA,CAAA,UAAU;OACV,oMAAA,CAAA,UAAM;OACN,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,kMAAA,CAAA,UAAI;OACJ,kMAAA,CAAA,UAAI;OACJ,qMAAA,CAAA,UAAO;OACP,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}