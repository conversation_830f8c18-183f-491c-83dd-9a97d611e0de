{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/github-dark-high-contrast.mjs"], "sourcesContent": ["var githubDarkHighContrast = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#ff967d\",\n    \"activityBar.background\": \"#0a0c10\",\n    \"activityBar.border\": \"#7a828e\",\n    \"activityBar.foreground\": \"#f0f3f6\",\n    \"activityBar.inactiveForeground\": \"#f0f3f6\",\n    \"activityBarBadge.background\": \"#409eff\",\n    \"activityBarBadge.foreground\": \"#0a0c10\",\n    \"badge.background\": \"#409eff\",\n    \"badge.foreground\": \"#0a0c10\",\n    \"breadcrumb.activeSelectionForeground\": \"#f0f3f6\",\n    \"breadcrumb.focusForeground\": \"#f0f3f6\",\n    \"breadcrumb.foreground\": \"#f0f3f6\",\n    \"breadcrumbPicker.background\": \"#272b33\",\n    \"button.background\": \"#09b43a\",\n    \"button.foreground\": \"#0a0c10\",\n    \"button.hoverBackground\": \"#26cd4d\",\n    \"button.secondaryBackground\": \"#4c525d\",\n    \"button.secondaryForeground\": \"#f0f3f6\",\n    \"button.secondaryHoverBackground\": \"#525964\",\n    \"checkbox.background\": \"#272b33\",\n    \"checkbox.border\": \"#7a828e\",\n    \"debugConsole.errorForeground\": \"#ffb1af\",\n    \"debugConsole.infoForeground\": \"#bdc4cc\",\n    \"debugConsole.sourceForeground\": \"#f7c843\",\n    \"debugConsole.warningForeground\": \"#f0b72f\",\n    \"debugConsoleInputIcon.foreground\": \"#cb9eff\",\n    \"debugIcon.breakpointForeground\": \"#ff6a69\",\n    \"debugTokenExpression.boolean\": \"#4ae168\",\n    \"debugTokenExpression.error\": \"#ffb1af\",\n    \"debugTokenExpression.name\": \"#91cbff\",\n    \"debugTokenExpression.number\": \"#4ae168\",\n    \"debugTokenExpression.string\": \"#addcff\",\n    \"debugTokenExpression.value\": \"#addcff\",\n    \"debugToolBar.background\": \"#272b33\",\n    \"descriptionForeground\": \"#f0f3f6\",\n    \"diffEditor.insertedLineBackground\": \"#09b43a26\",\n    \"diffEditor.insertedTextBackground\": \"#26cd4d4d\",\n    \"diffEditor.removedLineBackground\": \"#ff6a6926\",\n    \"diffEditor.removedTextBackground\": \"#ff94924d\",\n    \"dropdown.background\": \"#272b33\",\n    \"dropdown.border\": \"#7a828e\",\n    \"dropdown.foreground\": \"#f0f3f6\",\n    \"dropdown.listBackground\": \"#272b33\",\n    \"editor.background\": \"#0a0c10\",\n    \"editor.findMatchBackground\": \"#e09b13\",\n    \"editor.findMatchHighlightBackground\": \"#fbd66980\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#09b43a\",\n    \"editor.foldBackground\": \"#9ea7b31a\",\n    \"editor.foreground\": \"#f0f3f6\",\n    \"editor.inactiveSelectionBackground\": \"#9ea7b3\",\n    \"editor.lineHighlightBackground\": \"#9ea7b31a\",\n    \"editor.lineHighlightBorder\": \"#71b7ff\",\n    \"editor.linkedEditingBackground\": \"#71b7ff12\",\n    \"editor.selectionBackground\": \"#ffffff\",\n    \"editor.selectionForeground\": \"#0a0c10\",\n    \"editor.selectionHighlightBackground\": \"#26cd4d40\",\n    \"editor.stackFrameHighlightBackground\": \"#e09b13\",\n    \"editor.wordHighlightBackground\": \"#9ea7b380\",\n    \"editor.wordHighlightBorder\": \"#9ea7b399\",\n    \"editor.wordHighlightStrongBackground\": \"#9ea7b34d\",\n    \"editor.wordHighlightStrongBorder\": \"#9ea7b399\",\n    \"editorBracketHighlight.foreground1\": \"#91cbff\",\n    \"editorBracketHighlight.foreground2\": \"#4ae168\",\n    \"editorBracketHighlight.foreground3\": \"#f7c843\",\n    \"editorBracketHighlight.foreground4\": \"#ffb1af\",\n    \"editorBracketHighlight.foreground5\": \"#ffadd4\",\n    \"editorBracketHighlight.foreground6\": \"#dbb7ff\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#f0f3f6\",\n    \"editorBracketMatch.background\": \"#26cd4d40\",\n    \"editorBracketMatch.border\": \"#26cd4d99\",\n    \"editorCursor.foreground\": \"#71b7ff\",\n    \"editorGroup.border\": \"#7a828e\",\n    \"editorGroupHeader.tabsBackground\": \"#010409\",\n    \"editorGroupHeader.tabsBorder\": \"#7a828e\",\n    \"editorGutter.addedBackground\": \"#09b43a\",\n    \"editorGutter.deletedBackground\": \"#ff6a69\",\n    \"editorGutter.modifiedBackground\": \"#e09b13\",\n    \"editorIndentGuide.activeBackground\": \"#f0f3f63d\",\n    \"editorIndentGuide.background\": \"#f0f3f61f\",\n    \"editorInlayHint.background\": \"#bdc4cc33\",\n    \"editorInlayHint.foreground\": \"#f0f3f6\",\n    \"editorInlayHint.paramBackground\": \"#bdc4cc33\",\n    \"editorInlayHint.paramForeground\": \"#f0f3f6\",\n    \"editorInlayHint.typeBackground\": \"#bdc4cc33\",\n    \"editorInlayHint.typeForeground\": \"#f0f3f6\",\n    \"editorLineNumber.activeForeground\": \"#f0f3f6\",\n    \"editorLineNumber.foreground\": \"#9ea7b3\",\n    \"editorOverviewRuler.border\": \"#010409\",\n    \"editorWhitespace.foreground\": \"#7a828e\",\n    \"editorWidget.background\": \"#272b33\",\n    \"errorForeground\": \"#ff6a69\",\n    \"focusBorder\": \"#409eff\",\n    \"foreground\": \"#f0f3f6\",\n    \"gitDecoration.addedResourceForeground\": \"#26cd4d\",\n    \"gitDecoration.conflictingResourceForeground\": \"#e7811d\",\n    \"gitDecoration.deletedResourceForeground\": \"#ff6a69\",\n    \"gitDecoration.ignoredResourceForeground\": \"#9ea7b3\",\n    \"gitDecoration.modifiedResourceForeground\": \"#f0b72f\",\n    \"gitDecoration.submoduleResourceForeground\": \"#f0f3f6\",\n    \"gitDecoration.untrackedResourceForeground\": \"#26cd4d\",\n    \"icon.foreground\": \"#f0f3f6\",\n    \"input.background\": \"#0a0c10\",\n    \"input.border\": \"#7a828e\",\n    \"input.foreground\": \"#f0f3f6\",\n    \"input.placeholderForeground\": \"#9ea7b3\",\n    \"keybindingLabel.foreground\": \"#f0f3f6\",\n    \"list.activeSelectionBackground\": \"#9ea7b366\",\n    \"list.activeSelectionForeground\": \"#f0f3f6\",\n    \"list.focusBackground\": \"#409eff26\",\n    \"list.focusForeground\": \"#f0f3f6\",\n    \"list.highlightForeground\": \"#71b7ff\",\n    \"list.hoverBackground\": \"#9ea7b31a\",\n    \"list.hoverForeground\": \"#f0f3f6\",\n    \"list.inactiveFocusBackground\": \"#409eff26\",\n    \"list.inactiveSelectionBackground\": \"#9ea7b366\",\n    \"list.inactiveSelectionForeground\": \"#f0f3f6\",\n    \"minimapSlider.activeBackground\": \"#bdc4cc47\",\n    \"minimapSlider.background\": \"#bdc4cc33\",\n    \"minimapSlider.hoverBackground\": \"#bdc4cc3d\",\n    \"notificationCenterHeader.background\": \"#272b33\",\n    \"notificationCenterHeader.foreground\": \"#f0f3f6\",\n    \"notifications.background\": \"#272b33\",\n    \"notifications.border\": \"#7a828e\",\n    \"notifications.foreground\": \"#f0f3f6\",\n    \"notificationsErrorIcon.foreground\": \"#ff6a69\",\n    \"notificationsInfoIcon.foreground\": \"#71b7ff\",\n    \"notificationsWarningIcon.foreground\": \"#f0b72f\",\n    \"panel.background\": \"#010409\",\n    \"panel.border\": \"#7a828e\",\n    \"panelInput.border\": \"#7a828e\",\n    \"panelTitle.activeBorder\": \"#ff967d\",\n    \"panelTitle.activeForeground\": \"#f0f3f6\",\n    \"panelTitle.inactiveForeground\": \"#f0f3f6\",\n    \"peekViewEditor.background\": \"#9ea7b31a\",\n    \"peekViewEditor.matchHighlightBackground\": \"#e09b13\",\n    \"peekViewResult.background\": \"#0a0c10\",\n    \"peekViewResult.matchHighlightBackground\": \"#e09b13\",\n    \"pickerGroup.border\": \"#7a828e\",\n    \"pickerGroup.foreground\": \"#f0f3f6\",\n    \"progressBar.background\": \"#409eff\",\n    \"quickInput.background\": \"#272b33\",\n    \"quickInput.foreground\": \"#f0f3f6\",\n    \"scrollbar.shadow\": \"#7a828e33\",\n    \"scrollbarSlider.activeBackground\": \"#bdc4cc47\",\n    \"scrollbarSlider.background\": \"#bdc4cc33\",\n    \"scrollbarSlider.hoverBackground\": \"#bdc4cc3d\",\n    \"settings.headerForeground\": \"#f0f3f6\",\n    \"settings.modifiedItemIndicator\": \"#e09b13\",\n    \"sideBar.background\": \"#010409\",\n    \"sideBar.border\": \"#7a828e\",\n    \"sideBar.foreground\": \"#f0f3f6\",\n    \"sideBarSectionHeader.background\": \"#010409\",\n    \"sideBarSectionHeader.border\": \"#7a828e\",\n    \"sideBarSectionHeader.foreground\": \"#f0f3f6\",\n    \"sideBarTitle.foreground\": \"#f0f3f6\",\n    \"statusBar.background\": \"#0a0c10\",\n    \"statusBar.border\": \"#7a828e\",\n    \"statusBar.debuggingBackground\": \"#ff6a69\",\n    \"statusBar.debuggingForeground\": \"#0a0c10\",\n    \"statusBar.focusBorder\": \"#409eff80\",\n    \"statusBar.foreground\": \"#f0f3f6\",\n    \"statusBar.noFolderBackground\": \"#0a0c10\",\n    \"statusBarItem.activeBackground\": \"#f0f3f61f\",\n    \"statusBarItem.focusBorder\": \"#409eff\",\n    \"statusBarItem.hoverBackground\": \"#f0f3f614\",\n    \"statusBarItem.prominentBackground\": \"#9ea7b366\",\n    \"statusBarItem.remoteBackground\": \"#525964\",\n    \"statusBarItem.remoteForeground\": \"#f0f3f6\",\n    \"symbolIcon.arrayForeground\": \"#fe9a2d\",\n    \"symbolIcon.booleanForeground\": \"#71b7ff\",\n    \"symbolIcon.classForeground\": \"#fe9a2d\",\n    \"symbolIcon.colorForeground\": \"#91cbff\",\n    \"symbolIcon.constantForeground\": [\n      \"#acf7b6\",\n      \"#72f088\",\n      \"#4ae168\",\n      \"#26cd4d\",\n      \"#09b43a\",\n      \"#09b43a\",\n      \"#02a232\",\n      \"#008c2c\",\n      \"#007728\",\n      \"#006222\"\n    ],\n    \"symbolIcon.constructorForeground\": \"#dbb7ff\",\n    \"symbolIcon.enumeratorForeground\": \"#fe9a2d\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#71b7ff\",\n    \"symbolIcon.eventForeground\": \"#9ea7b3\",\n    \"symbolIcon.fieldForeground\": \"#fe9a2d\",\n    \"symbolIcon.fileForeground\": \"#f0b72f\",\n    \"symbolIcon.folderForeground\": \"#f0b72f\",\n    \"symbolIcon.functionForeground\": \"#cb9eff\",\n    \"symbolIcon.interfaceForeground\": \"#fe9a2d\",\n    \"symbolIcon.keyForeground\": \"#71b7ff\",\n    \"symbolIcon.keywordForeground\": \"#ff9492\",\n    \"symbolIcon.methodForeground\": \"#cb9eff\",\n    \"symbolIcon.moduleForeground\": \"#ff9492\",\n    \"symbolIcon.namespaceForeground\": \"#ff9492\",\n    \"symbolIcon.nullForeground\": \"#71b7ff\",\n    \"symbolIcon.numberForeground\": \"#26cd4d\",\n    \"symbolIcon.objectForeground\": \"#fe9a2d\",\n    \"symbolIcon.operatorForeground\": \"#91cbff\",\n    \"symbolIcon.packageForeground\": \"#fe9a2d\",\n    \"symbolIcon.propertyForeground\": \"#fe9a2d\",\n    \"symbolIcon.referenceForeground\": \"#71b7ff\",\n    \"symbolIcon.snippetForeground\": \"#71b7ff\",\n    \"symbolIcon.stringForeground\": \"#91cbff\",\n    \"symbolIcon.structForeground\": \"#fe9a2d\",\n    \"symbolIcon.textForeground\": \"#91cbff\",\n    \"symbolIcon.typeParameterForeground\": \"#91cbff\",\n    \"symbolIcon.unitForeground\": \"#71b7ff\",\n    \"symbolIcon.variableForeground\": \"#fe9a2d\",\n    \"tab.activeBackground\": \"#0a0c10\",\n    \"tab.activeBorder\": \"#0a0c10\",\n    \"tab.activeBorderTop\": \"#ff967d\",\n    \"tab.activeForeground\": \"#f0f3f6\",\n    \"tab.border\": \"#7a828e\",\n    \"tab.hoverBackground\": \"#0a0c10\",\n    \"tab.inactiveBackground\": \"#010409\",\n    \"tab.inactiveForeground\": \"#f0f3f6\",\n    \"tab.unfocusedActiveBorder\": \"#0a0c10\",\n    \"tab.unfocusedActiveBorderTop\": \"#7a828e\",\n    \"tab.unfocusedHoverBackground\": \"#9ea7b31a\",\n    \"terminal.ansiBlack\": \"#7a828e\",\n    \"terminal.ansiBlue\": \"#71b7ff\",\n    \"terminal.ansiBrightBlack\": \"#9ea7b3\",\n    \"terminal.ansiBrightBlue\": \"#91cbff\",\n    \"terminal.ansiBrightCyan\": \"#56d4dd\",\n    \"terminal.ansiBrightGreen\": \"#4ae168\",\n    \"terminal.ansiBrightMagenta\": \"#dbb7ff\",\n    \"terminal.ansiBrightRed\": \"#ffb1af\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#f7c843\",\n    \"terminal.ansiCyan\": \"#39c5cf\",\n    \"terminal.ansiGreen\": \"#26cd4d\",\n    \"terminal.ansiMagenta\": \"#cb9eff\",\n    \"terminal.ansiRed\": \"#ff9492\",\n    \"terminal.ansiWhite\": \"#d9dee3\",\n    \"terminal.ansiYellow\": \"#f0b72f\",\n    \"terminal.foreground\": \"#f0f3f6\",\n    \"textBlockQuote.background\": \"#010409\",\n    \"textBlockQuote.border\": \"#7a828e\",\n    \"textCodeBlock.background\": \"#9ea7b366\",\n    \"textLink.activeForeground\": \"#71b7ff\",\n    \"textLink.foreground\": \"#71b7ff\",\n    \"textPreformat.foreground\": \"#f0f3f6\",\n    \"textSeparator.foreground\": \"#7a828e\",\n    \"titleBar.activeBackground\": \"#0a0c10\",\n    \"titleBar.activeForeground\": \"#f0f3f6\",\n    \"titleBar.border\": \"#7a828e\",\n    \"titleBar.inactiveBackground\": \"#010409\",\n    \"titleBar.inactiveForeground\": \"#f0f3f6\",\n    \"tree.indentGuidesStroke\": \"#7a828e\",\n    \"welcomePage.buttonBackground\": \"#272b33\",\n    \"welcomePage.buttonHoverBackground\": \"#525964\"\n  },\n  \"displayName\": \"GitHub Dark High Contrast\",\n  \"name\": \"github-dark-high-contrast\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bdc4cc\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9492\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.other.constant\",\n        \"variable.other.enummember\",\n        \"variable.language\",\n        \"entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"meta.export.default\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb757\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function\",\n        \"meta.jsx.children\",\n        \"meta.block\",\n        \"meta.tag.attributes\",\n        \"entity.name.constant\",\n        \"meta.object.member\",\n        \"meta.embedded.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f0f3f6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#dbb7ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#72f088\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#ff9492\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9492\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f0f3f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#addcff\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#ffb757\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#f0f3f6\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#ff9492\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#addcff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#addcff\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#72f088\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#72f088\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffb757\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#72f088\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f0f3f6\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f0f3f6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strikethrough\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#ad0116\",\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9492\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#006222\",\n        \"foreground\": \"#72f088\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#a74c00\",\n        \"foreground\": \"#ffb757\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#91cbff\",\n        \"foreground\": \"#272b33\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#dbb7ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#91cbff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bdc4cc\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#ffb1af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#addcff\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { githubDarkHighContrast as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,yBAAyB,OAAO,MAAM,CAAC;IACzC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,kCAAkC;QAClC,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}