{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/po.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Gettext PO\", \"fileTypes\": [\"po\", \"pot\", \"potx\"], \"name\": \"po\", \"patterns\": [{ \"begin\": '^(?=(msgid(_plural)?|msgctxt)\\\\s*\"[^\"])|^\\\\s*$', \"comment\": \"Start of body of document, after header\", \"end\": \"\\\\z\", \"patterns\": [{ \"include\": \"#body\" }] }, { \"include\": \"#comments\" }, { \"match\": '^msg(id|str)\\\\s+\"\"\\\\s*$\\\\n?', \"name\": \"comment.line.number-sign.po\" }, { \"captures\": { \"1\": { \"name\": \"constant.language.po\" }, \"2\": { \"name\": \"punctuation.separator.key-value.po\" }, \"3\": { \"name\": \"string.other.po\" } }, \"match\": '^\"(?:([^\\\\s:]+)(:)\\\\s+)?([^\"]*)\"\\\\s*$\\\\n?', \"name\": \"meta.header.po\" }], \"repository\": { \"body\": { \"patterns\": [{ \"begin\": \"^(msgid(_plural)?)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.msgid.po\" } }, \"end\": '^(?!\")', \"name\": \"meta.scope.msgid.po\", \"patterns\": [{ \"begin\": '(\\\\G|^)\"', \"end\": '\"', \"name\": \"string.quoted.double.po\", \"patterns\": [{ \"match\": '\\\\\\\\[\\\\\\\\\"]', \"name\": \"constant.character.escape.po\" }] }] }, { \"begin\": \"^(msgstr)(?:(\\\\[)(\\\\d+)(\\\\]))?\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.msgstr.po\" }, \"2\": { \"name\": \"keyword.control.msgstr.po\" }, \"3\": { \"name\": \"constant.numeric.po\" }, \"4\": { \"name\": \"keyword.control.msgstr.po\" } }, \"end\": '^(?!\")', \"name\": \"meta.scope.msgstr.po\", \"patterns\": [{ \"begin\": '(\\\\G|^)\"', \"end\": '\"', \"name\": \"string.quoted.double.po\", \"patterns\": [{ \"match\": '\\\\\\\\[\\\\\\\\\"]', \"name\": \"constant.character.escape.po\" }] }] }, { \"begin\": \"^(msgctxt)(?:(\\\\[)(\\\\d+)(\\\\]))?\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.msgctxt.po\" }, \"2\": { \"name\": \"keyword.control.msgctxt.po\" }, \"3\": { \"name\": \"constant.numeric.po\" }, \"4\": { \"name\": \"keyword.control.msgctxt.po\" } }, \"end\": '^(?!\")', \"name\": \"meta.scope.msgctxt.po\", \"patterns\": [{ \"begin\": '(\\\\G|^)\"', \"end\": '\"', \"name\": \"string.quoted.double.po\", \"patterns\": [{ \"match\": '\\\\\\\\[\\\\\\\\\"]', \"name\": \"constant.character.escape.po\" }] }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.po\" } }, \"match\": \"^(#~).*$\\\\n?\", \"name\": \"comment.line.number-sign.obsolete.po\" }, { \"include\": \"#comments\" }, { \"comment\": 'a line that does not begin with # or \". Could improve this regexp', \"match\": '^(?!\\\\s*$)[^#\"].*$\\\\n?', \"name\": \"invalid.illegal.po\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"^(?=#)\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(#,)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.po\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.flag.po\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.type.flag.po\" } }, \"match\": \"(?:\\\\G|,\\\\s*)((?:fuzzy)|(?:no-)?(?:c|objc|sh|lisp|elisp|librep|scheme|smalltalk|java|csharp|awk|object-pascal|ycp|tcl|perl|perl-brace|php|gcc-internal|qt|boost)-format)\" }] }, { \"begin\": \"#\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.po\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.extracted.po\" }, { \"begin\": \"(#:)[ \\\\t]*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.po\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.reference.po\", \"patterns\": [{ \"match\": \"(\\\\S+:)([\\\\d;]*)\", \"name\": \"storage.type.class.po\" }] }, { \"begin\": \"#\\\\|\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.po\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.previous.po\" }, { \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.po\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.po\" }] }] } }, \"scopeName\": \"source.po\", \"aliases\": [\"pot\", \"potx\"] });\nvar po = [\n  lang\n];\n\nexport { po as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,aAAa;QAAC;QAAM;QAAO;KAAO;IAAE,QAAQ;IAAM,YAAY;QAAC;YAAE,SAAS;YAAkD,WAAW;YAA2C,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAA+B,QAAQ;QAA8B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkB;YAAE;YAAG,SAAS;YAA6C,QAAQ;QAAiB;KAAE;IAAE,cAAc;QAAE,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAU,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,SAAS;4BAAY,OAAO;4BAAK,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,SAAS;oCAAe,QAAQ;gCAA+B;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAU,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS;4BAAY,OAAO;4BAAK,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,SAAS;oCAAe,QAAQ;gCAA+B;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAU,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,SAAS;4BAAY,OAAO;4BAAK,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,SAAS;oCAAe,QAAQ;gCAA+B;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAAgB,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;oBAAqE,SAAS;oBAA0B,QAAQ;gBAAqB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAY,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA2B;oCAAE;oCAAG,SAAS;gCAA2K;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAQ,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAwC;wBAAG;4BAAE,SAAS;4BAAe,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;4BAAyC,YAAY;gCAAC;oCAAE,SAAS;oCAAoB,QAAQ;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAQ,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAuC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAa,WAAW;QAAC;QAAO;KAAO;AAAC;AACv8G,IAAI,KAAK;IACP;CACD", "ignoreList": [0], "debugId": null}}]}