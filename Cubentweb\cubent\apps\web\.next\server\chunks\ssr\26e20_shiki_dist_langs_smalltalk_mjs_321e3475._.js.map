{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/smalltalk.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Smalltalk\", \"fileTypes\": [\"st\"], \"foldingStartMarker\": \"\\\\[\", \"foldingStopMarker\": \"^\\\\s*\\\\]|^\\\\s\\\\]\", \"name\": \"smalltalk\", \"patterns\": [{ \"match\": \"\\\\$.\", \"name\": \"constant.character.smalltalk\" }, { \"match\": \"\\\\b(class)\\\\b\", \"name\": \"storage.type.$1.smalltalk\" }, { \"match\": \"\\\\b(extend|super|self)\\\\b\", \"name\": \"storage.modifier.$1.smalltalk\" }, { \"match\": \"\\\\b(yourself|new|Smalltalk)\\\\b\", \"name\": \"keyword.control.$1.smalltalk\" }, { \"match\": \":=\", \"name\": \"keyword.operator.assignment.smalltalk\" }, { \"comment\": \"Parse the variable declaration like: |a b c|\", \"match\": \"/^:\\\\w*\\\\s*\\\\|/\", \"name\": \"constant.other.block.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.instance-variables.begin.smalltalk\" }, \"2\": { \"patterns\": [{ \"match\": \"\\\\w+\", \"name\": \"support.type.variable.declaration.smalltalk\" }] }, \"3\": { \"name\": \"punctuation.definition.instance-variables.end.smalltalk\" } }, \"match\": \"(\\\\|)(\\\\s*\\\\w[\\\\w ]*)(\\\\|)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \":\\\\w+\", \"name\": \"entity.name.function.block.smalltalk\" }] } }, \"comment\": \"Parse the blocks like: [ :a :b | ...... ]\", \"match\": \"\\\\[((\\\\s+|:\\\\w+)*)\\\\|\" }, { \"include\": \"#numeric\" }, { \"match\": \"<(?!<|=)|>(?!<|=|>)|<=|>=|=|==|~=|~~|>>|\\\\^\", \"name\": \"keyword.operator.comparison.smalltalk\" }, { \"match\": \"(\\\\*|\\\\+|-|/|\\\\\\\\)\", \"name\": \"keyword.operator.arithmetic.smalltalk\" }, { \"match\": \"(?<=[ \\\\t])!+|\\\\bnot\\\\b|&|\\\\band\\\\b|\\\\||\\\\bor\\\\b\", \"name\": \"keyword.operator.logical.smalltalk\" }, { \"comment\": \"Fake reserved word -> main Smalltalk messages\", \"match\": \"(?<!\\\\.)\\\\b(ensure|resume|retry|signal)\\\\b(?![?!])\", \"name\": \"keyword.control.smalltalk\" }, { \"comment\": \"Fake conditionals. Smalltalk Methods.\", \"match\": \"ifCurtailed:|ifTrue:|ifFalse:|whileFalse:|whileTrue:\", \"name\": \"keyword.control.conditionals.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.inherited-class.smalltalk\" }, \"3\": { \"name\": \"keyword.control.smalltalk\" }, \"4\": { \"name\": \"entity.name.type.class.smalltalk\" } }, \"match\": \"(\\\\w+)(\\\\s+(subclass:))\\\\s*(\\\\w*)\", \"name\": \"meta.class.smalltalk\" }, { \"begin\": '\"', \"beginCaptures\": [{ \"name\": \"punctuation.definition.comment.begin.smalltalk\" }], \"end\": '\"', \"endCaptures\": [{ \"name\": \"punctuation.definition.comment.end.smalltalk\" }], \"name\": \"comment.block.smalltalk\" }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.smalltalk\" }, { \"match\": \"\\\\b(nil)\\\\b\", \"name\": \"constant.language.nil.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.smalltalk\" } }, \"comment\": \"messages/methods\", \"match\": \"(?>[a-zA-Z_]\\\\w*(?>[?!])?)(:)(?!:)\", \"name\": \"constant.other.messages.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.smalltalk\" } }, \"comment\": \"symbols\", \"match\": \"(#)[a-zA-Z_][a-zA-Z0-9_:]*\", \"name\": \"constant.other.symbol.smalltalk\" }, { \"begin\": \"#\\\\[\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.constant.begin.smalltalk\" }], \"end\": \"\\\\]\", \"endCaptures\": [{ \"name\": \"punctuation.definition.constant.end.smalltalk\" }], \"name\": \"meta.array.byte.smalltalk\", \"patterns\": [{ \"match\": \"\\\\d+(r[a-zA-Z0-9]+)?\", \"name\": \"constant.numeric.integer.smalltalk\" }, { \"match\": \"[^\\\\s\\\\]]+\", \"name\": \"invalid.illegal.character-not-allowed-here.smalltalk\" }] }, { \"begin\": \"#\\\\(\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.constant.begin.smalltalk\" }], \"comment\": \"Array Constructor\", \"end\": \"\\\\)\", \"endCaptures\": [{ \"name\": \"punctuation.definition.constant.end.smalltalk\" }], \"name\": \"constant.other.array.smalltalk\" }, { \"begin\": \"'\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.string.begin.smalltalk\" }], \"end\": \"'\", \"endCaptures\": [{ \"name\": \"punctuation.definition.string.end.smalltalk\" }], \"name\": \"string.quoted.single.smalltalk\" }, { \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"variable.other.constant.smalltalk\" }], \"repository\": { \"numeric\": { \"patterns\": [{ \"match\": \"(?<!\\\\w)\\\\d+\\\\.\\\\d+s\\\\d*\", \"name\": \"constant.numeric.float.scaled.smalltalk\" }, { \"match\": \"(?<!\\\\w)\\\\d+\\\\.\\\\d+([edq]-?\\\\d+)?\", \"name\": \"constant.numeric.float.smalltalk\" }, { \"match\": \"(?<!\\\\w)-?\\\\d+r[a-zA-Z0-9]+\", \"name\": \"constant.numeric.integer.radix.smalltalk\" }, { \"match\": \"(?<!\\\\w)-?\\\\d+([edq]-?\\\\d+)?\", \"name\": \"constant.numeric.integer.smalltalk\" }] } }, \"scopeName\": \"source.smalltalk\" });\nvar smalltalk = [\n  lang\n];\n\nexport { smalltalk as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAa,aAAa;QAAC;KAAK;IAAE,sBAAsB;IAAO,qBAAqB;IAAoB,QAAQ;IAAa,YAAY;QAAC;YAAE,SAAS;YAAQ,QAAQ;QAA+B;QAAG;YAAE,SAAS;YAAiB,QAAQ;QAA4B;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAAkC,QAAQ;QAA+B;QAAG;YAAE,SAAS;YAAM,QAAQ;QAAwC;QAAG;YAAE,WAAW;YAAgD,SAAS;YAAmB,QAAQ;QAAiC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAQ,QAAQ;wBAA8C;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,SAAS;QAA6B;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAuC;qBAAE;gBAAC;YAAE;YAAG,WAAW;YAA6C,SAAS;QAAwB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,SAAS;YAA+C,QAAQ;QAAwC;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAAwC;QAAG;YAAE,SAAS;YAAoD,QAAQ;QAAqC;QAAG;YAAE,WAAW;YAAiD,SAAS;YAAsD,QAAQ;QAA4B;QAAG;YAAE,WAAW;YAAyC,SAAS;YAAwD,QAAQ;QAAyC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;YAAqC,QAAQ;QAAuB;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAC;oBAAE,QAAQ;gBAAiD;aAAE;YAAE,OAAO;YAAK,eAAe;gBAAC;oBAAE,QAAQ;gBAA+C;aAAE;YAAE,QAAQ;QAA0B;QAAG;YAAE,SAAS;YAAsB,QAAQ;QAAsC;QAAG;YAAE,SAAS;YAAe,QAAQ;QAAkC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,WAAW;YAAoB,SAAS;YAAsC,QAAQ;QAAoC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,WAAW;YAAW,SAAS;YAA8B,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAQ,iBAAiB;gBAAC;oBAAE,QAAQ;gBAAkD;aAAE;YAAE,OAAO;YAAO,eAAe;gBAAC;oBAAE,QAAQ;gBAAgD;aAAE;YAAE,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAuD;aAAE;QAAC;QAAG;YAAE,SAAS;YAAQ,iBAAiB;gBAAC;oBAAE,QAAQ;gBAAkD;aAAE;YAAE,WAAW;YAAqB,OAAO;YAAO,eAAe;gBAAC;oBAAE,QAAQ;gBAAgD;aAAE;YAAE,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAC;oBAAE,QAAQ;gBAAgD;aAAE;YAAE,OAAO;YAAK,eAAe;gBAAC;oBAAE,QAAQ;gBAA8C;aAAE;YAAE,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAmB,QAAQ;QAAoC;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAqC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAmB;AAC5uI,IAAI,YAAY;IACd;CACD", "ignoreList": [0], "debugId": null}}]}