{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/fluent.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Fluent\", \"name\": \"fluent\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#message\" }, { \"include\": \"#wrong-line\" }], \"repository\": { \"attributes\": { \"begin\": \"\\\\s*(\\\\.[a-zA-Z][a-zA-Z0-9_-]*\\\\s*=\\\\s*)\", \"beginCaptures\": { \"1\": { \"name\": \"support.class.attribute-begin.fluent\" } }, \"end\": \"^(?=\\\\s*[^\\\\.])\", \"patterns\": [{ \"include\": \"#placeable\" }] }, \"comment\": { \"match\": \"^##?#?\\\\s.*$\", \"name\": \"comment.fluent\" }, \"function-comma\": { \"match\": \",\", \"name\": \"support.function.function-comma.fluent\" }, \"function-named-argument\": { \"begin\": '([a-zA-Z0-9]+:)\\\\s*([\"a-zA-Z0-9]+)', \"beginCaptures\": { \"1\": { \"name\": \"support.function.named-argument.name.fluent\" }, \"2\": { \"name\": \"variable.other.named-argument.value.fluent\" } }, \"end\": \"(?=\\\\)|,|\\\\s)\", \"name\": \"variable.other.named-argument.fluent\" }, \"function-positional-argument\": { \"match\": \"\\\\$[a-zA-Z0-9_-]+\", \"name\": \"variable.other.function.positional-argument.fluent\" }, \"invalid-placeable-string-missing-end-quote\": { \"match\": '\"[^\"]+$', \"name\": \"invalid.illegal.wrong-placeable-missing-end-quote.fluent\" }, \"invalid-placeable-wrong-placeable-missing-end\": { \"match\": \"([^}A-Z]*$|[^-][^>]$)\\\\b\", \"name\": \"invalid.illegal.wrong-placeable-missing-end.fluent\" }, \"message\": { \"begin\": \"^(-?[a-zA-Z][a-zA-Z0-9_-]*\\\\s*=\\\\s*)\", \"beginCaptures\": { \"1\": { \"name\": \"support.class.message-identifier.fluent\" } }, \"contentName\": \"string.fluent\", \"end\": \"^(?=\\\\S)\", \"patterns\": [{ \"include\": \"#attributes\" }, { \"include\": \"#placeable\" }] }, \"placeable\": { \"begin\": \"({)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.placeable.begin.fluent\" } }, \"contentName\": \"variable.other.placeable.content.fluent\", \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.placeable.end.fluent\" } }, \"patterns\": [{ \"include\": \"#placeable-string\" }, { \"include\": \"#placeable-function\" }, { \"include\": \"#placeable-reference-or-number\" }, { \"include\": \"#selector\" }, { \"include\": \"#invalid-placeable-wrong-placeable-missing-end\" }, { \"include\": \"#invalid-placeable-string-missing-end-quote\" }, { \"include\": \"#invalid-placeable-wrong-function-name\" }] }, \"placeable-function\": { \"begin\": \"([A-Z][A-Z0-9_-]*\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.placeable-function.call.begin.fluent\" } }, \"contentName\": \"string.placeable-function.fluent\", \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"support.function.placeable-function.call.end.fluent\" } }, \"patterns\": [{ \"include\": \"#function-comma\" }, { \"include\": \"#function-positional-argument\" }, { \"include\": \"#function-named-argument\" }] }, \"placeable-reference-or-number\": { \"match\": \"((-|\\\\$)[a-zA-Z0-9_-]+|[a-zA-Z][a-zA-Z0-9_-]*|\\\\d+)\", \"name\": \"variable.other.placeable.reference-or-number.fluent\" }, \"placeable-string\": { \"begin\": '(\")(?=[^\\\\n]*\")', \"beginCaptures\": { \"1\": { \"name\": \"variable.other.placeable-string-begin.fluent\" } }, \"contentName\": \"string.placeable-string-content.fluent\", \"end\": '(\")', \"endCaptures\": { \"1\": { \"name\": \"variable.other.placeable-string-end.fluent\" } } }, \"selector\": { \"begin\": \"(->)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.selector.begin.fluent\" } }, \"contentName\": \"string.selector.content.fluent\", \"end\": \"^(?=\\\\s*})\", \"patterns\": [{ \"include\": \"#selector-item\" }] }, \"selector-item\": { \"begin\": \"(\\\\s*\\\\*?\\\\[)([a-zA-Z0-9_-]+)(\\\\]\\\\s*)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.selector-item.begin.fluent\" }, \"2\": { \"name\": \"variable.other.selector-item.begin.fluent\" }, \"3\": { \"name\": \"support.function.selector-item.begin.fluent\" } }, \"contentName\": \"string.selector-item.content.fluent\", \"end\": \"^(?=(\\\\s*})|(\\\\s*\\\\[)|(\\\\s*\\\\*))\", \"patterns\": [{ \"include\": \"#placeable\" }] }, \"wrong-line\": { \"match\": \".*\", \"name\": \"invalid.illegal.wrong-line.fluent\" } }, \"scopeName\": \"source.ftl\", \"aliases\": [\"ftl\"] });\nvar fluent = [\n  lang\n];\n\nexport { fluent as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,QAAQ;IAAU,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAc;KAAE;IAAE,cAAc;QAAE,cAAc;YAAE,SAAS;YAA4C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAgB,QAAQ;QAAiB;QAAG,kBAAkB;YAAE,SAAS;YAAK,QAAQ;QAAyC;QAAG,2BAA2B;YAAE,SAAS;YAAsC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAiB,QAAQ;QAAuC;QAAG,gCAAgC;YAAE,SAAS;YAAqB,QAAQ;QAAqD;QAAG,8CAA8C;YAAE,SAAS;YAAW,QAAQ;QAA2D;QAAG,iDAAiD;YAAE,SAAS;YAA4B,QAAQ;QAAqD;QAAG,WAAW;YAAE,SAAS;YAAwC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,eAAe;YAAiB,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,eAAe;YAA2C,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiD;gBAAG;oBAAE,WAAW;gBAA8C;gBAAG;oBAAE,WAAW;gBAAyC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwD;YAAE;YAAG,eAAe;YAAoC,OAAO;YAAS,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAAuD,QAAQ;QAAsD;QAAG,oBAAoB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,eAAe;YAA0C,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;QAAE;QAAG,YAAY;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,eAAe;YAAkC,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,eAAe;YAAuC,OAAO;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAM,QAAQ;QAAoC;IAAE;IAAG,aAAa;IAAc,WAAW;QAAC;KAAM;AAAC;AAC7uH,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}