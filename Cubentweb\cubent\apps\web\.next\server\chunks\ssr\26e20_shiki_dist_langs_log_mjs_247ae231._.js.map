{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/log.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Log file\", \"fileTypes\": [\"log\"], \"name\": \"log\", \"patterns\": [{ \"match\": \"\\\\b(Trace)\\\\b:\", \"name\": \"comment log.verbose\" }, { \"match\": \"(?i)\\\\[(verbose|verb|vrb|vb|v)\\\\]\", \"name\": \"comment log.verbose\" }, { \"match\": \"(?<=^[\\\\s\\\\d\\\\p]*)\\\\bV\\\\b\", \"name\": \"comment log.verbose\" }, { \"match\": \"\\\\b(DEBUG|Debug)\\\\b|(?i)\\\\b(debug):\", \"name\": \"markup.changed log.debug\" }, { \"match\": \"(?i)\\\\[(debug|dbug|dbg|de|d)\\\\]\", \"name\": \"markup.changed log.debug\" }, { \"match\": \"(?<=^[\\\\s\\\\d\\\\p]*)\\\\bD\\\\b\", \"name\": \"markup.changed log.debug\" }, { \"match\": \"\\\\b(HINT|INFO|INFORMATION|Info|NOTICE|II)\\\\b|(?i)\\\\b(info|information):\", \"name\": \"markup.inserted log.info\" }, { \"match\": \"(?i)\\\\[(information|info|inf|in|i)\\\\]\", \"name\": \"markup.inserted log.info\" }, { \"match\": \"(?<=^[\\\\s\\\\d\\\\p]*)\\\\bI\\\\b\", \"name\": \"markup.inserted log.info\" }, { \"match\": \"\\\\b(WARNING|WARN|Warn|WW)\\\\b|(?i)\\\\b(warning):\", \"name\": \"markup.deleted log.warning\" }, { \"match\": \"(?i)\\\\[(warning|warn|wrn|wn|w)\\\\]\", \"name\": \"markup.deleted log.warning\" }, { \"match\": \"(?<=^[\\\\s\\\\d\\\\p]*)\\\\bW\\\\b\", \"name\": \"markup.deleted log.warning\" }, { \"match\": \"\\\\b(ALERT|CRITICAL|EMERGENCY|ERROR|FAILURE|FAIL|Fatal|FATAL|Error|EE)\\\\b|(?i)\\\\b(error):\", \"name\": \"string.regexp, strong log.error\" }, { \"match\": \"(?i)\\\\[(error|eror|err|er|e|fatal|fatl|ftl|fa|f)\\\\]\", \"name\": \"string.regexp, strong log.error\" }, { \"match\": \"(?<=^[\\\\s\\\\d\\\\p]*)\\\\bE\\\\b\", \"name\": \"string.regexp, strong log.error\" }, { \"match\": \"\\\\b\\\\d{4}-\\\\d{2}-\\\\d{2}(T|\\\\b)\", \"name\": \"comment log.date\" }, { \"match\": \"(?<=(^|\\\\s))\\\\d{2}[^\\\\w\\\\s]\\\\d{2}[^\\\\w\\\\s]\\\\d{4}\\\\b\", \"name\": \"comment log.date\" }, { \"match\": \"\\\\d{1,2}:\\\\d{2}(:\\\\d{2}([.,]\\\\d{1,})?)?(Z| ?[+-]\\\\d{1,2}:\\\\d{2})?\\\\b\", \"name\": \"comment log.date\" }, { \"match\": \"\\\\b([0-9a-fA-F]{40}|[0-9a-fA-F]{10}|[0-9a-fA-F]{7})\\\\b\", \"name\": \"constant.language\" }, { \"match\": \"\\\\b[0-9a-fA-F]{8}[-]?([0-9a-fA-F]{4}[-]?){3}[0-9a-fA-F]{12}\\\\b\", \"name\": \"constant.language log.constant\" }, { \"match\": \"\\\\b([0-9a-fA-F]{2,}[:-])+[0-9a-fA-F]{2,}+\\\\b\", \"name\": \"constant.language log.constant\" }, { \"match\": \"\\\\b(\\\\d+|true|false|null)\\\\b\", \"name\": \"constant.language log.constant\" }, { \"match\": \"\\\\b(0x[a-fA-F0-9]+)\\\\b\", \"name\": \"constant.language log.constant\" }, { \"match\": '\"[^\"]*\"', \"name\": \"string log.string\" }, { \"match\": \"(?<![\\\\w])'[^']*'\", \"name\": \"string log.string\" }, { \"match\": \"\\\\b([a-zA-Z.]*Exception)\\\\b\", \"name\": \"string.regexp, emphasis log.exceptiontype\" }, { \"begin\": \"^[\\\\t ]*at[\\\\t ]\", \"end\": \"$\", \"name\": \"string.key, emphasis log.exception\" }, { \"match\": \"\\\\b[a-z]+://\\\\S+\\\\b/?\", \"name\": \"constant.language log.constant\" }, { \"match\": \"(?<![\\\\w/\\\\\\\\])([\\\\w-]+\\\\.)+([\\\\w-])+(?![\\\\w/\\\\\\\\])\", \"name\": \"constant.language log.constant\" }], \"scopeName\": \"text.log\" });\nvar log = [\n  lang\n];\n\nexport { log as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAkB,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAqC,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAAsB;QAAG;YAAE,SAAS;YAAuC,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAmC,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAA2E,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAyC,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAA2B;QAAG;YAAE,SAAS;YAAkD,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAAqC,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAA6B;QAAG;YAAE,SAAS;YAA4F,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAuD,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAA6B,QAAQ;QAAkC;QAAG;YAAE,SAAS;YAAkC,QAAQ;QAAmB;QAAG;YAAE,SAAS;YAAuD,QAAQ;QAAmB;QAAG;YAAE,SAAS;YAAwE,QAAQ;QAAmB;QAAG;YAAE,SAAS;YAA0D,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAAkE,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAgD,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAgC,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAA0B,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAW,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAAqB,QAAQ;QAAoB;QAAG;YAAE,SAAS;YAA+B,QAAQ;QAA4C;QAAG;YAAE,SAAS;YAAoB,OAAO;YAAK,QAAQ;QAAqC;QAAG;YAAE,SAAS;YAAyB,QAAQ;QAAiC;QAAG;YAAE,SAAS;YAAuD,QAAQ;QAAiC;KAAE;IAAE,aAAa;AAAW;AACptF,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}