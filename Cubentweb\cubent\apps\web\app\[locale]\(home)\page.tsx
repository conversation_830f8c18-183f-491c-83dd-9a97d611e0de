import { showBetaFeature } from '@repo/feature-flags';
import { getDictionary } from '@repo/internationalization';
import { createMetadata } from '@repo/seo/metadata';
import type { Metadata } from 'next';
import { Community } from './components/community';
import { CTA } from './components/cta';
import { Download } from './components/download';
import { FAQ } from './components/faq';

import { Hero } from './components/hero';
import { Mockup } from './components/mockup';
import { ModelProviders } from './components/model-providers';
import { Stats } from './components/stats';
import { Testimonials } from './components/testimonials';
import { TrustedBy } from './components/trusted-by';

type HomeProps = {
  params: Promise<{
    locale: string;
  }>;
};

export const generateMetadata = async ({
  params,
}: HomeProps): Promise<Metadata> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return createMetadata(dictionary.web.home.meta);
};

const Home = async ({ params }: HomeProps) => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const betaFeature = await showBetaFeature();

  return (
    <>
      {betaFeature && (
        <div className="w-full bg-black py-2 text-center text-white">
          Beta feature now available
        </div>
      )}
      {/* Transparent box wrapper for Hero and TrustedBy sections */}
      <div className="w-full relative" style={{ backgroundColor: '#161616' }}>
        <div
          className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 -mt-20 pt-20 relative"
          style={{
            border: '1px solid rgba(255, 255, 255, 0.08)',
            borderTop: 'none',
            backgroundColor: 'transparent'
          }}
        >
          {/* Subtle centered white gradient */}
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              background: 'radial-gradient(ellipse 800px 600px at center, rgba(255, 255, 255, 0.02) 0%, transparent 50%)'
            }}
          ></div>

          {/* Grain overlay effect */}
          <div
            className="absolute inset-0 pointer-events-none opacity-20"
            style={{
              backgroundImage: `
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0.5px, transparent 0.5px),
                radial-gradient(circle at 75% 25%, rgba(255,255,255,0.08) 0.5px, transparent 0.5px),
                radial-gradient(circle at 25% 75%, rgba(255,255,255,0.06) 0.5px, transparent 0.5px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.04) 0.5px, transparent 0.5px),
                radial-gradient(circle at 50% 50%, rgba(255,255,255,0.03) 0.5px, transparent 0.5px)
              `,
              backgroundSize: '4px 4px, 6px 6px, 8px 8px, 12px 12px, 16px 16px',
              backgroundPosition: '0 0, 2px 2px, 4px 4px, 6px 6px, 8px 8px'
            }}
          ></div>

          <div className="relative z-10">
            <Hero dictionary={dictionary} />
            <TrustedBy dictionary={dictionary} />
          </div>
        </div>
      </div>
      <Mockup />
      <ModelProviders />
      <Community dictionary={dictionary} />
      <Download />
    </>
  );
};

export default Home;
