{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/catppuccin-mocha.mjs"], "sourcesContent": ["var catppuccinMocha = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#00000000\",\n    \"activityBar.activeBorder\": \"#00000000\",\n    \"activityBar.activeFocusBorder\": \"#00000000\",\n    \"activityBar.background\": \"#11111b\",\n    \"activityBar.border\": \"#00000000\",\n    \"activityBar.dropBorder\": \"#cba6f733\",\n    \"activityBar.foreground\": \"#cba6f7\",\n    \"activityBar.inactiveForeground\": \"#6c7086\",\n    \"activityBarBadge.background\": \"#cba6f7\",\n    \"activityBarBadge.foreground\": \"#11111b\",\n    \"activityBarTop.activeBorder\": \"#00000000\",\n    \"activityBarTop.dropBorder\": \"#cba6f733\",\n    \"activityBarTop.foreground\": \"#cba6f7\",\n    \"activityBarTop.inactiveForeground\": \"#6c7086\",\n    \"badge.background\": \"#45475a\",\n    \"badge.foreground\": \"#cdd6f4\",\n    \"banner.background\": \"#45475a\",\n    \"banner.foreground\": \"#cdd6f4\",\n    \"banner.iconForeground\": \"#cdd6f4\",\n    \"breadcrumb.activeSelectionForeground\": \"#cba6f7\",\n    \"breadcrumb.background\": \"#1e1e2e\",\n    \"breadcrumb.focusForeground\": \"#cba6f7\",\n    \"breadcrumb.foreground\": \"#cdd6f4cc\",\n    \"breadcrumbPicker.background\": \"#181825\",\n    \"button.background\": \"#cba6f7\",\n    \"button.border\": \"#00000000\",\n    \"button.foreground\": \"#11111b\",\n    \"button.hoverBackground\": \"#dec7fa\",\n    \"button.secondaryBackground\": \"#585b70\",\n    \"button.secondaryBorder\": \"#cba6f7\",\n    \"button.secondaryForeground\": \"#cdd6f4\",\n    \"button.secondaryHoverBackground\": \"#686b84\",\n    \"button.separator\": \"#00000000\",\n    \"charts.blue\": \"#89b4fa\",\n    \"charts.foreground\": \"#cdd6f4\",\n    \"charts.green\": \"#a6e3a1\",\n    \"charts.lines\": \"#bac2de\",\n    \"charts.orange\": \"#fab387\",\n    \"charts.purple\": \"#cba6f7\",\n    \"charts.red\": \"#f38ba8\",\n    \"charts.yellow\": \"#f9e2af\",\n    \"checkbox.background\": \"#45475a\",\n    \"checkbox.border\": \"#00000000\",\n    \"checkbox.foreground\": \"#cba6f7\",\n    \"commandCenter.activeBackground\": \"#585b7033\",\n    \"commandCenter.activeBorder\": \"#cba6f7\",\n    \"commandCenter.activeForeground\": \"#cba6f7\",\n    \"commandCenter.background\": \"#181825\",\n    \"commandCenter.border\": \"#00000000\",\n    \"commandCenter.foreground\": \"#bac2de\",\n    \"commandCenter.inactiveBorder\": \"#00000000\",\n    \"commandCenter.inactiveForeground\": \"#bac2de\",\n    \"debugConsole.errorForeground\": \"#f38ba8\",\n    \"debugConsole.infoForeground\": \"#89b4fa\",\n    \"debugConsole.sourceForeground\": \"#f5e0dc\",\n    \"debugConsole.warningForeground\": \"#fab387\",\n    \"debugConsoleInputIcon.foreground\": \"#cdd6f4\",\n    \"debugExceptionWidget.background\": \"#11111b\",\n    \"debugExceptionWidget.border\": \"#cba6f7\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#585b70\",\n    \"debugIcon.breakpointDisabledForeground\": \"#f38ba899\",\n    \"debugIcon.breakpointForeground\": \"#f38ba8\",\n    \"debugIcon.breakpointStackframeForeground\": \"#585b70\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#a6738c\",\n    \"debugIcon.continueForeground\": \"#a6e3a1\",\n    \"debugIcon.disconnectForeground\": \"#585b70\",\n    \"debugIcon.pauseForeground\": \"#89b4fa\",\n    \"debugIcon.restartForeground\": \"#94e2d5\",\n    \"debugIcon.startForeground\": \"#a6e3a1\",\n    \"debugIcon.stepBackForeground\": \"#585b70\",\n    \"debugIcon.stepIntoForeground\": \"#cdd6f4\",\n    \"debugIcon.stepOutForeground\": \"#cdd6f4\",\n    \"debugIcon.stepOverForeground\": \"#cba6f7\",\n    \"debugIcon.stopForeground\": \"#f38ba8\",\n    \"debugTokenExpression.boolean\": \"#cba6f7\",\n    \"debugTokenExpression.error\": \"#f38ba8\",\n    \"debugTokenExpression.number\": \"#fab387\",\n    \"debugTokenExpression.string\": \"#a6e3a1\",\n    \"debugToolBar.background\": \"#11111b\",\n    \"debugToolBar.border\": \"#00000000\",\n    \"descriptionForeground\": \"#cdd6f4\",\n    \"diffEditor.border\": \"#585b70\",\n    \"diffEditor.diagonalFill\": \"#585b7099\",\n    \"diffEditor.insertedLineBackground\": \"#a6e3a126\",\n    \"diffEditor.insertedTextBackground\": \"#a6e3a11a\",\n    \"diffEditor.removedLineBackground\": \"#f38ba826\",\n    \"diffEditor.removedTextBackground\": \"#f38ba81a\",\n    \"diffEditorOverview.insertedForeground\": \"#a6e3a1cc\",\n    \"diffEditorOverview.removedForeground\": \"#f38ba8cc\",\n    \"disabledForeground\": \"#a6adc8\",\n    \"dropdown.background\": \"#181825\",\n    \"dropdown.border\": \"#cba6f7\",\n    \"dropdown.foreground\": \"#cdd6f4\",\n    \"dropdown.listBackground\": \"#585b70\",\n    \"editor.background\": \"#1e1e2e\",\n    \"editor.findMatchBackground\": \"#5e3f53\",\n    \"editor.findMatchBorder\": \"#f38ba833\",\n    \"editor.findMatchHighlightBackground\": \"#3e5767\",\n    \"editor.findMatchHighlightBorder\": \"#89dceb33\",\n    \"editor.findRangeHighlightBackground\": \"#3e5767\",\n    \"editor.findRangeHighlightBorder\": \"#89dceb33\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#a6e3a126\",\n    \"editor.foldBackground\": \"#89dceb40\",\n    \"editor.foreground\": \"#cdd6f4\",\n    \"editor.hoverHighlightBackground\": \"#89dceb40\",\n    \"editor.lineHighlightBackground\": \"#cdd6f412\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#89dceb40\",\n    \"editor.rangeHighlightBorder\": \"#00000000\",\n    \"editor.selectionBackground\": \"#9399b240\",\n    \"editor.selectionHighlightBackground\": \"#9399b233\",\n    \"editor.selectionHighlightBorder\": \"#9399b233\",\n    \"editor.stackFrameHighlightBackground\": \"#f9e2af26\",\n    \"editor.wordHighlightBackground\": \"#9399b233\",\n    \"editorBracketHighlight.foreground1\": \"#f38ba8\",\n    \"editorBracketHighlight.foreground2\": \"#fab387\",\n    \"editorBracketHighlight.foreground3\": \"#f9e2af\",\n    \"editorBracketHighlight.foreground4\": \"#a6e3a1\",\n    \"editorBracketHighlight.foreground5\": \"#74c7ec\",\n    \"editorBracketHighlight.foreground6\": \"#cba6f7\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#eba0ac\",\n    \"editorBracketMatch.background\": \"#9399b21a\",\n    \"editorBracketMatch.border\": \"#9399b2\",\n    \"editorCodeLens.foreground\": \"#7f849c\",\n    \"editorCursor.background\": \"#1e1e2e\",\n    \"editorCursor.foreground\": \"#f5e0dc\",\n    \"editorError.background\": \"#00000000\",\n    \"editorError.border\": \"#00000000\",\n    \"editorError.foreground\": \"#f38ba8\",\n    \"editorGroup.border\": \"#585b70\",\n    \"editorGroup.dropBackground\": \"#cba6f733\",\n    \"editorGroup.emptyBackground\": \"#1e1e2e\",\n    \"editorGroupHeader.tabsBackground\": \"#11111b\",\n    \"editorGutter.addedBackground\": \"#a6e3a1\",\n    \"editorGutter.background\": \"#1e1e2e\",\n    \"editorGutter.commentGlyphForeground\": \"#cba6f7\",\n    \"editorGutter.commentRangeForeground\": \"#313244\",\n    \"editorGutter.deletedBackground\": \"#f38ba8\",\n    \"editorGutter.foldingControlForeground\": \"#9399b2\",\n    \"editorGutter.modifiedBackground\": \"#f9e2af\",\n    \"editorHoverWidget.background\": \"#181825\",\n    \"editorHoverWidget.border\": \"#585b70\",\n    \"editorHoverWidget.foreground\": \"#cdd6f4\",\n    \"editorIndentGuide.activeBackground\": \"#585b70\",\n    \"editorIndentGuide.background\": \"#45475a\",\n    \"editorInfo.background\": \"#00000000\",\n    \"editorInfo.border\": \"#00000000\",\n    \"editorInfo.foreground\": \"#89b4fa\",\n    \"editorInlayHint.background\": \"#181825bf\",\n    \"editorInlayHint.foreground\": \"#585b70\",\n    \"editorInlayHint.parameterBackground\": \"#181825bf\",\n    \"editorInlayHint.parameterForeground\": \"#a6adc8\",\n    \"editorInlayHint.typeBackground\": \"#181825bf\",\n    \"editorInlayHint.typeForeground\": \"#bac2de\",\n    \"editorLightBulb.foreground\": \"#f9e2af\",\n    \"editorLineNumber.activeForeground\": \"#cba6f7\",\n    \"editorLineNumber.foreground\": \"#7f849c\",\n    \"editorLink.activeForeground\": \"#cba6f7\",\n    \"editorMarkerNavigation.background\": \"#181825\",\n    \"editorMarkerNavigationError.background\": \"#f38ba8\",\n    \"editorMarkerNavigationInfo.background\": \"#89b4fa\",\n    \"editorMarkerNavigationWarning.background\": \"#fab387\",\n    \"editorOverviewRuler.background\": \"#181825\",\n    \"editorOverviewRuler.border\": \"#cdd6f412\",\n    \"editorOverviewRuler.modifiedForeground\": \"#f9e2af\",\n    \"editorRuler.foreground\": \"#585b70\",\n    \"editorStickyScrollHover.background\": \"#313244\",\n    \"editorSuggestWidget.background\": \"#181825\",\n    \"editorSuggestWidget.border\": \"#585b70\",\n    \"editorSuggestWidget.foreground\": \"#cdd6f4\",\n    \"editorSuggestWidget.highlightForeground\": \"#cba6f7\",\n    \"editorSuggestWidget.selectedBackground\": \"#313244\",\n    \"editorWarning.background\": \"#00000000\",\n    \"editorWarning.border\": \"#00000000\",\n    \"editorWarning.foreground\": \"#fab387\",\n    \"editorWhitespace.foreground\": \"#9399b266\",\n    \"editorWidget.background\": \"#181825\",\n    \"editorWidget.foreground\": \"#cdd6f4\",\n    \"editorWidget.resizeBorder\": \"#585b70\",\n    \"errorForeground\": \"#f38ba8\",\n    \"errorLens.errorBackground\": \"#f38ba826\",\n    \"errorLens.errorBackgroundLight\": \"#f38ba826\",\n    \"errorLens.errorForeground\": \"#f38ba8\",\n    \"errorLens.errorForegroundLight\": \"#f38ba8\",\n    \"errorLens.errorMessageBackground\": \"#f38ba826\",\n    \"errorLens.hintBackground\": \"#a6e3a126\",\n    \"errorLens.hintBackgroundLight\": \"#a6e3a126\",\n    \"errorLens.hintForeground\": \"#a6e3a1\",\n    \"errorLens.hintForegroundLight\": \"#a6e3a1\",\n    \"errorLens.hintMessageBackground\": \"#a6e3a126\",\n    \"errorLens.infoBackground\": \"#89b4fa26\",\n    \"errorLens.infoBackgroundLight\": \"#89b4fa26\",\n    \"errorLens.infoForeground\": \"#89b4fa\",\n    \"errorLens.infoForegroundLight\": \"#89b4fa\",\n    \"errorLens.infoMessageBackground\": \"#89b4fa26\",\n    \"errorLens.statusBarErrorForeground\": \"#f38ba8\",\n    \"errorLens.statusBarHintForeground\": \"#a6e3a1\",\n    \"errorLens.statusBarIconErrorForeground\": \"#f38ba8\",\n    \"errorLens.statusBarIconWarningForeground\": \"#fab387\",\n    \"errorLens.statusBarInfoForeground\": \"#89b4fa\",\n    \"errorLens.statusBarWarningForeground\": \"#fab387\",\n    \"errorLens.warningBackground\": \"#fab38726\",\n    \"errorLens.warningBackgroundLight\": \"#fab38726\",\n    \"errorLens.warningForeground\": \"#fab387\",\n    \"errorLens.warningForegroundLight\": \"#fab387\",\n    \"errorLens.warningMessageBackground\": \"#fab38726\",\n    \"extensionBadge.remoteBackground\": \"#89b4fa\",\n    \"extensionBadge.remoteForeground\": \"#11111b\",\n    \"extensionButton.prominentBackground\": \"#cba6f7\",\n    \"extensionButton.prominentForeground\": \"#11111b\",\n    \"extensionButton.prominentHoverBackground\": \"#dec7fa\",\n    \"extensionButton.separator\": \"#1e1e2e\",\n    \"extensionIcon.preReleaseForeground\": \"#585b70\",\n    \"extensionIcon.sponsorForeground\": \"#f5c2e7\",\n    \"extensionIcon.starForeground\": \"#f9e2af\",\n    \"extensionIcon.verifiedForeground\": \"#a6e3a1\",\n    \"focusBorder\": \"#cba6f7\",\n    \"foreground\": \"#cdd6f4\",\n    \"gitDecoration.addedResourceForeground\": \"#a6e3a1\",\n    \"gitDecoration.conflictingResourceForeground\": \"#cba6f7\",\n    \"gitDecoration.deletedResourceForeground\": \"#f38ba8\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6c7086\",\n    \"gitDecoration.modifiedResourceForeground\": \"#f9e2af\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#f38ba8\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#f9e2af\",\n    \"gitDecoration.submoduleResourceForeground\": \"#89b4fa\",\n    \"gitDecoration.untrackedResourceForeground\": \"#a6e3a1\",\n    \"gitlens.closedAutolinkedIssueIconColor\": \"#cba6f7\",\n    \"gitlens.closedPullRequestIconColor\": \"#f38ba8\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#a6e3a1\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#fab387\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#f9e2af\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#fab387\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#a6e3a1\",\n    \"gitlens.decorations.statusMergingOrRebasingConflictForegroundColor\": \"#eba0ac\",\n    \"gitlens.decorations.statusMergingOrRebasingForegroundColor\": \"#f9e2af\",\n    \"gitlens.decorations.workspaceCurrentForegroundColor\": \"#cba6f7\",\n    \"gitlens.decorations.workspaceRepoMissingForegroundColor\": \"#a6adc8\",\n    \"gitlens.decorations.workspaceRepoOpenForegroundColor\": \"#cba6f7\",\n    \"gitlens.decorations.worktreeHasUncommittedChangesForegroundColor\": \"#fab387\",\n    \"gitlens.decorations.worktreeMissingForegroundColor\": \"#eba0ac\",\n    \"gitlens.graphChangesColumnAddedColor\": \"#a6e3a1\",\n    \"gitlens.graphChangesColumnDeletedColor\": \"#f38ba8\",\n    \"gitlens.graphLane10Color\": \"#f5c2e7\",\n    \"gitlens.graphLane1Color\": \"#cba6f7\",\n    \"gitlens.graphLane2Color\": \"#f9e2af\",\n    \"gitlens.graphLane3Color\": \"#89b4fa\",\n    \"gitlens.graphLane4Color\": \"#f2cdcd\",\n    \"gitlens.graphLane5Color\": \"#a6e3a1\",\n    \"gitlens.graphLane6Color\": \"#b4befe\",\n    \"gitlens.graphLane7Color\": \"#f5e0dc\",\n    \"gitlens.graphLane8Color\": \"#f38ba8\",\n    \"gitlens.graphLane9Color\": \"#94e2d5\",\n    \"gitlens.graphMinimapMarkerHeadColor\": \"#a6e3a1\",\n    \"gitlens.graphMinimapMarkerHighlightsColor\": \"#f9e2af\",\n    \"gitlens.graphMinimapMarkerLocalBranchesColor\": \"#89b4fa\",\n    \"gitlens.graphMinimapMarkerRemoteBranchesColor\": \"#71a4f9\",\n    \"gitlens.graphMinimapMarkerStashesColor\": \"#cba6f7\",\n    \"gitlens.graphMinimapMarkerTagsColor\": \"#f2cdcd\",\n    \"gitlens.graphMinimapMarkerUpstreamColor\": \"#93dd8d\",\n    \"gitlens.graphScrollMarkerHeadColor\": \"#a6e3a1\",\n    \"gitlens.graphScrollMarkerHighlightsColor\": \"#f9e2af\",\n    \"gitlens.graphScrollMarkerLocalBranchesColor\": \"#89b4fa\",\n    \"gitlens.graphScrollMarkerRemoteBranchesColor\": \"#71a4f9\",\n    \"gitlens.graphScrollMarkerStashesColor\": \"#cba6f7\",\n    \"gitlens.graphScrollMarkerTagsColor\": \"#f2cdcd\",\n    \"gitlens.graphScrollMarkerUpstreamColor\": \"#93dd8d\",\n    \"gitlens.gutterBackgroundColor\": \"#3132444d\",\n    \"gitlens.gutterForegroundColor\": \"#cdd6f4\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#cba6f7\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#cba6f726\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#cba6f7cc\",\n    \"gitlens.mergedPullRequestIconColor\": \"#cba6f7\",\n    \"gitlens.openAutolinkedIssueIconColor\": \"#a6e3a1\",\n    \"gitlens.openPullRequestIconColor\": \"#a6e3a1\",\n    \"gitlens.trailingLineBackgroundColor\": \"#00000000\",\n    \"gitlens.trailingLineForegroundColor\": \"#cdd6f44d\",\n    \"gitlens.unpublishedChangesIconColor\": \"#a6e3a1\",\n    \"gitlens.unpublishedCommitIconColor\": \"#a6e3a1\",\n    \"gitlens.unpulledChangesIconColor\": \"#fab387\",\n    \"icon.foreground\": \"#cba6f7\",\n    \"input.background\": \"#313244\",\n    \"input.border\": \"#00000000\",\n    \"input.foreground\": \"#cdd6f4\",\n    \"input.placeholderForeground\": \"#cdd6f473\",\n    \"inputOption.activeBackground\": \"#585b70\",\n    \"inputOption.activeBorder\": \"#cba6f7\",\n    \"inputOption.activeForeground\": \"#cdd6f4\",\n    \"inputValidation.errorBackground\": \"#f38ba8\",\n    \"inputValidation.errorBorder\": \"#11111b33\",\n    \"inputValidation.errorForeground\": \"#11111b\",\n    \"inputValidation.infoBackground\": \"#89b4fa\",\n    \"inputValidation.infoBorder\": \"#11111b33\",\n    \"inputValidation.infoForeground\": \"#11111b\",\n    \"inputValidation.warningBackground\": \"#fab387\",\n    \"inputValidation.warningBorder\": \"#11111b33\",\n    \"inputValidation.warningForeground\": \"#11111b\",\n    \"issues.closed\": \"#cba6f7\",\n    \"issues.newIssueDecoration\": \"#f5e0dc\",\n    \"issues.open\": \"#a6e3a1\",\n    \"list.activeSelectionBackground\": \"#313244\",\n    \"list.activeSelectionForeground\": \"#cdd6f4\",\n    \"list.dropBackground\": \"#cba6f733\",\n    \"list.focusAndSelectionBackground\": \"#45475a\",\n    \"list.focusBackground\": \"#313244\",\n    \"list.focusForeground\": \"#cdd6f4\",\n    \"list.focusOutline\": \"#00000000\",\n    \"list.highlightForeground\": \"#cba6f7\",\n    \"list.hoverBackground\": \"#31324480\",\n    \"list.hoverForeground\": \"#cdd6f4\",\n    \"list.inactiveSelectionBackground\": \"#313244\",\n    \"list.inactiveSelectionForeground\": \"#cdd6f4\",\n    \"list.warningForeground\": \"#fab387\",\n    \"listFilterWidget.background\": \"#45475a\",\n    \"listFilterWidget.noMatchesOutline\": \"#f38ba8\",\n    \"listFilterWidget.outline\": \"#00000000\",\n    \"menu.background\": \"#1e1e2e\",\n    \"menu.border\": \"#1e1e2e80\",\n    \"menu.foreground\": \"#cdd6f4\",\n    \"menu.selectionBackground\": \"#585b70\",\n    \"menu.selectionBorder\": \"#00000000\",\n    \"menu.selectionForeground\": \"#cdd6f4\",\n    \"menu.separatorBackground\": \"#585b70\",\n    \"menubar.selectionBackground\": \"#45475a\",\n    \"menubar.selectionForeground\": \"#cdd6f4\",\n    \"merge.commonContentBackground\": \"#45475a\",\n    \"merge.commonHeaderBackground\": \"#585b70\",\n    \"merge.currentContentBackground\": \"#a6e3a133\",\n    \"merge.currentHeaderBackground\": \"#a6e3a166\",\n    \"merge.incomingContentBackground\": \"#89b4fa33\",\n    \"merge.incomingHeaderBackground\": \"#89b4fa66\",\n    \"minimap.background\": \"#18182580\",\n    \"minimap.errorHighlight\": \"#f38ba8bf\",\n    \"minimap.findMatchHighlight\": \"#89dceb4d\",\n    \"minimap.selectionHighlight\": \"#585b70bf\",\n    \"minimap.selectionOccurrenceHighlight\": \"#585b70bf\",\n    \"minimap.warningHighlight\": \"#fab387bf\",\n    \"minimapGutter.addedBackground\": \"#a6e3a1bf\",\n    \"minimapGutter.deletedBackground\": \"#f38ba8bf\",\n    \"minimapGutter.modifiedBackground\": \"#f9e2afbf\",\n    \"minimapSlider.activeBackground\": \"#cba6f799\",\n    \"minimapSlider.background\": \"#cba6f733\",\n    \"minimapSlider.hoverBackground\": \"#cba6f766\",\n    \"notificationCenter.border\": \"#cba6f7\",\n    \"notificationCenterHeader.background\": \"#181825\",\n    \"notificationCenterHeader.foreground\": \"#cdd6f4\",\n    \"notificationLink.foreground\": \"#89b4fa\",\n    \"notificationToast.border\": \"#cba6f7\",\n    \"notifications.background\": \"#181825\",\n    \"notifications.border\": \"#cba6f7\",\n    \"notifications.foreground\": \"#cdd6f4\",\n    \"notificationsErrorIcon.foreground\": \"#f38ba8\",\n    \"notificationsInfoIcon.foreground\": \"#89b4fa\",\n    \"notificationsWarningIcon.foreground\": \"#fab387\",\n    \"panel.background\": \"#1e1e2e\",\n    \"panel.border\": \"#585b70\",\n    \"panelSection.border\": \"#585b70\",\n    \"panelSection.dropBackground\": \"#cba6f733\",\n    \"panelTitle.activeBorder\": \"#cba6f7\",\n    \"panelTitle.activeForeground\": \"#cdd6f4\",\n    \"panelTitle.inactiveForeground\": \"#a6adc8\",\n    \"peekView.border\": \"#cba6f7\",\n    \"peekViewEditor.background\": \"#181825\",\n    \"peekViewEditor.matchHighlightBackground\": \"#89dceb4d\",\n    \"peekViewEditor.matchHighlightBorder\": \"#00000000\",\n    \"peekViewEditorGutter.background\": \"#181825\",\n    \"peekViewResult.background\": \"#181825\",\n    \"peekViewResult.fileForeground\": \"#cdd6f4\",\n    \"peekViewResult.lineForeground\": \"#cdd6f4\",\n    \"peekViewResult.matchHighlightBackground\": \"#89dceb4d\",\n    \"peekViewResult.selectionBackground\": \"#313244\",\n    \"peekViewResult.selectionForeground\": \"#cdd6f4\",\n    \"peekViewTitle.background\": \"#1e1e2e\",\n    \"peekViewTitleDescription.foreground\": \"#bac2deb3\",\n    \"peekViewTitleLabel.foreground\": \"#cdd6f4\",\n    \"pickerGroup.border\": \"#cba6f7\",\n    \"pickerGroup.foreground\": \"#cba6f7\",\n    \"problemsErrorIcon.foreground\": \"#f38ba8\",\n    \"problemsInfoIcon.foreground\": \"#89b4fa\",\n    \"problemsWarningIcon.foreground\": \"#fab387\",\n    \"progressBar.background\": \"#cba6f7\",\n    \"pullRequests.closed\": \"#f38ba8\",\n    \"pullRequests.draft\": \"#9399b2\",\n    \"pullRequests.merged\": \"#cba6f7\",\n    \"pullRequests.notification\": \"#cdd6f4\",\n    \"pullRequests.open\": \"#a6e3a1\",\n    \"sash.hoverBorder\": \"#cba6f7\",\n    \"scrollbar.shadow\": \"#11111b\",\n    \"scrollbarSlider.activeBackground\": \"#31324466\",\n    \"scrollbarSlider.background\": \"#585b7080\",\n    \"scrollbarSlider.hoverBackground\": \"#6c7086\",\n    \"selection.background\": \"#cba6f766\",\n    \"settings.dropdownBackground\": \"#45475a\",\n    \"settings.dropdownListBorder\": \"#00000000\",\n    \"settings.focusedRowBackground\": \"#585b7033\",\n    \"settings.headerForeground\": \"#cdd6f4\",\n    \"settings.modifiedItemIndicator\": \"#cba6f7\",\n    \"settings.numberInputBackground\": \"#45475a\",\n    \"settings.numberInputBorder\": \"#00000000\",\n    \"settings.textInputBackground\": \"#45475a\",\n    \"settings.textInputBorder\": \"#00000000\",\n    \"sideBar.background\": \"#181825\",\n    \"sideBar.border\": \"#00000000\",\n    \"sideBar.dropBackground\": \"#cba6f733\",\n    \"sideBar.foreground\": \"#cdd6f4\",\n    \"sideBarSectionHeader.background\": \"#181825\",\n    \"sideBarSectionHeader.foreground\": \"#cdd6f4\",\n    \"sideBarTitle.foreground\": \"#cba6f7\",\n    \"statusBar.background\": \"#11111b\",\n    \"statusBar.border\": \"#00000000\",\n    \"statusBar.debuggingBackground\": \"#fab387\",\n    \"statusBar.debuggingBorder\": \"#00000000\",\n    \"statusBar.debuggingForeground\": \"#11111b\",\n    \"statusBar.foreground\": \"#cdd6f4\",\n    \"statusBar.noFolderBackground\": \"#11111b\",\n    \"statusBar.noFolderBorder\": \"#00000000\",\n    \"statusBar.noFolderForeground\": \"#cdd6f4\",\n    \"statusBarItem.activeBackground\": \"#585b7066\",\n    \"statusBarItem.errorBackground\": \"#00000000\",\n    \"statusBarItem.errorForeground\": \"#f38ba8\",\n    \"statusBarItem.hoverBackground\": \"#585b7033\",\n    \"statusBarItem.prominentBackground\": \"#00000000\",\n    \"statusBarItem.prominentForeground\": \"#cba6f7\",\n    \"statusBarItem.prominentHoverBackground\": \"#585b7033\",\n    \"statusBarItem.remoteBackground\": \"#89b4fa\",\n    \"statusBarItem.remoteForeground\": \"#11111b\",\n    \"statusBarItem.warningBackground\": \"#00000000\",\n    \"statusBarItem.warningForeground\": \"#fab387\",\n    \"symbolIcon.arrayForeground\": \"#fab387\",\n    \"symbolIcon.booleanForeground\": \"#cba6f7\",\n    \"symbolIcon.classForeground\": \"#f9e2af\",\n    \"symbolIcon.colorForeground\": \"#f5c2e7\",\n    \"symbolIcon.constantForeground\": \"#fab387\",\n    \"symbolIcon.constructorForeground\": \"#b4befe\",\n    \"symbolIcon.enumeratorForeground\": \"#f9e2af\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#f9e2af\",\n    \"symbolIcon.eventForeground\": \"#f5c2e7\",\n    \"symbolIcon.fieldForeground\": \"#cdd6f4\",\n    \"symbolIcon.fileForeground\": \"#cba6f7\",\n    \"symbolIcon.folderForeground\": \"#cba6f7\",\n    \"symbolIcon.functionForeground\": \"#89b4fa\",\n    \"symbolIcon.interfaceForeground\": \"#f9e2af\",\n    \"symbolIcon.keyForeground\": \"#94e2d5\",\n    \"symbolIcon.keywordForeground\": \"#cba6f7\",\n    \"symbolIcon.methodForeground\": \"#89b4fa\",\n    \"symbolIcon.moduleForeground\": \"#cdd6f4\",\n    \"symbolIcon.namespaceForeground\": \"#f9e2af\",\n    \"symbolIcon.nullForeground\": \"#eba0ac\",\n    \"symbolIcon.numberForeground\": \"#fab387\",\n    \"symbolIcon.objectForeground\": \"#f9e2af\",\n    \"symbolIcon.operatorForeground\": \"#94e2d5\",\n    \"symbolIcon.packageForeground\": \"#f2cdcd\",\n    \"symbolIcon.propertyForeground\": \"#eba0ac\",\n    \"symbolIcon.referenceForeground\": \"#f9e2af\",\n    \"symbolIcon.snippetForeground\": \"#f2cdcd\",\n    \"symbolIcon.stringForeground\": \"#a6e3a1\",\n    \"symbolIcon.structForeground\": \"#94e2d5\",\n    \"symbolIcon.textForeground\": \"#cdd6f4\",\n    \"symbolIcon.typeParameterForeground\": \"#eba0ac\",\n    \"symbolIcon.unitForeground\": \"#cdd6f4\",\n    \"symbolIcon.variableForeground\": \"#cdd6f4\",\n    \"tab.activeBackground\": \"#1e1e2e\",\n    \"tab.activeBorder\": \"#00000000\",\n    \"tab.activeBorderTop\": \"#cba6f7\",\n    \"tab.activeForeground\": \"#cba6f7\",\n    \"tab.activeModifiedBorder\": \"#f9e2af\",\n    \"tab.border\": \"#181825\",\n    \"tab.hoverBackground\": \"#28283d\",\n    \"tab.hoverBorder\": \"#00000000\",\n    \"tab.hoverForeground\": \"#cba6f7\",\n    \"tab.inactiveBackground\": \"#181825\",\n    \"tab.inactiveForeground\": \"#6c7086\",\n    \"tab.inactiveModifiedBorder\": \"#f9e2af4d\",\n    \"tab.lastPinnedBorder\": \"#cba6f7\",\n    \"tab.unfocusedActiveBackground\": \"#181825\",\n    \"tab.unfocusedActiveBorder\": \"#00000000\",\n    \"tab.unfocusedActiveBorderTop\": \"#cba6f74d\",\n    \"tab.unfocusedInactiveBackground\": \"#0e0e16\",\n    \"table.headerBackground\": \"#313244\",\n    \"table.headerForeground\": \"#cdd6f4\",\n    \"terminal.ansiBlack\": \"#a6adc8\",\n    \"terminal.ansiBlue\": \"#89b4fa\",\n    \"terminal.ansiBrightBlack\": \"#585b70\",\n    \"terminal.ansiBrightBlue\": \"#89b4fa\",\n    \"terminal.ansiBrightCyan\": \"#89dceb\",\n    \"terminal.ansiBrightGreen\": \"#a6e3a1\",\n    \"terminal.ansiBrightMagenta\": \"#f5c2e7\",\n    \"terminal.ansiBrightRed\": \"#f38ba8\",\n    \"terminal.ansiBrightWhite\": \"#45475a\",\n    \"terminal.ansiBrightYellow\": \"#f9e2af\",\n    \"terminal.ansiCyan\": \"#89dceb\",\n    \"terminal.ansiGreen\": \"#a6e3a1\",\n    \"terminal.ansiMagenta\": \"#f5c2e7\",\n    \"terminal.ansiRed\": \"#f38ba8\",\n    \"terminal.ansiWhite\": \"#bac2de\",\n    \"terminal.ansiYellow\": \"#f9e2af\",\n    \"terminal.border\": \"#585b70\",\n    \"terminal.dropBackground\": \"#cba6f733\",\n    \"terminal.foreground\": \"#cdd6f4\",\n    \"terminal.inactiveSelectionBackground\": \"#585b7080\",\n    \"terminal.selectionBackground\": \"#585b70\",\n    \"terminal.tab.activeBorder\": \"#cba6f7\",\n    \"terminalCommandDecoration.defaultBackground\": \"#585b70\",\n    \"terminalCommandDecoration.errorBackground\": \"#f38ba8\",\n    \"terminalCommandDecoration.successBackground\": \"#a6e3a1\",\n    \"terminalCursor.background\": \"#1e1e2e\",\n    \"terminalCursor.foreground\": \"#f5e0dc\",\n    \"textBlockQuote.background\": \"#181825\",\n    \"textBlockQuote.border\": \"#11111b\",\n    \"textCodeBlock.background\": \"#1e1e2e\",\n    \"textLink.activeForeground\": \"#89dceb\",\n    \"textLink.foreground\": \"#89b4fa\",\n    \"textPreformat.foreground\": \"#cdd6f4\",\n    \"textSeparator.foreground\": \"#cba6f7\",\n    \"titleBar.activeBackground\": \"#11111b\",\n    \"titleBar.activeForeground\": \"#cdd6f4\",\n    \"titleBar.border\": \"#00000000\",\n    \"titleBar.inactiveBackground\": \"#11111b\",\n    \"titleBar.inactiveForeground\": \"#cdd6f480\",\n    \"tree.inactiveIndentGuidesStroke\": \"#45475a\",\n    \"tree.indentGuidesStroke\": \"#9399b2\",\n    \"walkThrough.embeddedEditorBackground\": \"#1e1e2e4d\",\n    \"welcomePage.progress.background\": \"#11111b\",\n    \"welcomePage.progress.foreground\": \"#cba6f7\",\n    \"welcomePage.tileBackground\": \"#181825\",\n    \"widget.shadow\": \"#18182580\",\n    \"window.activeBorder\": \"#00000000\",\n    \"window.inactiveBorder\": \"#00000000\"\n  },\n  \"displayName\": \"Catppuccin Mocha\",\n  \"name\": \"catppuccin-mocha\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"boolean\": {\n      \"foreground\": \"#fab387\"\n    },\n    \"builtinAttribute.attribute.library:rust\": {\n      \"foreground\": \"#89b4fa\"\n    },\n    \"class.builtin:python\": {\n      \"foreground\": \"#cba6f7\"\n    },\n    \"class:python\": {\n      \"foreground\": \"#f9e2af\"\n    },\n    \"constant.builtin.readonly:nix\": {\n      \"foreground\": \"#cba6f7\"\n    },\n    \"enumMember\": {\n      \"foreground\": \"#94e2d5\"\n    },\n    \"function.decorator:python\": {\n      \"foreground\": \"#fab387\"\n    },\n    \"generic.attribute:rust\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"heading\": {\n      \"foreground\": \"#f38ba8\"\n    },\n    \"number\": {\n      \"foreground\": \"#fab387\"\n    },\n    \"pol\": {\n      \"foreground\": \"#f2cdcd\"\n    },\n    \"property.readonly:javascript\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"property.readonly:javascriptreact\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"property.readonly:typescript\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"property.readonly:typescriptreact\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"selfKeyword\": {\n      \"foreground\": \"#f38ba8\"\n    },\n    \"text.emph\": {\n      \"fontStyle\": \"italic\",\n      \"foreground\": \"#f38ba8\"\n    },\n    \"text.math\": {\n      \"foreground\": \"#f2cdcd\"\n    },\n    \"text.strong\": {\n      \"fontStyle\": \"bold\",\n      \"foreground\": \"#f38ba8\"\n    },\n    \"tomlArrayKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#89b4fa\"\n    },\n    \"tomlTableKey\": {\n      \"fontStyle\": \"\",\n      \"foreground\": \"#89b4fa\"\n    },\n    \"type.defaultLibrary:go\": {\n      \"foreground\": \"#cba6f7\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#eba0ac\"\n    },\n    \"variable.readonly.defaultLibrary:go\": {\n      \"foreground\": \"#cba6f7\"\n    },\n    \"variable.readonly:javascript\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"variable.readonly:javascriptreact\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"variable.readonly:scala\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"variable.readonly:typescript\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"variable.readonly:typescriptreact\": {\n      \"foreground\": \"#cdd6f4\"\n    },\n    \"variable.typeHint:python\": {\n      \"foreground\": \"#f9e2af\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"text\",\n        \"source\",\n        \"variable.other.readwrite\",\n        \"punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#9399b2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6c7086\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"variable.other.constant\",\n        \"entity.name.constant\",\n        \"constant.language.boolean\",\n        \"constant.language.false\",\n        \"constant.language.true\",\n        \"keyword.other.unit.user-defined\",\n        \"keyword.other.unit.suffix.floating-point\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"keyword.operator.word\",\n        \"keyword.operator.new\",\n        \"variable.language.super\",\n        \"support.type.primitive\",\n        \"storage.type\",\n        \"storage.modifier\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.documentation\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"punctuation.accessor\",\n        \"punctuation.definition.generic\",\n        \"meta.function.closure punctuation.section.parameters\",\n        \"punctuation.definition.tag\",\n        \"punctuation.separator.key-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.method\",\n        \"support.function\",\n        \"support.function.misc\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.other.inherited-class\",\n        \"support.class\",\n        \"meta.function-call.constructor\",\n        \"entity.name.struct\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.enum\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.enum variable.other.readwrite\",\n        \"variable.other.enummember\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"meta.property.object\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type\",\n        \"meta.type-alias\",\n        \"support.type\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation variable.function\",\n        \"meta.annotation variable.annotation.function\",\n        \"meta.annotation punctuation.definition.annotation\",\n        \"meta.decorator\",\n        \"punctuation.decorator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"meta.function.parameters\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"support.function.builtin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.documentation\",\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.directive\",\n        \"punctuation.definition.directive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.typeparameters\",\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language.this\",\n        \"variable.language.this punctuation.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.template variable\",\n        \"string variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.new\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.specifier.extern.cpp\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.scope-resolution.template.call.cpp\",\n        \"entity.name.scope-resolution.parameter.cpp\",\n        \"entity.name.scope-resolution.cpp\",\n        \"entity.name.scope-resolution.function.definition.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.doxygen\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.reference.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"meta.interpolation.cs\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.documentation.cs\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css entity.other.attribute-name.class.css\",\n        \"entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.operator.css\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.other.attribute-name.pseudo-class\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"source.css constant.other.unicode-range\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"source.css variable.parameter.url\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.vendored.property-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-value variable\",\n        \"source.css meta.property-value variable.other.less\",\n        \"source.css meta.property-value variable.other.less punctuation.definition.variable.less\",\n        \"meta.definition.variable.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css meta.property-list variable\",\n        \"meta.property-list variable.other.less\",\n        \"meta.property-list variable.other.less punctuation.definition.variable.less\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.percentage.css\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"source.css meta.attribute-selector\",\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.definition.ini\",\n        \"punctuation.support.type.property-name.json\",\n        \"support.type.property-name.json\",\n        \"punctuation.support.type.property-name.toml\",\n        \"support.type.property-name.toml\",\n        \"entity.name.tag.yaml\",\n        \"punctuation.support.type.property-name.yaml\",\n        \"support.type.property-name.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.json\",\n        \"constant.language.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.anchor.yaml\",\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name.table\",\n        \"entity.name.section.group-title.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.time.datetime.offset.toml\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.anchor.yaml\",\n        \"punctuation.definition.alias.yaml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.document.begin.yaml\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.header.from-file\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.from-file.diff\",\n        \"punctuation.definition.to-file.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted variable.other.env\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.gdscript\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.go\",\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"comment meta.annotation.parameters.go\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.go\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"variable.graphql\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#f2cdcd\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql\",\n      \"settings\": {\n        \"foreground\": \"#f2cdcd\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.doctype\",\n        \"meta.tag.sgml.doctype punctuation.definition.tag\",\n        \"meta.tag.metadata.doctype entity.name.tag\",\n        \"meta.tag.metadata.doctype punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html constant.character.entity\",\n        \"text.html constant.character.entity punctuation\",\n        \"constant.character.entity.xml\",\n        \"constant.character.entity.xml punctuation\",\n        \"constant.character.entity.js.jsx\",\n        \"constant.charactger.entity.js.jsx punctuation\",\n        \"constant.character.entity.tsx\",\n        \"constant.character.entity.tsx punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\",\n        \"support.class.component.jsx\",\n        \"support.class.component.tsx\",\n        \"support.class.component.vue\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.annotation\",\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.enum.java\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"comment.block.javadoc.java keyword.other.documentation.javadoc.java\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"meta.export variable.other.readwrite.js\",\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.property.js\",\n        \"variable.other.property.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.jsdoc\",\n        \"comment.block.documentation variable.other\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.class.jsdoc\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console.js\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.node\",\n        \"support.type.object.module.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.implements\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.null.js\",\n        \"constant.language.null.ts\",\n        \"constant.language.undefined.js\",\n        \"constant.language.undefined.ts\",\n        \"support.type.builtin.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.generic\",\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.declaration.function.arrow.js\",\n        \"storage.type.function.arrow.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.decorator.ts\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.in.js\",\n        \"keyword.operator.expression.in.ts\",\n        \"keyword.operator.expression.infer.ts\",\n        \"keyword.operator.expression.instanceof.js\",\n        \"keyword.operator.expression.instanceof.ts\",\n        \"keyword.operator.expression.is\",\n        \"keyword.operator.expression.keyof.ts\",\n        \"keyword.operator.expression.of.js\",\n        \"keyword.operator.expression.of.ts\",\n        \"keyword.operator.expression.typeof.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol.julia\",\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"text.tex keyword.control.preamble\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"text.tex support.function.be\",\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.general.math.tex\",\n      \"settings\": {\n        \"foreground\": \"#f2cdcd\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-dash.documentation.lua storage.type.annotation.lua\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.double-dash.documentation.lua entity.name.variable.lua\",\n        \"comment.line.double-dash.documentation.lua variable.lua\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.1.markdown punctuation.definition.heading.markdown\",\n        \"heading.1.markdown\",\n        \"heading.1.quarto punctuation.definition.heading.quarto\",\n        \"heading.1.quarto\",\n        \"markup.heading.atx.1.mdx\",\n        \"markup.heading.atx.1.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.heading-0.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.2.markdown punctuation.definition.heading.markdown\",\n        \"heading.2.markdown\",\n        \"heading.2.quarto punctuation.definition.heading.quarto\",\n        \"heading.2.quarto\",\n        \"markup.heading.atx.2.mdx\",\n        \"markup.heading.atx.2.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.setext.2.markdown\",\n        \"markup.heading.heading-1.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.3.markdown punctuation.definition.heading.markdown\",\n        \"heading.3.markdown\",\n        \"heading.3.quarto punctuation.definition.heading.quarto\",\n        \"heading.3.quarto\",\n        \"markup.heading.atx.3.mdx\",\n        \"markup.heading.atx.3.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-2.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.4.markdown punctuation.definition.heading.markdown\",\n        \"heading.4.markdown\",\n        \"heading.4.quarto punctuation.definition.heading.quarto\",\n        \"heading.4.quarto\",\n        \"markup.heading.atx.4.mdx\",\n        \"markup.heading.atx.4.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-3.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.5.markdown punctuation.definition.heading.markdown\",\n        \"heading.5.markdown\",\n        \"heading.5.quarto punctuation.definition.heading.quarto\",\n        \"heading.5.quarto\",\n        \"markup.heading.atx.5.mdx\",\n        \"markup.heading.atx.5.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-4.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.6.markdown punctuation.definition.heading.markdown\",\n        \"heading.6.markdown\",\n        \"heading.6.quarto punctuation.definition.heading.quarto\",\n        \"heading.6.quarto\",\n        \"markup.heading.atx.6.mdx\",\n        \"markup.heading.atx.6.mdx punctuation.definition.heading.mdx\",\n        \"markup.heading.heading-5.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\",\n        \"foreground\": \"#a6adc8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.link\",\n        \"markup.underline.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown punctuation.definition.link.title\",\n        \"text.html.quarto punctuation.definition.link.title\",\n        \"string.other.link.title.markdown\",\n        \"string.other.link.title.quarto\",\n        \"markup.link\",\n        \"punctuation.definition.constant.markdown\",\n        \"punctuation.definition.constant.quarto\",\n        \"constant.other.reference.link.markdown\",\n        \"constant.other.reference.link.quarto\",\n        \"markup.substitution.attribute-reference\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b4befe\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.raw.markdown\",\n        \"punctuation.definition.raw.quarto\",\n        \"markup.inline.raw.string.markdown\",\n        \"markup.inline.raw.string.quarto\",\n        \"markup.raw.block.markdown\",\n        \"markup.raw.block.quarto\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block punctuation.definition\",\n        \"markup.raw support.asciidoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9399b2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\",\n        \"punctuation.definition.quote.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin.markdown\",\n        \"punctuation.definition.list.begin.quarto\",\n        \"markup.list.bullet\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.quarto\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.multipart.nix\",\n        \"entity.other.attribute-name.single.nix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded variable.parameter.name.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#b4befe\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.path.nix\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.attribute.builtin\",\n        \"meta.attribute.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.php punctuation.definition.variable.php\",\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"text.html.php support.function\",\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.phpdoc.php\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.magic.python\",\n        \"meta.function-call.arguments.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\",\n        \"variable.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow.python\",\n        \"keyword.operator.logical.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.python\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.token.decorator.python\",\n        \"meta.function.decorator.identifier.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.decorator.python\",\n        \"punctuation.definition.decorator.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.exception.python\",\n        \"support.function.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.indexed-name.python\",\n        \"meta.item-access.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.string.python\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.parameters.python\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.anchor.regexp\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.ts\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\",\n        \"keyword.other.back-reference.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a6e3a1\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.range.regexp\",\n      \"settings\": {\n        \"foreground\": \"#f5e0dc\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.numeric.regexp\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.no-capture.regexp\",\n        \"meta.assertion.look-ahead.regexp\",\n        \"meta.assertion.negative-look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.annotation.rust\",\n        \"meta.annotation.rust punctuation\",\n        \"meta.attribute.rust\",\n        \"punctuation.definition.attribute.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute.rust string.quoted.double.rust\",\n        \"meta.attribute.rust string.quoted.single.char.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.macro.rules.rust\",\n        \"storage.type.module.rust\",\n        \"storage.modifier.rust\",\n        \"storage.type.struct.rust\",\n        \"storage.type.enum.rust\",\n        \"storage.type.trait.rust\",\n        \"storage.type.union.rust\",\n        \"storage.type.impl.rust\",\n        \"storage.type.rust\",\n        \"storage.type.function.rust\",\n        \"storage.type.type.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.numeric.rust\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"meta.generic.rust\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.impl.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.trait.rust\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.union.rust\",\n      \"settings\": {\n        \"foreground\": \"#f9e2af\"\n      }\n    },\n    {\n      \"scope\": \"meta.enum.rust storage.type.source.rust\",\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.macro.rust\",\n        \"meta.macro.rust support.function.rust\",\n        \"entity.name.function.macro.rust\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.lifetime.rust\",\n        \"entity.name.type.lifetime\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.rust constant.other.placeholder.rust\",\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.return-type.rust meta.generic.rust storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust\",\n      \"settings\": {\n        \"foreground\": \"#89b4fa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.brackets.angle.rust\",\n      \"settings\": {\n        \"foreground\": \"#89dceb\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.caps.rust\",\n      \"settings\": {\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function.definition.rust variable.other.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eba0ac\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.call.rust variable.other.rust\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.self.rust\",\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.metavariable.name.rust\",\n        \"meta.macro.metavariable.rust keyword.operator.macro.dollar.rust\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.line.shebang\",\n        \"comment.line.shebang punctuation.definition.comment\",\n        \"comment.line.shebang\",\n        \"punctuation.definition.comment.shebang.shell\",\n        \"meta.shebang.shell\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f5c2e7\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.shebang constant.language\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\",\n        \"meta.function-call.arguments.shell punctuation.definition.variable.shell\",\n        \"meta.function-call.arguments.shell punctuation.section.interpolation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f38ba8\"\n      }\n    },\n    {\n      \"scope\": \"meta.string meta.interpolation.parameter.shell variable.other.readwrite\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fab387\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell punctuation.section.interpolation\",\n        \"punctuation.definition.evaluation.backticks.shell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#94e2d5\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#cba6f7\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell variable.other.normal.shell\",\n      \"settings\": {\n        \"foreground\": \"#cdd6f4\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { catppuccinMocha as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,kBAAkB,OAAO,MAAM,CAAC;IAClC,UAAU;QACR,gCAAgC;QAChC,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,qCAAqC;QACrC,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,0BAA0B;QAC1B,8BAA8B;QAC9B,mCAAmC;QACnC,oBAAoB;QACpB,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,oCAAoC;QACpC,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,wCAAwC;QACxC,kCAAkC;QAClC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,0CAA0C;QAC1C,0BAA0B;QAC1B,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,6BAA6B;QAC7B,kCAAkC;QAClC,6BAA6B;QAC7B,kCAAkC;QAClC,oCAAoC;QACpC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,sCAAsC;QACtC,qCAAqC;QACrC,0CAA0C;QAC1C,4CAA4C;QAC5C,qCAAqC;QACrC,wCAAwC;QACxC,+BAA+B;QAC/B,oCAAoC;QACpC,+BAA+B;QAC/B,oCAAoC;QACpC,sCAAsC;QACtC,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,6BAA6B;QAC7B,sCAAsC;QACtC,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,0CAA0C;QAC1C,sCAAsC;QACtC,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,uDAAuD;QACvD,2DAA2D;QAC3D,wDAAwD;QACxD,oEAAoE;QACpE,sDAAsD;QACtD,wCAAwC;QACxC,0CAA0C;QAC1C,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,uCAAuC;QACvC,6CAA6C;QAC7C,gDAAgD;QAChD,iDAAiD;QACjD,0CAA0C;QAC1C,uCAAuC;QACvC,2CAA2C;QAC3C,sCAAsC;QACtC,4CAA4C;QAC5C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,sCAAsC;QACtC,0CAA0C;QAC1C,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,wCAAwC;QACxC,oCAAoC;QACpC,uCAAuC;QACvC,uCAAuC;QACvC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,6BAA6B;QAC7B,eAAe;QACf,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,oCAAoC;QACpC,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,wCAAwC;QACxC,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,uBAAuB;QACvB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,uBAAuB;QACvB,sBAAsB;QACtB,uBAAuB;QACvB,6BAA6B;QAC7B,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,4BAA4B;QAC5B,sBAAsB;QACtB,kBAAkB;QAClB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,gCAAgC;QAChC,mCAAmC;QACnC,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,uBAAuB;QACvB,wCAAwC;QACxC,gCAAgC;QAChC,6BAA6B;QAC7B,+CAA+C;QAC/C,6CAA6C;QAC7C,+CAA+C;QAC/C,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wCAAwC;QACxC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,WAAW;YACT,cAAc;QAChB;QACA,2CAA2C;YACzC,cAAc;QAChB;QACA,wBAAwB;YACtB,cAAc;QAChB;QACA,gBAAgB;YACd,cAAc;QAChB;QACA,iCAAiC;YAC/B,cAAc;QAChB;QACA,cAAc;YACZ,cAAc;QAChB;QACA,6BAA6B;YAC3B,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,WAAW;YACT,cAAc;QAChB;QACA,UAAU;YACR,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,eAAe;YACb,cAAc;QAChB;QACA,aAAa;YACX,aAAa;YACb,cAAc;QAChB;QACA,aAAa;YACX,cAAc;QAChB;QACA,eAAe;YACb,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,gBAAgB;YACd,aAAa;YACb,cAAc;QAChB;QACA,0BAA0B;YACxB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,uCAAuC;YACrC,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;QACA,gCAAgC;YAC9B,cAAc;QAChB;QACA,qCAAqC;YACnC,cAAc;QAChB;QACA,4BAA4B;YAC1B,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}