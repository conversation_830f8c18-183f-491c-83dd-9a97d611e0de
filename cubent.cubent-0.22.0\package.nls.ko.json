{"extension.displayName": "cubent coder", "extension.description": "에디터에서 작동하는 AI 에이전트 개발팀.", "command.newTask.title": "새 작업", "command.explainCode.title": "코드 설명", "command.fixCode.title": "코드 수정", "command.improveCode.title": "코드 개선", "command.addToContext.title": "컨텍스트에 추가", "command.openInNewTab.title": "새 탭에서 열기", "command.focusInput.title": "입력 필드 포커스", "command.setCustomStoragePath.title": "사용자 지정 저장소 경로 설정", "command.terminal.addToContext.title": "터미널 내용을 컨텍스트에 추가", "command.terminal.fixCommand.title": "이 명령어 수정", "command.terminal.explainCommand.title": "이 명령어 설명", "command.acceptInput.title": "입력/제안 수락", "views.activitybar.title": "cubent coder", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent coder", "command.mcpServers.title": "MCP 서버", "command.prompts.title": "모드", "command.history.title": "기록", "command.openInEditor.title": "에디터에서 열기", "command.settings.title": "설정", "command.documentation.title": "문서", "configuration.title": "cubent coder", "commands.allowedCommands.description": "'항상 실행 작업 승인' 이 활성화되어 있을 때 자동으로 실행할 수 있는 명령어", "settings.vsCodeLmModelSelector.description": "VSCode 언어 모델 API 설정", "settings.vsCodeLmModelSelector.vendor.description": "언어 모델 공급자 (예: copilot)", "settings.vsCodeLmModelSelector.family.description": "언어 모델 계열 (예: gpt-4)", "settings.customStoragePath.description": "사용자 지정 저장소 경로. 기본 위치를 사용하려면 비워두세요. 절대 경로를 지원합니다 (예: 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "cubent coder Cloud 사용 설정"}