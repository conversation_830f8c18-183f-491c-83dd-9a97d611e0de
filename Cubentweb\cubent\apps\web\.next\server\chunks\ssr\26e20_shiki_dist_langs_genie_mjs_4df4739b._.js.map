{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/genie.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Genie\", \"fileTypes\": [\"gs\"], \"name\": \"genie\", \"patterns\": [{ \"include\": \"#code\" }], \"repository\": { \"code\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"include\": \"#keywords\" }, { \"include\": \"#types\" }, { \"include\": \"#functions\" }, { \"include\": \"#variables\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.vala\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.vala\" }, { \"include\": \"text.html.javadoc\" }, { \"include\": \"#comments-inline\" }] }, \"comments-inline\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.vala\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.vala\" }, { \"captures\": { \"1\": { \"name\": \"comment.line.double-slash.vala\" }, \"2\": { \"name\": \"punctuation.definition.comment.vala\" } }, \"match\": \"\\\\s*((//).*$\\\\n?)\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]*)|((\\\\d+\\\\.?\\\\d*)|(\\\\.\\\\d+))((e|E)(\\\\+|-)?\\\\d+)?)([LlFfUuDd]|UL|ul)?\\\\b\", \"name\": \"constant.numeric.vala\" }, { \"match\": \"\\\\b([A-Z][A-Z0-9_]+)\\\\b\", \"name\": \"variable.other.constant.vala\" }] }, \"functions\": { \"patterns\": [{ \"match\": \"(\\\\w+)(?=\\\\s*(<[\\\\s\\\\w.]+>\\\\s*)?\\\\()\", \"name\": \"entity.name.function.vala\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(?<=^|[^@\\\\w\\\\.])(as|do|if|in|is|of|or|to|and|def|for|get|isa|new|not|out|ref|set|try|var|case|dict|else|enum|init|list|lock|null|pass|prop|self|true|uses|void|weak|when|array|async|break|class|const|event|false|final|owned|print|super|raise|while|yield|assert|delete|downto|except|extern|inline|params|public|raises|return|sealed|sizeof|static|struct|typeof|default|dynamic|ensures|finally|private|unowned|virtual|abstract|continue|delegate|internal|override|readonly|requires|volatile|construct|errordomain|interface|namespace|protected|implements)\\\\b\", \"name\": \"keyword.vala\" }, { \"match\": \"(?<=^|[^@\\\\w\\\\.])(bool|double|float|unichar|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64)\\\\b\", \"name\": \"keyword.vala\" }, { \"match\": \"(#if|#elif|#else|#endif)\", \"name\": \"keyword.vala\" }] }, \"strings\": { \"patterns\": [{ \"begin\": '\"\"\"', \"end\": '\"\"\"', \"name\": \"string.quoted.triple.vala\" }, { \"begin\": '@\"', \"end\": '\"', \"name\": \"string.quoted.interpolated.vala\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.vala\" }, { \"match\": \"\\\\$\\\\w+\", \"name\": \"constant.character.escape.vala\" }, { \"match\": \"\\\\$\\\\(([^)(]|\\\\(([^)(]|\\\\([^)]*\\\\))*\\\\))*\\\\)\", \"name\": \"constant.character.escape.vala\" }] }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.vala\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.vala\" }] }, { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.vala\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.vala\" }] }, { \"match\": \"/((\\\\\\\\/)|([^/]))*/(?=\\\\s*[,;)\\\\.\\\\n])\", \"name\": \"string.regexp.vala\" }] }, \"types\": { \"patterns\": [{ \"match\": \"(?<=^|[^@\\\\w\\\\.])(bool|double|float|unichar|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64)\\\\b\", \"name\": \"storage.type.primitive.vala\" }, { \"match\": \"\\\\b([A-Z]+\\\\w*)\\\\b\", \"name\": \"entity.name.type.vala\" }] }, \"variables\": { \"patterns\": [{ \"match\": \"\\\\b([_a-z]+\\\\w*)\\\\b\", \"name\": \"variable.other.vala\" }] } }, \"scopeName\": \"source.genie\" });\nvar genie = [\n  lang\n];\n\nexport { genie as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,aAAa;QAAC;KAAK;IAAE,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAY,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAoB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwC,QAAQ;gBAA4B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6iB,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAAmL,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAe;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAO,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAM,OAAO;oBAAK,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS;4BAAgD,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,QAAQ;gBAAqB;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmL,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAAsB;aAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AACv4G,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}