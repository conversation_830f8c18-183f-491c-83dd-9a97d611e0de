{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/graphql.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport jsx from './jsx.mjs';\nimport tsx from './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"GraphQL\", \"fileTypes\": [\"graphql\", \"graphqls\", \"gql\", \"graphcool\"], \"name\": \"graphql\", \"patterns\": [{ \"include\": \"#graphql\" }], \"repository\": { \"graphql\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-fragment-definition\" }, { \"include\": \"#graphql-directive-definition\" }, { \"include\": \"#graphql-type-interface\" }, { \"include\": \"#graphql-enum\" }, { \"include\": \"#graphql-scalar\" }, { \"include\": \"#graphql-union\" }, { \"include\": \"#graphql-schema\" }, { \"include\": \"#graphql-operation-def\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-ampersand\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.graphql\" } }, \"match\": \"\\\\s*(&)\" }, \"graphql-arguments\": { \"begin\": \"\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"name\": \"meta.arguments.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(:))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"end\": \"(?=\\\\s*(?:(?:([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:))|\\\\)))|\\\\s*(,)\", \"endCaptures\": { \"3\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-value\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-boolean-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.boolean.graphql\" } }, \"match\": \"\\\\s*\\\\b(true|false)\\\\b\" }, \"graphql-colon\": { \"captures\": { \"1\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*(:)\" }, \"graphql-comma\": { \"captures\": { \"1\": { \"name\": \"punctuation.comma.graphql\" } }, \"match\": \"\\\\s*(,)\" }, \"graphql-comment\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"comment\": \"need to prefix comment space with a scope else Atom's reflow cmd doesn't work\", \"match\": \"(\\\\s*)(#).*\", \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\"\"\")', \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\")', \"name\": \"comment.line.graphql.js\" }] }, \"graphql-description-docstring\": { \"begin\": '\"\"\"', \"end\": '\"\"\"', \"name\": \"comment.block.graphql\" }, \"graphql-description-singleline\": { \"match\": '#(?=([^\"]*\"[^\"]*\")*[^\"]*$).*$', \"name\": \"comment.line.number-sign.graphql\" }, \"graphql-directive\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*((@)\\\\s*([_A-Za-z][_0-9A-Za-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.directive.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-directive-definition\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bdirective\\\\b)\\\\s*(@[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.directive.graphql\" }, \"2\": { \"name\": \"entity.name.function.directive.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-variable-definitions\" }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bon\\\\b)\\\\s*([_A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.on.graphql\" }, \"2\": { \"name\": \"support.type.location.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"2\": { \"name\": \"support.type.location.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z]*)\" }] }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-enum\": { \"begin\": \"\\\\s*+\\\\b(enum)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.enum.graphql\" }, \"2\": { \"name\": \"support.type.enum.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.enum.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }] }, \"graphql-enum-value\": { \"match\": \"\\\\s*(?!=\\\\b(true|false|null)\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"name\": \"constant.character.enum.graphql\" }, \"graphql-field\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.alias.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\" }, { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-float-value\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.float.graphql\" } }, \"match\": \"\\\\s*(-?(0|[1-9]\\\\d*)(\\\\.\\\\d+)?((e|E)(\\\\+|-)?\\\\d+)?)\" }, \"graphql-fragment-definition\": { \"begin\": \"\\\\s*(?:(\\\\bfragment\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)))\", \"captures\": { \"1\": { \"name\": \"keyword.fragment.graphql\" }, \"2\": { \"name\": \"entity.name.fragment.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.fragment.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-fragment-spread\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?!\\\\bon\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"variable.fragment.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-ignore-spaces\": { \"match\": \"\\\\s*\" }, \"graphql-inline-fragment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*))?\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"keyword.on.graphql\" }, \"3\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-input-types\": { \"patterns\": [{ \"include\": \"#graphql-scalar-type\" }, { \"captures\": { \"1\": { \"name\": \"support.type.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(!))?\" }, { \"begin\": \"\\\\s*(\\\\[)\", \"captures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"end\": \"\\\\s*(\\\\])(?:\\\\s*(!))?\", \"name\": \"meta.type.list.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#literal-quasi-embedded\" }] }] }, \"graphql-list-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"end\": \"\\\\s*(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"name\": \"meta.listvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }] }, \"graphql-name\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-null-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.null.graphql\" } }, \"match\": \"\\\\s*\\\\b(null)\\\\b\" }, \"graphql-object-field\": { \"captures\": { \"1\": { \"name\": \"constant.object.key.graphql\" }, \"2\": { \"name\": \"string.unquoted.graphql\" }, \"3\": { \"name\": \"punctuation.graphql\" } }, \"match\": \"\\\\s*(([_A-Za-z][_0-9A-Za-z]*))\\\\s*(:)\" }, \"graphql-object-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+({)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"name\": \"meta.objectvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-field\" }, { \"include\": \"#graphql-value\" }] }] }, \"graphql-operation-def\": { \"patterns\": [{ \"include\": \"#graphql-query-mutation\" }, { \"include\": \"#graphql-name\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }] }, \"graphql-query-mutation\": { \"captures\": { \"1\": { \"name\": \"keyword.operation.graphql\" } }, \"match\": \"\\\\s*\\\\b(query|mutation)\\\\b\" }, \"graphql-scalar\": { \"captures\": { \"1\": { \"name\": \"keyword.scalar.graphql\" }, \"2\": { \"name\": \"entity.scalar.graphql\" } }, \"match\": \"\\\\s*\\\\b(scalar)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-scalar-type\": { \"captures\": { \"1\": { \"name\": \"support.type.builtin.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*\\\\b(Int|Float|String|Boolean|ID)\\\\b(?:\\\\s*(!))?\" }, \"graphql-schema\": { \"begin\": \"\\\\s*\\\\b(schema)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.schema.graphql\" } }, \"end\": \"(?<=})\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"patterns\": [{ \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.arguments.graphql\" } }, \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-selection-set\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.selectionset.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-field\" }, { \"include\": \"#graphql-fragment-spread\" }, { \"include\": \"#graphql-inline-fragment\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#native-interpolation\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-skip-newlines\": { \"match\": \"\\\\s*\\n\" }, \"graphql-string-content\": { \"patterns\": [{ \"match\": `\\\\\\\\[/'\"\\\\\\\\nrtbf]`, \"name\": \"constant.character.escape.graphql\" }, { \"match\": \"\\\\\\\\u([0-9a-fA-F]{4})\", \"name\": \"constant.character.escape.graphql\" }] }, \"graphql-string-value\": { \"begin\": '\\\\s*+((\"))', \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.graphql\" } }, \"contentName\": \"string.quoted.double.graphql\", \"end\": '\\\\s*+(?:((\"))|(\\n))', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.graphql\" }, \"3\": { \"name\": \"invalid.illegal.newline.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-string-content\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-definition\": { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"comment\": \"key (optionalArgs): Type\", \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-interface\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(?:(extends?)?\\\\b\\\\s*\\\\b(type)|(interface)|(input))\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\", \"captures\": { \"1\": { \"name\": \"keyword.type.graphql\" }, \"2\": { \"name\": \"keyword.type.graphql\" }, \"3\": { \"name\": \"keyword.interface.graphql\" }, \"4\": { \"name\": \"keyword.input.graphql\" }, \"5\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"name\": \"meta.type.interface.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*\\\\b(implements)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.implements.graphql\" } }, \"end\": \"\\\\s*(?={)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-ampersand\" }, { \"include\": \"#graphql-comma\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-ignore-spaces\" }] }, \"graphql-type-object\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-type-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(union)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.union.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(=)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.or.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union-mark\": { \"captures\": { \"1\": { \"name\": \"punctuation.union.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\" }, \"graphql-value\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-variable-name\" }, { \"include\": \"#graphql-float-value\" }, { \"include\": \"#graphql-string-value\" }, { \"include\": \"#graphql-boolean-value\" }, { \"include\": \"#graphql-null-value\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#graphql-list-value\" }, { \"include\": \"#graphql-object-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-assignment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s(=)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" } }, \"end\": \"(?=[\\n,)])\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }, \"graphql-variable-definition\": { \"begin\": \"\\\\s*(\\\\$?[_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" } }, \"comment\": \"variable: type = value,.... which may be a list\", \"end\": \"(?=\\\\s*((\\\\$?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(}|\\\\))))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"name\": \"meta.variables.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-variable-assignment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-variable-definitions\": { \"begin\": \"\\\\s*(\\\\()\", \"captures\": { \"1\": { \"name\": \"meta.brace.round.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-variable-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-name\": { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*(\\\\$[_A-Za-z][_0-9A-Za-z]*)\" }, \"native-interpolation\": { \"begin\": \"\\\\s*(\\\\${)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.begin\" } }, \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.end\" } }, \"name\": \"native.interpolation\", \"patterns\": [{ \"include\": \"source.js\" }, { \"include\": \"source.ts\" }, { \"include\": \"source.js.jsx\" }, { \"include\": \"source.tsx\" }] } }, \"scopeName\": \"source.graphql\", \"embeddedLangs\": [\"javascript\", \"typescript\", \"jsx\", \"tsx\"], \"aliases\": [\"gql\"] });\nvar graphql = [\n  ...javascript,\n  ...typescript,\n  ...jsx,\n  ...tsx,\n  lang\n];\n\nexport { graphql as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAW;QAAY;QAAO;KAAY;IAAE,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAU;QAAG,qBAAqB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,SAAS;oBAA2C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAA+D,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAyB;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAU;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAU;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,WAAW;oBAAiF,SAAS;oBAAe,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAS,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAA0B;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;QAAwB;QAAG,kCAAkC;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG,qBAAqB;YAAE,uBAAuB;YAAG,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gCAAgC;YAAE,uBAAuB;YAAG,SAAS;YAAsD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,uBAAuB;oBAAG,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;4BAAE;4BAAG,SAAS;wBAA4B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAA4D,QAAQ;QAAkC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAsD;QAAG,+BAA+B;YAAE,SAAS;YAAuG,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,2BAA2B;YAAE,uBAAuB;YAAG,SAAS;YAA2D,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;QAAO;QAAG,2BAA2B;YAAE,uBAAuB;YAAG,SAAS;YAAkE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAA2C;gBAAG;oBAAE,SAAS;oBAAa,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAyB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA+B;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAmB;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAAwC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAA6B;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,SAAS;QAAiD;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAsD;QAAG,kBAAkB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,OAAO;YAAU,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA6C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAA6D,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAuB;oCAAE;oCAAG,SAAS;gCAA+B;gCAAG;oCAAE,WAAW;gCAAmB;gCAAG;oCAAE,WAAW;gCAAiC;gCAAG;oCAAE,WAAW;gCAAkC;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;QAAS;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,kBAAkB,CAAC;oBAAE,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAoC;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,eAAe;YAAgC,OAAO;YAAuB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAA6C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,WAAW;YAA4B,OAAO;YAA6D,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,uBAAuB;YAAG,SAAS;YAA8F,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;wBAA+B;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,iBAAiB;YAAE,uBAAuB;YAAG,SAAS;YAAiD,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,uBAAuB;oBAAG,SAAS;oBAAuC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;wBAAwC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAY;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,+BAA+B;YAAE,uBAAuB;YAAG,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAmD,OAAO;YAAqE,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAa,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,SAAS;QAAkC;QAAG,wBAAwB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;IAAE;IAAG,aAAa;IAAkB,iBAAiB;QAAC;QAAc;QAAc;QAAO;KAAM;IAAE,WAAW;QAAC;KAAM;AAAC;AAC9/lB,IAAI,UAAU;OACT,wMAAA,CAAA,UAAU;OACV,wMAAA,CAAA,UAAU;OACV,iMAAA,CAAA,UAAG;OACH,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}