{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/rose-pine.mjs"], "sourcesContent": ["var rosePine = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#e0def4\",\n    \"activityBar.background\": \"#191724\",\n    \"activityBar.dropBorder\": \"#26233a\",\n    \"activityBar.foreground\": \"#e0def4\",\n    \"activityBar.inactiveForeground\": \"#908caa\",\n    \"activityBarBadge.background\": \"#ebbcba\",\n    \"activityBarBadge.foreground\": \"#191724\",\n    \"badge.background\": \"#ebbcba\",\n    \"badge.foreground\": \"#191724\",\n    \"banner.background\": \"#1f1d2e\",\n    \"banner.foreground\": \"#e0def4\",\n    \"banner.iconForeground\": \"#908caa\",\n    \"breadcrumb.activeSelectionForeground\": \"#ebbcba\",\n    \"breadcrumb.background\": \"#191724\",\n    \"breadcrumb.focusForeground\": \"#908caa\",\n    \"breadcrumb.foreground\": \"#6e6a86\",\n    \"breadcrumbPicker.background\": \"#1f1d2e\",\n    \"button.background\": \"#ebbcba\",\n    \"button.foreground\": \"#191724\",\n    \"button.hoverBackground\": \"#ebbcbae6\",\n    \"button.secondaryBackground\": \"#1f1d2e\",\n    \"button.secondaryForeground\": \"#e0def4\",\n    \"button.secondaryHoverBackground\": \"#26233a\",\n    \"charts.blue\": \"#9ccfd8\",\n    \"charts.foreground\": \"#e0def4\",\n    \"charts.green\": \"#31748f\",\n    \"charts.lines\": \"#908caa\",\n    \"charts.orange\": \"#ebbcba\",\n    \"charts.purple\": \"#c4a7e7\",\n    \"charts.red\": \"#eb6f92\",\n    \"charts.yellow\": \"#f6c177\",\n    \"checkbox.background\": \"#1f1d2e\",\n    \"checkbox.border\": \"#6e6a8633\",\n    \"checkbox.foreground\": \"#e0def4\",\n    \"debugExceptionWidget.background\": \"#1f1d2e\",\n    \"debugExceptionWidget.border\": \"#6e6a8633\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#908caa\",\n    \"debugIcon.breakpointDisabledForeground\": \"#908caa\",\n    \"debugIcon.breakpointForeground\": \"#908caa\",\n    \"debugIcon.breakpointStackframeForeground\": \"#908caa\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#908caa\",\n    \"debugIcon.continueForeground\": \"#908caa\",\n    \"debugIcon.disconnectForeground\": \"#908caa\",\n    \"debugIcon.pauseForeground\": \"#908caa\",\n    \"debugIcon.restartForeground\": \"#908caa\",\n    \"debugIcon.startForeground\": \"#908caa\",\n    \"debugIcon.stepBackForeground\": \"#908caa\",\n    \"debugIcon.stepIntoForeground\": \"#908caa\",\n    \"debugIcon.stepOutForeground\": \"#908caa\",\n    \"debugIcon.stepOverForeground\": \"#908caa\",\n    \"debugIcon.stopForeground\": \"#eb6f92\",\n    \"debugToolBar.background\": \"#1f1d2e\",\n    \"debugToolBar.border\": \"#26233a\",\n    \"descriptionForeground\": \"#908caa\",\n    \"diffEditor.border\": \"#26233a\",\n    \"diffEditor.diagonalFill\": \"#6e6a8666\",\n    \"diffEditor.insertedLineBackground\": \"#9ccfd826\",\n    \"diffEditor.insertedTextBackground\": \"#9ccfd826\",\n    \"diffEditor.removedLineBackground\": \"#eb6f9226\",\n    \"diffEditor.removedTextBackground\": \"#eb6f9226\",\n    \"diffEditorOverview.insertedForeground\": \"#9ccfd880\",\n    \"diffEditorOverview.removedForeground\": \"#eb6f9280\",\n    \"dropdown.background\": \"#1f1d2e\",\n    \"dropdown.border\": \"#6e6a8633\",\n    \"dropdown.foreground\": \"#e0def4\",\n    \"dropdown.listBackground\": \"#1f1d2e\",\n    \"editor.background\": \"#191724\",\n    \"editor.findMatchBackground\": \"#6e6a8666\",\n    \"editor.findMatchHighlightBackground\": \"#6e6a8666\",\n    \"editor.findRangeHighlightBackground\": \"#6e6a8666\",\n    \"editor.findRangeHighlightBorder\": \"#0000\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#6e6a8633\",\n    \"editor.foldBackground\": \"#1f1d2e\",\n    \"editor.foreground\": \"#e0def4\",\n    \"editor.hoverHighlightBackground\": \"#0000\",\n    \"editor.inactiveSelectionBackground\": \"#6e6a861a\",\n    \"editor.inlineValuesBackground\": \"#0000\",\n    \"editor.inlineValuesForeground\": \"#908caa\",\n    \"editor.lineHighlightBackground\": \"#6e6a861a\",\n    \"editor.lineHighlightBorder\": \"#0000\",\n    \"editor.linkedEditingBackground\": \"#1f1d2e\",\n    \"editor.rangeHighlightBackground\": \"#6e6a861a\",\n    \"editor.selectionBackground\": \"#6e6a8633\",\n    \"editor.selectionForeground\": \"#e0def4\",\n    \"editor.selectionHighlightBackground\": \"#6e6a8633\",\n    \"editor.selectionHighlightBorder\": \"#191724\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#6e6a8633\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#1f1d2e\",\n    \"editor.snippetTabstopHighlightBackground\": \"#6e6a8633\",\n    \"editor.snippetTabstopHighlightBorder\": \"#1f1d2e\",\n    \"editor.stackFrameHighlightBackground\": \"#6e6a8633\",\n    \"editor.symbolHighlightBackground\": \"#6e6a8633\",\n    \"editor.symbolHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightBackground\": \"#6e6a8633\",\n    \"editor.wordHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightStrongBackground\": \"#6e6a8633\",\n    \"editor.wordHighlightStrongBorder\": \"#6e6a8633\",\n    \"editorBracketHighlight.foreground1\": \"#eb6f9280\",\n    \"editorBracketHighlight.foreground2\": \"#31748f80\",\n    \"editorBracketHighlight.foreground3\": \"#f6c17780\",\n    \"editorBracketHighlight.foreground4\": \"#9ccfd880\",\n    \"editorBracketHighlight.foreground5\": \"#ebbcba80\",\n    \"editorBracketHighlight.foreground6\": \"#c4a7e780\",\n    \"editorBracketMatch.background\": \"#0000\",\n    \"editorBracketMatch.border\": \"#908caa\",\n    \"editorBracketPairGuide.activeBackground1\": \"#31748f\",\n    \"editorBracketPairGuide.activeBackground2\": \"#ebbcba\",\n    \"editorBracketPairGuide.activeBackground3\": \"#c4a7e7\",\n    \"editorBracketPairGuide.activeBackground4\": \"#9ccfd8\",\n    \"editorBracketPairGuide.activeBackground5\": \"#f6c177\",\n    \"editorBracketPairGuide.activeBackground6\": \"#eb6f92\",\n    \"editorBracketPairGuide.background1\": \"#31748f80\",\n    \"editorBracketPairGuide.background2\": \"#ebbcba80\",\n    \"editorBracketPairGuide.background3\": \"#c4a7e780\",\n    \"editorBracketPairGuide.background4\": \"#9ccfd880\",\n    \"editorBracketPairGuide.background5\": \"#f6c17780\",\n    \"editorBracketPairGuide.background6\": \"#eb6f9280\",\n    \"editorCodeLens.foreground\": \"#ebbcba\",\n    \"editorCursor.background\": \"#e0def4\",\n    \"editorCursor.foreground\": \"#6e6a86\",\n    \"editorError.border\": \"#0000\",\n    \"editorError.foreground\": \"#eb6f92\",\n    \"editorGhostText.foreground\": \"#908caa\",\n    \"editorGroup.border\": \"#0000\",\n    \"editorGroup.dropBackground\": \"#1f1d2e\",\n    \"editorGroup.emptyBackground\": \"#0000\",\n    \"editorGroup.focusedEmptyBorder\": \"#0000\",\n    \"editorGroupHeader.noTabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBorder\": \"#0000\",\n    \"editorGutter.addedBackground\": \"#9ccfd8\",\n    \"editorGutter.background\": \"#191724\",\n    \"editorGutter.commentRangeForeground\": \"#908caa\",\n    \"editorGutter.deletedBackground\": \"#eb6f92\",\n    \"editorGutter.foldingControlForeground\": \"#c4a7e7\",\n    \"editorGutter.modifiedBackground\": \"#ebbcba\",\n    \"editorHint.border\": \"#0000\",\n    \"editorHint.foreground\": \"#908caa\",\n    \"editorHoverWidget.background\": \"#1f1d2e\",\n    \"editorHoverWidget.border\": \"#6e6a8680\",\n    \"editorHoverWidget.foreground\": \"#908caa\",\n    \"editorHoverWidget.highlightForeground\": \"#e0def4\",\n    \"editorHoverWidget.statusBarBackground\": \"#0000\",\n    \"editorIndentGuide.activeBackground\": \"#6e6a86\",\n    \"editorIndentGuide.background\": \"#6e6a8666\",\n    \"editorInfo.border\": \"#26233a\",\n    \"editorInfo.foreground\": \"#9ccfd8\",\n    \"editorInlayHint.background\": \"#26233a\",\n    \"editorInlayHint.foreground\": \"#908caa\",\n    \"editorInlayHint.parameterBackground\": \"#26233a\",\n    \"editorInlayHint.parameterForeground\": \"#c4a7e7\",\n    \"editorInlayHint.typeBackground\": \"#26233a\",\n    \"editorInlayHint.typeForeground\": \"#9ccfd8\",\n    \"editorLightBulb.foreground\": \"#31748f\",\n    \"editorLightBulbAutoFix.foreground\": \"#ebbcba\",\n    \"editorLineNumber.activeForeground\": \"#e0def4\",\n    \"editorLineNumber.foreground\": \"#908caa\",\n    \"editorLink.activeForeground\": \"#ebbcba\",\n    \"editorMarkerNavigation.background\": \"#1f1d2e\",\n    \"editorMarkerNavigationError.background\": \"#1f1d2e\",\n    \"editorMarkerNavigationInfo.background\": \"#1f1d2e\",\n    \"editorMarkerNavigationWarning.background\": \"#1f1d2e\",\n    \"editorOverviewRuler.addedForeground\": \"#9ccfd880\",\n    \"editorOverviewRuler.background\": \"#191724\",\n    \"editorOverviewRuler.border\": \"#6e6a8666\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#908caa\",\n    \"editorOverviewRuler.commonContentForeground\": \"#6e6a861a\",\n    \"editorOverviewRuler.currentContentForeground\": \"#6e6a8633\",\n    \"editorOverviewRuler.deletedForeground\": \"#eb6f9280\",\n    \"editorOverviewRuler.errorForeground\": \"#eb6f9280\",\n    \"editorOverviewRuler.findMatchForeground\": \"#6e6a8666\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#c4a7e780\",\n    \"editorOverviewRuler.infoForeground\": \"#9ccfd880\",\n    \"editorOverviewRuler.modifiedForeground\": \"#ebbcba80\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#6e6a8666\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#6e6a8666\",\n    \"editorOverviewRuler.warningForeground\": \"#f6c17780\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#6e6a8633\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#6e6a8666\",\n    \"editorPane.background\": \"#0000\",\n    \"editorRuler.foreground\": \"#6e6a8666\",\n    \"editorSuggestWidget.background\": \"#1f1d2e\",\n    \"editorSuggestWidget.border\": \"#0000\",\n    \"editorSuggestWidget.focusHighlightForeground\": \"#ebbcba\",\n    \"editorSuggestWidget.foreground\": \"#908caa\",\n    \"editorSuggestWidget.highlightForeground\": \"#ebbcba\",\n    \"editorSuggestWidget.selectedBackground\": \"#6e6a8633\",\n    \"editorSuggestWidget.selectedForeground\": \"#e0def4\",\n    \"editorSuggestWidget.selectedIconForeground\": \"#e0def4\",\n    \"editorUnnecessaryCode.border\": \"#0000\",\n    \"editorUnnecessaryCode.opacity\": \"#e0def480\",\n    \"editorWarning.border\": \"#0000\",\n    \"editorWarning.foreground\": \"#f6c177\",\n    \"editorWhitespace.foreground\": \"#6e6a86\",\n    \"editorWidget.background\": \"#1f1d2e\",\n    \"editorWidget.border\": \"#26233a\",\n    \"editorWidget.foreground\": \"#908caa\",\n    \"editorWidget.resizeBorder\": \"#6e6a86\",\n    \"errorForeground\": \"#eb6f92\",\n    \"extensionBadge.remoteBackground\": \"#c4a7e7\",\n    \"extensionBadge.remoteForeground\": \"#191724\",\n    \"extensionButton.prominentBackground\": \"#ebbcba\",\n    \"extensionButton.prominentForeground\": \"#191724\",\n    \"extensionButton.prominentHoverBackground\": \"#ebbcbae6\",\n    \"extensionIcon.preReleaseForeground\": \"#31748f\",\n    \"extensionIcon.starForeground\": \"#ebbcba\",\n    \"extensionIcon.verifiedForeground\": \"#c4a7e7\",\n    \"focusBorder\": \"#6e6a8633\",\n    \"foreground\": \"#e0def4\",\n    \"gitDecoration.addedResourceForeground\": \"#9ccfd8\",\n    \"gitDecoration.conflictingResourceForeground\": \"#eb6f92\",\n    \"gitDecoration.deletedResourceForeground\": \"#908caa\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6e6a86\",\n    \"gitDecoration.modifiedResourceForeground\": \"#ebbcba\",\n    \"gitDecoration.renamedResourceForeground\": \"#31748f\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#eb6f92\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#c4a7e7\",\n    \"gitDecoration.submoduleResourceForeground\": \"#f6c177\",\n    \"gitDecoration.untrackedResourceForeground\": \"#f6c177\",\n    \"icon.foreground\": \"#908caa\",\n    \"input.background\": \"#26233a80\",\n    \"input.border\": \"#6e6a8633\",\n    \"input.foreground\": \"#e0def4\",\n    \"input.placeholderForeground\": \"#908caa\",\n    \"inputOption.activeBackground\": \"#ebbcba26\",\n    \"inputOption.activeForeground\": \"#ebbcba\",\n    \"inputValidation.errorBackground\": \"#1f1d2e\",\n    \"inputValidation.errorBorder\": \"#6e6a8666\",\n    \"inputValidation.errorForeground\": \"#eb6f92\",\n    \"inputValidation.infoBackground\": \"#1f1d2e\",\n    \"inputValidation.infoBorder\": \"#6e6a8666\",\n    \"inputValidation.infoForeground\": \"#9ccfd8\",\n    \"inputValidation.warningBackground\": \"#1f1d2e\",\n    \"inputValidation.warningBorder\": \"#6e6a8666\",\n    \"inputValidation.warningForeground\": \"#9ccfd880\",\n    \"keybindingLabel.background\": \"#26233a\",\n    \"keybindingLabel.border\": \"#6e6a8666\",\n    \"keybindingLabel.bottomBorder\": \"#6e6a8666\",\n    \"keybindingLabel.foreground\": \"#c4a7e7\",\n    \"keybindingTable.headerBackground\": \"#26233a\",\n    \"keybindingTable.rowsBackground\": \"#1f1d2e\",\n    \"list.activeSelectionBackground\": \"#6e6a8633\",\n    \"list.activeSelectionForeground\": \"#e0def4\",\n    \"list.deemphasizedForeground\": \"#908caa\",\n    \"list.dropBackground\": \"#1f1d2e\",\n    \"list.errorForeground\": \"#eb6f92\",\n    \"list.filterMatchBackground\": \"#1f1d2e\",\n    \"list.filterMatchBorder\": \"#ebbcba\",\n    \"list.focusBackground\": \"#6e6a8666\",\n    \"list.focusForeground\": \"#e0def4\",\n    \"list.focusOutline\": \"#6e6a8633\",\n    \"list.highlightForeground\": \"#ebbcba\",\n    \"list.hoverBackground\": \"#6e6a861a\",\n    \"list.hoverForeground\": \"#e0def4\",\n    \"list.inactiveFocusBackground\": \"#6e6a861a\",\n    \"list.inactiveSelectionBackground\": \"#1f1d2e\",\n    \"list.inactiveSelectionForeground\": \"#e0def4\",\n    \"list.invalidItemForeground\": \"#eb6f92\",\n    \"list.warningForeground\": \"#f6c177\",\n    \"listFilterWidget.background\": \"#1f1d2e\",\n    \"listFilterWidget.noMatchesOutline\": \"#eb6f92\",\n    \"listFilterWidget.outline\": \"#26233a\",\n    \"menu.background\": \"#1f1d2e\",\n    \"menu.border\": \"#6e6a861a\",\n    \"menu.foreground\": \"#e0def4\",\n    \"menu.selectionBackground\": \"#6e6a8633\",\n    \"menu.selectionBorder\": \"#26233a\",\n    \"menu.selectionForeground\": \"#e0def4\",\n    \"menu.separatorBackground\": \"#6e6a8666\",\n    \"menubar.selectionBackground\": \"#6e6a8633\",\n    \"menubar.selectionBorder\": \"#6e6a861a\",\n    \"menubar.selectionForeground\": \"#e0def4\",\n    \"merge.border\": \"#26233a\",\n    \"merge.commonContentBackground\": \"#6e6a8633\",\n    \"merge.commonHeaderBackground\": \"#6e6a8633\",\n    \"merge.currentContentBackground\": \"#f6c17780\",\n    \"merge.currentHeaderBackground\": \"#f6c17780\",\n    \"merge.incomingContentBackground\": \"#9ccfd880\",\n    \"merge.incomingHeaderBackground\": \"#9ccfd880\",\n    \"minimap.background\": \"#1f1d2e\",\n    \"minimap.errorHighlight\": \"#eb6f9280\",\n    \"minimap.findMatchHighlight\": \"#6e6a8633\",\n    \"minimap.selectionHighlight\": \"#6e6a8633\",\n    \"minimap.warningHighlight\": \"#f6c17780\",\n    \"minimapGutter.addedBackground\": \"#9ccfd8\",\n    \"minimapGutter.deletedBackground\": \"#eb6f92\",\n    \"minimapGutter.modifiedBackground\": \"#ebbcba\",\n    \"minimapSlider.activeBackground\": \"#6e6a8666\",\n    \"minimapSlider.background\": \"#6e6a8633\",\n    \"minimapSlider.hoverBackground\": \"#6e6a8633\",\n    \"notebook.cellBorderColor\": \"#9ccfd880\",\n    \"notebook.cellEditorBackground\": \"#1f1d2e\",\n    \"notebook.cellHoverBackground\": \"#26233a80\",\n    \"notebook.focusedCellBackground\": \"#6e6a861a\",\n    \"notebook.focusedCellBorder\": \"#9ccfd8\",\n    \"notebook.outputContainerBackgroundColor\": \"#6e6a861a\",\n    \"notificationCenter.border\": \"#6e6a8633\",\n    \"notificationCenterHeader.background\": \"#1f1d2e\",\n    \"notificationCenterHeader.foreground\": \"#908caa\",\n    \"notificationLink.foreground\": \"#c4a7e7\",\n    \"notificationToast.border\": \"#6e6a8633\",\n    \"notifications.background\": \"#1f1d2e\",\n    \"notifications.border\": \"#6e6a8633\",\n    \"notifications.foreground\": \"#e0def4\",\n    \"notificationsErrorIcon.foreground\": \"#eb6f92\",\n    \"notificationsInfoIcon.foreground\": \"#9ccfd8\",\n    \"notificationsWarningIcon.foreground\": \"#f6c177\",\n    \"panel.background\": \"#1f1d2e\",\n    \"panel.border\": \"#0000\",\n    \"panel.dropBorder\": \"#26233a\",\n    \"panelInput.border\": \"#1f1d2e\",\n    \"panelSection.dropBackground\": \"#6e6a8633\",\n    \"panelSectionHeader.background\": \"#1f1d2e\",\n    \"panelSectionHeader.foreground\": \"#e0def4\",\n    \"panelTitle.activeBorder\": \"#6e6a8666\",\n    \"panelTitle.activeForeground\": \"#e0def4\",\n    \"panelTitle.inactiveForeground\": \"#908caa\",\n    \"peekView.border\": \"#26233a\",\n    \"peekViewEditor.background\": \"#1f1d2e\",\n    \"peekViewEditor.matchHighlightBackground\": \"#6e6a8666\",\n    \"peekViewResult.background\": \"#1f1d2e\",\n    \"peekViewResult.fileForeground\": \"#908caa\",\n    \"peekViewResult.lineForeground\": \"#908caa\",\n    \"peekViewResult.matchHighlightBackground\": \"#6e6a8666\",\n    \"peekViewResult.selectionBackground\": \"#6e6a8633\",\n    \"peekViewResult.selectionForeground\": \"#e0def4\",\n    \"peekViewTitle.background\": \"#26233a\",\n    \"peekViewTitleDescription.foreground\": \"#908caa\",\n    \"pickerGroup.border\": \"#6e6a8666\",\n    \"pickerGroup.foreground\": \"#c4a7e7\",\n    \"ports.iconRunningProcessForeground\": \"#ebbcba\",\n    \"problemsErrorIcon.foreground\": \"#eb6f92\",\n    \"problemsInfoIcon.foreground\": \"#9ccfd8\",\n    \"problemsWarningIcon.foreground\": \"#f6c177\",\n    \"progressBar.background\": \"#ebbcba\",\n    \"quickInput.background\": \"#1f1d2e\",\n    \"quickInput.foreground\": \"#908caa\",\n    \"quickInputList.focusBackground\": \"#6e6a8633\",\n    \"quickInputList.focusForeground\": \"#e0def4\",\n    \"quickInputList.focusIconForeground\": \"#e0def4\",\n    \"scrollbar.shadow\": \"#1f1d2e4d\",\n    \"scrollbarSlider.activeBackground\": \"#31748f80\",\n    \"scrollbarSlider.background\": \"#6e6a8633\",\n    \"scrollbarSlider.hoverBackground\": \"#6e6a8666\",\n    \"searchEditor.findMatchBackground\": \"#6e6a8633\",\n    \"selection.background\": \"#6e6a8666\",\n    \"settings.focusedRowBackground\": \"#1f1d2e\",\n    \"settings.focusedRowBorder\": \"#6e6a8633\",\n    \"settings.headerForeground\": \"#e0def4\",\n    \"settings.modifiedItemIndicator\": \"#ebbcba\",\n    \"settings.rowHoverBackground\": \"#1f1d2e\",\n    \"sideBar.background\": \"#191724\",\n    \"sideBar.dropBackground\": \"#1f1d2e\",\n    \"sideBar.foreground\": \"#908caa\",\n    \"sideBarSectionHeader.background\": \"#0000\",\n    \"sideBarSectionHeader.border\": \"#6e6a8633\",\n    \"statusBar.background\": \"#191724\",\n    \"statusBar.debuggingBackground\": \"#c4a7e7\",\n    \"statusBar.debuggingForeground\": \"#191724\",\n    \"statusBar.foreground\": \"#908caa\",\n    \"statusBar.noFolderBackground\": \"#191724\",\n    \"statusBar.noFolderForeground\": \"#908caa\",\n    \"statusBarItem.activeBackground\": \"#6e6a8666\",\n    \"statusBarItem.errorBackground\": \"#191724\",\n    \"statusBarItem.errorForeground\": \"#eb6f92\",\n    \"statusBarItem.hoverBackground\": \"#6e6a8633\",\n    \"statusBarItem.prominentBackground\": \"#26233a\",\n    \"statusBarItem.prominentForeground\": \"#e0def4\",\n    \"statusBarItem.prominentHoverBackground\": \"#6e6a8633\",\n    \"statusBarItem.remoteBackground\": \"#191724\",\n    \"statusBarItem.remoteForeground\": \"#f6c177\",\n    \"symbolIcon.arrayForeground\": \"#908caa\",\n    \"symbolIcon.classForeground\": \"#908caa\",\n    \"symbolIcon.colorForeground\": \"#908caa\",\n    \"symbolIcon.constantForeground\": \"#908caa\",\n    \"symbolIcon.constructorForeground\": \"#908caa\",\n    \"symbolIcon.enumeratorForeground\": \"#908caa\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#908caa\",\n    \"symbolIcon.eventForeground\": \"#908caa\",\n    \"symbolIcon.fieldForeground\": \"#908caa\",\n    \"symbolIcon.fileForeground\": \"#908caa\",\n    \"symbolIcon.folderForeground\": \"#908caa\",\n    \"symbolIcon.functionForeground\": \"#908caa\",\n    \"symbolIcon.interfaceForeground\": \"#908caa\",\n    \"symbolIcon.keyForeground\": \"#908caa\",\n    \"symbolIcon.keywordForeground\": \"#908caa\",\n    \"symbolIcon.methodForeground\": \"#908caa\",\n    \"symbolIcon.moduleForeground\": \"#908caa\",\n    \"symbolIcon.namespaceForeground\": \"#908caa\",\n    \"symbolIcon.nullForeground\": \"#908caa\",\n    \"symbolIcon.numberForeground\": \"#908caa\",\n    \"symbolIcon.objectForeground\": \"#908caa\",\n    \"symbolIcon.operatorForeground\": \"#908caa\",\n    \"symbolIcon.packageForeground\": \"#908caa\",\n    \"symbolIcon.propertyForeground\": \"#908caa\",\n    \"symbolIcon.referenceForeground\": \"#908caa\",\n    \"symbolIcon.snippetForeground\": \"#908caa\",\n    \"symbolIcon.stringForeground\": \"#908caa\",\n    \"symbolIcon.structForeground\": \"#908caa\",\n    \"symbolIcon.textForeground\": \"#908caa\",\n    \"symbolIcon.typeParameterForeground\": \"#908caa\",\n    \"symbolIcon.unitForeground\": \"#908caa\",\n    \"symbolIcon.variableForeground\": \"#908caa\",\n    \"tab.activeBackground\": \"#6e6a861a\",\n    \"tab.activeForeground\": \"#e0def4\",\n    \"tab.activeModifiedBorder\": \"#9ccfd8\",\n    \"tab.border\": \"#0000\",\n    \"tab.hoverBackground\": \"#6e6a8633\",\n    \"tab.inactiveBackground\": \"#0000\",\n    \"tab.inactiveForeground\": \"#908caa\",\n    \"tab.inactiveModifiedBorder\": \"#9ccfd880\",\n    \"tab.lastPinnedBorder\": \"#6e6a86\",\n    \"tab.unfocusedActiveBackground\": \"#0000\",\n    \"tab.unfocusedHoverBackground\": \"#0000\",\n    \"tab.unfocusedInactiveBackground\": \"#0000\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#9ccfd880\",\n    \"terminal.ansiBlack\": \"#26233a\",\n    \"terminal.ansiBlue\": \"#9ccfd8\",\n    \"terminal.ansiBrightBlack\": \"#908caa\",\n    \"terminal.ansiBrightBlue\": \"#9ccfd8\",\n    \"terminal.ansiBrightCyan\": \"#ebbcba\",\n    \"terminal.ansiBrightGreen\": \"#31748f\",\n    \"terminal.ansiBrightMagenta\": \"#c4a7e7\",\n    \"terminal.ansiBrightRed\": \"#eb6f92\",\n    \"terminal.ansiBrightWhite\": \"#e0def4\",\n    \"terminal.ansiBrightYellow\": \"#f6c177\",\n    \"terminal.ansiCyan\": \"#ebbcba\",\n    \"terminal.ansiGreen\": \"#31748f\",\n    \"terminal.ansiMagenta\": \"#c4a7e7\",\n    \"terminal.ansiRed\": \"#eb6f92\",\n    \"terminal.ansiWhite\": \"#e0def4\",\n    \"terminal.ansiYellow\": \"#f6c177\",\n    \"terminal.dropBackground\": \"#6e6a8633\",\n    \"terminal.foreground\": \"#e0def4\",\n    \"terminal.selectionBackground\": \"#6e6a8633\",\n    \"terminal.tab.activeBorder\": \"#e0def4\",\n    \"terminalCursor.background\": \"#e0def4\",\n    \"terminalCursor.foreground\": \"#6e6a86\",\n    \"textBlockQuote.background\": \"#1f1d2e\",\n    \"textBlockQuote.border\": \"#6e6a8633\",\n    \"textCodeBlock.background\": \"#1f1d2e\",\n    \"textLink.activeForeground\": \"#c4a7e7e6\",\n    \"textLink.foreground\": \"#c4a7e7\",\n    \"textPreformat.foreground\": \"#f6c177\",\n    \"textSeparator.foreground\": \"#908caa\",\n    \"titleBar.activeBackground\": \"#191724\",\n    \"titleBar.activeForeground\": \"#908caa\",\n    \"titleBar.inactiveBackground\": \"#1f1d2e\",\n    \"titleBar.inactiveForeground\": \"#908caa\",\n    \"toolbar.activeBackground\": \"#6e6a8666\",\n    \"toolbar.hoverBackground\": \"#6e6a8633\",\n    \"tree.indentGuidesStroke\": \"#908caa\",\n    \"walkThrough.embeddedEditorBackground\": \"#191724\",\n    \"welcomePage.background\": \"#191724\",\n    \"welcomePage.buttonBackground\": \"#1f1d2e\",\n    \"welcomePage.buttonHoverBackground\": \"#26233a\",\n    \"widget.shadow\": \"#1f1d2e4d\",\n    \"window.activeBorder\": \"#1f1d2e\",\n    \"window.inactiveBorder\": \"#1f1d2e\"\n  },\n  \"displayName\": \"Ros\\xE9 Pine\",\n  \"name\": \"rose-pine\",\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e6a86\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#31748f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ebbcba\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ebbcba\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.section\",\n        \"entity.name.tag\",\n        \"entity.name.namespace\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"variable.language.this\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#31748f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.range\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag\",\n        \"meta.brace\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0def4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import\",\n        \"meta.export\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#31748f\"\n      }\n    },\n    {\n      \"scope\": \"meta.directive.vue\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-value.css\",\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag.other.html\",\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#31748f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6e6a86\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#31748f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ebbcba\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other\",\n        \"variable.language\",\n        \"variable.function\",\n        \"variable.argument\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0def4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c4a7e7\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { rosePine as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW,OAAO,MAAM,CAAC;IAC3B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,iCAAiC;QACjC,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,mCAAmC;QACnC,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,iDAAiD;QACjD,6CAA6C;QAC7C,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,iCAAiC;QACjC,6BAA6B;QAC7B,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,kCAAkC;QAClC,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,qBAAqB;QACrB,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,yCAAyC;QACzC,yCAAyC;QACzC,sCAAsC;QACtC,gCAAgC;QAChC,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,kCAAkC;QAClC,8BAA8B;QAC9B,8CAA8C;QAC9C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,yBAAyB;QACzB,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,gDAAgD;QAChD,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,0CAA0C;QAC1C,8CAA8C;QAC9C,gCAAgC;QAChC,iCAAiC;QACjC,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,sCAAsC;QACtC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,2CAA2C;QAC3C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,+BAA+B;QAC/B,uBAAuB;QACvB,wBAAwB;QACxB,8BAA8B;QAC9B,0BAA0B;QAC1B,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,gBAAgB;QAChB,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,8BAA8B;QAC9B,2CAA2C;QAC3C,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,qBAAqB;QACrB,+BAA+B;QAC/B,iCAAiC;QACjC,iCAAiC;QACjC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,kCAAkC;QAClC,kCAAkC;QAClC,sCAAsC;QACtC,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,kCAAkC;QAClC,+BAA+B;QAC/B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,gCAAgC;QAChC,mCAAmC;QACnC,uCAAuC;QACvC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,2BAA2B;QAC3B,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,wCAAwC;QACxC,0BAA0B;QAC1B,gCAAgC;QAChC,qCAAqC;QACrC,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,eAAe;QACb;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}