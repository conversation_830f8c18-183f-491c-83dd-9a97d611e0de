{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/docker.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Dockerfile\", \"name\": \"docker\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.special-method.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*\\\\b(?i:(FROM))\\\\b.*?\\\\b(?i:(AS))\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*(?i:(ONBUILD)\\\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\\\s\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*(?i:(ONBUILD)\\\\s+)?(?i:(CMD|ENTRYPOINT))\\\\s\" }, { \"begin\": '\"', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.dockerfile\" } }, \"end\": '\"', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.dockerfile\" } }, \"name\": \"string.quoted.double.dockerfile\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escaped.dockerfile\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.dockerfile\" } }, \"end\": \"'\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.dockerfile\" } }, \"name\": \"string.quoted.single.dockerfile\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escaped.dockerfile\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.dockerfile\" }, \"2\": { \"name\": \"comment.line.number-sign.dockerfile\" }, \"3\": { \"name\": \"punctuation.definition.comment.dockerfile\" } }, \"comment\": \"comment.line\", \"match\": \"^(\\\\s*)((#).*$\\\\n?)\" }], \"scopeName\": \"source.dockerfile\", \"aliases\": [\"dockerfile\"] });\nvar docker = [\n  lang\n];\n\nexport { docker as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,QAAQ;IAAU,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAA2C;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAAsJ;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,SAAS;QAAmD;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAwC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAwC;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,WAAW;YAAgB,SAAS;QAAsB;KAAE;IAAE,aAAa;IAAqB,WAAW;QAAC;KAAa;AAAC;AACltD,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}