{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/github-dark-default.mjs"], "sourcesContent": ["var githubDarkDefault = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#f78166\",\n    \"activityBar.background\": \"#0d1117\",\n    \"activityBar.border\": \"#30363d\",\n    \"activityBar.foreground\": \"#e6edf3\",\n    \"activityBar.inactiveForeground\": \"#7d8590\",\n    \"activityBarBadge.background\": \"#1f6feb\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"badge.background\": \"#1f6feb\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumb.activeSelectionForeground\": \"#7d8590\",\n    \"breadcrumb.focusForeground\": \"#e6edf3\",\n    \"breadcrumb.foreground\": \"#7d8590\",\n    \"breadcrumbPicker.background\": \"#161b22\",\n    \"button.background\": \"#238636\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#2ea043\",\n    \"button.secondaryBackground\": \"#282e33\",\n    \"button.secondaryForeground\": \"#c9d1d9\",\n    \"button.secondaryHoverBackground\": \"#30363d\",\n    \"checkbox.background\": \"#161b22\",\n    \"checkbox.border\": \"#30363d\",\n    \"debugConsole.errorForeground\": \"#ffa198\",\n    \"debugConsole.infoForeground\": \"#8b949e\",\n    \"debugConsole.sourceForeground\": \"#e3b341\",\n    \"debugConsole.warningForeground\": \"#d29922\",\n    \"debugConsoleInputIcon.foreground\": \"#bc8cff\",\n    \"debugIcon.breakpointForeground\": \"#f85149\",\n    \"debugTokenExpression.boolean\": \"#56d364\",\n    \"debugTokenExpression.error\": \"#ffa198\",\n    \"debugTokenExpression.name\": \"#79c0ff\",\n    \"debugTokenExpression.number\": \"#56d364\",\n    \"debugTokenExpression.string\": \"#a5d6ff\",\n    \"debugTokenExpression.value\": \"#a5d6ff\",\n    \"debugToolBar.background\": \"#161b22\",\n    \"descriptionForeground\": \"#7d8590\",\n    \"diffEditor.insertedLineBackground\": \"#23863626\",\n    \"diffEditor.insertedTextBackground\": \"#3fb9504d\",\n    \"diffEditor.removedLineBackground\": \"#da363326\",\n    \"diffEditor.removedTextBackground\": \"#ff7b724d\",\n    \"dropdown.background\": \"#161b22\",\n    \"dropdown.border\": \"#30363d\",\n    \"dropdown.foreground\": \"#e6edf3\",\n    \"dropdown.listBackground\": \"#161b22\",\n    \"editor.background\": \"#0d1117\",\n    \"editor.findMatchBackground\": \"#9e6a03\",\n    \"editor.findMatchHighlightBackground\": \"#f2cc6080\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#2ea04366\",\n    \"editor.foldBackground\": \"#6e76811a\",\n    \"editor.foreground\": \"#e6edf3\",\n    \"editor.lineHighlightBackground\": \"#6e76811a\",\n    \"editor.linkedEditingBackground\": \"#2f81f712\",\n    \"editor.selectionHighlightBackground\": \"#3fb95040\",\n    \"editor.stackFrameHighlightBackground\": \"#bb800966\",\n    \"editor.wordHighlightBackground\": \"#6e768180\",\n    \"editor.wordHighlightBorder\": \"#6e768199\",\n    \"editor.wordHighlightStrongBackground\": \"#6e76814d\",\n    \"editor.wordHighlightStrongBorder\": \"#6e768199\",\n    \"editorBracketHighlight.foreground1\": \"#79c0ff\",\n    \"editorBracketHighlight.foreground2\": \"#56d364\",\n    \"editorBracketHighlight.foreground3\": \"#e3b341\",\n    \"editorBracketHighlight.foreground4\": \"#ffa198\",\n    \"editorBracketHighlight.foreground5\": \"#ff9bce\",\n    \"editorBracketHighlight.foreground6\": \"#d2a8ff\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#7d8590\",\n    \"editorBracketMatch.background\": \"#3fb95040\",\n    \"editorBracketMatch.border\": \"#3fb95099\",\n    \"editorCursor.foreground\": \"#2f81f7\",\n    \"editorGroup.border\": \"#30363d\",\n    \"editorGroupHeader.tabsBackground\": \"#010409\",\n    \"editorGroupHeader.tabsBorder\": \"#30363d\",\n    \"editorGutter.addedBackground\": \"#2ea04366\",\n    \"editorGutter.deletedBackground\": \"#f8514966\",\n    \"editorGutter.modifiedBackground\": \"#bb800966\",\n    \"editorIndentGuide.activeBackground\": \"#e6edf33d\",\n    \"editorIndentGuide.background\": \"#e6edf31f\",\n    \"editorInlayHint.background\": \"#8b949e33\",\n    \"editorInlayHint.foreground\": \"#7d8590\",\n    \"editorInlayHint.paramBackground\": \"#8b949e33\",\n    \"editorInlayHint.paramForeground\": \"#7d8590\",\n    \"editorInlayHint.typeBackground\": \"#8b949e33\",\n    \"editorInlayHint.typeForeground\": \"#7d8590\",\n    \"editorLineNumber.activeForeground\": \"#e6edf3\",\n    \"editorLineNumber.foreground\": \"#6e7681\",\n    \"editorOverviewRuler.border\": \"#010409\",\n    \"editorWhitespace.foreground\": \"#484f58\",\n    \"editorWidget.background\": \"#161b22\",\n    \"errorForeground\": \"#f85149\",\n    \"focusBorder\": \"#1f6feb\",\n    \"foreground\": \"#e6edf3\",\n    \"gitDecoration.addedResourceForeground\": \"#3fb950\",\n    \"gitDecoration.conflictingResourceForeground\": \"#db6d28\",\n    \"gitDecoration.deletedResourceForeground\": \"#f85149\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6e7681\",\n    \"gitDecoration.modifiedResourceForeground\": \"#d29922\",\n    \"gitDecoration.submoduleResourceForeground\": \"#7d8590\",\n    \"gitDecoration.untrackedResourceForeground\": \"#3fb950\",\n    \"icon.foreground\": \"#7d8590\",\n    \"input.background\": \"#0d1117\",\n    \"input.border\": \"#30363d\",\n    \"input.foreground\": \"#e6edf3\",\n    \"input.placeholderForeground\": \"#6e7681\",\n    \"keybindingLabel.foreground\": \"#e6edf3\",\n    \"list.activeSelectionBackground\": \"#6e768166\",\n    \"list.activeSelectionForeground\": \"#e6edf3\",\n    \"list.focusBackground\": \"#388bfd26\",\n    \"list.focusForeground\": \"#e6edf3\",\n    \"list.highlightForeground\": \"#2f81f7\",\n    \"list.hoverBackground\": \"#6e76811a\",\n    \"list.hoverForeground\": \"#e6edf3\",\n    \"list.inactiveFocusBackground\": \"#388bfd26\",\n    \"list.inactiveSelectionBackground\": \"#6e768166\",\n    \"list.inactiveSelectionForeground\": \"#e6edf3\",\n    \"minimapSlider.activeBackground\": \"#8b949e47\",\n    \"minimapSlider.background\": \"#8b949e33\",\n    \"minimapSlider.hoverBackground\": \"#8b949e3d\",\n    \"notificationCenterHeader.background\": \"#161b22\",\n    \"notificationCenterHeader.foreground\": \"#7d8590\",\n    \"notifications.background\": \"#161b22\",\n    \"notifications.border\": \"#30363d\",\n    \"notifications.foreground\": \"#e6edf3\",\n    \"notificationsErrorIcon.foreground\": \"#f85149\",\n    \"notificationsInfoIcon.foreground\": \"#2f81f7\",\n    \"notificationsWarningIcon.foreground\": \"#d29922\",\n    \"panel.background\": \"#010409\",\n    \"panel.border\": \"#30363d\",\n    \"panelInput.border\": \"#30363d\",\n    \"panelTitle.activeBorder\": \"#f78166\",\n    \"panelTitle.activeForeground\": \"#e6edf3\",\n    \"panelTitle.inactiveForeground\": \"#7d8590\",\n    \"peekViewEditor.background\": \"#6e76811a\",\n    \"peekViewEditor.matchHighlightBackground\": \"#bb800966\",\n    \"peekViewResult.background\": \"#0d1117\",\n    \"peekViewResult.matchHighlightBackground\": \"#bb800966\",\n    \"pickerGroup.border\": \"#30363d\",\n    \"pickerGroup.foreground\": \"#7d8590\",\n    \"progressBar.background\": \"#1f6feb\",\n    \"quickInput.background\": \"#161b22\",\n    \"quickInput.foreground\": \"#e6edf3\",\n    \"scrollbar.shadow\": \"#484f5833\",\n    \"scrollbarSlider.activeBackground\": \"#8b949e47\",\n    \"scrollbarSlider.background\": \"#8b949e33\",\n    \"scrollbarSlider.hoverBackground\": \"#8b949e3d\",\n    \"settings.headerForeground\": \"#e6edf3\",\n    \"settings.modifiedItemIndicator\": \"#bb800966\",\n    \"sideBar.background\": \"#010409\",\n    \"sideBar.border\": \"#30363d\",\n    \"sideBar.foreground\": \"#e6edf3\",\n    \"sideBarSectionHeader.background\": \"#010409\",\n    \"sideBarSectionHeader.border\": \"#30363d\",\n    \"sideBarSectionHeader.foreground\": \"#e6edf3\",\n    \"sideBarTitle.foreground\": \"#e6edf3\",\n    \"statusBar.background\": \"#0d1117\",\n    \"statusBar.border\": \"#30363d\",\n    \"statusBar.debuggingBackground\": \"#da3633\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.focusBorder\": \"#1f6feb80\",\n    \"statusBar.foreground\": \"#7d8590\",\n    \"statusBar.noFolderBackground\": \"#0d1117\",\n    \"statusBarItem.activeBackground\": \"#e6edf31f\",\n    \"statusBarItem.focusBorder\": \"#1f6feb\",\n    \"statusBarItem.hoverBackground\": \"#e6edf314\",\n    \"statusBarItem.prominentBackground\": \"#6e768166\",\n    \"statusBarItem.remoteBackground\": \"#30363d\",\n    \"statusBarItem.remoteForeground\": \"#e6edf3\",\n    \"symbolIcon.arrayForeground\": \"#f0883e\",\n    \"symbolIcon.booleanForeground\": \"#58a6ff\",\n    \"symbolIcon.classForeground\": \"#f0883e\",\n    \"symbolIcon.colorForeground\": \"#79c0ff\",\n    \"symbolIcon.constantForeground\": [\n      \"#aff5b4\",\n      \"#7ee787\",\n      \"#56d364\",\n      \"#3fb950\",\n      \"#2ea043\",\n      \"#238636\",\n      \"#196c2e\",\n      \"#0f5323\",\n      \"#033a16\",\n      \"#04260f\"\n    ],\n    \"symbolIcon.constructorForeground\": \"#d2a8ff\",\n    \"symbolIcon.enumeratorForeground\": \"#f0883e\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#58a6ff\",\n    \"symbolIcon.eventForeground\": \"#6e7681\",\n    \"symbolIcon.fieldForeground\": \"#f0883e\",\n    \"symbolIcon.fileForeground\": \"#d29922\",\n    \"symbolIcon.folderForeground\": \"#d29922\",\n    \"symbolIcon.functionForeground\": \"#bc8cff\",\n    \"symbolIcon.interfaceForeground\": \"#f0883e\",\n    \"symbolIcon.keyForeground\": \"#58a6ff\",\n    \"symbolIcon.keywordForeground\": \"#ff7b72\",\n    \"symbolIcon.methodForeground\": \"#bc8cff\",\n    \"symbolIcon.moduleForeground\": \"#ff7b72\",\n    \"symbolIcon.namespaceForeground\": \"#ff7b72\",\n    \"symbolIcon.nullForeground\": \"#58a6ff\",\n    \"symbolIcon.numberForeground\": \"#3fb950\",\n    \"symbolIcon.objectForeground\": \"#f0883e\",\n    \"symbolIcon.operatorForeground\": \"#79c0ff\",\n    \"symbolIcon.packageForeground\": \"#f0883e\",\n    \"symbolIcon.propertyForeground\": \"#f0883e\",\n    \"symbolIcon.referenceForeground\": \"#58a6ff\",\n    \"symbolIcon.snippetForeground\": \"#58a6ff\",\n    \"symbolIcon.stringForeground\": \"#79c0ff\",\n    \"symbolIcon.structForeground\": \"#f0883e\",\n    \"symbolIcon.textForeground\": \"#79c0ff\",\n    \"symbolIcon.typeParameterForeground\": \"#79c0ff\",\n    \"symbolIcon.unitForeground\": \"#58a6ff\",\n    \"symbolIcon.variableForeground\": \"#f0883e\",\n    \"tab.activeBackground\": \"#0d1117\",\n    \"tab.activeBorder\": \"#0d1117\",\n    \"tab.activeBorderTop\": \"#f78166\",\n    \"tab.activeForeground\": \"#e6edf3\",\n    \"tab.border\": \"#30363d\",\n    \"tab.hoverBackground\": \"#0d1117\",\n    \"tab.inactiveBackground\": \"#010409\",\n    \"tab.inactiveForeground\": \"#7d8590\",\n    \"tab.unfocusedActiveBorder\": \"#0d1117\",\n    \"tab.unfocusedActiveBorderTop\": \"#30363d\",\n    \"tab.unfocusedHoverBackground\": \"#6e76811a\",\n    \"terminal.ansiBlack\": \"#484f58\",\n    \"terminal.ansiBlue\": \"#58a6ff\",\n    \"terminal.ansiBrightBlack\": \"#6e7681\",\n    \"terminal.ansiBrightBlue\": \"#79c0ff\",\n    \"terminal.ansiBrightCyan\": \"#56d4dd\",\n    \"terminal.ansiBrightGreen\": \"#56d364\",\n    \"terminal.ansiBrightMagenta\": \"#d2a8ff\",\n    \"terminal.ansiBrightRed\": \"#ffa198\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#e3b341\",\n    \"terminal.ansiCyan\": \"#39c5cf\",\n    \"terminal.ansiGreen\": \"#3fb950\",\n    \"terminal.ansiMagenta\": \"#bc8cff\",\n    \"terminal.ansiRed\": \"#ff7b72\",\n    \"terminal.ansiWhite\": \"#b1bac4\",\n    \"terminal.ansiYellow\": \"#d29922\",\n    \"terminal.foreground\": \"#e6edf3\",\n    \"textBlockQuote.background\": \"#010409\",\n    \"textBlockQuote.border\": \"#30363d\",\n    \"textCodeBlock.background\": \"#6e768166\",\n    \"textLink.activeForeground\": \"#2f81f7\",\n    \"textLink.foreground\": \"#2f81f7\",\n    \"textPreformat.foreground\": \"#7d8590\",\n    \"textSeparator.foreground\": \"#21262d\",\n    \"titleBar.activeBackground\": \"#0d1117\",\n    \"titleBar.activeForeground\": \"#7d8590\",\n    \"titleBar.border\": \"#30363d\",\n    \"titleBar.inactiveBackground\": \"#010409\",\n    \"titleBar.inactiveForeground\": \"#7d8590\",\n    \"tree.indentGuidesStroke\": \"#21262d\",\n    \"welcomePage.buttonBackground\": \"#21262d\",\n    \"welcomePage.buttonHoverBackground\": \"#30363d\"\n  },\n  \"displayName\": \"GitHub Dark Default\",\n  \"name\": \"github-dark-default\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8b949e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7b72\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.other.constant\",\n        \"variable.other.enummember\",\n        \"variable.language\",\n        \"entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"meta.export.default\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffa657\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function\",\n        \"meta.jsx.children\",\n        \"meta.block\",\n        \"meta.tag.attributes\",\n        \"entity.name.constant\",\n        \"meta.object.member\",\n        \"meta.embedded.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e6edf3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#d2a8ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7ee787\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#ff7b72\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7b72\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e6edf3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a5d6ff\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#ffa657\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#e6edf3\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#ff7b72\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#f0f6fc\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a5d6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a5d6ff\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7ee787\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#7ee787\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffa657\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#7ee787\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e6edf3\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e6edf3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strikethrough\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#490202\",\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff7b72\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#04260f\",\n        \"foreground\": \"#7ee787\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#5a1e02\",\n        \"foreground\": \"#ffa657\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#79c0ff\",\n        \"foreground\": \"#161b22\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#d2a8ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#79c0ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8b949e\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#ffa198\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#a5d6ff\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { githubDarkDefault as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,oBAAoB,OAAO,MAAM,CAAC;IACpC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,kCAAkC;QAClC,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,kCAAkC;QAClC,kCAAkC;QAClC,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}