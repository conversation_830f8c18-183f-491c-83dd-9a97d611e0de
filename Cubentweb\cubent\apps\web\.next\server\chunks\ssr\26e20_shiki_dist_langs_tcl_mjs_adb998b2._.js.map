{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/tcl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Tcl\", \"fileTypes\": [\"tcl\"], \"foldingStartMarker\": \"\\\\{\\\\s*$\", \"foldingStopMarker\": \"^\\\\s*\\\\}\", \"name\": \"tcl\", \"patterns\": [{ \"begin\": \"(?<=^|;)\\\\s*((#))\", \"beginCaptures\": { \"1\": { \"name\": \"comment.line.number-sign.tcl\" }, \"2\": { \"name\": \"punctuation.definition.comment.tcl\" } }, \"contentName\": \"comment.line.number-sign.tcl\", \"end\": \"\\\\n\", \"patterns\": [{ \"match\": \"(\\\\\\\\\\\\\\\\|\\\\\\\\\\\\n)\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.tcl\" } }, \"match\": \"(?<=^|[\\\\[{;])\\\\s*(if|while|for|catch|default|return|break|continue|switch|exit|foreach|try|throw)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.tcl\" } }, \"match\": \"(?<=^|})\\\\s*(then|elseif|else)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.tcl\" }, \"2\": { \"name\": \"entity.name.function.tcl\" } }, \"match\": \"(?<=^|{)\\\\s*(proc)\\\\s+([^\\\\s]+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.tcl\" } }, \"match\": \"(?<=^|[\\\\[{;])\\\\s*(after|append|array|auto_execok|auto_import|auto_load|auto_mkindex|auto_mkindex_old|auto_qualify|auto_reset|bgerror|binary|cd|clock|close|concat|dde|encoding|eof|error|eval|exec|expr|fblocked|fconfigure|fcopy|file|fileevent|filename|flush|format|gets|glob|global|history|http|incr|info|interp|join|lappend|library|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort|memory|msgcat|namespace|open|package|parray|pid|pkg::create|pkg_mkIndex|proc|puts|pwd|re_syntax|read|registry|rename|resource|scan|seek|set|socket|SafeBase|source|split|string|subst|Tcl|tcl_endOfWord|tcl_findLibrary|tcl_startOfNextWord|tcl_startOfPreviousWord|tcl_wordBreakAfter|tcl_wordBreakBefore|tcltest|tclvars|tell|time|trace|unknown|unset|update|uplevel|upvar|variable|vwait)\\\\b\" }, { \"begin\": \"(?<=^|[\\\\[{;])\\\\s*(regexp|regsub)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tcl\" } }, \"comment\": \"special-case regexp/regsub keyword in order to handle the expression\", \"end\": \"[\\\\n;\\\\]]\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:.|\\\\n)\", \"name\": \"constant.character.escape.tcl\" }, { \"comment\": \"switch for regexp\", \"match\": \"-\\\\w+\\\\s*\" }, { \"applyEndPatternLast\": 1, \"begin\": \"--\\\\s*\", \"comment\": \"end of switches\", \"end\": \"\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"include\": \"#regexp\" }] }, { \"include\": \"#escape\" }, { \"include\": \"#variable\" }, { \"include\": \"#operator\" }, { \"include\": \"#numeric\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.tcl\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.tcl\" } }, \"name\": \"string.quoted.double.tcl\", \"patterns\": [{ \"include\": \"#escape\" }, { \"include\": \"#variable\" }, { \"include\": \"#embedded\" }] }], \"repository\": { \"bare-string\": { \"begin\": '(?:^|(?<=\\\\s))\"', \"comment\": \"matches a single quote-enclosed word without scoping\", \"end\": '\"([^\\\\s\\\\]]*)', \"endCaptures\": { \"1\": { \"name\": \"invalid.illegal.tcl\" } }, \"patterns\": [{ \"include\": \"#escape\" }, { \"include\": \"#variable\" }] }, \"braces\": { \"begin\": \"(?:^|(?<=\\\\s))\\\\{\", \"comment\": \"matches a single brace-enclosed word\", \"end\": \"\\\\}([^\\\\s\\\\]]*)\", \"endCaptures\": { \"1\": { \"name\": \"invalid.illegal.tcl\" } }, \"patterns\": [{ \"match\": \"\\\\\\\\[{}\\\\n]\", \"name\": \"constant.character.escape.tcl\" }, { \"include\": \"#inner-braces\" }] }, \"embedded\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.tcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.tcl\" } }, \"name\": \"source.tcl.embedded\", \"patterns\": [{ \"include\": \"source.tcl\" }] }, \"escape\": { \"match\": \"\\\\\\\\(\\\\d{1,3}|x[a-fA-F0-9]+|u[a-fA-F0-9]{1,4}|.|\\\\n)\", \"name\": \"constant.character.escape.tcl\" }, \"inner-braces\": { \"begin\": \"\\\\{\", \"comment\": \"matches a nested brace in a brace-enclosed word\", \"end\": \"\\\\}\", \"patterns\": [{ \"match\": \"\\\\\\\\[{}\\\\n]\", \"name\": \"constant.character.escape.tcl\" }, { \"include\": \"#inner-braces\" }] }, \"numeric\": { \"match\": \"(?<![a-zA-Z])([+-]?(\\\\d*[.])?\\\\d+f?)(?![\\\\.a-zA-Z])\", \"name\": \"constant.numeric.tcl\" }, \"operator\": { \"match\": \"(?<= |\\\\d)(-|\\\\+|~|&{1,2}|\\\\|{1,2}|<{1,2}|>{1,2}|\\\\*{1,2}|!|%|\\\\/|<=|>=|={1,2}|!=|\\\\^)(?= |\\\\d)\", \"name\": \"keyword.operator.tcl\" }, \"regexp\": { \"begin\": \"(?=\\\\S)(?![\\\\n;\\\\]])\", \"comment\": \"matches a single word, named as a regexp, then swallows the rest of the command\", \"end\": \"(?=[\\\\n;\\\\]])\", \"patterns\": [{ \"begin\": \"(?=[^ \\\\t\\\\n;])\", \"end\": \"(?=[ \\\\t\\\\n;])\", \"name\": \"string.regexp.tcl\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#bare-string\" }, { \"include\": \"#escape\" }, { \"include\": \"#variable\" }] }, { \"begin\": \"[ \\\\t]\", \"comment\": \"swallow the rest of the command\", \"end\": \"(?=[\\\\n;\\\\]])\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#embedded\" }, { \"include\": \"#escape\" }, { \"include\": \"#braces\" }, { \"include\": \"#string\" }] }] }, \"string\": { \"applyEndPatternLast\": 1, \"begin\": '(?:^|(?<=\\\\s))(?=\")', \"comment\": \"matches a single quote-enclosed word with scoping\", \"end\": \"\", \"name\": \"string.quoted.double.tcl\", \"patterns\": [{ \"include\": \"#bare-string\" }] }, \"variable\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.tcl\" } }, \"match\": \"(\\\\$)((?:\\\\w|::)+(\\\\([^)]+\\\\))?|\\\\{[^}]*\\\\})\", \"name\": \"support.function.tcl\" } }, \"scopeName\": \"source.tcl\" });\nvar tcl = [\n  lang\n];\n\nexport { tcl as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAM;IAAE,sBAAsB;IAAY,qBAAqB;IAAY,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,eAAe;YAAgC,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;gBAAqB;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAAwG;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAAoC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;QAAkC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;QAAmxB;QAAG;YAAE,SAAS;YAA4C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,WAAW;YAAwE,OAAO;YAAa,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;gBAAY;gBAAG;oBAAE,uBAAuB;oBAAG,SAAS;oBAAU,WAAW;oBAAmB,OAAO;oBAAI,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;KAAE;IAAE,cAAc;QAAE,eAAe;YAAE,SAAS;YAAmB,WAAW;YAAwD,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAqB,WAAW;YAAwC,OAAO;YAAmB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAAgC;QAAG,gBAAgB;YAAE,SAAS;YAAO,WAAW;YAAmD,OAAO;YAAO,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAuD,QAAQ;QAAuB;QAAG,YAAY;YAAE,SAAS;YAAmG,QAAQ;QAAuB;QAAG,UAAU;YAAE,SAAS;YAAwB,WAAW;YAAmF,OAAO;YAAiB,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,OAAO;oBAAkB,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAU,WAAW;oBAAmC,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,uBAAuB;YAAG,SAAS;YAAuB,WAAW;YAAqD,OAAO;YAAI,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAgD,QAAQ;QAAuB;IAAE;IAAG,aAAa;AAAa;AAC7kK,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}