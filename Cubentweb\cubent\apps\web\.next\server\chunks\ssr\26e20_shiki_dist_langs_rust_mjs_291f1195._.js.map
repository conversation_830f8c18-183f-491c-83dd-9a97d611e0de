{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/rust.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Rust\", \"name\": \"rust\", \"patterns\": [{ \"begin\": \"(<)(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.brackets.angle.rust\" }, \"2\": { \"name\": \"punctuation.brackets.square.rust\" } }, \"comment\": \"boxed slice literal\", \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.brackets.angle.rust\" } }, \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#types\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.macro.dollar.rust\" }, \"3\": { \"name\": \"keyword.other.crate.rust\" }, \"4\": { \"name\": \"entity.name.type.metavariable.rust\" }, \"6\": { \"name\": \"keyword.operator.key-value.rust\" }, \"7\": { \"name\": \"variable.other.metavariable.specifier.rust\" } }, \"comment\": \"macro type metavariables\", \"match\": \"(\\\\$)((crate)|([A-Z][A-Za-z0-9_]*))((:)(block|expr|ident|item|lifetime|literal|meta|path?|stmt|tt|ty|vis))?\", \"name\": \"meta.macro.metavariable.type.rust\", \"patterns\": [{ \"include\": \"#keywords\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.macro.dollar.rust\" }, \"2\": { \"name\": \"variable.other.metavariable.name.rust\" }, \"4\": { \"name\": \"keyword.operator.key-value.rust\" }, \"5\": { \"name\": \"variable.other.metavariable.specifier.rust\" } }, \"comment\": \"macro metavariables\", \"match\": \"(\\\\$)([a-z][A-Za-z0-9_]*)((:)(block|expr|ident|item|lifetime|literal|meta|path?|stmt|tt|ty|vis))?\", \"name\": \"meta.macro.metavariable.rust\", \"patterns\": [{ \"include\": \"#keywords\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.macro.rules.rust\" }, \"3\": { \"name\": \"entity.name.function.macro.rust\" }, \"4\": { \"name\": \"entity.name.type.macro.rust\" }, \"5\": { \"name\": \"punctuation.brackets.curly.rust\" } }, \"comment\": \"macro rules\", \"match\": \"\\\\b(macro_rules!)\\\\s+(([a-z0-9_]+)|([A-Z][a-z0-9_]*))\\\\s+(\\\\{)\", \"name\": \"meta.macro.rules.rust\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.rust\" }, \"2\": { \"name\": \"entity.name.module.rust\" } }, \"comment\": \"modules\", \"match\": \"(mod)\\\\s+((?:r#(?!crate|[Ss]elf|super))?[a-z][A-Za-z0-9_]*)\" }, { \"begin\": \"\\\\b(extern)\\\\s+(crate)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.rust\" }, \"2\": { \"name\": \"keyword.other.crate.rust\" } }, \"comment\": \"external crate imports\", \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.semi.rust\" } }, \"name\": \"meta.import.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#punctuation\" }] }, { \"begin\": \"\\\\b(use)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.rust\" } }, \"comment\": \"use statements\", \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.semi.rust\" } }, \"name\": \"meta.use.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#namespaces\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#types\" }, { \"include\": \"#lvariables\" }] }, { \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#attributes\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#constants\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#functions\" }, { \"include\": \"#types\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#macros\" }, { \"include\": \"#namespaces\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#strings\" }, { \"include\": \"#variables\" }], \"repository\": { \"attributes\": { \"begin\": \"(#)(!?)(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.attribute.rust\" }, \"3\": { \"name\": \"punctuation.brackets.attribute.rust\" } }, \"comment\": \"attributes\", \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.brackets.attribute.rust\" } }, \"name\": \"meta.attribute.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#strings\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#types\" }] }, \"block-comments\": { \"patterns\": [{ \"comment\": \"empty block comments\", \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.rust\" }, { \"begin\": \"/\\\\*\\\\*\", \"comment\": \"block documentation comments\", \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }] }, { \"begin\": \"/\\\\*(?!\\\\*)\", \"comment\": \"block comments\", \"end\": \"\\\\*/\", \"name\": \"comment.block.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }] }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.rust\" } }, \"comment\": \"documentation comments\", \"match\": \"(///).*$\", \"name\": \"comment.line.documentation.rust\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.rust\" } }, \"comment\": \"line comments\", \"match\": \"(//).*$\", \"name\": \"comment.line.double-slash.rust\" }] }, \"constants\": { \"patterns\": [{ \"comment\": \"ALL CAPS constants\", \"match\": \"\\\\b[A-Z]{2}[A-Z0-9_]*\\\\b\", \"name\": \"constant.other.caps.rust\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.rust\" }, \"2\": { \"name\": \"constant.other.caps.rust\" } }, \"comment\": \"constant declarations\", \"match\": \"\\\\b(const)\\\\s+([A-Z][A-Za-z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.dot.decimal.rust\" }, \"2\": { \"name\": \"keyword.operator.exponent.rust\" }, \"3\": { \"name\": \"keyword.operator.exponent.sign.rust\" }, \"4\": { \"name\": \"constant.numeric.decimal.exponent.mantissa.rust\" }, \"5\": { \"name\": \"entity.name.type.numeric.rust\" } }, \"comment\": \"decimal integers and floats\", \"match\": \"\\\\b\\\\d[\\\\d_]*(\\\\.?)[\\\\d_]*(?:(E|e)([+-]?)([\\\\d_]+))?(f32|f64|i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\\\b\", \"name\": \"constant.numeric.decimal.rust\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.numeric.rust\" } }, \"comment\": \"hexadecimal integers\", \"match\": \"\\\\b0x[\\\\da-fA-F_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\\\b\", \"name\": \"constant.numeric.hex.rust\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.numeric.rust\" } }, \"comment\": \"octal integers\", \"match\": \"\\\\b0o[0-7_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\\\b\", \"name\": \"constant.numeric.oct.rust\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.numeric.rust\" } }, \"comment\": \"binary integers\", \"match\": \"\\\\b0b[01_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\\\b\", \"name\": \"constant.numeric.bin.rust\" }, { \"comment\": \"booleans\", \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.bool.rust\" }] }, \"escapes\": { \"captures\": { \"1\": { \"name\": \"constant.character.escape.backslash.rust\" }, \"2\": { \"name\": \"constant.character.escape.bit.rust\" }, \"3\": { \"name\": \"constant.character.escape.unicode.rust\" }, \"4\": { \"name\": \"constant.character.escape.unicode.punctuation.rust\" }, \"5\": { \"name\": \"constant.character.escape.unicode.punctuation.rust\" } }, \"comment\": \"escapes: ASCII, byte, Unicode, quote, regex\", \"match\": \"(\\\\\\\\)(?:(?:(x[0-7][\\\\da-fA-F])|(u(\\\\{)[\\\\da-fA-F]{4,6}(\\\\}))|.))\", \"name\": \"constant.character.escape.rust\" }, \"functions\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.rust\" }, \"2\": { \"name\": \"punctuation.brackets.round.rust\" } }, \"comment\": \"pub as a function\", \"match\": \"\\\\b(pub)(\\\\()\" }, { \"begin\": \"\\\\b(fn)\\\\s+((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)((\\\\()|(<))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.fn.rust\" }, \"2\": { \"name\": \"entity.name.function.rust\" }, \"4\": { \"name\": \"punctuation.brackets.round.rust\" }, \"5\": { \"name\": \"punctuation.brackets.angle.rust\" } }, \"comment\": \"function definition\", \"end\": \"(\\\\{)|(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.brackets.curly.rust\" }, \"2\": { \"name\": \"punctuation.semi.rust\" } }, \"name\": \"meta.function.definition.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#constants\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#functions\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#macros\" }, { \"include\": \"#namespaces\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#strings\" }, { \"include\": \"#types\" }, { \"include\": \"#variables\" }] }, { \"begin\": \"((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.rust\" }, \"2\": { \"name\": \"punctuation.brackets.round.rust\" } }, \"comment\": \"function/method calls, chaining\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.brackets.round.rust\" } }, \"name\": \"meta.function.call.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#attributes\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#constants\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#functions\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#macros\" }, { \"include\": \"#namespaces\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#strings\" }, { \"include\": \"#types\" }, { \"include\": \"#variables\" }] }, { \"begin\": \"((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)(?=::<.*>\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.rust\" } }, \"comment\": \"function/method calls with turbofish\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.brackets.round.rust\" } }, \"name\": \"meta.function.call.rust\", \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#attributes\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#constants\" }, { \"include\": \"#gtypes\" }, { \"include\": \"#functions\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#macros\" }, { \"include\": \"#namespaces\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#strings\" }, { \"include\": \"#types\" }, { \"include\": \"#variables\" }] }] }, \"gtypes\": { \"patterns\": [{ \"comment\": \"option types\", \"match\": \"\\\\b(Some|None)\\\\b\", \"name\": \"entity.name.type.option.rust\" }, { \"comment\": \"result types\", \"match\": \"\\\\b(Ok|Err)\\\\b\", \"name\": \"entity.name.type.result.rust\" }] }, \"interpolations\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.interpolation.rust\" }, \"2\": { \"name\": \"punctuation.definition.interpolation.rust\" } }, \"comment\": \"curly brace interpolations\", \"match\": '({)[^\"{}]*(})', \"name\": \"meta.interpolation.rust\" }, \"keywords\": { \"patterns\": [{ \"comment\": \"control flow keywords\", \"match\": \"\\\\b(await|break|continue|do|else|for|if|loop|match|return|try|while|yield)\\\\b\", \"name\": \"keyword.control.rust\" }, { \"comment\": \"storage keywords\", \"match\": \"\\\\b(extern|let|macro|mod)\\\\b\", \"name\": \"keyword.other.rust storage.type.rust\" }, { \"comment\": \"const keyword\", \"match\": \"\\\\b(const)\\\\b\", \"name\": \"storage.modifier.rust\" }, { \"comment\": \"type keyword\", \"match\": \"\\\\b(type)\\\\b\", \"name\": \"keyword.declaration.type.rust storage.type.rust\" }, { \"comment\": \"enum keyword\", \"match\": \"\\\\b(enum)\\\\b\", \"name\": \"keyword.declaration.enum.rust storage.type.rust\" }, { \"comment\": \"trait keyword\", \"match\": \"\\\\b(trait)\\\\b\", \"name\": \"keyword.declaration.trait.rust storage.type.rust\" }, { \"comment\": \"struct keyword\", \"match\": \"\\\\b(struct)\\\\b\", \"name\": \"keyword.declaration.struct.rust storage.type.rust\" }, { \"comment\": \"storage modifiers\", \"match\": \"\\\\b(abstract|static)\\\\b\", \"name\": \"storage.modifier.rust\" }, { \"comment\": \"other keywords\", \"match\": \"\\\\b(as|async|become|box|dyn|move|final|gen|impl|in|override|priv|pub|ref|typeof|union|unsafe|unsized|use|virtual|where)\\\\b\", \"name\": \"keyword.other.rust\" }, { \"comment\": \"fn\", \"match\": \"\\\\bfn\\\\b\", \"name\": \"keyword.other.fn.rust\" }, { \"comment\": \"crate\", \"match\": \"\\\\bcrate\\\\b\", \"name\": \"keyword.other.crate.rust\" }, { \"comment\": \"mut\", \"match\": \"\\\\bmut\\\\b\", \"name\": \"storage.modifier.mut.rust\" }, { \"comment\": \"logical operators\", \"match\": \"(\\\\^|\\\\||\\\\|\\\\||&&|<<|>>|!)(?!=)\", \"name\": \"keyword.operator.logical.rust\" }, { \"comment\": \"logical AND, borrow references\", \"match\": \"&(?![&=])\", \"name\": \"keyword.operator.borrow.and.rust\" }, { \"comment\": \"assignment operators\", \"match\": \"(\\\\+=|-=|\\\\*=|/=|%=|\\\\^=|&=|\\\\|=|<<=|>>=)\", \"name\": \"keyword.operator.assignment.rust\" }, { \"comment\": \"single equal\", \"match\": \"(?<![<>])=(?!=|>)\", \"name\": \"keyword.operator.assignment.equal.rust\" }, { \"comment\": \"comparison operators\", \"match\": \"(=(=)?(?!>)|!=|<=|(?<!=)>=)\", \"name\": \"keyword.operator.comparison.rust\" }, { \"comment\": \"math operators\", \"match\": \"(([+%]|(\\\\*(?!\\\\w)))(?!=))|(-(?!>))|(/(?!/))\", \"name\": \"keyword.operator.math.rust\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.brackets.round.rust\" }, \"2\": { \"name\": \"punctuation.brackets.square.rust\" }, \"3\": { \"name\": \"punctuation.brackets.curly.rust\" }, \"4\": { \"name\": \"keyword.operator.comparison.rust\" }, \"5\": { \"name\": \"punctuation.brackets.round.rust\" }, \"6\": { \"name\": \"punctuation.brackets.square.rust\" }, \"7\": { \"name\": \"punctuation.brackets.curly.rust\" } }, \"comment\": \"less than, greater than (special case)\", \"match\": \"(?:\\\\b|(?:(\\\\))|(\\\\])|(\\\\})))[ \\\\t]+([<>])[ \\\\t]+(?:\\\\b|(?:(\\\\()|(\\\\[)|(\\\\{)))\" }, { \"comment\": \"namespace operator\", \"match\": \"::\", \"name\": \"keyword.operator.namespace.rust\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.dereference.rust\" } }, \"comment\": \"dereference asterisk\", \"match\": \"(\\\\*)(?=\\\\w+)\" }, { \"comment\": \"subpattern binding\", \"match\": \"@\", \"name\": \"keyword.operator.subpattern.rust\" }, { \"comment\": \"dot access\", \"match\": \"\\\\.(?!\\\\.)\", \"name\": \"keyword.operator.access.dot.rust\" }, { \"comment\": \"ranges, range patterns\", \"match\": \"\\\\.{2}(=|\\\\.)?\", \"name\": \"keyword.operator.range.rust\" }, { \"comment\": \"colon\", \"match\": \":(?!:)\", \"name\": \"keyword.operator.key-value.rust\" }, { \"comment\": \"dashrocket, skinny arrow\", \"match\": \"->|<-\", \"name\": \"keyword.operator.arrow.skinny.rust\" }, { \"comment\": \"hashrocket, fat arrow\", \"match\": \"=>\", \"name\": \"keyword.operator.arrow.fat.rust\" }, { \"comment\": \"dollar macros\", \"match\": \"\\\\$\", \"name\": \"keyword.operator.macro.dollar.rust\" }, { \"comment\": \"question mark operator, questionably sized, macro kleene matcher\", \"match\": \"\\\\?\", \"name\": \"keyword.operator.question.rust\" }] }, \"lifetimes\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.lifetime.rust\" }, \"2\": { \"name\": \"entity.name.type.lifetime.rust\" } }, \"comment\": \"named lifetime parameters\", \"match\": \"(['])([a-zA-Z_][0-9a-zA-Z_]*)(?!['])\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.borrow.rust\" }, \"2\": { \"name\": \"punctuation.definition.lifetime.rust\" }, \"3\": { \"name\": \"entity.name.type.lifetime.rust\" } }, \"comment\": \"borrowing references to named lifetimes\", \"match\": \"(\\\\&)(['])([a-zA-Z_][0-9a-zA-Z_]*)(?!['])\\\\b\" }] }, \"lvariables\": { \"patterns\": [{ \"comment\": \"self\", \"match\": \"\\\\b[Ss]elf\\\\b\", \"name\": \"variable.language.self.rust\" }, { \"comment\": \"super\", \"match\": \"\\\\bsuper\\\\b\", \"name\": \"variable.language.super.rust\" }] }, \"macros\": { \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"entity.name.function.macro.rust\" }, \"3\": { \"name\": \"entity.name.type.macro.rust\" } }, \"comment\": \"macros\", \"match\": \"(([a-z_][A-Za-z0-9_]*!)|([A-Z_][A-Za-z0-9_]*!))\", \"name\": \"meta.macro.rust\" }] }, \"namespaces\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.namespace.rust\" }, \"2\": { \"name\": \"keyword.operator.namespace.rust\" } }, \"comment\": \"namespace (non-type, non-function path segment)\", \"match\": \"(?<![A-Za-z0-9_])([A-Za-z0-9_]+)((?<!super|self)::)\" }] }, \"punctuation\": { \"patterns\": [{ \"comment\": \"comma\", \"match\": \",\", \"name\": \"punctuation.comma.rust\" }, { \"comment\": \"curly braces\", \"match\": \"[{}]\", \"name\": \"punctuation.brackets.curly.rust\" }, { \"comment\": \"parentheses, round brackets\", \"match\": \"[()]\", \"name\": \"punctuation.brackets.round.rust\" }, { \"comment\": \"semicolon\", \"match\": \";\", \"name\": \"punctuation.semi.rust\" }, { \"comment\": \"square brackets\", \"match\": \"[\\\\[\\\\]]\", \"name\": \"punctuation.brackets.square.rust\" }, { \"comment\": \"angle brackets\", \"match\": \"(?<!=)[<>]\", \"name\": \"punctuation.brackets.angle.rust\" }] }, \"strings\": { \"patterns\": [{ \"begin\": '(b?)(\")', \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.byte.raw.rust\" }, \"2\": { \"name\": \"punctuation.definition.string.rust\" } }, \"comment\": \"double-quoted strings and byte strings\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.rust\" } }, \"name\": \"string.quoted.double.rust\", \"patterns\": [{ \"include\": \"#escapes\" }, { \"include\": \"#interpolations\" }] }, { \"begin\": '(b?r)(#*)(\")', \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.byte.raw.rust\" }, \"2\": { \"name\": \"punctuation.definition.string.raw.rust\" }, \"3\": { \"name\": \"punctuation.definition.string.rust\" } }, \"comment\": \"double-quoted raw strings and raw byte strings\", \"end\": '(\")(\\\\2)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.rust\" }, \"2\": { \"name\": \"punctuation.definition.string.raw.rust\" } }, \"name\": \"string.quoted.double.rust\" }, { \"begin\": \"(b)?(')\", \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.byte.raw.rust\" }, \"2\": { \"name\": \"punctuation.definition.char.rust\" } }, \"comment\": \"characters and bytes\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.char.rust\" } }, \"name\": \"string.quoted.single.char.rust\", \"patterns\": [{ \"include\": \"#escapes\" }] }] }, \"types\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.type.numeric.rust\" } }, \"comment\": \"numeric types\", \"match\": \"(?<![A-Za-z])(f32|f64|i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)\\\\b\" }, { \"begin\": \"\\\\b(_?[A-Z][A-Za-z0-9_]*)(<)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.type.rust\" }, \"2\": { \"name\": \"punctuation.brackets.angle.rust\" } }, \"comment\": \"parameterized types\", \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.brackets.angle.rust\" } }, \"patterns\": [{ \"include\": \"#block-comments\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#lvariables\" }, { \"include\": \"#lifetimes\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#types\" }, { \"include\": \"#variables\" }] }, { \"comment\": \"primitive types\", \"match\": \"\\\\b(bool|char|str)\\\\b\", \"name\": \"entity.name.type.primitive.rust\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.trait.rust storage.type.rust\" }, \"2\": { \"name\": \"entity.name.type.trait.rust\" } }, \"comment\": \"trait declarations\", \"match\": \"\\\\b(trait)\\\\s+(_?[A-Z][A-Za-z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.struct.rust storage.type.rust\" }, \"2\": { \"name\": \"entity.name.type.struct.rust\" } }, \"comment\": \"struct declarations\", \"match\": \"\\\\b(struct)\\\\s+(_?[A-Z][A-Za-z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.enum.rust storage.type.rust\" }, \"2\": { \"name\": \"entity.name.type.enum.rust\" } }, \"comment\": \"enum declarations\", \"match\": \"\\\\b(enum)\\\\s+(_?[A-Z][A-Za-z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.declaration.type.rust storage.type.rust\" }, \"2\": { \"name\": \"entity.name.type.declaration.rust\" } }, \"comment\": \"type declarations\", \"match\": \"\\\\b(type)\\\\s+(_?[A-Z][A-Za-z0-9_]*)\\\\b\" }, { \"comment\": \"types\", \"match\": \"\\\\b_?[A-Z][A-Za-z0-9_]*\\\\b(?!!)\", \"name\": \"entity.name.type.rust\" }] }, \"variables\": { \"patterns\": [{ \"comment\": \"variables\", \"match\": \"\\\\b(?<!(?<!\\\\.)\\\\.)(?:r#(?!(crate|[Ss]elf|super)))?[a-z0-9_]+\\\\b\", \"name\": \"variable.other.rust\" }] } }, \"scopeName\": \"source.rust\", \"aliases\": [\"rs\"] });\nvar rust = [\n  lang\n];\n\nexport { rust as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAY,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,WAAW;YAAuB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,WAAW;YAA4B,SAAS;YAA+G,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,WAAW;YAAuB,SAAS;YAAqG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,WAAW;YAAe,SAAS;YAAkE,QAAQ;QAAwB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,WAAW;YAAW,SAAS;QAA8D;QAAG;YAAE,SAAS;YAA0B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,WAAW;YAA0B,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAe,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,WAAW;YAAkB,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,cAAc;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,WAAW;YAAc,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAwB,SAAS;oBAAY,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAW,WAAW;oBAAgC,OAAO;oBAAQ,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,WAAW;oBAAkB,OAAO;oBAAQ,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,WAAW;oBAA0B,SAAS;oBAAY,QAAQ;gBAAkC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,WAAW;oBAAiB,SAAS;oBAAW,QAAQ;gBAAiC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAsB,SAAS;oBAA4B,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,WAAW;oBAAyB,SAAS;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAA+B,SAAS;oBAAyH,QAAQ;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAAwB,SAAS;oBAA+E,QAAQ;gBAA4B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAAkB,SAAS;oBAAyE,QAAQ;gBAA4B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAAmB,SAAS;oBAAwE,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;oBAAY,SAAS;oBAAsB,QAAQ;gBAA8B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqD;gBAAG,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,WAAW;YAA+C,SAAS;YAAqE,QAAQ;QAAiC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAAqB,SAAS;gBAAgB;gBAAG;oBAAE,SAAS;oBAAuE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAAuB,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAAmC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,WAAW;oBAAwC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAgB,SAAS;oBAAqB,QAAQ;gBAA+B;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAkB,QAAQ;gBAA+B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,WAAW;YAA8B,SAAS;YAAiB,QAAQ;QAA0B;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAyB,SAAS;oBAAiF,QAAQ;gBAAuB;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAAgC,QAAQ;gBAAuC;gBAAG;oBAAE,WAAW;oBAAiB,SAAS;oBAAiB,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAgB,QAAQ;gBAAkD;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAgB,QAAQ;gBAAkD;gBAAG;oBAAE,WAAW;oBAAiB,SAAS;oBAAiB,QAAQ;gBAAmD;gBAAG;oBAAE,WAAW;oBAAkB,SAAS;oBAAkB,QAAQ;gBAAoD;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;oBAA2B,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;oBAAkB,SAAS;oBAA8H,QAAQ;gBAAqB;gBAAG;oBAAE,WAAW;oBAAM,SAAS;oBAAY,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;oBAAS,SAAS;oBAAe,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;oBAAO,SAAS;oBAAa,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;oBAAoC,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;oBAAkC,SAAS;oBAAa,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAwB,SAAS;oBAA6C,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAqB,QAAQ;gBAAyC;gBAAG;oBAAE,WAAW;oBAAwB,SAAS;oBAA+B,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAkB,SAAS;oBAAgD,QAAQ;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAA0C,SAAS;gBAAiF;gBAAG;oBAAE,WAAW;oBAAsB,SAAS;oBAAM,QAAQ;gBAAkC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,WAAW;oBAAwB,SAAS;gBAAgB;gBAAG;oBAAE,WAAW;oBAAsB,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAc,SAAS;oBAAc,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAA0B,SAAS;oBAAkB,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;oBAAS,SAAS;oBAAU,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;oBAA4B,SAAS;oBAAS,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;oBAAyB,SAAS;oBAAM,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;oBAAiB,SAAS;oBAAO,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;oBAAoE,SAAS;oBAAO,QAAQ;gBAAiC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,WAAW;oBAA6B,SAAS;gBAA0C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,WAAW;oBAA2C,SAAS;gBAA+C;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAQ,SAAS;oBAAiB,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;oBAAS,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,WAAW;oBAAU,SAAS;oBAAmD,QAAQ;gBAAkB;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAAmD,SAAS;gBAAsD;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAS,SAAS;oBAAK,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAgB,SAAS;oBAAQ,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;oBAA+B,SAAS;oBAAQ,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;oBAAa,SAAS;oBAAK,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;oBAAmB,SAAS;oBAAY,QAAQ;gBAAmC;gBAAG;oBAAE,WAAW;oBAAkB,SAAS;oBAAc,QAAQ;gBAAkC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,WAAW;oBAA0C,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,WAAW;oBAAkD,OAAO;oBAAY,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAAwB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,WAAW;oBAAiB,SAAS;gBAAgF;gBAAG;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,WAAW;oBAAuB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;oBAAmB,SAAS;oBAAyB,QAAQ;gBAAkC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,WAAW;oBAAsB,SAAS;gBAA0C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,WAAW;oBAAuB,SAAS;gBAA2C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,WAAW;oBAAqB,SAAS;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,WAAW;oBAAqB,SAAS;gBAAyC;gBAAG;oBAAE,WAAW;oBAAS,SAAS;oBAAmC,QAAQ;gBAAwB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAa,SAAS;oBAAoE,QAAQ;gBAAsB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAe,WAAW;QAAC;KAAK;AAAC;AACxklB,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}