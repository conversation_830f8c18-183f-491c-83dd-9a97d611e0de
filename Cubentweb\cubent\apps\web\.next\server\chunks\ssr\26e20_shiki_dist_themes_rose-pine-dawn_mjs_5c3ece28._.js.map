{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/rose-pine-dawn.mjs"], "sourcesContent": ["var rosePineDawn = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#575279\",\n    \"activityBar.background\": \"#faf4ed\",\n    \"activityBar.dropBorder\": \"#f2e9e1\",\n    \"activityBar.foreground\": \"#575279\",\n    \"activityBar.inactiveForeground\": \"#797593\",\n    \"activityBarBadge.background\": \"#d7827e\",\n    \"activityBarBadge.foreground\": \"#faf4ed\",\n    \"badge.background\": \"#d7827e\",\n    \"badge.foreground\": \"#faf4ed\",\n    \"banner.background\": \"#fffaf3\",\n    \"banner.foreground\": \"#575279\",\n    \"banner.iconForeground\": \"#797593\",\n    \"breadcrumb.activeSelectionForeground\": \"#d7827e\",\n    \"breadcrumb.background\": \"#faf4ed\",\n    \"breadcrumb.focusForeground\": \"#797593\",\n    \"breadcrumb.foreground\": \"#9893a5\",\n    \"breadcrumbPicker.background\": \"#fffaf3\",\n    \"button.background\": \"#d7827e\",\n    \"button.foreground\": \"#faf4ed\",\n    \"button.hoverBackground\": \"#d7827ee6\",\n    \"button.secondaryBackground\": \"#fffaf3\",\n    \"button.secondaryForeground\": \"#575279\",\n    \"button.secondaryHoverBackground\": \"#f2e9e1\",\n    \"charts.blue\": \"#56949f\",\n    \"charts.foreground\": \"#575279\",\n    \"charts.green\": \"#286983\",\n    \"charts.lines\": \"#797593\",\n    \"charts.orange\": \"#d7827e\",\n    \"charts.purple\": \"#907aa9\",\n    \"charts.red\": \"#b4637a\",\n    \"charts.yellow\": \"#ea9d34\",\n    \"checkbox.background\": \"#fffaf3\",\n    \"checkbox.border\": \"#6e6a8614\",\n    \"checkbox.foreground\": \"#575279\",\n    \"debugExceptionWidget.background\": \"#fffaf3\",\n    \"debugExceptionWidget.border\": \"#6e6a8614\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#797593\",\n    \"debugIcon.breakpointDisabledForeground\": \"#797593\",\n    \"debugIcon.breakpointForeground\": \"#797593\",\n    \"debugIcon.breakpointStackframeForeground\": \"#797593\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#797593\",\n    \"debugIcon.continueForeground\": \"#797593\",\n    \"debugIcon.disconnectForeground\": \"#797593\",\n    \"debugIcon.pauseForeground\": \"#797593\",\n    \"debugIcon.restartForeground\": \"#797593\",\n    \"debugIcon.startForeground\": \"#797593\",\n    \"debugIcon.stepBackForeground\": \"#797593\",\n    \"debugIcon.stepIntoForeground\": \"#797593\",\n    \"debugIcon.stepOutForeground\": \"#797593\",\n    \"debugIcon.stepOverForeground\": \"#797593\",\n    \"debugIcon.stopForeground\": \"#b4637a\",\n    \"debugToolBar.background\": \"#fffaf3\",\n    \"debugToolBar.border\": \"#f2e9e1\",\n    \"descriptionForeground\": \"#797593\",\n    \"diffEditor.border\": \"#f2e9e1\",\n    \"diffEditor.diagonalFill\": \"#6e6a8626\",\n    \"diffEditor.insertedLineBackground\": \"#56949f26\",\n    \"diffEditor.insertedTextBackground\": \"#56949f26\",\n    \"diffEditor.removedLineBackground\": \"#b4637a26\",\n    \"diffEditor.removedTextBackground\": \"#b4637a26\",\n    \"diffEditorOverview.insertedForeground\": \"#56949f80\",\n    \"diffEditorOverview.removedForeground\": \"#b4637a80\",\n    \"dropdown.background\": \"#fffaf3\",\n    \"dropdown.border\": \"#6e6a8614\",\n    \"dropdown.foreground\": \"#575279\",\n    \"dropdown.listBackground\": \"#fffaf3\",\n    \"editor.background\": \"#faf4ed\",\n    \"editor.findMatchBackground\": \"#6e6a8626\",\n    \"editor.findMatchHighlightBackground\": \"#6e6a8626\",\n    \"editor.findRangeHighlightBackground\": \"#6e6a8626\",\n    \"editor.findRangeHighlightBorder\": \"#0000\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#6e6a8614\",\n    \"editor.foldBackground\": \"#fffaf3\",\n    \"editor.foreground\": \"#575279\",\n    \"editor.hoverHighlightBackground\": \"#0000\",\n    \"editor.inactiveSelectionBackground\": \"#6e6a860d\",\n    \"editor.inlineValuesBackground\": \"#0000\",\n    \"editor.inlineValuesForeground\": \"#797593\",\n    \"editor.lineHighlightBackground\": \"#6e6a860d\",\n    \"editor.lineHighlightBorder\": \"#0000\",\n    \"editor.linkedEditingBackground\": \"#fffaf3\",\n    \"editor.rangeHighlightBackground\": \"#6e6a860d\",\n    \"editor.selectionBackground\": \"#6e6a8614\",\n    \"editor.selectionForeground\": \"#575279\",\n    \"editor.selectionHighlightBackground\": \"#6e6a8614\",\n    \"editor.selectionHighlightBorder\": \"#faf4ed\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#6e6a8614\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#fffaf3\",\n    \"editor.snippetTabstopHighlightBackground\": \"#6e6a8614\",\n    \"editor.snippetTabstopHighlightBorder\": \"#fffaf3\",\n    \"editor.stackFrameHighlightBackground\": \"#6e6a8614\",\n    \"editor.symbolHighlightBackground\": \"#6e6a8614\",\n    \"editor.symbolHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightBackground\": \"#6e6a8614\",\n    \"editor.wordHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightStrongBackground\": \"#6e6a8614\",\n    \"editor.wordHighlightStrongBorder\": \"#6e6a8614\",\n    \"editorBracketHighlight.foreground1\": \"#b4637a80\",\n    \"editorBracketHighlight.foreground2\": \"#28698380\",\n    \"editorBracketHighlight.foreground3\": \"#ea9d3480\",\n    \"editorBracketHighlight.foreground4\": \"#56949f80\",\n    \"editorBracketHighlight.foreground5\": \"#d7827e80\",\n    \"editorBracketHighlight.foreground6\": \"#907aa980\",\n    \"editorBracketMatch.background\": \"#0000\",\n    \"editorBracketMatch.border\": \"#797593\",\n    \"editorBracketPairGuide.activeBackground1\": \"#286983\",\n    \"editorBracketPairGuide.activeBackground2\": \"#d7827e\",\n    \"editorBracketPairGuide.activeBackground3\": \"#907aa9\",\n    \"editorBracketPairGuide.activeBackground4\": \"#56949f\",\n    \"editorBracketPairGuide.activeBackground5\": \"#ea9d34\",\n    \"editorBracketPairGuide.activeBackground6\": \"#b4637a\",\n    \"editorBracketPairGuide.background1\": \"#28698380\",\n    \"editorBracketPairGuide.background2\": \"#d7827e80\",\n    \"editorBracketPairGuide.background3\": \"#907aa980\",\n    \"editorBracketPairGuide.background4\": \"#56949f80\",\n    \"editorBracketPairGuide.background5\": \"#ea9d3480\",\n    \"editorBracketPairGuide.background6\": \"#b4637a80\",\n    \"editorCodeLens.foreground\": \"#d7827e\",\n    \"editorCursor.background\": \"#575279\",\n    \"editorCursor.foreground\": \"#9893a5\",\n    \"editorError.border\": \"#0000\",\n    \"editorError.foreground\": \"#b4637a\",\n    \"editorGhostText.foreground\": \"#797593\",\n    \"editorGroup.border\": \"#0000\",\n    \"editorGroup.dropBackground\": \"#fffaf3\",\n    \"editorGroup.emptyBackground\": \"#0000\",\n    \"editorGroup.focusedEmptyBorder\": \"#0000\",\n    \"editorGroupHeader.noTabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBorder\": \"#0000\",\n    \"editorGutter.addedBackground\": \"#56949f\",\n    \"editorGutter.background\": \"#faf4ed\",\n    \"editorGutter.commentRangeForeground\": \"#797593\",\n    \"editorGutter.deletedBackground\": \"#b4637a\",\n    \"editorGutter.foldingControlForeground\": \"#907aa9\",\n    \"editorGutter.modifiedBackground\": \"#d7827e\",\n    \"editorHint.border\": \"#0000\",\n    \"editorHint.foreground\": \"#797593\",\n    \"editorHoverWidget.background\": \"#fffaf3\",\n    \"editorHoverWidget.border\": \"#9893a580\",\n    \"editorHoverWidget.foreground\": \"#797593\",\n    \"editorHoverWidget.highlightForeground\": \"#575279\",\n    \"editorHoverWidget.statusBarBackground\": \"#0000\",\n    \"editorIndentGuide.activeBackground\": \"#9893a5\",\n    \"editorIndentGuide.background\": \"#6e6a8626\",\n    \"editorInfo.border\": \"#f2e9e1\",\n    \"editorInfo.foreground\": \"#56949f\",\n    \"editorInlayHint.background\": \"#f2e9e1\",\n    \"editorInlayHint.foreground\": \"#797593\",\n    \"editorInlayHint.parameterBackground\": \"#f2e9e1\",\n    \"editorInlayHint.parameterForeground\": \"#907aa9\",\n    \"editorInlayHint.typeBackground\": \"#f2e9e1\",\n    \"editorInlayHint.typeForeground\": \"#56949f\",\n    \"editorLightBulb.foreground\": \"#286983\",\n    \"editorLightBulbAutoFix.foreground\": \"#d7827e\",\n    \"editorLineNumber.activeForeground\": \"#575279\",\n    \"editorLineNumber.foreground\": \"#797593\",\n    \"editorLink.activeForeground\": \"#d7827e\",\n    \"editorMarkerNavigation.background\": \"#fffaf3\",\n    \"editorMarkerNavigationError.background\": \"#fffaf3\",\n    \"editorMarkerNavigationInfo.background\": \"#fffaf3\",\n    \"editorMarkerNavigationWarning.background\": \"#fffaf3\",\n    \"editorOverviewRuler.addedForeground\": \"#56949f80\",\n    \"editorOverviewRuler.background\": \"#faf4ed\",\n    \"editorOverviewRuler.border\": \"#6e6a8626\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#797593\",\n    \"editorOverviewRuler.commonContentForeground\": \"#6e6a860d\",\n    \"editorOverviewRuler.currentContentForeground\": \"#6e6a8614\",\n    \"editorOverviewRuler.deletedForeground\": \"#b4637a80\",\n    \"editorOverviewRuler.errorForeground\": \"#b4637a80\",\n    \"editorOverviewRuler.findMatchForeground\": \"#6e6a8626\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#907aa980\",\n    \"editorOverviewRuler.infoForeground\": \"#56949f80\",\n    \"editorOverviewRuler.modifiedForeground\": \"#d7827e80\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#6e6a8626\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#6e6a8626\",\n    \"editorOverviewRuler.warningForeground\": \"#ea9d3480\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#6e6a8614\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#6e6a8626\",\n    \"editorPane.background\": \"#0000\",\n    \"editorRuler.foreground\": \"#6e6a8626\",\n    \"editorSuggestWidget.background\": \"#fffaf3\",\n    \"editorSuggestWidget.border\": \"#0000\",\n    \"editorSuggestWidget.focusHighlightForeground\": \"#d7827e\",\n    \"editorSuggestWidget.foreground\": \"#797593\",\n    \"editorSuggestWidget.highlightForeground\": \"#d7827e\",\n    \"editorSuggestWidget.selectedBackground\": \"#6e6a8614\",\n    \"editorSuggestWidget.selectedForeground\": \"#575279\",\n    \"editorSuggestWidget.selectedIconForeground\": \"#575279\",\n    \"editorUnnecessaryCode.border\": \"#0000\",\n    \"editorUnnecessaryCode.opacity\": \"#57527980\",\n    \"editorWarning.border\": \"#0000\",\n    \"editorWarning.foreground\": \"#ea9d34\",\n    \"editorWhitespace.foreground\": \"#9893a5\",\n    \"editorWidget.background\": \"#fffaf3\",\n    \"editorWidget.border\": \"#f2e9e1\",\n    \"editorWidget.foreground\": \"#797593\",\n    \"editorWidget.resizeBorder\": \"#9893a5\",\n    \"errorForeground\": \"#b4637a\",\n    \"extensionBadge.remoteBackground\": \"#907aa9\",\n    \"extensionBadge.remoteForeground\": \"#faf4ed\",\n    \"extensionButton.prominentBackground\": \"#d7827e\",\n    \"extensionButton.prominentForeground\": \"#faf4ed\",\n    \"extensionButton.prominentHoverBackground\": \"#d7827ee6\",\n    \"extensionIcon.preReleaseForeground\": \"#286983\",\n    \"extensionIcon.starForeground\": \"#d7827e\",\n    \"extensionIcon.verifiedForeground\": \"#907aa9\",\n    \"focusBorder\": \"#6e6a8614\",\n    \"foreground\": \"#575279\",\n    \"gitDecoration.addedResourceForeground\": \"#56949f\",\n    \"gitDecoration.conflictingResourceForeground\": \"#b4637a\",\n    \"gitDecoration.deletedResourceForeground\": \"#797593\",\n    \"gitDecoration.ignoredResourceForeground\": \"#9893a5\",\n    \"gitDecoration.modifiedResourceForeground\": \"#d7827e\",\n    \"gitDecoration.renamedResourceForeground\": \"#286983\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#b4637a\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#907aa9\",\n    \"gitDecoration.submoduleResourceForeground\": \"#ea9d34\",\n    \"gitDecoration.untrackedResourceForeground\": \"#ea9d34\",\n    \"icon.foreground\": \"#797593\",\n    \"input.background\": \"#f2e9e180\",\n    \"input.border\": \"#6e6a8614\",\n    \"input.foreground\": \"#575279\",\n    \"input.placeholderForeground\": \"#797593\",\n    \"inputOption.activeBackground\": \"#d7827e26\",\n    \"inputOption.activeForeground\": \"#d7827e\",\n    \"inputValidation.errorBackground\": \"#fffaf3\",\n    \"inputValidation.errorBorder\": \"#6e6a8626\",\n    \"inputValidation.errorForeground\": \"#b4637a\",\n    \"inputValidation.infoBackground\": \"#fffaf3\",\n    \"inputValidation.infoBorder\": \"#6e6a8626\",\n    \"inputValidation.infoForeground\": \"#56949f\",\n    \"inputValidation.warningBackground\": \"#fffaf3\",\n    \"inputValidation.warningBorder\": \"#6e6a8626\",\n    \"inputValidation.warningForeground\": \"#56949f80\",\n    \"keybindingLabel.background\": \"#f2e9e1\",\n    \"keybindingLabel.border\": \"#6e6a8626\",\n    \"keybindingLabel.bottomBorder\": \"#6e6a8626\",\n    \"keybindingLabel.foreground\": \"#907aa9\",\n    \"keybindingTable.headerBackground\": \"#f2e9e1\",\n    \"keybindingTable.rowsBackground\": \"#fffaf3\",\n    \"list.activeSelectionBackground\": \"#6e6a8614\",\n    \"list.activeSelectionForeground\": \"#575279\",\n    \"list.deemphasizedForeground\": \"#797593\",\n    \"list.dropBackground\": \"#fffaf3\",\n    \"list.errorForeground\": \"#b4637a\",\n    \"list.filterMatchBackground\": \"#fffaf3\",\n    \"list.filterMatchBorder\": \"#d7827e\",\n    \"list.focusBackground\": \"#6e6a8626\",\n    \"list.focusForeground\": \"#575279\",\n    \"list.focusOutline\": \"#6e6a8614\",\n    \"list.highlightForeground\": \"#d7827e\",\n    \"list.hoverBackground\": \"#6e6a860d\",\n    \"list.hoverForeground\": \"#575279\",\n    \"list.inactiveFocusBackground\": \"#6e6a860d\",\n    \"list.inactiveSelectionBackground\": \"#fffaf3\",\n    \"list.inactiveSelectionForeground\": \"#575279\",\n    \"list.invalidItemForeground\": \"#b4637a\",\n    \"list.warningForeground\": \"#ea9d34\",\n    \"listFilterWidget.background\": \"#fffaf3\",\n    \"listFilterWidget.noMatchesOutline\": \"#b4637a\",\n    \"listFilterWidget.outline\": \"#f2e9e1\",\n    \"menu.background\": \"#fffaf3\",\n    \"menu.border\": \"#6e6a860d\",\n    \"menu.foreground\": \"#575279\",\n    \"menu.selectionBackground\": \"#6e6a8614\",\n    \"menu.selectionBorder\": \"#f2e9e1\",\n    \"menu.selectionForeground\": \"#575279\",\n    \"menu.separatorBackground\": \"#6e6a8626\",\n    \"menubar.selectionBackground\": \"#6e6a8614\",\n    \"menubar.selectionBorder\": \"#6e6a860d\",\n    \"menubar.selectionForeground\": \"#575279\",\n    \"merge.border\": \"#f2e9e1\",\n    \"merge.commonContentBackground\": \"#6e6a8614\",\n    \"merge.commonHeaderBackground\": \"#6e6a8614\",\n    \"merge.currentContentBackground\": \"#ea9d3480\",\n    \"merge.currentHeaderBackground\": \"#ea9d3480\",\n    \"merge.incomingContentBackground\": \"#56949f80\",\n    \"merge.incomingHeaderBackground\": \"#56949f80\",\n    \"minimap.background\": \"#fffaf3\",\n    \"minimap.errorHighlight\": \"#b4637a80\",\n    \"minimap.findMatchHighlight\": \"#6e6a8614\",\n    \"minimap.selectionHighlight\": \"#6e6a8614\",\n    \"minimap.warningHighlight\": \"#ea9d3480\",\n    \"minimapGutter.addedBackground\": \"#56949f\",\n    \"minimapGutter.deletedBackground\": \"#b4637a\",\n    \"minimapGutter.modifiedBackground\": \"#d7827e\",\n    \"minimapSlider.activeBackground\": \"#6e6a8626\",\n    \"minimapSlider.background\": \"#6e6a8614\",\n    \"minimapSlider.hoverBackground\": \"#6e6a8614\",\n    \"notebook.cellBorderColor\": \"#56949f80\",\n    \"notebook.cellEditorBackground\": \"#fffaf3\",\n    \"notebook.cellHoverBackground\": \"#f2e9e180\",\n    \"notebook.focusedCellBackground\": \"#6e6a860d\",\n    \"notebook.focusedCellBorder\": \"#56949f\",\n    \"notebook.outputContainerBackgroundColor\": \"#6e6a860d\",\n    \"notificationCenter.border\": \"#6e6a8614\",\n    \"notificationCenterHeader.background\": \"#fffaf3\",\n    \"notificationCenterHeader.foreground\": \"#797593\",\n    \"notificationLink.foreground\": \"#907aa9\",\n    \"notificationToast.border\": \"#6e6a8614\",\n    \"notifications.background\": \"#fffaf3\",\n    \"notifications.border\": \"#6e6a8614\",\n    \"notifications.foreground\": \"#575279\",\n    \"notificationsErrorIcon.foreground\": \"#b4637a\",\n    \"notificationsInfoIcon.foreground\": \"#56949f\",\n    \"notificationsWarningIcon.foreground\": \"#ea9d34\",\n    \"panel.background\": \"#fffaf3\",\n    \"panel.border\": \"#0000\",\n    \"panel.dropBorder\": \"#f2e9e1\",\n    \"panelInput.border\": \"#fffaf3\",\n    \"panelSection.dropBackground\": \"#6e6a8614\",\n    \"panelSectionHeader.background\": \"#fffaf3\",\n    \"panelSectionHeader.foreground\": \"#575279\",\n    \"panelTitle.activeBorder\": \"#6e6a8626\",\n    \"panelTitle.activeForeground\": \"#575279\",\n    \"panelTitle.inactiveForeground\": \"#797593\",\n    \"peekView.border\": \"#f2e9e1\",\n    \"peekViewEditor.background\": \"#fffaf3\",\n    \"peekViewEditor.matchHighlightBackground\": \"#6e6a8626\",\n    \"peekViewResult.background\": \"#fffaf3\",\n    \"peekViewResult.fileForeground\": \"#797593\",\n    \"peekViewResult.lineForeground\": \"#797593\",\n    \"peekViewResult.matchHighlightBackground\": \"#6e6a8626\",\n    \"peekViewResult.selectionBackground\": \"#6e6a8614\",\n    \"peekViewResult.selectionForeground\": \"#575279\",\n    \"peekViewTitle.background\": \"#f2e9e1\",\n    \"peekViewTitleDescription.foreground\": \"#797593\",\n    \"pickerGroup.border\": \"#6e6a8626\",\n    \"pickerGroup.foreground\": \"#907aa9\",\n    \"ports.iconRunningProcessForeground\": \"#d7827e\",\n    \"problemsErrorIcon.foreground\": \"#b4637a\",\n    \"problemsInfoIcon.foreground\": \"#56949f\",\n    \"problemsWarningIcon.foreground\": \"#ea9d34\",\n    \"progressBar.background\": \"#d7827e\",\n    \"quickInput.background\": \"#fffaf3\",\n    \"quickInput.foreground\": \"#797593\",\n    \"quickInputList.focusBackground\": \"#6e6a8614\",\n    \"quickInputList.focusForeground\": \"#575279\",\n    \"quickInputList.focusIconForeground\": \"#575279\",\n    \"scrollbar.shadow\": \"#fffaf34d\",\n    \"scrollbarSlider.activeBackground\": \"#28698380\",\n    \"scrollbarSlider.background\": \"#6e6a8614\",\n    \"scrollbarSlider.hoverBackground\": \"#6e6a8626\",\n    \"searchEditor.findMatchBackground\": \"#6e6a8614\",\n    \"selection.background\": \"#6e6a8626\",\n    \"settings.focusedRowBackground\": \"#fffaf3\",\n    \"settings.focusedRowBorder\": \"#6e6a8614\",\n    \"settings.headerForeground\": \"#575279\",\n    \"settings.modifiedItemIndicator\": \"#d7827e\",\n    \"settings.rowHoverBackground\": \"#fffaf3\",\n    \"sideBar.background\": \"#faf4ed\",\n    \"sideBar.dropBackground\": \"#fffaf3\",\n    \"sideBar.foreground\": \"#797593\",\n    \"sideBarSectionHeader.background\": \"#0000\",\n    \"sideBarSectionHeader.border\": \"#6e6a8614\",\n    \"statusBar.background\": \"#faf4ed\",\n    \"statusBar.debuggingBackground\": \"#907aa9\",\n    \"statusBar.debuggingForeground\": \"#faf4ed\",\n    \"statusBar.foreground\": \"#797593\",\n    \"statusBar.noFolderBackground\": \"#faf4ed\",\n    \"statusBar.noFolderForeground\": \"#797593\",\n    \"statusBarItem.activeBackground\": \"#6e6a8626\",\n    \"statusBarItem.errorBackground\": \"#faf4ed\",\n    \"statusBarItem.errorForeground\": \"#b4637a\",\n    \"statusBarItem.hoverBackground\": \"#6e6a8614\",\n    \"statusBarItem.prominentBackground\": \"#f2e9e1\",\n    \"statusBarItem.prominentForeground\": \"#575279\",\n    \"statusBarItem.prominentHoverBackground\": \"#6e6a8614\",\n    \"statusBarItem.remoteBackground\": \"#faf4ed\",\n    \"statusBarItem.remoteForeground\": \"#ea9d34\",\n    \"symbolIcon.arrayForeground\": \"#797593\",\n    \"symbolIcon.classForeground\": \"#797593\",\n    \"symbolIcon.colorForeground\": \"#797593\",\n    \"symbolIcon.constantForeground\": \"#797593\",\n    \"symbolIcon.constructorForeground\": \"#797593\",\n    \"symbolIcon.enumeratorForeground\": \"#797593\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#797593\",\n    \"symbolIcon.eventForeground\": \"#797593\",\n    \"symbolIcon.fieldForeground\": \"#797593\",\n    \"symbolIcon.fileForeground\": \"#797593\",\n    \"symbolIcon.folderForeground\": \"#797593\",\n    \"symbolIcon.functionForeground\": \"#797593\",\n    \"symbolIcon.interfaceForeground\": \"#797593\",\n    \"symbolIcon.keyForeground\": \"#797593\",\n    \"symbolIcon.keywordForeground\": \"#797593\",\n    \"symbolIcon.methodForeground\": \"#797593\",\n    \"symbolIcon.moduleForeground\": \"#797593\",\n    \"symbolIcon.namespaceForeground\": \"#797593\",\n    \"symbolIcon.nullForeground\": \"#797593\",\n    \"symbolIcon.numberForeground\": \"#797593\",\n    \"symbolIcon.objectForeground\": \"#797593\",\n    \"symbolIcon.operatorForeground\": \"#797593\",\n    \"symbolIcon.packageForeground\": \"#797593\",\n    \"symbolIcon.propertyForeground\": \"#797593\",\n    \"symbolIcon.referenceForeground\": \"#797593\",\n    \"symbolIcon.snippetForeground\": \"#797593\",\n    \"symbolIcon.stringForeground\": \"#797593\",\n    \"symbolIcon.structForeground\": \"#797593\",\n    \"symbolIcon.textForeground\": \"#797593\",\n    \"symbolIcon.typeParameterForeground\": \"#797593\",\n    \"symbolIcon.unitForeground\": \"#797593\",\n    \"symbolIcon.variableForeground\": \"#797593\",\n    \"tab.activeBackground\": \"#6e6a860d\",\n    \"tab.activeForeground\": \"#575279\",\n    \"tab.activeModifiedBorder\": \"#56949f\",\n    \"tab.border\": \"#0000\",\n    \"tab.hoverBackground\": \"#6e6a8614\",\n    \"tab.inactiveBackground\": \"#0000\",\n    \"tab.inactiveForeground\": \"#797593\",\n    \"tab.inactiveModifiedBorder\": \"#56949f80\",\n    \"tab.lastPinnedBorder\": \"#9893a5\",\n    \"tab.unfocusedActiveBackground\": \"#0000\",\n    \"tab.unfocusedHoverBackground\": \"#0000\",\n    \"tab.unfocusedInactiveBackground\": \"#0000\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#56949f80\",\n    \"terminal.ansiBlack\": \"#f2e9e1\",\n    \"terminal.ansiBlue\": \"#56949f\",\n    \"terminal.ansiBrightBlack\": \"#797593\",\n    \"terminal.ansiBrightBlue\": \"#56949f\",\n    \"terminal.ansiBrightCyan\": \"#d7827e\",\n    \"terminal.ansiBrightGreen\": \"#286983\",\n    \"terminal.ansiBrightMagenta\": \"#907aa9\",\n    \"terminal.ansiBrightRed\": \"#b4637a\",\n    \"terminal.ansiBrightWhite\": \"#575279\",\n    \"terminal.ansiBrightYellow\": \"#ea9d34\",\n    \"terminal.ansiCyan\": \"#d7827e\",\n    \"terminal.ansiGreen\": \"#286983\",\n    \"terminal.ansiMagenta\": \"#907aa9\",\n    \"terminal.ansiRed\": \"#b4637a\",\n    \"terminal.ansiWhite\": \"#575279\",\n    \"terminal.ansiYellow\": \"#ea9d34\",\n    \"terminal.dropBackground\": \"#6e6a8614\",\n    \"terminal.foreground\": \"#575279\",\n    \"terminal.selectionBackground\": \"#6e6a8614\",\n    \"terminal.tab.activeBorder\": \"#575279\",\n    \"terminalCursor.background\": \"#575279\",\n    \"terminalCursor.foreground\": \"#9893a5\",\n    \"textBlockQuote.background\": \"#fffaf3\",\n    \"textBlockQuote.border\": \"#6e6a8614\",\n    \"textCodeBlock.background\": \"#fffaf3\",\n    \"textLink.activeForeground\": \"#907aa9e6\",\n    \"textLink.foreground\": \"#907aa9\",\n    \"textPreformat.foreground\": \"#ea9d34\",\n    \"textSeparator.foreground\": \"#797593\",\n    \"titleBar.activeBackground\": \"#faf4ed\",\n    \"titleBar.activeForeground\": \"#797593\",\n    \"titleBar.inactiveBackground\": \"#fffaf3\",\n    \"titleBar.inactiveForeground\": \"#797593\",\n    \"toolbar.activeBackground\": \"#6e6a8626\",\n    \"toolbar.hoverBackground\": \"#6e6a8614\",\n    \"tree.indentGuidesStroke\": \"#797593\",\n    \"walkThrough.embeddedEditorBackground\": \"#faf4ed\",\n    \"welcomePage.background\": \"#faf4ed\",\n    \"welcomePage.buttonBackground\": \"#fffaf3\",\n    \"welcomePage.buttonHoverBackground\": \"#f2e9e1\",\n    \"widget.shadow\": \"#fffaf34d\",\n    \"window.activeBorder\": \"#fffaf3\",\n    \"window.inactiveBorder\": \"#fffaf3\"\n  },\n  \"displayName\": \"Ros\\xE9 Pine Dawn\",\n  \"name\": \"rose-pine-dawn\",\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#9893a5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#286983\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d7827e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d7827e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.section\",\n        \"entity.name.tag\",\n        \"entity.name.namespace\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56949f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#907aa9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b4637a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#797593\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"variable.language.this\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#286983\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56949f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#b4637a\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.range\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#907aa9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag\",\n        \"meta.brace\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#575279\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import\",\n        \"meta.export\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#286983\"\n      }\n    },\n    {\n      \"scope\": \"meta.directive.vue\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#907aa9\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#56949f\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-value.css\",\n      \"settings\": {\n        \"foreground\": \"#ea9d34\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag.other.html\",\n      \"settings\": {\n        \"foreground\": \"#797593\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#797593\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#286983\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea9d34\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9893a5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#286983\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea9d34\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#56949f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea9d34\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#b4637a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#d7827e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other\",\n        \"variable.language\",\n        \"variable.function\",\n        \"variable.argument\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#575279\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#907aa9\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { rosePineDawn as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe,OAAO,MAAM,CAAC;IAC/B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,0BAA0B;QAC1B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,mCAAmC;QACnC,+BAA+B;QAC/B,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,2BAA2B;QAC3B,uBAAuB;QACvB,yBAAyB;QACzB,qBAAqB;QACrB,2BAA2B;QAC3B,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,yCAAyC;QACzC,wCAAwC;QACxC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,mCAAmC;QACnC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,iCAAiC;QACjC,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,mCAAmC;QACnC,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,iDAAiD;QACjD,6CAA6C;QAC7C,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,oCAAoC;QACpC,gCAAgC;QAChC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,iCAAiC;QACjC,6BAA6B;QAC7B,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,4CAA4C;QAC5C,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,+BAA+B;QAC/B,kCAAkC;QAClC,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,qBAAqB;QACrB,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,yCAAyC;QACzC,yCAAyC;QACzC,sCAAsC;QACtC,gCAAgC;QAChC,qBAAqB;QACrB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,kCAAkC;QAClC,8BAA8B;QAC9B,8CAA8C;QAC9C,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,yBAAyB;QACzB,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,gDAAgD;QAChD,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,0CAA0C;QAC1C,8CAA8C;QAC9C,gCAAgC;QAChC,iCAAiC;QACjC,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,6BAA6B;QAC7B,mBAAmB;QACnB,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,sCAAsC;QACtC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,2CAA2C;QAC3C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,+BAA+B;QAC/B,uBAAuB;QACvB,wBAAwB;QACxB,8BAA8B;QAC9B,0BAA0B;QAC1B,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,0BAA0B;QAC1B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,gBAAgB;QAChB,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,4BAA4B;QAC5B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,8BAA8B;QAC9B,2CAA2C;QAC3C,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,qBAAqB;QACrB,+BAA+B;QAC/B,iCAAiC;QACjC,iCAAiC;QACjC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,kCAAkC;QAClC,kCAAkC;QAClC,sCAAsC;QACtC,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,kCAAkC;QAClC,+BAA+B;QAC/B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,wBAAwB;QACxB,iCAAiC;QACjC,gCAAgC;QAChC,mCAAmC;QACnC,uCAAuC;QACvC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,2BAA2B;QAC3B,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,wCAAwC;QACxC,0BAA0B;QAC1B,gCAAgC;QAChC,qCAAqC;QACrC,iBAAiB;QACjB,uBAAuB;QACvB,yBAAyB;IAC3B;IACA,eAAe;IACf,QAAQ;IACR,eAAe;QACb;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}