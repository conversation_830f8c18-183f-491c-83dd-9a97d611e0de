{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/ayu-dark.mjs"], "sourcesContent": ["var ayuDark = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#e6b450b3\",\n    \"activityBar.background\": \"#0b0e14\",\n    \"activityBar.border\": \"#0b0e14\",\n    \"activityBar.foreground\": \"#565b66cc\",\n    \"activityBar.inactiveForeground\": \"#565b6699\",\n    \"activityBarBadge.background\": \"#e6b450\",\n    \"activityBarBadge.foreground\": \"#0b0e14\",\n    \"badge.background\": \"#e6b45033\",\n    \"badge.foreground\": \"#e6b450\",\n    \"button.background\": \"#e6b450\",\n    \"button.foreground\": \"#0b0e14\",\n    \"button.hoverBackground\": \"#e1af4b\",\n    \"button.secondaryBackground\": \"#565b6633\",\n    \"button.secondaryForeground\": \"#bfbdb6\",\n    \"button.secondaryHoverBackground\": \"#565b6680\",\n    \"debugConsoleInputIcon.foreground\": \"#e6b450\",\n    \"debugExceptionWidget.background\": \"#0f131a\",\n    \"debugExceptionWidget.border\": \"#11151c\",\n    \"debugIcon.breakpointDisabledForeground\": \"#f2966880\",\n    \"debugIcon.breakpointForeground\": \"#f29668\",\n    \"debugToolBar.background\": \"#0f131a\",\n    \"descriptionForeground\": \"#565b66\",\n    \"diffEditor.diagonalFill\": \"#11151c\",\n    \"diffEditor.insertedTextBackground\": \"#7fd9621f\",\n    \"diffEditor.removedTextBackground\": \"#f26d781f\",\n    \"dropdown.background\": \"#0d1017\",\n    \"dropdown.border\": \"#565b6645\",\n    \"dropdown.foreground\": \"#565b66\",\n    \"editor.background\": \"#0b0e14\",\n    \"editor.findMatchBackground\": \"#6c5980\",\n    \"editor.findMatchBorder\": \"#6c5980\",\n    \"editor.findMatchHighlightBackground\": \"#6c598066\",\n    \"editor.findMatchHighlightBorder\": \"#5f4c7266\",\n    \"editor.findRangeHighlightBackground\": \"#6c598040\",\n    \"editor.foreground\": \"#bfbdb6\",\n    \"editor.inactiveSelectionBackground\": \"#409fff21\",\n    \"editor.lineHighlightBackground\": \"#131721\",\n    \"editor.rangeHighlightBackground\": \"#6c598033\",\n    \"editor.selectionBackground\": \"#409fff4d\",\n    \"editor.selectionHighlightBackground\": \"#7fd96226\",\n    \"editor.selectionHighlightBorder\": \"#7fd96200\",\n    \"editor.snippetTabstopHighlightBackground\": \"#7fd96233\",\n    \"editor.wordHighlightBackground\": \"#73b8ff14\",\n    \"editor.wordHighlightBorder\": \"#73b8ff80\",\n    \"editor.wordHighlightStrongBackground\": \"#7fd96214\",\n    \"editor.wordHighlightStrongBorder\": \"#7fd96280\",\n    \"editorBracketMatch.background\": \"#6c73804d\",\n    \"editorBracketMatch.border\": \"#6c73804d\",\n    \"editorCodeLens.foreground\": \"#acb6bf8c\",\n    \"editorCursor.foreground\": \"#e6b450\",\n    \"editorError.foreground\": \"#d95757\",\n    \"editorGroup.background\": \"#0f131a\",\n    \"editorGroup.border\": \"#11151c\",\n    \"editorGroupHeader.noTabsBackground\": \"#0b0e14\",\n    \"editorGroupHeader.tabsBackground\": \"#0b0e14\",\n    \"editorGroupHeader.tabsBorder\": \"#0b0e14\",\n    \"editorGutter.addedBackground\": \"#7fd962cc\",\n    \"editorGutter.deletedBackground\": \"#f26d78cc\",\n    \"editorGutter.modifiedBackground\": \"#73b8ffcc\",\n    \"editorHoverWidget.background\": \"#0f131a\",\n    \"editorHoverWidget.border\": \"#11151c\",\n    \"editorIndentGuide.activeBackground\": \"#6c738080\",\n    \"editorIndentGuide.background\": \"#6c738033\",\n    \"editorLineNumber.activeForeground\": \"#6c7380e6\",\n    \"editorLineNumber.foreground\": \"#6c738099\",\n    \"editorLink.activeForeground\": \"#e6b450\",\n    \"editorMarkerNavigation.background\": \"#0f131a\",\n    \"editorOverviewRuler.addedForeground\": \"#7fd962\",\n    \"editorOverviewRuler.border\": \"#11151c\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#6c7380b3\",\n    \"editorOverviewRuler.deletedForeground\": \"#f26d78\",\n    \"editorOverviewRuler.errorForeground\": \"#d95757\",\n    \"editorOverviewRuler.findMatchForeground\": \"#6c5980\",\n    \"editorOverviewRuler.modifiedForeground\": \"#73b8ff\",\n    \"editorOverviewRuler.warningForeground\": \"#e6b450\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#73b8ff66\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#7fd96266\",\n    \"editorRuler.foreground\": \"#6c738033\",\n    \"editorSuggestWidget.background\": \"#0f131a\",\n    \"editorSuggestWidget.border\": \"#11151c\",\n    \"editorSuggestWidget.highlightForeground\": \"#e6b450\",\n    \"editorSuggestWidget.selectedBackground\": \"#47526640\",\n    \"editorWarning.foreground\": \"#e6b450\",\n    \"editorWhitespace.foreground\": \"#6c738099\",\n    \"editorWidget.background\": \"#0f131a\",\n    \"editorWidget.border\": \"#11151c\",\n    \"errorForeground\": \"#d95757\",\n    \"extensionButton.prominentBackground\": \"#e6b450\",\n    \"extensionButton.prominentForeground\": \"#0d1017\",\n    \"extensionButton.prominentHoverBackground\": \"#e1af4b\",\n    \"focusBorder\": \"#e6b450b3\",\n    \"foreground\": \"#565b66\",\n    \"gitDecoration.conflictingResourceForeground\": \"\",\n    \"gitDecoration.deletedResourceForeground\": \"#f26d78b3\",\n    \"gitDecoration.ignoredResourceForeground\": \"#565b6680\",\n    \"gitDecoration.modifiedResourceForeground\": \"#73b8ffb3\",\n    \"gitDecoration.submoduleResourceForeground\": \"#d2a6ffb3\",\n    \"gitDecoration.untrackedResourceForeground\": \"#7fd962b3\",\n    \"icon.foreground\": \"#565b66\",\n    \"input.background\": \"#0d1017\",\n    \"input.border\": \"#565b6645\",\n    \"input.foreground\": \"#bfbdb6\",\n    \"input.placeholderForeground\": \"#565b6680\",\n    \"inputOption.activeBackground\": \"#e6b45033\",\n    \"inputOption.activeBorder\": \"#e6b4504d\",\n    \"inputOption.activeForeground\": \"#e6b450\",\n    \"inputValidation.errorBackground\": \"#0d1017\",\n    \"inputValidation.errorBorder\": \"#d95757\",\n    \"inputValidation.infoBackground\": \"#0b0e14\",\n    \"inputValidation.infoBorder\": \"#39bae6\",\n    \"inputValidation.warningBackground\": \"#0b0e14\",\n    \"inputValidation.warningBorder\": \"#ffb454\",\n    \"keybindingLabel.background\": \"#565b661a\",\n    \"keybindingLabel.border\": \"#bfbdb61a\",\n    \"keybindingLabel.bottomBorder\": \"#bfbdb61a\",\n    \"keybindingLabel.foreground\": \"#bfbdb6\",\n    \"list.activeSelectionBackground\": \"#47526640\",\n    \"list.activeSelectionForeground\": \"#bfbdb6\",\n    \"list.deemphasizedForeground\": \"#d95757\",\n    \"list.errorForeground\": \"#d95757\",\n    \"list.filterMatchBackground\": \"#5f4c7266\",\n    \"list.filterMatchBorder\": \"#6c598066\",\n    \"list.focusBackground\": \"#47526640\",\n    \"list.focusForeground\": \"#bfbdb6\",\n    \"list.focusOutline\": \"#47526640\",\n    \"list.highlightForeground\": \"#e6b450\",\n    \"list.hoverBackground\": \"#47526640\",\n    \"list.inactiveSelectionBackground\": \"#47526633\",\n    \"list.inactiveSelectionForeground\": \"#565b66\",\n    \"list.invalidItemForeground\": \"#565b664d\",\n    \"listFilterWidget.background\": \"#0f131a\",\n    \"listFilterWidget.noMatchesOutline\": \"#d95757\",\n    \"listFilterWidget.outline\": \"#e6b450\",\n    \"minimap.background\": \"#0b0e14\",\n    \"minimap.errorHighlight\": \"#d95757\",\n    \"minimap.findMatchHighlight\": \"#6c5980\",\n    \"minimap.selectionHighlight\": \"#409fff4d\",\n    \"minimapGutter.addedBackground\": \"#7fd962\",\n    \"minimapGutter.deletedBackground\": \"#f26d78\",\n    \"minimapGutter.modifiedBackground\": \"#73b8ff\",\n    \"panel.background\": \"#0b0e14\",\n    \"panel.border\": \"#11151c\",\n    \"panelTitle.activeBorder\": \"#e6b450\",\n    \"panelTitle.activeForeground\": \"#bfbdb6\",\n    \"panelTitle.inactiveForeground\": \"#565b66\",\n    \"peekView.border\": \"#47526640\",\n    \"peekViewEditor.background\": \"#0f131a\",\n    \"peekViewEditor.matchHighlightBackground\": \"#6c598066\",\n    \"peekViewEditor.matchHighlightBorder\": \"#5f4c7266\",\n    \"peekViewResult.background\": \"#0f131a\",\n    \"peekViewResult.fileForeground\": \"#bfbdb6\",\n    \"peekViewResult.lineForeground\": \"#565b66\",\n    \"peekViewResult.matchHighlightBackground\": \"#6c598066\",\n    \"peekViewResult.selectionBackground\": \"#47526640\",\n    \"peekViewTitle.background\": \"#47526640\",\n    \"peekViewTitleDescription.foreground\": \"#565b66\",\n    \"peekViewTitleLabel.foreground\": \"#bfbdb6\",\n    \"pickerGroup.border\": \"#11151c\",\n    \"pickerGroup.foreground\": \"#565b6680\",\n    \"progressBar.background\": \"#e6b450\",\n    \"scrollbar.shadow\": \"#11151c00\",\n    \"scrollbarSlider.activeBackground\": \"#565b66b3\",\n    \"scrollbarSlider.background\": \"#565b6666\",\n    \"scrollbarSlider.hoverBackground\": \"#565b6699\",\n    \"selection.background\": \"#409fff4d\",\n    \"settings.headerForeground\": \"#bfbdb6\",\n    \"settings.modifiedItemIndicator\": \"#73b8ff\",\n    \"sideBar.background\": \"#0b0e14\",\n    \"sideBar.border\": \"#0b0e14\",\n    \"sideBarSectionHeader.background\": \"#0b0e14\",\n    \"sideBarSectionHeader.border\": \"#0b0e14\",\n    \"sideBarSectionHeader.foreground\": \"#565b66\",\n    \"sideBarTitle.foreground\": \"#565b66\",\n    \"statusBar.background\": \"#0b0e14\",\n    \"statusBar.border\": \"#0b0e14\",\n    \"statusBar.debuggingBackground\": \"#f29668\",\n    \"statusBar.debuggingForeground\": \"#0d1017\",\n    \"statusBar.foreground\": \"#565b66\",\n    \"statusBar.noFolderBackground\": \"#0f131a\",\n    \"statusBarItem.activeBackground\": \"#565b6633\",\n    \"statusBarItem.hoverBackground\": \"#565b6633\",\n    \"statusBarItem.prominentBackground\": \"#11151c\",\n    \"statusBarItem.prominentHoverBackground\": \"#00000030\",\n    \"statusBarItem.remoteBackground\": \"#e6b450\",\n    \"statusBarItem.remoteForeground\": \"#0d1017\",\n    \"tab.activeBackground\": \"#0b0e14\",\n    \"tab.activeBorder\": \"#e6b450\",\n    \"tab.activeForeground\": \"#bfbdb6\",\n    \"tab.border\": \"#0b0e14\",\n    \"tab.inactiveBackground\": \"#0b0e14\",\n    \"tab.inactiveForeground\": \"#565b66\",\n    \"tab.unfocusedActiveBorder\": \"#565b66\",\n    \"tab.unfocusedActiveForeground\": \"#565b66\",\n    \"tab.unfocusedInactiveForeground\": \"#565b66\",\n    \"terminal.ansiBlack\": \"#11151c\",\n    \"terminal.ansiBlue\": \"#53bdfa\",\n    \"terminal.ansiBrightBlack\": \"#686868\",\n    \"terminal.ansiBrightBlue\": \"#59c2ff\",\n    \"terminal.ansiBrightCyan\": \"#95e6cb\",\n    \"terminal.ansiBrightGreen\": \"#aad94c\",\n    \"terminal.ansiBrightMagenta\": \"#d2a6ff\",\n    \"terminal.ansiBrightRed\": \"#f07178\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#ffb454\",\n    \"terminal.ansiCyan\": \"#90e1c6\",\n    \"terminal.ansiGreen\": \"#7fd962\",\n    \"terminal.ansiMagenta\": \"#cda1fa\",\n    \"terminal.ansiRed\": \"#ea6c73\",\n    \"terminal.ansiWhite\": \"#c7c7c7\",\n    \"terminal.ansiYellow\": \"#f9af4f\",\n    \"terminal.background\": \"#0b0e14\",\n    \"terminal.foreground\": \"#bfbdb6\",\n    \"textBlockQuote.background\": \"#0f131a\",\n    \"textLink.activeForeground\": \"#e6b450\",\n    \"textLink.foreground\": \"#e6b450\",\n    \"textPreformat.foreground\": \"#bfbdb6\",\n    \"titleBar.activeBackground\": \"#0b0e14\",\n    \"titleBar.activeForeground\": \"#bfbdb6\",\n    \"titleBar.border\": \"#0b0e14\",\n    \"titleBar.inactiveBackground\": \"#0b0e14\",\n    \"titleBar.inactiveForeground\": \"#565b66\",\n    \"tree.indentGuidesStroke\": \"#6c738080\",\n    \"walkThrough.embeddedEditorBackground\": \"#0f131a\",\n    \"welcomePage.buttonBackground\": \"#e6b45066\",\n    \"welcomePage.progress.background\": \"#131721\",\n    \"welcomePage.tileBackground\": \"#0b0e14\",\n    \"welcomePage.tileShadow\": \"#00000080\",\n    \"widget.shadow\": \"#00000080\"\n  },\n  \"displayName\": \"Ayu Dark\",\n  \"name\": \"ayu-dark\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"parameter.label\": \"#bfbdb6\"\n  },\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#0b0e14\",\n        \"foreground\": \"#bfbdb6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#acb6bf8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"constant.other.symbol\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#aad94c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"constant.character\",\n        \"constant.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#95e6cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d2a6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d2a6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"variable.parameter.function-call\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bfbdb6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.member\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f29668\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.separator\",\n        \"punctuation.terminator\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bfbdb6b3\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bfbdb6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f29668\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bfbdb6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.java storage.type\",\n        \"source.haskell storage.type\",\n        \"source.c storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59c2ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.java storage.type.primitive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb454\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\",\n        \"meta.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d2a6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.function\",\n        \"variable.annotation\",\n        \"meta.function-call.generic\",\n        \"support.function.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb454\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"support.macro\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.import\",\n        \"entity.name.package\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#aad94c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59c2ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"meta.tag.sgml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59c2ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag.end\",\n        \"punctuation.definition.tag.begin\",\n        \"punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae680\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb454\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f29668\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type\",\n        \"support.class\",\n        \"source.go storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator variable.other\",\n        \"meta.decorator punctuation.decorator\",\n        \"storage.type.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e6b673\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d95757\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c594c5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ruby variable.other.readwrite\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb454\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css entity.name.tag\",\n        \"source.sass entity.name.tag\",\n        \"source.scss entity.name.tag\",\n        \"source.less entity.name.tag\",\n        \"source.stylus entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#59c2ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.type\",\n        \"source.sass support.type\",\n        \"source.scss support.type\",\n        \"source.less support.type\",\n        \"source.stylus support.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acb6bf8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.property-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric.line-number.find-in-files - match\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acb6bf8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric.line-number.match\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff8f40\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.filename.find-in-files\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#aad94c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"message.error\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d95757\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#aad94c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic markup.bold\",\n        \"markup.bold markup.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.raw\"\n      ],\n      \"settings\": {\n        \"background\": \"#bfbdb605\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.raw.inline\"\n      ],\n      \"settings\": {\n        \"background\": \"#bfbdb60f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.separator\"\n      ],\n      \"settings\": {\n        \"background\": \"#bfbdb60f\",\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#acb6bf8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#95e6cb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.list punctuation.definition.list.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffb454\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fd962\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73b8ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f26d78\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strike\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e6b673\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.table\"\n      ],\n      \"settings\": {\n        \"background\": \"#bfbdb60f\",\n        \"foreground\": \"#39bae6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown markup.inline.raw\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f29668\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.html.markdown meta.dummy.line-break\"\n      ],\n      \"settings\": {\n        \"background\": \"#acb6bf8c\",\n        \"foreground\": \"#acb6bf8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.markdown\"\n      ],\n      \"settings\": {\n        \"background\": \"#bfbdb6\",\n        \"foreground\": \"#acb6bf8c\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { ayuDark as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,mCAAmC;QACnC,+BAA+B;QAC/B,0CAA0C;QAC1C,kCAAkC;QAClC,2BAA2B;QAC3B,yBAAyB;QACzB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,qBAAqB;QACrB,sCAAsC;QACtC,kCAAkC;QAClC,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,uCAAuC;QACvC,8BAA8B;QAC9B,8CAA8C;QAC9C,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,0CAA0C;QAC1C,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,mBAAmB;QACnB,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,cAAc;QACd,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,mCAAmC;QACnC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,iCAAiC;QACjC,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,+BAA+B;QAC/B,wBAAwB;QACxB,8BAA8B;QAC9B,0BAA0B;QAC1B,wBAAwB;QACxB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,oBAAoB;QACpB,gBAAgB;QAChB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,iCAAiC;QACjC,mCAAmC;QACnC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,wCAAwC;QACxC,gCAAgC;QAChC,mCAAmC;QACnC,8BAA8B;QAC9B,0BAA0B;QAC1B,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,mBAAmB;IACrB;IACA,eAAe;QACb;YACE,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;gBACd,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}