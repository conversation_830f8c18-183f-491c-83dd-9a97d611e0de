{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getWindow.js"], "sourcesContent": ["export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,UAAU,IAAI;IACpC,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IAEA,IAAI,KAAK,QAAQ,OAAO,mBAAmB;QACzC,IAAI,gBAAgB,KAAK,aAAa;QACtC,OAAO,gBAAgB,cAAc,WAAW,IAAI,SAAS;IAC/D;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/instanceOf.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };"], "names": [], "mappings": ";;;;;AAAA;;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,aAAa,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,MAAM,OAAO;IACxC,OAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,aAAa,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,MAAM,WAAW;IAC5C,OAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,IAAI;IACxB,0BAA0B;IAC1B,IAAI,OAAO,eAAe,aAAa;QACrC,OAAO;IACT;IAEA,IAAI,aAAa,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,MAAM,UAAU;IAC3C,OAAO,gBAAgB,cAAc,gBAAgB;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/math.js"], "sourcesContent": ["export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;"], "names": [], "mappings": ";;;;;AAAO,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,QAAQ,KAAK,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/userAgent.js"], "sourcesContent": ["export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}"], "names": [], "mappings": ";;;AAAe,SAAS;IACtB,IAAI,SAAS,UAAU,aAAa;IAEpC,IAAI,UAAU,QAAQ,OAAO,MAAM,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;QACnE,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,SAAU,IAAI;YACrC,OAAO,KAAK,KAAK,GAAG,MAAM,KAAK,OAAO;QACxC,GAAG,IAAI,CAAC;IACV;IAEA,OAAO,UAAU,SAAS;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/isLayoutViewport.js"], "sourcesContent": ["import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS;IACtB,OAAO,CAAC,iCAAiC,IAAI,CAAC,CAAA,GAAA,kOAAA,CAAA,UAAW,AAAD;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getBoundingClientRect.js"], "sourcesContent": ["import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,sBAAsB,OAAO,EAAE,YAAY,EAAE,eAAe;IAClF,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IAEA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IAEA,IAAI,aAAa,QAAQ,qBAAqB;IAC9C,IAAI,SAAS;IACb,IAAI,SAAS;IAEb,IAAI,gBAAgB,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;QAC1C,SAAS,QAAQ,WAAW,GAAG,IAAI,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,WAAW,KAAK,IAAI,QAAQ,WAAW,IAAI,IAAI;QACxF,SAAS,QAAQ,YAAY,GAAG,IAAI,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,IAAI,QAAQ,YAAY,IAAI,IAAI;IAC7F;IAEA,IAAI,OAAO,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,WAAW,QACjD,iBAAiB,KAAK,cAAc;IAExC,IAAI,mBAAmB,CAAC,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,OAAO;IAC9C,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,oBAAoB,iBAAiB,eAAe,UAAU,GAAG,CAAC,CAAC,IAAI;IACnG,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,oBAAoB,iBAAiB,eAAe,SAAS,GAAG,CAAC,CAAC,IAAI;IACjG,IAAI,QAAQ,WAAW,KAAK,GAAG;IAC/B,IAAI,SAAS,WAAW,MAAM,GAAG;IACjC,OAAO;QACL,OAAO;QACP,QAAQ;QACR,KAAK;QACL,OAAO,IAAI;QACX,QAAQ,IAAI;QACZ,MAAM;QACN,GAAG;QACH,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getWindowScroll.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,gBAAgB,IAAI;IAC1C,IAAI,MAAM,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE;IACpB,IAAI,aAAa,IAAI,WAAW;IAChC,IAAI,YAAY,IAAI,WAAW;IAC/B,OAAO;QACL,YAAY;QACZ,WAAW;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getHTMLElementScroll.js"], "sourcesContent": ["export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}"], "names": [], "mappings": ";;;AAAe,SAAS,qBAAqB,OAAO;IAClD,OAAO;QACL,YAAY,QAAQ,UAAU;QAC9B,WAAW,QAAQ,SAAS;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getNodeScroll.js"], "sourcesContent": ["import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,cAAc,IAAI;IACxC,IAAI,SAAS,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;QACpD,OAAO,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE;IACzB,OAAO;QACL,OAAO,CAAA,GAAA,oPAAA,CAAA,UAAoB,AAAD,EAAE;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getNodeName.js"], "sourcesContent": ["export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,YAAY,OAAO;IACzC,OAAO,UAAU,CAAC,QAAQ,QAAQ,IAAI,EAAE,EAAE,WAAW,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getDocumentElement.js"], "sourcesContent": ["import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,mBAAmB,OAAO;IAChD,mEAAmE;IACnE,OAAO,CAAC,CAAC,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,WAAW,QAAQ,aAAa,GACnD,QAAQ,QAAQ,KAAK,OAAO,QAAQ,EAAE,eAAe;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getWindowScrollBarX.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACe,SAAS,oBAAoB,OAAO;IACjD,yEAAyE;IACzE,qBAAqB;IACrB,6EAA6E;IAC7E,yEAAyE;IACzE,UAAU;IACV,0EAA0E;IAC1E,sCAAsC;IACtC,OAAO,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE,UAAU,IAAI,GAAG,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,SAAS,UAAU;AACtG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getComputedStyle.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,iBAAiB,OAAO;IAC9C,OAAO,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,SAAS,gBAAgB,CAAC;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/isScrollParent.js"], "sourcesContent": ["import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,eAAe,OAAO;IAC5C,6DAA6D;IAC7D,IAAI,oBAAoB,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,UACrC,WAAW,kBAAkB,QAAQ,EACrC,YAAY,kBAAkB,SAAS,EACvC,YAAY,kBAAkB,SAAS;IAE3C,OAAO,6BAA6B,IAAI,CAAC,WAAW,YAAY;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getCompositeRect.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,OAAO,QAAQ,qBAAqB;IACxC,IAAI,SAAS,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,IAAI,QAAQ,WAAW,IAAI;IACxD,IAAI,SAAS,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,KAAK,MAAM,IAAI,QAAQ,YAAY,IAAI;IAC1D,OAAO,WAAW,KAAK,WAAW;AACpC,EAAE,yEAAyE;AAI5D,SAAS,iBAAiB,uBAAuB,EAAE,YAAY,EAAE,OAAO;IACrF,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,IAAI,0BAA0B,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IAC5C,IAAI,uBAAuB,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,gBAAgB;IAC1E,IAAI,kBAAkB,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE;IACzC,IAAI,OAAO,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,yBAAyB,sBAAsB;IAChF,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,IAAI,UAAU;QACZ,GAAG;QACH,GAAG;IACL;IAEA,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,kBAAkB,UAAU,sDAAsD;QAClG,CAAA,GAAA,8OAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB;YAC/B,SAAS,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;QACzB;QAEA,IAAI,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;YAC/B,UAAU,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,cAAc;YAC9C,QAAQ,CAAC,IAAI,aAAa,UAAU;YACpC,QAAQ,CAAC,IAAI,aAAa,SAAS;QACrC,OAAO,IAAI,iBAAiB;YAC1B,QAAQ,CAAC,GAAG,CAAA,GAAA,mPAAA,CAAA,UAAmB,AAAD,EAAE;QAClC;IACF;IAEA,OAAO;QACL,GAAG,KAAK,IAAI,GAAG,OAAO,UAAU,GAAG,QAAQ,CAAC;QAC5C,GAAG,KAAK,GAAG,GAAG,OAAO,SAAS,GAAG,QAAQ,CAAC;QAC1C,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getLayoutRect.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}"], "names": [], "mappings": ";;;AAAA,waAAgE,6EAA6E;;AAG9H,SAAS,cAAc,OAAO;IAC3C,IAAI,aAAa,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,UAAU,yDAAyD;IAC1G,4DAA4D;IAE5D,IAAI,QAAQ,QAAQ,WAAW;IAC/B,IAAI,SAAS,QAAQ,YAAY;IAEjC,IAAI,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,UAAU,GAAG;QAC3C,QAAQ,WAAW,KAAK;IAC1B;IAEA,IAAI,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,WAAW,GAAG;QAC7C,SAAS,WAAW,MAAM;IAC5B;IAEA,OAAO;QACL,GAAG,QAAQ,UAAU;QACrB,GAAG,QAAQ,SAAS;QACpB,OAAO;QACP,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getParentNode.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACe,SAAS,cAAc,OAAO;IAC3C,IAAI,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,aAAa,QAAQ;QACnC,OAAO;IACT;IAEA,OACE,kCAAkC;IAClC,2BAA2B;IAC3B,QAAQ,YAAY,IAAI,2DAA2D;IACnF,QAAQ,UAAU,IAAI,CACtB,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EAAE,WAAW,QAAQ,IAAI,GAAG,IAAI,KAAK,sBAAsB;IACtE,uDAAuD;IACvD,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE,SAAS,WAAW;;AAG3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getScrollParent.js"], "sourcesContent": ["import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,gBAAgB,IAAI;IAC1C,IAAI;QAAC;QAAQ;QAAQ;KAAY,CAAC,OAAO,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,UAAU,GAAG;QACjE,mEAAmE;QACnE,OAAO,KAAK,aAAa,CAAC,IAAI;IAChC;IAEA,IAAI,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAA,GAAA,8OAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QAC/C,OAAO;IACT;IAEA,OAAO,gBAAgB,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/listScrollParents.js"], "sourcesContent": ["import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAQe,SAAS,kBAAkB,OAAO,EAAE,IAAI;IACrD,IAAI;IAEJ,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,EAAE;IACX;IAEA,IAAI,eAAe,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE;IACnC,IAAI,SAAS,iBAAiB,CAAC,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;IAC5H,IAAI,MAAM,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE;IACpB,IAAI,SAAS,SAAS;QAAC;KAAI,CAAC,MAAM,CAAC,IAAI,cAAc,IAAI,EAAE,EAAE,CAAA,GAAA,8OAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,eAAe,EAAE,IAAI;IACjH,IAAI,cAAc,KAAK,MAAM,CAAC;IAC9B,OAAO,SAAS,cAChB,YAAY,MAAM,CAAC,kBAAkB,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/isTableElement.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,eAAe,OAAO;IAC5C,OAAO;QAAC;QAAS;QAAM;KAAK,CAAC,OAAO,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,aAAa;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getOffsetParent.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,SAAS,oBAAoB,OAAO;IAClC,IAAI,CAAC,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,qDAAqD;IACpF,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK,SAAS;QAC9C,OAAO;IACT;IAEA,OAAO,QAAQ,YAAY;AAC7B,EAAE,6EAA6E;AAC/E,8BAA8B;AAG9B,SAAS,mBAAmB,OAAO;IACjC,IAAI,YAAY,WAAW,IAAI,CAAC,CAAA,GAAA,kOAAA,CAAA,UAAW,AAAD;IAC1C,IAAI,OAAO,WAAW,IAAI,CAAC,CAAA,GAAA,kOAAA,CAAA,UAAW,AAAD;IAErC,IAAI,QAAQ,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;QAClC,2FAA2F;QAC3F,IAAI,aAAa,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE;QAElC,IAAI,WAAW,QAAQ,KAAK,SAAS;YACnC,OAAO;QACT;IACF;IAEA,IAAI,cAAc,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;IAEhC,IAAI,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EAAE,cAAc;QAC7B,cAAc,YAAY,IAAI;IAChC;IAEA,MAAO,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,gBAAgB;QAAC;QAAQ;KAAO,CAAC,OAAO,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,gBAAgB,EAAG;QAC3F,IAAI,MAAM,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,cAAc,wEAAwE;QACjH,6BAA6B;QAC7B,qGAAqG;QAErG,IAAI,IAAI,SAAS,KAAK,UAAU,IAAI,WAAW,KAAK,UAAU,IAAI,OAAO,KAAK,WAAW;YAAC;YAAa;SAAc,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM,CAAC,KAAK,aAAa,IAAI,UAAU,KAAK,YAAY,aAAa,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,QAAQ;YACpP,OAAO;QACT,OAAO;YACL,cAAc,YAAY,UAAU;QACtC;IACF;IAEA,OAAO;AACT,EAAE,yEAAyE;AAI5D,SAAS,gBAAgB,OAAO;IAC7C,IAAI,SAAS,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE;IACvB,IAAI,eAAe,oBAAoB;IAEvC,MAAO,gBAAgB,CAAA,GAAA,8OAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,cAAc,QAAQ,KAAK,SAAU;QAC3G,eAAe,oBAAoB;IACrC;IAEA,IAAI,gBAAgB,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,cAAc,QAAQ,KAAK,QAAQ,GAAG;QAC1J,OAAO;IACT;IAEA,OAAO,gBAAgB,mBAAmB,YAAY;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/enums.js"], "sourcesContent": ["export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB;IAAC;IAAK;IAAQ;IAAO;CAAK;AAC/C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAsB,WAAW,GAAE,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;IAC1F,OAAO,IAAI,MAAM,CAAC;QAAC,YAAY,MAAM;QAAO,YAAY,MAAM;KAAI;AACpE,GAAG,EAAE;AACE,IAAI,aAAa,WAAW,GAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB;IAAC;CAAK,EAAE,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;IACpG,OAAO,IAAI,MAAM,CAAC;QAAC;QAAW,YAAY,MAAM;QAAO,YAAY,MAAM;KAAI;AAC/E,GAAG,EAAE,GAAG,sCAAsC;AAEvC,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY,aAAa,uBAAuB;AAEpD,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY,aAAa,kFAAkF;AAE/G,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB;IAAC;IAAY;IAAM;IAAW;IAAY;IAAM;IAAW;IAAa;IAAO;CAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/orderModifiers.js"], "sourcesContent": ["import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}"], "names": [], "mappings": ";;;AAAA,8WAA8C,uDAAuD;;AAErG,SAAS,MAAM,SAAS;IACtB,IAAI,MAAM,IAAI;IACd,IAAI,UAAU,IAAI;IAClB,IAAI,SAAS,EAAE;IACf,UAAU,OAAO,CAAC,SAAU,QAAQ;QAClC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE;IACzB,IAAI,4EAA4E;IAEhF,SAAS,KAAK,QAAQ;QACpB,QAAQ,GAAG,CAAC,SAAS,IAAI;QACzB,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,SAAS,QAAQ,IAAI,EAAE,EAAE,SAAS,gBAAgB,IAAI,EAAE;QACjF,SAAS,OAAO,CAAC,SAAU,GAAG;YAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,MAAM;gBACrB,IAAI,cAAc,IAAI,GAAG,CAAC;gBAE1B,IAAI,aAAa;oBACf,KAAK;gBACP;YACF;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,UAAU,OAAO,CAAC,SAAU,QAAQ;QAClC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAAI,GAAG;YAC/B,2BAA2B;YAC3B,KAAK;QACP;IACF;IACA,OAAO;AACT;AAEe,SAAS,eAAe,SAAS;IAC9C,8BAA8B;IAC9B,IAAI,mBAAmB,MAAM,YAAY,uBAAuB;IAEhE,OAAO,qNAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,KAAK;QAC/C,OAAO,IAAI,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAU,QAAQ;YAC1D,OAAO,SAAS,KAAK,KAAK;QAC5B;IACF,GAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/debounce.js"], "sourcesContent": ["export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}"], "names": [], "mappings": ";;;AAAe,SAAS,SAAS,EAAE;IACjC,IAAI;IACJ,OAAO;QACL,IAAI,CAAC,SAAS;YACZ,UAAU,IAAI,QAAQ,SAAU,OAAO;gBACrC,QAAQ,OAAO,GAAG,IAAI,CAAC;oBACrB,UAAU;oBACV,QAAQ;gBACV;YACF;QACF;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/mergeByName.js"], "sourcesContent": ["export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}"], "names": [], "mappings": ";;;AAAe,SAAS,YAAY,SAAS;IAC3C,IAAI,SAAS,UAAU,MAAM,CAAC,SAAU,MAAM,EAAE,OAAO;QACrD,IAAI,WAAW,MAAM,CAAC,QAAQ,IAAI,CAAC;QACnC,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,SAAS;YACrE,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,OAAO,EAAE,QAAQ,OAAO;YAC5D,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,QAAQ,IAAI;QACrD,KAAK;QACL,OAAO;IACT,GAAG,CAAC,IAAI,sCAAsC;IAE9C,OAAO,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAU,GAAG;QAC1C,OAAO,MAAM,CAAC,IAAI;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/createPopper.js"], "sourcesContent": ["import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AACA,IAAI,kBAAkB;IACpB,WAAW;IACX,WAAW,EAAE;IACb,UAAU;AACZ;AAEA,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC9B;IAEA,OAAO,CAAC,KAAK,IAAI,CAAC,SAAU,OAAO;QACjC,OAAO,CAAC,CAAC,WAAW,OAAO,QAAQ,qBAAqB,KAAK,UAAU;IACzE;AACF;AAEO,SAAS,gBAAgB,gBAAgB;IAC9C,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB,CAAC;IACtB;IAEA,IAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,gBAAgB,EAC1D,mBAAmB,0BAA0B,KAAK,IAAI,EAAE,GAAG,uBAC3D,yBAAyB,kBAAkB,cAAc,EACzD,iBAAiB,2BAA2B,KAAK,IAAI,kBAAkB;IAC3E,OAAO,SAAS,aAAa,SAAS,EAAE,MAAM,EAAE,OAAO;QACrD,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU;QACZ;QAEA,IAAI,QAAQ;YACV,WAAW;YACX,kBAAkB,EAAE;YACpB,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,iBAAiB;YAC5C,eAAe,CAAC;YAChB,UAAU;gBACR,WAAW;gBACX,QAAQ;YACV;YACA,YAAY,CAAC;YACb,QAAQ,CAAC;QACX;QACA,IAAI,mBAAmB,EAAE;QACzB,IAAI,cAAc;QAClB,IAAI,WAAW;YACb,OAAO;YACP,YAAY,SAAS,WAAW,gBAAgB;gBAC9C,IAAI,UAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;gBACzF;gBACA,MAAM,OAAO,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,MAAM,OAAO,EAAE;gBACjE,MAAM,aAAa,GAAG;oBACpB,WAAW,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAA,GAAA,iPAAA,CAAA,UAAiB,AAAD,EAAE,aAAa,UAAU,cAAc,GAAG,CAAA,GAAA,iPAAA,CAAA,UAAiB,AAAD,EAAE,UAAU,cAAc,IAAI,EAAE;oBAC5I,QAAQ,CAAA,GAAA,iPAAA,CAAA,UAAiB,AAAD,EAAE;gBAC5B,GAAG,+DAA+D;gBAClE,aAAa;gBAEb,IAAI,mBAAmB,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,CAAA,GAAA,oOAAA,CAAA,UAAW,AAAD,EAAE,EAAE,CAAC,MAAM,CAAC,kBAAkB,MAAM,OAAO,CAAC,SAAS,KAAK,+BAA+B;gBAEzI,MAAM,gBAAgB,GAAG,iBAAiB,MAAM,CAAC,SAAU,CAAC;oBAC1D,OAAO,EAAE,OAAO;gBAClB;gBACA;gBACA,OAAO,SAAS,MAAM;YACxB;YACA,wEAAwE;YACxE,yEAAyE;YACzE,SAAS;YACT,yEAAyE;YACzE,wCAAwC;YACxC,aAAa,SAAS;gBACpB,IAAI,aAAa;oBACf;gBACF;gBAEA,IAAI,kBAAkB,MAAM,QAAQ,EAChC,YAAY,gBAAgB,SAAS,EACrC,SAAS,gBAAgB,MAAM,EAAE,kEAAkE;gBACvG,UAAU;gBAEV,IAAI,CAAC,iBAAiB,WAAW,SAAS;oBACxC;gBACF,EAAE,+DAA+D;gBAGjE,MAAM,KAAK,GAAG;oBACZ,WAAW,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,WAAW,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,SAAS,MAAM,OAAO,CAAC,QAAQ,KAAK;oBAC3F,QAAQ,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;gBACxB,GAAG,oEAAoE;gBACvE,oEAAoE;gBACpE,uEAAuE;gBACvE,uEAAuE;gBACvE,kBAAkB;gBAElB,MAAM,KAAK,GAAG;gBACd,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,uEAAuE;gBAClH,wEAAwE;gBACxE,kDAAkD;gBAClD,sDAAsD;gBAEtD,MAAM,gBAAgB,CAAC,OAAO,CAAC,SAAU,QAAQ;oBAC/C,OAAO,MAAM,aAAa,CAAC,SAAS,IAAI,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,IAAI;gBAC7E;gBAEA,IAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,gBAAgB,CAAC,MAAM,EAAE,QAAS;oBAClE,IAAI,MAAM,KAAK,KAAK,MAAM;wBACxB,MAAM,KAAK,GAAG;wBACd,QAAQ,CAAC;wBACT;oBACF;oBAEA,IAAI,wBAAwB,MAAM,gBAAgB,CAAC,MAAM,EACrD,KAAK,sBAAsB,EAAE,EAC7B,yBAAyB,sBAAsB,OAAO,EACtD,WAAW,2BAA2B,KAAK,IAAI,CAAC,IAAI,wBACpD,OAAO,sBAAsB,IAAI;oBAErC,IAAI,OAAO,OAAO,YAAY;wBAC5B,QAAQ,GAAG;4BACT,OAAO;4BACP,SAAS;4BACT,MAAM;4BACN,UAAU;wBACZ,MAAM;oBACR;gBACF;YACF;YACA,yEAAyE;YACzE,yDAAyD;YACzD,QAAQ,CAAA,GAAA,iOAAA,CAAA,UAAQ,AAAD,EAAE;gBACf,OAAO,IAAI,QAAQ,SAAU,OAAO;oBAClC,SAAS,WAAW;oBACpB,QAAQ;gBACV;YACF;YACA,SAAS,SAAS;gBAChB;gBACA,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,iBAAiB,WAAW,SAAS;YACxC,OAAO;QACT;QAEA,SAAS,UAAU,CAAC,SAAS,IAAI,CAAC,SAAU,KAAK;YAC/C,IAAI,CAAC,eAAe,QAAQ,aAAa,EAAE;gBACzC,QAAQ,aAAa,CAAC;YACxB;QACF,IAAI,wEAAwE;QAC5E,2EAA2E;QAC3E,uEAAuE;QACvE,2EAA2E;QAC3E,OAAO;QAEP,SAAS;YACP,MAAM,gBAAgB,CAAC,OAAO,CAAC,SAAU,IAAI;gBAC3C,IAAI,OAAO,KAAK,IAAI,EAChB,eAAe,KAAK,OAAO,EAC3B,UAAU,iBAAiB,KAAK,IAAI,CAAC,IAAI,cACzC,SAAS,KAAK,MAAM;gBAExB,IAAI,OAAO,WAAW,YAAY;oBAChC,IAAI,YAAY,OAAO;wBACrB,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,SAAS;oBACX;oBAEA,IAAI,SAAS,SAAS,UAAU;oBAEhC,iBAAiB,IAAI,CAAC,aAAa;gBACrC;YACF;QACF;QAEA,SAAS;YACP,iBAAiB,OAAO,CAAC,SAAU,EAAE;gBACnC,OAAO;YACT;YACA,mBAAmB,EAAE;QACvB;QAEA,OAAO;IACT;AACF;AACO,IAAI,eAAe,WAAW,GAAE,mBAAmB,oDAAoD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/eventListeners.js"], "sourcesContent": ["import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};"], "names": [], "mappings": ";;;AAAA,gZAAmD,oDAAoD;;AAEvG,IAAI,UAAU;IACZ,SAAS;AACX;AAEA,SAAS,OAAO,IAAI;IAClB,IAAI,QAAQ,KAAK,KAAK,EAClB,WAAW,KAAK,QAAQ,EACxB,UAAU,KAAK,OAAO;IAC1B,IAAI,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO,iBAC7C,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO;IACjD,IAAI,SAAS,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,MAAM,QAAQ,CAAC,MAAM;IAC5C,IAAI,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,aAAa,CAAC,SAAS,EAAE,MAAM,aAAa,CAAC,MAAM;IAEvF,IAAI,QAAQ;QACV,cAAc,OAAO,CAAC,SAAU,YAAY;YAC1C,aAAa,gBAAgB,CAAC,UAAU,SAAS,MAAM,EAAE;QAC3D;IACF;IAEA,IAAI,QAAQ;QACV,OAAO,gBAAgB,CAAC,UAAU,SAAS,MAAM,EAAE;IACrD;IAEA,OAAO;QACL,IAAI,QAAQ;YACV,cAAc,OAAO,CAAC,SAAU,YAAY;gBAC1C,aAAa,mBAAmB,CAAC,UAAU,SAAS,MAAM,EAAE;YAC9D;QACF;QAEA,IAAI,QAAQ;YACV,OAAO,mBAAmB,CAAC,UAAU,SAAS,MAAM,EAAE;QACxD;IACF;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI,SAAS,MAAM;IACnB,QAAQ;IACR,MAAM,CAAC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getBasePlacement.js"], "sourcesContent": ["import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}"], "names": [], "mappings": ";;;;AACe,SAAS,iBAAiB,SAAS;IAChD,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getVariation.js"], "sourcesContent": ["export default function getVariation(placement) {\n  return placement.split('-')[1];\n}"], "names": [], "mappings": ";;;AAAe,SAAS,aAAa,SAAS;IAC5C,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getMainAxisFromPlacement.js"], "sourcesContent": ["export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}"], "names": [], "mappings": ";;;AAAe,SAAS,yBAAyB,SAAS;IACxD,OAAO;QAAC;QAAO;KAAS,CAAC,OAAO,CAAC,cAAc,IAAI,MAAM;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/computeOffsets.js"], "sourcesContent": ["import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,eAAe,IAAI;IACzC,IAAI,YAAY,KAAK,SAAS,EAC1B,UAAU,KAAK,OAAO,EACtB,YAAY,KAAK,SAAS;IAC9B,IAAI,gBAAgB,YAAY,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,aAAa;IAC9D,IAAI,YAAY,YAAY,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE,aAAa;IACtD,IAAI,UAAU,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;IAClE,IAAI,UAAU,UAAU,CAAC,GAAG,UAAU,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;IACpE,IAAI;IAEJ,OAAQ;QACN,KAAK,qNAAA,CAAA,MAAG;YACN,UAAU;gBA<PERSON>,<PERSON>G;gBACH,GAAG,UAAU,CAAC,GAAG,QAAQ,MAAM;YACjC;YACA;QAEF,KAAK,qNAAA,CAAA,SAAM;YACT,UAAU;gBACR,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,UAAU,MAAM;YACnC;YACA;QAEF,KAAK,qNAAA,CAAA,QAAK;YACR,UAAU;gBACR,GAAG,UAAU,CAAC,GAAG,UAAU,KAAK;gBAChC,GAAG;YACL;YACA;QAEF,KAAK,qNAAA,CAAA,OAAI;YACP,UAAU;gBACR,GAAG,UAAU,CAAC,GAAG,QAAQ,KAAK;gBAC9B,GAAG;YACL;YACA;QAEF;YACE,UAAU;gBACR,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;YAChB;IACJ;IAEA,IAAI,WAAW,gBAAgB,CAAA,GAAA,iPAAA,CAAA,UAAwB,AAAD,EAAE,iBAAiB;IAEzE,IAAI,YAAY,MAAM;QACpB,IAAI,MAAM,aAAa,MAAM,WAAW;QAExC,OAAQ;YACN,KAAK,qNAAA,CAAA,QAAK;gBACR,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC;gBAC9E;YAEF,KAAK,qNAAA,CAAA,MAAG;gBACN,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC;gBAC9E;YAEF;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/popperOffsets.js"], "sourcesContent": ["import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;IACpB,iEAAiE;IACjE,iDAAiD;IACjD,4DAA4D;IAC5D,iCAAiC;IACjC,MAAM,aAAa,CAAC,KAAK,GAAG,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE;QACzC,WAAW,MAAM,KAAK,CAAC,SAAS;QAChC,SAAS,MAAM,KAAK,CAAC,MAAM;QAC3B,UAAU;QACV,WAAW,MAAM,SAAS;IAC5B;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,MAAM,CAAC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/computeStyles.js"], "sourcesContent": ["import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2XAA0C,oDAAoD;;;;;;;;;AAE9F,IAAI,aAAa;IACf,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR,GAAG,uEAAuE;AAC1E,uEAAuE;AACvE,4DAA4D;AAE5D,SAAS,kBAAkB,IAAI,EAAE,GAAG;IAClC,IAAI,IAAI,KAAK,CAAC,EACV,IAAI,KAAK,CAAC;IACd,IAAI,MAAM,IAAI,gBAAgB,IAAI;IAClC,OAAO;QACL,GAAG,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,IAAI,OAAO,OAAO;QAC3B,GAAG,CAAA,GAAA,6NAAA,CAAA,QAAK,AAAD,EAAE,IAAI,OAAO,OAAO;IAC7B;AACF;AAEO,SAAS,YAAY,KAAK;IAC/B,IAAI;IAEJ,IAAI,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,eAAe,EACvC,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,YAAY,EACjC,UAAU,MAAM,OAAO;IAC3B,IAAI,aAAa,QAAQ,CAAC,EACtB,IAAI,eAAe,KAAK,IAAI,IAAI,YAChC,aAAa,QAAQ,CAAC,EACtB,IAAI,eAAe,KAAK,IAAI,IAAI;IAEpC,IAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;QAC5D,GAAG;QACH,GAAG;IACL,KAAK;QACH,GAAG;QACH,GAAG;IACL;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,CAAC;IACX,IAAI,OAAO,QAAQ,cAAc,CAAC;IAClC,IAAI,OAAO,QAAQ,cAAc,CAAC;IAClC,IAAI,QAAQ,qNAAA,CAAA,OAAI;IAChB,IAAI,QAAQ,qNAAA,CAAA,MAAG;IACf,IAAI,MAAM;IAEV,IAAI,UAAU;QACZ,IAAI,eAAe,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE;QACnC,IAAI,aAAa;QACjB,IAAI,YAAY;QAEhB,IAAI,iBAAiB,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,SAAS;YACtC,eAAe,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE;YAElC,IAAI,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,cAAc,QAAQ,KAAK,YAAY,aAAa,YAAY;gBACnF,aAAa;gBACb,YAAY;YACd;QACF,EAAE,8HAA8H;QAGhI,eAAe;QAEf,IAAI,cAAc,qNAAA,CAAA,MAAG,IAAI,CAAC,cAAc,qNAAA,CAAA,OAAI,IAAI,cAAc,qNAAA,CAAA,QAAK,KAAK,cAAc,qNAAA,CAAA,MAAG,EAAE;YACzF,QAAQ,qNAAA,CAAA,SAAM;YACd,IAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM,GAC/F,YAAY,CAAC,WAAW;YACxB,KAAK,UAAU,WAAW,MAAM;YAChC,KAAK,kBAAkB,IAAI,CAAC;QAC9B;QAEA,IAAI,cAAc,qNAAA,CAAA,OAAI,IAAI,CAAC,cAAc,qNAAA,CAAA,MAAG,IAAI,cAAc,qNAAA,CAAA,SAAM,KAAK,cAAc,qNAAA,CAAA,MAAG,EAAE;YAC1F,QAAQ,qNAAA,CAAA,QAAK;YACb,IAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,KAAK,GAC9F,YAAY,CAAC,UAAU;YACvB,KAAK,UAAU,WAAW,KAAK;YAC/B,KAAK,kBAAkB,IAAI,CAAC;QAC9B;IACF;IAEA,IAAI,eAAe,OAAO,MAAM,CAAC;QAC/B,UAAU;IACZ,GAAG,YAAY;IAEf,IAAI,QAAQ,iBAAiB,OAAO,kBAAkB;QACpD,GAAG;QACH,GAAG;IACL,GAAG,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACtB,GAAG;QACH,GAAG;IACL;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,CAAC;IAEX,IAAI,iBAAiB;QACnB,IAAI;QAEJ,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,OAAO,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,OAAO,MAAM,IAAI,eAAe,SAAS,GAAG,CAAC,IAAI,gBAAgB,IAAI,CAAC,KAAK,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,cAAc;IACjT;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,eAAe,CAAC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,gBAAgB,SAAS,GAAG,IAAI,eAAe;AAC7M;AAEA,SAAS,cAAc,KAAK;IAC1B,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO;IAC3B,IAAI,wBAAwB,QAAQ,eAAe,EAC/C,kBAAkB,0BAA0B,KAAK,IAAI,OAAO,uBAC5D,oBAAoB,QAAQ,QAAQ,EACpC,WAAW,sBAAsB,KAAK,IAAI,OAAO,mBACjD,wBAAwB,QAAQ,YAAY,EAC5C,eAAe,0BAA0B,KAAK,IAAI,OAAO;IAC7D,IAAI,eAAe;QACjB,WAAW,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,SAAS;QAC3C,WAAW,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE,MAAM,SAAS;QACvC,QAAQ,MAAM,QAAQ,CAAC,MAAM;QAC7B,YAAY,MAAM,KAAK,CAAC,MAAM;QAC9B,iBAAiB;QACjB,SAAS,MAAM,OAAO,CAAC,QAAQ,KAAK;IACtC;IAEA,IAAI,MAAM,aAAa,CAAC,aAAa,IAAI,MAAM;QAC7C,MAAM,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;YACvG,SAAS,MAAM,aAAa,CAAC,aAAa;YAC1C,UAAU,MAAM,OAAO,CAAC,QAAQ;YAChC,UAAU;YACV,cAAc;QAChB;IACF;IAEA,IAAI,MAAM,aAAa,CAAC,KAAK,IAAI,MAAM;QACrC,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;YACrG,SAAS,MAAM,aAAa,CAAC,KAAK;YAClC,UAAU;YACV,UAAU;YACV,cAAc;QAChB;IACF;IAEA,MAAM,UAAU,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE;QACnE,yBAAyB,MAAM,SAAS;IAC1C;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,MAAM,CAAC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/applyStyles.js"], "sourcesContent": ["import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};"], "names": [], "mappings": ";;;AAAA;AACA,kZAA4D,0EAA0E;;;AACtI,gEAAgE;AAEhE,SAAS,YAAY,IAAI;IACvB,IAAI,QAAQ,KAAK,KAAK;IACtB,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE,OAAO,CAAC,SAAU,IAAI;QAChD,IAAI,QAAQ,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC;QACnC,IAAI,aAAa,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC;QAC5C,IAAI,UAAU,MAAM,QAAQ,CAAC,KAAK,EAAE,uCAAuC;QAE3E,IAAI,CAAC,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,UAAU;YACpD;QACF,EAAE,kEAAkE;QACpE,kDAAkD;QAClD,2BAA2B;QAG3B,OAAO,MAAM,CAAC,QAAQ,KAAK,EAAE;QAC7B,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,SAAU,IAAI;YAC5C,IAAI,QAAQ,UAAU,CAAC,KAAK;YAE5B,IAAI,UAAU,OAAO;gBACnB,QAAQ,eAAe,CAAC;YAC1B,OAAO;gBACL,QAAQ,YAAY,CAAC,MAAM,UAAU,OAAO,KAAK;YACnD;QACF;IACF;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,IAAI,QAAQ,MAAM,KAAK;IACvB,IAAI,gBAAgB;QAClB,QAAQ;YACN,UAAU,MAAM,OAAO,CAAC,QAAQ;YAChC,MAAM;YACN,KAAK;YACL,QAAQ;QACV;QACA,OAAO;YACL,UAAU;QACZ;QACA,WAAW,CAAC;IACd;IACA,OAAO,MAAM,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,cAAc,MAAM;IAC/D,MAAM,MAAM,GAAG;IAEf,IAAI,MAAM,QAAQ,CAAC,KAAK,EAAE;QACxB,OAAO,MAAM,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,cAAc,KAAK;IAC/D;IAEA,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE,OAAO,CAAC,SAAU,IAAI;YAChD,IAAI,UAAU,MAAM,QAAQ,CAAC,KAAK;YAClC,IAAI,aAAa,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC;YAC5C,IAAI,kBAAkB,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,MAAM,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,GAAG,kDAAkD;YAEnK,IAAI,QAAQ,gBAAgB,MAAM,CAAC,SAAU,KAAK,EAAE,QAAQ;gBAC1D,KAAK,CAAC,SAAS,GAAG;gBAClB,OAAO;YACT,GAAG,CAAC,IAAI,uCAAuC;YAE/C,IAAI,CAAC,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAC,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,UAAU;gBACpD;YACF;YAEA,OAAO,MAAM,CAAC,QAAQ,KAAK,EAAE;YAC7B,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,SAAU,SAAS;gBACjD,QAAQ,eAAe,CAAC;YAC1B;QACF;IACF;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,UAAU;QAAC;KAAgB;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/offset.js"], "sourcesContent": ["import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};"], "names": [], "mappings": ";;;;AAAA;AACA,8WAA4D,oDAAoD;;;AAEzG,SAAS,wBAAwB,SAAS,EAAE,KAAK,EAAE,MAAM;IAC9D,IAAI,gBAAgB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE;IACrC,IAAI,iBAAiB;QAAC,qNAAA,CAAA,OAAI;QAAE,qNAAA,CAAA,MAAG;KAAC,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,IAAI;IAEpE,IAAI,OAAO,OAAO,WAAW,aAAa,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QACxE,WAAW;IACb,MAAM,QACF,WAAW,IAAI,CAAC,EAAE,EAClB,WAAW,IAAI,CAAC,EAAE;IAEtB,WAAW,YAAY;IACvB,WAAW,CAAC,YAAY,CAAC,IAAI;IAC7B,OAAO;QAAC,qNAAA,CAAA,OAAI;QAAE,qNAAA,CAAA,QAAK;KAAC,CAAC,OAAO,CAAC,kBAAkB,IAAI;QACjD,GAAG;QACH,GAAG;IACL,IAAI;QACF,GAAG;QACH,GAAG;IACL;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO,EACvB,OAAO,MAAM,IAAI;IACrB,IAAI,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI;QAAC;QAAG;KAAE,GAAG;IACnD,IAAI,OAAO,qNAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QACnD,GAAG,CAAC,UAAU,GAAG,wBAAwB,WAAW,MAAM,KAAK,EAAE;QACjE,OAAO;IACT,GAAG,CAAC;IACJ,IAAI,wBAAwB,IAAI,CAAC,MAAM,SAAS,CAAC,EAC7C,IAAI,sBAAsB,CAAC,EAC3B,IAAI,sBAAsB,CAAC;IAE/B,IAAI,MAAM,aAAa,CAAC,aAAa,IAAI,MAAM;QAC7C,MAAM,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI;QACvC,MAAM,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI;IACzC;IAEA,MAAM,aAAa,CAAC,KAAK,GAAG;AAC9B,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,UAAU;QAAC;KAAgB;IAC3B,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getOppositePlacement.js"], "sourcesContent": ["var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}"], "names": [], "mappings": ";;;AAAA,IAAI,OAAO;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;AACP;AACe,SAAS,qBAAqB,SAAS;IACpD,OAAO,UAAU,OAAO,CAAC,0BAA0B,SAAU,OAAO;QAClE,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getOppositeVariationPlacement.js"], "sourcesContent": ["var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}"], "names": [], "mappings": ";;;AAAA,IAAI,OAAO;IACT,OAAO;IACP,KAAK;AACP;AACe,SAAS,8BAA8B,SAAS;IAC7D,OAAO,UAAU,OAAO,CAAC,cAAc,SAAU,OAAO;QACtD,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getViewportRect.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACvD,IAAI,MAAM,CAAA,GAAA,yOAAA,CAAA,UAAS,AAAD,EAAE;IACpB,IAAI,OAAO,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE;IAC9B,IAAI,iBAAiB,IAAI,cAAc;IACvC,IAAI,QAAQ,KAAK,WAAW;IAC5B,IAAI,SAAS,KAAK,YAAY;IAC9B,IAAI,IAAI;IACR,IAAI,IAAI;IAER,IAAI,gBAAgB;QAClB,QAAQ,eAAe,KAAK;QAC5B,SAAS,eAAe,MAAM;QAC9B,IAAI,iBAAiB,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD;QAEpC,IAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;YAC7D,IAAI,eAAe,UAAU;YAC7B,IAAI,eAAe,SAAS;QAC9B;IACF;IAEA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,GAAG,IAAI,CAAA,GAAA,mPAAA,CAAA,UAAmB,AAAD,EAAE;QAC3B,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getDocumentRect.js"], "sourcesContent": ["import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA,2XAAwC,+EAA+E;;;;;;AAGxG,SAAS,gBAAgB,OAAO;IAC7C,IAAI;IAEJ,IAAI,OAAO,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE;IAC9B,IAAI,YAAY,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE;IAChC,IAAI,OAAO,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;IACxG,IAAI,QAAQ,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,OAAO,KAAK,WAAW,GAAG,GAAG,OAAO,KAAK,WAAW,GAAG;IAC3G,IAAI,SAAS,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,OAAO,KAAK,YAAY,GAAG,GAAG,OAAO,KAAK,YAAY,GAAG;IAChH,IAAI,IAAI,CAAC,UAAU,UAAU,GAAG,CAAA,GAAA,mPAAA,CAAA,UAAmB,AAAD,EAAE;IACpD,IAAI,IAAI,CAAC,UAAU,SAAS;IAE5B,IAAI,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,QAAQ,MAAM,SAAS,KAAK,OAAO;QACtD,KAAK,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,OAAO,KAAK,WAAW,GAAG,KAAK;IAC5D;IAEA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,GAAG;QACH,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/contains.js"], "sourcesContent": ["import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,SAAS,MAAM,EAAE,KAAK;IAC5C,IAAI,WAAW,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,2CAA2C;IAEpG,IAAI,OAAO,QAAQ,CAAC,QAAQ;QAC1B,OAAO;IACT,OACK,IAAI,YAAY,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QACzC,IAAI,OAAO;QAEX,GAAG;YACD,IAAI,QAAQ,OAAO,UAAU,CAAC,OAAO;gBACnC,OAAO;YACT,EAAE,gEAAgE;YAGlE,OAAO,KAAK,UAAU,IAAI,KAAK,IAAI;QACrC,QAAS,KAAM;IACjB,EAAE,+BAA+B;IAGnC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/rectToClientRect.js"], "sourcesContent": ["export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}"], "names": [], "mappings": ";;;AAAe,SAAS,iBAAiB,IAAI;IAC3C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC7B,MAAM,KAAK,CAAC;QACZ,KAAK,KAAK,CAAC;QACX,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;QAC1B,QAAQ,KAAK,CAAC,GAAG,KAAK,MAAM;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/dom-utils/getClippingRect.js"], "sourcesContent": ["import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA,SAAS,2BAA2B,OAAO,EAAE,QAAQ;IACnD,IAAI,OAAO,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,SAAS,OAAO,aAAa;IAC9D,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,QAAQ,SAAS;IACvC,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,QAAQ,UAAU;IAC1C,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,QAAQ,YAAY;IAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG,QAAQ,WAAW;IAC5C,KAAK,KAAK,GAAG,QAAQ,WAAW;IAChC,KAAK,MAAM,GAAG,QAAQ,YAAY;IAClC,KAAK,CAAC,GAAG,KAAK,IAAI;IAClB,KAAK,CAAC,GAAG,KAAK,GAAG;IACjB,OAAO;AACT;AAEA,SAAS,2BAA2B,OAAO,EAAE,cAAc,EAAE,QAAQ;IACnE,OAAO,mBAAmB,qNAAA,CAAA,WAAQ,GAAG,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,SAAS,aAAa,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,2BAA2B,gBAAgB,YAAY,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE;AACrO,EAAE,8EAA8E;AAChF,2EAA2E;AAC3E,YAAY;AAGZ,SAAS,mBAAmB,OAAO;IACjC,IAAI,kBAAkB,CAAA,GAAA,iPAAA,CAAA,UAAiB,AAAD,EAAE,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;IACtD,IAAI,oBAAoB;QAAC;QAAY;KAAQ,CAAC,OAAO,CAAC,CAAA,GAAA,gPAAA,CAAA,UAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK;IAC7F,IAAI,iBAAiB,qBAAqB,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,WAAW;IAE9F,IAAI,CAAC,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QAC9B,OAAO,EAAE;IACX,EAAE,gFAAgF;IAGlF,OAAO,gBAAgB,MAAM,CAAC,SAAU,cAAc;QACpD,OAAO,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,CAAA,GAAA,wOAAA,CAAA,UAAQ,AAAD,EAAE,gBAAgB,mBAAmB,CAAA,GAAA,2OAAA,CAAA,UAAW,AAAD,EAAE,oBAAoB;IAClH;AACF,EAAE,4EAA4E;AAI/D,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ;IAC/E,IAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,WAAW,EAAE,CAAC,MAAM,CAAC;IACnG,IAAI,kBAAkB,EAAE,CAAC,MAAM,CAAC,qBAAqB;QAAC;KAAa;IACnE,IAAI,sBAAsB,eAAe,CAAC,EAAE;IAC5C,IAAI,eAAe,gBAAgB,MAAM,CAAC,SAAU,OAAO,EAAE,cAAc;QACzE,IAAI,OAAO,2BAA2B,SAAS,gBAAgB;QAC/D,QAAQ,GAAG,GAAG,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG;QACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,EAAE,QAAQ,KAAK;QAC7C,QAAQ,MAAM,GAAG,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,MAAM,EAAE,QAAQ,MAAM;QAChD,QAAQ,IAAI,GAAG,CAAA,GAAA,6NAAA,CAAA,MAAG,AAAD,EAAE,KAAK,IAAI,EAAE,QAAQ,IAAI;QAC1C,OAAO;IACT,GAAG,2BAA2B,SAAS,qBAAqB;IAC5D,aAAa,KAAK,GAAG,aAAa,KAAK,GAAG,aAAa,IAAI;IAC3D,aAAa,MAAM,GAAG,aAAa,MAAM,GAAG,aAAa,GAAG;IAC5D,aAAa,CAAC,GAAG,aAAa,IAAI;IAClC,aAAa,CAAC,GAAG,aAAa,GAAG;IACjC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getFreshSideObject.js"], "sourcesContent": ["export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}"], "names": [], "mappings": ";;;AAAe,SAAS;IACtB,OAAO;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/mergePaddingObject.js"], "sourcesContent": ["import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,mBAAmB,aAAa;IACtD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,CAAA,GAAA,2OAAA,CAAA,UAAkB,AAAD,KAAK;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/expandToHashMap.js"], "sourcesContent": ["export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}"], "names": [], "mappings": ";;;AAAe,SAAS,gBAAgB,KAAK,EAAE,IAAI;IACjD,OAAO,KAAK,MAAM,CAAC,SAAU,OAAO,EAAE,GAAG;QACvC,OAAO,CAAC,IAAI,GAAG;QACf,OAAO;IACT,GAAG,CAAC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/detectOverflow.js"], "sourcesContent": ["import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iZAAoD,oDAAoD;;;;;;;;;;AAEzF,SAAS,eAAe,KAAK,EAAE,OAAO;IACnD,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IAEA,IAAI,WAAW,SACX,qBAAqB,SAAS,SAAS,EACvC,YAAY,uBAAuB,KAAK,IAAI,MAAM,SAAS,GAAG,oBAC9D,oBAAoB,SAAS,QAAQ,EACrC,WAAW,sBAAsB,KAAK,IAAI,MAAM,QAAQ,GAAG,mBAC3D,oBAAoB,SAAS,QAAQ,EACrC,WAAW,sBAAsB,KAAK,IAAI,qNAAA,CAAA,kBAAe,GAAG,mBAC5D,wBAAwB,SAAS,YAAY,EAC7C,eAAe,0BAA0B,KAAK,IAAI,qNAAA,CAAA,WAAQ,GAAG,uBAC7D,wBAAwB,SAAS,cAAc,EAC/C,iBAAiB,0BAA0B,KAAK,IAAI,qNAAA,CAAA,SAAM,GAAG,uBAC7D,uBAAuB,SAAS,WAAW,EAC3C,cAAc,yBAAyB,KAAK,IAAI,QAAQ,sBACxD,mBAAmB,SAAS,OAAO,EACnC,UAAU,qBAAqB,KAAK,IAAI,IAAI;IAChD,IAAI,gBAAgB,CAAA,GAAA,2OAAA,CAAA,UAAkB,AAAD,EAAE,OAAO,YAAY,WAAW,UAAU,CAAA,GAAA,wOAAA,CAAA,UAAe,AAAD,EAAE,SAAS,qNAAA,CAAA,iBAAc;IACtH,IAAI,aAAa,mBAAmB,qNAAA,CAAA,SAAM,GAAG,qNAAA,CAAA,YAAS,GAAG,qNAAA,CAAA,SAAM;IAC/D,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,UAAU,MAAM,QAAQ,CAAC,cAAc,aAAa,eAAe;IACvE,IAAI,qBAAqB,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,0OAAA,CAAA,YAAS,AAAD,EAAE,WAAW,UAAU,QAAQ,cAAc,IAAI,CAAA,GAAA,kPAAA,CAAA,UAAkB,AAAD,EAAE,MAAM,QAAQ,CAAC,MAAM,GAAG,UAAU,cAAc;IACrK,IAAI,sBAAsB,CAAA,GAAA,qPAAA,CAAA,UAAqB,AAAD,EAAE,MAAM,QAAQ,CAAC,SAAS;IACxE,IAAI,gBAAgB,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE;QACjC,WAAW;QACX,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA,IAAI,mBAAmB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;IACtE,IAAI,oBAAoB,mBAAmB,qNAAA,CAAA,SAAM,GAAG,mBAAmB,qBAAqB,2CAA2C;IACvI,2CAA2C;IAE3C,IAAI,kBAAkB;QACpB,KAAK,mBAAmB,GAAG,GAAG,kBAAkB,GAAG,GAAG,cAAc,GAAG;QACvE,QAAQ,kBAAkB,MAAM,GAAG,mBAAmB,MAAM,GAAG,cAAc,MAAM;QACnF,MAAM,mBAAmB,IAAI,GAAG,kBAAkB,IAAI,GAAG,cAAc,IAAI;QAC3E,OAAO,kBAAkB,KAAK,GAAG,mBAAmB,KAAK,GAAG,cAAc,KAAK;IACjF;IACA,IAAI,aAAa,MAAM,aAAa,CAAC,MAAM,EAAE,oDAAoD;IAEjG,IAAI,mBAAmB,qNAAA,CAAA,SAAM,IAAI,YAAY;QAC3C,IAAI,SAAS,UAAU,CAAC,UAAU;QAClC,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,SAAU,GAAG;YAChD,IAAI,WAAW;gBAAC,qNAAA,CAAA,QAAK;gBAAE,qNAAA,CAAA,SAAM;aAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;YACxD,IAAI,OAAO;gBAAC,qNAAA,CAAA,MAAG;gBAAE,qNAAA,CAAA,SAAM;aAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,MAAM;YACnD,eAAe,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG;QACzC;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/computeAutoPlacement.js"], "sourcesContent": ["import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACe,SAAS,qBAAqB,KAAK,EAAE,OAAO;IACzD,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IAEA,IAAI,WAAW,SACX,YAAY,SAAS,SAAS,EAC9B,WAAW,SAAS,QAAQ,EAC5B,eAAe,SAAS,YAAY,EACpC,UAAU,SAAS,OAAO,EAC1B,iBAAiB,SAAS,cAAc,EACxC,wBAAwB,SAAS,qBAAqB,EACtD,wBAAwB,0BAA0B,KAAK,IAAI,qNAAA,CAAA,aAAa,GAAG;IAC/E,IAAI,YAAY,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE;IAC7B,IAAI,aAAa,YAAY,iBAAiB,qNAAA,CAAA,sBAAmB,GAAG,qNAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,SAAU,SAAS;QAChH,OAAO,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE,eAAe;IACrC,KAAK,qNAAA,CAAA,iBAAc;IACnB,IAAI,oBAAoB,WAAW,MAAM,CAAC,SAAU,SAAS;QAC3D,OAAO,sBAAsB,OAAO,CAAC,cAAc;IACrD;IAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAClC,oBAAoB;IACtB,EAAE,sFAAsF;IAGxF,IAAI,YAAY,kBAAkB,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QAC/D,GAAG,CAAC,UAAU,GAAG,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,OAAO;YACrC,WAAW;YACX,UAAU;YACV,cAAc;YACd,SAAS;QACX,EAAE,CAAC,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,WAAW;QAC/B,OAAO;IACT,GAAG,CAAC;IACJ,OAAO,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAC/C,OAAO,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/flip.js"], "sourcesContent": ["import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,2YAAqD,oDAAoD;;;;;;;;AAEzG,SAAS,8BAA8B,SAAS;IAC9C,IAAI,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,eAAe,qNAAA,CAAA,OAAI,EAAE;QACxC,OAAO,EAAE;IACX;IAEA,IAAI,oBAAoB,CAAA,GAAA,6OAAA,CAAA,UAAoB,AAAD,EAAE;IAC7C,OAAO;QAAC,CAAA,GAAA,sPAAA,CAAA,UAA6B,AAAD,EAAE;QAAY;QAAmB,CAAA,GAAA,sPAAA,CAAA,UAA6B,AAAD,EAAE;KAAmB;AACxH;AAEA,SAAS,KAAK,IAAI;IAChB,IAAI,QAAQ,KAAK,KAAK,EAClB,UAAU,KAAK,OAAO,EACtB,OAAO,KAAK,IAAI;IAEpB,IAAI,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE;QACnC;IACF;IAEA,IAAI,oBAAoB,QAAQ,QAAQ,EACpC,gBAAgB,sBAAsB,KAAK,IAAI,OAAO,mBACtD,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,OAAO,kBACpD,8BAA8B,QAAQ,kBAAkB,EACxD,UAAU,QAAQ,OAAO,EACzB,WAAW,QAAQ,QAAQ,EAC3B,eAAe,QAAQ,YAAY,EACnC,cAAc,QAAQ,WAAW,EACjC,wBAAwB,QAAQ,cAAc,EAC9C,iBAAiB,0BAA0B,KAAK,IAAI,OAAO,uBAC3D,wBAAwB,QAAQ,qBAAqB;IACzD,IAAI,qBAAqB,MAAM,OAAO,CAAC,SAAS;IAChD,IAAI,gBAAgB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE;IACrC,IAAI,kBAAkB,kBAAkB;IACxC,IAAI,qBAAqB,+BAA+B,CAAC,mBAAmB,CAAC,iBAAiB;QAAC,CAAA,GAAA,6OAAA,CAAA,UAAoB,AAAD,EAAE;KAAoB,GAAG,8BAA8B,mBAAmB;IAC5L,IAAI,aAAa;QAAC;KAAmB,CAAC,MAAM,CAAC,oBAAoB,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QAC9F,OAAO,IAAI,MAAM,CAAC,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,eAAe,qNAAA,CAAA,OAAI,GAAG,CAAA,GAAA,6OAAA,CAAA,UAAoB,AAAD,EAAE,OAAO;YACnF,WAAW;YACX,UAAU;YACV,cAAc;YACd,SAAS;YACT,gBAAgB;YAChB,uBAAuB;QACzB,KAAK;IACP,GAAG,EAAE;IACL,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,YAAY,IAAI;IACpB,IAAI,qBAAqB;IACzB,IAAI,wBAAwB,UAAU,CAAC,EAAE;IAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,IAAI,YAAY,UAAU,CAAC,EAAE;QAE7B,IAAI,iBAAiB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE;QAEtC,IAAI,mBAAmB,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE,eAAe,qNAAA,CAAA,QAAK;QACxD,IAAI,aAAa;YAAC,qNAAA,CAAA,MAAG;YAAE,qNAAA,CAAA,SAAM;SAAC,CAAC,OAAO,CAAC,mBAAmB;QAC1D,IAAI,MAAM,aAAa,UAAU;QACjC,IAAI,WAAW,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,OAAO;YACnC,WAAW;YACX,UAAU;YACV,cAAc;YACd,aAAa;YACb,SAAS;QACX;QACA,IAAI,oBAAoB,aAAa,mBAAmB,qNAAA,CAAA,QAAK,GAAG,qNAAA,CAAA,OAAI,GAAG,mBAAmB,qNAAA,CAAA,SAAM,GAAG,qNAAA,CAAA,MAAG;QAEtG,IAAI,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE;YACxC,oBAAoB,CAAA,GAAA,6OAAA,CAAA,UAAoB,AAAD,EAAE;QAC3C;QAEA,IAAI,mBAAmB,CAAA,GAAA,6OAAA,CAAA,UAAoB,AAAD,EAAE;QAC5C,IAAI,SAAS,EAAE;QAEf,IAAI,eAAe;YACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI;QAC1C;QAEA,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,IAAI,GAAG,QAAQ,CAAC,iBAAiB,IAAI;QAC9E;QAEA,IAAI,OAAO,KAAK,CAAC,SAAU,KAAK;YAC9B,OAAO;QACT,IAAI;YACF,wBAAwB;YACxB,qBAAqB;YACrB;QACF;QAEA,UAAU,GAAG,CAAC,WAAW;IAC3B;IAEA,IAAI,oBAAoB;QACtB,oDAAoD;QACpD,IAAI,iBAAiB,iBAAiB,IAAI;QAE1C,IAAI,QAAQ,SAAS,MAAM,EAAE;YAC3B,IAAI,mBAAmB,WAAW,IAAI,CAAC,SAAU,SAAS;gBACxD,IAAI,SAAS,UAAU,GAAG,CAAC;gBAE3B,IAAI,QAAQ;oBACV,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,SAAU,KAAK;wBAC9C,OAAO;oBACT;gBACF;YACF;YAEA,IAAI,kBAAkB;gBACpB,wBAAwB;gBACxB,OAAO;YACT;QACF;QAEA,IAAK,IAAI,KAAK,gBAAgB,KAAK,GAAG,KAAM;YAC1C,IAAI,OAAO,MAAM;YAEjB,IAAI,SAAS,SAAS;QACxB;IACF;IAEA,IAAI,MAAM,SAAS,KAAK,uBAAuB;QAC7C,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;QAClC,MAAM,SAAS,GAAG;QAClB,MAAM,KAAK,GAAG;IAChB;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,kBAAkB;QAAC;KAAS;IAC5B,MAAM;QACJ,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/getAltAxis.js"], "sourcesContent": ["export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}"], "names": [], "mappings": ";;;AAAe,SAAS,WAAW,IAAI;IACrC,OAAO,SAAS,MAAM,MAAM;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/utils/within.js"], "sourcesContent": ["import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,OAAO,GAAG,EAAE,KAAK,EAAE,GAAG;IACpC,OAAO,CAAA,GAAA,6NAAA,CAAA,MAAO,AAAD,EAAE,KAAK,CAAA,GAAA,6NAAA,CAAA,MAAO,AAAD,EAAE,OAAO;AACrC;AACO,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,GAAG;IAC5C,IAAI,IAAI,OAAO,KAAK,OAAO;IAC3B,OAAO,IAAI,MAAM,MAAM;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/preventOverflow.js"], "sourcesContent": ["import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,SAAS,gBAAgB,IAAI;IAC3B,IAAI,QAAQ,KAAK,KAAK,EAClB,UAAU,KAAK,OAAO,EACtB,OAAO,KAAK,IAAI;IACpB,IAAI,oBAAoB,QAAQ,QAAQ,EACpC,gBAAgB,sBAAsB,KAAK,IAAI,OAAO,mBACtD,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,QAAQ,kBACrD,WAAW,QAAQ,QAAQ,EAC3B,eAAe,QAAQ,YAAY,EACnC,cAAc,QAAQ,WAAW,EACjC,UAAU,QAAQ,OAAO,EACzB,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO,iBAC7C,wBAAwB,QAAQ,YAAY,EAC5C,eAAe,0BAA0B,KAAK,IAAI,IAAI;IAC1D,IAAI,WAAW,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QACnC,UAAU;QACV,cAAc;QACd,SAAS;QACT,aAAa;IACf;IACA,IAAI,gBAAgB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,SAAS;IACpD,IAAI,YAAY,CAAA,GAAA,qOAAA,CAAA,UAAY,AAAD,EAAE,MAAM,SAAS;IAC5C,IAAI,kBAAkB,CAAC;IACvB,IAAI,WAAW,CAAA,GAAA,iPAAA,CAAA,UAAwB,AAAD,EAAE;IACxC,IAAI,UAAU,CAAA,GAAA,mOAAA,CAAA,UAAU,AAAD,EAAE;IACzB,IAAI,gBAAgB,MAAM,aAAa,CAAC,aAAa;IACrD,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE;QACvG,WAAW,MAAM,SAAS;IAC5B,MAAM;IACN,IAAI,8BAA8B,OAAO,sBAAsB,WAAW;QACxE,UAAU;QACV,SAAS;IACX,IAAI,OAAO,MAAM,CAAC;QAChB,UAAU;QACV,SAAS;IACX,GAAG;IACH,IAAI,sBAAsB,MAAM,aAAa,CAAC,MAAM,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG;IACrG,IAAI,OAAO;QACT,GAAG;QACH,GAAG;IACL;IAEA,IAAI,CAAC,eAAe;QAClB;IACF;IAEA,IAAI,eAAe;QACjB,IAAI;QAEJ,IAAI,WAAW,aAAa,MAAM,qNAAA,CAAA,MAAG,GAAG,qNAAA,CAAA,OAAI;QAC5C,IAAI,UAAU,aAAa,MAAM,qNAAA,CAAA,SAAM,GAAG,qNAAA,CAAA,QAAK;QAC/C,IAAI,MAAM,aAAa,MAAM,WAAW;QACxC,IAAI,SAAS,aAAa,CAAC,SAAS;QACpC,IAAI,MAAM,SAAS,QAAQ,CAAC,SAAS;QACrC,IAAI,MAAM,SAAS,QAAQ,CAAC,QAAQ;QACpC,IAAI,WAAW,SAAS,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI;QAC/C,IAAI,SAAS,cAAc,qNAAA,CAAA,QAAK,GAAG,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;QACvE,IAAI,SAAS,cAAc,qNAAA,CAAA,QAAK,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,0EAA0E;QACrJ,+BAA+B;QAE/B,IAAI,eAAe,MAAM,QAAQ,CAAC,KAAK;QACvC,IAAI,YAAY,UAAU,eAAe,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE,gBAAgB;YACrE,OAAO;YACP,QAAQ;QACV;QACA,IAAI,qBAAqB,MAAM,aAAa,CAAC,mBAAmB,GAAG,MAAM,aAAa,CAAC,mBAAmB,CAAC,OAAO,GAAG,CAAA,GAAA,2OAAA,CAAA,UAAkB,AAAD;QACtI,IAAI,kBAAkB,kBAAkB,CAAC,SAAS;QAClD,IAAI,kBAAkB,kBAAkB,CAAC,QAAQ,EAAE,0EAA0E;QAC7H,yEAAyE;QACzE,uEAAuE;QACvE,sEAAsE;QACtE,mBAAmB;QAEnB,IAAI,WAAW,CAAA,GAAA,+NAAA,CAAA,SAAM,AAAD,EAAE,GAAG,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI;QAC3D,IAAI,YAAY,kBAAkB,aAAa,CAAC,IAAI,GAAG,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,QAAQ,GAAG,SAAS,WAAW,kBAAkB,4BAA4B,QAAQ;QACpN,IAAI,YAAY,kBAAkB,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,QAAQ,GAAG,SAAS,WAAW,kBAAkB,4BAA4B,QAAQ;QACrN,IAAI,oBAAoB,MAAM,QAAQ,CAAC,KAAK,IAAI,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE,MAAM,QAAQ,CAAC,KAAK;QACpF,IAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,SAAS,IAAI,IAAI,kBAAkB,UAAU,IAAI,IAAI;QACjI,IAAI,sBAAsB,CAAC,wBAAwB,uBAAuB,OAAO,KAAK,IAAI,mBAAmB,CAAC,SAAS,KAAK,OAAO,wBAAwB;QAC3J,IAAI,YAAY,SAAS,YAAY,sBAAsB;QAC3D,IAAI,YAAY,SAAS,YAAY;QACrC,IAAI,kBAAkB,CAAA,GAAA,+NAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAA,GAAA,6NAAA,CAAA,MAAO,AAAD,EAAE,KAAK,aAAa,KAAK,QAAQ,SAAS,CAAA,GAAA,6NAAA,CAAA,MAAO,AAAD,EAAE,KAAK,aAAa;QAChH,aAAa,CAAC,SAAS,GAAG;QAC1B,IAAI,CAAC,SAAS,GAAG,kBAAkB;IACrC;IAEA,IAAI,cAAc;QAChB,IAAI;QAEJ,IAAI,YAAY,aAAa,MAAM,qNAAA,CAAA,MAAG,GAAG,qNAAA,CAAA,OAAI;QAE7C,IAAI,WAAW,aAAa,MAAM,qNAAA,CAAA,SAAM,GAAG,qNAAA,CAAA,QAAK;QAEhD,IAAI,UAAU,aAAa,CAAC,QAAQ;QAEpC,IAAI,OAAO,YAAY,MAAM,WAAW;QAExC,IAAI,OAAO,UAAU,QAAQ,CAAC,UAAU;QAExC,IAAI,OAAO,UAAU,QAAQ,CAAC,SAAS;QAEvC,IAAI,eAAe;YAAC,qNAAA,CAAA,MAAG;YAAE,qNAAA,CAAA,OAAI;SAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAE3D,IAAI,uBAAuB,CAAC,yBAAyB,uBAAuB,OAAO,KAAK,IAAI,mBAAmB,CAAC,QAAQ,KAAK,OAAO,yBAAyB;QAE7J,IAAI,aAAa,eAAe,OAAO,UAAU,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,uBAAuB,4BAA4B,OAAO;QAEpJ,IAAI,aAAa,eAAe,UAAU,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,uBAAuB,4BAA4B,OAAO,GAAG;QAEhJ,IAAI,mBAAmB,UAAU,eAAe,CAAA,GAAA,+NAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,SAAS,cAAc,CAAA,GAAA,+NAAA,CAAA,SAAM,AAAD,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa;QAEpK,aAAa,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG,mBAAmB;IACrC;IAEA,MAAM,aAAa,CAAC,KAAK,GAAG;AAC9B,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,kBAAkB;QAAC;KAAS;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/arrow.js"], "sourcesContent": ["import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8WAAwE,oDAAoD;;;;;;;;;;AAE5H,IAAI,kBAAkB,SAAS,gBAAgB,OAAO,EAAE,KAAK;IAC3D,UAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE;QAC/E,WAAW,MAAM,SAAS;IAC5B,MAAM;IACN,OAAO,CAAA,GAAA,2OAAA,CAAA,UAAkB,AAAD,EAAE,OAAO,YAAY,WAAW,UAAU,CAAA,GAAA,wOAAA,CAAA,UAAe,AAAD,EAAE,SAAS,qNAAA,CAAA,iBAAc;AAC3G;AAEA,SAAS,MAAM,IAAI;IACjB,IAAI;IAEJ,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI,EAChB,UAAU,KAAK,OAAO;IAC1B,IAAI,eAAe,MAAM,QAAQ,CAAC,KAAK;IACvC,IAAI,gBAAgB,MAAM,aAAa,CAAC,aAAa;IACrD,IAAI,gBAAgB,CAAA,GAAA,yOAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,SAAS;IACpD,IAAI,OAAO,CAAA,GAAA,iPAAA,CAAA,UAAwB,AAAD,EAAE;IACpC,IAAI,aAAa;QAAC,qNAAA,CAAA,OAAI;QAAE,qNAAA,CAAA,QAAK;KAAC,CAAC,OAAO,CAAC,kBAAkB;IACzD,IAAI,MAAM,aAAa,WAAW;IAElC,IAAI,CAAC,gBAAgB,CAAC,eAAe;QACnC;IACF;IAEA,IAAI,gBAAgB,gBAAgB,QAAQ,OAAO,EAAE;IACrD,IAAI,YAAY,CAAA,GAAA,6OAAA,CAAA,UAAa,AAAD,EAAE;IAC9B,IAAI,UAAU,SAAS,MAAM,qNAAA,CAAA,MAAG,GAAG,qNAAA,CAAA,OAAI;IACvC,IAAI,UAAU,SAAS,MAAM,qNAAA,CAAA,SAAM,GAAG,qNAAA,CAAA,QAAK;IAC3C,IAAI,UAAU,MAAM,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI;IACtH,IAAI,YAAY,aAAa,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK;IACjE,IAAI,oBAAoB,CAAA,GAAA,+OAAA,CAAA,UAAe,AAAD,EAAE;IACxC,IAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,YAAY,IAAI,IAAI,kBAAkB,WAAW,IAAI,IAAI;IAC/H,IAAI,oBAAoB,UAAU,IAAI,YAAY,GAAG,yEAAyE;IAC9H,+BAA+B;IAE/B,IAAI,MAAM,aAAa,CAAC,QAAQ;IAChC,IAAI,MAAM,aAAa,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC,QAAQ;IAC9D,IAAI,SAAS,aAAa,IAAI,SAAS,CAAC,IAAI,GAAG,IAAI;IACnD,IAAI,SAAS,CAAA,GAAA,+NAAA,CAAA,SAAM,AAAD,EAAE,KAAK,QAAQ,MAAM,2CAA2C;IAElF,IAAI,WAAW;IACf,MAAM,aAAa,CAAC,KAAK,GAAG,CAAC,wBAAwB,CAAC,GAAG,qBAAqB,CAAC,SAAS,GAAG,QAAQ,sBAAsB,YAAY,GAAG,SAAS,QAAQ,qBAAqB;AAChL;AAEA,SAAS,OAAO,KAAK;IACnB,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO;IAC3B,IAAI,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,wBAAwB;IAEzE,IAAI,gBAAgB,MAAM;QACxB;IACF,EAAE,eAAe;IAGjB,IAAI,OAAO,iBAAiB,UAAU;QACpC,eAAe,MAAM,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QAEnD,IAAI,CAAC,cAAc;YACjB;QACF;IACF;IAEA,IAAI,CAAC,CAAA,GAAA,wOAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,QAAQ,CAAC,MAAM,EAAE,eAAe;QAClD;IACF;IAEA,MAAM,QAAQ,CAAC,KAAK,GAAG;AACzB,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,UAAU;QAAC;KAAgB;IAC3B,kBAAkB;QAAC;KAAkB;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/modifiers/hide.js"], "sourcesContent": ["import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,eAAe,QAAQ,EAAE,IAAI,EAAE,gBAAgB;IACtD,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;YACjB,GAAG;YACH,GAAG;QACL;IACF;IAEA,OAAO;QACL,KAAK,SAAS,GAAG,GAAG,KAAK,MAAM,GAAG,iBAAiB,CAAC;QACpD,OAAO,SAAS,KAAK,GAAG,KAAK,KAAK,GAAG,iBAAiB,CAAC;QACvD,QAAQ,SAAS,MAAM,GAAG,KAAK,MAAM,GAAG,iBAAiB,CAAC;QAC1D,MAAM,SAAS,IAAI,GAAG,KAAK,KAAK,GAAG,iBAAiB,CAAC;IACvD;AACF;AAEA,SAAS,sBAAsB,QAAQ;IACrC,OAAO;QAAC,qNAAA,CAAA,MAAG;QAAE,qNAAA,CAAA,QAAK;QAAE,qNAAA,CAAA,SAAM;QAAE,qNAAA,CAAA,OAAI;KAAC,CAAC,IAAI,CAAC,SAAU,IAAI;QACnD,OAAO,QAAQ,CAAC,KAAK,IAAI;IAC3B;AACF;AAEA,SAAS,KAAK,IAAI;IAChB,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;IACpB,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,mBAAmB,MAAM,aAAa,CAAC,eAAe;IAC1D,IAAI,oBAAoB,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QAC5C,gBAAgB;IAClB;IACA,IAAI,oBAAoB,CAAA,GAAA,uOAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QAC5C,aAAa;IACf;IACA,IAAI,2BAA2B,eAAe,mBAAmB;IACjE,IAAI,sBAAsB,eAAe,mBAAmB,YAAY;IACxE,IAAI,oBAAoB,sBAAsB;IAC9C,IAAI,mBAAmB,sBAAsB;IAC7C,MAAM,aAAa,CAAC,KAAK,GAAG;QAC1B,0BAA0B;QAC1B,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;IACpB;IACA,MAAM,UAAU,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE;QACnE,gCAAgC;QAChC,uBAAuB;IACzB;AACF,EAAE,oDAAoD;uCAGvC;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,kBAAkB;QAAC;KAAkB;IACrC,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40popperjs%2Bcore%402.11.8/node_modules/%40popperjs/core/lib/popper.js"], "sourcesContent": ["import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,IAAI,mBAAmB;IAAC,2OAAA,CAAA,UAAc;IAAE,0OAAA,CAAA,UAAa;IAAE,0OAAA,CAAA,UAAa;IAAE,wOAAA,CAAA,UAAW;IAAE,mOAAA,CAAA,UAAM;IAAE,iOAAA,CAAA,UAAI;IAAE,4OAAA,CAAA,UAAe;IAAE,kOAAA,CAAA,UAAK;IAAE,iOAAA,CAAA,UAAI;CAAC;AAC9H,IAAI,eAAe,WAAW,GAAE,CAAA,GAAA,4OAAA,CAAA,kBAAe,AAAD,EAAE;IAC9C,kBAAkB;AACpB,IAAI,oDAAoD", "ignoreList": [0], "debugId": null}}]}