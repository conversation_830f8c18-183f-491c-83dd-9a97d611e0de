{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/github-dark-dimmed.mjs"], "sourcesContent": ["var githubDarkDimmed = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#ec775c\",\n    \"activityBar.background\": \"#22272e\",\n    \"activityBar.border\": \"#444c56\",\n    \"activityBar.foreground\": \"#adbac7\",\n    \"activityBar.inactiveForeground\": \"#768390\",\n    \"activityBarBadge.background\": \"#316dca\",\n    \"activityBarBadge.foreground\": \"#cdd9e5\",\n    \"badge.background\": \"#316dca\",\n    \"badge.foreground\": \"#cdd9e5\",\n    \"breadcrumb.activeSelectionForeground\": \"#768390\",\n    \"breadcrumb.focusForeground\": \"#adbac7\",\n    \"breadcrumb.foreground\": \"#768390\",\n    \"breadcrumbPicker.background\": \"#2d333b\",\n    \"button.background\": \"#347d39\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#46954a\",\n    \"button.secondaryBackground\": \"#3d444d\",\n    \"button.secondaryForeground\": \"#adbac7\",\n    \"button.secondaryHoverBackground\": \"#444c56\",\n    \"checkbox.background\": \"#2d333b\",\n    \"checkbox.border\": \"#444c56\",\n    \"debugConsole.errorForeground\": \"#ff938a\",\n    \"debugConsole.infoForeground\": \"#768390\",\n    \"debugConsole.sourceForeground\": \"#daaa3f\",\n    \"debugConsole.warningForeground\": \"#c69026\",\n    \"debugConsoleInputIcon.foreground\": \"#b083f0\",\n    \"debugIcon.breakpointForeground\": \"#e5534b\",\n    \"debugTokenExpression.boolean\": \"#6bc46d\",\n    \"debugTokenExpression.error\": \"#ff938a\",\n    \"debugTokenExpression.name\": \"#6cb6ff\",\n    \"debugTokenExpression.number\": \"#6bc46d\",\n    \"debugTokenExpression.string\": \"#96d0ff\",\n    \"debugTokenExpression.value\": \"#96d0ff\",\n    \"debugToolBar.background\": \"#2d333b\",\n    \"descriptionForeground\": \"#768390\",\n    \"diffEditor.insertedLineBackground\": \"#347d3926\",\n    \"diffEditor.insertedTextBackground\": \"#57ab5a4d\",\n    \"diffEditor.removedLineBackground\": \"#c93c3726\",\n    \"diffEditor.removedTextBackground\": \"#f470674d\",\n    \"dropdown.background\": \"#2d333b\",\n    \"dropdown.border\": \"#444c56\",\n    \"dropdown.foreground\": \"#adbac7\",\n    \"dropdown.listBackground\": \"#2d333b\",\n    \"editor.background\": \"#22272e\",\n    \"editor.findMatchBackground\": \"#966600\",\n    \"editor.findMatchHighlightBackground\": \"#eac55f80\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#46954a66\",\n    \"editor.foldBackground\": \"#636e7b1a\",\n    \"editor.foreground\": \"#adbac7\",\n    \"editor.lineHighlightBackground\": \"#636e7b1a\",\n    \"editor.linkedEditingBackground\": \"#539bf512\",\n    \"editor.selectionHighlightBackground\": \"#57ab5a40\",\n    \"editor.stackFrameHighlightBackground\": \"#ae7c1466\",\n    \"editor.wordHighlightBackground\": \"#636e7b80\",\n    \"editor.wordHighlightBorder\": \"#636e7b99\",\n    \"editor.wordHighlightStrongBackground\": \"#636e7b4d\",\n    \"editor.wordHighlightStrongBorder\": \"#636e7b99\",\n    \"editorBracketHighlight.foreground1\": \"#6cb6ff\",\n    \"editorBracketHighlight.foreground2\": \"#6bc46d\",\n    \"editorBracketHighlight.foreground3\": \"#daaa3f\",\n    \"editorBracketHighlight.foreground4\": \"#ff938a\",\n    \"editorBracketHighlight.foreground5\": \"#fc8dc7\",\n    \"editorBracketHighlight.foreground6\": \"#dcbdfb\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#768390\",\n    \"editorBracketMatch.background\": \"#57ab5a40\",\n    \"editorBracketMatch.border\": \"#57ab5a99\",\n    \"editorCursor.foreground\": \"#539bf5\",\n    \"editorGroup.border\": \"#444c56\",\n    \"editorGroupHeader.tabsBackground\": \"#1c2128\",\n    \"editorGroupHeader.tabsBorder\": \"#444c56\",\n    \"editorGutter.addedBackground\": \"#46954a66\",\n    \"editorGutter.deletedBackground\": \"#e5534b66\",\n    \"editorGutter.modifiedBackground\": \"#ae7c1466\",\n    \"editorIndentGuide.activeBackground\": \"#adbac73d\",\n    \"editorIndentGuide.background\": \"#adbac71f\",\n    \"editorInlayHint.background\": \"#76839033\",\n    \"editorInlayHint.foreground\": \"#768390\",\n    \"editorInlayHint.paramBackground\": \"#76839033\",\n    \"editorInlayHint.paramForeground\": \"#768390\",\n    \"editorInlayHint.typeBackground\": \"#76839033\",\n    \"editorInlayHint.typeForeground\": \"#768390\",\n    \"editorLineNumber.activeForeground\": \"#adbac7\",\n    \"editorLineNumber.foreground\": \"#636e7b\",\n    \"editorOverviewRuler.border\": \"#1c2128\",\n    \"editorWhitespace.foreground\": \"#545d68\",\n    \"editorWidget.background\": \"#2d333b\",\n    \"errorForeground\": \"#e5534b\",\n    \"focusBorder\": \"#316dca\",\n    \"foreground\": \"#adbac7\",\n    \"gitDecoration.addedResourceForeground\": \"#57ab5a\",\n    \"gitDecoration.conflictingResourceForeground\": \"#cc6b2c\",\n    \"gitDecoration.deletedResourceForeground\": \"#e5534b\",\n    \"gitDecoration.ignoredResourceForeground\": \"#636e7b\",\n    \"gitDecoration.modifiedResourceForeground\": \"#c69026\",\n    \"gitDecoration.submoduleResourceForeground\": \"#768390\",\n    \"gitDecoration.untrackedResourceForeground\": \"#57ab5a\",\n    \"icon.foreground\": \"#768390\",\n    \"input.background\": \"#22272e\",\n    \"input.border\": \"#444c56\",\n    \"input.foreground\": \"#adbac7\",\n    \"input.placeholderForeground\": \"#636e7b\",\n    \"keybindingLabel.foreground\": \"#adbac7\",\n    \"list.activeSelectionBackground\": \"#636e7b66\",\n    \"list.activeSelectionForeground\": \"#adbac7\",\n    \"list.focusBackground\": \"#4184e426\",\n    \"list.focusForeground\": \"#adbac7\",\n    \"list.highlightForeground\": \"#539bf5\",\n    \"list.hoverBackground\": \"#636e7b1a\",\n    \"list.hoverForeground\": \"#adbac7\",\n    \"list.inactiveFocusBackground\": \"#4184e426\",\n    \"list.inactiveSelectionBackground\": \"#636e7b66\",\n    \"list.inactiveSelectionForeground\": \"#adbac7\",\n    \"minimapSlider.activeBackground\": \"#76839047\",\n    \"minimapSlider.background\": \"#76839033\",\n    \"minimapSlider.hoverBackground\": \"#7683903d\",\n    \"notificationCenterHeader.background\": \"#2d333b\",\n    \"notificationCenterHeader.foreground\": \"#768390\",\n    \"notifications.background\": \"#2d333b\",\n    \"notifications.border\": \"#444c56\",\n    \"notifications.foreground\": \"#adbac7\",\n    \"notificationsErrorIcon.foreground\": \"#e5534b\",\n    \"notificationsInfoIcon.foreground\": \"#539bf5\",\n    \"notificationsWarningIcon.foreground\": \"#c69026\",\n    \"panel.background\": \"#1c2128\",\n    \"panel.border\": \"#444c56\",\n    \"panelInput.border\": \"#444c56\",\n    \"panelTitle.activeBorder\": \"#ec775c\",\n    \"panelTitle.activeForeground\": \"#adbac7\",\n    \"panelTitle.inactiveForeground\": \"#768390\",\n    \"peekViewEditor.background\": \"#636e7b1a\",\n    \"peekViewEditor.matchHighlightBackground\": \"#ae7c1466\",\n    \"peekViewResult.background\": \"#22272e\",\n    \"peekViewResult.matchHighlightBackground\": \"#ae7c1466\",\n    \"pickerGroup.border\": \"#444c56\",\n    \"pickerGroup.foreground\": \"#768390\",\n    \"progressBar.background\": \"#316dca\",\n    \"quickInput.background\": \"#2d333b\",\n    \"quickInput.foreground\": \"#adbac7\",\n    \"scrollbar.shadow\": \"#545d6833\",\n    \"scrollbarSlider.activeBackground\": \"#76839047\",\n    \"scrollbarSlider.background\": \"#76839033\",\n    \"scrollbarSlider.hoverBackground\": \"#7683903d\",\n    \"settings.headerForeground\": \"#adbac7\",\n    \"settings.modifiedItemIndicator\": \"#ae7c1466\",\n    \"sideBar.background\": \"#1c2128\",\n    \"sideBar.border\": \"#444c56\",\n    \"sideBar.foreground\": \"#adbac7\",\n    \"sideBarSectionHeader.background\": \"#1c2128\",\n    \"sideBarSectionHeader.border\": \"#444c56\",\n    \"sideBarSectionHeader.foreground\": \"#adbac7\",\n    \"sideBarTitle.foreground\": \"#adbac7\",\n    \"statusBar.background\": \"#22272e\",\n    \"statusBar.border\": \"#444c56\",\n    \"statusBar.debuggingBackground\": \"#c93c37\",\n    \"statusBar.debuggingForeground\": \"#cdd9e5\",\n    \"statusBar.focusBorder\": \"#316dca80\",\n    \"statusBar.foreground\": \"#768390\",\n    \"statusBar.noFolderBackground\": \"#22272e\",\n    \"statusBarItem.activeBackground\": \"#adbac71f\",\n    \"statusBarItem.focusBorder\": \"#316dca\",\n    \"statusBarItem.hoverBackground\": \"#adbac714\",\n    \"statusBarItem.prominentBackground\": \"#636e7b66\",\n    \"statusBarItem.remoteBackground\": \"#444c56\",\n    \"statusBarItem.remoteForeground\": \"#adbac7\",\n    \"symbolIcon.arrayForeground\": \"#e0823d\",\n    \"symbolIcon.booleanForeground\": \"#539bf5\",\n    \"symbolIcon.classForeground\": \"#e0823d\",\n    \"symbolIcon.colorForeground\": \"#6cb6ff\",\n    \"symbolIcon.constantForeground\": [\n      \"#b4f1b4\",\n      \"#8ddb8c\",\n      \"#6bc46d\",\n      \"#57ab5a\",\n      \"#46954a\",\n      \"#347d39\",\n      \"#2b6a30\",\n      \"#245829\",\n      \"#1b4721\",\n      \"#113417\"\n    ],\n    \"symbolIcon.constructorForeground\": \"#dcbdfb\",\n    \"symbolIcon.enumeratorForeground\": \"#e0823d\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#539bf5\",\n    \"symbolIcon.eventForeground\": \"#636e7b\",\n    \"symbolIcon.fieldForeground\": \"#e0823d\",\n    \"symbolIcon.fileForeground\": \"#c69026\",\n    \"symbolIcon.folderForeground\": \"#c69026\",\n    \"symbolIcon.functionForeground\": \"#b083f0\",\n    \"symbolIcon.interfaceForeground\": \"#e0823d\",\n    \"symbolIcon.keyForeground\": \"#539bf5\",\n    \"symbolIcon.keywordForeground\": \"#f47067\",\n    \"symbolIcon.methodForeground\": \"#b083f0\",\n    \"symbolIcon.moduleForeground\": \"#f47067\",\n    \"symbolIcon.namespaceForeground\": \"#f47067\",\n    \"symbolIcon.nullForeground\": \"#539bf5\",\n    \"symbolIcon.numberForeground\": \"#57ab5a\",\n    \"symbolIcon.objectForeground\": \"#e0823d\",\n    \"symbolIcon.operatorForeground\": \"#6cb6ff\",\n    \"symbolIcon.packageForeground\": \"#e0823d\",\n    \"symbolIcon.propertyForeground\": \"#e0823d\",\n    \"symbolIcon.referenceForeground\": \"#539bf5\",\n    \"symbolIcon.snippetForeground\": \"#539bf5\",\n    \"symbolIcon.stringForeground\": \"#6cb6ff\",\n    \"symbolIcon.structForeground\": \"#e0823d\",\n    \"symbolIcon.textForeground\": \"#6cb6ff\",\n    \"symbolIcon.typeParameterForeground\": \"#6cb6ff\",\n    \"symbolIcon.unitForeground\": \"#539bf5\",\n    \"symbolIcon.variableForeground\": \"#e0823d\",\n    \"tab.activeBackground\": \"#22272e\",\n    \"tab.activeBorder\": \"#22272e\",\n    \"tab.activeBorderTop\": \"#ec775c\",\n    \"tab.activeForeground\": \"#adbac7\",\n    \"tab.border\": \"#444c56\",\n    \"tab.hoverBackground\": \"#22272e\",\n    \"tab.inactiveBackground\": \"#1c2128\",\n    \"tab.inactiveForeground\": \"#768390\",\n    \"tab.unfocusedActiveBorder\": \"#22272e\",\n    \"tab.unfocusedActiveBorderTop\": \"#444c56\",\n    \"tab.unfocusedHoverBackground\": \"#636e7b1a\",\n    \"terminal.ansiBlack\": \"#545d68\",\n    \"terminal.ansiBlue\": \"#539bf5\",\n    \"terminal.ansiBrightBlack\": \"#636e7b\",\n    \"terminal.ansiBrightBlue\": \"#6cb6ff\",\n    \"terminal.ansiBrightCyan\": \"#56d4dd\",\n    \"terminal.ansiBrightGreen\": \"#6bc46d\",\n    \"terminal.ansiBrightMagenta\": \"#dcbdfb\",\n    \"terminal.ansiBrightRed\": \"#ff938a\",\n    \"terminal.ansiBrightWhite\": \"#cdd9e5\",\n    \"terminal.ansiBrightYellow\": \"#daaa3f\",\n    \"terminal.ansiCyan\": \"#39c5cf\",\n    \"terminal.ansiGreen\": \"#57ab5a\",\n    \"terminal.ansiMagenta\": \"#b083f0\",\n    \"terminal.ansiRed\": \"#f47067\",\n    \"terminal.ansiWhite\": \"#909dab\",\n    \"terminal.ansiYellow\": \"#c69026\",\n    \"terminal.foreground\": \"#adbac7\",\n    \"textBlockQuote.background\": \"#1c2128\",\n    \"textBlockQuote.border\": \"#444c56\",\n    \"textCodeBlock.background\": \"#636e7b66\",\n    \"textLink.activeForeground\": \"#539bf5\",\n    \"textLink.foreground\": \"#539bf5\",\n    \"textPreformat.foreground\": \"#768390\",\n    \"textSeparator.foreground\": \"#373e47\",\n    \"titleBar.activeBackground\": \"#22272e\",\n    \"titleBar.activeForeground\": \"#768390\",\n    \"titleBar.border\": \"#444c56\",\n    \"titleBar.inactiveBackground\": \"#1c2128\",\n    \"titleBar.inactiveForeground\": \"#768390\",\n    \"tree.indentGuidesStroke\": \"#373e47\",\n    \"welcomePage.buttonBackground\": \"#373e47\",\n    \"welcomePage.buttonHoverBackground\": \"#444c56\"\n  },\n  \"displayName\": \"GitHub Dark Dimmed\",\n  \"name\": \"github-dark-dimmed\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"string.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#768390\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\",\n        \"constant.character\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f47067\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"entity.name.constant\",\n        \"variable.other.constant\",\n        \"variable.other.enummember\",\n        \"variable.language\",\n        \"entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\",\n        \"meta.export.default\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f69d50\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function\",\n        \"meta.jsx.children\",\n        \"meta.block\",\n        \"meta.tag.attributes\",\n        \"entity.name.constant\",\n        \"meta.object.member\",\n        \"meta.embedded.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#adbac7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#dcbdfb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"support.class.component\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#8ddb8c\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#f47067\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"storage.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f47067\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.package\",\n        \"storage.modifier.import\",\n        \"storage.type.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#adbac7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"string punctuation.section.embedded source\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#96d0ff\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#f69d50\"\n      }\n    },\n    {\n      \"scope\": \"variable.other\",\n      \"settings\": {\n        \"foreground\": \"#adbac7\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": \"carriage-return\",\n      \"settings\": {\n        \"background\": \"#f47067\",\n        \"content\": \"^M\",\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#cdd9e5\"\n      }\n    },\n    {\n      \"scope\": \"message.error\",\n      \"settings\": {\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.regexp\",\n        \"string.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#96d0ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp.character-class\",\n        \"string.regexp constant.character.escape\",\n        \"string.regexp source.ruby.embedded\",\n        \"string.regexp string.regexp.arbitrary-repitition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#96d0ff\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp constant.character.escape\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#8ddb8c\"\n      }\n    },\n    {\n      \"scope\": \"support.constant\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"support.variable\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#8ddb8c\"\n      }\n    },\n    {\n      \"scope\": \"meta.module-reference\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#f69d50\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading entity.name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#8ddb8c\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#adbac7\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#adbac7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.strikethrough\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\",\n        \"meta.diff.header.from-file\",\n        \"punctuation.definition.deleted\"\n      ],\n      \"settings\": {\n        \"background\": \"#5d0f12\",\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f47067\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\",\n        \"meta.diff.header.to-file\",\n        \"punctuation.definition.inserted\"\n      ],\n      \"settings\": {\n        \"background\": \"#113417\",\n        \"foreground\": \"#8ddb8c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"punctuation.definition.changed\"\n      ],\n      \"settings\": {\n        \"background\": \"#682d0f\",\n        \"foreground\": \"#f69d50\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.ignored\",\n        \"markup.untracked\"\n      ],\n      \"settings\": {\n        \"background\": \"#6cb6ff\",\n        \"foreground\": \"#2d333b\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#dcbdfb\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.output\",\n      \"settings\": {\n        \"foreground\": \"#6cb6ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"brackethighlighter.tag\",\n        \"brackethighlighter.curly\",\n        \"brackethighlighter.round\",\n        \"brackethighlighter.square\",\n        \"brackethighlighter.angle\",\n        \"brackethighlighter.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#768390\"\n      }\n    },\n    {\n      \"scope\": \"brackethighlighter.unmatched\",\n      \"settings\": {\n        \"foreground\": \"#ff938a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.reference.link\",\n        \"string.other.link\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#96d0ff\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { githubDarkDimmed as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,mBAAmB,OAAO,MAAM,CAAC;IACnC,UAAU;QACR,4BAA4B;QAC5B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,+BAA+B;QAC/B,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,kCAAkC;QAClC,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,2BAA2B;QAC3B,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,+CAA+C;QAC/C,yBAAyB;QACzB,qBAAqB;QACrB,kCAAkC;QAClC,kCAAkC;QAClC,uCAAuC;QACvC,wCAAwC;QACxC,kCAAkC;QAClC,8BAA8B;QAC9B,wCAAwC;QACxC,oCAAoC;QACpC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,2BAA2B;QAC3B,sBAAsB;QACtB,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,mCAAmC;QACnC,sCAAsC;QACtC,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,mCAAmC;QACnC,kCAAkC;QAClC,kCAAkC;QAClC,qCAAqC;QACrC,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,8BAA8B;QAC9B,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,kCAAkC;QAClC,4BAA4B;QAC5B,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,2CAA2C;QAC3C,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,6BAA6B;QAC7B,kCAAkC;QAClC,sBAAsB;QACtB,kBAAkB;QAClB,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,yBAAyB;QACzB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,iCAAiC;QACjC,qCAAqC;QACrC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,gCAAgC;QAChC,qCAAqC;IACvC;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;gBACd,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}