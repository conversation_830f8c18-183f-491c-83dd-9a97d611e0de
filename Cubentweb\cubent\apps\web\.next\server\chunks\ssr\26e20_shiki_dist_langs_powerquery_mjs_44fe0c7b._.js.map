{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/powerquery.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PowerQuery\", \"fileTypes\": [\"pq\", \"pqm\"], \"name\": \"powerquery\", \"patterns\": [{ \"include\": \"#Noise\" }, { \"include\": \"#LiteralExpression\" }, { \"include\": \"#Keywords\" }, { \"include\": \"#ImplicitVariable\" }, { \"include\": \"#IntrinsicVariable\" }, { \"include\": \"#Operators\" }, { \"include\": \"#DotOperators\" }, { \"include\": \"#TypeName\" }, { \"include\": \"#RecordExpression\" }, { \"include\": \"#Punctuation\" }, { \"include\": \"#QuotedIdentifier\" }, { \"include\": \"#Identifier\" }], \"repository\": { \"BlockComment\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.powerquery\" }, \"DecimalNumber\": { \"match\": \"(?<![\\\\d\\\\w])(\\\\d*\\\\.\\\\d+)\\\\b\", \"name\": \"constant.numeric.decimal.powerquery\" }, \"DotOperators\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.ellipsis.powerquery\" }, \"2\": { \"name\": \"keyword.operator.list.powerquery\" } }, \"match\": \"(?<!\\\\.)(?:(\\\\.\\\\.\\\\.)|(\\\\.\\\\.))(?!\\\\.)\" }, \"EscapeSequence\": { \"begin\": \"#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.escapesequence.begin.powerquery\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.escapesequence.end.powerquery\" } }, \"name\": \"constant.character.escapesequence.powerquery\", \"patterns\": [{ \"match\": \"(#|\\\\h{4}|\\\\h{8}|cr|lf|tab)(?:,(#|\\\\h{4}|\\\\h{8}|cr|lf|tab))*\" }, { \"match\": \"[^)]\", \"name\": \"invalid.illegal.escapesequence.powerquery\" }] }, \"FloatNumber\": { \"match\": \"(\\\\d*\\\\.)?\\\\d+(e|E)(\\\\+|-)?\\\\d+\", \"name\": \"constant.numeric.float.powerquery\" }, \"HexNumber\": { \"match\": \"0(x|X)\\\\h+\", \"name\": \"constant.numeric.integer.hexadecimal.powerquery\" }, \"Identifier\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.inclusiveidentifier.powerquery\" }, \"2\": { \"name\": \"entity.name.powerquery\" } }, \"match\": \"(?:(?<![\\\\._\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}])(@?)([_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}][_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}]*(?:\\\\.[_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}][_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}])*)\\\\b)\" }, \"ImplicitVariable\": { \"match\": \"\\\\b_\\\\b\", \"name\": \"keyword.operator.implicitvariable.powerquery\" }, \"InclusiveIdentifier\": { \"captures\": { \"0\": { \"name\": \"inclusiveidentifier.powerquery\" } }, \"match\": \"@\" }, \"IntNumber\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powerquery\" } }, \"match\": \"\\\\b(\\\\d+)\\\\b\" }, \"IntrinsicVariable\": { \"captures\": { \"1\": { \"name\": \"constant.language.intrinsicvariable.powerquery\" } }, \"match\": \"(?<![\\\\d\\\\w])(#sections|#shared)\\\\b\" }, \"Keywords\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.word.logical.powerquery\" }, \"2\": { \"name\": \"keyword.control.conditional.powerquery\" }, \"3\": { \"name\": \"keyword.control.exception.powerquery\" }, \"4\": { \"name\": \"keyword.other.powerquery\" }, \"5\": { \"name\": \"keyword.powerquery\" } }, \"match\": \"\\\\b(?:(and|or|not)|(if|then|else)|(try|otherwise)|(as|each|in|is|let|meta|type|error)|(section|shared))\\\\b\" }, \"LineComment\": { \"match\": \"//.*\", \"name\": \"comment.line.double-slash.powerquery\" }, \"LiteralExpression\": { \"patterns\": [{ \"include\": \"#String\" }, { \"include\": \"#NumericConstant\" }, { \"include\": \"#LogicalConstant\" }, { \"include\": \"#NullConstant\" }, { \"include\": \"#FloatNumber\" }, { \"include\": \"#DecimalNumber\" }, { \"include\": \"#HexNumber\" }, { \"include\": \"#IntNumber\" }] }, \"LogicalConstant\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.logical.powerquery\" }, \"Noise\": { \"patterns\": [{ \"include\": \"#BlockComment\" }, { \"include\": \"#LineComment\" }, { \"include\": \"#Whitespace\" }] }, \"NullConstant\": { \"match\": \"\\\\b(null)\\\\b\", \"name\": \"constant.language.null.powerquery\" }, \"NumericConstant\": { \"captures\": { \"1\": { \"name\": \"constant.language.numeric.float.powerquery\" } }, \"match\": \"(?<![\\\\d\\\\w])(#infinity|#nan)\\\\b\" }, \"Operators\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.function.powerquery\" }, \"2\": { \"name\": \"keyword.operator.assignment-or-comparison.powerquery\" }, \"3\": { \"name\": \"keyword.operator.comparison.powerquery\" }, \"4\": { \"name\": \"keyword.operator.combination.powerquery\" }, \"5\": { \"name\": \"keyword.operator.arithmetic.powerquery\" }, \"6\": { \"name\": \"keyword.operator.sectionaccess.powerquery\" }, \"7\": { \"name\": \"keyword.operator.optional.powerquery\" } }, \"match\": \"(=>)|(=)|(<>|<|>|<=|>=)|(&)|(\\\\+|-|\\\\*|\\\\/)|(!)|(\\\\?)\" }, \"Punctuation\": { \"captures\": { \"1\": { \"name\": \"punctuation.separator.powerquery\" }, \"2\": { \"name\": \"punctuation.section.parens.begin.powerquery\" }, \"3\": { \"name\": \"punctuation.section.parens.end.powerquery\" }, \"4\": { \"name\": \"punctuation.section.braces.begin.powerquery\" }, \"5\": { \"name\": \"punctuation.section.braces.end.powerquery\" } }, \"match\": \"(,)|(\\\\()|(\\\\))|({)|(})\" }, \"QuotedIdentifier\": { \"begin\": '#\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.quotedidentifier.begin.powerquery\" } }, \"end\": '\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.quotedidentifier.end.powerquery\" } }, \"name\": \"entity.name.powerquery\", \"patterns\": [{ \"match\": '\"\"', \"name\": \"constant.character.escape.quote.powerquery\" }, { \"include\": \"#EscapeSequence\" }] }, \"RecordExpression\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin.powerquery\" } }, \"contentName\": \"meta.recordexpression.powerquery\", \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.powerquery\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, \"String\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.powerquery\" } }, \"end\": '\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powerquery\" } }, \"name\": \"string.quoted.double.powerquery\", \"patterns\": [{ \"match\": '\"\"', \"name\": \"constant.character.escape.quote.powerquery\" }, { \"include\": \"#EscapeSequence\" }] }, \"TypeName\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.powerquery\" }, \"2\": { \"name\": \"storage.type.powerquery\" } }, \"match\": \"\\\\b(?:(optional|nullable)|(action|any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|null|number|record|table|text|type))\\\\b\" }, \"Whitespace\": { \"match\": \"\\\\s+\" } }, \"scopeName\": \"source.powerquery\" });\nvar powerquery = [\n  lang\n];\n\nexport { powerquery as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,aAAa;QAAC;QAAM;KAAM;IAAE,QAAQ;IAAc,YAAY;QAAC;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAc;KAAE;IAAE,cAAc;QAAE,gBAAgB;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;QAA2B;QAAG,iBAAiB;YAAE,SAAS;YAAiC,QAAQ;QAAsC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAA0C;QAAG,kBAAkB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,QAAQ;YAAgD,YAAY;gBAAC;oBAAE,SAAS;gBAA+D;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA4C;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAmC,QAAQ;QAAoC;QAAG,aAAa;YAAE,SAAS;YAAc,QAAQ;QAAkD;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;QAA2W;QAAG,oBAAoB;YAAE,SAAS;YAAW,QAAQ;QAA+C;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAI;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;QAAe;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,SAAS;QAAsC;QAAG,YAAY;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;YAAE;YAAG,SAAS;QAA6G;QAAG,eAAe;YAAE,SAAS;YAAQ,QAAQ;QAAuC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAsB,QAAQ;QAAuC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAgB,QAAQ;QAAoC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,SAAS;QAAmC;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuD;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA0C;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;QAAwD;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,SAAS;QAA0B;QAAG,oBAAoB;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAA6C;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,eAAe;YAAoC,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAA6C;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;QAAiK;QAAG,cAAc;YAAE,SAAS;QAAO;IAAE;IAAG,aAAa;AAAoB;AAChjM,IAAI,aAAa;IACf;CACD", "ignoreList": [0], "debugId": null}}]}