module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/wenyan.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>wenyan)
});
const lang = Object.freeze({
    "displayName": "Wenyan",
    "name": "wenyan",
    "patterns": [
        {
            "include": "#keywords"
        },
        {
            "include": "#constants"
        },
        {
            "include": "#operators"
        },
        {
            "include": "#symbols"
        },
        {
            "include": "#expression"
        },
        {
            "include": "#comment-blocks"
        },
        {
            "include": "#comment-lines"
        }
    ],
    "repository": {
        "comment-blocks": {
            "begin": "(\u6CE8\u66F0|\u758F\u66F0|\u6279\u66F0)\u3002?(\u300C\u300C|\u300E)",
            "end": "(\u300D\u300D|\u300F)",
            "name": "comment.block",
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character"
                }
            ]
        },
        "comment-lines": {
            "begin": "\u6CE8\u66F0|\u758F\u66F0|\u6279\u66F0",
            "end": "$",
            "name": "comment.line",
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character"
                }
            ]
        },
        "constants": {
            "patterns": [
                {
                    "match": "\u8CA0|\xB7|\u53C8|\u96F6|\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D|\u5341|\u767E|\u5343|\u842C|\u5104|\u5146|\u4EAC|\u5793|\u79ED|\u7A70|\u6E9D|\u6F97|\u6B63|\u8F09|\u6975|\u5206|\u91D0|\u6BEB|\u7D72|\u5FFD|\u5FAE|\u7E96|\u6C99|\u5875|\u57C3|\u6E3A|\u6F20",
                    "name": "constant.numeric"
                },
                {
                    "match": "\u5176|\u9670|\u967D",
                    "name": "constant.language"
                },
                {
                    "begin": "\u300C\u300C|\u300E",
                    "end": "\u300D\u300D|\u300F",
                    "name": "string.quoted",
                    "patterns": [
                        {
                            "match": "\\\\.",
                            "name": "constant.character"
                        }
                    ]
                }
            ]
        },
        "expression": {
            "patterns": [
                {
                    "include": "#variables"
                }
            ]
        },
        "keywords": {
            "patterns": [
                {
                    "match": "\u6578|\u5217|\u8A00|\u8853|\u723B|\u7269|\u5143",
                    "name": "storage.type"
                },
                {
                    "match": "\u4E43\u884C\u662F\u8853\u66F0|\u82E5\u5176\u4E0D\u7136\u8005|\u4E43\u6B78\u7A7A\u7121|\u6B32\u884C\u662F\u8853|\u4E43\u6B62\u662F\u904D|\u82E5\u5176\u7136\u8005|\u5176\u7269\u5982\u662F|\u4E43\u5F97\u77E3|\u4E4B\u8853\u4E5F|\u5FC5\u5148\u5F97|\u662F\u8853\u66F0|\u6046\u70BA\u662F|\u4E4B\u7269\u4E5F|\u4E43\u5F97|\u662F\u8B02|\u4E91\u4E91|\u4E2D\u4E4B|\u70BA\u662F|\u4E43\u6B62|\u82E5\u975E|\u6216\u82E5|\u4E4B\u9577|\u5176\u9918",
                    "name": "keyword.control"
                },
                {
                    "match": "\u6216\u4E91|\u84CB\u8B02",
                    "name": "keyword.control"
                },
                {
                    "match": "\u4E2D\u6709\u967D\u4E4E|\u4E2D\u7121\u9670\u4E4E|\u6240\u9918\u5E7E\u4F55|\u4E0D\u7B49\u65BC|\u4E0D\u5927\u65BC|\u4E0D\u5C0F\u65BC|\u7B49\u65BC|\u5927\u65BC|\u5C0F\u65BC|\u52A0|\u6E1B|\u4E58|\u9664|\u8B8A|\u4EE5|\u65BC",
                    "name": "keyword.operator"
                },
                {
                    "match": "\u4E0D\u77E5\u4F55\u798D\u6B5F|\u4E0D\u5FA9\u5B58\u77E3|\u59D1\u5984\u884C\u6B64|\u5982\u4E8B\u4E0D\u8AE7|\u540D\u4E4B\u66F0|\u543E\u5617\u89C0|\u4E4B\u798D\u6B5F|\u4E43\u4F5C\u7F77|\u543E\u6709|\u4ECA\u6709|\u7269\u4E4B|\u66F8\u4E4B|\u4EE5\u65BD|\u6614\u4E4B|\u662F\u77E3|\u4E4B\u66F8|\u65B9\u609F|\u4E4B\u7FA9|\u55DA\u547C|\u4E4B\u798D|\u6709|\u65BD|\u66F0|\u566B|\u53D6|\u4ECA|\u592B|\u4E2D|\u8C48",
                    "name": "keyword.other"
                },
                {
                    "match": "\u4E5F|\u51E1|\u904D|\u82E5|\u8005|\u4E4B|\u5145|\u929C",
                    "name": "keyword.control"
                }
            ]
        },
        "symbols": {
            "patterns": [
                {
                    "match": "\u3002|\u3001",
                    "name": "punctuation.separator"
                }
            ]
        },
        "variables": {
            "begin": "\u300C",
            "end": "\u300D",
            "name": "variable.other",
            "patterns": [
                {
                    "match": "\\\\.",
                    "name": "constant.character"
                }
            ]
        }
    },
    "scopeName": "source.wenyan",
    "aliases": [
        "\u6587\u8A00"
    ]
});
var wenyan = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_wenyan_mjs_e9eb80b1._.js.map