{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/solarized-light.mjs"], "sourcesContent": ["var solarizedLight = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#DDD6C1\",\n    \"activityBar.foreground\": \"#584c27\",\n    \"activityBarBadge.background\": \"#B58900\",\n    \"badge.background\": \"#B58900AA\",\n    \"button.background\": \"#AC9D57\",\n    \"debugExceptionWidget.background\": \"#DDD6C1\",\n    \"debugExceptionWidget.border\": \"#AB395B\",\n    \"debugToolBar.background\": \"#DDD6C1\",\n    \"dropdown.background\": \"#EEE8D5\",\n    \"dropdown.border\": \"#D3AF86\",\n    \"editor.background\": \"#FDF6E3\",\n    \"editor.foreground\": \"#657B83\",\n    \"editor.lineHighlightBackground\": \"#EEE8D5\",\n    \"editor.selectionBackground\": \"#EEE8D5\",\n    \"editorCursor.foreground\": \"#657B83\",\n    \"editorGroup.border\": \"#DDD6C1\",\n    \"editorGroup.dropBackground\": \"#DDD6C1AA\",\n    \"editorGroupHeader.tabsBackground\": \"#D9D2C2\",\n    \"editorHoverWidget.background\": \"#CCC4B0\",\n    \"editorIndentGuide.activeBackground\": \"#081E2580\",\n    \"editorIndentGuide.background\": \"#586E7580\",\n    \"editorLineNumber.activeForeground\": \"#567983\",\n    \"editorWhitespace.foreground\": \"#586E7580\",\n    \"editorWidget.background\": \"#EEE8D5\",\n    \"extensionButton.prominentBackground\": \"#b58900\",\n    \"extensionButton.prominentHoverBackground\": \"#584c27aa\",\n    \"focusBorder\": \"#b49471\",\n    \"input.background\": \"#DDD6C1\",\n    \"input.foreground\": \"#586E75\",\n    \"input.placeholderForeground\": \"#586E75AA\",\n    \"inputOption.activeBorder\": \"#D3AF86\",\n    \"list.activeSelectionBackground\": \"#DFCA88\",\n    \"list.activeSelectionForeground\": \"#6C6C6C\",\n    \"list.highlightForeground\": \"#B58900\",\n    \"list.hoverBackground\": \"#DFCA8844\",\n    \"list.inactiveSelectionBackground\": \"#D1CBB8\",\n    \"minimap.selectionHighlight\": \"#EEE8D5\",\n    \"notebook.cellEditorBackground\": \"#F7F0E0\",\n    \"panel.border\": \"#DDD6C1\",\n    \"peekView.border\": \"#B58900\",\n    \"peekViewEditor.background\": \"#FFFBF2\",\n    \"peekViewEditor.matchHighlightBackground\": \"#7744AA40\",\n    \"peekViewResult.background\": \"#EEE8D5\",\n    \"peekViewTitle.background\": \"#EEE8D5\",\n    \"pickerGroup.border\": \"#2AA19899\",\n    \"pickerGroup.foreground\": \"#2AA19899\",\n    \"ports.iconRunningProcessForeground\": \"#2AA19899\",\n    \"progressBar.background\": \"#B58900\",\n    \"quickInputList.focusBackground\": \"#DFCA8866\",\n    \"selection.background\": \"#878b9180\",\n    \"sideBar.background\": \"#EEE8D5\",\n    \"sideBarTitle.foreground\": \"#586E75\",\n    \"statusBar.background\": \"#EEE8D5\",\n    \"statusBar.debuggingBackground\": \"#EEE8D5\",\n    \"statusBar.foreground\": \"#586E75\",\n    \"statusBar.noFolderBackground\": \"#EEE8D5\",\n    \"statusBarItem.prominentBackground\": \"#DDD6C1\",\n    \"statusBarItem.prominentHoverBackground\": \"#DDD6C199\",\n    \"statusBarItem.remoteBackground\": \"#AC9D57\",\n    \"tab.activeBackground\": \"#FDF6E3\",\n    \"tab.activeModifiedBorder\": \"#cb4b16\",\n    \"tab.border\": \"#DDD6C1\",\n    \"tab.inactiveBackground\": \"#D3CBB7\",\n    \"tab.inactiveForeground\": \"#586E75\",\n    \"tab.lastPinnedBorder\": \"#FDF6E3\",\n    \"terminal.ansiBlack\": \"#073642\",\n    \"terminal.ansiBlue\": \"#268bd2\",\n    \"terminal.ansiBrightBlack\": \"#002b36\",\n    \"terminal.ansiBrightBlue\": \"#839496\",\n    \"terminal.ansiBrightCyan\": \"#93a1a1\",\n    \"terminal.ansiBrightGreen\": \"#586e75\",\n    \"terminal.ansiBrightMagenta\": \"#6c71c4\",\n    \"terminal.ansiBrightRed\": \"#cb4b16\",\n    \"terminal.ansiBrightWhite\": \"#fdf6e3\",\n    \"terminal.ansiBrightYellow\": \"#657b83\",\n    \"terminal.ansiCyan\": \"#2aa198\",\n    \"terminal.ansiGreen\": \"#859900\",\n    \"terminal.ansiMagenta\": \"#d33682\",\n    \"terminal.ansiRed\": \"#dc322f\",\n    \"terminal.ansiWhite\": \"#eee8d5\",\n    \"terminal.ansiYellow\": \"#b58900\",\n    \"terminal.background\": \"#FDF6E3\",\n    \"titleBar.activeBackground\": \"#EEE8D5\",\n    \"walkThrough.embeddedEditorBackground\": \"#00000014\"\n  },\n  \"displayName\": \"Solarized Light\",\n  \"name\": \"solarized-light\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#657B83\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded\",\n        \"source.groovy.embedded\",\n        \"string meta.image.inline.markdown\",\n        \"variable.legacy.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#657B83\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#93A1A1\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#2AA198\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#D33682\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.language\",\n        \"variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#586E75\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"entity.name.type\",\n        \"entity.name.namespace\",\n        \"entity.name.scope-resolution\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.section.embedded.begin\",\n        \"punctuation.section.embedded.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"meta.preprocessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B58900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.construct\",\n        \"keyword.other.new\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character\",\n        \"constant.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"foreground\": \"#6C71C4\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter\",\n      \"settings\": {}\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#93A1A1\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#93A1A1\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.continuation\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\",\n        \"support.variable\"\n      ],\n      \"settings\": {}\n    },\n    {\n      \"scope\": [\n        \"support.type\",\n        \"support.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"support.type.exception\",\n      \"settings\": {\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"support.other.variable\",\n      \"settings\": {}\n    },\n    {\n      \"scope\": \"invalid\",\n      \"settings\": {\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#DC322F\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#CB4B16\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#859900\"\n      }\n    },\n    {\n      \"scope\": \"markup.list\",\n      \"settings\": {\n        \"foreground\": \"#B58900\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D33682\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#2AA198\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#268BD2\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.setext\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#268BD2\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { solarizedLight as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,iBAAiB,OAAO,MAAM,CAAC;IACjC,UAAU;QACR,0BAA0B;QAC1B,0BAA0B;QAC1B,+BAA+B;QAC/B,oBAAoB;QACpB,qBAAqB;QACrB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,mBAAmB;QACnB,qBAAqB;QACrB,qBAAqB;QACrB,kCAAkC;QAClC,8BAA8B;QAC9B,2BAA2B;QAC3B,sBAAsB;QACtB,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,qCAAqC;QACrC,+BAA+B;QAC/B,2BAA2B;QAC3B,uCAAuC;QACvC,4CAA4C;QAC5C,eAAe;QACf,oBAAoB;QACpB,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,kCAAkC;QAClC,kCAAkC;QAClC,4BAA4B;QAC5B,wBAAwB;QACxB,oCAAoC;QACpC,8BAA8B;QAC9B,iCAAiC;QACjC,gBAAgB;QAChB,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,6BAA6B;QAC7B,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,0BAA0B;QAC1B,kCAAkC;QAClC,wBAAwB;QACxB,sBAAsB;QACtB,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,wBAAwB;QACxB,4BAA4B;QAC5B,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,wCAAwC;IAC1C;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,eAAe;QACb;YACE,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY,CAAC;QACf;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY,CAAC;QACf;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY,CAAC;QACf;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}