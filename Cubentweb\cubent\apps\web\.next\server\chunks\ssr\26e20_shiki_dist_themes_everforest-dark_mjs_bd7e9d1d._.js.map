{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/everforest-dark.mjs"], "sourcesContent": ["var everforestDark = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#a7c080d0\",\n    \"activityBar.activeFocusBorder\": \"#a7c080\",\n    \"activityBar.background\": \"#2d353b\",\n    \"activityBar.border\": \"#2d353b\",\n    \"activityBar.dropBackground\": \"#2d353b\",\n    \"activityBar.foreground\": \"#d3c6aa\",\n    \"activityBar.inactiveForeground\": \"#859289\",\n    \"activityBarBadge.background\": \"#a7c080\",\n    \"activityBarBadge.foreground\": \"#2d353b\",\n    \"badge.background\": \"#a7c080\",\n    \"badge.foreground\": \"#2d353b\",\n    \"breadcrumb.activeSelectionForeground\": \"#d3c6aa\",\n    \"breadcrumb.focusForeground\": \"#d3c6aa\",\n    \"breadcrumb.foreground\": \"#859289\",\n    \"button.background\": \"#a7c080\",\n    \"button.foreground\": \"#2d353b\",\n    \"button.hoverBackground\": \"#a7c080d0\",\n    \"button.secondaryBackground\": \"#3d484d\",\n    \"button.secondaryForeground\": \"#d3c6aa\",\n    \"button.secondaryHoverBackground\": \"#475258\",\n    \"charts.blue\": \"#7fbbb3\",\n    \"charts.foreground\": \"#d3c6aa\",\n    \"charts.green\": \"#a7c080\",\n    \"charts.orange\": \"#e69875\",\n    \"charts.purple\": \"#d699b6\",\n    \"charts.red\": \"#e67e80\",\n    \"charts.yellow\": \"#dbbc7f\",\n    \"checkbox.background\": \"#2d353b\",\n    \"checkbox.border\": \"#4f585e\",\n    \"checkbox.foreground\": \"#e69875\",\n    \"debugConsole.errorForeground\": \"#e67e80\",\n    \"debugConsole.infoForeground\": \"#a7c080\",\n    \"debugConsole.sourceForeground\": \"#d699b6\",\n    \"debugConsole.warningForeground\": \"#dbbc7f\",\n    \"debugConsoleInputIcon.foreground\": \"#83c092\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#7fbbb3\",\n    \"debugIcon.breakpointDisabledForeground\": \"#da6362\",\n    \"debugIcon.breakpointForeground\": \"#e67e80\",\n    \"debugIcon.breakpointStackframeForeground\": \"#e67e80\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#9aa79d\",\n    \"debugIcon.continueForeground\": \"#7fbbb3\",\n    \"debugIcon.disconnectForeground\": \"#d699b6\",\n    \"debugIcon.pauseForeground\": \"#dbbc7f\",\n    \"debugIcon.restartForeground\": \"#83c092\",\n    \"debugIcon.startForeground\": \"#83c092\",\n    \"debugIcon.stepBackForeground\": \"#7fbbb3\",\n    \"debugIcon.stepIntoForeground\": \"#7fbbb3\",\n    \"debugIcon.stepOutForeground\": \"#7fbbb3\",\n    \"debugIcon.stepOverForeground\": \"#7fbbb3\",\n    \"debugIcon.stopForeground\": \"#e67e80\",\n    \"debugTokenExpression.boolean\": \"#d699b6\",\n    \"debugTokenExpression.error\": \"#e67e80\",\n    \"debugTokenExpression.name\": \"#7fbbb3\",\n    \"debugTokenExpression.number\": \"#d699b6\",\n    \"debugTokenExpression.string\": \"#dbbc7f\",\n    \"debugTokenExpression.value\": \"#a7c080\",\n    \"debugToolBar.background\": \"#2d353b\",\n    \"descriptionForeground\": \"#859289\",\n    \"diffEditor.diagonalFill\": \"#4f585e\",\n    \"diffEditor.insertedTextBackground\": \"#569d7930\",\n    \"diffEditor.removedTextBackground\": \"#da636230\",\n    \"dropdown.background\": \"#2d353b\",\n    \"dropdown.border\": \"#4f585e\",\n    \"dropdown.foreground\": \"#9aa79d\",\n    \"editor.background\": \"#2d353b\",\n    \"editor.findMatchBackground\": \"#d77f4840\",\n    \"editor.findMatchHighlightBackground\": \"#899c4040\",\n    \"editor.findRangeHighlightBackground\": \"#47525860\",\n    \"editor.foldBackground\": \"#4f585e80\",\n    \"editor.foreground\": \"#d3c6aa\",\n    \"editor.hoverHighlightBackground\": \"#475258b0\",\n    \"editor.inactiveSelectionBackground\": \"#47525860\",\n    \"editor.lineHighlightBackground\": \"#3d484d90\",\n    \"editor.lineHighlightBorder\": \"#4f585e00\",\n    \"editor.rangeHighlightBackground\": \"#3d484d80\",\n    \"editor.selectionBackground\": \"#475258c0\",\n    \"editor.selectionHighlightBackground\": \"#47525860\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#899c4040\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#2d353b\",\n    \"editor.snippetTabstopHighlightBackground\": \"#3d484d\",\n    \"editor.symbolHighlightBackground\": \"#5a93a240\",\n    \"editor.wordHighlightBackground\": \"#47525858\",\n    \"editor.wordHighlightStrongBackground\": \"#475258b0\",\n    \"editorBracketHighlight.foreground1\": \"#e67e80\",\n    \"editorBracketHighlight.foreground2\": \"#dbbc7f\",\n    \"editorBracketHighlight.foreground3\": \"#a7c080\",\n    \"editorBracketHighlight.foreground4\": \"#7fbbb3\",\n    \"editorBracketHighlight.foreground5\": \"#e69875\",\n    \"editorBracketHighlight.foreground6\": \"#d699b6\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#859289\",\n    \"editorBracketMatch.background\": \"#4f585e\",\n    \"editorBracketMatch.border\": \"#2d353b00\",\n    \"editorCodeLens.foreground\": \"#7f897da0\",\n    \"editorCursor.foreground\": \"#d3c6aa\",\n    \"editorError.background\": \"#da636200\",\n    \"editorError.foreground\": \"#da6362\",\n    \"editorGhostText.background\": \"#2d353b00\",\n    \"editorGhostText.foreground\": \"#7f897da0\",\n    \"editorGroup.border\": \"#21272b\",\n    \"editorGroup.dropBackground\": \"#4f585e60\",\n    \"editorGroupHeader.noTabsBackground\": \"#2d353b\",\n    \"editorGroupHeader.tabsBackground\": \"#2d353b\",\n    \"editorGutter.addedBackground\": \"#899c40a0\",\n    \"editorGutter.background\": \"#2d353b00\",\n    \"editorGutter.commentRangeForeground\": \"#7f897d\",\n    \"editorGutter.deletedBackground\": \"#da6362a0\",\n    \"editorGutter.modifiedBackground\": \"#5a93a2a0\",\n    \"editorHint.foreground\": \"#b87b9d\",\n    \"editorHoverWidget.background\": \"#343f44\",\n    \"editorHoverWidget.border\": \"#475258\",\n    \"editorIndentGuide.activeBackground\": \"#9aa79d50\",\n    \"editorIndentGuide.background\": \"#9aa79d20\",\n    \"editorInfo.background\": \"#5a93a200\",\n    \"editorInfo.foreground\": \"#5a93a2\",\n    \"editorInlayHint.background\": \"#2d353b00\",\n    \"editorInlayHint.foreground\": \"#7f897da0\",\n    \"editorInlayHint.parameterBackground\": \"#2d353b00\",\n    \"editorInlayHint.parameterForeground\": \"#7f897da0\",\n    \"editorInlayHint.typeBackground\": \"#2d353b00\",\n    \"editorInlayHint.typeForeground\": \"#7f897da0\",\n    \"editorLightBulb.foreground\": \"#dbbc7f\",\n    \"editorLightBulbAutoFix.foreground\": \"#83c092\",\n    \"editorLineNumber.activeForeground\": \"#9aa79de0\",\n    \"editorLineNumber.foreground\": \"#7f897da0\",\n    \"editorLink.activeForeground\": \"#a7c080\",\n    \"editorMarkerNavigation.background\": \"#343f44\",\n    \"editorMarkerNavigationError.background\": \"#da636280\",\n    \"editorMarkerNavigationInfo.background\": \"#5a93a280\",\n    \"editorMarkerNavigationWarning.background\": \"#bf983d80\",\n    \"editorOverviewRuler.addedForeground\": \"#899c40a0\",\n    \"editorOverviewRuler.border\": \"#2d353b00\",\n    \"editorOverviewRuler.commonContentForeground\": \"#859289\",\n    \"editorOverviewRuler.currentContentForeground\": \"#5a93a2\",\n    \"editorOverviewRuler.deletedForeground\": \"#da6362a0\",\n    \"editorOverviewRuler.errorForeground\": \"#e67e80\",\n    \"editorOverviewRuler.findMatchForeground\": \"#569d79\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#569d79\",\n    \"editorOverviewRuler.infoForeground\": \"#d699b6\",\n    \"editorOverviewRuler.modifiedForeground\": \"#5a93a2a0\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#569d79\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#569d79\",\n    \"editorOverviewRuler.warningForeground\": \"#dbbc7f\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#4f585e\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#4f585e\",\n    \"editorRuler.foreground\": \"#475258a0\",\n    \"editorSuggestWidget.background\": \"#3d484d\",\n    \"editorSuggestWidget.border\": \"#3d484d\",\n    \"editorSuggestWidget.foreground\": \"#d3c6aa\",\n    \"editorSuggestWidget.highlightForeground\": \"#a7c080\",\n    \"editorSuggestWidget.selectedBackground\": \"#475258\",\n    \"editorUnnecessaryCode.border\": \"#2d353b\",\n    \"editorUnnecessaryCode.opacity\": \"#00000080\",\n    \"editorWarning.background\": \"#bf983d00\",\n    \"editorWarning.foreground\": \"#bf983d\",\n    \"editorWhitespace.foreground\": \"#475258\",\n    \"editorWidget.background\": \"#2d353b\",\n    \"editorWidget.border\": \"#4f585e\",\n    \"editorWidget.foreground\": \"#d3c6aa\",\n    \"errorForeground\": \"#e67e80\",\n    \"extensionBadge.remoteBackground\": \"#a7c080\",\n    \"extensionBadge.remoteForeground\": \"#2d353b\",\n    \"extensionButton.prominentBackground\": \"#a7c080\",\n    \"extensionButton.prominentForeground\": \"#2d353b\",\n    \"extensionButton.prominentHoverBackground\": \"#a7c080d0\",\n    \"extensionIcon.preReleaseForeground\": \"#e69875\",\n    \"extensionIcon.starForeground\": \"#83c092\",\n    \"extensionIcon.verifiedForeground\": \"#a7c080\",\n    \"focusBorder\": \"#2d353b00\",\n    \"foreground\": \"#9aa79d\",\n    \"gitDecoration.addedResourceForeground\": \"#a7c080a0\",\n    \"gitDecoration.conflictingResourceForeground\": \"#d699b6a0\",\n    \"gitDecoration.deletedResourceForeground\": \"#e67e80a0\",\n    \"gitDecoration.ignoredResourceForeground\": \"#4f585e\",\n    \"gitDecoration.modifiedResourceForeground\": \"#7fbbb3a0\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#83c092a0\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#83c092a0\",\n    \"gitDecoration.submoduleResourceForeground\": \"#e69875a0\",\n    \"gitDecoration.untrackedResourceForeground\": \"#dbbc7fa0\",\n    \"gitlens.closedPullRequestIconColor\": \"#e67e80\",\n    \"gitlens.decorations.addedForegroundColor\": \"#a7c080\",\n    \"gitlens.decorations.branchAheadForegroundColor\": \"#83c092\",\n    \"gitlens.decorations.branchBehindForegroundColor\": \"#e69875\",\n    \"gitlens.decorations.branchDivergedForegroundColor\": \"#dbbc7f\",\n    \"gitlens.decorations.branchMissingUpstreamForegroundColor\": \"#e67e80\",\n    \"gitlens.decorations.branchUnpublishedForegroundColor\": \"#7fbbb3\",\n    \"gitlens.decorations.branchUpToDateForegroundColor\": \"#d3c6aa\",\n    \"gitlens.decorations.copiedForegroundColor\": \"#d699b6\",\n    \"gitlens.decorations.deletedForegroundColor\": \"#e67e80\",\n    \"gitlens.decorations.ignoredForegroundColor\": \"#9aa79d\",\n    \"gitlens.decorations.modifiedForegroundColor\": \"#7fbbb3\",\n    \"gitlens.decorations.renamedForegroundColor\": \"#d699b6\",\n    \"gitlens.decorations.untrackedForegroundColor\": \"#dbbc7f\",\n    \"gitlens.gutterBackgroundColor\": \"#2d353b\",\n    \"gitlens.gutterForegroundColor\": \"#d3c6aa\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#7fbbb3\",\n    \"gitlens.lineHighlightBackgroundColor\": \"#343f44\",\n    \"gitlens.lineHighlightOverviewRulerColor\": \"#a7c080\",\n    \"gitlens.mergedPullRequestIconColor\": \"#d699b6\",\n    \"gitlens.openPullRequestIconColor\": \"#83c092\",\n    \"gitlens.trailingLineForegroundColor\": \"#859289\",\n    \"gitlens.unpublishedCommitIconColor\": \"#dbbc7f\",\n    \"gitlens.unpulledChangesIconColor\": \"#e69875\",\n    \"gitlens.unpushlishedChangesIconColor\": \"#7fbbb3\",\n    \"icon.foreground\": \"#83c092\",\n    \"imagePreview.border\": \"#2d353b\",\n    \"input.background\": \"#2d353b00\",\n    \"input.border\": \"#4f585e\",\n    \"input.foreground\": \"#d3c6aa\",\n    \"input.placeholderForeground\": \"#7f897d\",\n    \"inputOption.activeBorder\": \"#83c092\",\n    \"inputValidation.errorBackground\": \"#da6362\",\n    \"inputValidation.errorBorder\": \"#e67e80\",\n    \"inputValidation.errorForeground\": \"#d3c6aa\",\n    \"inputValidation.infoBackground\": \"#5a93a2\",\n    \"inputValidation.infoBorder\": \"#7fbbb3\",\n    \"inputValidation.infoForeground\": \"#d3c6aa\",\n    \"inputValidation.warningBackground\": \"#bf983d\",\n    \"inputValidation.warningBorder\": \"#dbbc7f\",\n    \"inputValidation.warningForeground\": \"#d3c6aa\",\n    \"issues.closed\": \"#e67e80\",\n    \"issues.open\": \"#83c092\",\n    \"keybindingLabel.background\": \"#2d353b00\",\n    \"keybindingLabel.border\": \"#272e33\",\n    \"keybindingLabel.bottomBorder\": \"#21272b\",\n    \"keybindingLabel.foreground\": \"#d3c6aa\",\n    \"keybindingTable.headerBackground\": \"#3d484d\",\n    \"keybindingTable.rowsBackground\": \"#343f44\",\n    \"list.activeSelectionBackground\": \"#47525880\",\n    \"list.activeSelectionForeground\": \"#d3c6aa\",\n    \"list.dropBackground\": \"#343f4480\",\n    \"list.errorForeground\": \"#e67e80\",\n    \"list.focusBackground\": \"#47525880\",\n    \"list.focusForeground\": \"#d3c6aa\",\n    \"list.highlightForeground\": \"#a7c080\",\n    \"list.hoverBackground\": \"#2d353b00\",\n    \"list.hoverForeground\": \"#d3c6aa\",\n    \"list.inactiveFocusBackground\": \"#47525860\",\n    \"list.inactiveSelectionBackground\": \"#47525880\",\n    \"list.inactiveSelectionForeground\": \"#9aa79d\",\n    \"list.invalidItemForeground\": \"#da6362\",\n    \"list.warningForeground\": \"#dbbc7f\",\n    \"menu.background\": \"#2d353b\",\n    \"menu.foreground\": \"#9aa79d\",\n    \"menu.selectionBackground\": \"#343f44\",\n    \"menu.selectionForeground\": \"#d3c6aa\",\n    \"menubar.selectionBackground\": \"#2d353b\",\n    \"menubar.selectionBorder\": \"#2d353b\",\n    \"merge.border\": \"#2d353b00\",\n    \"merge.currentContentBackground\": \"#5a93a240\",\n    \"merge.currentHeaderBackground\": \"#5a93a280\",\n    \"merge.incomingContentBackground\": \"#569d7940\",\n    \"merge.incomingHeaderBackground\": \"#569d7980\",\n    \"minimap.errorHighlight\": \"#da636280\",\n    \"minimap.findMatchHighlight\": \"#569d7960\",\n    \"minimap.selectionHighlight\": \"#4f585ef0\",\n    \"minimap.warningHighlight\": \"#bf983d80\",\n    \"minimapGutter.addedBackground\": \"#899c40a0\",\n    \"minimapGutter.deletedBackground\": \"#da6362a0\",\n    \"minimapGutter.modifiedBackground\": \"#5a93a2a0\",\n    \"notebook.cellBorderColor\": \"#4f585e\",\n    \"notebook.cellHoverBackground\": \"#2d353b\",\n    \"notebook.cellStatusBarItemHoverBackground\": \"#343f44\",\n    \"notebook.cellToolbarSeparator\": \"#4f585e\",\n    \"notebook.focusedCellBackground\": \"#2d353b\",\n    \"notebook.focusedCellBorder\": \"#4f585e\",\n    \"notebook.focusedEditorBorder\": \"#4f585e\",\n    \"notebook.focusedRowBorder\": \"#4f585e\",\n    \"notebook.inactiveFocusedCellBorder\": \"#4f585e\",\n    \"notebook.outputContainerBackgroundColor\": \"#272e33\",\n    \"notebook.selectedCellBorder\": \"#4f585e\",\n    \"notebookStatusErrorIcon.foreground\": \"#e67e80\",\n    \"notebookStatusRunningIcon.foreground\": \"#7fbbb3\",\n    \"notebookStatusSuccessIcon.foreground\": \"#a7c080\",\n    \"notificationCenterHeader.background\": \"#3d484d\",\n    \"notificationCenterHeader.foreground\": \"#d3c6aa\",\n    \"notificationLink.foreground\": \"#a7c080\",\n    \"notifications.background\": \"#2d353b\",\n    \"notifications.foreground\": \"#d3c6aa\",\n    \"notificationsErrorIcon.foreground\": \"#e67e80\",\n    \"notificationsInfoIcon.foreground\": \"#7fbbb3\",\n    \"notificationsWarningIcon.foreground\": \"#dbbc7f\",\n    \"panel.background\": \"#2d353b\",\n    \"panel.border\": \"#2d353b\",\n    \"panelInput.border\": \"#4f585e\",\n    \"panelSection.border\": \"#21272b\",\n    \"panelSectionHeader.background\": \"#2d353b\",\n    \"panelTitle.activeBorder\": \"#a7c080d0\",\n    \"panelTitle.activeForeground\": \"#d3c6aa\",\n    \"panelTitle.inactiveForeground\": \"#859289\",\n    \"peekView.border\": \"#475258\",\n    \"peekViewEditor.background\": \"#343f44\",\n    \"peekViewEditor.matchHighlightBackground\": \"#bf983d50\",\n    \"peekViewEditorGutter.background\": \"#343f44\",\n    \"peekViewResult.background\": \"#343f44\",\n    \"peekViewResult.fileForeground\": \"#d3c6aa\",\n    \"peekViewResult.lineForeground\": \"#9aa79d\",\n    \"peekViewResult.matchHighlightBackground\": \"#bf983d50\",\n    \"peekViewResult.selectionBackground\": \"#569d7950\",\n    \"peekViewResult.selectionForeground\": \"#d3c6aa\",\n    \"peekViewTitle.background\": \"#475258\",\n    \"peekViewTitleDescription.foreground\": \"#d3c6aa\",\n    \"peekViewTitleLabel.foreground\": \"#a7c080\",\n    \"pickerGroup.border\": \"#a7c0801a\",\n    \"pickerGroup.foreground\": \"#d3c6aa\",\n    \"ports.iconRunningProcessForeground\": \"#e69875\",\n    \"problemsErrorIcon.foreground\": \"#e67e80\",\n    \"problemsInfoIcon.foreground\": \"#7fbbb3\",\n    \"problemsWarningIcon.foreground\": \"#dbbc7f\",\n    \"progressBar.background\": \"#a7c080\",\n    \"quickInputTitle.background\": \"#343f44\",\n    \"rust_analyzer.inlayHints.background\": \"#2d353b00\",\n    \"rust_analyzer.inlayHints.foreground\": \"#7f897da0\",\n    \"rust_analyzer.syntaxTreeBorder\": \"#e67e80\",\n    \"sash.hoverBorder\": \"#475258\",\n    \"scrollbar.shadow\": \"#00000070\",\n    \"scrollbarSlider.activeBackground\": \"#9aa79d\",\n    \"scrollbarSlider.background\": \"#4f585e80\",\n    \"scrollbarSlider.hoverBackground\": \"#4f585e\",\n    \"selection.background\": \"#475258e0\",\n    \"settings.checkboxBackground\": \"#2d353b\",\n    \"settings.checkboxBorder\": \"#4f585e\",\n    \"settings.checkboxForeground\": \"#e69875\",\n    \"settings.dropdownBackground\": \"#2d353b\",\n    \"settings.dropdownBorder\": \"#4f585e\",\n    \"settings.dropdownForeground\": \"#83c092\",\n    \"settings.focusedRowBackground\": \"#343f44\",\n    \"settings.headerForeground\": \"#9aa79d\",\n    \"settings.modifiedItemIndicator\": \"#7f897d\",\n    \"settings.numberInputBackground\": \"#2d353b\",\n    \"settings.numberInputBorder\": \"#4f585e\",\n    \"settings.numberInputForeground\": \"#d699b6\",\n    \"settings.rowHoverBackground\": \"#343f44\",\n    \"settings.textInputBackground\": \"#2d353b\",\n    \"settings.textInputBorder\": \"#4f585e\",\n    \"settings.textInputForeground\": \"#7fbbb3\",\n    \"sideBar.background\": \"#2d353b\",\n    \"sideBar.foreground\": \"#859289\",\n    \"sideBarSectionHeader.background\": \"#2d353b00\",\n    \"sideBarSectionHeader.foreground\": \"#9aa79d\",\n    \"sideBarTitle.foreground\": \"#9aa79d\",\n    \"statusBar.background\": \"#2d353b\",\n    \"statusBar.border\": \"#2d353b\",\n    \"statusBar.debuggingBackground\": \"#2d353b\",\n    \"statusBar.debuggingForeground\": \"#e69875\",\n    \"statusBar.foreground\": \"#9aa79d\",\n    \"statusBar.noFolderBackground\": \"#2d353b\",\n    \"statusBar.noFolderBorder\": \"#2d353b\",\n    \"statusBar.noFolderForeground\": \"#9aa79d\",\n    \"statusBarItem.activeBackground\": \"#47525870\",\n    \"statusBarItem.errorBackground\": \"#2d353b\",\n    \"statusBarItem.errorForeground\": \"#e67e80\",\n    \"statusBarItem.hoverBackground\": \"#475258a0\",\n    \"statusBarItem.prominentBackground\": \"#2d353b\",\n    \"statusBarItem.prominentForeground\": \"#d3c6aa\",\n    \"statusBarItem.prominentHoverBackground\": \"#475258a0\",\n    \"statusBarItem.remoteBackground\": \"#2d353b\",\n    \"statusBarItem.remoteForeground\": \"#9aa79d\",\n    \"statusBarItem.warningBackground\": \"#2d353b\",\n    \"statusBarItem.warningForeground\": \"#dbbc7f\",\n    \"symbolIcon.arrayForeground\": \"#7fbbb3\",\n    \"symbolIcon.booleanForeground\": \"#d699b6\",\n    \"symbolIcon.classForeground\": \"#dbbc7f\",\n    \"symbolIcon.colorForeground\": \"#d3c6aa\",\n    \"symbolIcon.constantForeground\": \"#83c092\",\n    \"symbolIcon.constructorForeground\": \"#d699b6\",\n    \"symbolIcon.enumeratorForeground\": \"#d699b6\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#83c092\",\n    \"symbolIcon.eventForeground\": \"#dbbc7f\",\n    \"symbolIcon.fieldForeground\": \"#d3c6aa\",\n    \"symbolIcon.fileForeground\": \"#d3c6aa\",\n    \"symbolIcon.folderForeground\": \"#d3c6aa\",\n    \"symbolIcon.functionForeground\": \"#a7c080\",\n    \"symbolIcon.interfaceForeground\": \"#dbbc7f\",\n    \"symbolIcon.keyForeground\": \"#a7c080\",\n    \"symbolIcon.keywordForeground\": \"#e67e80\",\n    \"symbolIcon.methodForeground\": \"#a7c080\",\n    \"symbolIcon.moduleForeground\": \"#d699b6\",\n    \"symbolIcon.namespaceForeground\": \"#d699b6\",\n    \"symbolIcon.nullForeground\": \"#83c092\",\n    \"symbolIcon.numberForeground\": \"#d699b6\",\n    \"symbolIcon.objectForeground\": \"#d699b6\",\n    \"symbolIcon.operatorForeground\": \"#e69875\",\n    \"symbolIcon.packageForeground\": \"#d699b6\",\n    \"symbolIcon.propertyForeground\": \"#83c092\",\n    \"symbolIcon.referenceForeground\": \"#7fbbb3\",\n    \"symbolIcon.snippetForeground\": \"#d3c6aa\",\n    \"symbolIcon.stringForeground\": \"#a7c080\",\n    \"symbolIcon.structForeground\": \"#dbbc7f\",\n    \"symbolIcon.textForeground\": \"#d3c6aa\",\n    \"symbolIcon.typeParameterForeground\": \"#83c092\",\n    \"symbolIcon.unitForeground\": \"#d3c6aa\",\n    \"symbolIcon.variableForeground\": \"#7fbbb3\",\n    \"tab.activeBackground\": \"#2d353b\",\n    \"tab.activeBorder\": \"#a7c080d0\",\n    \"tab.activeForeground\": \"#d3c6aa\",\n    \"tab.border\": \"#2d353b\",\n    \"tab.hoverBackground\": \"#2d353b\",\n    \"tab.hoverForeground\": \"#d3c6aa\",\n    \"tab.inactiveBackground\": \"#2d353b\",\n    \"tab.inactiveForeground\": \"#7f897d\",\n    \"tab.lastPinnedBorder\": \"#a7c080d0\",\n    \"tab.unfocusedActiveBorder\": \"#859289\",\n    \"tab.unfocusedActiveForeground\": \"#9aa79d\",\n    \"tab.unfocusedHoverForeground\": \"#d3c6aa\",\n    \"tab.unfocusedInactiveForeground\": \"#7f897d\",\n    \"terminal.ansiBlack\": \"#343f44\",\n    \"terminal.ansiBlue\": \"#7fbbb3\",\n    \"terminal.ansiBrightBlack\": \"#859289\",\n    \"terminal.ansiBrightBlue\": \"#7fbbb3\",\n    \"terminal.ansiBrightCyan\": \"#83c092\",\n    \"terminal.ansiBrightGreen\": \"#a7c080\",\n    \"terminal.ansiBrightMagenta\": \"#d699b6\",\n    \"terminal.ansiBrightRed\": \"#e67e80\",\n    \"terminal.ansiBrightWhite\": \"#d3c6aa\",\n    \"terminal.ansiBrightYellow\": \"#dbbc7f\",\n    \"terminal.ansiCyan\": \"#83c092\",\n    \"terminal.ansiGreen\": \"#a7c080\",\n    \"terminal.ansiMagenta\": \"#d699b6\",\n    \"terminal.ansiRed\": \"#e67e80\",\n    \"terminal.ansiWhite\": \"#d3c6aa\",\n    \"terminal.ansiYellow\": \"#dbbc7f\",\n    \"terminal.foreground\": \"#d3c6aa\",\n    \"terminalCursor.foreground\": \"#d3c6aa\",\n    \"testing.iconErrored\": \"#e67e80\",\n    \"testing.iconFailed\": \"#e67e80\",\n    \"testing.iconPassed\": \"#83c092\",\n    \"testing.iconQueued\": \"#7fbbb3\",\n    \"testing.iconSkipped\": \"#d699b6\",\n    \"testing.iconUnset\": \"#dbbc7f\",\n    \"testing.runAction\": \"#83c092\",\n    \"textBlockQuote.background\": \"#272e33\",\n    \"textBlockQuote.border\": \"#475258\",\n    \"textCodeBlock.background\": \"#272e33\",\n    \"textLink.activeForeground\": \"#a7c080c0\",\n    \"textLink.foreground\": \"#a7c080\",\n    \"textPreformat.foreground\": \"#dbbc7f\",\n    \"titleBar.activeBackground\": \"#2d353b\",\n    \"titleBar.activeForeground\": \"#9aa79d\",\n    \"titleBar.border\": \"#2d353b\",\n    \"titleBar.inactiveBackground\": \"#2d353b\",\n    \"titleBar.inactiveForeground\": \"#7f897d\",\n    \"toolbar.hoverBackground\": \"#343f44\",\n    \"tree.indentGuidesStroke\": \"#7f897d\",\n    \"walkThrough.embeddedEditorBackground\": \"#272e33\",\n    \"welcomePage.buttonBackground\": \"#343f44\",\n    \"welcomePage.buttonHoverBackground\": \"#343f44a0\",\n    \"welcomePage.progress.foreground\": \"#a7c080\",\n    \"welcomePage.tileHoverBackground\": \"#343f44\",\n    \"widget.shadow\": \"#00000070\"\n  },\n  \"displayName\": \"Everforest Dark\",\n  \"name\": \"everforest-dark\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"class:python\": \"#83c092\",\n    \"class:typescript\": \"#83c092\",\n    \"class:typescriptreact\": \"#83c092\",\n    \"enum:typescript\": \"#d699b6\",\n    \"enum:typescriptreact\": \"#d699b6\",\n    \"enumMember:typescript\": \"#7fbbb3\",\n    \"enumMember:typescriptreact\": \"#7fbbb3\",\n    \"interface:typescript\": \"#83c092\",\n    \"interface:typescriptreact\": \"#83c092\",\n    \"intrinsic:python\": \"#d699b6\",\n    \"macro:rust\": \"#83c092\",\n    \"memberOperatorOverload\": \"#e69875\",\n    \"module:python\": \"#7fbbb3\",\n    \"namespace:rust\": \"#d699b6\",\n    \"namespace:typescript\": \"#d699b6\",\n    \"namespace:typescriptreact\": \"#d699b6\",\n    \"operatorOverload\": \"#e69875\",\n    \"property.defaultLibrary:javascript\": \"#d699b6\",\n    \"property.defaultLibrary:javascriptreact\": \"#d699b6\",\n    \"property.defaultLibrary:typescript\": \"#d699b6\",\n    \"property.defaultLibrary:typescriptreact\": \"#d699b6\",\n    \"selfKeyword:rust\": \"#d699b6\",\n    \"variable.defaultLibrary:javascript\": \"#d699b6\",\n    \"variable.defaultLibrary:javascriptreact\": \"#d699b6\",\n    \"variable.defaultLibrary:typescript\": \"#d699b6\",\n    \"variable.defaultLibrary:typescriptreact\": \"#d699b6\"\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": \"keyword, storage.type.function, storage.type.class, storage.type.enum, storage.type.interface, storage.type.property, keyword.operator.new, keyword.operator.expression, keyword.operator.new, keyword.operator.delete, storage.type.extends\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.debugger\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage, modifier, keyword.var, entity.name.tag, keyword.control.case, keyword.control.switch\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"string, punctuation.definition.string.end, punctuation.definition.string.begin, punctuation.definition.string.template.begin, punctuation.definition.string.template.end\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape, punctuation.quasi.element, punctuation.definition.template-expression, punctuation.section.embedded, storage.type.format, constant.other.placeholder, constant.other.placeholder, variable.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, support.function, meta.function, meta.function-call, meta.definition.method\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule, keyword.control.import, keyword.control.export, storage.type.namespace, punctuation.decorator, keyword.control.directive, keyword.preprocessor, punctuation.definition.preprocessor, punctuation.definition.directive, keyword.other.import, keyword.other.package, entity.name.type.namespace, entity.name.scope-resolution, keyword.other.using, keyword.package, keyword.import, keyword.map\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.annotation\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.label, constant.other.label\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"support.module, support.node, support.other.module, support.type.object.module, entity.name.type.module, entity.name.type.class.module, keyword.control.module\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type, support.type, entity.name.type, keyword.type\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.class, support.class, entity.name.class, entity.other.inherited-class, storage.class\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.this, variable.language.self, variable.language.super, keyword.other.this, variable.language.special, constant.language.null, constant.language.undefined, constant.language.nan\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"constant.language, support.constant\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"variable, support.variable, meta.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property, support.variable.property, variable.other.property, variable.other.object.property, variable.other.enummember, variable.other.member, meta.object-literal.key\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation, meta.brace, meta.delimiter, meta.bracket\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"heading.1.markdown, markup.heading.setext.1.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"heading.2.markdown, markup.heading.setext.2.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"heading.3.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"heading.4.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"heading.5.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"heading.6.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown, constant.other.reference.link.markdown, string.other.link.description.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link.image.markdown, markup.underline.link.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin.markdown, punctuation.definition.string.end.markdown, punctuation.definition.italic.markdown, punctuation.definition.quote.begin.markdown, punctuation.definition.metadata.markdown, punctuation.separator.key-value.markdown, punctuation.definition.constant.markdown\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator.markdown, punctuation.definition.constant.begin.markdown, punctuation.definition.constant.end.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold markup.italic, markup.italic markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"italic bold\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.markdown, punctuation.definition.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"fenced_code.block.language\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"markup.fenced_code.block.markdown, markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.restructuredtext\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.field.restructuredtext, punctuation.separator.key-value.restructuredtext, punctuation.definition.directive.restructuredtext, punctuation.definition.constant.restructuredtext, punctuation.definition.italic.restructuredtext, punctuation.definition.table.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.restructuredtext\",\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.restructuredtext, punctuation.definition.link.restructuredtext, punctuation.definition.raw.restructuredtext, punctuation.section.raw.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.footnote.link.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"support.directive.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.directive.restructuredtext, markup.raw.restructuredtext, markup.raw.inner.restructuredtext, string.other.link.title.restructuredtext\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.function.latex, punctuation.definition.function.tex, punctuation.definition.keyword.latex, constant.character.newline.tex, punctuation.definition.keyword.tex\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"support.function.be.latex\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"support.function.section.latex, keyword.control.table.cell.latex, keyword.control.table.newline.latex\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"support.class.latex, variable.parameter.latex, variable.parameter.function.latex, variable.parameter.definition.label.latex, constant.other.reference.label.latex\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.preamble.latex\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.namespace.xml\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.html, entity.name.tag.xml, entity.name.tag.localname.xml\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.html, entity.other.attribute-name.xml, entity.other.attribute-name.localname.xml\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.html, string.quoted.single.html, punctuation.definition.string.begin.html, punctuation.definition.string.end.html, punctuation.separator.key-value.html, punctuation.definition.string.begin.xml, punctuation.definition.string.end.xml, string.quoted.double.xml, string.quoted.single.xml, punctuation.definition.tag.begin.html, punctuation.definition.tag.end.html, punctuation.definition.tag.xml, meta.tag.xml, meta.tag.preprocessor.xml, meta.tag.other.html, meta.tag.block.any.html, meta.tag.inline.any.html\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.documentroot.xml, meta.tag.sgml.doctype.xml\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.proto\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.proto.syntax, string.quoted.single.proto.syntax, string.quoted.double.proto, string.quoted.single.proto\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.proto, entity.name.class.message.proto\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.css, punctuation.separator.key-value.css, punctuation.terminator.rule.css, punctuation.separator.list.comma.css\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.css\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.pseudo-class.css, entity.other.attribute-name.pseudo-element.css\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.css, string.quoted.double.css, support.constant.property-value.css, meta.property-value.css, punctuation.definition.string.begin.css, punctuation.definition.string.end.css, constant.numeric.css, support.constant.font-name.css, variable.parameter.keyframe-list.css\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"support.type.vendored.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.css, entity.other.keyframe-offset.css, punctuation.definition.keyword.css, keyword.control.at-rule.keyframes.css, meta.selector.css\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.scss, punctuation.separator.key-value.scss, punctuation.terminator.rule.scss, punctuation.separator.list.comma.scss\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule.keyframes.scss\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.interpolation.begin.bracket.curly.scss, punctuation.definition.interpolation.end.bracket.curly.scss\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin.scss, punctuation.definition.string.end.scss, string.quoted.double.scss, string.quoted.single.scss, constant.character.css.sass, meta.property-value.scss\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.at-rule.include.scss, keyword.control.at-rule.use.scss, keyword.control.at-rule.mixin.scss, keyword.control.at-rule.extend.scss, keyword.control.at-rule.import.scss\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.stylus\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.stylus\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.js\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.accessor.js, punctuation.separator.key-value.js, punctuation.separator.label.js, keyword.operator.accessor.js\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.block.tag.jsdoc\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.js, storage.type.function.arrow.js\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"JSXNested\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.jsx, entity.other.attribute-name.jsx, punctuation.definition.tag.begin.js.jsx, punctuation.definition.tag.end.js.jsx, entity.other.attribute-name.js.jsx\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.ts\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.annotation.ts, punctuation.accessor.ts, punctuation.separator.key-value.ts\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.directive.ts, entity.other.attribute-name.directive.ts\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.ts, entity.name.type.interface.ts, entity.other.inherited-class.ts, entity.name.type.alias.ts, entity.name.type.class.ts, entity.name.type.enum.ts\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.ts, storage.type.function.arrow.ts, storage.type.type.ts\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.ts\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.ts, keyword.control.export.ts, storage.type.namespace.ts\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.tsx\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.annotation.tsx, punctuation.accessor.tsx, punctuation.separator.key-value.tsx\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.directive.tsx, entity.other.attribute-name.directive.tsx, punctuation.definition.tag.begin.tsx, punctuation.definition.tag.end.tsx, entity.other.attribute-name.tsx\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.tsx, entity.name.type.interface.tsx, entity.other.inherited-class.tsx, entity.name.type.alias.tsx, entity.name.type.class.tsx, entity.name.type.enum.tsx\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module.tsx\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.tsx, keyword.control.export.tsx, storage.type.namespace.tsx\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.tsx, storage.type.function.arrow.tsx, storage.type.type.tsx, support.class.component.tsx\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.coffee\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"meta.type-signature.purescript\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.double-colon.purescript, keyword.other.arrow.purescript, keyword.other.big-arrow.purescript\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.purescript\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.purescript, string.quoted.double.purescript, punctuation.definition.string.begin.purescript, punctuation.definition.string.end.purescript, string.quoted.triple.purescript, entity.name.type.purescript\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"support.other.module.purescript\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.dot.dart\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.primitive.dart\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"support.class.dart\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.dart, string.interpolated.single.dart, string.interpolated.double.dart\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.dart\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.dart, storage.type.annotation.dart\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.pug\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.pug\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.tag.pug\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.pug, storage.type.import.include.pug\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.c, storage.modifier.array.bracket.square.c, meta.function.definition.parameters.c\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.dot-access.c, constant.character.escape.line-continuation.c\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.include.c, punctuation.definition.directive.c, keyword.control.directive.pragma.c, keyword.control.directive.line.c, keyword.control.directive.define.c, keyword.control.directive.conditional.c, keyword.control.directive.diagnostic.error.c, keyword.control.directive.undef.c, keyword.control.directive.conditional.ifdef.c, keyword.control.directive.endif.c, keyword.control.directive.conditional.ifndef.c, keyword.control.directive.conditional.if.c, keyword.control.directive.else.c\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.pointer-access.c\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.c\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.cpp, storage.modifier.array.bracket.square.cpp, meta.function.definition.parameters.cpp, meta.body.function.definition.cpp\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.dot-access.cpp, constant.character.escape.line-continuation.cpp\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.include.cpp, punctuation.definition.directive.cpp, keyword.control.directive.pragma.cpp, keyword.control.directive.line.cpp, keyword.control.directive.define.cpp, keyword.control.directive.conditional.cpp, keyword.control.directive.diagnostic.error.cpp, keyword.control.directive.undef.cpp, keyword.control.directive.conditional.ifdef.cpp, keyword.control.directive.endif.cpp, keyword.control.directive.conditional.ifndef.cpp, keyword.control.directive.conditional.if.cpp, keyword.control.directive.else.cpp, storage.type.namespace.definition.cpp, keyword.other.using.directive.cpp, storage.type.struct.cpp\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.pointer-access.cpp, punctuation.section.angle-brackets.begin.template.call.cpp, punctuation.section.angle-brackets.end.template.call.cpp\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.cpp\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.using.cs\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.cs, constant.character.escape.cs, punctuation.definition.interpolation.begin.cs, punctuation.definition.interpolation.end.cs\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.cs, string.quoted.single.cs, punctuation.definition.string.begin.cs, punctuation.definition.string.end.cs\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.object.property.cs\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.namespace.cs\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.symbol.fsharp, constant.language.unit.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.format.specifier.fsharp, entity.name.type.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.fsharp, string.quoted.single.fsharp, punctuation.definition.string.begin.fsharp, punctuation.definition.string.end.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"support.function.attribute.fsharp\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.java, punctuation.separator.period.java\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.java, keyword.other.package.java\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.arrow.java, keyword.control.ternary.java\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.property.java\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.wildcard.java, storage.modifier.import.java, storage.type.annotation.java, punctuation.definition.annotation.java, storage.modifier.package.java, entity.name.type.module.java\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.kotlin, storage.type.annotation.kotlin\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.scala\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.scala\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.import.scala\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.scala, string.quoted.single.scala, punctuation.definition.string.begin.scala, punctuation.definition.string.end.scala, string.quoted.double.interpolated.scala, string.quoted.single.interpolated.scala, string.quoted.triple.scala\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class, entity.other.inherited-class.scala\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"keyword.declaration.stable.scala, keyword.other.arrow.scala\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.scala\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.navigation.groovy, meta.method.body.java, meta.definition.method.groovy, meta.definition.method.signature.java\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.groovy\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.import.groovy, keyword.other.package.groovy, keyword.other.import.static.groovy\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.def.groovy\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.interpolated.groovy, meta.method.groovy\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.groovy, storage.modifier.package.groovy\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.annotation.groovy\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.go\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.package.go\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.import.go, keyword.package.go\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.mod.rust\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.path.rust, keyword.operator.member-access.rust\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.rust\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.core.rust\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"meta.attribute.rust, variable.language.rust, storage.type.module.rust\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.swift, support.function.any-method.swift\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.swift\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.class.php\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.trait.php\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.php, support.other.namespace.php\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.modifier.access.control.public.cpp, storage.type.modifier.access.control.private.cpp\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.include.php, storage.type.php\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.arguments.python\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.decorator.python, punctuation.separator.period.python\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.python, keyword.control.import.from.python\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.lua\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.lua\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.method.with-arguments.ruby\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.method.ruby\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.pseudo-method.ruby, storage.type.variable.ruby\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.special-method.ruby\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.module.ruby, punctuation.definition.constant.ruby\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp.character-class.ruby,string.regexp.interpolated.ruby,punctuation.definition.character-class.ruby,string.regexp.group.ruby, punctuation.section.regexp.ruby, punctuation.definition.group.ruby\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant.ruby\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.arrow.haskell, keyword.other.big-arrow.haskell, keyword.other.double-colon.haskell\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.haskell, string.quoted.double.haskell, string.quoted.single.haskell, punctuation.definition.string.begin.haskell, punctuation.definition.string.end.haskell\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.haskell\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace, meta.preprocessor.haskell\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.julia, keyword.control.export.julia\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.storage.modifier.julia\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.julia\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"support.function.macro.julia\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.period.elm\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.elm\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.r\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.r, variable.function.r\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.r\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.namespace.r\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.module-function.erlang, punctuation.section.directive.begin.erlang\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.erlang, keyword.control.directive.define.erlang\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.class.module.erlang\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.erlang, string.quoted.single.erlang, punctuation.definition.string.begin.erlang, punctuation.definition.string.end.erlang\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.directive.export.erlang, keyword.control.directive.module.erlang, keyword.control.directive.import.erlang, keyword.control.directive.behaviour.erlang\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite.module.elixir, punctuation.definition.variable.elixir\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.elixir\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.module.elixir\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.value-signature.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.variant.ocaml\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.sub.perl, storage.type.declare.routine.perl\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.lisp\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function-type.lisp\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"keyword.constant.lisp\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.lisp\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword.clojure, support.variable.clojure, meta.definition.variable.clojure\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.global.clojure\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.clojure\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"meta.scope.if-block.shell, meta.scope.group.shell\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.shell, entity.name.function.shell\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.shell, string.quoted.single.shell, punctuation.definition.string.begin.shell, punctuation.definition.string.end.shell, string.unquoted.heredoc.shell\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.heredoc-token.shell, variable.other.normal.shell, punctuation.definition.variable.shell, variable.other.special.shell, variable.other.positional.shell, variable.other.bracket.shell\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"support.function.builtin.fish\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"support.function.unix.fish\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.normal.fish, punctuation.definition.variable.fish, variable.other.fixed.fish, variable.other.special.fish\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.fish, punctuation.definition.string.end.fish, punctuation.definition.string.begin.fish, string.quoted.single.fish\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape.single.fish\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.variable.powershell\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.powershell, support.function.attribute.powershell, support.function.powershell\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.powershell, string.quoted.double.powershell, punctuation.definition.string.begin.powershell, punctuation.definition.string.end.powershell, string.quoted.double.heredoc.powershell\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.member.powershell\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.alias.graphql\",\n      \"settings\": {\n        \"foreground\": \"#d3c6aa\"\n      }\n    },\n    {\n      \"scope\": \"keyword.type.graphql\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.fragment.graphql\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.target.makefile\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.makefile\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"meta.scope.prerequisites.makefile\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"string.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"storage.source.cmake\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.map.viml\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.map.viml\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.map.viml, constant.character.map.key.viml\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.map.special.viml\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.tmux, constant.numeric.tmux\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.package-manager.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.flag.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.dockerfile, string.quoted.single.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.base-image.dockerfile, entity.name.image.dockerfile\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.separator.diff\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff, punctuation.definition.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.range.context, punctuation.definition.range.diff\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header.from-file\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff, punctuation.definition.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff, punctuation.definition.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.from-file.diff\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.group-title.ini, punctuation.definition.entity.ini\",\n      \"settings\": {\n        \"foreground\": \"#e67e80\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value.ini\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.ini, string.quoted.single.ini, punctuation.definition.string.begin.ini, punctuation.definition.string.end.ini\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.definition.ini\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"support.function.aggregate.sql\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.sql, punctuation.definition.string.end.sql, punctuation.definition.string.begin.sql, string.quoted.double.sql\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"support.type.graphql\",\n      \"settings\": {\n        \"foreground\": \"#dbbc7f\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.graphql\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.enum.graphql\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.support.type.property-name.begin.json, punctuation.support.type.property-name.end.json, punctuation.separator.dictionary.key-value.json, punctuation.definition.string.begin.json, punctuation.definition.string.end.json, punctuation.separator.dictionary.pair.json, punctuation.separator.array.json\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.json\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value.mapping.yaml\",\n      \"settings\": {\n        \"foreground\": \"#859289\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.plain.out.yaml, string.quoted.single.yaml, string.quoted.double.yaml, punctuation.definition.string.begin.yaml, punctuation.definition.string.end.yaml, string.unquoted.plain.in.yaml, string.unquoted.block.yaml\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.anchor.yaml, punctuation.definition.block.sequence.item.yaml\",\n      \"settings\": {\n        \"foreground\": \"#83c092\"\n      }\n    },\n    {\n      \"scope\": \"keyword.key.toml\",\n      \"settings\": {\n        \"foreground\": \"#e69875\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.single.basic.line.toml, string.quoted.single.literal.line.toml, punctuation.definition.keyValuePair.toml\",\n      \"settings\": {\n        \"foreground\": \"#a7c080\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.boolean.toml\",\n      \"settings\": {\n        \"foreground\": \"#7fbbb3\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.table.toml, punctuation.definition.table.toml, entity.other.attribute-name.table.array.toml, punctuation.definition.table.array.toml\",\n      \"settings\": {\n        \"foreground\": \"#d699b6\"\n      }\n    },\n    {\n      \"scope\": \"comment, string.comment, punctuation.definition.comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#859289\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { everforestDark as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,iBAAiB,OAAO,MAAM,CAAC;IACjC,UAAU;QACR,4BAA4B;QAC5B,iCAAiC;QACjC,0BAA0B;QAC1B,sBAAsB;QACtB,8BAA8B;QAC9B,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,8BAA8B;QAC9B,yBAAyB;QACzB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,oCAAoC;QACpC,mDAAmD;QACnD,0CAA0C;QAC1C,kCAAkC;QAClC,4CAA4C;QAC5C,4CAA4C;QAC5C,gCAAgC;QAChC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,gCAAgC;QAChC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,yBAAyB;QACzB,2BAA2B;QAC3B,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,8BAA8B;QAC9B,uCAAuC;QACvC,iDAAiD;QACjD,6CAA6C;QAC7C,4CAA4C;QAC5C,oCAAoC;QACpC,kCAAkC;QAClC,wCAAwC;QACxC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,uDAAuD;QACvD,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,sCAAsC;QACtC,oCAAoC;QACpC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,mCAAmC;QACnC,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,yBAAyB;QACzB,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,qCAAqC;QACrC,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,8BAA8B;QAC9B,+CAA+C;QAC/C,gDAAgD;QAChD,yCAAyC;QACzC,uCAAuC;QACvC,2CAA2C;QAC3C,iDAAiD;QACjD,sCAAsC;QACtC,0CAA0C;QAC1C,gDAAgD;QAChD,oDAAoD;QACpD,yCAAyC;QACzC,+CAA+C;QAC/C,qDAAqD;QACrD,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,gCAAgC;QAChC,iCAAiC;QACjC,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,mBAAmB;QACnB,mCAAmC;QACnC,mCAAmC;QACnC,uCAAuC;QACvC,uCAAuC;QACvC,4CAA4C;QAC5C,sCAAsC;QACtC,gCAAgC;QAChC,oCAAoC;QACpC,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,sCAAsC;QACtC,4CAA4C;QAC5C,kDAAkD;QAClD,mDAAmD;QACnD,qDAAqD;QACrD,4DAA4D;QAC5D,wDAAwD;QACxD,qDAAqD;QACrD,6CAA6C;QAC7C,8CAA8C;QAC9C,8CAA8C;QAC9C,+CAA+C;QAC/C,8CAA8C;QAC9C,gDAAgD;QAChD,iCAAiC;QACjC,iCAAiC;QACjC,4CAA4C;QAC5C,wCAAwC;QACxC,2CAA2C;QAC3C,sCAAsC;QACtC,oCAAoC;QACpC,uCAAuC;QACvC,sCAAsC;QACtC,oCAAoC;QACpC,wCAAwC;QACxC,mBAAmB;QACnB,uBAAuB;QACvB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,4BAA4B;QAC5B,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,qCAAqC;QACrC,iBAAiB;QACjB,eAAe;QACf,8BAA8B;QAC9B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,gCAAgC;QAChC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,0BAA0B;QAC1B,mBAAmB;QACnB,mBAAmB;QACnB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,gBAAgB;QAChB,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,4BAA4B;QAC5B,gCAAgC;QAChC,6CAA6C;QAC7C,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,gCAAgC;QAChC,6BAA6B;QAC7B,sCAAsC;QACtC,2CAA2C;QAC3C,+BAA+B;QAC/B,sCAAsC;QACtC,wCAAwC;QACxC,wCAAwC;QACxC,uCAAuC;QACvC,uCAAuC;QACvC,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,uBAAuB;QACvB,iCAAiC;QACjC,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,sCAAsC;QACtC,gCAAgC;QAChC,+BAA+B;QAC/B,kCAAkC;QAClC,0BAA0B;QAC1B,8BAA8B;QAC9B,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,oBAAoB;QACpB,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,kCAAkC;QAClC,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sBAAsB;QACtB,sBAAsB;QACtB,mCAAmC;QACnC,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,iCAAiC;QACjC,qCAAqC;QACrC,qCAAqC;QACrC,0CAA0C;QAC1C,kCAAkC;QAClC,kCAAkC;QAClC,mCAAmC;QACnC,mCAAmC;QACnC,8BAA8B;QAC9B,gCAAgC;QAChC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,oCAAoC;QACpC,mCAAmC;QACnC,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,sCAAsC;QACtC,6BAA6B;QAC7B,iCAAiC;QACjC,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,wBAAwB;QACxB,6BAA6B;QAC7B,iCAAiC;QACjC,gCAAgC;QAChC,mCAAmC;QACnC,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,uBAAuB;QACvB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,qBAAqB;QACrB,qBAAqB;QACrB,6BAA6B;QAC7B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,uBAAuB;QACvB,4BAA4B;QAC5B,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,wCAAwC;QACxC,gCAAgC;QAChC,qCAAqC;QACrC,mCAAmC;QACnC,mCAAmC;QACnC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,gBAAgB;QAChB,oBAAoB;QACpB,yBAAyB;QACzB,mBAAmB;QACnB,wBAAwB;QACxB,yBAAyB;QACzB,8BAA8B;QAC9B,wBAAwB;QACxB,6BAA6B;QAC7B,oBAAoB;QACpB,cAAc;QACd,0BAA0B;QAC1B,iBAAiB;QACjB,kBAAkB;QAClB,wBAAwB;QACxB,6BAA6B;QAC7B,oBAAoB;QACpB,sCAAsC;QACtC,2CAA2C;QAC3C,sCAAsC;QACtC,2CAA2C;QAC3C,oBAAoB;QACpB,sCAAsC;QACtC,2CAA2C;QAC3C,sCAAsC;QACtC,2CAA2C;IAC7C;IACA,eAAe;QACb;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}