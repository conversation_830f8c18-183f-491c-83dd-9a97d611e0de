"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5374],{4640:(e,t,n)=>{n.d(t,{Ak:()=>eo,Ap:()=>L,BU:()=>R,F2:()=>tW,Fr:()=>eZ,Ge:()=>ea,HM:()=>eY,HN:()=>G,OX:()=>H,P$:()=>tX,UU:()=>tC,WU:()=>I,Xf:()=>Z,aG:()=>tJ,b4:()=>eX,dP:()=>tY,ez:()=>X,gk:()=>eI,j$:()=>D,k2:()=>m,nn:()=>e_,x7:()=>tG,xb:()=>eb,xl:()=>T,zb:()=>tx});var r=Object.defineProperty,i="@liveblocks/core",o="2.24.2",s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};function a(e){console.error(e)}function d(e){let t=e.editedAt?new Date(e.editedAt):void 0,n=new Date(e.createdAt),r=e.reactions.map(e=>({...e,createdAt:new Date(e.createdAt)}));if(e.body)return{...e,reactions:r,createdAt:n,editedAt:t};{let i=new Date(e.deletedAt);return{...e,reactions:r,createdAt:n,editedAt:t,deletedAt:i}}}function c(e){let t=new Date(e.createdAt),n=new Date(e.updatedAt),r=e.comments.map(e=>d(e));return{...e,createdAt:t,updatedAt:n,comments:r}}function l(e){let t=new Date(e.notifiedAt),n=e.readAt?new Date(e.readAt):null;if("activities"in e){let r=e.activities.map(e=>({...e,createdAt:new Date(e.createdAt)}));return{...e,notifiedAt:t,readAt:n,activities:r}}return{...e,notifiedAt:t,readAt:n}}function u(e){let t=new Date(e.createdAt);return{...e,createdAt:t}}function h(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}function p(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}function f(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}var m={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(m,{error:()=>b,errorWithTitle:()=>E,warn:()=>v,warnWithTitle:()=>w});var y="background:#0e0d12;border-radius:9999px;color:#fff;padding:3px 7px;font-family:sans-serif;font-weight:600;";function g(e){return"undefined"==typeof window?console[e]:(t,...n)=>console[e]("%cLiveblocks",y,t,...n)}var v=g("warn"),b=g("error");function _(e){return"undefined"==typeof window?console[e]:(t,n,...r)=>console[e](`%cLiveblocks%c ${t}`,y,"font-weight:600",n,...r)}var w=_("warn"),E=_("error");function S(e){return null!==e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)}function I(e){return S(e)&&"string"==typeof e.startsWith}function T(e){throw Error(e)}function O(e){return Object.entries(e)}function A(e){try{return JSON.parse(e)}catch(e){return}}function k(e){return JSON.parse(JSON.stringify(e))}function C(e){return e.filter(e=>null!=e)}function R(e){let t={...e};return Object.keys(e).forEach(e=>{void 0===t[e]&&delete t[e]}),t}async function N(e,t,n){let r;return Promise.race([e,new Promise((e,i)=>{r=setTimeout(()=>{i(Error(n))},t)})]).finally(()=>clearTimeout(r))}function P(e){let t=null;return()=>(null===t&&(t=e().catch(e=>{throw setTimeout(()=>{t=null},5e3),e})),t)}var D=class e extends Error{response;details;constructor(e,t,n){super(e),this.name="HttpError",this.response=t,this.details=n}static async fromResponse(t){let n,r,i;try{n=await t.text()}catch{}let o=n?A(n):void 0;S(o)&&(r=o);let s="";s||="string"==typeof r?.message?r.message:"",s||="string"==typeof r?.error?r.error:"",void 0===o&&(s||=n||""),s||=t.statusText;try{i=new URL(t.url).pathname}catch{}return new e(s+=void 0!==i?` (got status ${t.status} from ${i})`:` (got status ${t.status})`,t,r)}get status(){return this.response.status}},x=e=>e instanceof D&&e.status>=400&&e.status<500;async function L(e,t,n,r=x){let i=n.length>0?n[n.length-1]:0,o=0;for(;;){o++;try{return await e()}catch(e){if(r(e))throw e;if(o>=t)throw Error(`Failed after ${t} attempts: ${String(e)}`)}let s=n[o-1]??i;v(`Attempt ${o} was unsuccessful. Retrying in ${s} milliseconds.`),await function(e){return new Promise(t=>setTimeout(t,e))}(s)}}function U(){let e,t;return[new Promise((n,r)=>{e=n,t=r}),e,t]}function M(){let[e,t,n]=U();return{promise:e,resolve:t,reject:n}}function $(){let e=new Set;function t(t){return e.add(t),()=>e.delete(t)}function n(e){let n=t(t=>(n(),e(t)));return n}async function r(e){let n;return new Promise(r=>{n=t(t=>{(void 0===e||e(t))&&r(t)})}).finally(()=>n?.())}return{notify:function(t){let n=!1;for(let r of e)r(t),n=!0;return n},subscribe:t,subscribeOnce:n,count:function(){return e.size},waitUntil:r,dispose(){e.clear()},observable:{subscribe:t,subscribeOnce:n,waitUntil:r}}}var K=e=>e,j=Symbol("kSinks"),q=Symbol("kTrigger"),z=null,V=null;function H(e){if(null!==z)return void e();z=new Set;try{e()}finally{for(let e of z)e[q]();z=null}}function F(e){z||T("Expected to be in an active batch"),z.add(e)}function B(e,t){let n=!1,r={...e};return Object.keys(t).forEach(e=>{let i=t[e];r[e]!==i&&(void 0===i?delete r[e]:r[e]=i,n=!0)}),n?r:e}var W=class{equals;#e;[j];constructor(e){this.equals=e??Object.is,this.#e=$(),this[j]=new Set,this.get=this.get.bind(this),this.subscribe=this.subscribe.bind(this),this.subscribeOnce=this.subscribeOnce.bind(this)}dispose(){this.#e.dispose(),this.#e="(disposed)",this.equals="(disposed)"}get hasWatchers(){if(this.#e.count()>0)return!0;for(let e of this[j])if(e.hasWatchers)return!0;return!1}[q](){for(let e of(this.#e.notify(),this[j]))F(e)}subscribe(e){return 0===this.#e.count()&&this.get(),this.#e.subscribe(e)}subscribeOnce(e){let t=this.subscribe(()=>(t(),e()));return t}waitUntil(){throw Error("waitUntil not supported on Signals")}markSinksDirty(){for(let e of this[j])e.markDirty()}addSink(e){this[j].add(e)}removeSink(e){this[j].delete(e)}asReadonly(){return this}},G=class extends W{#t;constructor(e,t){super(t),this.#t=K(e)}dispose(){super.dispose(),this.#t="(disposed)"}get(){return V?.add(this),this.#t}set(e){H(()=>{"function"==typeof e&&(e=e(this.#t)),this.equals(this.#t,e)||(this.#t=K(e),this.markSinksDirty(),F(this))})}},Y=class extends G{constructor(e){super(K(R(e)))}set(){throw Error("Don't call .set() directly, use .patch()")}patch(e){super.set(t=>B(t,e))}},J=Symbol(),X=class e extends W{#n;#r;#i;#o;#s;static from(...t){let n=t.pop();if("function"!=typeof n&&T("Invalid .from() call, last argument expected to be a function"),"function"!=typeof t[t.length-1])return new e(t,n);{let r=t.pop();return new e(t,r,n)}}constructor(e,t,n){super(n),this.#r=!0,this.#n=J,this.#o=e,this.#i=new Set,this.#s=t}dispose(){for(let e of this.#i)e.removeSink(this);this.#n="(disposed)",this.#i="(disposed)",this.#o="(disposed)",this.#s="(disposed)"}get isDirty(){return this.#r}#a(){let e,t=V;V=new Set;try{e=this.#s(...this.#o.map(e=>e.get()))}finally{let e=this.#i;for(let t of(this.#i=new Set,V))this.#i.add(t),e.delete(t);for(let t of e)t.removeSink(this);for(let e of this.#i)e.addSink(this);V=t}return this.#r=!1,!this.equals(this.#n,e)&&(this.#n=e,!0)}markDirty(){this.#r||(this.#r=!0,this.markSinksDirty())}get(){return this.#r&&this.#a(),V?.add(this),this.#n}[q](){this.hasWatchers&&this.#a()&&super[q]()}},Z=class extends W{#d;constructor(e){super(),this.#d=e}dispose(){super.dispose(),this.#d="(disposed)"}get(){return V?.add(this),this.#d}mutate(e){H(()=>{let t=!e||e(this.#d);null!==t&&"object"==typeof t&&"then"in t&&T("MutableSignal.mutate() does not support async callbacks"),!1!==t&&(this.markSinksDirty(),F(this))})}};function Q(e,t){return null===t||"object"!=typeof t||Array.isArray(t)?t:Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{})}function ee(e){return JSON.stringify(e,Q)}function et(e){try{return JSON.stringify(e)}catch(t){throw console.error(`Could not stringify: ${t.message}`),console.error(e),t}}var en=class{input;resolve;reject;promise;constructor(e){this.input=e;let{promise:t,resolve:n,reject:r}=M();this.promise=t,this.resolve=n,this.reject=r}},er=class{#c=[];#l;#u;#h;#p;error=!1;constructor(e,t){this.#l=e,this.#u=t.size??50,this.#h=t.delay}#f(){void 0!==this.#p&&(clearTimeout(this.#p),this.#p=void 0)}#m(){this.#c.length===this.#u?this.#y():1===this.#c.length&&(this.#f(),this.#p=setTimeout(()=>void this.#y(),this.#h))}async #y(){if(0===this.#c.length)return;let e=this.#c.splice(0),t=e.map(e=>e.input);try{let n=await this.#l(t);this.error=!1,e.forEach((t,r)=>{let i=n?.[r];Array.isArray(n)?e.length!==n.length?t.reject(Error(`Callback must return an array of the same length as the number of provided items. Expected ${e.length}, but got ${n.length}.`)):i instanceof Error?t.reject(i):t.resolve(i):t.reject(Error("Callback must return an array."))})}catch(t){this.error=!0,e.forEach(e=>{e.reject(t)})}}get(e){let t=this.#c.find(t=>ee(t.input)===ee(e));if(t)return t.promise;let n=new en(e);return this.#c.push(n),this.#m(),n.promise}clear(){this.#c=[],this.error=!1,this.#f()}};function ei(e){let t=new Z(new Map);function n(e,n){t.mutate(t=>{t.set(e,n)})}async function r(r){let i=ee(r);if(!t.get().has(i))try{n(i,{isLoading:!0});let t=await e.get(r);n(i,{isLoading:!1,data:t})}catch(e){n(i,{isLoading:!1,error:e})}}return{subscribe:t.subscribe,enqueue:r,getItemState:function(e){let n=ee(e);return t.get().get(n)},invalidate:function(e){t.mutate(t=>{if(Array.isArray(e))for(let n of e)t.delete(ee(n));else t.clear()})},batch:e,_cacheKeys:function(){return[...t.get().keys()]}}}var eo=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t<63?"_":"-","");function es(e){return`${e}_${eo()}`}var ea=class extends Map{#g;constructor(e,t){super(t),this.#g=e}getOrCreate(e,t){if(super.has(e))return super.get(e);{let n=(t??this.#g??T("DefaultMap used without a factory function"))(e);return this.set(e,n),n}}},ed=/^[a-zA-Z_][a-zA-Z0-9_]*$/;function ec(e){let t=[],n=Object.entries(e),r=[],i=[],o=[];return n.forEach(([e,t])=>{if(!ed.test(e))throw Error("Key must only contain letters, numbers, _");eh(t)?r.push([e,t]):S(t)&&(I(t)?i.push([e,t]):o.push([e,t]))}),t=[...el(r),...eu(i)],o.forEach(([e,n])=>{let r=Object.entries(n),i=[],o=[];r.forEach(([t,n])=>{if(ef(t))throw Error("Key cannot be empty");eh(n)?i.push([ep(e,t),n]):I(n)&&o.push([ep(e,t),n])}),t=[...t,...el(i),...eu(o)]}),t.map(({key:e,operator:t,value:n})=>`${e}${t}${em(n)}`).join(" ")}var el=e=>{let t=[];return e.forEach(([e,n])=>{t.push({key:e,operator:":",value:n})}),t},eu=e=>{let t=[];return e.forEach(([e,n])=>{"startsWith"in n&&"string"==typeof n.startsWith&&t.push({key:e,operator:"^",value:n.startsWith})}),t},eh=e=>"string"==typeof e||"number"==typeof e||"boolean"==typeof e||null===e,ep=(e,t)=>t?`${e}[${em(t)}]`:e,ef=e=>!e||""===e.toString().trim();function em(e){let t=JSON.stringify(e);return"string"!=typeof e||t.includes("'")?t:`'${t.slice(1,-1).replace(/\\"/g,'"')}'`}function ey(e,...t){return e.reduce((e,n,r)=>e+encodeURIComponent(t[r-1]??"")+n)}function eg(e){return"public"===e.type?e.publicApiKey:e.token.raw}var ev=class{#v;#b;constructor(e,t){this.#v=e,this.#b=t}async #_(e,t,n,r){e.startsWith("/v2/c/")||T("This client can only be used to make /v2/c/* requests");let i=function(e,t,n){let r=new URL(t,e);return void 0!==n&&(r.search=(n instanceof URLSearchParams?n:function(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))null!=r&&t.set(n,r.toString());return t}(n)).toString()),r.toString()}(this.#v,e,r);return await this.#b(i,{...n,headers:{"Content-Type":"application/json; charset=utf-8",...n?.headers,Authorization:`Bearer ${eg(t)}`,"X-LB-Client":o||"dev"}})}async #w(e,t,n,r){let i,o=await this.#_(e,t,n,r);if(!o.ok)throw await D.fromResponse(o);try{i=await o.json()}catch{i={}}return i}async rawGet(e,t,n,r){return await this.#_(e,t,r,n)}async rawPost(e,t,n){return await this.#_(e,t,{method:"POST",body:et(n)})}async rawDelete(e,t){return await this.#_(e,t,{method:"DELETE"})}async get(e,t,n,r){return await this.#w(e,t,r,n)}async post(e,t,n,r,i){return await this.#w(e,t,{...r,method:"POST",body:et(n)},i)}async delete(e,t){return await this.#w(e,t,{method:"DELETE"})}async putBlob(e,t,n,r,i){return await this.#w(e,t,{...i,method:"PUT",headers:{"Content-Type":"application/octet-stream"},body:n},r)}};function eb(e,t){throw Error(t)}function e_(e,t="Expected value to be non-nullable"){return e}var ew=class{#E;constructor(e){this.#E=e}get current(){return this.#E}allowPatching(e){let t=this,n=!0;e({...this.#E,patch(e){if(n)for(let n of(t.#E=Object.assign({},t.#E,e),Object.entries(e))){let[e,t]=n;"patch"!==e&&(this[e]=t)}else throw Error("Can no longer patch stale context")}}),n=!1}},eE=1,eS=class{id;#S;#I;#T;#O;#A;#k;events;#C;#R;#N;get #P(){let e=this.#T.values()[Symbol.iterator]().next();if(!e.done)return e.value;throw Error("No states defined yet")}get currentState(){if(null===this.#O)if(0===this.#S)throw Error("Not started yet");else throw Error("Already stopped");return this.#O}start(){if(0!==this.#S)throw Error("State machine has already started");return this.#S=1,this.#O=this.#P,this.#D(null),this}stop(){if(1!==this.#S)throw Error("Cannot stop a state machine that hasn't started yet");this.#x(null),this.#S=2,this.#O=null}constructor(e){this.id=eE++,this.#S=0,this.#O=null,this.#T=new Set,this.#R=new Map,this.#C=[],this.#N=new Set,this.#A=new Map,this.#I=new ew(e),this.#k={didReceiveEvent:$(),willTransition:$(),didIgnoreEvent:$(),willExitState:$(),didEnterState:$()},this.events={didReceiveEvent:this.#k.didReceiveEvent.observable,willTransition:this.#k.willTransition.observable,didIgnoreEvent:this.#k.didIgnoreEvent.observable,willExitState:this.#k.willExitState.observable,didEnterState:this.#k.didEnterState.observable}}get context(){return this.#I.current}addState(e){if(0!==this.#S)throw Error("Already started");return this.#T.add(e),this}onEnter(e,t){if(0!==this.#S)throw Error("Already started");if(this.#R.has(e))throw Error(`enter/exit function for ${e} already exists`);return this.#R.set(e,t),this}onEnterAsync(e,t,n,r,i){return this.onEnter(e,()=>{let e=new AbortController,o=e.signal,s=i?setTimeout(()=>{let e=Error("Timed out");this.#L({type:"ASYNC_ERROR",reason:e},r)},i):void 0,a=!1;return t(this.#I.current,o).then(e=>{o.aborted||(a=!0,this.#L({type:"ASYNC_OK",data:e},n))},e=>{o.aborted||(a=!0,this.#L({type:"ASYNC_ERROR",reason:e},r))}),()=>{clearTimeout(s),a||e.abort()}})}#U(e){let t=[];if("*"===e)for(let e of this.#T)t.push(e);else if(e.endsWith(".*")){let n=e.slice(0,-1);for(let e of this.#T)e.startsWith(n)&&t.push(e)}else this.#T.has(e)&&t.push(e);if(0===t.length)throw Error(`No states match ${JSON.stringify(e)}`);return t}addTransitions(e,t){if(0!==this.#S)throw Error("Already started");for(let n of this.#U(e)){let r=this.#A.get(n);for(let[i,o]of(void 0===r&&(r=new Map,this.#A.set(n,r)),Object.entries(t))){if(r.has(i))throw Error(`Trying to set transition "${i}" on "${n}" (via "${e}"), but a transition already exists there.`);let t=o;if(this.#N.add(i),void 0!==t){let e="function"==typeof t?t:()=>t;r.set(i,e)}}}return this}addTimedTransition(e,t,n){return this.onEnter(e,()=>{let e=setTimeout(()=>{this.#L({type:"TIMER"},n)},"function"==typeof t?t(this.#I.current):t);return()=>{clearTimeout(e)}})}#M(e){return this.#A.get(this.currentState)?.get(e)}#x(e){this.#k.willExitState.notify(this.currentState),this.#I.allowPatching(t=>{e=e??this.#C.length;for(let n=0;n<e;n++)this.#C.pop()?.(t)})}#D(e){let t=function(e,t){let n=e.split(".");if(t<1||t>n.length+1)throw Error("Invalid number of levels");let r=[];t>n.length&&r.push("*");for(let e=n.length-t+1;e<n.length;e++){let t=n.slice(0,e);t.length>0&&r.push(t.join(".")+".*")}return r.push(e),r}(this.currentState,e??this.currentState.split(".").length+1);this.#I.allowPatching(e=>{for(let n of t){let t=this.#R.get(n),r=t?.(e);"function"==typeof r?this.#C.push(r):this.#C.push(null)}}),this.#k.didEnterState.notify(this.currentState)}send(e){if(!this.#N.has(e.type))throw Error(`Invalid event ${JSON.stringify(e.type)}`);if(2===this.#S)return;let t=this.#M(e.type);if(void 0!==t)return this.#L(e,t);this.#k.didIgnoreEvent.notify(e)}#L(e,t){let n,r;this.#k.didReceiveEvent.notify(e);let i=this.currentState,o=("function"==typeof t?t:()=>t)(e,this.#I.current);if(null===o)return void this.#k.didIgnoreEvent.notify(e);if("string"==typeof o?n=o:(n=o.target,r=Array.isArray(o.effect)?o.effect:[o.effect]),!this.#T.has(n))throw Error(`Invalid next state name: ${JSON.stringify(n)}`);this.#k.willTransition.notify({from:i,to:n});let[s,a]=function(e,t){if(e===t)return[0,0];let n=e.split("."),r=t.split("."),i=Math.min(n.length,r.length),o=0;for(;o<i&&n[o]===r[o];o++);return[n.length-o,r.length-o]}(this.currentState,n);if(s>0&&this.#x(s),this.#O=n,void 0!==r){let t=r;this.#I.allowPatching(n=>{for(let r of t)"function"==typeof r?r(n,e):n.patch(r)})}a>0&&this.#D(a)}},eI=(e=>(e[e.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",e[e.USER_JOINED=101]="USER_JOINED",e[e.USER_LEFT=102]="USER_LEFT",e[e.BROADCASTED_EVENT=103]="BROADCASTED_EVENT",e[e.ROOM_STATE=104]="ROOM_STATE",e[e.INITIAL_STORAGE_STATE=200]="INITIAL_STORAGE_STATE",e[e.UPDATE_STORAGE=201]="UPDATE_STORAGE",e[e.REJECT_STORAGE_OP=299]="REJECT_STORAGE_OP",e[e.UPDATE_YDOC=300]="UPDATE_YDOC",e[e.THREAD_CREATED=400]="THREAD_CREATED",e[e.THREAD_DELETED=407]="THREAD_DELETED",e[e.THREAD_METADATA_UPDATED=401]="THREAD_METADATA_UPDATED",e[e.THREAD_UPDATED=408]="THREAD_UPDATED",e[e.COMMENT_CREATED=402]="COMMENT_CREATED",e[e.COMMENT_EDITED=403]="COMMENT_EDITED",e[e.COMMENT_DELETED=404]="COMMENT_DELETED",e[e.COMMENT_REACTION_ADDED=405]="COMMENT_REACTION_ADDED",e[e.COMMENT_REACTION_REMOVED=406]="COMMENT_REACTION_REMOVED",e))(eI||{}),eT=(e=>(e[e.CLOSE_NORMAL=1e3]="CLOSE_NORMAL",e[e.CLOSE_ABNORMAL=1006]="CLOSE_ABNORMAL",e[e.UNEXPECTED_CONDITION=1011]="UNEXPECTED_CONDITION",e[e.TRY_AGAIN_LATER=1013]="TRY_AGAIN_LATER",e[e.INVALID_MESSAGE_FORMAT=4e3]="INVALID_MESSAGE_FORMAT",e[e.NOT_ALLOWED=4001]="NOT_ALLOWED",e[e.MAX_NUMBER_OF_MESSAGES_PER_SECONDS=4002]="MAX_NUMBER_OF_MESSAGES_PER_SECONDS",e[e.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS=4003]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS",e[e.MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP=4004]="MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP",e[e.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM=4005]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM",e[e.ROOM_ID_UPDATED=4006]="ROOM_ID_UPDATED",e[e.KICKED=4100]="KICKED",e[e.TOKEN_EXPIRED=4109]="TOKEN_EXPIRED",e[e.CLOSE_WITHOUT_RETRY=4999]="CLOSE_WITHOUT_RETRY",e))(eT||{});function eO(e){return 4999===e||e>=4e3&&e<4100}function eA(e){return 1013===e||e>=4200&&e<4300}function ek(e){let t=e.currentState;switch(t){case"@ok.connected":case"@ok.awaiting-pong":return"connected";case"@idle.initial":return"initial";case"@auth.busy":case"@auth.backoff":case"@connecting.busy":case"@connecting.backoff":case"@idle.zombie":return e.context.successCount>0?"reconnecting":"connecting";case"@idle.failed":return"disconnected";default:return eb(t,"Unknown state")}}var eC=[250,500,1e3,2e3,4e3,8e3,1e4],eR=eC[0]-1,eN=[2e3,3e4,6e4,3e5],eP=class extends Error{constructor(e){super(e)}};function eD(e,t){return t.find(t=>t>e)??t[t.length-1]}function ex(e){e.patch({backoffDelay:eD(e.backoffDelay,eC)})}function eL(e){e.patch({backoffDelay:eD(e.backoffDelay,eN)})}function eU(e){e.patch({successCount:0})}function eM(e,t){let n=2===e?b:1===e?v:()=>{};return()=>{n(t)}}function e$(e){let t="Connection to Liveblocks websocket server";return n=>{e instanceof Error?v(`${t} could not be established. ${String(e)}`):v(eq(e)?`${t} closed prematurely (code: ${e.code}). Retrying in ${n.backoffDelay}ms.`:`${t} could not be established.`)}}function eK(e){let t=[`code: ${e.code}`];return e.reason&&t.push(`reason: ${e.reason}`),e=>{v(`Connection to Liveblocks websocket server closed (${t.join(", ")}). Retrying in ${e.backoffDelay}ms.`)}}var ej=eM(1,"Connection to WebSocket closed permanently. Won't retry.");function eq(e){return!(e instanceof Error)&&"close"===e.type}var ez=e=>t=>t.patch(e),eV=class{#$;#K;events;constructor(e,t=!1,n=!0){let{machine:r,events:i,cleanups:o}=function(e,t){let n=function(){let e=$(),t=null;return{...e,notify:function(n){return null!==t?(t.push(n),!1):e.notify(n)},pause:function(){t=[]},unpause:function(){if(null!==t){for(let n of t)e.notify(n);t=null}},dispose(){e.dispose(),null!==t&&(t.length=0)}}}();n.pause();let r=$();function i(e,t){return()=>{r.notify({message:e,code:t})}}let o=new eS({successCount:0,authValue:null,socket:null,backoffDelay:eR}).addState("@idle.initial").addState("@idle.failed").addState("@idle.zombie").addState("@auth.busy").addState("@auth.backoff").addState("@connecting.busy").addState("@connecting.backoff").addState("@ok.connected").addState("@ok.awaiting-pong");o.addTransitions("*",{RECONNECT:{target:"@auth.backoff",effect:[ex,eU]},DISCONNECT:"@idle.initial"}),o.onEnter("@idle.*",eU).addTransitions("@idle.*",{CONNECT:(e,t)=>null!==t.authValue?"@connecting.busy":"@auth.busy"}),o.addTransitions("@auth.backoff",{NAVIGATOR_ONLINE:{target:"@auth.busy",effect:ez({backoffDelay:eR})}}).addTimedTransition("@auth.backoff",e=>e.backoffDelay,"@auth.busy").onEnterAsync("@auth.busy",()=>N(e.authenticate(),1e4,"Timed out during auth"),e=>({target:"@connecting.busy",effect:ez({authValue:e.data})}),e=>e.reason instanceof eP?{target:"@idle.failed",effect:[eM(2,e.reason.message),i(e.reason.message,-1)]}:{target:"@auth.backoff",effect:[ex,eM(2,`Authentication failed: ${e.reason instanceof Error?e.reason.message:String(e.reason)}`)]});let s=e=>o.send({type:"EXPLICIT_SOCKET_ERROR",event:e}),a=e=>o.send({type:"EXPLICIT_SOCKET_CLOSE",event:e}),d=e=>"pong"===e.data?o.send({type:"PONG"}):n.notify(e);function c(e){e&&(e.removeEventListener("error",s),e.removeEventListener("close",a),e.removeEventListener("message",d),e.close())}o.addTransitions("@connecting.backoff",{NAVIGATOR_ONLINE:{target:"@connecting.busy",effect:ez({backoffDelay:eR})}}).addTimedTransition("@connecting.backoff",e=>e.backoffDelay,"@connecting.busy").onEnterAsync("@connecting.busy",async(n,r)=>{let i=null,o=null;return N(new Promise((r,c)=>{if(null===n.authValue)throw Error("No auth authValue");let l=e.createSocket(n.authValue);function u(e){i=e,l.removeEventListener("message",d),c(e)}o=l;let[h,p]=U();function f(e){let t=A(e.data);t?.type===104&&p()}t.waitForActorId||p(),l.addEventListener("message",d),t.waitForActorId&&l.addEventListener("message",f),l.addEventListener("error",u),l.addEventListener("close",u),l.addEventListener("open",()=>{l.addEventListener("error",s),l.addEventListener("close",a);let e=()=>{l.removeEventListener("error",u),l.removeEventListener("close",u),l.removeEventListener("message",f)};h.then(()=>{r([l,e])})})}),1e4,"Timed out during websocket connection").then(([e,t])=>{if(t(),r.aborted)throw Error("Aborted");if(i)throw i;return e}).catch(e=>{throw c(o),e})},e=>({target:"@ok.connected",effect:ez({socket:e.data,backoffDelay:eR})}),e=>{let t=e.reason;if(t instanceof eP)return{target:"@idle.failed",effect:[eM(2,t.message),i(t.message,-1)]};if(eq(t)){if(4109===t.code)return"@auth.busy";if(eA(t.code))return{target:"@connecting.backoff",effect:[eL,e$(t)]};if(eO(t.code))return{target:"@idle.failed",effect:[eM(2,t.reason),i(t.reason,t.code)]}}return{target:"@auth.backoff",effect:[ex,e$(t)]}});let l={target:"@ok.awaiting-pong",effect:e=>{e.socket?.send("ping")}},u=()=>("undefined"!=typeof document?document:void 0)?.visibilityState==="hidden"&&e.canZombie()?"@idle.zombie":l;if(o.addTimedTransition("@ok.connected",3e4,u).addTransitions("@ok.connected",{NAVIGATOR_OFFLINE:u,WINDOW_GOT_FOCUS:l}),o.addTransitions("@idle.zombie",{WINDOW_GOT_FOCUS:"@connecting.backoff"}),o.onEnter("@ok.*",e=>{e.patch({successCount:e.successCount+1});let t=setTimeout(n.unpause,0);return e=>{c(e.socket),e.patch({socket:null}),clearTimeout(t),n.pause()}}).addTransitions("@ok.awaiting-pong",{PONG:"@ok.connected"}).addTimedTransition("@ok.awaiting-pong",2e3,{target:"@connecting.busy",effect:eM(1,"Received no pong from server, assume implicit connection loss.")}).addTransitions("@ok.*",{EXPLICIT_SOCKET_ERROR:(e,t)=>t.socket?.readyState===1?null:{target:"@connecting.backoff",effect:ex},EXPLICIT_SOCKET_CLOSE:e=>{var t;if(eO(e.event.code))return{target:"@idle.failed",effect:[ej,i(e.event.reason,e.event.code)]};if((t=e.event.code)>=4100&&t<4200)if(4109===e.event.code)return"@auth.busy";else return{target:"@auth.backoff",effect:[ex,eK(e.event)]};return eA(e.event.code)?{target:"@connecting.backoff",effect:[eL,eK(e.event)]}:{target:"@connecting.backoff",effect:[ex,eK(e.event)]}}}),"undefined"!=typeof document){let e="undefined"!=typeof document?document:void 0,t="undefined"!=typeof window?window:void 0,n=t??e;o.onEnter("*",r=>{function i(){o.send({type:"NAVIGATOR_OFFLINE"})}function s(){o.send({type:"NAVIGATOR_ONLINE"})}function a(){e?.visibilityState==="visible"&&o.send({type:"WINDOW_GOT_FOCUS"})}return t?.addEventListener("online",s),t?.addEventListener("offline",i),n?.addEventListener("visibilitychange",a),()=>{n?.removeEventListener("visibilitychange",a),t?.removeEventListener("online",s),t?.removeEventListener("offline",i),c(r.socket)}})}let h=[],{statusDidChange:p,didConnect:f,didDisconnect:m,unsubscribe:y}=function(e){let t=$(),n=$(),r=$(),i=null,o=e.events.didEnterState.subscribe(()=>{let o=ek(e);o!==i&&t.notify(o),"connected"===i&&"connected"!==o?r.notify():"connected"!==i&&"connected"===o&&n.notify(),i=o});return{statusDidChange:t.observable,didConnect:n.observable,didDisconnect:r.observable,unsubscribe:o}}(o);return h.push(y),t.enableDebugLogging&&h.push(function(e){let t=new Date().getTime();function n(...r){v(`${((new Date().getTime()-t)/1e3).toFixed(2)} [FSM #${e.id}]`,...r)}let r=[e.events.didReceiveEvent.subscribe(e=>n(`Event ${e.type}`)),e.events.willTransition.subscribe(({from:e,to:t})=>n("Transitioning",e,"→",t)),e.events.didIgnoreEvent.subscribe(e=>n("Ignored event",e.type,e,"(current state won't handle it)"))];return()=>{for(let e of r)e()}}(o)),o.start(),{machine:o,cleanups:h,events:{statusDidChange:p,didConnect:f,didDisconnect:m,onMessage:n.observable,onConnectionError:r.observable}}}(e,{waitForActorId:n,enableDebugLogging:t});this.#$=r,this.events=i,this.#K=o}getStatus(){try{return ek(this.#$)}catch{return"initial"}}get authValue(){return this.#$.context.authValue}connect(){this.#$.send({type:"CONNECT"})}reconnect(){this.#$.send({type:"RECONNECT"})}disconnect(){this.#$.send({type:"DISCONNECT"})}destroy(){let e;for(this.#$.stop();e=this.#K.pop();)e()}send(e){let t=this.#$.context?.socket;null===t?v("Cannot send: not connected yet",e):1!==t.readyState?v("Cannot send: WebSocket no longer open",e):t.send(e)}_privateSendMachineEvent(e){this.#$.send(e)}},eH=(e=>(e.Read="room:read",e.Write="room:write",e.PresenceWrite="room:presence:write",e.CommentsWrite="comments:write",e.CommentsRead="comments:read",e))(eH||{});function eF(e){return e.includes("room:write")}function eB(e){return e.includes("comments:write")||e.includes("room:write")}function eW(e){var t;let n=e.split(".");if(3!==n.length)throw Error("Authentication error: invalid JWT token");let r=A(function(e){try{let t=e.replace(/-/g,"+").replace(/_/g,"/");return decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))}catch(t){return atob(e)}}(n[1]));if(!(r&&S(t=r)&&("acc"===t.k||"id"===t.k||"sec-legacy"===t.k)))throw Error("Authentication error: expected a valid token but did not get one. Hint: if you are using a callback, ensure the room is passed when creating the token. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientCallback");return{raw:e,parsed:r}}async function eG(e,t,n){let r,i=await e(t,{method:"POST",headers:{"Content-Type":"application/json"},body:et(n)});if(!i.ok){let e=`${(await i.text()).trim()||"reason not provided in auth response"} (${i.status} returned by POST ${t})`;if(401===i.status||403===i.status)throw new eP(`Unauthorized: ${e}`);throw Error(`Failed to authenticate: ${e}`)}try{r=await i.json()}catch(e){throw Error(`Expected a JSON response when doing a POST request on "${t}". ${String(e)}`)}if(!S(r)||"string"!=typeof r.token)throw Error(`Expected a JSON response of the form \`{ token: "..." }\` when doing a POST request on "${t}", but got ${et(r)}`);let{token:o}=r;return{token:o}}var eY=Symbol();$().observable,Date.now();var eJ=Symbol("notification-settings-plain");function eX(e){let t={[eJ]:{value:e,enumerable:!1}};for(let e of["email","slack","teams","webPush"])t[e]={enumerable:!0,get(){let t=this[eJ][e];return void 0===t?(b(`In order to use the '${e}' channel, please set up your project first. For more information: https://liveblocks.io/docs/errors/enable-a-notification-channel`),null):t}};return void 0!==t?Object.create(null,t):Object.create(null)}function eZ(e,t){let n=eX({...e[eJ]});for(let e of Object.keys(t)){let r=t[e];if(void 0!==r){let t=Object.fromEntries(O(r).filter(([,e])=>void 0!==e));n[eJ][e]={...n[eJ][e],...t}}}return n}var eQ=e2(0),e0=e2(1),e1=eQ+e2(-1);function e2(e){let t=32+(e<0?95+e:e);if(t<32||t>126)throw Error(`Invalid n value: ${e}`);return String.fromCharCode(t)}function e3(e,t){if(void 0!==e&&void 0!==t){var n=e,r=t;if(n<r)return e4(n,r);if(n>r)return e4(r,n);throw Error("Cannot compute value between two equal positions")}if(void 0!==e){var i=e;for(let e=0;e<=i.length-1;e++){let t=i.charCodeAt(e);if(!(t>=126))return i.substring(0,e)+String.fromCharCode(t+1)}return i+e0}if(void 0===t)return e0;{var o=t;let e=o.length-1;for(let t=0;t<=e;t++){let n=o.charCodeAt(t);if(!(n<=32))if(t!==e)return o.substring(0,t+1);else if(33===n)return o.substring(0,t)+e1;else return o.substring(0,t)+String.fromCharCode(n-1)}return e0}}function e4(e,t){let n=0,r=e.length,i=t.length;for(;;){let a=n<r?e.charCodeAt(n):32,d=n<i?t.charCodeAt(n):126;if(a===d){n++;continue}if(d-a!=1){var o,s;return o=e,((s=n)<o.length?o.substring(0,s):o+eQ.repeat(s-o.length))+String.fromCharCode(d+a>>1)}{let t=n+1,r=e.substring(0,t);return r.length<t&&(r+=eQ.repeat(t-r.length)),r+e4(e.substring(t),"")}}}function e5(e){return!function(e){if(""===e)return!1;let t=e.length-1,n=e.charCodeAt(t);if(n<33||n>126)return!1;for(let n=0;n<t;n++){let t=e.charCodeAt(n);if(t<32||t>126)return!1}return!0}(e)?function(e){let t=[];for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);t.push(r<32?32:r>126?126:r)}for(;t.length>0&&32===t[t.length-1];)t.length--;return t.length>0?String.fromCharCode(...t):e0}(e):e}var e6=(e=>(e[e.INIT=0]="INIT",e[e.SET_PARENT_KEY=1]="SET_PARENT_KEY",e[e.CREATE_LIST=2]="CREATE_LIST",e[e.UPDATE_OBJECT=3]="UPDATE_OBJECT",e[e.CREATE_OBJECT=4]="CREATE_OBJECT",e[e.DELETE_CRDT=5]="DELETE_CRDT",e[e.DELETE_OBJECT_KEY=6]="DELETE_OBJECT_KEY",e[e.CREATE_MAP=7]="CREATE_MAP",e[e.CREATE_REGISTER=8]="CREATE_REGISTER",e))(e6||{});function e9(e,t,n=e5(t)){return Object.freeze({type:"HasParent",node:e,key:t,pos:n})}var e8=Object.freeze({type:"NoParent"}),e7=class{#j;#q;#z=e8;_getParentKeyOrThrow(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldKey;default:return eb(this.parent,"Unknown state")}}get _parentPos(){switch(this.parent.type){case"HasParent":return this.parent.pos;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldPos;default:return eb(this.parent,"Unknown state")}}get _pool(){return this.#j}get roomId(){return this.#j?this.#j.roomId:null}get _id(){return this.#q}get parent(){return this.#z}get _parentKey(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":return null;case"Orphaned":return this.parent.oldKey;default:return eb(this.parent,"Unknown state")}}_apply(e,t){if(5===e.type&&"HasParent"===this.parent.type)return this.parent.node._detachChild(this);return{modified:!1}}_setParentLink(e,t){switch(this.parent.type){case"HasParent":if(this.parent.node!==e)throw Error("Cannot set parent: node already has a parent");this.#z=e9(e,t);return;case"Orphaned":case"NoParent":this.#z=e9(e,t);return;default:return eb(this.parent,"Unknown state")}}_attach(e,t){if(this.#q||this.#j)throw Error("Cannot attach node: already attached");t.addNode(e,this),this.#q=e,this.#j=t}_detach(){switch(this.#j&&this.#q&&this.#j.deleteNode(this.#q),this.parent.type){case"HasParent":this.#z=function(e,t=e5(e)){return Object.freeze({type:"Orphaned",oldKey:e,oldPos:t})}(this.parent.key,this.parent.pos);break;case"NoParent":this.#z=e8;break;case"Orphaned":break;default:eb(this.parent,"Unknown state")}this.#j=void 0}#V;#H;#F;invalidate(){(void 0!==this.#V||void 0!==this.#F)&&(this.#V=void 0,this.#F=void 0,"HasParent"===this.parent.type&&this.parent.node.invalidate())}toTreeNode(e){return(void 0===this.#F||this.#H!==e)&&(this.#H=e,this.#F=this._toTreeNode(e)),this.#F}toImmutable(){return void 0===this.#V&&(this.#V=this._toImmutable()),this.#V}},te=(e=>(e[e.OBJECT=0]="OBJECT",e[e.LIST=1]="LIST",e[e.MAP=2]="MAP",e[e.REGISTER=3]="REGISTER",e))(te||{}),tt=class e extends e7{#B;constructor(e){super(),this.#B=e}get data(){return this.#B}static _deserialize([t,n],r,i){let o=new e(n.data);return o._attach(t,i),o}_toOps(e,t,n){if(void 0===this._id)throw Error("Cannot serialize register if parentId or parentKey is undefined");return[{type:8,opId:n?.generateOpId(),id:this._id,parentId:e,parentKey:t,data:this.data}]}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveRegister if parent is missing");return{type:3,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key,data:this.data}}_attachChild(e){throw Error("Method not implemented.")}_detachChild(e){throw Error("Method not implemented.")}_apply(e,t){return super._apply(e,t)}_toTreeNode(e){return{type:"Json",id:this._id??eo(),key:e,payload:this.#B}}_toImmutable(){return this.#B}clone(){return k(this.data)}};function tn(e,t){let n=e._parentPos,r=t._parentPos;return n===r?0:n<r?-1:1}var tr=class e extends e7{#W;#G;#Y;constructor(e){let t;for(let n of(super(),this.#W=[],this.#G=new WeakSet,this.#Y=new Map,e)){let e=e3(t),r=tw(n);r._setParentLink(this,e),this.#W.push(r),t=e}}static _deserialize([t],n,r){let i=new e([]);i._attach(t,r);let o=n.get(t);if(void 0===o)return i;for(let[e,t]of o){let o=tm([e,t],n,r);o._setParentLink(i,t.parentKey),i._insertAndSort(o)}return i}_toOps(e,t,n){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],i={id:this._id,opId:n?.generateOpId(),type:2,parentId:e,parentKey:t};for(let e of(r.push(i),this.#W)){let t=e._getParentKeyOrThrow(),i=tl(e._toOps(this._id,t,n),void 0),o=i[0].opId;void 0!==o&&this.#Y.set(t,o),r.push(...i)}return r}_insertAndSort(e){this.#W.push(e),this._sortItems()}_sortItems(){this.#W.sort(tn),this.invalidate()}_indexOfPosition(e){return this.#W.findIndex(t=>t._getParentKeyOrThrow()===e)}_attach(e,t){for(let n of(super._attach(e,t),this.#W))n._attach(t.generateId(),t)}_detach(){for(let e of(super._detach(),this.#W))e._detach()}#J(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:t,parentKey:n}=e,r=tp(e);r._attach(t,this._pool),r._setParentLink(this,n);let i=e.deletedId,o=this._indexOfPosition(n);if(-1!==o){let t=this.#W[o];if(t._id===i)return t._detach(),this.#W[o]=r,{modified:to(this,[ts(o,r)]),reverse:[]};{this.#G.add(t),this.#W[o]=r;let n=[ts(o,r)],i=this.#X(e.deletedId);return i&&n.push(i),{modified:to(this,n),reverse:[]}}}{let t=[],i=this.#X(e.deletedId);return i&&t.push(i),this._insertAndSort(r),t.push(td(this._indexOfPosition(n),r)),{reverse:[],modified:to(this,t)}}}#Z(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let t=[],n=this.#X(e.deletedId);n&&t.push(n);let r=this.#Y.get(e.parentKey);if(void 0!==r)if(r!==e.opId)return 0===t.length?{modified:!1}:{modified:to(this,t),reverse:[]};else this.#Y.delete(e.parentKey);let i=this._indexOfPosition(e.parentKey),o=this.#W.find(t=>t._id===e.id);if(void 0!==o){if(o._parentKey===e.parentKey)return{modified:t.length>0&&to(this,t),reverse:[]};if(-1!==i){this.#G.add(this.#W[i]);let[e]=this.#W.splice(i,1);t.push(ta(i,e))}let n=this.#W.indexOf(o);o._setParentLink(this,e.parentKey),this._sortItems();let r=this.#W.indexOf(o);return r!==n&&t.push(tc(n,r,o)),{modified:t.length>0&&to(this,t),reverse:[]}}{let n=this._pool.getNode(e.id);if(n&&this.#G.has(n)){n._setParentLink(this,e.parentKey),this.#G.delete(n),this._insertAndSort(n);let r=this.#W.indexOf(n);return{modified:to(this,[-1===i?td(r,n):ts(r,n),...t]),reverse:[]}}{-1!==i&&this.#W.splice(i,1);let{newItem:n,newIndex:r}=this.#Q(e,e.parentKey);return{modified:to(this,[-1===i?td(r,n):ts(r,n),...t]),reverse:[]}}}}#X(e){if(void 0===e||void 0===this._pool)return null;let t=this._pool.getNode(e);if(void 0===t)return null;let n=this._detachChild(t);return!1===n.modified?null:n.modified.updates[0]}#ee(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let t=e5(e.parentKey),n=this._indexOfPosition(t);-1!==n&&this.#et(n,t);let{newItem:r,newIndex:i}=this.#Q(e,t);return{modified:to(this,[td(i,r)]),reverse:[]}}#en(e){let t=this.#W.find(t=>t._id===e.id),n=e5(e.parentKey),r=this._indexOfPosition(n);if(t)if(t._parentKey===n)return{modified:!1};else{let e=this.#W.indexOf(t);-1!==r&&this.#et(r,n),t._setParentLink(this,n),this._sortItems();let i=this._indexOfPosition(n);return i===e?{modified:!1}:{modified:to(this,[tc(e,i,t)]),reverse:[]}}{let t=e_(this._pool).getNode(e.id);if(t&&this.#G.has(t))return t._setParentLink(this,n),this.#G.delete(t),this._insertAndSort(t),{modified:to(this,[td(this._indexOfPosition(n),t)]),reverse:[]};{-1!==r&&this.#et(r,n);let{newItem:t,newIndex:i}=this.#Q(e,n);return{modified:to(this,[td(i,t)]),reverse:[]}}}}#er(e){let{id:t,parentKey:n}=e,r=tp(e);if(this._pool?.getNode(t)!==void 0)return{modified:!1};r._attach(t,e_(this._pool)),r._setParentLink(this,n);let i=this._indexOfPosition(n),o=n;return -1!==i&&(o=e3(this.#W[i]?._parentPos,this.#W[i+1]?._parentPos),r._setParentLink(this,o)),this._insertAndSort(r),{modified:to(this,[td(this._indexOfPosition(o),r)]),reverse:[{type:5,id:t}]}}#ei(e){let{id:t,parentKey:n}=e,r=tp(e);if(this._pool?.getNode(t)!==void 0)return{modified:!1};this.#Y.set(n,e_(e.opId));let i=this._indexOfPosition(n);if(r._attach(t,e_(this._pool)),r._setParentLink(this,n),-1===i)return this._insertAndSort(r),this.#X(e.deletedId),{reverse:[{type:5,id:t}],modified:to(this,[td(this._indexOfPosition(n),r)])};{let t=this.#W[i];t._detach(),this.#W[i]=r;let o=tl(t._toOps(e_(this._id),n,this._pool),e.id),s=[ts(i,r)],a=this.#X(e.deletedId);return a&&s.push(a),{modified:to(this,s),reverse:o}}}_attachChild(e,t){let n;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");return!1!==(n="set"===e.intent?1===t?this.#J(e):2===t?this.#Z(e):this.#ei(e):1===t?this.#ee(e):2===t?this.#en(e):this.#er(e)).modified&&this.invalidate(),n}_detachChild(e){if(e){let t=e_(e._parentKey),n=e._toOps(e_(this._id),t,this._pool),r=this.#W.indexOf(e);if(-1===r)return{modified:!1};let[i]=this.#W.splice(r,1);return this.invalidate(),e._detach(),{modified:to(this,[ta(r,i)]),reverse:n}}return{modified:!1}}#eo(e,t){if(this.#G.has(t))return this.#G.delete(t),t._setParentLink(this,e),this._insertAndSort(t),{modified:to(this,[td(this.#W.indexOf(t),t)]),reverse:[]};if(e===t._parentKey)return{modified:!1};let n=this._indexOfPosition(e);if(-1===n){let n=this.#W.indexOf(t);t._setParentLink(this,e),this._sortItems();let r=this.#W.indexOf(t);return r===n?{modified:!1}:{modified:to(this,[tc(n,r,t)]),reverse:[]}}{this.#W[n]._setParentLink(this,e3(e,this.#W[n+1]?._parentPos));let r=this.#W.indexOf(t);t._setParentLink(this,e),this._sortItems();let i=this.#W.indexOf(t);return i===r?{modified:!1}:{modified:to(this,[tc(r,i,t)]),reverse:[]}}}#es(e,t){let n=e_(t._parentKey);if(this.#G.has(t)){let n=this._indexOfPosition(e);return this.#G.delete(t),-1!==n&&this.#W[n]._setParentLink(this,e3(e,this.#W[n+1]?._parentPos)),t._setParentLink(this,e),this._insertAndSort(t),{modified:!1}}{if(e===n)return{modified:!1};let r=this.#W.indexOf(t),i=this._indexOfPosition(e);-1!==i&&this.#W[i]._setParentLink(this,e3(e,this.#W[i+1]?._parentPos)),t._setParentLink(this,e),this._sortItems();let o=this.#W.indexOf(t);return r===o?{modified:!1}:{modified:to(this,[tc(r,o,t)]),reverse:[]}}}#ea(e,t){let n=e_(t._parentKey),r=this.#W.indexOf(t),i=this._indexOfPosition(e);-1!==i&&this.#W[i]._setParentLink(this,e3(e,this.#W[i+1]?._parentPos)),t._setParentLink(this,e),this._sortItems();let o=this.#W.indexOf(t);return r===o?{modified:!1}:{modified:to(this,[tc(r,o,t)]),reverse:[{type:1,id:e_(t._id),parentKey:n}]}}_setChildKey(e,t,n){return 1===n?this.#eo(e,t):2===n?this.#es(e,t):this.#ea(e,t)}_apply(e,t){return super._apply(e,t)}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveList if parent is missing");return{type:1,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get length(){return this.#W.length}push(e){return this._pool?.assertStorageIsWritable(),this.insert(e,this.length)}insert(e,t){if(this._pool?.assertStorageIsWritable(),t<0||t>this.#W.length)throw Error(`Cannot insert list item at index "${t}". index should be between 0 and ${this.#W.length}`);let n=e3(this.#W[t-1]?this.#W[t-1]._parentPos:void 0,this.#W[t]?this.#W[t]._parentPos:void 0),r=tw(e);if(r._setParentLink(this,n),this._insertAndSort(r),this._pool&&this._id){let e=this._pool.generateId();r._attach(e,this._pool),this._pool.dispatch(r._toOps(this._id,n,this._pool),[{type:5,id:e}],new Map([[this._id,to(this,[td(t,r)])]]))}}move(e,t){if(this._pool?.assertStorageIsWritable(),t<0)throw Error("targetIndex cannot be less than 0");if(t>=this.#W.length)throw Error("targetIndex cannot be greater or equal than the list length");if(e<0)throw Error("index cannot be less than 0");if(e>=this.#W.length)throw Error("index cannot be greater or equal than the list length");let n=null,r=null;e<t?(r=t===this.#W.length-1?void 0:this.#W[t+1]._parentPos,n=this.#W[t]._parentPos):(r=this.#W[t]._parentPos,n=0===t?void 0:this.#W[t-1]._parentPos);let i=e3(n,r),o=this.#W[e],s=o._getParentKeyOrThrow();if(o._setParentLink(this,i),this._sortItems(),this._pool&&this._id){let n=new Map([[this._id,to(this,[tc(e,t,o)])]]);this._pool.dispatch([{type:1,id:e_(o._id),opId:this._pool.generateOpId(),parentKey:i}],[{type:1,id:e_(o._id),parentKey:s}],n)}}delete(e){if(this._pool?.assertStorageIsWritable(),e<0||e>=this.#W.length)throw Error(`Cannot delete list item at index "${e}". index should be between 0 and ${this.#W.length-1}`);let t=this.#W[e];t._detach();let[n]=this.#W.splice(e,1);if(this.invalidate(),this._pool){let r=t._id;if(r){let i=new Map;i.set(e_(this._id),to(this,[ta(e,n)])),this._pool.dispatch([{id:r,opId:this._pool.generateOpId(),type:5}],t._toOps(e_(this._id),t._getParentKeyOrThrow()),i)}}}clear(){if(this._pool?.assertStorageIsWritable(),this._pool){let e=[],t=[],n=[];for(let r of this.#W){r._detach();let i=r._id;i&&(e.push({type:5,id:i,opId:this._pool.generateOpId()}),t.push(...r._toOps(e_(this._id),r._getParentKeyOrThrow())),n.push(ta(0,r)))}this.#W=[],this.invalidate();let r=new Map;r.set(e_(this._id),to(this,n)),this._pool.dispatch(e,t,r)}else{for(let e of this.#W)e._detach();this.#W=[],this.invalidate()}}set(e,t){if(this._pool?.assertStorageIsWritable(),e<0||e>=this.#W.length)throw Error(`Cannot set list item at index "${e}". index should be between 0 and ${this.#W.length-1}`);let n=this.#W[e],r=n._getParentKeyOrThrow(),i=n._id;n._detach();let o=tw(t);if(o._setParentLink(this,r),this.#W[e]=o,this.invalidate(),this._pool&&this._id){let t=this._pool.generateId();o._attach(t,this._pool);let s=new Map;s.set(this._id,to(this,[ts(e,o)]));let a=tl(o._toOps(this._id,r,this._pool),i);this.#Y.set(r,e_(a[0].opId));let d=tl(n._toOps(this._id,r,void 0),t);this._pool.dispatch(a,d,s)}}toArray(){return this.#W.map(e=>t_(e))}every(e){return this.toArray().every(e)}filter(e){return this.toArray().filter(e)}find(e){return this.toArray().find(e)}findIndex(e){return this.toArray().findIndex(e)}forEach(e){return this.toArray().forEach(e)}get(e){if(!(e<0)&&!(e>=this.#W.length))return t_(this.#W[e])}indexOf(e,t){return this.toArray().indexOf(e,t)}lastIndexOf(e,t){return this.toArray().lastIndexOf(e,t)}map(e){return this.#W.map((t,n)=>e(t_(t),n))}some(e){return this.toArray().some(e)}[Symbol.iterator](){return new ti(this.#W)}#Q(e,t){let n=tp(e);return n._attach(e.id,e_(this._pool)),n._setParentLink(this,t),this._insertAndSort(n),{newItem:n,newIndex:this._indexOfPosition(t)}}#et(e,t){let n=e3(t,this.#W.length>e+1?this.#W[e+1]?._parentPos:void 0);this.#W[e]._setParentLink(this,n)}_toTreeNode(e){return{type:"LiveList",id:this._id??eo(),key:e,payload:this.#W.map((e,t)=>e.toTreeNode(t.toString()))}}toImmutable(){return super.toImmutable()}_toImmutable(){return this.#W.map(e=>e.toImmutable())}clone(){return new e(this.#W.map(e=>e.clone()))}},ti=class{#ed;constructor(e){this.#ed=e[Symbol.iterator]()}[Symbol.iterator](){return this}next(){let e=this.#ed.next();return e.done?{done:!0,value:void 0}:{value:t_(e.value)}}};function to(e,t){return{node:e,type:"LiveList",updates:t}}function ts(e,t){return{index:e,type:"set",item:t instanceof tt?t.data:t}}function ta(e,t){return{type:"delete",index:e,deletedItem:t instanceof tt?t.data:t}}function td(e,t){return{index:e,type:"insert",item:t instanceof tt?t.data:t}}function tc(e,t,n){return{type:"move",index:t,item:n instanceof tt?n.data:n,previousIndex:e}}function tl(e,t){return e.map((e,n)=>0===n?{...e,intent:"set",deletedId:t}:e)}var tu=class e extends e7{#ec;#el;constructor(e){if(super(),this.#el=new Map,e){let t=[];for(let[n,r]of e){let e=tw(r);e._setParentLink(this,n),t.push([n,e])}this.#ec=new Map(t)}else this.#ec=new Map}_toOps(e,t,n){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],i={id:this._id,opId:n?.generateOpId(),type:7,parentId:e,parentKey:t};for(let[e,t]of(r.push(i),this.#ec))r.push(...t._toOps(this._id,e,n));return r}static _deserialize([t,n],r,i){let o=new e;o._attach(t,i);let s=r.get(t);if(void 0===s)return o;for(let[e,t]of s){let n=tm([e,t],r,i);n._setParentLink(o,t.parentKey),o.#ec.set(t.parentKey,n),o.invalidate()}return o}_attach(e,t){for(let[n,r]of(super._attach(e,t),this.#ec))tg(r)&&r._attach(t.generateId(),t)}_attachChild(e,t){let n;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,parentKey:i,opId:o}=e,s=tp(e);if(void 0!==this._pool.getNode(r))return{modified:!1};if(2===t){let e=this.#el.get(i);if(e===o)return this.#el.delete(i),{modified:!1};if(void 0!==e)return{modified:!1}}else 1===t&&this.#el.delete(i);let a=this.#ec.get(i);if(a){let e=e_(this._id);n=a._toOps(e,i),a._detach()}else n=[{type:5,id:r}];return s._setParentLink(this,i),s._attach(r,this._pool),this.#ec.set(i,s),this.invalidate(),{modified:{node:this,type:"LiveMap",updates:{[i]:{type:"update"}}},reverse:n}}_detach(){for(let e of(super._detach(),this.#ec.values()))e._detach()}_detachChild(e){let t=e_(this._id),n=e_(e._parentKey),r=e._toOps(t,n,this._pool);for(let[t,n]of this.#ec)n===e&&(this.#ec.delete(t),this.invalidate());return e._detach(),{modified:{node:this,type:"LiveMap",updates:{[n]:{type:"delete"}}},reverse:r}}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveMap if parent is missing");return{type:2,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get(e){let t=this.#ec.get(e);if(void 0!==t)return t_(t)}set(e,t){this._pool?.assertStorageIsWritable();let n=this.#ec.get(e);n&&n._detach();let r=tw(t);if(r._setParentLink(this,e),this.#ec.set(e,r),this.invalidate(),this._pool&&this._id){let t=this._pool.generateId();r._attach(t,this._pool);let i=new Map;i.set(this._id,{node:this,type:"LiveMap",updates:{[e]:{type:"update"}}});let o=r._toOps(this._id,e,this._pool);this.#el.set(e,e_(o[0].opId)),this._pool.dispatch(r._toOps(this._id,e,this._pool),n?n._toOps(this._id,e):[{type:5,id:t}],i)}}get size(){return this.#ec.size}has(e){return this.#ec.has(e)}delete(e){this._pool?.assertStorageIsWritable();let t=this.#ec.get(e);if(void 0===t)return!1;if(t._detach(),this.#ec.delete(e),this.invalidate(),this._pool&&t._id){let n=e_(this._id),r=new Map;r.set(n,{node:this,type:"LiveMap",updates:{[e]:{type:"delete"}}}),this._pool.dispatch([{type:5,id:t._id,opId:this._pool.generateOpId()}],t._toOps(n,e),r)}return!0}entries(){let e=this.#ec.entries();return{[Symbol.iterator](){return this},next(){let t=e.next();return t.done?{done:!0,value:void 0}:{value:[t.value[0],t_(t.value[1])]}}}}[Symbol.iterator](){return this.entries()}keys(){return this.#ec.keys()}values(){let e=this.#ec.values();return{[Symbol.iterator](){return this},next(){let t=e.next();return t.done?{done:!0,value:void 0}:{value:t_(t.value)}}}}forEach(e){for(let t of this)e(t[1],t[0],this)}_toTreeNode(e){return{type:"LiveMap",id:this._id??eo(),key:e,payload:Array.from(this.#ec.entries()).map(([e,t])=>t.toTreeNode(e))}}toImmutable(){return super.toImmutable()}_toImmutable(){let e=new Map;for(let[t,n]of this.#ec)e.set(t,n.toImmutable());return K(e)}clone(){return new e(Array.from(this.#ec).map(([e,t])=>[e,t.clone()]))}},th=class e extends e7{#ec;#eu;static #eh(e){let t=new Map,n=null;for(let[i,o]of e){var r;if(0!==o.type||void 0!==(r=o).parentId&&void 0!==r.parentKey){let e=[i,o],n=t.get(o.parentId);void 0!==n?n.push(e):t.set(o.parentId,[e])}else n=[i,o]}if(null===n)throw Error("Root can't be null");return[n,t]}static _fromItems(t,n){let[r,i]=e.#eh(t);return e._deserialize(r,i,n)}constructor(e={}){super(),this.#eu=new Map;let t=R(e);for(let e of Object.keys(t)){let n=t[e];tg(n)&&n._setParentLink(this,e)}this.#ec=new Map(Object.entries(t))}_toOps(e,t,n){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=n?.generateOpId(),i=[],o={type:4,id:this._id,opId:r,parentId:e,parentKey:t,data:{}};for(let[e,t]of(i.push(o),this.#ec))tg(t)?i.push(...t._toOps(this._id,e,n)):o.data[e]=t;return i}static _deserialize([t,n],r,i){let o=new e(n.data);return o._attach(t,i),this._deserializeChildren(o,r,i)}static _deserializeChildren(e,t,n){let r=t.get(e_(e._id));if(void 0===r)return e;for(let[i,o]of r){let r=function([e,t],n,r){switch(t.type){case 0:return th._deserialize([e,t],n,r);case 1:return tr._deserialize([e,t],n,r);case 2:return tu._deserialize([e,t],n,r);case 3:return t.data;default:throw Error("Unexpected CRDT type")}}([i,o],t,n);ty(r)&&r._setParentLink(e,o.parentKey),e.#ec.set(o.parentKey,r),e.invalidate()}return e}_attach(e,t){for(let[n,r]of(super._attach(e,t),this.#ec))tg(r)&&r._attach(t.generateId(),t)}_attachChild(e,t){let n;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,opId:i,parentKey:o}=e,s=tf(e);if(void 0!==this._pool.getNode(r))return this.#eu.get(o)===i&&this.#eu.delete(o),{modified:!1};if(0===t)this.#eu.set(o,e_(i));else if(void 0===this.#eu.get(o));else if(this.#eu.get(o)===i)return this.#eu.delete(o),{modified:!1};else return{modified:!1};let a=e_(this._id),d=this.#ec.get(o);return tg(d)?(n=d._toOps(a,o),d._detach()):n=void 0===d?[{type:6,id:a,key:o}]:[{type:3,id:a,data:{[o]:d}}],this.#ec.set(o,s),this.invalidate(),ty(s)&&(s._setParentLink(this,o),s._attach(r,this._pool)),{reverse:n,modified:{node:this,type:"LiveObject",updates:{[o]:{type:"update"}}}}}_detachChild(e){if(e){let t=e_(this._id),n=e_(e._parentKey),r=e._toOps(t,n,this._pool);for(let[t,n]of this.#ec)n===e&&(this.#ec.delete(t),this.invalidate());return e._detach(),{modified:{node:this,type:"LiveObject",updates:{[n]:{type:"delete"}}},reverse:r}}return{modified:!1}}_detach(){for(let e of(super._detach(),this.#ec.values()))tg(e)&&e._detach()}_apply(e,t){return 3===e.type?this.#ep(e,t):6===e.type?this.#ef(e,t):super._apply(e,t)}_serialize(){let e={};for(let[t,n]of this.#ec)tg(n)||(e[t]=n);return"HasParent"===this.parent.type&&this.parent.node._id?{type:0,parentId:this.parent.node._id,parentKey:this.parent.key,data:e}:{type:0,data:e}}#ep(e,t){let n=!1,r=e_(this._id),i=[],o={type:3,id:r,data:{}};for(let t in e.data){let e=this.#ec.get(t);tg(e)?(i.push(...e._toOps(r,t)),e._detach()):void 0!==e?o.data[t]=e:void 0===e&&i.push({type:6,id:r,key:t})}let s={};for(let r in e.data){let i=e.data[r];if(void 0===i)continue;if(t)this.#eu.set(r,e_(e.opId));else if(void 0===this.#eu.get(r))n=!0;else{if(this.#eu.get(r)!==e.opId)continue;this.#eu.delete(r);continue}let o=this.#ec.get(r);tg(o)&&o._detach(),n=!0,s[r]={type:"update"},this.#ec.set(r,i),this.invalidate()}return 0!==Object.keys(o.data).length&&i.unshift(o),n?{modified:{node:this,type:"LiveObject",updates:s},reverse:i}:{modified:!1}}#ef(e,t){let n=e.key;if(!1===this.#ec.has(n)||!t&&void 0!==this.#eu.get(n))return{modified:!1};let r=this.#ec.get(n),i=e_(this._id),o=[];return tg(r)?(o=r._toOps(i,e.key),r._detach()):void 0!==r&&(o=[{type:3,id:i,data:{[n]:r}}]),this.#ec.delete(n),this.invalidate(),{modified:{node:this,type:"LiveObject",updates:{[e.key]:{type:"delete"}}},reverse:o}}toObject(){return Object.fromEntries(this.#ec)}set(e,t){this._pool?.assertStorageIsWritable(),this.update({[e]:t})}get(e){return this.#ec.get(e)}delete(e){let t;this._pool?.assertStorageIsWritable();let n=this.#ec.get(e);if(void 0===n)return;if(void 0===this._pool||void 0===this._id){tg(n)&&n._detach(),this.#ec.delete(e),this.invalidate();return}tg(n)?(n._detach(),t=n._toOps(this._id,e)):t=[{type:3,data:{[e]:n},id:this._id}],this.#ec.delete(e),this.invalidate();let r=new Map;r.set(this._id,{node:this,type:"LiveObject",updates:{[e]:{type:"delete"}}}),this._pool.dispatch([{type:6,key:e,id:this._id,opId:this._pool.generateOpId()}],t,r)}update(e){if(this._pool?.assertStorageIsWritable(),void 0===this._pool||void 0===this._id){for(let t in e){let n=e[t];if(void 0===n)continue;let r=this.#ec.get(t);tg(r)&&r._detach(),tg(n)&&n._setParentLink(this,t),this.#ec.set(t,n),this.invalidate()}return}let t=[],n=[],r=this._pool.generateOpId(),i={},o={id:this._id,type:3,data:{}},s={};for(let a in e){let d=e[a];if(void 0===d)continue;let c=this.#ec.get(a);if(tg(c)?(n.push(...c._toOps(this._id,a)),c._detach()):void 0===c?n.push({type:6,id:this._id,key:a}):o.data[a]=c,tg(d)){d._setParentLink(this,a),d._attach(this._pool.generateId(),this._pool);let e=d._toOps(this._id,a,this._pool),n=e.find(e=>e.parentId===this._id);n&&this.#eu.set(a,e_(n.opId)),t.push(...e)}else i[a]=d,this.#eu.set(a,r);this.#ec.set(a,d),this.invalidate(),s[a]={type:"update"}}0!==Object.keys(o.data).length&&n.unshift(o),0!==Object.keys(i).length&&t.unshift({opId:r,id:this._id,type:3,data:i});let a=new Map;a.set(this._id,{node:this,type:"LiveObject",updates:s}),this._pool.dispatch(t,n,a)}toImmutable(){return super.toImmutable()}toTreeNode(e){return super.toTreeNode(e)}_toTreeNode(e){let t=this._id??eo();return{type:"LiveObject",id:t,key:e,payload:Array.from(this.#ec.entries()).map(([e,n])=>tg(n)?n.toTreeNode(e):{type:"Json",id:`${t}:${e}`,key:e,payload:n})}}_toImmutable(){let e={};for(let[t,n]of this.#ec)e[t]=ty(n)?n.toImmutable():n;return e}clone(){return new e(Object.fromEntries(Array.from(this.#ec).map(([e,t])=>[e,ty(t)?t.clone():k(t)])))}};function tp(e){return tw(tf(e))}function tf(e){switch(e.type){case 8:return e.data;case 4:return new th(e.data);case 7:return new tu;case 2:return new tr([]);default:return eb(e,"Unknown creation Op")}}function tm([e,t],n,r){switch(t.type){case 0:return th._deserialize([e,t],n,r);case 1:return tr._deserialize([e,t],n,r);case 2:return tu._deserialize([e,t],n,r);case 3:return tt._deserialize([e,t],n,r);default:throw Error("Unexpected CRDT type")}}function ty(e){return tv(e)||e instanceof tu||tb(e)}function tg(e){return ty(e)||e instanceof tt}function tv(e){return e instanceof tr}function tb(e){return e instanceof th}function t_(e){return e instanceof tt?e.data:e instanceof tr||e instanceof tu||e instanceof th?e:eb(e,"Unknown AbstractCrdt")}function tw(e){return e instanceof th||e instanceof tu||e instanceof tr?e:new tt(e)}function tE(e,t){if(void 0===e)return t;if("LiveObject"===e.type&&"LiveObject"===t.type||"LiveMap"===e.type&&"LiveMap"===t.type){let n=e.updates;for(let[e,r]of O(t.updates))n[e]=r;return{...t,updates:n}}if("LiveList"===e.type&&"LiveList"===t.type){let n=e.updates;return{...t,updates:n.concat(t.updates)}}return t}var tS=class{#B;#em;#ey;#u;constructor(){this.#B={},this.#em=0,this.#ey=1,this.#u=0}get length(){return this.#u}*[Symbol.iterator](){let e=this.#u,t=this.#em;for(let n=0;n<e;n++)yield this.#B[t+n]}push(e){let t=Array.isArray(e)?e:[e];for(let e of(this.#ey>Number.MAX_SAFE_INTEGER-t.length-1&&T("Deque full"),t))this.#B[this.#ey++-1]=e;this.#u+=t.length}pop(){if(this.#u<1)return;this.#ey--;let e=this.#B[this.#ey-1];return delete this.#B[this.#ey-1],this.#u--,e}pushLeft(e){let t=Array.isArray(e)?e:[e];this.#em<Number.MIN_SAFE_INTEGER+t.length&&T("Deque full");for(let e=t.length-1;e>=0;e--)this.#B[--this.#em]=t[e];this.#u+=t.length}popLeft(){if(this.#u<1)return;let e=this.#B[this.#em];return delete this.#B[this.#em],this.#em++,this.#u--,e}};function tI(e){return Array.isArray(e)}var tT=(e=>(e[e.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",e[e.BROADCAST_EVENT=103]="BROADCAST_EVENT",e[e.FETCH_STORAGE=200]="FETCH_STORAGE",e[e.UPDATE_STORAGE=201]="UPDATE_STORAGE",e[e.FETCH_YDOC=300]="FETCH_YDOC",e[e.UPDATE_YDOC=301]="UPDATE_YDOC",e))(tT||{}),tO=class{#eg;#ev;signal;constructor(){this.#eg=new Z({connections:new Map,presences:new Map}),this.signal=X.from(this.#eg,e=>C(Array.from(this.#eg.get().presences.keys()).map(e=>this.getUser(Number(e))))),this.#ev=new Map}get(){return this.signal.get()}connectionIds(){return this.#eg.get().connections.keys()}clearOthers(){this.#eg.mutate(e=>{e.connections.clear(),e.presences.clear(),this.#ev.clear()})}#eb(e){let t=this.#eg.get(),n=t.connections.get(e),r=t.presences.get(e);if(void 0!==n&&void 0!==r){let{connectionId:e,id:t,info:i}=n,o=eF(n.scopes);return K(R({connectionId:e,id:t,info:i,canWrite:o,canComment:eB(n.scopes),isReadOnly:!o,presence:r}))}}getUser(e){let t=this.#ev.get(e);if(t)return t;let n=this.#eb(e);if(n)return this.#ev.set(e,n),n}#e_(e){this.#ev.delete(e)}setConnection(e,t,n,r){this.#eg.mutate(i=>(i.connections.set(e,K({connectionId:e,id:t,info:n,scopes:r})),!!i.presences.has(e)&&this.#e_(e)))}removeConnection(e){this.#eg.mutate(t=>{t.connections.delete(e),t.presences.delete(e),this.#e_(e)})}setOther(e,t){this.#eg.mutate(n=>(n.presences.set(e,K(R(t))),!!n.connections.has(e)&&this.#e_(e)))}patchOther(e,t){this.#eg.mutate(n=>{let r=n.presences.get(e);if(void 0===r)return!1;let i=B(r,t);return r!==i&&(n.presences.set(e,K(i)),this.#e_(e))})}},tA=class e extends Error{context;constructor(e,t,n){super(e,{cause:n}),this.context=t,this.name="LiveblocksError"}get roomId(){return this.context.roomId}get code(){return this.context.code}static from(t,n){return new e(function(e){switch(e.type){case"ROOM_CONNECTION_ERROR":switch(e.code){case 4001:return"Not allowed to connect to the room";case 4005:return"Room is already full";case 4006:return"Kicked out of the room, because the room ID changed";default:return"Could not connect to the room"}case"CREATE_THREAD_ERROR":return"Could not create new thread";case"DELETE_THREAD_ERROR":return"Could not delete thread";case"EDIT_THREAD_METADATA_ERROR":return"Could not edit thread metadata";case"MARK_THREAD_AS_RESOLVED_ERROR":return"Could not mark thread as resolved";case"MARK_THREAD_AS_UNRESOLVED_ERROR":return"Could not mark thread as unresolved";case"SUBSCRIBE_TO_THREAD_ERROR":return"Could not subscribe to thread";case"UNSUBSCRIBE_FROM_THREAD_ERROR":return"Could not unsubscribe from thread";case"CREATE_COMMENT_ERROR":return"Could not create new comment";case"EDIT_COMMENT_ERROR":return"Could not edit comment";case"DELETE_COMMENT_ERROR":return"Could not delete comment";case"ADD_REACTION_ERROR":return"Could not add reaction";case"REMOVE_REACTION_ERROR":return"Could not remove reaction";case"MARK_INBOX_NOTIFICATION_AS_READ_ERROR":return"Could not mark inbox notification as read";case"DELETE_INBOX_NOTIFICATION_ERROR":return"Could not delete inbox notification";case"MARK_ALL_INBOX_NOTIFICATIONS_AS_READ_ERROR":return"Could not mark all inbox notifications as read";case"DELETE_ALL_INBOX_NOTIFICATIONS_ERROR":return"Could not delete all inbox notifications";case"UPDATE_NOTIFICATION_SETTINGS_ERROR":case"UPDATE_USER_NOTIFICATION_SETTINGS_ERROR":return"Could not update notification settings";case"UPDATE_ROOM_SUBSCRIPTION_SETTINGS_ERROR":return"Could not update room subscription settings";default:return eb(e,"Unhandled case")}}(t),t,n)}};function tk(e,t){return{type:"User",id:`${t.connectionId}`,key:e,payload:{connectionId:t.connectionId,id:t.id,info:t.info,presence:t.presence,isReadOnly:!t.canWrite}}}function tC(e){var t;let n=tR("throttle",e.throttle??100,16,1e3),r=tR("lostConnectionTimeout",e.lostConnectionTimeout??5e3,200,3e4,1e3),i=function(e){if(void 0!==e)return tR("backgroundKeepAliveTimeout",e,15e3)}(e.backgroundKeepAliveTimeout),s="string"==typeof(t=e.baseUrl)&&t.startsWith("http")?t:"https://api.liveblocks.io",a=new G(void 0),m=function(e,t){let n=function(e){let{publicApiKey:t,authEndpoint:n}=e;if(void 0!==n&&void 0!==t)throw Error("You cannot simultaneously use `publicApiKey` and `authEndpoint` options. Please pick one and leave the other option unspecified. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClient");if("string"==typeof t){if(t.startsWith("sk_"))throw Error("Invalid `publicApiKey` option. The value you passed is a secret key, which should not be used from the client. Please only ever pass a public key here. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientPublicKey");if(!t.startsWith("pk_"))throw Error("Invalid key. Please use the public key format: pk_<public key>. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientPublicKey");return{type:"public",publicApiKey:t}}if("string"==typeof n)return{type:"private",url:n};if("function"==typeof n)return{type:"custom",callback:n};if(void 0!==n)throw Error("The `authEndpoint` option must be a string or a function. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientAuthEndpoint");throw Error("Invalid Liveblocks client options. Please provide either a `publicApiKey` or `authEndpoint` option. They cannot both be empty. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClient")}(e),r=new Set,i=[],o=[],s=new Map;function a(e,t){return"comments:read"===e?t.includes("comments:read")||t.includes("comments:write")||t.includes("room:read")||t.includes("room:write"):"room:read"===e&&(t.includes("room:read")||t.includes("room:write"))}async function d(i){let o=e.polyfills?.fetch??("undefined"==typeof window?void 0:window.fetch);if("private"===n.type){if(void 0===o)throw new eP("To use Liveblocks client in a non-DOM environment with a url as auth endpoint, you need to provide a fetch polyfill.");let e=eW((await eG(o,n.url,{room:i.roomId})).token);if(r.has(e.raw))throw new eP("The same Liveblocks auth token was issued from the backend before. Caching Liveblocks tokens is not supported.");return t?.(e.parsed),e}if("custom"===n.type){let e=await n.callback(i.roomId);if(e&&"object"==typeof e){if("string"==typeof e.token){let n=eW(e.token);return t?.(n.parsed),n}else if("string"==typeof e.error){let t=`Authentication failed: ${"reason"in e&&"string"==typeof e.reason?e.reason:"Forbidden"}`;if("forbidden"===e.error)throw new eP(t);throw Error(t)}}throw Error('Your authentication callback function should return a token, but it did not. Hint: the return value should look like: { token: "..." }')}throw Error("Unexpected authentication type. Must be private or custom.")}return{reset:function(){r.clear(),i.length=0,o.length=0,s.clear()},getAuthValue:async function(e){let t;if("public"===n.type)return{type:"public",publicApiKey:n.publicApiKey};let c=function(e){let t=Math.ceil(Date.now()/1e3);for(let n=i.length-1;n>=0;n--){let r=i[n];if(o[n]<=t){i.splice(n,1),o.splice(n,1);continue}if("id"===r.parsed.k)return r;if("acc"===r.parsed.k){if(!e.roomId&&0===Object.entries(r.parsed.perms).length)return r;for(let[t,n]of Object.entries(r.parsed.perms))if(e.roomId){if(t.includes("*")&&e.roomId.startsWith(t.replace("*",""))||e.roomId===t&&a(e.requestedScope,n))return r}else if(t.includes("*")&&a(e.requestedScope,n))return r}}}(e);if(void 0!==c)return{type:"secret",token:c};e.roomId?void 0===(t=s.get(e.roomId))&&(t=d(e),s.set(e.roomId,t)):void 0===(t=s.get("liveblocks-user-token"))&&(t=d(e),s.set("liveblocks-user-token",t));try{let e=await t,n=Math.floor(Date.now()/1e3)+(e.parsed.exp-e.parsed.iat)-30;return r.add(e.raw),"sec-legacy"!==e.parsed.k&&(i.push(e),o.push(n)),{type:"secret",token:e}}finally{e.roomId?s.delete(e.roomId):s.delete("liveblocks-user-token")}}}}(e,e=>{let t="sec-legacy"===e.k?e.id:e.uid;a.set(()=>t)}),y=function({baseUrl:e,authManager:t,fetchPolyfill:n}){let r=new ev(e,n);async function i(e){let n=await r.get(ey`/v2/c/rooms/${e.roomId}/threads/delta`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{since:e.since.toISOString()},{signal:e.signal});return{threads:{updated:n.data.map(c),deleted:n.deletedThreads.map(h)},inboxNotifications:{updated:n.inboxNotifications.map(l),deleted:n.deletedInboxNotifications.map(p)},subscriptions:{updated:n.subscriptions.map(u),deleted:n.deletedSubscriptions.map(f)},requestedAt:new Date(n.meta.requestedAt),permissionHints:n.meta.permissionHints}}async function o(e){let n;e.query&&(n=ec(e.query));try{let i=await r.get(ey`/v2/c/rooms/${e.roomId}/threads`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{cursor:e.cursor,query:n,limit:50});return{threads:i.data.map(c),inboxNotifications:i.inboxNotifications.map(l),subscriptions:i.subscriptions.map(u),nextCursor:i.meta.nextCursor,requestedAt:new Date(i.meta.requestedAt),permissionHints:i.meta.permissionHints}}catch(e){if(e instanceof D&&404===e.status)return{threads:[],inboxNotifications:[],subscriptions:[],nextCursor:null,requestedAt:new Date(Date.now()-216e5),permissionHints:{}};throw e}}async function s(e){let n=e.commentId??es("cm"),i=e.threadId??es("th");return c(await r.post(ey`/v2/c/rooms/${e.roomId}/threads`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{id:i,comment:{id:n,body:e.body,attachmentIds:e.attachmentIds},metadata:e.metadata}))}async function a(e){await r.delete(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function m(e){let n=await r.rawGet(ey`/v2/c/rooms/${e.roomId}/thread-with-notification/${e.threadId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}));if(n.ok){let e=await n.json();return{thread:c(e.thread),inboxNotification:e.inboxNotification?l(e.inboxNotification):void 0,subscription:e.subscription?u(e.subscription):void 0}}if(404===n.status)return{thread:void 0,inboxNotification:void 0,subscription:void 0};throw Error(`There was an error while getting thread ${e.threadId}.`)}async function y(e){return await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/metadata`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),e.metadata)}async function g(e){let n=e.commentId??es("cm");return d(await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{id:n,body:e.body,attachmentIds:e.attachmentIds}))}async function v(e){return d(await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{body:e.body,attachmentIds:e.attachmentIds}))}async function b(e){await r.delete(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function _(e){var n;return{...n=await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}/reactions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{emoji:e.emoji}),createdAt:new Date(n.createdAt)}}async function w(e){await r.delete(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}/reactions/${e.emoji}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function E(e){await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/mark-as-resolved`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function S(e){await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/mark-as-unresolved`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function I(e){return u(await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/subscribe`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId})))}async function T(e){await r.post(ey`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/unsubscribe`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function O(e){let n=e.roomId,i=e.signal,o=e.attachment,s=i?new DOMException(`Upload of attachment ${e.attachment.id} was aborted.`,"AbortError"):void 0;if(i?.aborted)throw s;let a=e=>{if(i?.aborted)throw s;if(e instanceof D&&413===e.status)throw e;return!1},d=[2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3];if(o.size<=5242880)return L(async()=>r.putBlob(ey`/v2/c/rooms/${n}/attachments/${o.id}/upload/${encodeURIComponent(o.name)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:n}),o.file,{fileSize:o.size},{signal:i}),10,d,a);{let e,c=[],l=await L(async()=>r.post(ey`/v2/c/rooms/${n}/attachments/${o.id}/multipart/${encodeURIComponent(o.name)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:n}),void 0,{signal:i},{fileSize:o.size}),10,d,a);try{e=l.uploadId;let u=function(e){let t=[],n=0;for(;n<e.size;){let r=Math.min(n+5242880,e.size);t.push({partNumber:t.length+1,part:e.slice(n,r)}),n=r}return t}(o.file);if(i?.aborted)throw s;for(let e of function(e,t){let n=[];for(let t=0,r=e.length;t<r;t+=5)n.push(e.slice(t,t+5));return n}(u,5)){let s=[];for(let{part:c,partNumber:u}of e)s.push(L(async()=>r.putBlob(ey`/v2/c/rooms/${n}/attachments/${o.id}/multipart/${l.uploadId}/${String(u)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:n}),c,void 0,{signal:i}),10,d,a));c.push(...await Promise.all(s))}if(i?.aborted)throw s;let h=c.sort((e,t)=>e.partNumber-t.partNumber);return r.post(ey`/v2/c/rooms/${n}/attachments/${o.id}/multipart/${e}/complete`,await t.getAuthValue({requestedScope:"comments:read",roomId:n}),{parts:h},{signal:i})}catch(i){if(e&&i?.name&&("AbortError"===i.name||"TimeoutError"===i.name))try{await r.rawDelete(ey`/v2/c/rooms/${n}/attachments/${o.id}/multipart/${e}`,await t.getAuthValue({requestedScope:"comments:read",roomId:n}))}catch(e){}throw i}}}let A=new ea(e=>ei(new er(async n=>{let i=n.flat(),{urls:o}=await r.post(ey`/v2/c/rooms/${e}/attachments/presigned-urls`,await t.getAuthValue({requestedScope:"comments:read",roomId:e}),{attachmentIds:i});return o.map(e=>e??Error("There was an error while getting this attachment's URL"))},{delay:50})));function k(e){return A.getOrCreate(e)}async function C(e){return r.get(ey`/v2/c/rooms/${e.roomId}/subscription-settings`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),void 0,{signal:e.signal})}async function R(e){return r.post(ey`/v2/c/rooms/${e.roomId}/subscription-settings`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),e.settings)}let N=new ea(e=>new er(async n=>{let i=n.flat();return await r.post(ey`/v2/c/rooms/${e}/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read",roomId:e}),{inboxNotificationIds:i}),i},{delay:50}));async function P(e){return N.getOrCreate(e.roomId).get(e.inboxNotificationId)}async function x(e){await r.rawPost(ey`/v2/c/rooms/${e.roomId}/text-mentions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{userId:e.userId,mentionId:e.mentionId})}async function U(e){await r.rawDelete(ey`/v2/c/rooms/${e.roomId}/text-mentions/${e.mentionId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function M(e){return r.rawGet(ey`/v2/c/rooms/${e.roomId}/y-version/${e.versionId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function $(e){await r.rawPost(ey`/v2/c/rooms/${e.roomId}/version`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function K(e){await r.rawPost(ey`/v2/c/rooms/${e.roomId}/text-metadata`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{type:e.type,rootKey:e.rootKey})}async function j(e){let n=await r.post(ey`/v2/c/rooms/${e.roomId}/ai/contextual-prompt`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}),{prompt:e.prompt,context:{beforeSelection:e.context.beforeSelection,selection:e.context.selection,afterSelection:e.context.afterSelection},previous:e.previous},{signal:e.signal});if(!n||0===n.content.length)throw Error("No content returned from server");return n.content[0].text}async function q(e){let n=await r.get(ey`/v2/c/rooms/${e.roomId}/versions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}));return{versions:n.versions.map(({createdAt:e,...t})=>({createdAt:new Date(e),...t})),requestedAt:new Date(n.meta.requestedAt)}}async function z(e){let n=await r.get(ey`/v2/c/rooms/${e.roomId}/versions/delta`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{since:e.since.toISOString()},{signal:e.signal});return{versions:n.versions.map(({createdAt:e,...t})=>({createdAt:new Date(e),...t})),requestedAt:new Date(n.meta.requestedAt)}}async function V(e){let n=await r.rawGet(ey`/v2/c/rooms/${e.roomId}/storage`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}));return await n.json()}async function H(e){return r.rawPost(ey`/v2/c/rooms/${e.roomId}/send-message`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}),{nonce:e.nonce,messages:e.messages})}async function F(e){let n=await r.get(ey`/v2/c/inbox-notifications`,await t.getAuthValue({requestedScope:"comments:read"}),{cursor:e?.cursor,limit:50});return{inboxNotifications:n.inboxNotifications.map(l),threads:n.threads.map(c),subscriptions:n.subscriptions.map(u),nextCursor:n.meta.nextCursor,requestedAt:new Date(n.meta.requestedAt)}}async function B(e){let n=await r.get(ey`/v2/c/inbox-notifications/delta`,await t.getAuthValue({requestedScope:"comments:read"}),{since:e.since.toISOString()},{signal:e.signal});return{inboxNotifications:{updated:n.inboxNotifications.map(l),deleted:n.deletedInboxNotifications.map(p)},threads:{updated:n.threads.map(c),deleted:n.deletedThreads.map(h)},subscriptions:{updated:n.subscriptions.map(u),deleted:n.deletedSubscriptions.map(f)},requestedAt:new Date(n.meta.requestedAt)}}async function W(){let{count:e}=await r.get(ey`/v2/c/inbox-notifications/count`,await t.getAuthValue({requestedScope:"comments:read"}));return e}async function G(){await r.post(ey`/v2/c/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read"}),{inboxNotificationIds:"all"})}async function Y(e){await r.post(ey`/v2/c/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read"}),{inboxNotificationIds:e})}let J=new er(async e=>{let t=e.flat();return await Y(t),t},{delay:50});return{getThreads:o,getThreadsSince:i,createThread:s,getThread:m,deleteThread:a,editThreadMetadata:y,createComment:g,editComment:v,deleteComment:b,addReaction:_,removeReaction:w,markThreadAsResolved:E,markThreadAsUnresolved:S,subscribeToThread:I,unsubscribeFromThread:T,markRoomInboxNotificationAsRead:P,getSubscriptionSettings:C,updateSubscriptionSettings:R,createTextMention:x,deleteTextMention:U,getTextVersion:M,createTextVersion:$,reportTextEditor:K,listTextVersions:q,listTextVersionsSince:z,getAttachmentUrl:function(e){return k(e.roomId).batch.get(e.attachmentId)},uploadAttachment:O,getOrCreateAttachmentUrlsStore:k,streamStorage:V,sendMessages:H,getInboxNotifications:F,getInboxNotificationsSince:B,getUnreadInboxNotificationsCount:W,markAllInboxNotificationsAsRead:G,markInboxNotificationAsRead:async function(e){await J.get(e)},deleteAllInboxNotifications:async function(){await r.delete(ey`/v2/c/inbox-notifications`,await t.getAuthValue({requestedScope:"comments:read"}))},deleteInboxNotification:async function(e){await r.delete(ey`/v2/c/inbox-notifications/${e}`,await t.getAuthValue({requestedScope:"comments:read"}))},getNotificationSettings:async function(e){return r.get(ey`/v2/c/notification-settings`,await t.getAuthValue({requestedScope:"comments:read"}),void 0,{signal:e?.signal})},updateNotificationSettings:async function(e){return r.post(ey`/v2/c/notification-settings`,await t.getAuthValue({requestedScope:"comments:read"}),e)},getUserThreads_experimental:async function(e){let n;e?.query&&(n=ec(e.query));let i=await r.get(ey`/v2/c/threads`,await t.getAuthValue({requestedScope:"comments:read"}),{cursor:e?.cursor,query:n,limit:50});return{threads:i.threads.map(c),inboxNotifications:i.inboxNotifications.map(l),subscriptions:i.subscriptions.map(u),nextCursor:i.meta.nextCursor,requestedAt:new Date(i.meta.requestedAt),permissionHints:i.meta.permissionHints}},getUserThreadsSince_experimental:async function(e){let n=await r.get(ey`/v2/c/threads/delta`,await t.getAuthValue({requestedScope:"comments:read"}),{since:e.since.toISOString()},{signal:e.signal});return{threads:{updated:n.threads.map(c),deleted:n.deletedThreads.map(h)},inboxNotifications:{updated:n.inboxNotifications.map(l),deleted:n.deletedInboxNotifications.map(p)},subscriptions:{updated:n.subscriptions.map(u),deleted:n.deletedSubscriptions.map(f)},requestedAt:new Date(n.meta.requestedAt),permissionHints:n.meta.permissionHints}},executeContextualPrompt:j}}({baseUrl:s,fetchPolyfill:e.polyfills?.fetch||globalThis.fetch?.bind(globalThis),authManager:m}),g=new Map;function _(e){let t=()=>{if(e.unsubs.delete(t)){var n,r;0===e.unsubs.size&&((n=e.room).id,g.delete(n.id),n.destroy())}else v("This leave function was already called. Calling it more than once has no effect.")};return e.unsubs.add(t),{room:e.room,leave:t}}let w=e.resolveUsers,S=tN(()=>!w,"Set the resolveUsers option in createClient to specify user info."),I=ei(new er(async e=>{let t=e.flat(),n=await w?.({userIds:t});return S(),n??t.map(()=>void 0)},{delay:50})),O=e.resolveRoomsInfo,R=tN(()=>!O,"Set the resolveRoomsInfo option in createClient to specify room info."),N=ei(new er(async e=>{let t=e.flat(),n=await O?.({roomIds:t});return R(),n??t.map(()=>void 0)},{delay:50})),x=new Map,U=[],K=new G("synchronized"),j=$();function q(){K.set(U.some(e=>"synchronizing"===e.get())?"synchronizing":U.some(e=>"has-local-changes"===e.get())?"has-local-changes":"synchronized")}function z(){let e=new G("synchronized");U.push(e);let t=e.subscribe(()=>q());return{setSyncStatus:function(t){e.set(t)},destroy:function(){t();let n=U.findIndex(t=>t===e);if(n>-1){let[e]=U.splice(n,1);"synchronized"!==e.get()&&q()}}}}{let t="undefined"!=typeof window?window:void 0;t?.addEventListener("beforeunload",t=>{e.preventUnsavedChanges&&"synchronized"!==K.get()&&t.preventDefault()})}async function V(e){return eX(await y.getNotificationSettings(e))}async function H(e){return eX(await y.updateNotificationSettings(e))}let F=Object.defineProperty({enterRoom:function(t,...a){var d;let c=g.get(t);if(void 0!==c)return _(c);let l=a[0]??{},u=function(e,t){var n,r,i;let o,s,a,d=t.roomId,c=e.initialPresence,l=e.initialStorage,u=t.roomHttpClient,[h,p]=function(){let e="undefined"!=typeof document?document:void 0,t={current:null};function n(){e?.visibilityState==="hidden"?t.current=t.current??Date.now():t.current=null}return e?.addEventListener("visibilitychange",n),[t,()=>{e?.removeEventListener("visibilitychange",n)}]}(),f=new eV({...t.delegates,canZombie:()=>void 0!==t.backgroundKeepAliveTimeout&&null!==h.current&&Date.now()>h.current+t.backgroundKeepAliveTimeout&&"synchronizing"!==eu()},t.enableDebugLogging),m={buffer:{flushTimerID:void 0,lastFlushedAt:0,presenceUpdates:{type:"full",data:c},messages:[],storageOperations:[]},staticSessionInfoSig:new G(null),dynamicSessionInfoSig:new G(null),myPresence:new Y(c),others:new tO,initialStorage:l,idFactory:null,yjsProvider:void 0,yjsProviderDidChange:$(),pool:function(e,t){let{getCurrentConnectionId:n,onDispatch:r,isStorageWritable:i=()=>!0}=t,o=0,s=0,a=new Map;return{roomId:e,nodes:a,getNode:e=>a.get(e),addNode:(e,t)=>void a.set(e,t),deleteNode:e=>void a.delete(e),generateId:()=>`${n()}:${o++}`,generateOpId:()=>`${n()}:${s++}`,dispatch(e,t,n){r?.(e,t,n)},assertStorageIsWritable:()=>{if(!i())throw Error("Cannot write to storage with a read only user, please ensure the user has write permissions")}}}(d,{getCurrentConnectionId:function(){let e=m.dynamicSessionInfoSig.get();if(e)return e.actor;throw Error("Internal. Tried to get connection id but connection was never open")},onDispatch:function(e,t,n){if(m.activeBatch){for(let t of e)m.activeBatch.ops.push(t);for(let[e,t]of n)m.activeBatch.updates.storageUpdates.set(e,tE(m.activeBatch.updates.storageUpdates.get(e),t));m.activeBatch.reverseOps.pushLeft(t)}else z(t),m.redoStack.length=0,Q(e),V({storageUpdates:n})},isStorageWritable:function(){let e=m.dynamicSessionInfoSig.get()?.scopes;return void 0===e||eF(e)}}),root:void 0,undoStack:[],redoStack:[],pausedHistory:null,activeBatch:null,unacknowledgedOps:new Map,opStackTraces:void 0},y=!1;f.events.onMessage.subscribe(function(e){if("string"!=typeof e.data)return;let t=function(e){let t=A(e);return void 0===t?null:tI(t)?C(t.map(e=>J(e))):C([J(t)])}(e.data);if(null===t||0===t.length)return;let n={storageUpdates:new Map,others:[]};for(let e of t)switch(e.type){case 101:{let t=function(e){m.others.setConnection(e.actor,e.id,e.info,e.scopes),m.buffer.messages.push({type:100,data:m.myPresence.get(),targetActor:e.actor}),Z();let t=m.others.getUser(e.actor);return t?{type:"enter",user:t}:void 0}(e);t&&n.others.push(t);break}case 100:{let t=function(e){if(void 0!==e.targetActor){let t=m.others.getUser(e.actor);m.others.setOther(e.actor,e.data);let n=m.others.getUser(e.actor);if(void 0===t&&void 0!==n)return{type:"enter",user:n}}else m.others.patchOther(e.actor,e.data);let t=m.others.getUser(e.actor);return t?{type:"update",updates:e.data,user:t}:void 0}(e);t&&n.others.push(t);break}case 103:{let t=m.others.get();g.customEvent.notify({connectionId:e.actor,user:e.actor<0?null:t.find(t=>t.connectionId===e.actor)??null,event:e.event});break}case 102:{let t=function(e){let t=m.others.getUser(e.actor);return t?(m.others.removeConnection(e.actor),{type:"leave",user:t}):null}(e);t&&n.others.push(t);break}case 300:g.ydoc.notify(e);break;case 104:n.others.push(function(e){var t;let n;for(let r of(m.dynamicSessionInfoSig.set({actor:e.actor,nonce:e.nonce,scopes:e.scopes}),t=e.actor,n=0,m.idFactory=()=>`${t}:${n++}`,K(),m.others.connectionIds()))void 0===e.users[r]&&m.others.removeConnection(r);for(let t in e.users){let n=e.users[t],r=Number(t);m.others.setConnection(r,n.id,n.info,n.scopes)}return{type:"reset"}}(e));break;case 200:er(e);break;case 201:for(let[t,r]of H(e.ops,!1).updates.storageUpdates)n.storageUpdates.set(t,tE(n.storageUpdates.get(t),r));break;case 299:E("Storage mutation rejection error",e.reason);break;case 400:case 407:case 401:case 408:case 405:case 406:case 402:case 403:case 404:g.comments.notify(e)}V(n)}),f.events.statusDidChange.subscribe(function(e){let t=f.authValue;if(null!==t){let e=eg(t);if(e!==o)if(o=e,"secret"===t.type){let e=t.token.parsed;m.staticSessionInfoSig.set({userId:"sec-legacy"===e.k?e.id:e.uid,userInfo:"sec-legacy"===e.k?e.info:e.ui})}else m.staticSessionInfoSig.set({userId:void 0,userInfo:void 0})}g.status.notify(e),K()}),f.events.statusDidChange.subscribe(function(e){"reconnecting"===e?s=setTimeout(()=>{g.lostConnection.notify("lost"),y=!0,m.others.clearOthers(),V({others:[{type:"reset"}]})},t.lostConnectionTimeout):(clearTimeout(s),y&&("disconnected"===e?g.lostConnection.notify("failed"):g.lostConnection.notify("restored"),y=!1))}),f.events.didConnect.subscribe(function(){m.buffer.presenceUpdates={type:"full",data:{...m.myPresence.get()}},null!==ee&&eo({flush:!1}),Z()}),f.events.didDisconnect.subscribe(function(){clearTimeout(m.buffer.flushTimerID)}),f.events.onConnectionError.subscribe(({message:e,code:n})=>{let r=new tA(e,{type:"ROOM_CONNECTION_ERROR",code:n,roomId:d});t.errorEventSource.notify(r)});let g={status:$(),lostConnection:$(),customEvent:$(),self:$(),myPresence:$(),others:$(),storageBatch:$(),history:$(),storageDidLoad:$(),storageStatus:$(),ydoc:$(),comments:$(),roomWillDestroy:$()};async function _(e,t){return u.createTextMention({roomId:d,userId:e,mentionId:t})}async function w(e){return u.deleteTextMention({roomId:d,mentionId:e})}async function S(e,t){await u.reportTextEditor({roomId:d,type:e,rootKey:t})}async function I(){return u.listTextVersions({roomId:d})}async function O(e){return u.listTextVersionsSince({roomId:d,since:e.since,signal:e.signal})}async function R(e){return u.getTextVersion({roomId:d,versionId:e})}async function N(){return u.createTextVersion({roomId:d})}async function D(e){return u.executeContextualPrompt({roomId:d,...e})}function x(e){return!(4*e.length<1048064)&&new TextEncoder().encode(e).length>=1048064}function L(e){let n=t.largeMessageStrategy??"default",r=et(e);if(!x(r))return f.send(r);switch(n){case"default":return void b("Message is too large for websockets, not sending. Configure largeMessageStrategy option to deal with this.");case"split":for(let t of(v("Message is too large for websockets, splitting into smaller chunks"),function* e(t){if(t.length<2)if(201===t[0].type)return void(yield*function* e(t){let{ops:n,...r}=t;if(n.length<2)throw Error("Cannot split ops into smaller chunks");let i=Math.floor(n.length/2);for(let t of[n.slice(0,i),n.slice(i)]){let n={ops:t,...r},i=et([n]);x(i)?yield*e(n):yield i}}(t[0]));else throw Error("Cannot split into chunks smaller than the allowed message size");let n=Math.floor(t.length/2);for(let r of[t.slice(0,n),t.slice(n)]){let t=et(r);x(t)?yield*e(r):yield t}}(e)))f.send(t);return;case"experimental-fallback-to-http":{v("Message is too large for websockets, so sending over HTTP instead");let t=m.dynamicSessionInfoSig.get()?.nonce??T("Session is not authorized to send message over HTTP");u.sendMessages({roomId:d,nonce:t,messages:e}).then(e=>{e.ok||403!==e.status||f.reconnect()});return}}}let U=X.from(m.staticSessionInfoSig,m.dynamicSessionInfoSig,m.myPresence,(e,t,n)=>{if(null===e||null===t)return null;{let r=eF(t.scopes);return{connectionId:t.actor,id:e.userId,info:e.userInfo,presence:n,canWrite:r,canComment:eB(t.scopes)}}});function K(){let e=U.get();null!==e&&e!==a&&(g.self.notify(e),a=e)}let j=X.from(U,e=>null!==e?tk("Me",e):null);function q(e){m.undoStack.length>=50&&m.undoStack.shift(),m.undoStack.push(e),W()}function z(e){null!==m.pausedHistory?m.pausedHistory.pushLeft(e):q(e)}function V(e){let t=e.storageUpdates,n=e.others;if(void 0!==n&&n.length>0){let e=m.others.get();for(let t of n)g.others.notify({...t,others:e})}if(e.presence&&(K(),g.myPresence.notify(m.myPresence.get())),void 0!==t&&t.size>0){let e=Array.from(t.values());g.storageBatch.notify(e)}ep()}function H(e,t){let n={reverse:new tS,storageUpdates:new Map,presence:!1},r=new Set,i=e.map(e=>"presence"===e.type||e.opId?e:{...e,opId:m.pool.generateOpId()});for(let e of i)if("presence"===e.type){let t={type:"presence",data:{}};for(let n in e.data)t.data[n]=m.myPresence.get()[n];if(m.myPresence.patch(e.data),null===m.buffer.presenceUpdates)m.buffer.presenceUpdates={type:"partial",data:e.data};else for(let t in e.data)m.buffer.presenceUpdates.data[t]=e.data[t];n.reverse.pushLeft(t),n.presence=!0}else{let i;if(t)i=0;else{let t=e_(e.opId);i=m.unacknowledgedOps.delete(t)?2:1}let o=function(e,t){if(5===e.type&&"ACK"===e.id)return{modified:!1};switch(e.type){case 6:case 3:case 5:{let n=m.pool.nodes.get(e.id);if(void 0===n)return{modified:!1};return n._apply(e,0===t)}case 1:{let n=m.pool.nodes.get(e.id);if(void 0===n)return{modified:!1};if("HasParent"===n.parent.type&&tv(n.parent.node))return n.parent.node._setChildKey(e5(e.parentKey),n,t);return{modified:!1}}case 4:case 2:case 7:case 8:{if(void 0===e.parentId)return{modified:!1};let n=m.pool.nodes.get(e.parentId);if(void 0===n)return{modified:!1};return n._attachChild(e,t)}}}(e,i);if(o.modified){let t=o.modified.node._id;t&&r.has(t)||(n.storageUpdates.set(e_(o.modified.node._id),tE(n.storageUpdates.get(e_(o.modified.node._id)),o.modified)),n.reverse.pushLeft(o.reverse)),(2===e.type||7===e.type||4===e.type)&&r.add(e_(e.id))}}return{ops:i,reverse:Array.from(n.reverse),updates:{storageUpdates:n.storageUpdates,presence:n.presence}}}function F(){return m.undoStack.length>0}function B(){return m.redoStack.length>0}function W(){g.history.notify({canUndo:F(),canRedo:B()})}function J(e){return!function(e){return null!==e&&"string"!=typeof e&&"number"!=typeof e&&"boolean"!=typeof e&&!tI(e)}(e)?null:e}function Z(){let e=m.buffer.storageOperations;if(e.length>0){for(let t of e)m.unacknowledgedOps.set(e_(t.opId),t);ep()}if("connected"!==f.getStatus()){m.buffer.storageOperations=[];return}let n=Date.now(),r=n-m.buffer.lastFlushedAt;if(r>=t.throttleDelay){let e=function(){let e=[];for(let t of(m.buffer.presenceUpdates&&e.push("full"===m.buffer.presenceUpdates.type?{type:100,targetActor:-1,data:m.buffer.presenceUpdates.data}:{type:100,data:m.buffer.presenceUpdates.data}),m.buffer.messages))e.push(t);return m.buffer.storageOperations.length>0&&e.push({type:201,ops:m.buffer.storageOperations}),e}();if(0===e.length)return;L(e),m.buffer={flushTimerID:void 0,lastFlushedAt:n,messages:[],storageOperations:[],presenceUpdates:null}}else clearTimeout(m.buffer.flushTimerID),m.buffer.flushTimerID=setTimeout(Z,t.throttleDelay-r)}function Q(e){let{storageOperations:t}=m.buffer;for(let n of e)t.push(n);Z()}let ee=null,en=null;function er(e){var t;let n=new Map(m.unacknowledgedOps);if(0===e.items.length)throw Error("Internal error: cannot load storage without items");void 0!==m.root?function(e){if(void 0===m.root)return;let t=new Map;for(let[e,n]of m.pool.nodes)t.set(e,n._serialize());V(H(function(e,t){let n=[];return e.forEach((e,r)=>{t.get(r)||n.push({type:5,id:r})}),t.forEach((t,r)=>{let i=e.get(r);if(i)0===t.type&&(0!==i.type||et(t.data)!==et(i.data))&&n.push({type:3,id:r,data:t.data}),t.parentKey!==i.parentKey&&n.push({type:1,id:r,parentKey:e_(t.parentKey,"Parent key must not be missing")});else switch(t.type){case 3:n.push({type:8,id:r,parentId:t.parentId,parentKey:t.parentKey,data:t.data});break;case 1:n.push({type:2,id:r,parentId:t.parentId,parentKey:t.parentKey});break;case 0:if(void 0===t.parentId||void 0===t.parentKey)throw Error("Internal error. Cannot serialize storage root into an operation");n.push({type:4,id:r,parentId:t.parentId,parentKey:t.parentKey,data:t.data});break;case 2:n.push({type:7,id:r,parentId:t.parentId,parentKey:t.parentKey})}}),n}(t,new Map(e)),!1).updates)}(e.items):m.root=th._fromItems(e.items,m.pool);let r=U.get()?.canWrite??!0,i=m.undoStack.length;for(let e in m.initialStorage)void 0===m.root.get(e)&&(r?m.root.set(e,void 0===(t=m.initialStorage[e])?void 0:ty(t)?t.clone():k(t)):v(`Attempted to populate missing storage key '${e}', but current user has no write access`));m.undoStack.length=i,function(e){if(0===e.size)return;let t=[],n=H(Array.from(e.values()),!0);t.push({type:201,ops:n.ops}),V(n.updates),L(t)}(n),en?.(),ep(),g.storageDidLoad.notify()}async function ei(){f.authValue&&er({type:200,items:await u.streamStorage({roomId:d})})}function eo(e){let n=m.buffer.messages;t.unstable_streamData?ei():n.some(e=>200===e.type)||n.push({type:200}),e.flush&&Z()}function ea(){return null===ee&&(eo({flush:!0}),ee=new Promise(e=>{en=e}),ep()),ee}function ed(){let e=m.root;return void 0!==e?e:(ea(),null)}async function ec(){return void 0!==m.root?Promise.resolve({root:m.root}):(await ea(),{root:e_(m.root)})}let el=t.createSyncSource();function eu(){return void 0===m.root?null===ee?"not-loaded":"loading":0===m.unacknowledgedOps.size?"synchronized":"synchronizing"}let eh=eu();function ep(){let e=eu();eh!==e&&(eh=e,g.storageStatus.notify(e)),el.setSyncStatus("synchronizing"===e?"synchronizing":"synchronized")}function ef(){return null!==U.get()}async function em(){for(;!ef();){let{promise:e,resolve:t}=M(),n=eE.self.subscribeOnce(t),r=eE.status.subscribeOnce(t);await e,n(),r()}}function ey(){return null!==ed()}async function ev(){for(;!ey();)await ec()}let ew=X.from(m.others.signal,e=>e.map((e,t)=>tk(`Other ${t}`,e))),eE={status:g.status.observable,lostConnection:g.lostConnection.observable,customEvent:g.customEvent.observable,others:g.others.observable,self:g.self.observable,myPresence:g.myPresence.observable,storage:g.storageBatch.observable,storageBatch:g.storageBatch.observable,history:g.history.observable,storageDidLoad:g.storageDidLoad.observable,storageStatus:g.storageStatus.observable,ydoc:g.ydoc.observable,comments:g.comments.observable,roomWillDestroy:g.roomWillDestroy.observable};async function eS(e){return u.getThreadsSince({roomId:d,since:e.since,signal:e.signal})}async function eI(e){return u.getThreads({roomId:d,query:e?.query,cursor:e?.cursor})}async function eT(e){return u.getThread({roomId:d,threadId:e})}async function eO(e){return u.createThread({roomId:d,threadId:e.threadId,commentId:e.commentId,metadata:e.metadata,body:e.body,attachmentIds:e.attachmentIds})}async function eA(e){return u.deleteThread({roomId:d,threadId:e})}async function ek({metadata:e,threadId:t}){return u.editThreadMetadata({roomId:d,threadId:t,metadata:e})}async function eC(e){return u.markThreadAsResolved({roomId:d,threadId:e})}async function eR(e){return u.markThreadAsUnresolved({roomId:d,threadId:e})}async function eN(e){return u.subscribeToThread({roomId:d,threadId:e})}async function eP(e){return u.unsubscribeFromThread({roomId:d,threadId:e})}async function eD(e){return u.createComment({roomId:d,threadId:e.threadId,commentId:e.commentId,body:e.body,attachmentIds:e.attachmentIds})}async function ex(e){return u.editComment({roomId:d,threadId:e.threadId,commentId:e.commentId,body:e.body,attachmentIds:e.attachmentIds})}async function eL({threadId:e,commentId:t}){return u.deleteComment({roomId:d,threadId:e,commentId:t})}async function eU({threadId:e,commentId:t,emoji:n}){return u.addReaction({roomId:d,threadId:e,commentId:t,emoji:n})}async function eM({threadId:e,commentId:t,emoji:n}){return await u.removeReaction({roomId:d,threadId:e,commentId:t,emoji:n})}async function e$(e,t={}){return u.uploadAttachment({roomId:d,attachment:e,signal:t.signal})}function eK(e){return u.getSubscriptionSettings({roomId:d,signal:e?.signal})}function ej(e){return u.updateSubscriptionSettings({roomId:d,settings:e})}async function eq(e){await u.markRoomInboxNotificationAsRead({roomId:d,inboxNotificationId:e})}let ez=t.createSyncSource();function eH(e){return ez.setSyncStatus("synchronizing"===e?"synchronizing":"synchronized")}return Object.defineProperty({[eY]:{get presenceBuffer(){return k(m.buffer.presenceUpdates?.data??null)},get undoStack(){return k(m.undoStack)},get nodeCount(){return m.pool.nodes.size},getYjsProvider:()=>m.yjsProvider,setYjsProvider(e){m.yjsProvider?.off("status",eH),m.yjsProvider=e,e?.on("status",eH),m.yjsProviderDidChange.notify()},yjsProviderDidChange:m.yjsProviderDidChange.observable,reportTextEditor:S,createTextMention:_,deleteTextMention:w,listTextVersions:I,listTextVersionsSince:O,getTextVersion:R,createTextVersion:N,executeContextualPrompt:D,getSelf_forDevTools:()=>j.get(),getOthers_forDevTools:()=>ew.get(),simulate:{explicitClose:e=>f._privateSendMachineEvent({type:"EXPLICIT_SOCKET_CLOSE",event:e}),rawSend:e=>f.send(e)},attachmentUrlsStore:u.getOrCreateAttachmentUrlsStore(d)},id:d,subscribe:(n=d,r=eE,i=t.errorEventSource,function(e,t,o){var s;if("string"==typeof e&&("my-presence"===(s=e)||"others"===s||"event"===s||"error"===s||"history"===s||"status"===s||"storage-status"===s||"lost-connection"===s||"connection"===s||"comments"===s)){if("function"!=typeof t)throw Error("Second argument must be a callback function");switch(e){case"event":return r.customEvent.subscribe(t);case"my-presence":return r.myPresence.subscribe(t);case"others":return r.others.subscribe(e=>{let{others:n,...r}=e;return t(n,r)});case"error":return i.subscribe(e=>{if(e.roomId===n)return t(e)});case"status":return r.status.subscribe(t);case"lost-connection":return r.lostConnection.subscribe(t);case"history":return r.history.subscribe(t);case"storage-status":return r.storageStatus.subscribe(t);case"comments":return r.comments.subscribe(t);default:return eb(e,`"${String(e)}" is not a valid event name`)}}if(void 0===t||"function"==typeof e)if("function"==typeof e)return r.storageBatch.subscribe(e);else throw Error("Please specify a listener callback");if(tg(e))return o?.isDeep?r.storageBatch.subscribe(n=>{let r=n.filter(t=>(function e(t,n){return t===n||"HasParent"===t.parent.type&&e(t.parent.node,n)})(t.node,e));r.length>0&&t(r)}):r.storageBatch.subscribe(n=>{for(let r of n)r.node._id===e._id&&t(r.node)});throw Error(`${String(e)} is not a value that can be subscribed to.`)}),connect:()=>f.connect(),reconnect:()=>f.reconnect(),disconnect:()=>f.disconnect(),destroy:()=>{let{roomWillDestroy:e,...t}=g;for(let e of Object.values(t))e.dispose();g.roomWillDestroy.notify(),m.yjsProvider?.off("status",eH),el.destroy(),ez.destroy(),p(),f.destroy(),e.dispose()},updatePresence:function(e,t){let n={};for(let t in null===m.buffer.presenceUpdates&&(m.buffer.presenceUpdates={type:"partial",data:{}}),e){let r=e[t];void 0!==r&&(m.buffer.presenceUpdates.data[t]=r,n[t]=m.myPresence.get()[t])}m.myPresence.patch(e),m.activeBatch?(t?.addToHistory&&m.activeBatch.reverseOps.pushLeft({type:"presence",data:n}),m.activeBatch.updates.presence=!0):(Z(),t?.addToHistory&&z([{type:"presence",data:n}]),V({presence:!0}))},updateYDoc:function(e,t,n){let r={type:301,update:e,guid:t,v2:n};m.buffer.messages.push(r),g.ydoc.notify(r),Z()},broadcastEvent:function(e,t={shouldQueueEventIfNotReady:!1}){("connected"===f.getStatus()||t.shouldQueueEventIfNotReady)&&(m.buffer.messages.push({type:103,event:e}),Z())},batch:function(e){let t;if(m.activeBatch)return e();m.activeBatch={ops:[],updates:{storageUpdates:new Map,presence:!1,others:[]},reverseOps:new tS};try{t=e()}finally{let e=m.activeBatch;m.activeBatch=null,e.reverseOps.length>0&&z(Array.from(e.reverseOps)),e.ops.length>0&&(m.redoStack.length=0),e.ops.length>0&&Q(e.ops),V(e.updates),Z()}return t},history:{undo:function(){if(m.activeBatch)throw Error("undo is not allowed during a batch");let e=m.undoStack.pop();if(void 0===e)return;m.pausedHistory=null;let t=H(e,!0);for(let e of(V(t.updates),m.redoStack.push(t.reverse),W(),t.ops))"presence"!==e.type&&m.buffer.storageOperations.push(e);Z()},redo:function(){if(m.activeBatch)throw Error("redo is not allowed during a batch");let e=m.redoStack.pop();if(void 0===e)return;m.pausedHistory=null;let t=H(e,!0);for(let e of(V(t.updates),m.undoStack.push(t.reverse),W(),t.ops))"presence"!==e.type&&m.buffer.storageOperations.push(e);Z()},canUndo:F,canRedo:B,clear:function(){m.undoStack.length=0,m.redoStack.length=0},pause:function(){null===m.pausedHistory&&(m.pausedHistory=new tS)},resume:function(){let e=m.pausedHistory;m.pausedHistory=null,null!==e&&e.length>0&&q(Array.from(e))}},fetchYDoc:function(e,t,n){m.buffer.messages.find(r=>300===r.type&&r.vector===e&&r.guid===t&&r.v2===n)||m.buffer.messages.push({type:300,vector:e,guid:t,v2:n}),Z()},getStorage:ec,getStorageSnapshot:ed,getStorageStatus:eu,isPresenceReady:ef,isStorageReady:ey,waitUntilPresenceReady:P(em),waitUntilStorageReady:P(ev),events:eE,getStatus:()=>f.getStatus(),getSelf:()=>U.get(),getPresence:()=>m.myPresence.get(),getOthers:()=>m.others.get(),getThreads:eI,getThreadsSince:eS,getThread:eT,createThread:eO,deleteThread:eA,editThreadMetadata:ek,markThreadAsResolved:eC,markThreadAsUnresolved:eR,subscribeToThread:eN,unsubscribeFromThread:eP,createComment:eD,editComment:ex,deleteComment:eL,addReaction:eU,removeReaction:eM,prepareAttachment:function(e){return{type:"localAttachment",status:"idle",id:es("at"),name:e.name,size:e.size,mimeType:e.type,file:e}},uploadAttachment:e$,getAttachmentUrl:function(e){return u.getAttachmentUrl({roomId:d,attachmentId:e})},getNotificationSettings:eK,getSubscriptionSettings:eK,updateNotificationSettings:ej,updateSubscriptionSettings:ej,markInboxNotificationAsRead:eq},eY,{enumerable:!1})}({initialPresence:("function"==typeof l.initialPresence?l.initialPresence(t):l.initialPresence)??{},initialStorage:("function"==typeof l.initialStorage?l.initialStorage(t):l.initialStorage)??{}},{roomId:t,throttleDelay:n,lostConnectionTimeout:r,backgroundKeepAliveTimeout:i,polyfills:e.polyfills,delegates:e.mockedDelegates??{createSocket:(d=e.polyfills?.WebSocket,e=>{let n=d??("undefined"==typeof WebSocket?void 0:WebSocket);if(void 0===n)throw new eP("To use Liveblocks client in a non-DOM environment, you need to provide a WebSocket polyfill.");let r=new URL(s);if(r.protocol="http:"===r.protocol?"ws":"wss",r.pathname="/v7",r.searchParams.set("roomId",t),"secret"===e.type)r.searchParams.set("tok",e.token.raw);else{if("public"!==e.type)return eb(e,"Unhandled case");r.searchParams.set("pubkey",e.publicApiKey)}return r.searchParams.set("version",o||"dev"),new n(r.toString())}),authenticate:async()=>m.getAuthValue({requestedScope:"room:read",roomId:t})},enableDebugLogging:e.enableDebugLogging,baseUrl:s,errorEventSource:j,largeMessageStrategy:e.largeMessageStrategy??(e.unstable_fallbackToHTTP?"experimental-fallback-to-http":void 0),unstable_streamData:!!e.unstable_streamData,roomHttpClient:y,createSyncSource:z}),h={room:u,unsubs:new Set};if(g.set(t,h),l.autoConnect??!0){if("undefined"==typeof atob){if(e.polyfills?.atob===void 0)throw Error("You need to polyfill atob to use the client in your environment. Please follow the instructions at https://liveblocks.io/docs/errors/liveblocks-client/atob-polyfill");global.atob=e.polyfills.atob}u.connect()}return _(h)},getRoom:function(e){return g.get(e)?.room||null},logout:function(){for(let{room:t}of(m.reset(),a.set(()=>void 0),g.values())){var e;"initial"!==(e=t.getStatus())&&"disconnected"!==e&&t.reconnect()}},getInboxNotifications:y.getInboxNotifications,getInboxNotificationsSince:y.getInboxNotificationsSince,getUnreadInboxNotificationsCount:y.getUnreadInboxNotificationsCount,markAllInboxNotificationsAsRead:y.markAllInboxNotificationsAsRead,markInboxNotificationAsRead:y.markInboxNotificationAsRead,deleteAllInboxNotifications:y.deleteAllInboxNotifications,deleteInboxNotification:y.deleteInboxNotification,getNotificationSettings:V,updateNotificationSettings:H,resolvers:{invalidateUsers:function(e){I.invalidate(e)},invalidateRoomsInfo:function(e){N.invalidate(e)},invalidateMentionSuggestions:function(){x.clear()}},getSyncStatus:function(){let e=K.get();return"synchronizing"===e?e:"synchronized"},events:{error:j,syncStatus:K},[eY]:{currentUserId:a,mentionSuggestionsCache:x,resolveMentionSuggestions:e.resolveMentionSuggestions,usersStore:I,roomsInfoStore:N,getRoomIds:()=>Array.from(g.keys()),httpClient:y,as:()=>F,createSyncSource:z,emitError:(e,t)=>{let n=tA.from(e,t);j.notify(n)||b(n.message)}}},eY,{enumerable:!1});return F}function tR(e,t,n,r,i){if("number"!=typeof t||t<n||void 0!==r&&t>r)throw Error(void 0!==r?`${e} should be between ${i??n} and ${r}.`:`${e} should be at least ${i??n}.`);return t}function tN(e,...t){return()=>{}}var tP={paragraph:function(e){return"type"in e&&"paragraph"===e.type},text:function(e){return!("type"in e)&&"text"in e&&"string"==typeof e.text},link:function(e){return"type"in e&&"link"===e.type},mention:function(e){return"type"in e&&"mention"===e.type}},tD={paragraph:"block",text:"inline",link:"inline",mention:"inline"};function tx(e){let t=new Set;return!function(e,t,n){if(!e||!e?.content)return;let r="string"==typeof t?t:void 0,i=r?tD[r]:"all",o=r?tP[r]:()=>!0,s="function"==typeof t?t:n;for(let t of e.content)if(("all"===i||"block"===i)&&o(t)&&s?.(t),"all"===i||"inline"===i)for(let e of t.children)o(e)&&s?.(e)}(e,"mention",e=>t.add(e.id)),Array.from(t)}var tL={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},tU=RegExp(Object.keys(tL).map(e=>`\\${e}`).join("|"),"g"),tM=class{#ew;#eE;constructor(e,t){this.#ew=e,this.#eE=t}toString(){return this.#ew.reduce((e,t,n)=>e+function(e){if(e instanceof tM)return e.toString();if(Array.isArray(e))return(e.length<=0?new tM([""],[]):new tM(["",...Array(e.length-1).fill(""),""],e)).toString();return String(e).replace(tU,e=>tL[e])}(e_(this.#eE[n-1]))+t)}};function t$(e,...t){return new tM(e,t)}var tK={_:"\\_","*":"\\*","#":"\\#","`":"\\`","~":"\\~","!":"\\!","|":"\\|","(":"\\(",")":"\\)","{":"\\{","}":"\\}","[":"\\[","]":"\\]"},tj=RegExp(Object.keys(tK).map(e=>`\\${e}`).join("|"),"g"),tq=class{#ew;#eE;constructor(e,t){this.#ew=e,this.#eE=t}toString(){return this.#ew.reduce((e,t,n)=>e+function(e){if(e instanceof tq)return e.toString();if(Array.isArray(e))return(e.length<=0?new tq([""],[]):new tq(["",...Array(e.length-1).fill(""),""],e)).toString();return String(e).replace(tj,e=>tK[e])}(e_(this.#eE[n-1]))+t)}};function tz(e,...t){return new tq(e,t)}function tV(e){let t={};for(let n in e){let r=e[n];void 0!==r&&(t[n]=tH(r))}return t}function tH(e){if(e instanceof th)return tV(e.toObject());if(e instanceof tr)return e.toArray().map(tH);if(e instanceof tu){let t={};for(let[n,r]of e.entries())t[n]=tH(r);return t}return e instanceof tt?e.data:Array.isArray(e)?e.map(tH):S(e)?tV(e):e}function tF(e){if(Array.isArray(e))return new tr(e.map(tF));if(!S(e))return e;{let t={};for(let n in e){let r=e[n];void 0!==r&&(t[n]=tF(r))}return new th(t)}}var tB=[1e3,2e3,4e3,8e3,1e4];function tW(e,t,n){let r=performance.now(),i="undefined"!=typeof document?document:void 0,o="undefined"!=typeof window?window:void 0,s=n?.maxStaleTimeMs??Number.POSITIVE_INFINITY,a={inForeground:i?.visibilityState!=="hidden",lastSuccessfulPollAt:r,count:0,backoff:0};function d(){return a.count>0&&a.inForeground}let c=new eS({}).addState("@idle").addState("@enabled").addState("@polling");function l(){d()?c.send({type:"START"}):c.send({type:"STOP"})}function u(){performance.now()-a.lastSuccessfulPollAt>s&&c.send({type:"POLL"})}function h(e){a.inForeground=e,l(),u()}function p(){h(i?.visibilityState!=="hidden")}return c.addTransitions("@idle",{START:"@enabled"}),c.addTransitions("@enabled",{STOP:"@idle",POLL:"@polling"}),c.addTimedTransition("@enabled",()=>Math.max(0,a.lastSuccessfulPollAt+t-performance.now())+a.backoff,"@polling"),c.onEnterAsync("@polling",async(t,n)=>{await e(n),n.aborted||(a.lastSuccessfulPollAt=performance.now())},()=>({target:d()?"@enabled":"@idle",effect:()=>{a.backoff=0}}),()=>({target:d()?"@enabled":"@idle",effect:()=>{a.backoff=tB.find(e=>e>a.backoff)??tB[tB.length-1]}}),3e4),i?.addEventListener("visibilitychange",p),o?.addEventListener("online",p),o?.addEventListener("focus",u),c.start(),{inc:function(){a.count++,l()},dec:function(){a.count--,a.count<0&&(a.count=0),l()},pollNowIfStale:u,markAsStale:function(){a.lastSuccessfulPollAt=performance.now()-s-1},setInForeground:h}}function tG(e,t){if(Object.is(e,t))return!0;let n=Array.isArray(e),r=Array.isArray(t);if(n||r){if(!n||!r)return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!Object.is(e[n],t[n]))return!1;return!0}if(!S(e)||!S(t))return!1;let i=Object.keys(e);return i.length===Object.keys(t).length&&i.every(n=>Object.prototype.hasOwnProperty.call(t,n)&&Object.is(e[n],t[n]))}function tY(e,t){if(!S(e)||!S(t))return tG(e,t);let n=Object.keys(e);return n.length===Object.keys(t).length&&n.every(n=>Object.prototype.hasOwnProperty.call(t,n)&&tG(e[n],t[n]))}var tJ=class e{#B;#eS;constructor(e,t){this.#eS=t,this.#B=e}static from(t,n){let r=new e([],n);for(let e of t)r.add(e);return r}static fromAlreadySorted(t,n){return new e(t,n)}clone(){return new e(this.#B.slice(),this.#eS)}add(e){let t=function(e,t,n){let r=0,i=e.length;for(;r<i;){let o=r+(i-r>>1);n(t,e[o])?i=o:r=o+1}return r}(this.#B,e,this.#eS);this.#B.splice(t,0,e)}remove(e){let t=this.#B.indexOf(e);return t>=0&&(this.#B.splice(t,1),!0)}get length(){return this.#B.length}*filter(e){for(let t of this.#B)e(t)&&(yield t)}[Symbol.iterator](){return this.#B[Symbol.iterator]()}};function tX(e,t){return"string"==typeof e?`${e}:${t}`:`${e.kind}:${e.subjectId}`}var tZ=(e=>(e.Lexical="lexical",e.TipTap="tiptap",e.BlockNote="blocknote",e))(tZ||{});!function(e,t,n){let r=Symbol.for(e),d=`${t||"dev"} (esm)`;s[r]?s[r]===d||a(`Multiple copies of Liveblocks are being loaded in your project. This will cause issues! See https://liveblocks.io/docs/errors/dupes 

Conflicts:
- ${e} ${s[r]} (already loaded)
- ${e} ${d} (trying to load this now)`):s[r]=d,t&&o&&t!==o&&a(`Cross-linked versions of Liveblocks found, which will cause issues! See https://liveblocks.io/docs/errors/cross-linked 

Conflicts:
- ${i} is at ${o}
- ${e} is at ${t}

Always upgrade all Liveblocks packages to the same version number.`)}(i,o,"esm")}}]);