"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3572],{4844:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(50628),o=n(6341),i=n(64826),l=n(84268),a=n(6024),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=c||f&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},13859:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},17691:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(50628),i=n(84268),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},29823:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(50628),i=n(84268),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},37484:(e,t,n)=>{n.d(t,{UE:()=>ed,ll:()=>el,rD:()=>eh,UU:()=>ec,jD:()=>ef,ER:()=>ep,cY:()=>ea,BN:()=>eu,Ej:()=>es});let r=["top","right","bottom","left"],o=Math.min,i=Math.max,l=Math.round,a=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function y(e){return"y"===e?"height":"width"}function m(e){return["top","bottom"].includes(d(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>s[e])}function g(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function x(e,t,n){let r,{reference:o,floating:i}=e,l=m(t),a=h(m(t)),u=y(a),c=d(t),s="y"===l,f=o.x+o.width/2-i.width/2,v=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:v};break;case"left":r={x:o.x-i.width,y:v};break;default:r={x:o.x,y:o.y}}switch(p(t)){case"start":r[a]-=g*(n&&s?-1:1);break;case"end":r[a]+=g*(n&&s?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=x(c,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:y}=a[n],{x:m,y:v,data:g,reset:w}=await y({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,f=null!=v?v:f,p={...p,[i]:{...p[i],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=x(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),y=w(h),m=a[p?"floating"===d?"reference":"floating":d],v=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},O=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:x,strategy:u}):g);return{top:(v.top-O.top+y.top)/E.y,bottom:(O.bottom-v.bottom+y.bottom)/E.y,left:(v.left-O.left+y.left)/E.x,right:(O.right-v.right+y.right)/E.x}}function _(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return r.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=d(n),a=p(n),u="y"===m(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,h=f(t,e),{mainAxis:y,crossAxis:v,alignmentAxis:g}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof g&&(v="end"===a?-1*g:g),u?{x:v*s,y:y*c}:{x:y*c,y:v*s}}function R(){return"undefined"!=typeof window}function j(e){return C(e)?(e.nodeName||"").toLowerCase():"#document"}function A(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function T(e){var t;return null==(t=(C(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function C(e){return!!R()&&(e instanceof Node||e instanceof A(e).Node)}function L(e){return!!R()&&(e instanceof Element||e instanceof A(e).Element)}function D(e){return!!R()&&(e instanceof HTMLElement||e instanceof A(e).HTMLElement)}function k(e){return!!R()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof A(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=I(),n=L(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function I(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function M(e){return["html","body","#document"].includes(j(e))}function W(e){return A(e).getComputedStyle(e)}function H(e){return L(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||k(e)&&e.host||T(e);return k(t)?t.host:t}function $(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return M(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&N(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=A(o);if(i){let e=U(l);return t.concat(l,l.visualViewport||[],N(o)?o:[],e&&n?$(e):[])}return t.concat(o,$(o,[],n))}function U(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function V(e){let t=W(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function G(e){return L(e)?e:e.contextElement}function Y(e){let t=G(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=V(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let X=u(0);function q(e){let t=A(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function K(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=G(e),a=u(1);t&&(r?L(r)&&(a=Y(r)):a=Y(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===A(l))&&o)?q(l):u(0),s=(i.left+c.x)/a.x,f=(i.top+c.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=A(l),t=r&&L(r)?A(r):r,n=e,o=U(n);for(;o&&r&&t!==n;){let e=Y(o),t=o.getBoundingClientRect(),r=W(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=U(n=A(o))}}return b({width:d,height:p,x:s,y:f})}function Z(e,t){let n=H(e).scrollLeft;return t?t.left+n:K(T(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Z(e,r)),y:r.top+t.scrollTop}}function Q(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=A(e),r=T(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=I();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=T(e),n=H(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Z(e),u=-n.scrollTop;return"rtl"===W(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:u}}(T(e));else if(L(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?Y(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function ee(e){return"static"===W(e).position}function et(e,t){if(!D(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return T(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=A(e);if(F(e))return n;if(!D(e)){let t=z(e);for(;t&&!M(t);){if(L(t)&&!ee(t))return t;t=z(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(j(r))&&ee(r);)r=et(r,t);return r&&M(r)&&ee(r)&&!B(r)?n:r||function(e){let t=z(e);for(;D(t)&&!M(t);){if(B(t))return t;if(F(t))break;t=z(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=T(t),i="fixed"===n,l=K(e,!0,i,t),a={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i)if(("body"!==j(t)||N(o))&&(a=H(t)),r){let e=K(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=Z(o));let s=!o||r||i?u(0):J(o,a);return{x:l.left+a.scrollLeft-c.x-s.x,y:l.top+a.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=T(r),a=!!t&&F(t.floating);if(r===l||a&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0),d=D(r);if((d||!d&&!i)&&(("body"!==j(r)||N(l))&&(c=H(r)),D(r))){let e=K(r);s=Y(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?u(0):J(l,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:T,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=$(e,[],!1).filter(e=>L(e)&&"body"!==j(e)),o=null,i="fixed"===W(e).position,l=i?z(e):e;for(;L(l)&&!M(l);){let t=W(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||N(l)&&!n&&function e(t,n){let r=z(t);return!(r===n||!L(r)||M(r))&&("fixed"===W(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=z(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=Q(t,n,l);return e.top=i(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=i(r.left,e.left),e},Q(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=V(e);return{width:t,height:n}},getScale:Y,isElement:L,isRTL:function(e){return"rtl"===W(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function el(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),h=u||c?[...p?$(p):[],...$(t)]:[];h.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let y=p&&f?function(e,t){let n,r=null,l=T(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),u();let d=e.getBoundingClientRect(),{left:p,top:h,width:y,height:m}=d;if(s||t(),!y||!m)return;let v=a(h),g=a(l.clientWidth-(p+y)),w={rootMargin:-v+"px "+-g+"px "+-a(l.clientHeight-(h+m))+"px "+-a(p)+"px",threshold:i(0,o(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ei(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),u}(p,n):null,m=-1,v=null;s&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let g=d?K(e):null;return d&&function t(){let r=K(e);g&&!ei(g,r)&&n(),g=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==y||y(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(l)}}let ea=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await S(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}},eu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),p={x:n,y:r},y=await O(t,s),v=m(d(l)),g=h(v),w=p[g],b=p[v];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+y[e],r=w-y[t];w=i(n,o(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+y[e],r=b-y[t];b=i(n,o(b,r))}let x=c.fn({...t,[g]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:a,[v]:u}}}}}},ec=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:w,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:_,fallbackStrategy:P="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:R=!0,...j}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let A=d(a),T=m(s),C=d(s)===s,L=await (null==w.isRTL?void 0:w.isRTL(b.floating)),D=_||(C||!R?[g(s)]:function(e){let t=g(e);return[v(e),t,v(t)]}(s)),k="none"!==S;!_&&k&&D.push(...function(e,t,n,r){let o=p(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(s,R,S,L));let N=[s,...D],F=await O(t,j),B=[],I=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&B.push(F[A]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),o=h(m(e)),i=y(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=g(l)),[l,g(l)]}(a,c,L);B.push(F[e[0]],F[e[1]])}if(I=[...I,{placement:a,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=N[e];if(t)return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(i=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(P){case"bestFit":{let e=null==(l=I.filter(e=>{if(k){let t=m(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}},es=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,a,{placement:u,rects:c,platform:s,elements:h}=t,{apply:y=()=>{},...v}=f(e,t),g=await O(t,v),w=d(u),b=p(u),x="y"===m(u),{width:E,height:_}=c.floating;"top"===w||"bottom"===w?(l=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(h.floating))?"start":"end")?"left":"right"):(a=w,l="end"===b?"top":"bottom");let P=_-g.top-g.bottom,S=E-g.left-g.right,R=o(_-g[l],P),j=o(E-g[a],S),A=!t.middlewareData.shift,T=R,C=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=P),A&&!b){let e=i(g.left,0),t=i(g.right,0),n=i(g.top,0),r=i(g.bottom,0);x?C=E-2*(0!==e||0!==t?e+t:i(g.left,g.right)):T=_-2*(0!==n||0!==r?n+r:i(g.top,g.bottom))}await y({...t,availableWidth:C,availableHeight:T});let L=await s.getDimensions(h.floating);return E!==L.width||_!==L.height?{reset:{rects:!0}}:{}}}},ef=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=_(await O(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=_(await O(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}},ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:v=0}=f(e,t)||{};if(null==d)return{};let g=w(v),b={x:n,y:r},x=h(m(l)),E=y(x),O=await u.getDimensions(d),_="y"===x,P=_?"clientHeight":"clientWidth",S=a.reference[E]+a.reference[x]-b[x]-a.floating[E],R=b[x]-a.reference[x],j=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),A=j?j[P]:0;A&&await (null==u.isElement?void 0:u.isElement(j))||(A=c.floating[P]||a.floating[E]);let T=A/2-O[E]/2-1,C=o(g[_?"top":"left"],T),L=o(g[_?"bottom":"right"],T),D=A-O[E]-L,k=A/2-O[E]/2+(S/2-R/2),N=i(C,o(k,D)),F=!s.arrow&&null!=p(l)&&k!==N&&a.reference[E]/2-(k<C?C:L)-O[E]/2<0,B=F?k<C?k-C:k-D:0;return{[x]:b[x]+B,data:{[x]:N,centerOffset:k-N-B,...F&&{alignmentOffset:B}},reset:F}}}),ep=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},p=m(o),y=h(p),v=s[y],g=s[p],w=f(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===y?"height":"width",t=i.reference[y]-i.floating[e]+b.mainAxis,n=i.reference[y]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===y?"width":"height",t=["top","left"].includes(d(o)),n=i.reference[p]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[p])||0)+(t?0:b.crossAxis),r=i.reference[p]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[p])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[y]:v,[p]:g}}}},eh=(e,t,n)=>{let r=new Map,o={platform:eo,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})}},42189:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(50628),o=n(84268);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},48237:(e,t,n)=>{n.d(t,{U:()=>i});var r=n(50628),o=n(72336);function i(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},48733:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(50628),o=n(6024);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},64826:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(50628),o=n(6341),i=n(89840),l=n(6024),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},72336:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(50628);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},79447:(e,t,n)=>{n.d(t,{qW:()=>p});var r,o=n(50628),i=n(13859),l=n(64826),a=n(98064),u=n(72336),c=n(48237),s=n(6024),f="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef((e,t)=>{var n,p;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,...E}=e,O=o.useContext(d),[_,P]=o.useState(null),S=null!=(p=null==_?void 0:_.ownerDocument)?p:null==(n=globalThis)?void 0:n.document,[,R]=o.useState({}),j=(0,a.s)(t,e=>P(e)),A=Array.from(O.layers),[T]=[...O.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(T),L=_?A.indexOf(_):-1,D=O.layersWithOutsidePointerEventsDisabled.size>0,k=L>=C,N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){y("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...O.branches].some(e=>e.contains(t));k&&!n&&(null==g||g(e),null==b||b(e),e.defaultPrevented||null==x||x())},S),F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&y("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...O.branches].some(e=>e.contains(t))&&(null==w||w(e),null==b||b(e),e.defaultPrevented||null==x||x())},S);return(0,c.U)(e=>{L===O.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},S),o.useEffect(()=>{if(_)return m&&(0===O.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),O.layersWithOutsidePointerEventsDisabled.add(_)),O.layers.add(_),h(),()=>{m&&1===O.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[_,S,m,O]),o.useEffect(()=>()=>{_&&(O.layers.delete(_),O.layersWithOutsidePointerEventsDisabled.delete(_),h())},[_,O]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,s.jsx)(l.sG.div,{...E,ref:j,style:{pointerEvents:D?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,N.onPointerDownCapture)})});function h(){let e=new CustomEvent(f);document.dispatchEvent(e)}function y(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},84268:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(50628),o=globalThis?.document?r.useLayoutEffect:()=>{}},84421:(e,t,n)=>{n.d(t,{BN:()=>h,ER:()=>y,Ej:()=>v,UE:()=>w,UU:()=>m,cY:()=>p,jD:()=>g,we:()=>f});var r=n(37484),o=n(50628),i=n(6341),l="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function c(e,t){let n=u(e);return Math.round(t*n)/n}function s(e){let t=o.useRef(e);return l(()=>{t.current=e}),t}function f(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:f=[],platform:d,elements:{reference:p,floating:h}={},transform:y=!0,whileElementsMounted:m,open:v}=e,[g,w]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[b,x]=o.useState(f);a(b,f)||x(f);let[E,O]=o.useState(null),[_,P]=o.useState(null),S=o.useCallback(e=>{e!==T.current&&(T.current=e,O(e))},[]),R=o.useCallback(e=>{e!==C.current&&(C.current=e,P(e))},[]),j=p||E,A=h||_,T=o.useRef(null),C=o.useRef(null),L=o.useRef(g),D=null!=m,k=s(m),N=s(d),F=s(v),B=o.useCallback(()=>{if(!T.current||!C.current)return;let e={placement:t,strategy:n,middleware:b};N.current&&(e.platform=N.current),(0,r.rD)(T.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==F.current};I.current&&!a(L.current,t)&&(L.current=t,i.flushSync(()=>{w(t)}))})},[b,t,n,N,F]);l(()=>{!1===v&&L.current.isPositioned&&(L.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[v]);let I=o.useRef(!1);l(()=>(I.current=!0,()=>{I.current=!1}),[]),l(()=>{if(j&&(T.current=j),A&&(C.current=A),j&&A){if(k.current)return k.current(j,A,B);B()}},[j,A,B,k,D]);let M=o.useMemo(()=>({reference:T,floating:C,setReference:S,setFloating:R}),[S,R]),W=o.useMemo(()=>({reference:j,floating:A}),[j,A]),H=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!W.floating)return e;let t=c(W.floating,g.x),r=c(W.floating,g.y);return y?{...e,transform:"translate("+t+"px, "+r+"px)",...u(W.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,y,W.floating,g.x,g.y]);return o.useMemo(()=>({...g,update:B,refs:M,elements:W,floatingStyles:H}),[g,B,M,W,H])}let d=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:o}).fn(t):{}:n?(0,r.UE)({element:n,padding:o}).fn(t):{}}}),p=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),h=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),y=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),m=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),v=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),g=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),w=(e,t)=>({...d(e),options:[e,t]})},88237:(e,t,n)=>{n.r(t),n.d(t,{__addDisposableResource:()=>N,__assign:()=>i,__asyncDelegator:()=>P,__asyncGenerator:()=>_,__asyncValues:()=>S,__await:()=>O,__awaiter:()=>h,__classPrivateFieldGet:()=>L,__classPrivateFieldIn:()=>k,__classPrivateFieldSet:()=>D,__createBinding:()=>m,__decorate:()=>a,__disposeResources:()=>B,__esDecorate:()=>c,__exportStar:()=>v,__extends:()=>o,__generator:()=>y,__importDefault:()=>C,__importStar:()=>T,__makeTemplateObject:()=>R,__metadata:()=>p,__param:()=>u,__propKey:()=>f,__read:()=>w,__rest:()=>l,__rewriteRelativeImportExtension:()=>I,__runInitializers:()=>s,__setFunctionName:()=>d,__spread:()=>b,__spreadArray:()=>E,__spreadArrays:()=>x,__values:()=>g,default:()=>M});var r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function a(e,t,n,r){var o,i=arguments.length,l=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(l=(i<3?o(l):i>3?o(t,n,l):o(t,n))||l);return i>3&&l&&Object.defineProperty(t,n,l),l}function u(e,t){return function(n,r){t(n,r,e)}}function c(e,t,n,r,o,i){function l(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var a,u=r.kind,c="getter"===u?"get":"setter"===u?"set":"value",s=!t&&e?r.static?e:e.prototype:null,f=t||(s?Object.getOwnPropertyDescriptor(s,r.name):{}),d=!1,p=n.length-1;p>=0;p--){var h={};for(var y in r)h[y]="access"===y?{}:r[y];for(var y in r.access)h.access[y]=r.access[y];h.addInitializer=function(e){if(d)throw TypeError("Cannot add initializers after decoration has completed");i.push(l(e||null))};var m=(0,n[p])("accessor"===u?{get:f.get,set:f.set}:f[c],h);if("accessor"===u){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw TypeError("Object expected");(a=l(m.get))&&(f.get=a),(a=l(m.set))&&(f.set=a),(a=l(m.init))&&o.unshift(a)}else(a=l(m))&&("field"===u?o.unshift(a):f[c]=a)}s&&Object.defineProperty(s,r.name,f),d=!0}function s(e,t,n){for(var r=arguments.length>2,o=0;o<t.length;o++)n=r?t[o].call(e,n):t[o].call(e);return r?n:void 0}function f(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function h(e,t,n,r){return new(n||(n=Promise))(function(o,i){function l(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(l,a)}u((r=r.apply(e,t||[])).next())})}function y(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},l=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return l.next=a(0),l.throw=a(1),l.return=a(2),"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function a(a){return function(u){var c=[a,u];if(n)throw TypeError("Generator is already executing.");for(;l&&(l=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===c[0]||2===c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}}var m=Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function v(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||m(t,e,n)}function g(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function w(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),l=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)l.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return l}function b(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(w(arguments[t]));return e}function x(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),o=0,t=0;t<n;t++)for(var i=arguments[t],l=0,a=i.length;l<a;l++,o++)r[o]=i[l];return r}function E(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function O(e){return this instanceof O?(this.v=e,this):new O(e)}function _(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),i=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),l("next"),l("throw"),l("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),r[Symbol.asyncIterator]=function(){return this},r;function l(e,t){o[e]&&(r[e]=function(t){return new Promise(function(n,r){i.push([e,t,n,r])>1||a(e,t)})},t&&(r[e]=t(r[e])))}function a(e,t){try{var n;(n=o[e](t)).value instanceof O?Promise.resolve(n.value.v).then(u,c):s(i[0][2],n)}catch(e){s(i[0][3],e)}}function u(e){a("next",e)}function c(e){a("throw",e)}function s(e,t){e(t),i.shift(),i.length&&a(i[0][0],i[0][1])}}function P(e){var t,n;return t={},r("next"),r("throw",function(e){throw e}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,o){t[r]=e[r]?function(t){return(n=!n)?{value:O(e[r](t)),done:!1}:o?o(t):t}:o}}function S(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=g(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise(function(r,o){var i,l,a;i=r,l=o,a=(t=e[n](t)).done,Promise.resolve(t.value).then(function(e){i({value:e,done:a})},l)})}}}function R(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var j=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},A=function(e){return(A=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t})(e)};function T(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=A(e),r=0;r<n.length;r++)"default"!==n[r]&&m(t,e,n[r]);return j(t,e),t}function C(e){return e&&e.__esModule?e:{default:e}}function L(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function D(e,t,n,r,o){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?o.call(e,n):o?o.value=n:t.set(e,n),n}function k(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function N(e,t,n){if(null!=t){var r,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(n){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(o=r)}if("function"!=typeof r)throw TypeError("Object not disposable.");o&&(r=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var F="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function B(e){function t(t){e.error=e.hasError?new F(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function o(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(o);if(n.dispose){var i=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(i).then(o,function(e){return t(e),o()})}else r|=1}catch(e){t(e)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function I(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,n,r,o,i){return n?t?".jsx":".js":!r||o&&i?r+o+"."+i.toLowerCase()+"js":e}):e}let M={__extends:o,__assign:i,__rest:l,__decorate:a,__param:u,__esDecorate:c,__runInitializers:s,__propKey:f,__setFunctionName:d,__metadata:p,__awaiter:h,__generator:y,__createBinding:m,__exportStar:v,__values:g,__read:w,__spread:b,__spreadArrays:x,__spreadArray:E,__await:O,__asyncGenerator:_,__asyncDelegator:P,__asyncValues:S,__makeTemplateObject:R,__importStar:T,__importDefault:C,__classPrivateFieldGet:L,__classPrivateFieldSet:D,__classPrivateFieldIn:k,__addDisposableResource:N,__disposeResources:B,__rewriteRelativeImportExtension:I}},93965:(e,t,n)=>{n.d(t,{Mz:()=>D,i3:()=>N,UC:()=>k,bL:()=>L,Bk:()=>m});var r=n(50628),o=n(84421),i=n(37484),l=n(64826),a=n(6024),u=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,a.jsx)(l.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});u.displayName="Arrow";var c=n(98064),s=n(48733),f=n(72336),d=n(84268),p=n(42189),h="Popper",[y,m]=(0,s.A)(h),[v,g]=y(h),w=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,a.jsx)(v,{scope:t,anchor:o,onAnchorChange:i,children:n})};w.displayName=h;var b="PopperAnchor",x=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,u=g(b,n),s=r.useRef(null),f=(0,c.s)(t,s);return r.useEffect(()=>{u.onAnchorChange((null==o?void 0:o.current)||s.current)}),o?null:(0,a.jsx)(l.sG.div,{...i,ref:f})});x.displayName=b;var E="PopperContent",[O,_]=y(E),P=r.forwardRef((e,t)=>{var n,u,s,h,y,m,v,w;let{__scopePopper:b,side:x="bottom",sideOffset:_=0,align:P="center",alignOffset:S=0,arrowPadding:R=0,avoidCollisions:j=!0,collisionBoundary:L=[],collisionPadding:D=0,sticky:k="partial",hideWhenDetached:N=!1,updatePositionStrategy:F="optimized",onPlaced:B,...I}=e,M=g(E,b),[W,H]=r.useState(null),z=(0,c.s)(t,e=>H(e)),[$,U]=r.useState(null),V=(0,p.X)($),G=null!=(v=null==V?void 0:V.width)?v:0,Y=null!=(w=null==V?void 0:V.height)?w:0,X="number"==typeof D?D:{top:0,right:0,bottom:0,left:0,...D},q=Array.isArray(L)?L:[L],K=q.length>0,Z={padding:X,boundary:q.filter(A),altBoundary:K},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:en}=(0,o.we)({strategy:"fixed",placement:x+("center"!==P?"-"+P:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.ll)(...t,{animationFrame:"always"===F})},elements:{reference:M.anchor},middleware:[(0,o.cY)({mainAxis:_+Y,alignmentAxis:S}),j&&(0,o.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===k?(0,o.ER)():void 0,...Z}),j&&(0,o.UU)({...Z}),(0,o.Ej)({...Z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),$&&(0,o.UE)({element:$,padding:R}),T({arrowWidth:G,arrowHeight:Y}),N&&(0,o.jD)({strategy:"referenceHidden",...Z})]}),[er,eo]=C(ee),ei=(0,f.c)(B);(0,d.N)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null==(n=en.arrow)?void 0:n.x,ea=null==(u=en.arrow)?void 0:u.y,eu=(null==(s=en.arrow)?void 0:s.centerOffset)!==0,[ec,es]=r.useState();return(0,d.N)(()=>{W&&es(window.getComputedStyle(W).zIndex)},[W]),(0,a.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ec,"--radix-popper-transform-origin":[null==(h=en.transformOrigin)?void 0:h.x,null==(y=en.transformOrigin)?void 0:y.y].join(" "),...(null==(m=en.hide)?void 0:m.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(O,{scope:b,placedSide:er,onArrowChange:U,arrowX:el,arrowY:ea,shouldHideArrow:eu,children:(0,a.jsx)(l.sG.div,{"data-side":er,"data-align":eo,...I,ref:z,style:{...I.style,animation:et?void 0:"none"}})})})});P.displayName=E;var S="PopperArrow",R={top:"bottom",right:"left",bottom:"top",left:"right"},j=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=_(S,n),i=R[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(u,{...r,ref:t,style:{...r.style,display:"block"}})})});function A(e){return null!==e}j.displayName=S;var T=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,h]=C(a),y={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,v=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+d/2,g="",w="";return"bottom"===p?(g=s?y:"".concat(m,"px"),w="".concat(-d,"px")):"top"===p?(g=s?y:"".concat(m,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(g="".concat(-d,"px"),w=s?y:"".concat(v,"px")):"left"===p&&(g="".concat(u.floating.width+d,"px"),w=s?y:"".concat(v,"px")),{data:{x:g,y:w}}}});function C(e){let[t,n="center"]=e.split("-");return[t,n]}var L=w,D=x,k=P,N=j}}]);