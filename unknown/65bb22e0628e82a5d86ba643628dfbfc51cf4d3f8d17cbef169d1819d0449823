(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1217],{3671:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9207:(e,t,r)=>{"use strict";r.d(t,{LoginFlow:()=>M});var n=r(6024),s=r(70234),i=r(69680),o=r(50628),a=r(98064),l=r(48733),c=r(13859),u=r(17691),d=r(84406),f=r(42189),m=r(64714),h=r(64826),v="Checkbox",[p,x]=(0,l.A)(v),[g,b]=p(v);function y(e){let{__scopeCheckbox:t,checked:r,children:s,defaultChecked:i,disabled:a,form:l,name:c,onCheckedChange:d,required:f,value:m="on",internal_do_not_use_render:h}=e,[p,x]=(0,u.i)({prop:r,defaultProp:null!=i&&i,onChange:d,caller:v}),[b,y]=o.useState(null),[N,w]=o.useState(null),j=o.useRef(!1),k=!b||!!l||!!b.closest("form"),C={checked:p,disabled:a,setChecked:x,control:b,setControl:y,name:c,form:l,value:m,hasConsumerStoppedPropagationRef:j,required:f,defaultChecked:!S(i)&&i,isFormControl:k,bubbleInput:N,setBubbleInput:w};return(0,n.jsx)(g,{scope:t,...C,children:"function"==typeof h?h(C):s})}var N="CheckboxTrigger",w=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:s,onClick:i,...l}=e,{control:u,value:d,disabled:f,checked:m,required:v,setControl:p,setChecked:x,hasConsumerStoppedPropagationRef:g,isFormControl:y,bubbleInput:w}=b(N,r),j=(0,a.s)(t,p),k=o.useRef(m);return o.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>x(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,x]),(0,n.jsx)(h.sG.button,{type:"button",role:"checkbox","aria-checked":S(m)?"mixed":m,"aria-required":v,"data-state":T(m),"data-disabled":f?"":void 0,disabled:f,value:d,...l,ref:j,onKeyDown:(0,c.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,c.m)(i,e=>{x(e=>!!S(e)||!e),w&&y&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});w.displayName=N;var j=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:s,checked:i,defaultChecked:o,required:a,disabled:l,value:c,onCheckedChange:u,form:d,...f}=e;return(0,n.jsx)(y,{__scopeCheckbox:r,checked:i,defaultChecked:o,disabled:l,required:a,onCheckedChange:u,name:s,form:d,value:c,internal_do_not_use_render:e=>{let{isFormControl:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(w,{...f,ref:t,__scopeCheckbox:r}),s&&(0,n.jsx)(A,{__scopeCheckbox:r})]})}})});j.displayName=v;var k="CheckboxIndicator",C=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...i}=e,o=b(k,r);return(0,n.jsx)(m.C,{present:s||S(o.checked)||!0===o.checked,children:(0,n.jsx)(h.sG.span,{"data-state":T(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=k;var E="CheckboxBubbleInput",A=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,...s}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:u,required:m,disabled:v,name:p,value:x,form:g,bubbleInput:y,setBubbleInput:N}=b(E,r),w=(0,a.s)(t,N),j=(0,d.Z)(c),k=(0,f.X)(i);o.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(j!==c&&e){let r=new Event("click",{bubbles:t});y.indeterminate=S(c),e.call(y,!S(c)&&c),y.dispatchEvent(r)}},[y,j,c,l]);let C=o.useRef(!S(c)&&c);return(0,n.jsx)(h.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=u?u:C.current,required:m,disabled:v,name:p,value:x,form:g,...s,tabIndex:-1,ref:w,style:{...s.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function S(e){return"indeterminate"===e}function T(e){return S(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=E;var O=r(3671),_=r(31918);function R(e){let{className:t,...r}=e;return(0,n.jsx)(j,{"data-slot":"checkbox",className:(0,_.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,n.jsx)(C,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,n.jsx)(O.A,{className:"size-3.5"})})})}var L=r(13957);let M=e=>{let{deviceId:t,state:r,user:a}=e,[l,c]=(0,o.useState)(a.termsAccepted),[u,d]=(0,o.useState)(!1),[f,m]=(0,o.useState)(!1),h=async()=>{if(!l)return void L.toast.error("Please accept the Terms of Use to continue");d(!0);try{let e=await fetch("/api/extension/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deviceId:t,state:r,acceptTerms:!a.termsAccepted})});if(!e.ok){let t=await e.json();throw Error(t.message||"Failed to complete login")}let{token:n,redirectUrl:s}=await e.json();m(!0),L.toast.success("Login successful! You can now return to VS Code."),s&&setTimeout(()=>{window.location.href=s},2e3)}catch(e){console.error("Login error:",e),L.toast.error(e instanceof Error?e.message:"Login failed")}finally{d(!1)}};return f?(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(i.aR,{className:"text-center",children:[(0,n.jsx)(i.ZB,{className:"text-green-600",children:"Login Successful!"}),(0,n.jsx)(i.BT,{children:"Your VS Code extension has been authorized successfully."})]}),(0,n.jsxs)(i.Wu,{className:"text-center",children:[(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)("div",{className:"mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center",children:(0,n.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"You can now return to VS Code. The extension should automatically detect the authorization."})]})]})}):(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"Authorize VS Code Extension"}),(0,n.jsxs)(i.BT,{children:["Welcome, ",a.name||a.email,"! Please review and accept our terms to continue."]})]}),(0,n.jsxs)(i.Wu,{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"font-semibold",children:"Terms of Use"}),(0,n.jsxs)("div",{className:"max-h-48 overflow-y-auto rounded border p-4 text-sm",children:[(0,n.jsx)("p",{className:"mb-4",children:"By using the Cubent VS Code extension, you agree to the following terms:"}),(0,n.jsxs)("ul",{className:"list-disc space-y-2 pl-4",children:[(0,n.jsx)("li",{children:"You will use the extension in accordance with our usage policies"}),(0,n.jsx)("li",{children:"You understand that AI-generated code should be reviewed before use"}),(0,n.jsx)("li",{children:"You agree to our data collection and processing practices"}),(0,n.jsx)("li",{children:"You will not use the extension for malicious or harmful purposes"}),(0,n.jsx)("li",{children:'You acknowledge that the service is provided "as is"'})]}),(0,n.jsx)("p",{className:"mt-4 text-xs text-gray-500",children:"For full terms, visit our website's Terms of Service page."})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(R,{id:"terms",checked:l,onCheckedChange:e=>c(e)}),(0,n.jsx)("label",{htmlFor:"terms",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"I accept the Terms of Use"})]}),(0,n.jsx)(s.$,{onClick:h,disabled:!l||u,className:"w-full",children:u?"Authorizing...":"Authorize Extension"}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["Device ID: ",t.slice(0,8),"..."]})})]})]})})}},13859:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},17691:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n,s=r(50628),i=r(84268),o=(n||(n=r.t(s,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,a,l]=function({defaultProp:e,onChange:t}){let[r,n]=s.useState(e),i=s.useRef(r),a=s.useRef(t);return o(()=>{a.current=t},[t]),s.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,n,a]}({defaultProp:t,onChange:r}),c=void 0!==e,u=c?e:i;{let t=s.useRef(void 0!==e);s.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[u,s.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else a(t)},[c,e,a,l])]}Symbol("RADIX:SYNC_STATE")},31918:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i}),r(63410);var n=r(49973);r(13957);var s=r(22928);let i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},33745:(e,t,r)=>{Promise.resolve().then(r.bind(r,9207))},42189:(e,t,r)=>{"use strict";r.d(t,{X:()=>i});var n=r(50628),s=r(84268);function i(e){let[t,r]=n.useState(void 0);return(0,s.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,s;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,s=t.blockSize}else n=e.offsetWidth,s=e.offsetHeight;r({width:n,height:s})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},45707:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(50628);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:u="",children:d,iconNode:f,...m}=e;return(0,n.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:o?24*Number(i)/Number(s):i,className:a("lucide",u),...!d&&!l(m)&&{"aria-hidden":"true"},...m},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:l,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:a("lucide-".concat(s(o(e))),"lucide-".concat(e),l),...c})});return r.displayName=o(e),r}},48733:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,q:()=>i});var n=r(50628),s=r(6024);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,s.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(s){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${s}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let s=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),a=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,c=r?.[e]?.[a]||o,u=n.useMemo(()=>l,Object.values(l));return(0,s.jsx)(c.Provider,{value:u,children:i})};return l.displayName=t+"Provider",[l,function(r,s){let l=s?.[e]?.[a]||o,c=n.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:n})=>{let s=r(e)[`__scope${n}`];return{...t,...s}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(i,...t)]}},64714:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(50628),s=r(98064),i=r(84268),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[s,o]=n.useState(),l=n.useRef(null),c=n.useRef(e),u=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(l.current);u.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=l.current,r=c.current;if(r!==e){let n=u.current,s=a(t);e?f("MOUNT"):"none"===s||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==s?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(s){var e;let t,r=null!=(e=s.ownerDocument.defaultView)?e:window,n=e=>{let n=a(l.current).includes(e.animationName);if(e.target===s&&n&&(f("ANIMATION_END"),!c.current)){let e=s.style.animationFillMode;s.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=e)})}},i=e=>{e.target===s&&(u.current=a(l.current))};return s.addEventListener("animationstart",i),s.addEventListener("animationcancel",n),s.addEventListener("animationend",n),()=>{r.clearTimeout(t),s.removeEventListener("animationstart",i),s.removeEventListener("animationcancel",n),s.removeEventListener("animationend",n)}}f("ANIMATION_END")},[s,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),c=(0,s.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,s=n&&"isReactWarning"in n&&n.isReactWarning;return s?e.ref:(s=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},64826:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>a});var n=r(50628),s=r(6341),i=r(89840),o=r(6024),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s?r:t,{...i,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},69680:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>a,Zp:()=>i,aR:()=>o});var n=r(6024);r(50628);var s=r(31918);function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function a(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},70234:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(6024);r(50628);var s=r(89840),i=r(81197),o=r(31918);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...c}=e,u=l?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(a({variant:r,size:i,className:t})),...c})}},81197:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(49973);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=s(t)||s(n);return o[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},84268:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var n=r(50628),s=globalThis?.document?n.useLayoutEffect:()=>{}},84406:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(50628);function s(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,2913,4499,7358],()=>t(33745)),_N_E=e.O()}]);