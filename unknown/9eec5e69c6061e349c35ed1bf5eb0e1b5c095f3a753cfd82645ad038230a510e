(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7183],{1601:(e,t,s)=>{"use strict";s.d(t,{ExtensionSessionsList:()=>x});var a=s(6024),r=s(70234),n=s(29011),i=s(50247),o=s(50628),l=s(13957),c=s(71499),d=s(45707);let u=(0,d.A)("unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]),h=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var v=s(70645);function x(e){let{activeSessions:t,inactiveSessions:s}=e,[d,x]=(0,o.useState)(null),m=async e=>{x(e);try{(await fetch("/api/extension/sessions?sessionId=".concat(e),{method:"DELETE"})).ok?(l.toast.success("Session disconnected successfully"),window.location.reload()):l.toast.error("Failed to disconnect session")}catch(e){l.toast.error("Failed to disconnect session")}finally{x(null)}},p=e=>new Date(e).toLocaleString(),g=e=>{let t=new Date().getTime()-new Date(e).getTime(),s=Math.floor(t/6e4),a=Math.floor(t/36e5),r=Math.floor(t/864e5);return s<1?"Just now":s<60?"".concat(s,"m ago"):a<24?"".concat(a,"h ago"):"".concat(r,"d ago")};return 0===t.length&&0===s.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)("p",{children:"No extension sessions found."}),(0,a.jsx)("p",{className:"text-sm",children:"Connect your VS Code extension to see sessions here."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[t.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("h3",{className:"font-semibold",children:["Active Sessions (",t.length,")"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.E,{variant:"default",children:"Active"}),(0,a.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{children:["Connected: ",p(e.createdAt)]}),(0,a.jsxs)("span",{children:["Last seen: ",g(e.lastActiveAt)]})]})})]}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>m(e.sessionId),disabled:d===e.sessionId,children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),d===e.sessionId?"Disconnecting...":"Disconnect"]})]},e.id))})]}),t.length>0&&s.length>0&&(0,a.jsx)(i.Separator,{}),s.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("h3",{className:"font-semibold",children:["Past Sessions (",s.length,")"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[s.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg opacity-60",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.E,{variant:"secondary",children:"Inactive"}),(0,a.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{children:["Connected: ",p(e.createdAt)]}),(0,a.jsxs)("span",{children:["Last seen: ",p(e.lastActiveAt)]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Disconnected"})]})]},e.id)),s.length>5&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["... and ",s.length-5," more past sessions"]})]})]})]})}},29011:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(6024);s(50628);var r=s(89840),n=s(81197),i=s(31918);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,asChild:n=!1,...l}=e,c=n?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:s}),t),...l})}},31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n}),s(63410);var a=s(49973);s(13957);var r=s(22928);let n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},50247:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>i});var a=s(6024);s(50628);var r=s(52482),n=s(31918);function i(e){let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,a.jsx)(r.b,{"data-slot":"separator-root",decorative:i,orientation:s,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},52482:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var a=s(50628),r=s(64826),n=s(6024),i="horizontal",o=["horizontal","vertical"],l=a.forwardRef((e,t)=>{var s;let{decorative:a,orientation:l=i,...c}=e,d=(s=l,o.includes(s))?l:i;return(0,n.jsx)(r.sG.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},64826:(e,t,s)=>{"use strict";s.d(t,{hO:()=>l,sG:()=>o});var a=s(50628),r=s(6341),n=s(89840),i=s(6024),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.TL)(`Primitive.${t}`),r=a.forwardRef((e,a)=>{let{asChild:r,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?s:t,{...n,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function l(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},66173:(e,t,s)=>{Promise.resolve().then(s.bind(s,82605)),Promise.resolve().then(s.bind(s,1601)),Promise.resolve().then(s.bind(s,43432)),Promise.resolve().then(s.t.bind(s,35685,23)),Promise.resolve().then(s.bind(s,13957))},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(6024);s(50628);var r=s(89840),n=s(81197),i=s(31918);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:n,className:t})),...c})}},70645:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(45707).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},71499:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(45707).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79242:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(6024);s(50628);var r=s(31918);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},81197:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var a=s(49973);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,i=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],a=null==o?void 0:o[e];if(null===t)return null;let n=r(t)||r(a);return i[e][n]}),c=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return n(e,l,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...o,...c}[t]):({...o,...c})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},82605:(e,t,s)=>{"use strict";s.d(t,{ApiKeyManager:()=>v});var a=s(6024),r=s(70234),n=s(79242),i=s(50628),o=s(13957),l=s(45707);let c=(0,l.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),d=(0,l.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),u=(0,l.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var h=s(84665);function v(e){let{apiKey:t,userId:s}=e,[l,v]=(0,i.useState)(!1),[x,m]=(0,i.useState)(!1),p=async()=>{if(t)try{await navigator.clipboard.writeText(t),o.toast.success("API key copied to clipboard")}catch(e){o.toast.error("Failed to copy API key")}},g=async()=>{m(!0);try{(await fetch("/api/extension/generate-key",{method:"POST"})).ok?(o.toast.success("New API key generated successfully"),window.location.reload()):o.toast.error("Failed to generate new API key")}catch(e){o.toast.error("Failed to generate new API key")}finally{m(!1)}},f=t?"".concat(t.slice(0,12)).concat("*".repeat(t.length-16)).concat(t.slice(-4)):"";return(0,a.jsx)("div",{className:"space-y-4",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Your API Key"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.p,{type:"text",value:l?t:f,readOnly:!0,className:"font-mono text-sm"}),(0,a.jsx)(r.$,{variant:"outline",size:"icon",onClick:()=>v(!l),children:l?(0,a.jsx)(c,{className:"h-4 w-4"}):(0,a.jsx)(d,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{variant:"outline",size:"icon",onClick:p,children:(0,a.jsx)(u,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(r.$,{variant:"outline",onClick:g,disabled:x,className:"flex-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2 ".concat(x?"animate-spin":"")}),x?"Generating...":"Regenerate Key"]})}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("p",{children:"• Keep your API key secure and don't share it with others"}),(0,a.jsx)("p",{children:"• Regenerating will invalidate the current key"}),(0,a.jsx)("p",{children:"• The extension will need to reconnect after regenerating"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No API key generated yet. Connect your extension to generate one automatically."}),(0,a.jsxs)(r.$,{onClick:g,disabled:x,className:"w-full",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2 ".concat(x?"animate-spin":"")}),x?"Generating...":"Generate API Key"]})]})})}},84665:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(45707).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,7651,2913,4499,7358],()=>t(66173)),_N_E=e.O()}]);