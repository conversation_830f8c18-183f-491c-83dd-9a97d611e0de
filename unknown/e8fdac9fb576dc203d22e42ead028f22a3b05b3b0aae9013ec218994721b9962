"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2068,4731],{4937:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{bindSnapshot:function(){return s},createAsyncLocalStorage:function(){return o},createSnapshot:function(){return a}});let i=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw i}getStore(){}run(){throw i}exit(){throw i}enterWith(){throw i}static bind(e){return e}}let r="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return r?new r:new n}function s(e){return r?r.bind(e):n.bind(e)}function a(){return r?r.snapshot():function(e,...t){return e(...t)}}},5659:(e,t,i)=>{function n(e){let{reason:t,children:i}=e;return i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),i(46319)},17087:(e,t,i)=>{i.d(t,{H4:()=>R,_V:()=>y,bL:()=>T});var n=i(50628),r=i(48733),o=i(72336),s=i(84268),a=i(64826),d=i(92144);function l(){return()=>{}}var c=i(6024),u="Avatar",[f,h]=(0,r.A)(u),[p,m]=f(u),g=n.forwardRef((e,t)=>{let{__scopeAvatar:i,...r}=e,[o,s]=n.useState("idle");return(0,c.jsx)(p,{scope:i,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:(0,c.jsx)(a.sG.span,{...r,ref:t})})});g.displayName=u;var v="AvatarImage",b=n.forwardRef((e,t)=>{let{__scopeAvatar:i,src:r,onLoadingStatusChange:u=()=>{},...f}=e,h=m(v,i),p=function(e,t){let{referrerPolicy:i,crossOrigin:r}=t,o=(0,d.useSyncExternalStore)(l,()=>!0,()=>!1),a=n.useRef(null),c=o?(a.current||(a.current=new window.Image),a.current):null,[u,f]=n.useState(()=>w(c,e));return(0,s.N)(()=>{f(w(c,e))},[c,e]),(0,s.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),i&&(c.referrerPolicy=i),"string"==typeof r&&(c.crossOrigin=r),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,r,i]),u}(r,f),g=(0,o.c)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,s.N)(()=>{"idle"!==p&&g(p)},[p,g]),"loaded"===p?(0,c.jsx)(a.sG.img,{...f,ref:t,src:r}):null});b.displayName=v;var A="AvatarFallback",S=n.forwardRef((e,t)=>{let{__scopeAvatar:i,delayMs:r,...o}=e,s=m(A,i),[d,l]=n.useState(void 0===r);return n.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>l(!0),r);return()=>window.clearTimeout(e)}},[r]),d&&"loaded"!==s.imageLoadingStatus?(0,c.jsx)(a.sG.span,{...o,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}S.displayName=A;var T=g,y=b,R=S},25451:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(45707).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},31200:(e,t,i)=>{i.d(t,{zf:()=>o});var n=i(50628),r=i(6024);function o(e){let[t,i]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{i(!0)},[]),(0,r.jsx)(n.Suspense,{fallback:e.fallback,children:t?"function"==typeof e.children?e.children():e.children:e.fallback})}},45707:(e,t,i)=>{i.d(t,{A:()=>u});var n=i(50628);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:i="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:u,iconNode:f,...h}=e;return(0,n.createElement)("svg",{ref:t,...l,width:r,height:r,stroke:i,strokeWidth:s?24*Number(o)/Number(r):o,className:a("lucide",c),...!u&&!d(h)&&{"aria-hidden":"true"},...h},[...f.map(e=>{let[t,i]=e;return(0,n.createElement)(t,i)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let i=(0,n.forwardRef)((i,o)=>{let{className:d,...l}=i;return(0,n.createElement)(c,{ref:o,iconNode:t,className:a("lucide-".concat(r(s(e))),"lucide-".concat(e),d),...l})});return i.displayName=s(e),i}},48957:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=i(69555)},50817:(e,t,i)=>{i.d(t,{UC:()=>ee,VY:()=>ei,ZL:()=>J,bL:()=>Z,bm:()=>en,hE:()=>et,hJ:()=>Q});var n=i(50628),r=i(13859),o=i(98064),s=i(48733),a=i(29823),d=i(17691),l=i(79447),c=i(9665),u=i(4844),f=i(64714),h=i(64826),p=i(16279),m=i(10345),g=i(11712),v=i(89840),b=i(6024),A="Dialog",[S,w]=(0,s.A)(A),[T,y]=S(A),R=e=>{let{__scopeDialog:t,children:i,open:r,defaultOpen:o,onOpenChange:s,modal:l=!0}=e,c=n.useRef(null),u=n.useRef(null),[f,h]=(0,d.i)({prop:r,defaultProp:null!=o&&o,onChange:s,caller:A});return(0,b.jsx)(T,{scope:t,triggerRef:c,contentRef:u,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:l,children:i})};R.displayName=A;var I="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:i,...n}=e,s=y(I,i),a=(0,o.s)(t,s.triggerRef);return(0,b.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":G(s.open),...n,ref:a,onClick:(0,r.m)(e.onClick,s.onOpenToggle)})}).displayName=I;var E="DialogPortal",[O,_]=S(E,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:i,children:r,container:o}=e,s=y(E,t);return(0,b.jsx)(O,{scope:t,forceMount:i,children:n.Children.map(r,e=>(0,b.jsx)(f.C,{present:i||s.open,children:(0,b.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};M.displayName=E;var N="DialogOverlay",L=n.forwardRef((e,t)=>{let i=_(N,e.__scopeDialog),{forceMount:n=i.forceMount,...r}=e,o=y(N,e.__scopeDialog);return o.modal?(0,b.jsx)(f.C,{present:n||o.open,children:(0,b.jsx)(x,{...r,ref:t})}):null});L.displayName=N;var k=(0,v.TL)("DialogOverlay.RemoveScroll"),x=n.forwardRef((e,t)=>{let{__scopeDialog:i,...n}=e,r=y(N,i);return(0,b.jsx)(m.A,{as:k,allowPinchZoom:!0,shards:[r.contentRef],children:(0,b.jsx)(h.sG.div,{"data-state":G(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),C="DialogContent",D=n.forwardRef((e,t)=>{let i=_(C,e.__scopeDialog),{forceMount:n=i.forceMount,...r}=e,o=y(C,e.__scopeDialog);return(0,b.jsx)(f.C,{present:n||o.open,children:o.modal?(0,b.jsx)(P,{...r,ref:t}):(0,b.jsx)(U,{...r,ref:t})})});D.displayName=C;var P=n.forwardRef((e,t)=>{let i=y(C,e.__scopeDialog),s=n.useRef(null),a=(0,o.s)(t,i.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(j,{...e,ref:a,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=i.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,i=0===t.button&&!0===t.ctrlKey;(2===t.button||i)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=n.forwardRef((e,t)=>{let i=y(C,e.__scopeDialog),r=n.useRef(!1),o=n.useRef(!1);return(0,b.jsx)(j,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,s;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(r.current||null==(s=i.triggerRef.current)||s.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var n,s;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let a=t.target;(null==(s=i.triggerRef.current)?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),j=n.forwardRef((e,t)=>{let{__scopeDialog:i,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,...d}=e,u=y(C,i),f=n.useRef(null),h=(0,o.s)(t,f);return(0,p.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:a,children:(0,b.jsx)(l.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":G(u.open),...d,ref:h,onDismiss:()=>u.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(Y,{titleId:u.titleId}),(0,b.jsx)(K,{contentRef:f,descriptionId:u.descriptionId})]})]})}),F="DialogTitle",H=n.forwardRef((e,t)=>{let{__scopeDialog:i,...n}=e,r=y(F,i);return(0,b.jsx)(h.sG.h2,{id:r.titleId,...n,ref:t})});H.displayName=F;var q="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:i,...n}=e,r=y(q,i);return(0,b.jsx)(h.sG.p,{id:r.descriptionId,...n,ref:t})});V.displayName=q;var X="DialogClose",B=n.forwardRef((e,t)=>{let{__scopeDialog:i,...n}=e,o=y(X,i);return(0,b.jsx)(h.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function G(e){return e?"open":"closed"}B.displayName=X;var z="DialogTitleWarning",[$,W]=(0,s.q)(z,{contentName:C,titleName:F,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,i=W(z),r="`".concat(i.contentName,"` requires a `").concat(i.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(i.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(i.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},K=e=>{let{contentRef:t,descriptionId:i}=e,r=W("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");i&&n&&(document.getElementById(i)||console.warn(o))},[o,t,i]),null},Z=R,J=M,Q=L,ee=D,et=H,ei=V,en=B},52482:(e,t,i)=>{i.d(t,{b:()=>l});var n=i(50628),r=i(64826),o=i(6024),s="horizontal",a=["horizontal","vertical"],d=n.forwardRef((e,t)=>{var i;let{decorative:n,orientation:d=s,...l}=e,c=(i=d,a.includes(i))?d:s;return(0,o.jsx)(r.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...l,ref:t})});d.displayName="Separator";var l=d},60006:(e,t,i)=>{function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),i(6024),i(6341),i(48957),i(85524)},65581:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(45707).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},69555:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,i(4937).createAsyncLocalStorage)()},70988:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return o},findSourceMapURL:function(){return r.findSourceMapURL}});let n=i(20091),r=i(39763),o=i(20240).createServerReference},78647:(e,t,i)=>{i.d(t,{Ac:()=>eb,Z5:()=>eA,aq:()=>W,o6:()=>ev,qo:()=>eg});var n=i(50628),r=i(4640),o=i(6024),s=(0,n.createContext)(null);function a(){return(0,n.useContext)(s)}function d(){return null!==a()}function l(e,t,i,r,o){let s,a=(0,n.useRef)(null);null===a.current?a.current=s={hasValue:!1,value:null}:s=a.current;let[d,l]=(0,n.useMemo)(()=>{let e,n,a=!1,d=t=>{var i;if(!a){a=!0,e=t;let i=r(t);if(void 0!==o&&s.hasValue){let e=s.value;if(o(e,i))return n=e,e}return n=i,i}let d=n;if((i=e)===t&&(0!==i||1/i==1/t)||i!=i&&t!=t)return d;let l=r(t);return void 0!==o&&o(d,l)?(e=t,d):(e=t,n=l,l)},l=void 0===i?null:i;return[()=>d(t()),null===l?void 0:()=>d(l())]},[t,i,r,o]),c=(0,n.useSyncExternalStore)(e,d,l);return(0,n.useEffect)(()=>{s.hasValue=!0,s.value=c},[c]),(0,n.useDebugValue)(c),c}var c=e=>e;function u(e,t,i){return l(e.subscribe,e.get,e.get,t??c,i)}var f={SMOOTH_DELAY:1e3,NOTIFICATIONS_POLL_INTERVAL:6e4,NOTIFICATIONS_MAX_STALE_TIME:5e3,ROOM_THREADS_POLL_INTERVAL:3e5,ROOM_THREADS_MAX_STALE_TIME:5e3,USER_THREADS_POLL_INTERVAL:6e4,USER_THREADS_MAX_STALE_TIME:3e4,HISTORY_VERSIONS_POLL_INTERVAL:6e4,HISTORY_VERSIONS_MAX_STALE_TIME:5e3,ROOM_SUBSCRIPTION_SETTINGS_POLL_INTERVAL:6e4,ROOM_SUBSCRIPTION_SETTINGS_MAX_STALE_TIME:5e3,USER_NOTIFICATION_SETTINGS_INTERVAL:3e5,USER_NOTIFICATION_SETTINGS_MAX_STALE_TIME:6e4},h=Object.freeze({isLoading:!0}),p=e=>Object.freeze({isLoading:!1,error:e});function m(e,t){return 1==arguments.length?Object.freeze({isLoading:!1,data:e}):Object.freeze({isLoading:!1,[e]:t})}function g(){if("undefined"==typeof window)throw Error("You cannot use the Suspense version of Liveblocks hooks server side. Make sure to only call them client side by using a ClientSideSuspense wrapper.\nFor tips, see https://liveblocks.io/docs/api-reference/liveblocks-react#ClientSideSuspense")}function v(e){let t=(0,n.useRef)(e);return(0,n.useEffect)(()=>{t.current=e},[e]),t}var b=e=>e;function A(e){return(0,n.useReducer)(b,e)[0]}function S(e){let t=A(e);if("function"!=typeof t)return t;{let t=v(e);return(0,n.useCallback)((...e)=>t.current(...e),[t])}}var w=e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e};function T(e){let t=new Set;t.add("constructor");let i=e.constructor.prototype;do for(let n of Reflect.ownKeys(i)){if(t.has(n))continue;let r=Reflect.getOwnPropertyDescriptor(i,n);"function"==typeof r?.value&&(t.add(n),e[n]=e[n].bind(e))}while((i=Reflect.getPrototypeOf(i))&&i!==Object.prototype)}var y=class e{#e;#t;#i;signal;constructor(){this.#t=r.aG.from([],(e,t)=>{let i=e.createdAt,n=t.createdAt;return i<n||i===n&&e.id<t.id}),this.#i=r.aG.from([],(e,t)=>{let i=t.updatedAt,n=e.updatedAt;return i<n||i===n&&t.id<e.id}),this.#e=new Map,this.signal=new r.Xf(this)}clone(){let t=new e;return t.#e=new Map(this.#e),t.#t=this.#t.clone(),t.#i=this.#i.clone(),t}get(e){let t=this.getEvenIfDeleted(e);return t?.deletedAt?void 0:t}getEvenIfDeleted(e){return this.#e.get(e)}upsert(e){this.signal.mutate(()=>{var t;let i=(e=(t=e).deletedAt&&t.comments.length>0?{...t,comments:[]}:!t.comments.some(e=>!e.deletedAt)?{...t,deletedAt:new Date,comments:[]}:t).id,n=this.#e.get(i);if(n){if(n.deletedAt)return!1;this.#t.remove(n),this.#i.remove(n)}return e.deletedAt||(this.#t.add(e),this.#i.add(e)),this.#e.set(i,e),!0})}upsertIfNewer(e){let t=this.get(e.id);(!t||e.updatedAt>=t.updatedAt)&&this.upsert(e)}applyDelta(e,t){(0,r.OX)(()=>{for(let t of e)this.upsertIfNewer(t);for(let{id:e,deletedAt:i}of t)this.getEvenIfDeleted(e)&&this.delete(e,i)})}delete(e,t){let i=this.#e.get(e);i&&!i.deletedAt&&this.upsert({...i,deletedAt:t,updatedAt:t})}findMany(e,t,i){let n="desc"===i?this.#i:this.#t,o=[];return void 0!==e&&o.push(t=>t.roomId===e),void 0!==t&&o.push(e=>{var i,n;return i=e,(void 0===(n=t).resolved||i.resolved===n.resolved)&&function(e,t){let i=e.metadata;return void 0===t.metadata||Object.entries(t.metadata).every(([e,t])=>{var n,o;return void 0===t||(n=i[e],null===(o=t)?void 0===n:(0,r.WU)(o)?"string"==typeof n&&n.startsWith(o.startsWith):n===o)})}(e,t)}),Array.from(n.filter(e=>o.every(t=>t(e))))}};function R(e,t){return stableStringify([e,t??{}])}function I(e){return stableStringify(e??{})}function E(e){return"status"in e||(e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t})),e}var O=Promise.resolve(),_=class{#n;signal;#r;#o;constructor(e){this.#n=new r.HN(h),this.#r=e,this.#o=null,this.signal=this.#n.asReadonly(),T(this)}get(){return this.#n.get()}#s(e){let t=this.#n.get();void 0!==t.data&&this.#n.set(m({...t.data,...e}))}async #a(){let e=this.#n.get();if(e.data?.cursor&&!e.data.isFetchingMore){this.#s({isFetchingMore:!0});try{let t=await this.#r(e.data.cursor);this.#s({cursor:t,hasFetchedAll:null===t,fetchMoreError:void 0,isFetchingMore:!1})}catch(e){this.#s({isFetchingMore:!1,fetchMoreError:e})}}}fetchMore(){let e=this.#n.get();return e.data?.cursor?(this.#o||(this.#o=this.#a().finally(()=>{this.#o=null})),this.#o):O}#d=null;waitUntilLoaded(){if(this.#d)return this.#d;let e=E((0,r.Ap)(()=>this.#r(void 0),5,[5e3,5e3,1e4,15e3]));return e.then(e=>{this.#n.set(m({cursor:e,hasFetchedAll:null===e,isFetchingMore:!1,fetchMoreError:void 0,fetchMore:this.fetchMore}))},e=>{this.#n.set(p(e)),setTimeout(()=>{this.#d=null,this.#n.set(h)},5e3)}),this.#d=e,this.#d}},M=class{#n;signal;#r;constructor(e){this.#n=new r.HN(h),this.signal=this.#n.asReadonly(),this.#r=e,T(this)}get(){return this.#n.get()}#d=null;waitUntilLoaded(){if(this.#d)return this.#d;let e=E((0,r.Ap)(()=>this.#r(),5,[5e3,5e3,1e4,15e3]));return e.then(()=>{this.#n.set(m(void 0))},e=>{this.#n.set(p(e)),setTimeout(()=>{this.#d=null,this.#n.set(h)},5e3)}),this.#d=e,e}},N=class{#l;threads;notifications;subscriptions;roomSubscriptionSettings;historyVersions;permissionHints;notificationSettings;optimisticUpdates;outputs;#c=null;#u;#f=new Map;#h=null;#p=new Map;#m;constructor(e){this.#l=e[r.HM].as(),this.optimisticUpdates=function(e){let t=new r.HN([]),i=e[r.HM].createSyncSource();return t.subscribe(()=>i.setSyncStatus(t.get().length>0?"synchronizing":"synchronized")),{signal:t.asReadonly(),add:function(e){let i=(0,r.Ak)(),n={...e,id:i};return t.set(e=>[...e,n]),i},remove:function(e){t.set(t=>t.filter(t=>t.id!==e))}}}(this.#l),this.permissionHints=function(){let e=new r.Xf(new r.Ge(()=>new Set));return{signal:e.asReadonly(),update:function(t){e.mutate(e=>{for(let[i,n]of Object.entries(t)){let t=e.getOrCreate(i);for(let e of n)t.add(e)}})}}}(),this.#u=new _(async e=>{let t=await this.#l.getInboxNotifications({cursor:e});return this.updateThreadifications(t.threads,t.inboxNotifications,t.subscriptions),null===this.#c&&(this.#c=t.requestedAt),t.nextCursor});let t=async()=>{let e=await this.#l.getNotificationSettings();this.notificationSettings.update(e)};this.notificationSettings=function(e){let t=new r.HN((0,r.b4)({}));return{signal:r.ez.from(t,e,(e,t)=>(function(e,t){let i=e;for(let e of t)"update-notification-settings"===e.type&&(i=(0,r.Fr)(i,e.settings));return i})(e,t)),update:function(e){t.set(e)}}}(this.optimisticUpdates.signal),this.#m=new M(t),this.threads=new y,this.subscriptions=function(e,t){let i=new r.Xf(new Map);return{signal:r.ez.from(i,e,(e,i)=>(function(e,t,i){let n=Object.fromEntries(e);for(let e of i)if("update-room-subscription-settings"===e.type){if(!e.settings.threads)continue;for(let i of t.findMany(e.roomId,void 0,"desc")){let t=(0,r.P$)("thread",i.id);switch(e.settings.threads){case"all":n[t]={kind:"thread",subjectId:i.id,createdAt:new Date};break;case"none":delete n[t];break;case"replies_and_mentions":(function(e,t){let i=!1;for(let n of e.comments)if(!n.deletedAt&&(n.userId===t||(0,r.zb)(n.body).includes(t))){i=!0;break}return i})(i,e.userId)&&!n[t]&&(n[t]={kind:"thread",subjectId:i.id,createdAt:new Date});break;default:(0,r.xb)(e.settings.threads,"Unexpected thread subscription settings.")}}}return n})(e,t,i)),applyDelta:function(e,t){i.mutate(i=>{let n=!1;for(let t of e)i.set((0,r.P$)(t),t),n=!0;for(let e of t)i.delete((0,r.P$)(e)),n=!0;return n})},create:function(e){i.mutate(t=>{t.set((0,r.P$)(e),e)})},delete:function(e){i.mutate(t=>{t.delete(e)})}}}(this.optimisticUpdates.signal,this.threads),this.notifications=function(){let e=new r.Xf(new Map);return{signal:e.asReadonly(),markAllRead:function(t){e.mutate(e=>{for(let i of e.values())i.readAt=t})},markRead:function(t,i){e.mutate(e=>{let n=e.get(t);return!!n&&(e.set(t,{...n,readAt:i}),!0)})},delete:function(t){e.mutate(e=>e.delete(t))},applyDelta:function(t,i){e.mutate(e=>{let n=!1;for(let i of t){var r,o;let t=e.get(i.id);(!t||1!==(r=t,o=i,r.notifiedAt>o.notifiedAt?1:r.notifiedAt<o.notifiedAt?-1:r.readAt&&o.readAt?r.readAt>o.readAt?1:r.readAt<o.readAt?-1:0:r.readAt||o.readAt?r.readAt?1:-1:0))&&(e.set(i.id,i),n=!0)}for(let t of i)e.delete(t.id),n=!0;return n})},clear:function(){e.mutate(e=>e.clear())},updateAssociatedNotification:function(t){e.mutate(e=>{let i=function(e,t){for(let i of e)if(t(i))return i}(e.values(),e=>"thread"===e.kind&&e.threadId===t.threadId);return!!i&&(e.set(i.id,{...i,notifiedAt:t.createdAt,readAt:t.createdAt}),!0)})},upsert:function(t){e.mutate(e=>{e.set(t.id,t)})}}}(),this.roomSubscriptionSettings=function(e){let t=new r.Xf(new Map);return{signal:r.ez.from(t,e,(e,t)=>(function(e,t){let i=Object.fromEntries(e);for(let e of t)switch(e.type){case"update-room-subscription-settings":{let t=i[e.roomId];if(void 0===t)break;i[e.roomId]={...t,...e.settings}}}return i})(e,t)),update:function(e,i){t.mutate(t=>{t.set(e,i)})}}}(this.optimisticUpdates.signal),this.historyVersions=function(){let e=new r.Xf(new r.Ge(()=>new Map));return{signal:r.ez.from(e,e=>Object.fromEntries([...e].map(([e,t])=>[e,Object.fromEntries(t)]))),update:function(t,i){e.mutate(e=>{let n=e.getOrCreate(t);for(let e of i)n.set(e.id,e)})}}}();let i=r.ez.from(this.threads.signal,this.notifications.signal,this.optimisticUpdates.signal,(e,t,i)=>(function(e,t,i){let n=e.clone(),r=Object.fromEntries(t);for(let e of i)switch(e.type){case"create-thread":n.upsert(e.thread);break;case"edit-thread-metadata":{let t=n.get(e.threadId);if(void 0===t||t.updatedAt>e.updatedAt)break;n.upsert({...t,updatedAt:e.updatedAt,metadata:{...t.metadata,...e.metadata}});break}case"mark-thread-as-resolved":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert({...t,resolved:!0});break}case"mark-thread-as-unresolved":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert({...t,resolved:!1});break}case"create-comment":{let t=n.get(e.comment.threadId);if(void 0===t)break;n.upsert(L(t,e.comment));let i=Object.values(r).find(e=>"thread"===e.kind&&e.threadId===t.id);if(void 0===i)break;r[i.id]={...i,notifiedAt:e.comment.createdAt,readAt:e.comment.createdAt};break}case"edit-comment":{let t=n.get(e.comment.threadId);if(void 0===t)break;n.upsert(L(t,e.comment));break}case"delete-comment":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert(k(t,e.commentId,e.deletedAt));break}case"delete-thread":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert({...t,deletedAt:e.deletedAt,updatedAt:e.deletedAt,comments:[]});break}case"add-reaction":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert(x(t,e.commentId,e.reaction));break}case"remove-reaction":{let t=n.get(e.threadId);if(void 0===t)break;n.upsert(C(t,e.commentId,e.emoji,e.userId,e.removedAt));break}case"mark-inbox-notification-as-read":{let t=r[e.inboxNotificationId];if(void 0===t)break;r[e.inboxNotificationId]={...t,readAt:e.readAt};break}case"mark-all-inbox-notifications-as-read":for(let t in r){let i=r[t];if(void 0===i)break;r[t]={...i,readAt:e.readAt}}break;case"delete-inbox-notification":delete r[e.inboxNotificationId];break;case"delete-all-inbox-notifications":r={}}return{sortedNotifications:Object.values(r).filter(e=>"thread"!==e.kind||void 0!==n.get(e.threadId)).sort((e,t)=>t.notifiedAt.getTime()-e.notifiedAt.getTime()),notificationsById:r,threadsDB:n}})(e,t,i)),n=r.ez.from(i,e=>e.threadsDB),o=r.ez.from(i,e=>({sortedNotifications:e.sortedNotifications,notificationsById:e.notificationsById}),r.x7),s=r.ez.from(o,this.subscriptions.signal,(e,t)=>({subscriptions:t,notifications:e.sortedNotifications})),a=new r.Ge(e=>{let t=JSON.parse(e),i=new _(async e=>{let i=await this.#l[r.HM].httpClient.getUserThreads_experimental({cursor:e,query:t});return this.updateThreadifications(i.threads,i.inboxNotifications,i.subscriptions),this.permissionHints.update(i.permissionHints),null===this.#h&&(this.#h=i.requestedAt),i.nextCursor});return{signal:r.ez.from(()=>{let e=i.get();if(e.isLoading||e.error)return e;let n=this.outputs.threads.get().findMany(void 0,t??{},"desc"),r=e.data;return{isLoading:!1,threads:n,hasFetchedAll:r.hasFetchedAll,isFetchingMore:r.isFetchingMore,fetchMoreError:r.fetchMoreError,fetchMore:r.fetchMore}},r.dP),waitUntilLoaded:i.waitUntilLoaded}}),d=new r.Ge(e=>{let[t,i]=JSON.parse(e),n=new _(async e=>{let n=await this.#l[r.HM].httpClient.getThreads({roomId:t,cursor:e,query:i});this.updateThreadifications(n.threads,n.inboxNotifications,n.subscriptions),this.permissionHints.update(n.permissionHints);let o=this.#f.get(t);return(void 0===o||o>n.requestedAt)&&this.#f.set(t,n.requestedAt),n.nextCursor});return{signal:r.ez.from(()=>{let e=n.get();if(e.isLoading||e.error)return e;let r=this.outputs.threads.get().findMany(t,i??{},"asc"),o=e.data;return{isLoading:!1,threads:r,hasFetchedAll:o.hasFetchedAll,isFetchingMore:o.isFetchingMore,fetchMoreError:o.fetchMoreError,fetchMore:o.fetchMore}},r.dP),waitUntilLoaded:n.waitUntilLoaded}}),l={signal:r.ez.from(()=>{let e=this.#u.get();if(e.isLoading||e.error)return e;let t=e.data;return{isLoading:!1,inboxNotifications:this.outputs.notifications.get().sortedNotifications,hasFetchedAll:t.hasFetchedAll,isFetchingMore:t.isFetchingMore,fetchMoreError:t.fetchMoreError,fetchMore:t.fetchMore}}),waitUntilLoaded:this.#u.waitUntilLoaded},c=new r.Ge(e=>{let t=new M(async()=>{let t=this.#l.getRoom(e);if(null===t)throw Error(`Room '${e}' is not available on client`);let i=await t.getSubscriptionSettings();this.roomSubscriptionSettings.update(e,i)});return{signal:r.ez.from(()=>{let i=t.get();return i.isLoading||i.error?i:m("settings",(0,r.nn)(this.roomSubscriptionSettings.signal.get()[e]))},r.x7),waitUntilLoaded:t.waitUntilLoaded}}),u=new r.Ge(e=>{let t=new M(async()=>{let t=this.#l.getRoom(e);if(null===t)throw Error(`Room '${e}' is not available on client`);let i=await t[r.HM].listTextVersions();this.historyVersions.update(e,i.versions);let n=this.#p.get(e);(void 0===n||n>i.requestedAt)&&this.#p.set(e,i.requestedAt)});return{signal:r.ez.from(()=>{let i=t.get();return i.isLoading||i.error?i:m("versions",Object.values(this.historyVersions.signal.get()[e]??{}))},r.x7),waitUntilLoaded:t.waitUntilLoaded}}),f={signal:r.ez.from(()=>{let e=this.#m.get();return e.isLoading||e.error?e:m("settings",(0,r.nn)(this.notificationSettings.signal.get()))},r.x7),waitUntilLoaded:this.#m.waitUntilLoaded};this.outputs={threadifications:i,threads:n,loadingRoomThreads:d,loadingUserThreads:a,notifications:o,loadingNotifications:l,roomSubscriptionSettingsByRoomId:c,versionsByRoomId:u,notificationSettings:f,threadSubscriptions:s},T(this)}markInboxNotificationRead(e,t,i){(0,r.OX)(()=>{this.optimisticUpdates.remove(i),this.notifications.markRead(e,t)})}markAllInboxNotificationsRead(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(e),this.notifications.markAllRead(t)})}deleteInboxNotification(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(t),this.notifications.delete(e)})}deleteAllInboxNotifications(e){(0,r.OX)(()=>{this.optimisticUpdates.remove(e),this.notifications.clear()})}createSubscription(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(t),this.subscriptions.create(e)})}deleteSubscription(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(t),this.subscriptions.delete(e)})}createThread(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(e),this.threads.upsert(t)})}#g(e,t,i,n){(0,r.OX)(()=>{null!==t&&this.optimisticUpdates.remove(t);let r=this.threads,o=r.get(e);o&&(n&&o.updatedAt>n||r.upsert(i(o)))})}patchThread(e,t,i,n){return this.#g(e,t,e=>({...e,...(0,r.BU)(i)}),n)}addReaction(e,t,i,n,r){this.#g(e,t,e=>x(e,i,n),r)}removeReaction(e,t,i,n,r,o){this.#g(e,t,e=>C(e,i,n,r,o),o)}deleteThread(e,t){return this.#g(e,t,e=>({...e,updatedAt:new Date,deletedAt:new Date}))}createComment(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(t);let i=this.threads.get(e.threadId);i&&(this.threads.upsert(L(i,e)),this.notifications.updateAssociatedNotification(e))})}editComment(e,t,i){return this.#g(e,t,e=>L(e,i))}deleteComment(e,t,i,n){return this.#g(e,t,e=>k(e,i,n),n)}updateThreadifications(e,t,i,n=[],o=[],s=[]){(0,r.OX)(()=>{this.threads.applyDelta(e,n),this.notifications.applyDelta(t,o),this.subscriptions.applyDelta(i,s)})}updateRoomSubscriptionSettings(e,t,i){(0,r.OX)(()=>{this.optimisticUpdates.remove(t),this.roomSubscriptionSettings.update(e,i)})}async fetchNotificationsDeltaUpdate(e){let t=this.#c;if(null===t)return;let i=await this.#l.getInboxNotificationsSince({since:t,signal:e});t<i.requestedAt&&(this.#c=i.requestedAt),this.updateThreadifications(i.threads.updated,i.inboxNotifications.updated,i.subscriptions.updated,i.threads.deleted,i.inboxNotifications.deleted,i.subscriptions.deleted)}async fetchRoomThreadsDeltaUpdate(e,t){let i=this.#f.get(e);if(void 0===i)return;let n=await this.#l[r.HM].httpClient.getThreadsSince({roomId:e,since:i,signal:t});this.updateThreadifications(n.threads.updated,n.inboxNotifications.updated,n.subscriptions.updated,n.threads.deleted,n.inboxNotifications.deleted,n.subscriptions.deleted),this.permissionHints.update(n.permissionHints),i<n.requestedAt&&this.#f.set(e,n.requestedAt)}async fetchUserThreadsDeltaUpdate(e){let t=this.#h;if(null===t)return;let i=await this.#l[r.HM].httpClient.getUserThreadsSince_experimental({since:t,signal:e});t<i.requestedAt&&(this.#c=i.requestedAt),this.updateThreadifications(i.threads.updated,i.inboxNotifications.updated,i.subscriptions.updated,i.threads.deleted,i.inboxNotifications.deleted,i.subscriptions.deleted),this.permissionHints.update(i.permissionHints)}async fetchRoomVersionsDeltaUpdate(e,t){let i=this.#p.get(e);if(void 0===i)return;let n=(0,r.nn)(this.#l.getRoom(e),`Room with id ${e} is not available on client`),o=await n[r.HM].listTextVersionsSince({since:i,signal:t});this.historyVersions.update(e,o.versions),i<o.requestedAt&&this.#p.set(e,o.requestedAt)}async refreshRoomSubscriptionSettings(e,t){let i=(0,r.nn)(this.#l.getRoom(e),`Room with id ${e} is not available on client`),n=await i.getSubscriptionSettings({signal:t});this.roomSubscriptionSettings.update(e,n)}async refreshNotificationSettings(e){let t=await this.#l.getNotificationSettings({signal:e});this.notificationSettings.update(t)}updateNotificationSettings_confirmOptimisticUpdate(e,t){(0,r.OX)(()=>{this.optimisticUpdates.remove(t),this.notificationSettings.update(e)})}};function L(e,t){if(void 0!==e.deletedAt)return e;if(t.threadId!==e.id)return r.k2.warn(`Comment ${t.id} does not belong to thread ${e.id}`),e;let i=e.comments.find(e=>e.id===t.id);if(void 0===i){let i=new Date(Math.max(e.updatedAt.getTime(),t.createdAt.getTime()));return{...e,updatedAt:i,comments:[...e.comments,t]}}if(void 0!==i.deletedAt)return e;if(void 0===i.editedAt||void 0===t.editedAt||i.editedAt<=t.editedAt){let i=e.comments.map(e=>e.id===t.id?t:e);return{...e,updatedAt:new Date(Math.max(e.updatedAt.getTime(),t.editedAt?.getTime()||t.createdAt.getTime())),comments:i}}return e}function k(e,t,i){if(void 0!==e.deletedAt)return e;let n=e.comments.find(e=>e.id===t);if(void 0===n||void 0!==n.deletedAt)return e;let r=e.comments.map(e=>e.id===t?{...e,deletedAt:i,body:void 0,attachments:[]}:e);return r.every(e=>void 0!==e.deletedAt)?{...e,deletedAt:i,updatedAt:i}:{...e,updatedAt:i,comments:r}}function x(e,t,i){if(void 0!==e.deletedAt)return e;let n=e.comments.find(e=>e.id===t);if(void 0===n||void 0!==n.deletedAt)return e;let r=e.comments.map(e=>e.id===t?{...e,reactions:function(e,t){let i=e.find(e=>e.emoji===t.emoji);return void 0===i?[...e,{emoji:t.emoji,createdAt:t.createdAt,users:[{id:t.userId}]}]:!1===i.users.some(e=>e.id===t.userId)?e.map(e=>e.emoji===t.emoji?{...e,users:[...e.users,{id:t.userId}]}:e):e}(e.reactions,i)}:e);return{...e,updatedAt:new Date(Math.max(i.createdAt.getTime(),e.updatedAt.getTime())),comments:r}}function C(e,t,i,n,r){if(void 0!==e.deletedAt)return e;let o=e.comments.find(e=>e.id===t);if(void 0===o||void 0!==o.deletedAt)return e;let s=e.comments.map(e=>e.id===t?{...e,reactions:e.reactions.map(e=>e.emoji===i?{...e,users:e.users.filter(e=>e.id!==n)}:e).filter(e=>e.users.length>0)}:e);return{...e,updatedAt:new Date(Math.max(r.getTime(),e.updatedAt.getTime())),comments:s}}var D=(0,n.createContext)(null);function P(e){return Error(`resolveUsers didn't return anything for user '${e}'`)}function U(e){return Error(`resolveRoomsInfo didn't return anything for room '${e}'`)}function j(e){return e}var F=new WeakMap,H=new WeakMap;function q(e){return e.inboxNotifications?m("count",function(e,t){let i=0;for(let n of e)t(n)&&i++;return i}(e.inboxNotifications,e=>null===e.readAt||e.readAt<e.notifiedAt)):e}function V(e){let t=F.get(e);return t||(t=new N(e),F.set(e,t)),t}function X(e){let t=H.get(e);return t||(t=function(e){let t=V(e),i=makePoller(async e=>{try{return await t.fetchNotificationsDeltaUpdate(e)}catch(e){throw console.warn(`Polling new inbox notifications failed: ${String(e)}`),e}},f.NOTIFICATIONS_POLL_INTERVAL,{maxStaleTimeMs:f.NOTIFICATIONS_MAX_STALE_TIME}),n=makePoller(async e=>{try{return await t.fetchUserThreadsDeltaUpdate(e)}catch(e){throw console.warn(`Polling new user threads failed: ${String(e)}`),e}},f.USER_THREADS_POLL_INTERVAL,{maxStaleTimeMs:f.USER_THREADS_MAX_STALE_TIME}),r=makePoller(async e=>{try{return await t.refreshNotificationSettings(e)}catch(e){throw console.warn(`Polling new notification settings failed: ${String(e)}`),e}},f.USER_NOTIFICATION_SETTINGS_INTERVAL,{maxStaleTimeMs:f.USER_NOTIFICATION_SETTINGS_MAX_STALE_TIME});return{store:t,notificationsPoller:i,userThreadsPoller:n,notificationSettingsPoller:r}}(e),H.set(e,t)),t}function B(e,t,i){let{store:n,notificationsPoller:r}=X(e);return useEffect3(()=>void n.outputs.loadingNotifications.waitUntilLoaded()),useEffect3(()=>(r.inc(),r.pollNowIfStale(),()=>{r.dec()}),[r]),u(n.outputs.loadingNotifications.signal,t,i)}function G(){return(0,n.useContext)(D)}function z(){return G()??(0,r.xl)("LiveblocksProvider is missing from the React tree.")}function $(e){return function(e){let t=G();if(!e?.allowNesting&&null!==t)throw Error("You cannot nest multiple LiveblocksProvider instances in the same React tree.")}(e),(0,o.jsx)(D.Provider,{value:e.client,children:e.children})}function W(e){let{children:t,...i}=e,s={publicApiKey:A(i.publicApiKey),throttle:A(i.throttle),lostConnectionTimeout:A(i.lostConnectionTimeout),backgroundKeepAliveTimeout:A(i.backgroundKeepAliveTimeout),polyfills:A(i.polyfills),largeMessageStrategy:A(i.largeMessageStrategy),unstable_fallbackToHTTP:A(i.unstable_fallbackToHTTP),unstable_streamData:A(i.unstable_streamData),preventUnsavedChanges:A(i.preventUnsavedChanges),authEndpoint:S(i.authEndpoint),resolveMentionSuggestions:S(i.resolveMentionSuggestions),resolveUsers:S(i.resolveUsers),resolveRoomsInfo:S(i.resolveRoomsInfo),baseUrl:A(i.baseUrl),enableDebugLogging:A(i.enableDebugLogging)},a=(0,n.useMemo)(()=>(0,r.UU)(s),[]);return(0,o.jsx)($,{client:a,children:t})}function Y(e){let t=z(),i=v(e);useEffect3(()=>t.events.error.subscribe(e=>i.current(e)),[t,i])}var K=()=>{},Z=e=>e,J=Object.freeze([]);function Q(){return J}function ee(){return null}function et(e){return e.map(e=>e.connectionId)}function ei(e){let t=e[kInternal3].currentUserId.get();return void 0===t?"anonymous":t}var en=new WeakMap;function er(e){let t=en.get(e);return t||(t=function(e){let t=V(e),i=new r.Ge(e=>(0,r.F2)(async i=>{try{return await t.fetchRoomThreadsDeltaUpdate(e,i)}catch(t){throw r.k2.warn(`Polling new threads for '${e}' failed: ${String(t)}`),t}},f.ROOM_THREADS_POLL_INTERVAL,{maxStaleTimeMs:f.ROOM_THREADS_MAX_STALE_TIME})),n=new r.Ge(e=>(0,r.F2)(async i=>{try{return await t.fetchRoomVersionsDeltaUpdate(e,i)}catch(t){throw r.k2.warn(`Polling new history versions for '${e}' failed: ${String(t)}`),t}},f.HISTORY_VERSIONS_POLL_INTERVAL,{maxStaleTimeMs:f.HISTORY_VERSIONS_MAX_STALE_TIME})),o=new r.Ge(e=>(0,r.F2)(async i=>{try{return await t.refreshRoomSubscriptionSettings(e,i)}catch(t){throw r.k2.warn(`Polling subscription settings for '${e}' failed: ${String(t)}`),t}},f.ROOM_SUBSCRIPTION_SETTINGS_POLL_INTERVAL,{maxStaleTimeMs:f.ROOM_SUBSCRIPTION_SETTINGS_MAX_STALE_TIME}));return{store:t,onMutationFailure:function(i,n,o){if(t.optimisticUpdates.remove(i),o instanceof r.j$){if(403===o.status){let e=[o.message,o.details?.suggestion,o.details?.docs].filter(Boolean).join("\n");r.k2.error(e)}e[r.HM].emitError(n,o)}else throw o},pollThreadsForRoomId:e=>{let t=i.getOrCreate(e);t&&(t.markAsStale(),t.pollNowIfStale())},getOrCreateThreadsPollerForRoomId:i.getOrCreate.bind(i),getOrCreateVersionsPollerForRoomId:n.getOrCreate.bind(n),getOrCreateSubscriptionSettingsPollerForRoomId:o.getOrCreate.bind(o)}}(e),en.set(e,t)),t}function eo(e){let t=z(),{id:i,stableEnterRoom:a}=e,d=A({initialPresence:e.initialPresence,initialStorage:e.initialStorage,autoConnect:e.autoConnect??"undefined"!=typeof window}),[{room:l},c]=(0,n.useState)(()=>a(i,{...d,autoConnect:!1}));return(0,n.useEffect)(()=>{let{store:e}=er(t);async function i(t){if(t.type===r.gk.THREAD_DELETED)return void e.deleteThread(t.threadId,null);let i=await l.getThread(t.threadId);if(!i.thread)return void e.deleteThread(t.threadId,null);let{thread:n,inboxNotification:o,subscription:s}=i,a=e.outputs.threads.get().getEvenIfDeleted(t.threadId);switch(t.type){case r.gk.COMMENT_EDITED:case r.gk.THREAD_METADATA_UPDATED:case r.gk.THREAD_UPDATED:case r.gk.COMMENT_REACTION_ADDED:case r.gk.COMMENT_REACTION_REMOVED:case r.gk.COMMENT_DELETED:if(!a)break;e.updateThreadifications([n],o?[o]:[],s?[s]:[]);break;case r.gk.COMMENT_CREATED:e.updateThreadifications([n],o?[o]:[],s?[s]:[])}}return l.events.comments.subscribe(e=>void i(e))},[t,l]),(0,n.useEffect)(()=>{let e=a(i,d);c(e);let{room:t,leave:n}=e;return d.autoConnect&&t.connect(),()=>{n()}},[i,d,a]),(0,o.jsx)(s.Provider,{value:l,children:e.children})}function es(e){let t=a();if(null===t&&!e?.allowOutsideRoom)throw Error("RoomProvider is missing from the React tree.");return t}function ea(){return es().history}function ed(e,t){let i=es(),n=i.events.others.subscribe;return l(n,i.getOthers,Q,e??Z,t)}var el=Symbol();function ec(){let e=es(),t=e.events.storageDidLoad.subscribeOnce;return useSyncExternalStore3(t,e.getStorageSnapshot,ee)}function eu(e){let t=z();return useCallback3(i=>{let n=new Date,{store:r,onMutationFailure:o}=er(t),s=r.optimisticUpdates.add({type:"subscribe-to-thread",threadId:i,subscribedAt:n});t[kInternal3].httpClient.subscribeToThread({roomId:e,threadId:i}).then(e=>{r.createSubscription(e,s)},t=>o(s,{type:"SUBSCRIBE_TO_THREAD_ERROR",roomId:e,threadId:i},t))},[t,e])}function ef(e){let t=z();return useCallback3(i=>{let n=new Date,{store:r,onMutationFailure:o}=er(t),s=r.optimisticUpdates.add({type:"unsubscribe-from-thread",threadId:i,unsubscribedAt:n});t[kInternal3].httpClient.unsubscribeFromThread({roomId:e,threadId:i}).then(()=>{r.deleteSubscription(getSubscriptionKey2("thread",i),s)},t=>o(s,{type:"UNSUBSCRIBE_FROM_THREAD_ERROR",roomId:e,threadId:i},t))},[t,e])}function eh(){g(),w(es().waitUntilPresenceReady())}function ep(){g(),w(es().waitUntilStorageReady())}function em(e){return void 0===e||e?.isLoading?e??{isLoading:!0}:e.error?e:(assert2(void 0!==e.data,"Unexpected missing attachment URL"),{isLoading:!1,url:e.data})}var eg=function(e){let t=z(),[i]=(0,n.useState)(()=>new Map),r=(0,n.useCallback)((e,n)=>{let r=i.get(e);if(r)return r;let o=t.enterRoom(e,n),s=o.leave;return o.leave=()=>{s(),i.delete(e)},i.set(e,o),o},[t,i]);return(0,o.jsx)(eo,{...e,stableEnterRoom:r})},ev=function(){let e=es(),t=e.events.myPresence.subscribe,i=e.getPresence;return[(0,n.useSyncExternalStore)(t,i,i),e.updatePresence]};function eb(...e){return function(e,t){return eh(),ed(e,t)}(...e)}function eA(...e){return function(e,t){return eh(),function(e,t){let i=es(),r=i.events.self.subscribe,o=i.getSelf,s=e??Z;return l(r,o,ee,(0,n.useCallback)(e=>null!==e?s(e):null,[s]),t)}(e,t)}(...e)}},89959:(e,t,i)=>{var n=i(50628),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,s=n.useEffect,a=n.useLayoutEffect,d=n.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var i=t();return!r(e,i)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var i=t(),n=o({inst:{value:i,getSnapshot:t}}),r=n[0].inst,c=n[1];return a(function(){r.value=i,r.getSnapshot=t,l(r)&&c({inst:r})},[e,i,t]),s(function(){return l(r)&&c({inst:r}),e(function(){l(r)&&c({inst:r})})},[e]),d(i),i};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},92144:(e,t,i)=>{e.exports=i(89959)}}]);