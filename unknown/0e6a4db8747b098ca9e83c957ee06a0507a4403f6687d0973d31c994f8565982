(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2936],{11703:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var n=s(6024),r=s(70234),a=s(69680),i=s(50628);function c(){let[e,t]=(0,i.useState)([]),s=e=>{let s=new Date().toLocaleTimeString();t(t=>[...t,"[".concat(s,"] ").concat(e)])};return(0,n.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Authentication Flow Debugger"}),(0,n.jsx)("p",{className:"text-muted-foreground mt-2",children:"Test and debug extension authentication flows"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)(a.Zp,{children:[(0,n.jsx)(a.aR,{children:(0,n.jsx)(a.ZB,{children:"Device OAuth Flow"})}),(0,n.jsxs)(a.Wu,{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:'Tests the primary authentication flow used by the "Connect to Cubent Cloud" button.'}),(0,n.jsx)(r.$,{onClick:()=>{s("Testing Device OAuth Flow...");let e="test-device-"+Math.random().toString(36).substr(2,9),t="test-state-"+Math.random().toString(36).substr(2,9),n="/sign-in?device_id=".concat(e,"&state=").concat(t);s("Generated URL: ".concat(n)),s("Opening in new tab..."),window.open(n,"_blank")},className:"w-full",children:"Test Device OAuth"})]})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsx)(a.aR,{children:(0,n.jsx)(a.ZB,{children:"Legacy Flow"})}),(0,n.jsxs)(a.Wu,{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:'Tests the legacy authentication flow used by the "Sign In (Legacy)" button.'}),(0,n.jsx)(r.$,{onClick:()=>{s("Testing Legacy Flow...");let e="test-state-"+Math.random().toString(36).substr(2,9),t="/api/extension/sign-in?state=".concat(e,"&auth_redirect=").concat(encodeURIComponent("vscode://cubent.cubent/auth"));s("Generated URL: ".concat(t)),s("Opening in new tab..."),window.open(t,"_blank")},className:"w-full",children:"Test Legacy Flow"})]})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsx)(a.aR,{children:(0,n.jsx)(a.ZB,{children:"Direct Login"})}),(0,n.jsxs)(a.Wu,{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tests direct access to the login page with device parameters."}),(0,n.jsx)(r.$,{onClick:()=>{s("Testing Direct Login Page...");let e="test-device-"+Math.random().toString(36).substr(2,9),t="test-state-"+Math.random().toString(36).substr(2,9),n="/login?device_id=".concat(e,"&state=").concat(t);s("Generated URL: ".concat(n)),s("Opening in new tab..."),window.open(n,"_blank")},className:"w-full",children:"Test Direct Login"})]})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsx)(a.aR,{children:(0,n.jsx)(a.ZB,{children:"Auth Success"})}),(0,n.jsxs)(a.Wu,{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tests the auth success page redirect functionality."}),(0,n.jsx)(r.$,{onClick:()=>{s("Testing Auth Success Page...");let e="/auth-success?redirect_url=".concat(encodeURIComponent("vscode://cubent.cubent/auth/callback?token=test-token&state=test-state"));s("Generated URL: ".concat(e)),s("Opening in new tab..."),window.open(e,"_blank")},className:"w-full",children:"Test Auth Success"})]})]})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsxs)(a.aR,{className:"flex flex-row items-center justify-between",children:[(0,n.jsx)(a.ZB,{children:"Debug Logs"}),(0,n.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>t([]),children:"Clear Logs"})]}),(0,n.jsx)(a.Wu,{children:(0,n.jsx)("div",{className:"bg-muted p-4 rounded-md max-h-64 overflow-y-auto",children:0===e.length?(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:"No logs yet. Click a test button to start."}):(0,n.jsx)("div",{className:"space-y-1",children:e.map((e,t)=>(0,n.jsx)("div",{className:"text-sm font-mono",children:e},t))})})})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsx)(a.aR,{children:(0,n.jsx)(a.ZB,{children:"Current Environment"})}),(0,n.jsx)(a.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Current URL:"}),(0,n.jsx)("br",{}),(0,n.jsx)("code",{className:"text-xs",children:window.location.href})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"User Agent:"}),(0,n.jsx)("br",{}),(0,n.jsx)("code",{className:"text-xs",children:window.navigator.userAgent.slice(0,50)+"..."})]})]})})]})]})}},15608:(e,t,s)=>{Promise.resolve().then(s.bind(s,11703))},31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a}),s(63410);var n=s(49973);s(13957);var r=s(22928);let a=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,n.$)(t))}},69680:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>a,aR:()=>i});var n=s(6024);s(50628);var r=s(31918);function a(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var n=s(6024);s(50628);var r=s(89840),a=s(81197),i=s(31918);let c=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:a,asChild:d=!1,...o}=e,l=d?r.DX:"button";return(0,n.jsx)(l,{"data-slot":"button",className:(0,i.cn)(c({variant:s,size:a,className:t})),...o})}},81197:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var n=s(49973);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>s=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:c}=t,d=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],n=null==c?void 0:c[e];if(null===t)return null;let a=r(t)||r(n);return i[e][a]}),o=s&&Object.entries(s).reduce((e,t)=>{let[s,n]=t;return void 0===n||(e[s]=n),e},{});return a(e,d,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:s,className:n,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...c,...o}[t]):({...c,...o})[t]===s})?[...e,s,n]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,2913,4499,7358],()=>t(15608)),_N_E=e.O()}]);