"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6655],{3671:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(45707).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11676:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(50628),l=r(64826),o=r(6024),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},38083:(e,t,r)=>{r.d(t,{UC:()=>eD,In:()=>eE,q7:()=>eL,VF:()=>eH,p4:()=>eA,ZL:()=>eI,bL:()=>eT,wn:()=>e_,PP:()=>eB,l9:()=>eN,WT:()=>eP,LM:()=>eM});var n=r(50628),l=r(6341);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(13859),i=r(97369),s=r(98064),u=r(48733),d=r(85532),c=r(79447),p=r(16279),f=r(9665),v=r(29823),h=r(93965),m=r(4844),w=r(64826),g=r(89840),x=r(72336),y=r(17691),b=r(84268),S=r(84406),C=r(63680),k=r(11712),j=r(10345),R=r(6024),T=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],P="Select",[E,I,D]=(0,i.N)(P),[M,L]=(0,u.A)(P,[D,h.Bk]),A=(0,h.Bk)(),[H,B]=M(P),[_,G]=M(P),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=A(t),[b,S]=n.useState(null),[C,k]=n.useState(null),[j,T]=n.useState(!1),N=(0,d.jH)(c),[I,D]=(0,y.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:P}),[M,L]=(0,y.i)({prop:i,defaultProp:s,onChange:u,caller:P}),B=n.useRef(null),G=!b||g||!!b.closest("form"),[V,O]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,R.jsx)(h.bL,{...x,children:(0,R.jsxs)(H,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:k,valueNodeHasChildren:j,onValueNodeHasChildrenChange:T,contentId:(0,v.B)(),value:M,onValueChange:L,open:I,onOpenChange:D,dir:N,triggerPointerDownPosRef:B,disabled:m,children:[(0,R.jsx)(E.Provider,{scope:t,children:(0,R.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),G?(0,R.jsxs)(eC,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===M?(0,R.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=P;var O="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),u=B(O,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=I(r),f=n.useRef("touch"),[v,m,g]=ej(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eR(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{d||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,R.jsx)(h.Mz,{asChild:!0,...i,children:(0,R.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(x(),e.preventDefault())})})})});F.displayName=O;var K="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=B(K,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{d(c)},[d,c]),(0,R.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(u.value)?(0,R.jsx)(R.Fragment,{children:a}):o})});W.displayName=K;var z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,R.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});z.displayName="SelectIcon";var U=e=>(0,R.jsx)(m.Z,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=B(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,R.jsx)(J,{...e,ref:t}):o?l.createPortal((0,R.jsx)(X,{scope:e.__scopeSelect,children:(0,R.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,R.jsx)("div",{children:e.children})})}),o):null});Z.displayName=q;var[X,Q]=M(q),Y=(0,g.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...C}=e,T=B(q,r),[N,P]=n.useState(null),[E,D]=n.useState(null),M=(0,s.s)(t,e=>P(e)),[L,A]=n.useState(null),[H,_]=n.useState(null),G=I(r),[V,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,k.Eq)(N)},[N]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[G,E]),W=n.useCallback(()=>K([L,N]),[K,L,N]);n.useEffect(()=>{V&&W()},[V,W]);let{onOpenChange:z,triggerPointerDownPosRef:U}=T;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=U.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=U.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,z,U]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Z,Q]=ej(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==T.value&&T.value===t||n)&&(A(e),n&&(F.current=!0))},[T.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==T.value&&T.value===t||n)&&_(e)},[T.value]),en="popper"===l?ee:$,el=en===ee?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,R.jsx)(X,{scope:r,content:N,viewport:E,onViewportChange:D,itemRefCallback:J,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:H,position:l,isPositioned:V,searchRef:Z,children:(0,R.jsx)(j.A,{as:Y,allowPinchZoom:!0,children:(0,R.jsx)(f.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=T.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,R.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,R.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...C,...el,onPlaced:()=>O(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=B(q,r),u=Q(q,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=I(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:C}=u,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=h(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+u+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),k=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,T=y.offsetHeight/2,N=f+v+(y.offsetTop+T);if(N<=R){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,T+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+w);d.style.height=N+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(R,f+x.offsetTop+(e?k:0)+T);d.style.height=t+(g-N)+"px",x.scrollTop=N-R+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,y,S,i.dir,l]);(0,b.N)(()=>k(),[k]);let[j,T]=n.useState();(0,b.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(k(),null==C||C(),g.current=!1)},[k,C]);return(0,R.jsx)(et,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,R.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,R.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,R.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=M(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Q(en,r),u=er(en,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,R.jsx)(E.Slot,{scope:r,children:(0,R.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=M(eo);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,R.jsx)(ea,{scope:r,id:l,children:(0,R.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eo;var es="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(es,r);return(0,R.jsx)(w.sG.div,{id:l.id,...n,ref:t})}).displayName=es;var eu="SelectItem",[ed,ec]=M(eu),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=B(eu,r),c=Q(eu,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),y=(0,v.B)(),b=n.useRef("touch"),S=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,R.jsx)(ed,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,R.jsx)(E.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,R.jsx)(w.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.m)(u.onFocus,()=>g(!0)),onBlur:(0,a.m)(u.onBlur,()=>g(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=eu;var ef="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=B(ef,r),d=Q(ef,r),c=ec(ef,r),p=G(ef,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,R.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.N)(()=>(x(g),()=>y(g)),[x,y,g]),(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});ev.displayName=ef;var eh="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ec(eh,r).isSelected?(0,R.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=eh;var ew="SelectScrollUpButton",eg=n.forwardRef((e,t)=>{let r=Q(ew,e.__scopeSelect),l=er(ew,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,R.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=ew;var ex="SelectScrollDownButton",ey=n.forwardRef((e,t)=>{let r=Q(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,R.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=ex;var eb=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Q("SelectScrollButton",r),s=n.useRef(null),u=I(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,R.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,R.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=B(eS,r),a=Q(eS,r);return o.open&&"popper"===a.position?(0,R.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=eS;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=(0,S.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,R.jsx)(w.sG.select,{...o,style:{...C.Qg,...o.style},ref:i,defaultValue:l})});function ek(e){return""===e||void 0===e}function ej(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eC.displayName="SelectBubbleInput";var eT=V,eN=F,eP=W,eE=z,eI=U,eD=Z,eM=el,eL=ep,eA=ev,eH=em,eB=eg,e_=ey},53138:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(45707).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},63680:(e,t,r)=>{r.d(t,{Qg:()=>a,bL:()=>s});var n=r(50628),l=r(64826),o=r(6024),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.span,{...e,ref:t,style:{...a,...e.style}}));i.displayName="VisuallyHidden";var s=i},76227:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(45707).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84406:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(50628);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},96155:(e,t,r)=>{r.d(t,{bL:()=>S,zi:()=>C});var n=r(50628),l=r(13859),o=r(98064),a=r(48733),i=r(17691),s=r(84406),u=r(42189),d=r(64826),c=r(6024),p="Switch",[f,v]=(0,a.A)(p),[h,m]=f(p),w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:s,defaultChecked:u,required:f,disabled:v,value:m="on",onCheckedChange:w,form:g,...x}=e,[S,C]=n.useState(null),k=(0,o.s)(t,e=>C(e)),j=n.useRef(!1),R=!S||g||!!S.closest("form"),[T,N]=(0,i.i)({prop:s,defaultProp:null!=u&&u,onChange:w,caller:p});return(0,c.jsxs)(h,{scope:r,checked:T,disabled:v,children:[(0,c.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":f,"data-state":b(T),"data-disabled":v?"":void 0,disabled:v,value:m,...x,ref:k,onClick:(0,l.m)(e.onClick,e=>{N(e=>!e),R&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),R&&(0,c.jsx)(y,{control:S,bubbles:!j.current,name:a,value:m,checked:T,required:f,disabled:v,form:g,style:{transform:"translateX(-100%)"}})]})});w.displayName=p;var g="SwitchThumb",x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,l=m(g,r);return(0,c.jsx)(d.sG.span,{"data-state":b(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t})});x.displayName=g;var y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:l,checked:a,bubbles:i=!0,...d}=e,p=n.useRef(null),f=(0,o.s)(p,t),v=(0,s.Z)(a),h=(0,u.X)(l);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==a&&t){let r=new Event("click",{bubbles:i});t.call(e,a),e.dispatchEvent(r)}},[v,a,i]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...d,tabIndex:-1,ref:f,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var S=w,C=x}}]);