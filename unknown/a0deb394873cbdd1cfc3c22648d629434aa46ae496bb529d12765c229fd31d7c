"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5514],{9665:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(50628),o=n(98064),a=n(64826),c=n(72336),u=n(6024),i="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:p,onUnmountAutoFocus:g,...y}=e,[E,b]=r.useState(null),w=(0,c.c)(p),S=(0,c.c)(g),C=r.useRef(null),k=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!E)return;let t=e.target;E.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(A.paused||!E)return;let t=e.relatedTarget;null!==t&&(E.contains(t)||m(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(E)});return E&&n.observe(E,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,E,A.paused]),r.useEffect(()=>{if(E){h.add(A);let e=document.activeElement;if(!E.contains(e)){let t=new CustomEvent(i,s);E.addEventListener(i,w),E.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(E).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(E))}return()=>{E.removeEventListener(i,w),setTimeout(()=>{let t=new CustomEvent(l,s);E.addEventListener(l,S),E.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),E.removeEventListener(l,S),h.remove(A)},0)}}},[E,w,S,A]);let N=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(a,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,u.jsx)(a.sG.div,{tabIndex:-1,...y,ref:k,onKeyDown:N})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=p(e,t)).unshift(t)},remove(t){var n;null==(n=(e=p(e,t))[0])||n.resume()}}}();function p(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},10345:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,a=n(88237),c=n(50628),u="right-scroll-bar-position",i="width-before-scroll-bar";function l(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?c.useLayoutEffect:c.useEffect,d=new WeakMap;function f(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o,c=(t=null,void 0===n&&(n=f),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(a)};c(),r={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),r}}}});return c.options=(0,a.__assign)({async:!0,ssr:!1},e),c}(),m=function(){},h=c.forwardRef(function(e,t){var n,r,o,u,i=c.useRef(null),f=c.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=f[0],p=f[1],g=e.forwardProps,y=e.children,E=e.className,b=e.removeScrollBar,w=e.enabled,S=e.shards,C=e.sideCar,k=e.noIsolation,A=e.inert,N=e.allowPinchZoom,M=e.as,L=e.gapMode,_=(0,a.__rest)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[i,t],r=function(e){return n.forEach(function(t){return l(t,e)})},(o=(0,c.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||l(e,null)}),r.forEach(function(e){t.has(e)||l(e,o)})}d.set(u,n)},[n]),u),R=(0,a.__assign)((0,a.__assign)({},_),h);return c.createElement(c.Fragment,null,w&&c.createElement(C,{sideCar:v,removeScrollBar:b,shards:S,noIsolation:k,inert:A,setCallbacks:p,allowPinchZoom:!!N,lockRef:i,gapMode:L}),g?c.cloneElement(c.Children.only(y),(0,a.__assign)((0,a.__assign)({},R),{ref:T})):c.createElement(void 0===M?"div":M,(0,a.__assign)({},R,{className:E,ref:T}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:i,zeroRight:u};var p=function(e){var t=e.sideCar,n=(0,a.__rest)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return c.createElement(r,(0,a.__assign)({},n))};p.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,c;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),c=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(c)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},E=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},w=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[w(n),w(r),w(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=E(),A="data-scroll-locked",N=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(i," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},L=function(){c.useEffect(function(){return document.body.setAttribute(A,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},_=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var a=c.useMemo(function(){return C(o)},[o]);return c.createElement(k,{styles:N(a,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var R=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",R,R),window.removeEventListener("test",R,R)}catch(e){T=!1}var x=!!T&&{passive:!1},I=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},P=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),F(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},F=function(e,t){return"v"===e?I(t,"overflowY"):I(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},O=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=c*r,i=n.target,l=t.contains(i),s=!1,d=u>0,f=0,v=0;do{var m=W(e,i),h=m[0],p=m[1]-m[2]-c*h;(h||p)&&F(e,i)&&(f+=p,v+=h),i=i instanceof ShadowRoot?i.host:i.parentNode}while(!l&&i!==document.body||l&&(t.contains(i)||t===i));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},D=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},j=0,X=[];let Y=(r=function(e){var t=c.useRef([]),n=c.useRef([0,0]),r=c.useRef(),o=c.useState(j++)[0],u=c.useState(E)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.__spreadArray)([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=c.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=B(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=P(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=P(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var v=r.current||o;return O(v,t,e,"h"===v?u:l,!0)},[]),s=c.useCallback(function(e){if(X.length&&X[X.length-1]===u){var n="deltaY"in e?D(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=c.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=c.useCallback(function(e){n.current=B(e),r.current=void 0},[]),v=c.useCallback(function(t){d(t.type,D(t),t.target,l(t,e.lockRef.current))},[]),m=c.useCallback(function(t){d(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);c.useEffect(function(){return X.push(u),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:m}),document.addEventListener("wheel",s,x),document.addEventListener("touchmove",s,x),document.addEventListener("touchstart",f,x),function(){X=X.filter(function(e){return e!==u}),document.removeEventListener("wheel",s,x),document.removeEventListener("touchmove",s,x),document.removeEventListener("touchstart",f,x)}},[]);var h=e.removeScrollBar,p=e.inert;return c.createElement(c.Fragment,null,p?c.createElement(u,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?c.createElement(_,{gapMode:e.gapMode}):null)},v.useMedium(r),p);var q=c.forwardRef(function(e,t){return c.createElement(h,(0,a.__assign)({},e,{ref:t,sideCar:Y}))});q.classNames=h.classNames;let H=q},11712:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,c={},u=0,i=function(e){return e&&(e.host||i(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=i(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});c[n]||(c[n]=new WeakMap);var s=c[n],d=[],f=new Set,v=new Set(l),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};l.forEach(m);var h=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),c=null!==t&&"false"!==t,u=(o.get(e)||0)+1,i=(s.get(e)||0)+1;o.set(e,u),s.set(e,i),d.push(e),1===u&&c&&a.set(e,!0),1===i&&e.setAttribute(n,"true"),c||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,c=s.get(e)-1;o.set(e,t),s.set(e,c),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),c||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,c={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),l(o,a,n,"aria-hidden")):function(){return null}}},16279:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(50628),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:c()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:c()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function c(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},81197:(e,t,n)=>{n.d(t,{F:()=>c});var r=n(49973);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,c=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:c,defaultVariants:u}=t,i=Object.keys(c).map(e=>{let t=null==n?void 0:n[e],r=null==u?void 0:u[e];if(null===t)return null;let a=o(t)||o(r);return c[e][a]}),l=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,i,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...u,...l}[t]):({...u,...l})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);