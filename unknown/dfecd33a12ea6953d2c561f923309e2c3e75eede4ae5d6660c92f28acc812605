exports.id=5480,exports.ids=[5480],exports.modules={9507:(e,t,r)=>{"use strict";r.d(t,{Vz:()=>B,bC:()=>tL,gK:()=>tO});var s,n,i,o,a,l,c=Object.create,h=Object.defineProperty,u=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,d=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,y=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,m=(e,t)=>h(e,"name",{value:t,configurable:!0}),g=(e,t)=>()=>(e&&(t=e(e=0)),t),_=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),b=(e,t)=>{for(var r in t)h(e,r,{get:t[r],enumerable:!0})},v=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of f(t))p.call(e,n)||n===r||h(e,n,{get:()=>t[n],enumerable:!(s=u(t,n))||s.enumerable});return e},w=(e,t,r)=>(r=null!=e?c(d(e)):{},v(!t&&e&&e.__esModule?r:h(r,"default",{value:e,enumerable:!0}),e)),S=e=>v(h({},"__esModule",{value:!0}),e),E=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r),x=_(e=>{T(),e.byteLength=l,e.toByteArray=h,e.fromByteArray=d;var t,r,s=[],n=[],i="u">typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(t=0,r=o.length;t<r;++t)s[t]=o[t],n[o.charCodeAt(t)]=t;function a(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var s=r===t?0:4-r%4;return[r,s]}function l(e){var t=a(e),r=t[0],s=t[1];return(r+s)*3/4-s}function c(e,t,r){return(t+r)*3/4-r}function h(e){var t,r,s=a(e),o=s[0],l=s[1],h=new i(c(e,o,l)),u=0,f=l>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],h[u++]=t>>16&255,h[u++]=t>>8&255,h[u++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,h[u++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,h[u++]=t>>8&255,h[u++]=255&t),h}function u(e){return s[e>>18&63]+s[e>>12&63]+s[e>>6&63]+s[63&e]}function f(e,t,r){for(var s=[],n=t;n<r;n+=3)s.push(u((e[n]<<16&0xff0000)+(e[n+1]<<8&65280)+(255&e[n+2])));return s.join("")}function d(e){for(var t,r=e.length,n=r%3,i=[],o=0,a=r-n;o<a;o+=16383)i.push(f(e,o,o+16383>a?a:o+16383));return 1===n?i.push(s[(t=e[r-1])>>2]+s[t<<4&63]+"=="):2===n&&i.push(s[(t=(e[r-2]<<8)+e[r-1])>>10]+s[t>>4&63]+s[t<<2&63]+"="),i.join("")}n[45]=62,n[95]=63,m(a,"getLens"),m(l,"byteLength"),m(c,"_byteLength"),m(h,"toByteArray"),m(u,"tripletToBase64"),m(f,"encodeChunk"),m(d,"fromByteArray")}),A=_(e=>{T(),e.read=function(e,t,r,s,n){var i,o,a=8*n-s-1,l=(1<<a)-1,c=l>>1,h=-7,u=r?n-1:0,f=r?-1:1,d=e[t+u];for(u+=f,i=d&(1<<-h)-1,d>>=-h,h+=a;h>0;i=256*i+e[t+u],u+=f,h-=8);for(o=i&(1<<-h)-1,i>>=-h,h+=s;h>0;o=256*o+e[t+u],u+=f,h-=8);if(0===i)i=1-c;else{if(i===l)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,s),i-=c}return(d?-1:1)*o*Math.pow(2,i-s)},e.write=function(e,t,r,s,n,i){var o,a,l,c=8*i-n-1,h=(1<<c)-1,u=h>>1,f=5960464477539062e-23*(23===n),d=s?0:i-1,p=s?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),o=h):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+u>=1?t+=f/l:t+=f*Math.pow(2,1-u),t*l>=2&&(o++,l/=2),o+u>=h?(a=0,o=h):o+u>=1?(a=(t*l-1)*Math.pow(2,n),o+=u):(a=t*Math.pow(2,u-1)*Math.pow(2,n),o=0));n>=8;e[r+d]=255&a,d+=p,a/=256,n-=8);for(o=o<<n|a,c+=n;c>0;e[r+d]=255&o,d+=p,o/=256,c-=8);e[r+d-p]|=128*y}}),R=_(e=>{T();var t=x(),r=A(),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function n(){try{let e=new Uint8Array(1),t={foo:m(function(){return 42},"foo")};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch{return!1}}function i(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,o.prototype),t}function o(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return h(e)}return a(e,t,r)}function a(e,t,r){if("string"==typeof e)return u(e,t);if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(eo(e,ArrayBuffer)||e&&eo(e.buffer,ArrayBuffer)||"u">typeof SharedArrayBuffer&&(eo(e,SharedArrayBuffer)||e&&eo(e.buffer,SharedArrayBuffer)))return p(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');let s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return o.from(s,t,r);let n=y(e);if(n)return n;if("u">typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return o.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function l(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e,t,r){return l(e),e<=0?i(e):void 0!==t?"string"==typeof r?i(e).fill(t,r):i(e).fill(t):i(e)}function h(e){return l(e),i(e<0?0:0|g(e))}function u(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!o.isEncoding(t))throw TypeError("Unknown encoding: "+t);let r=0|b(e,t),s=i(r),n=s.write(e,t);return n!==r&&(s=s.slice(0,n)),s}function f(e){let t=e.length<0?0:0|g(e.length),r=i(t);for(let s=0;s<t;s+=1)r[s]=255&e[s];return r}function d(e){if(eo(e,Uint8Array)){let t=new Uint8Array(e);return p(t.buffer,t.byteOffset,t.byteLength)}return f(e)}function p(e,t,r){let s;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),o.prototype),s}function y(e){if(o.isBuffer(e)){let t=0|g(e.length),r=i(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||ea(e.length)?i(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}function g(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function _(e){return+e!=e&&(e=0),o.alloc(+e)}function b(e,t){if(o.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||eo(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let r=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===r)return 0;let n=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return et(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return en(e).length;default:if(n)return s?-1:et(e).length;t=(""+t).toLowerCase(),n=!0}}function v(e,t,r){let s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0)||(r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return D(this,t,r);case"utf8":case"utf-8":return O(this,t,r);case"ascii":return M(this,t,r);case"latin1":case"binary":return N(this,t,r);case"base64":return P(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function w(e,t,r){let s=e[t];e[t]=e[r],e[r]=s}function S(e,t,r,s,n){if(0===e.length)return -1;if("string"==typeof r?(s=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),ea(r*=1)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return -1;r=e.length-1}else if(r<0)if(!n)return -1;else r=0;if("string"==typeof t&&(t=o.from(t,s)),o.isBuffer(t))return 0===t.length?-1:E(e,t,r,s,n);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):E(e,[t],r,s,n);throw TypeError("val must be string, number or Buffer")}function E(e,t,r,s,n){let i,o=1,a=e.length,l=t.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||t.length<2)return -1;o=2,a/=2,l/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(m(c,"read"),n){let s=-1;for(i=r;i<a;i++)if(c(e,i)===c(t,-1===s?0:i-s)){if(-1===s&&(s=i),i-s+1===l)return s*o}else -1!==s&&(i-=i-s),s=-1}else for(r+l>a&&(r=a-l),i=r;i>=0;i--){let r=!0;for(let s=0;s<l;s++)if(c(e,i+s)!==c(t,s)){r=!1;break}if(r)return i}return -1}function R(e,t,r,s){let n;r=Number(r)||0;let i=e.length-r;s?(s=Number(s))>i&&(s=i):s=i;let o=t.length;for(s>o/2&&(s=o/2),n=0;n<s;++n){let s=parseInt(t.substr(2*n,2),16);if(ea(s))break;e[r+n]=s}return n}function C(e,t,r,s){return ei(et(t,e.length-r),e,r,s)}function I(e,t,r,s){return ei(er(t),e,r,s)}function k(e,t,r,s){return ei(en(t),e,r,s)}function L(e,t,r,s){return ei(es(t,e.length-r),e,r,s)}function P(e,r,s){return 0===r&&s===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(r,s))}function O(e,t,r){r=Math.min(e.length,r);let s=[],n=t;for(;n<r;){let t=e[n],i=null,o=t>239?4:t>223?3:t>191?2:1;if(n+o<=r){let r,s,a,l;switch(o){case 1:t<128&&(i=t);break;case 2:(192&(r=e[n+1]))==128&&(l=(31&t)<<6|63&r)>127&&(i=l);break;case 3:r=e[n+1],s=e[n+2],(192&r)==128&&(192&s)==128&&(l=(15&t)<<12|(63&r)<<6|63&s)>2047&&(l<55296||l>57343)&&(i=l);break;case 4:r=e[n+1],s=e[n+2],a=e[n+3],(192&r)==128&&(192&s)==128&&(192&a)==128&&(l=(15&t)<<18|(63&r)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(i=l)}}null===i?(i=65533,o=1):i>65535&&(i-=65536,s.push(i>>>10&1023|55296),i=56320|1023&i),s.push(i),n+=o}return B(s)}function B(e){let t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);let r="",s=0;for(;s<t;)r+=String.fromCharCode.apply(String,e.slice(s,s+=4096));return r}function M(e,t,r){let s="";r=Math.min(e.length,r);for(let n=t;n<r;++n)s+=String.fromCharCode(127&e[n]);return s}function N(e,t,r){let s="";r=Math.min(e.length,r);for(let n=t;n<r;++n)s+=String.fromCharCode(e[n]);return s}function D(e,t,r){let s=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>s)&&(r=s);let n="";for(let s=t;s<r;++s)n+=el[e[s]];return n}function U(e,t,r){let s=e.slice(t,r),n="";for(let e=0;e<s.length-1;e+=2)n+=String.fromCharCode(s[e]+256*s[e+1]);return n}function F(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function j(e,t,r,s,n,i){if(!o.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<i)throw RangeError('"value" argument is out of bounds');if(r+s>e.length)throw RangeError("Index out of range")}function q(e,t,r,s,n){K(t,s,n,e,r,7);let i=Number(t&BigInt(0xffffffff));e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,r}function Q(e,t,r,s,n){K(t,s,n,e,r,7);let i=Number(t&BigInt(0xffffffff));e[r+7]=i,i>>=8,e[r+6]=i,i>>=8,e[r+5]=i,i>>=8,e[r+4]=i;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r+3]=o,o>>=8,e[r+2]=o,o>>=8,e[r+1]=o,o>>=8,e[r]=o,r+8}function W(e,t,r,s,n,i){if(r+s>e.length||r<0)throw RangeError("Index out of range")}function $(e,t,s,n,i){return t*=1,s>>>=0,i||W(e,t,s,4,34028234663852886e22,-34028234663852886e22),r.write(e,t,s,n,23,4),s+4}function G(e,t,s,n,i){return t*=1,s>>>=0,i||W(e,t,s,8,17976931348623157e292,-17976931348623157e292),r.write(e,t,s,n,52,8),s+8}e.Buffer=o,e.SlowBuffer=_,e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,o.TYPED_ARRAY_SUPPORT=n(),!o.TYPED_ARRAY_SUPPORT&&"u">typeof console&&"function"==typeof console.error&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),m(n,"typedArraySupport"),Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:m(function(){if(o.isBuffer(this))return this.buffer},"get")}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:m(function(){if(o.isBuffer(this))return this.byteOffset},"get")}),m(i,"createBuffer"),m(o,"Buffer"),o.poolSize=8192,m(a,"from"),o.from=function(e,t,r){return a(e,t,r)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array),m(l,"assertSize"),m(c,"alloc"),o.alloc=function(e,t,r){return c(e,t,r)},m(h,"allocUnsafe"),o.allocUnsafe=function(e){return h(e)},o.allocUnsafeSlow=function(e){return h(e)},m(u,"fromString"),m(f,"fromArrayLike"),m(d,"fromArrayView"),m(p,"fromArrayBuffer"),m(y,"fromObject"),m(g,"checked"),m(_,"SlowBuffer"),o.isBuffer=m(function(e){return null!=e&&!0===e._isBuffer&&e!==o.prototype},"isBuffer"),o.compare=m(function(e,t){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),eo(t,Uint8Array)&&(t=o.from(t,t.offset,t.byteLength)),!o.isBuffer(e)||!o.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,s=t.length;for(let n=0,i=Math.min(r,s);n<i;++n)if(e[n]!==t[n]){r=e[n],s=t[n];break}return r<s?-1:+(s<r)},"compare"),o.isEncoding=m(function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},"isEncoding"),o.concat=m(function(e,t){let r;if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;let s=o.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){let t=e[r];if(eo(t,Uint8Array))n+t.length>s.length?(o.isBuffer(t)||(t=o.from(t)),t.copy(s,n)):Uint8Array.prototype.set.call(s,t,n);else if(o.isBuffer(t))t.copy(s,n);else throw TypeError('"list" argument must be an Array of Buffers');n+=t.length}return s},"concat"),m(b,"byteLength"),o.byteLength=b,m(v,"slowToString"),o.prototype._isBuffer=!0,m(w,"swap"),o.prototype.swap16=m(function(){let e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)w(this,t,t+1);return this},"swap16"),o.prototype.swap32=m(function(){let e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)w(this,t,t+3),w(this,t+1,t+2);return this},"swap32"),o.prototype.swap64=m(function(){let e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)w(this,t,t+7),w(this,t+1,t+6),w(this,t+2,t+5),w(this,t+3,t+4);return this},"swap64"),o.prototype.toString=m(function(){let e=this.length;return 0===e?"":0==arguments.length?O(this,0,e):v.apply(this,arguments)},"toString"),o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=m(function(e){if(!o.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},"equals"),o.prototype.inspect=m(function(){let t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},"inspect"),s&&(o.prototype[s]=o.prototype.inspect),o.prototype.compare=m(function(e,t,r,s,n){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),!o.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===s&&(s=0),void 0===n&&(n=this.length),t<0||r>e.length||s<0||n>this.length)throw RangeError("out of range index");if(s>=n&&t>=r)return 0;if(s>=n)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,s>>>=0,n>>>=0,this===e)return 0;let i=n-s,a=r-t,l=Math.min(i,a),c=this.slice(s,n),h=e.slice(t,r);for(let e=0;e<l;++e)if(c[e]!==h[e]){i=c[e],a=h[e];break}return i<a?-1:+(a<i)},"compare"),m(S,"bidirectionalIndexOf"),m(E,"arrayIndexOf"),o.prototype.includes=m(function(e,t,r){return -1!==this.indexOf(e,t,r)},"includes"),o.prototype.indexOf=m(function(e,t,r){return S(this,e,t,r,!0)},"indexOf"),o.prototype.lastIndexOf=m(function(e,t,r){return S(this,e,t,r,!1)},"lastIndexOf"),m(R,"hexWrite"),m(C,"utf8Write"),m(I,"asciiWrite"),m(k,"base64Write"),m(L,"ucs2Write"),o.prototype.write=m(function(e,t,r,s){if(void 0===t)s="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)s=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===s&&(s="utf8")):(s=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");let i=!1;for(;;)switch(s){case"hex":return R(this,e,t,r);case"utf8":case"utf-8":return C(this,e,t,r);case"ascii":case"latin1":case"binary":return I(this,e,t,r);case"base64":return k(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return L(this,e,t,r);default:if(i)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),i=!0}},"write"),o.prototype.toJSON=m(function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},"toJSON"),m(P,"base64Slice"),m(O,"utf8Slice"),m(B,"decodeCodePointsArray"),m(M,"asciiSlice"),m(N,"latin1Slice"),m(D,"hexSlice"),m(U,"utf16leSlice"),o.prototype.slice=m(function(e,t){let r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);let s=this.subarray(e,t);return Object.setPrototypeOf(s,o.prototype),s},"slice"),m(F,"checkOffset"),o.prototype.readUintLE=o.prototype.readUIntLE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e],n=1,i=0;for(;++i<t&&(n*=256);)s+=this[e+i]*n;return s},"readUIntLE"),o.prototype.readUintBE=o.prototype.readUIntBE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e+--t],n=1;for(;t>0&&(n*=256);)s+=this[e+--t]*n;return s},"readUIntBE"),o.prototype.readUint8=o.prototype.readUInt8=m(function(e,t){return e>>>=0,t||F(e,1,this.length),this[e]},"readUInt8"),o.prototype.readUint16LE=o.prototype.readUInt16LE=m(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]|this[e+1]<<8},"readUInt16LE"),o.prototype.readUint16BE=o.prototype.readUInt16BE=m(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]<<8|this[e+1]},"readUInt16BE"),o.prototype.readUint32LE=o.prototype.readUInt32LE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},"readUInt32LE"),o.prototype.readUint32BE=o.prototype.readUInt32BE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},"readUInt32BE"),o.prototype.readBigUInt64LE=ec(m(function(e){J(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&Z(e,this.length-8);let s=t+256*this[++e]+65536*this[++e]+0x1000000*this[++e],n=this[++e]+256*this[++e]+65536*this[++e]+0x1000000*r;return BigInt(s)+(BigInt(n)<<BigInt(32))},"readBigUInt64LE")),o.prototype.readBigUInt64BE=ec(m(function(e){J(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&Z(e,this.length-8);let s=0x1000000*t+65536*this[++e]+256*this[++e]+this[++e],n=0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r;return(BigInt(s)<<BigInt(32))+BigInt(n)},"readBigUInt64BE")),o.prototype.readIntLE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e],n=1,i=0;for(;++i<t&&(n*=256);)s+=this[e+i]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*t)),s},"readIntLE"),o.prototype.readIntBE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=t,n=1,i=this[e+--s];for(;s>0&&(n*=256);)i+=this[e+--s]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},"readIntBE"),o.prototype.readInt8=m(function(e,t){return e>>>=0,t||F(e,1,this.length),128&this[e]?-((255-this[e]+1)*1):this[e]},"readInt8"),o.prototype.readInt16LE=m(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},"readInt16LE"),o.prototype.readInt16BE=m(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},"readInt16BE"),o.prototype.readInt32LE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},"readInt32LE"),o.prototype.readInt32BE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},"readInt32BE"),o.prototype.readBigInt64LE=ec(m(function(e){J(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&Z(e,this.length-8),(BigInt(this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24))<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+0x1000000*this[++e])},"readBigInt64LE")),o.prototype.readBigInt64BE=ec(m(function(e){J(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&Z(e,this.length-8),(BigInt((t<<24)+65536*this[++e]+256*this[++e]+this[++e])<<BigInt(32))+BigInt(0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r)},"readBigInt64BE")),o.prototype.readFloatLE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!0,23,4)},"readFloatLE"),o.prototype.readFloatBE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!1,23,4)},"readFloatBE"),o.prototype.readDoubleLE=m(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!0,52,8)},"readDoubleLE"),o.prototype.readDoubleBE=m(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!1,52,8)},"readDoubleBE"),m(j,"checkInt"),o.prototype.writeUintLE=o.prototype.writeUIntLE=m(function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){let s=Math.pow(2,8*r)-1;j(this,e,t,r,s,0)}let n=1,i=0;for(this[t]=255&e;++i<r&&(n*=256);)this[t+i]=e/n&255;return t+r},"writeUIntLE"),o.prototype.writeUintBE=o.prototype.writeUIntBE=m(function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){let s=Math.pow(2,8*r)-1;j(this,e,t,r,s,0)}let n=r-1,i=1;for(this[t+n]=255&e;--n>=0&&(i*=256);)this[t+n]=e/i&255;return t+r},"writeUIntBE"),o.prototype.writeUint8=o.prototype.writeUInt8=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,1,255,0),this[t]=255&e,t+1},"writeUInt8"),o.prototype.writeUint16LE=o.prototype.writeUInt16LE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeUInt16LE"),o.prototype.writeUint16BE=o.prototype.writeUInt16BE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeUInt16BE"),o.prototype.writeUint32LE=o.prototype.writeUInt32LE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},"writeUInt32LE"),o.prototype.writeUint32BE=o.prototype.writeUInt32BE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeUInt32BE"),m(q,"wrtBigUInt64LE"),m(Q,"wrtBigUInt64BE"),o.prototype.writeBigUInt64LE=ec(m(function(e,t=0){return q(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64LE")),o.prototype.writeBigUInt64BE=ec(m(function(e,t=0){return Q(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64BE")),o.prototype.writeIntLE=m(function(e,t,r,s){if(e*=1,t>>>=0,!s){let s=Math.pow(2,8*r-1);j(this,e,t,r,s-1,-s)}let n=0,i=1,o=0;for(this[t]=255&e;++n<r&&(i*=256);)e<0&&0===o&&0!==this[t+n-1]&&(o=1),this[t+n]=(e/i|0)-o&255;return t+r},"writeIntLE"),o.prototype.writeIntBE=m(function(e,t,r,s){if(e*=1,t>>>=0,!s){let s=Math.pow(2,8*r-1);j(this,e,t,r,s-1,-s)}let n=r-1,i=1,o=0;for(this[t+n]=255&e;--n>=0&&(i*=256);)e<0&&0===o&&0!==this[t+n+1]&&(o=1),this[t+n]=(e/i|0)-o&255;return t+r},"writeIntBE"),o.prototype.writeInt8=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},"writeInt8"),o.prototype.writeInt16LE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeInt16LE"),o.prototype.writeInt16BE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeInt16BE"),o.prototype.writeInt32LE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},"writeInt32LE"),o.prototype.writeInt32BE=m(function(e,t,r){return e*=1,t>>>=0,r||j(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeInt32BE"),o.prototype.writeBigInt64LE=ec(m(function(e,t=0){return q(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64LE")),o.prototype.writeBigInt64BE=ec(m(function(e,t=0){return Q(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64BE")),m(W,"checkIEEE754"),m($,"writeFloat"),o.prototype.writeFloatLE=m(function(e,t,r){return $(this,e,t,!0,r)},"writeFloatLE"),o.prototype.writeFloatBE=m(function(e,t,r){return $(this,e,t,!1,r)},"writeFloatBE"),m(G,"writeDouble"),o.prototype.writeDoubleLE=m(function(e,t,r){return G(this,e,t,!0,r)},"writeDoubleLE"),o.prototype.writeDoubleBE=m(function(e,t,r){return G(this,e,t,!1,r)},"writeDoubleBE"),o.prototype.copy=m(function(e,t,r,s){if(!o.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),s||0===s||(s=this.length),t>=e.length&&(t=e.length),t||(t=0),s>0&&s<r&&(s=r),s===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-t<s-r&&(s=e.length-t+r);let n=s-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,s):Uint8Array.prototype.set.call(e,this.subarray(r,s),t),n},"copy"),o.prototype.fill=m(function(e,t,r,s){let n;if("string"==typeof e){if("string"==typeof t?(s=t,t=0,r=this.length):"string"==typeof r&&(s=r,r=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!o.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){let t=e.charCodeAt(0);("utf8"===s&&t<128||"latin1"===s)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(n=t;n<r;++n)this[n]=e;else{let i=o.isBuffer(e)?e:o.from(e,s),a=i.length;if(0===a)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(n=0;n<r-t;++n)this[n+t]=i[n%a]}return this},"fill");var V={};function Y(e,t,r){var s;V[e]=(m(s=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}\
]: ${this.message}`}},"NodeError"),s)}function H(e){let t="",r=e.length,s=+("-"===e[0]);for(;r>=s+4;r-=3)t=`\
_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function z(e,t,r){J(t,"offset"),(void 0===e[t]||void 0===e[t+r])&&Z(t,e.length-(r+1))}function K(e,t,r,s,n,i){if(e>r||e<t){let s="bigint"==typeof t?"n":"",n;throw n=i>3?0===t||t===BigInt(0)?`>= 0${s} and < 2${s}\
 ** ${(i+1)*8}${s}`:`>= -(2${s} ** ${(i+1)*8-1}${s}) and < 2 ** ${(i+1)*8-1}${s}`:`>= ${t}${s} a\
nd <= ${r}${s}`,new V.ERR_OUT_OF_RANGE("value",n,e)}z(s,n,i)}function J(e,t){if("number"!=typeof e)throw new V.ERR_INVALID_ARG_TYPE(t,"number",e)}function Z(e,t,r){throw Math.floor(e)!==e?(J(e,r),new V.ERR_OUT_OF_RANGE(r||"offset","an integer",e)):t<0?new V.ERR_BUFFER_OUT_OF_BOUNDS:new V.ERR_OUT_OF_RANGE(r||"offset",`>= ${+!!r} and <= ${t}`,e)}m(Y,"E"),Y("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),Y("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argument must be of type number. Received typ\
e ${typeof t}`},TypeError),Y("ERR_OUT_OF_RANGE",function(e,t,r){let s=`The value of "${e}" is out o\
f range.`,n=r;return Number.isInteger(r)&&Math.abs(r)>0x100000000?n=H(String(r)):"bigint"==typeof r&&(n=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(n=H(n)),n+="n"),s+=` It must be ${t}. Re\
ceived ${n}`},RangeError),m(H,"addNumericalSeparator"),m(z,"checkBounds"),m(K,"checkIntBI"),m(J,"validateNumber"),m(Z,"boundsError");var X=/[^+/0-9A-Za-z-_]/g;function ee(e){if((e=(e=e.split("=")[0]).trim().replace(X,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function et(e,t){t=t||1/0;let r,s=e.length,n=null,i=[];for(let o=0;o<s;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!n){if(r>56319||o+1===s){(t-=3)>-1&&i.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(t-=3)>-1&&i.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function er(e){let t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function es(e,t){let r,s,n=[];for(let i=0;i<e.length&&!((t-=2)<0);++i)s=(r=e.charCodeAt(i))>>8,n.push(r%256),n.push(s);return n}function en(e){return t.toByteArray(ee(e))}function ei(e,t,r,s){let n;for(n=0;n<s&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}function eo(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function ea(e){return e!=e}m(ee,"base64clean"),m(et,"utf8ToBytes"),m(er,"asciiToBytes"),m(es,"utf16leToBytes"),m(en,"base64ToBytes"),m(ei,"blitBuffer"),m(eo,"isInstance"),m(ea,"numberIsNaN");var el=function(){let e="0123456789abcdef",t=Array(256);for(let r=0;r<16;++r){let s=16*r;for(let n=0;n<16;++n)t[s+n]=e[r]+e[n]}return t}();function ec(e){return typeof BigInt>"u"?eh:e}function eh(){throw Error("BigInt not supported")}m(ec,"defineBigIntMethod"),m(eh,"BufferBigIntNotDefined")}),T=g(()=>{i=globalThis,o=globalThis.setImmediate??(e=>setTimeout(e,0)),globalThis.clearImmediate??(e=>clearTimeout(e)),a="function"==typeof globalThis.Buffer&&"function"==typeof globalThis.Buffer.allocUnsafe?globalThis.Buffer:R().Buffer,(l=globalThis.process??{}).env??(l.env={});try{l.nextTick(()=>{})}catch{let e=Promise.resolve();l.nextTick=e.then.bind(e)}}),C=_((e,t)=>{T();var r,s="object"==typeof Reflect?Reflect:null,n=s&&"function"==typeof s.apply?s.apply:m(function(e,t,r){return Function.prototype.apply.call(e,t,r)},"ReflectApply");function i(e){console&&console.warn&&console.warn(e)}r=s&&"function"==typeof s.ownKeys?s.ownKeys:Object.getOwnPropertySymbols?m(function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))},"ReflectOwnKeys"):m(function(e){return Object.getOwnPropertyNames(e)},"ReflectOwnKeys"),m(i,"ProcessEmitWarning");var o=Number.isNaN||m(function(e){return e!=e},"NumberIsNaN");function a(){a.init.call(this)}m(a,"EventEmitter"),t.exports=a,t.exports.once=v,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var l=10;function c(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function h(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function u(e,t,r,s){var n,o,a;if(c(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]),void 0===a)a=o[t]=r,++e._eventsCount;else if("function"==typeof a?a=o[t]=s?[r,a]:[a,r]:s?a.unshift(r):a.push(r),(n=h(e))>0&&a.length>n&&!a.warned){a.warned=!0;var l=Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=a.length,i(l)}return e}function f(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var s={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},n=f.bind(s);return n.listener=r,s.wrapFn=n,n}function p(e,t,r){var s=e._events;if(void 0===s)return[];var n=s[t];return void 0===n?[]:"function"==typeof n?r?[n.listener||n]:[n]:r?b(n):g(n,n.length)}function y(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function g(e,t){for(var r=Array(t),s=0;s<t;++s)r[s]=e[s];return r}function _(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function b(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}function v(e,t){return new Promise(function(r,s){function n(r){e.removeListener(t,i),s(r)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",n),r([].slice.call(arguments))}m(n,"errorListener"),m(i,"resolver"),S(e,t,i,{once:!0}),"error"!==t&&w(e,n,{once:!0})})}function w(e,t,r){"function"==typeof e.on&&S(e,"error",t,r)}function S(e,t,r,s){if("function"==typeof e.on)s.once?e.once(t,r):e.on(t,r);else if("function"==typeof e.addEventListener)e.addEventListener(t,m(function n(i){s.once&&e.removeEventListener(t,n),r(i)},"wrapListener"));else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}m(c,"checkListener"),Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:m(function(){return l},"get"),set:m(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");l=e},"set")}),a.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=m(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},"setMaxListeners"),m(h,"_getMaxListeners"),a.prototype.getMaxListeners=m(function(){return h(this)},"getMaxListeners"),a.prototype.emit=m(function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var s="error"===e,i=this._events;if(void 0!==i)s=s&&void 0===i.error;else if(!s)return!1;if(s){if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var o,a=Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var l=i[e];if(void 0===l)return!1;if("function"==typeof l)n(l,this,t);else for(var c=l.length,h=g(l,c),r=0;r<c;++r)n(h[r],this,t);return!0},"emit"),m(u,"_addListener"),a.prototype.addListener=m(function(e,t){return u(this,e,t,!1)},"addListener"),a.prototype.on=a.prototype.addListener,a.prototype.prependListener=m(function(e,t){return u(this,e,t,!0)},"prependListener"),m(f,"onceWrapper"),m(d,"_onceWrap"),a.prototype.once=m(function(e,t){return c(t),this.on(e,d(this,e,t)),this},"once"),a.prototype.prependOnceListener=m(function(e,t){return c(t),this.prependListener(e,d(this,e,t)),this},"prependOnceListener"),a.prototype.removeListener=m(function(e,t){var r,s,n,i,o;if(c(t),void 0===(s=this._events)||void 0===(r=s[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(n=-1,i=r.length-1;i>=0;i--)if(r[i]===t||r[i].listener===t){o=r[i].listener,n=i;break}if(n<0)return this;0===n?r.shift():_(r,n),1===r.length&&(s[e]=r[0]),void 0!==s.removeListener&&this.emit("removeListener",e,o||t)}return this},"removeListener"),a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=m(function(e){var t,r,s;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var n,i=Object.keys(r);for(s=0;s<i.length;++s)"removeListener"!==(n=i[s])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(s=t.length-1;s>=0;s--)this.removeListener(e,t[s]);return this},"removeAllListeners"),m(p,"_listeners"),a.prototype.listeners=m(function(e){return p(this,e,!0)},"listeners"),a.prototype.rawListeners=m(function(e){return p(this,e,!1)},"rawListeners"),a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):y.call(e,t)},a.prototype.listenerCount=y,m(y,"listenerCount"),a.prototype.eventNames=m(function(){return this._eventsCount>0?r(this._events):[]},"eventNames"),m(g,"arrayClone"),m(_,"spliceOne"),m(b,"unwrapListeners"),m(v,"once"),m(w,"addErrorHandlerIfEventEmitter"),m(S,"eventTargetAgnosticAddListener")}),I={};function k(e){return 0}b(I,{Socket:()=>B,isIP:()=>k});var L,P,O,B,M=g(()=>{T(),L=w(C(),1),m(k,"isIP"),P=/^[^.]+\./,O=class e extends L.EventEmitter{constructor(){super(...arguments),E(this,"opts",{}),E(this,"connecting",!1),E(this,"pending",!0),E(this,"writable",!0),E(this,"encrypted",!1),E(this,"authorized",!1),E(this,"destroyed",!1),E(this,"ws",null),E(this,"writeBuffer"),E(this,"tlsState",0),E(this,"tlsRead"),E(this,"tlsWrite")}static get poolQueryViaFetch(){return e.opts.poolQueryViaFetch??e.defaults.poolQueryViaFetch}static set poolQueryViaFetch(t){e.opts.poolQueryViaFetch=t}static get fetchEndpoint(){return e.opts.fetchEndpoint??e.defaults.fetchEndpoint}static set fetchEndpoint(t){e.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(e){console.warn("The `fetchConnectionCache` option is deprecated (now always `true`)")}static get fetchFunction(){return e.opts.fetchFunction??e.defaults.fetchFunction}static set fetchFunction(t){e.opts.fetchFunction=t}static get webSocketConstructor(){return e.opts.webSocketConstructor??e.defaults.webSocketConstructor}static set webSocketConstructor(t){e.opts.webSocketConstructor=t}get webSocketConstructor(){return this.opts.webSocketConstructor??e.webSocketConstructor}set webSocketConstructor(e){this.opts.webSocketConstructor=e}static get wsProxy(){return e.opts.wsProxy??e.defaults.wsProxy}static set wsProxy(t){e.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??e.wsProxy}set wsProxy(e){this.opts.wsProxy=e}static get coalesceWrites(){return e.opts.coalesceWrites??e.defaults.coalesceWrites}static set coalesceWrites(t){e.opts.coalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??e.coalesceWrites}set coalesceWrites(e){this.opts.coalesceWrites=e}static get useSecureWebSocket(){return e.opts.useSecureWebSocket??e.defaults.useSecureWebSocket}static set useSecureWebSocket(t){e.opts.useSecureWebSocket=t}get useSecureWebSocket(){return this.opts.useSecureWebSocket??e.useSecureWebSocket}set useSecureWebSocket(e){this.opts.useSecureWebSocket=e}static get forceDisablePgSSL(){return e.opts.forceDisablePgSSL??e.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){e.opts.forceDisablePgSSL=t}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??e.forceDisablePgSSL}set forceDisablePgSSL(e){this.opts.forceDisablePgSSL=e}static get disableSNI(){return e.opts.disableSNI??e.defaults.disableSNI}static set disableSNI(t){e.opts.disableSNI=t}get disableSNI(){return this.opts.disableSNI??e.disableSNI}set disableSNI(e){this.opts.disableSNI=e}static get pipelineConnect(){return e.opts.pipelineConnect??e.defaults.pipelineConnect}static set pipelineConnect(t){e.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??e.pipelineConnect}set pipelineConnect(e){this.opts.pipelineConnect=e}static get subtls(){return e.opts.subtls??e.defaults.subtls}static set subtls(t){e.opts.subtls=t}get subtls(){return this.opts.subtls??e.subtls}set subtls(e){this.opts.subtls=e}static get pipelineTLS(){return e.opts.pipelineTLS??e.defaults.pipelineTLS}static set pipelineTLS(t){e.opts.pipelineTLS=t}get pipelineTLS(){return this.opts.pipelineTLS??e.pipelineTLS}set pipelineTLS(e){this.opts.pipelineTLS=e}static get rootCerts(){return e.opts.rootCerts??e.defaults.rootCerts}static set rootCerts(t){e.opts.rootCerts=t}get rootCerts(){return this.opts.rootCerts??e.rootCerts}set rootCerts(e){this.opts.rootCerts=e}wsProxyAddrForHost(e,t){let r=this.wsProxy;if(void 0===r)throw Error("No WebSocket proxy is configured. Please see https://github.com/neondatabase/serverless/blob/main/CONFIG.md#wsproxy-string--host-string-port-number--string--string");return"function"==typeof r?r(e,t):`${r}?address=${e}:${t}`}setNoDelay(){return this}setKeepAlive(){return this}ref(){return this}unref(){return this}connect(e,t,r){this.connecting=!0,r&&this.once("connect",r);let s=m(()=>{this.connecting=!1,this.pending=!1,this.emit("connect"),this.emit("ready")},"handleWebSocketOpen"),n=m((e,t=!1)=>{e.binaryType="arraybuffer",e.addEventListener("error",e=>{this.emit("error",e),this.emit("close")}),e.addEventListener("message",e=>{if(0===this.tlsState){let t=a.from(e.data);this.emit("data",t)}}),e.addEventListener("close",()=>{this.emit("close")}),t?s():e.addEventListener("open",s)},"configureWebSocket"),i;try{i=this.wsProxyAddrForHost(t,"string"==typeof e?parseInt(e,10):e)}catch(e){this.emit("error",e),this.emit("close");return}try{let e=(this.useSecureWebSocket?"wss:":"ws:")+"//"+i;if(void 0!==this.webSocketConstructor)this.ws=new this.webSocketConstructor(e),n(this.ws);else try{this.ws=new WebSocket(e),n(this.ws)}catch{this.ws=new __unstable_WebSocket(e),n(this.ws)}}catch(e){fetch((this.useSecureWebSocket?"https:":"http:")+"//"+i,{headers:{Upgrade:"websocket"}}).then(t=>{if(this.ws=t.webSocket,null==this.ws)throw e;this.ws.accept(),n(this.ws,!0)}).catch(e=>{this.emit("error",Error(`All attempts to open a WebSocket to connect to the database failed. Please refer \
to https://github.com/neondatabase/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websoc\
ket--undefined. Details: ${e}`)),this.emit("close")})}}async startTls(e){if(void 0===this.subtls)throw Error("For Postgres SSL connections, you must set `neonConfig.subtls` to the subtls library. See https://github.com/neondatabase/serverless/blob/main/CONFIG.md for more information.");this.tlsState=1;let t=await this.subtls.TrustedCert.databaseFromPEM(this.rootCerts),r=new this.subtls.WebSocketReadQueue(this.ws),s=r.read.bind(r),n=this.rawWrite.bind(this),{read:i,write:o}=await this.subtls.startTls(e,t,s,n,{useSNI:!this.disableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=i,this.tlsWrite=o,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit("secureConnection",this),this.tlsReadLoop()}async tlsReadLoop(){for(;;){let e=await this.tlsRead();if(void 0===e)break;{let t=a.from(e);this.emit("data",t)}}}rawWrite(e){if(!this.coalesceWrites)return void this.ws.send(e);if(void 0===this.writeBuffer)this.writeBuffer=e,setTimeout(()=>{this.ws.send(this.writeBuffer),this.writeBuffer=void 0},0);else{let t=new Uint8Array(this.writeBuffer.length+e.length);t.set(this.writeBuffer),t.set(e,this.writeBuffer.length),this.writeBuffer=t}}write(e,t="utf8",r=e=>{}){return 0===e.length?r():("string"==typeof e&&(e=a.from(e,t)),0===this.tlsState?(this.rawWrite(e),r()):1===this.tlsState?this.once("secureConnection",()=>{this.write(e,t,r)}):(this.tlsWrite(e),r())),!0}end(e=a.alloc(0),t="utf8",r=()=>{}){return this.write(e,t,()=>{this.ws.close(),r()}),this}destroy(){return this.destroyed=!0,this.end()}},m(O,"Socket"),E(O,"defaults",{poolQueryViaFetch:!1,fetchEndpoint:m((e,t,r)=>{let s;return"https://"+(r?.jwtAuth?e.replace(P,"apiauth."):e.replace(P,"api."))+"/sql"},"fetchEndpoint"),fetchConnectionCache:!0,fetchFunction:void 0,webSocketConstructor:void 0,wsProxy:m(e=>e+"/v2","wsProxy"),useSecureWebSocket:!0,forceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:"password",subtls:void 0,rootCerts:"",pipelineTLS:!1,disableSNI:!1}),E(O,"opts",{}),B=O}),N={};function D(e,t=!1){let{protocol:r}=new URL(e),{username:s,password:n,host:i,hostname:o,port:a,pathname:l,search:c,searchParams:h,hash:u}=new URL("http:"+e.substring(r.length));n=decodeURIComponent(n),s=decodeURIComponent(s),l=decodeURIComponent(l);let f=s+":"+n,d=t?Object.fromEntries(h.entries()):c;return{href:e,protocol:r,auth:f,username:s,password:n,host:i,hostname:o,port:a,pathname:l,search:c,query:d,hash:u}}b(N,{parse:()=>D});var U=g(()=>{T(),m(D,"parse")}),F=_(e=>{T(),e.parse=function(e,t){return new r(e,t).parse()};var t=class e{constructor(e,t){this.source=e,this.transform=t||s,this.position=0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){return this.position>=this.source.length}nextCharacter(){var e=this.source[this.position++];return"\\"===e?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){this.recorded.push(e)}newEntry(e){var t;(this.recorded.length>0||e)&&("NULL"!==(t=this.recorded.join(""))||e||(t=null),null!==t&&(t=this.transform(t)),this.entries.push(t),this.recorded=[])}consumeDimensions(){if("["===this.source[0])for(;!this.isEof()&&"="!==this.nextCharacter().value;);}parse(t){var r,s,n;for(this.consumeDimensions();!this.isEof();)if("{"!==(r=this.nextCharacter()).value||n){if("}"!==r.value||n)'"'!==r.value||r.escaped?","!==r.value||n?this.record(r.value):this.newEntry():(n&&this.newEntry(!0),n=!n);else if(this.dimension--,!this.dimension&&(this.newEntry(),t))return this.entries}else this.dimension++,this.dimension>1&&(s=new e(this.source.substr(this.position-1),this.transform),this.entries.push(s.parse(!0)),this.position+=s.position-2);if(0!==this.dimension)throw Error("array dimension not balanced");return this.entries}};m(t,"ArrayParser");var r=t;function s(e){return e}m(s,"identity")}),j=_((e,t)=>{T();var r=F();t.exports={create:m(function(e,t){return{parse:m(function(){return r.parse(e,t)},"parse")}},"create")}}),q=_((e,t)=>{T();var r=/(\d{1,})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})(\.\d{1,})?.*?( BC)?$/,s=/^(\d{1,})-(\d{2})-(\d{2})( BC)?$/,n=/([Z+-])(\d{2})?:?(\d{2})?:?(\d{2})?/,i=/^-?infinity$/;function o(e){var t=s.exec(e);if(t){var r=parseInt(t[1],10);t[4]&&(r=l(r));var n=new Date(r,parseInt(t[2],10)-1,t[3]);return c(r)&&n.setFullYear(r),n}}function a(e){if(e.endsWith("+00"))return 0;var t=n.exec(e.split(" ")[1]);if(t){var r=t[1];return"Z"===r?0:(3600*parseInt(t[2],10)+60*parseInt(t[3]||0,10)+parseInt(t[4]||0,10))*("-"===r?-1:1)*1e3}}function l(e){return-(e-1)}function c(e){return e>=0&&e<100}t.exports=m(function(e){if(i.test(e))return Number(e.replace("i","I"));var t=r.exec(e);if(!t)return o(e)||null;var s=!!t[8],n=parseInt(t[1],10);s&&(n=l(n));var h=parseInt(t[2],10)-1,u=t[3],f=parseInt(t[4],10),d=parseInt(t[5],10),p=parseInt(t[6],10),y=t[7];y=y?1e3*parseFloat(y):0;var m,g=a(e);return null!=g?(m=new Date(Date.UTC(n,h,u,f,d,p,y)),c(n)&&m.setUTCFullYear(n),0!==g&&m.setTime(m.getTime()-g)):(m=new Date(n,h,u,f,d,p,y),c(n)&&m.setFullYear(n)),m},"parseDate"),m(o,"getDate"),m(a,"timeZoneOffset"),m(l,"bcYearToNegativeYear"),m(c,"is0To99")}),Q=_((e,t)=>{T(),t.exports=s;var r=Object.prototype.hasOwnProperty;function s(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)r.call(s,n)&&(e[n]=s[n])}return e}m(s,"extend")}),W=_((e,t)=>{T();var r=Q();function s(e){if(!(this instanceof s))return new s(e);r(this,d(e))}t.exports=s,m(s,"PostgresInterval");var n=["seconds","minutes","hours","days","months","years"];s.prototype.toPostgres=function(){var e=n.filter(this.hasOwnProperty,this);return this.milliseconds&&0>e.indexOf("seconds")&&e.push("seconds"),0===e.length?"0":e.map(function(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\.?0+$/,"")),t+" "+e},this).join(" ")};var i={years:"Y",months:"M",days:"D",hours:"H",minutes:"M",seconds:"S"},o=["years","months","days"],a=["hours","minutes","seconds"];s.prototype.toISOString=s.prototype.toISO=function(){return"P"+o.map(e,this).join("")+"T"+a.map(e,this).join("");function e(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/0+$/,"")),t+i[e]}};var l="([+-]?\\d+)",c=new RegExp([l+"\\s+years?",l+"\\s+mons?",l+"\\s+days?","([+-])?([\\d]*):(\\d\\d):(\\d\\d)\\.?(\\d{1,6})?"].map(function(e){return"("+e+")?"}).join("\\s*")),h={years:2,months:4,days:6,hours:9,minutes:10,seconds:11,milliseconds:12},u=["hours","minutes","seconds","milliseconds"];function f(e){return parseInt(e+"000000".slice(e.length),10)/1e3}function d(e){if(!e)return{};var t=c.exec(e),r="-"===t[8];return Object.keys(h).reduce(function(e,s){var n=t[h[s]];return n&&(n="milliseconds"===s?f(n):parseInt(n,10))&&(r&&~u.indexOf(s)&&(n*=-1),e[s]=n),e},{})}m(f,"parseMilliseconds"),m(d,"parse")}),$=_((e,t)=>{T(),t.exports=m(function(e){if(/^\\x/.test(e))return new a(e.substr(2),"hex");for(var t="",r=0;r<e.length;)if("\\"!==e[r])t+=e[r],++r;else if(/[0-7]{3}/.test(e.substr(r+1,3)))t+=String.fromCharCode(parseInt(e.substr(r+1,3),8)),r+=4;else{for(var s=1;r+s<e.length&&"\\"===e[r+s];)s++;for(var n=0;n<Math.floor(s/2);++n)t+="\\";r+=2*Math.floor(s/2)}return new a(t,"binary")},"parseBytea")}),G=_((e,t)=>{T();var r=F(),s=j(),n=q(),i=W(),o=$();function a(e){return m(function(t){return null===t?t:e(t)},"nullAllowed")}function l(e){return null===e?e:"TRUE"===e||"t"===e||"true"===e||"y"===e||"yes"===e||"on"===e||"1"===e}function c(e){return e?r.parse(e,l):null}function h(e){return parseInt(e,10)}function u(e){return e?r.parse(e,a(h)):null}function f(e){return e?r.parse(e,a(function(e){return w(e).trim()})):null}m(a,"allowNull"),m(l,"parseBool"),m(c,"parseBoolArray"),m(h,"parseBaseTenInt"),m(u,"parseIntegerArray"),m(f,"parseBigIntegerArray");var d=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=E(e)),e}).parse():null},"parsePointArray"),p=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=parseFloat(e)),e}).parse():null},"parseFloatArray"),y=m(function(e){return e?s.create(e).parse():null},"parseStringArray"),g=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=n(e)),e}).parse():null},"parseDateArray"),_=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=i(e)),e}).parse():null},"parseIntervalArray"),b=m(function(e){return e?r.parse(e,a(o)):null},"parseByteAArray"),v=m(function(e){return parseInt(e,10)},"parseInteger"),w=m(function(e){var t=String(e);return/^\d+$/.test(t)?t:e},"parseBigInteger"),S=m(function(e){return e?r.parse(e,a(JSON.parse)):null},"parseJsonArray"),E=m(function(e){return"("!==e[0]?null:{x:parseFloat((e=e.substring(1,e.length-1).split(","))[0]),y:parseFloat(e[1])}},"parsePoint"),x=m(function(e){if("<"!==e[0]&&"("!==e[1])return null;for(var t="(",r="",s=!1,n=2;n<e.length-1;n++){if(s||(t+=e[n]),")"===e[n]){s=!0;continue}s&&","!==e[n]&&(r+=e[n])}var i=E(t);return i.radius=parseFloat(r),i},"parseCircle");t.exports={init:m(function(e){e(20,w),e(21,v),e(23,v),e(26,v),e(700,parseFloat),e(701,parseFloat),e(16,l),e(1082,n),e(1114,n),e(1184,n),e(600,E),e(651,y),e(718,x),e(1e3,c),e(1001,b),e(1005,u),e(1007,u),e(1028,u),e(1016,f),e(1017,d),e(1021,p),e(1022,p),e(1231,p),e(1014,y),e(1015,y),e(1008,y),e(1009,y),e(1040,y),e(1041,y),e(1115,g),e(1182,g),e(1185,g),e(1186,i),e(1187,_),e(17,o),e(114,JSON.parse.bind(JSON)),e(3802,JSON.parse.bind(JSON)),e(199,S),e(3807,S),e(3907,y),e(2951,y),e(791,y),e(1183,y),e(1270,y)},"init")}}),V=_((e,t)=>{function r(e){var t=e.readInt32BE(0),r=e.readUInt32BE(4),s="";t<0&&(t=~t+(0===r),r=~r+1>>>0,s="-");var n,i,o,a,l,c,h="";if(n=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*n+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return s+o+h;for(a="",l=6-o.length,c=0;c<l;c++)a+="0";if(h=a+o+h,n=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*n+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return s+o+h;for(a="",l=6-o.length,c=0;c<l;c++)a+="0";if(h=a+o+h,n=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*n+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return s+o+h;for(a="",l=6-o.length,c=0;c<l;c++)a+="0";return h=a+o+h,s+(o=""+(i=0x100000000*(n=t%1e6)+r)%1e6)+h}T(),m(r,"readInt8"),t.exports=r}),Y=_((e,t)=>{T();var r=V(),s=m(function(e,t,r,s,n){r=r||0,s=s||!1,n=n||function(e,t,r){return e*Math.pow(2,r)+t};var i=r>>3,o=m(function(e){return s?255&~e:e},"inv"),a=255,l=8-r%8;t<l&&(a=255<<8-t&255,l=t),r&&(a>>=r%8);var c=0;r%8+t>=8&&(c=n(0,o(e[i])&a,l));for(var h=t+r>>3,u=i+1;u<h;u++)c=n(c,o(e[u]),8);var f=(t+r)%8;return f>0&&(c=n(c,o(e[h])>>8-f,f)),c},"parseBits"),n=m(function(e,t,r){var n=Math.pow(2,r-1)-1,i=s(e,1),o=s(e,r,1);if(0===o)return 0;var a=1,l=s(e,t,r+1,!1,m(function(e,t,r){0===e&&(e=1);for(var s=1;s<=r;s++)a/=2,(t&1<<r-s)>0&&(e+=a);return e},"parsePrecisionBits"));return o==Math.pow(2,r+1)-1?0===l?0===i?1/0:-1/0:NaN:(0===i?1:-1)*Math.pow(2,o-n)*l},"parseFloatFromBits"),i=m(function(e){return 1==s(e,1)?-1*(s(e,15,1,!0)+1):s(e,15,1)},"parseInt16"),o=m(function(e){return 1==s(e,1)?-1*(s(e,31,1,!0)+1):s(e,31,1)},"parseInt32"),a=m(function(e){return n(e,23,8)},"parseFloat32"),l=m(function(e){return n(e,52,11)},"parseFloat64"),c=m(function(e){var t=s(e,16,32);if(49152==t)return NaN;for(var r=Math.pow(1e4,s(e,16,16)),n=0,i=s(e,16),o=0;o<i;o++)n+=s(e,16,64+16*o)*r,r/=1e4;var a=Math.pow(10,s(e,16,48));return(0===t?1:-1)*Math.round(n*a)/a},"parseNumeric"),h=m(function(e,t){var r=s(t,1),n=s(t,63,1),i=new Date((0===r?1:-1)*n/1e3+9466848e5);return e||i.setTime(i.getTime()+6e4*i.getTimezoneOffset()),i.usec=n%1e3,i.getMicroSeconds=function(){return this.usec},i.setMicroSeconds=function(e){this.usec=e},i.getUTCMicroSeconds=function(){return this.usec},i},"parseDate"),u=m(function(e){for(var t=s(e,32),r=(s(e,32,32),s(e,32,64)),n=96,i=[],o=0;o<t;o++)i[o]=s(e,32,n),n+=64;var a=m(function(t){var r,i=s(e,32,n);return(n+=32,0xffffffff==i)?null:23==t||20==t?(r=s(e,8*i,n),n+=8*i,r):25==t?e.toString(this.encoding,n>>3,(n+=i<<3)>>3):void console.log("ERROR: ElementType not implemented: "+t)},"parseElement"),l=m(function(e,t){var r,s=[];if(e.length>1){var n=e.shift();for(r=0;r<n;r++)s[r]=l(e,t);e.unshift(n)}else for(r=0;r<e[0];r++)s[r]=a(t);return s},"parse");return l(i,r)},"parseArray"),f=m(function(e){return e.toString("utf8")},"parseText"),d=m(function(e){return null===e?null:s(e,8)>0},"parseBool");t.exports={init:m(function(e){e(20,r),e(21,i),e(23,o),e(26,o),e(1700,c),e(700,a),e(701,l),e(16,d),e(1114,h.bind(null,!1)),e(1184,h.bind(null,!0)),e(1e3,u),e(1007,u),e(1016,u),e(1008,u),e(1009,u),e(25,f)},"init")}}),H=_((e,t)=>{T(),t.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,REGPROC:24,TEXT:25,OID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,SMGR:210,PATH:602,POLYGON:604,CIDR:650,FLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,TINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,INET:869,ACLITEM:1033,BPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,TIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,REGOPERATOR:2204,REGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,PG_NDISTINCT:3361,PG_DEPENDENCIES:3402,TSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,REGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,REGROLE:4096}}),z=_(e=>{T();var t=G(),r=Y(),s=j(),n=H();e.getTypeParser=a,e.setTypeParser=l,e.arrayParser=s,e.builtins=n;var i={text:{},binary:{}};function o(e){return String(e)}function a(e,t){return i[t=t||"text"]&&i[t][e]||o}function l(e,t,r){"function"==typeof t&&(r=t,t="text"),i[t][e]=r}m(o,"noParse"),m(a,"getTypeParser"),m(l,"setTypeParser"),t.init(function(e,t){i.text[e]=t}),r.init(function(e,t){i.binary[e]=t})}),K=_((e,t)=>{T();var r=z();function s(e){this._types=e||r,this.text={},this.binary={}}m(s,"TypeOverrides"),s.prototype.getOverrides=function(e){switch(e){case"text":return this.text;case"binary":return this.binary;default:return{}}},s.prototype.setTypeParser=function(e,t,r){"function"==typeof t&&(r=t,t="text"),this.getOverrides(t)[e]=r},s.prototype.getTypeParser=function(e,t){return t=t||"text",this.getOverrides(t)[e]||this._types.getTypeParser(e,t)},t.exports=s});function J(e){let t=0x6a09e667,r=0xbb67ae85,s=0x3c6ef372,n=0xa54ff53a,i=0x510e527f,o=0x9b05688c,a=0x1f83d9ab,l=0x5be0cd19,c=0,h=0,u=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],f=m((e,t)=>e>>>t|e<<32-t,"rrot"),d=new Uint32Array(64),p=new Uint8Array(64),y=m(()=>{for(let e=0,t=0;e<16;e++,t+=4)d[e]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(let e=16;e<64;e++){let t=f(d[e-15],7)^f(d[e-15],18)^d[e-15]>>>3,r=f(d[e-2],17)^f(d[e-2],19)^d[e-2]>>>10;d[e]=d[e-16]+t+d[e-7]+r|0}let e=t,c=r,y=s,m=n,g=i,_=o,b=a,v=l;for(let t=0;t<64;t++){let r=v+(f(g,6)^f(g,11)^f(g,25))+(g&_^~g&b)+u[t]+d[t]|0,s=(f(e,2)^f(e,13)^f(e,22))+(e&c^e&y^c&y)|0;v=b,b=_,_=g,g=m+r|0,m=y,y=c,c=e,e=r+s|0}t=t+e|0,r=r+c|0,s=s+y|0,n=n+m|0,i=i+g|0,o=o+_|0,a=a+b|0,l=l+v|0,h=0},"process"),g=m(e=>{"string"==typeof e&&(e=new TextEncoder().encode(e));for(let t=0;t<e.length;t++)p[h++]=e[t],64===h&&y();c+=e.length},"add"),_=m(()=>{if(p[h++]=128,64==h&&y(),h+8>64){for(;h<64;)p[h++]=0;y()}for(;h<58;)p[h++]=0;let e=8*c;p[h++]=e/0x10000000000&255,p[h++]=e/0x100000000&255,p[h++]=e>>>24,p[h++]=e>>>16&255,p[h++]=e>>>8&255,p[h++]=255&e,y();let u=new Uint8Array(32);return u[0]=t>>>24,u[1]=t>>>16&255,u[2]=t>>>8&255,u[3]=255&t,u[4]=r>>>24,u[5]=r>>>16&255,u[6]=r>>>8&255,u[7]=255&r,u[8]=s>>>24,u[9]=s>>>16&255,u[10]=s>>>8&255,u[11]=255&s,u[12]=n>>>24,u[13]=n>>>16&255,u[14]=n>>>8&255,u[15]=255&n,u[16]=i>>>24,u[17]=i>>>16&255,u[18]=i>>>8&255,u[19]=255&i,u[20]=o>>>24,u[21]=o>>>16&255,u[22]=o>>>8&255,u[23]=255&o,u[24]=a>>>24,u[25]=a>>>16&255,u[26]=a>>>8&255,u[27]=255&a,u[28]=l>>>24,u[29]=l>>>16&255,u[30]=l>>>8&255,u[31]=255&l,u},"digest");return void 0===e?{add:g,digest:_}:(g(e),_())}var Z,X,ee=g(()=>{T(),m(J,"sha256")}),et=g(()=>{T(),m(Z=class e{constructor(){E(this,"_dataLength",0),E(this,"_bufferLength",0),E(this,"_state",new Int32Array(4)),E(this,"_buffer",new ArrayBuffer(68)),E(this,"_buffer8"),E(this,"_buffer32"),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashByteArray(e,t=!1){return this.onePassHasher.start().appendByteArray(e).end(t)}static hashStr(e,t=!1){return this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){return this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(t){let r=e.hexChars,s=e.hexOut,n,i,o,a;for(a=0;a<4;a+=1)for(i=8*a,n=t[a],o=0;o<8;o+=2)s[i+1+o]=r.charAt(15&n),n>>>=4,s[i+0+o]=r.charAt(15&n),n>>>=4;return s.join("")}static _md5cycle(e,t){let r=e[0],s=e[1],n=e[2],i=e[3];r+=(s&n|~s&i)+t[0]-0x28955b88|0,i+=((r=(r<<7|r>>>25)+s|0)&s|~r&n)+t[1]-0x173848aa|0,n+=((i=(i<<12|i>>>20)+r|0)&r|~i&s)+t[2]+0x242070db|0,s+=((n=(n<<17|n>>>15)+i|0)&i|~n&r)+t[3]-0x3e423112|0,r+=((s=(s<<22|s>>>10)+n|0)&n|~s&i)+t[4]-0xa83f051|0,i+=((r=(r<<7|r>>>25)+s|0)&s|~r&n)+t[5]+0x4787c62a|0,n+=((i=(i<<12|i>>>20)+r|0)&r|~i&s)+t[6]-0x57cfb9ed|0,s+=((n=(n<<17|n>>>15)+i|0)&i|~n&r)+t[7]-0x2b96aff|0,r+=((s=(s<<22|s>>>10)+n|0)&n|~s&i)+t[8]+0x698098d8|0,i+=((r=(r<<7|r>>>25)+s|0)&s|~r&n)+t[9]-0x74bb0851|0,n+=((i=(i<<12|i>>>20)+r|0)&r|~i&s)+t[10]-42063|0,s+=((n=(n<<17|n>>>15)+i|0)&i|~n&r)+t[11]-0x76a32842|0,r+=((s=(s<<22|s>>>10)+n|0)&n|~s&i)+t[12]+0x6b901122|0,i+=((r=(r<<7|r>>>25)+s|0)&s|~r&n)+t[13]-0x2678e6d|0,n+=((i=(i<<12|i>>>20)+r|0)&r|~i&s)+t[14]-0x5986bc72|0,s+=((n=(n<<17|n>>>15)+i|0)&i|~n&r)+t[15]+0x49b40821|0,r+=((s=(s<<22|s>>>10)+n|0)&i|n&~i)+t[1]-0x9e1da9e|0,i+=((r=(r<<5|r>>>27)+s|0)&n|s&~n)+t[6]-0x3fbf4cc0|0,n+=((i=(i<<9|i>>>23)+r|0)&s|r&~s)+t[11]+0x265e5a51|0,s+=((n=(n<<14|n>>>18)+i|0)&r|i&~r)+t[0]-0x16493856|0,r+=((s=(s<<20|s>>>12)+n|0)&i|n&~i)+t[5]-0x29d0efa3|0,i+=((r=(r<<5|r>>>27)+s|0)&n|s&~n)+t[10]+0x2441453|0,n+=((i=(i<<9|i>>>23)+r|0)&s|r&~s)+t[15]-0x275e197f|0,s+=((n=(n<<14|n>>>18)+i|0)&r|i&~r)+t[4]-0x182c0438|0,r+=((s=(s<<20|s>>>12)+n|0)&i|n&~i)+t[9]+0x21e1cde6|0,i+=((r=(r<<5|r>>>27)+s|0)&n|s&~n)+t[14]-0x3cc8f82a|0,n+=((i=(i<<9|i>>>23)+r|0)&s|r&~s)+t[3]-0xb2af279|0,s+=((n=(n<<14|n>>>18)+i|0)&r|i&~r)+t[8]+0x455a14ed|0,r+=((s=(s<<20|s>>>12)+n|0)&i|n&~i)+t[13]-0x561c16fb|0,i+=((r=(r<<5|r>>>27)+s|0)&n|s&~n)+t[2]-0x3105c08|0,n+=((i=(i<<9|i>>>23)+r|0)&s|r&~s)+t[7]+0x676f02d9|0,s+=((n=(n<<14|n>>>18)+i|0)&r|i&~r)+t[12]-0x72d5b376|0,r+=((s=(s<<20|s>>>12)+n|0)^n^i)+t[5]-378558|0,i+=((r=(r<<4|r>>>28)+s|0)^s^n)+t[8]-0x788e097f|0,n+=((i=(i<<11|i>>>21)+r|0)^r^s)+t[11]+0x6d9d6122|0,s+=((n=(n<<16|n>>>16)+i|0)^i^r)+t[14]-0x21ac7f4|0,r+=((s=(s<<23|s>>>9)+n|0)^n^i)+t[1]-0x5b4115bc|0,i+=((r=(r<<4|r>>>28)+s|0)^s^n)+t[4]+0x4bdecfa9|0,n+=((i=(i<<11|i>>>21)+r|0)^r^s)+t[7]-0x944b4a0|0,s+=((n=(n<<16|n>>>16)+i|0)^i^r)+t[10]-0x41404390|0,r+=((s=(s<<23|s>>>9)+n|0)^n^i)+t[13]+0x289b7ec6|0,i+=((r=(r<<4|r>>>28)+s|0)^s^n)+t[0]-0x155ed806|0,n+=((i=(i<<11|i>>>21)+r|0)^r^s)+t[3]-0x2b10cf7b|0,s+=((n=(n<<16|n>>>16)+i|0)^i^r)+t[6]+0x4881d05|0,r+=((s=(s<<23|s>>>9)+n|0)^n^i)+t[9]-0x262b2fc7|0,i+=((r=(r<<4|r>>>28)+s|0)^s^n)+t[12]-0x1924661b|0,n+=((i=(i<<11|i>>>21)+r|0)^r^s)+t[15]+0x1fa27cf8|0,s+=((n=(n<<16|n>>>16)+i|0)^i^r)+t[2]-0x3b53a99b|0,s=(s<<23|s>>>9)+n|0,r+=(n^(s|~i))+t[0]-0xbd6ddbc|0,r=(r<<6|r>>>26)+s|0,i+=(s^(r|~n))+t[7]+0x432aff97|0,i=(i<<10|i>>>22)+r|0,n+=(r^(i|~s))+t[14]-0x546bdc59|0,n=(n<<15|n>>>17)+i|0,s+=(i^(n|~r))+t[5]-0x36c5fc7|0,s=(s<<21|s>>>11)+n|0,r+=(n^(s|~i))+t[12]+0x655b59c3|0,r=(r<<6|r>>>26)+s|0,i+=(s^(r|~n))+t[3]-0x70f3336e|0,i=(i<<10|i>>>22)+r|0,n+=(r^(i|~s))+t[10]-1051523|0,n=(n<<15|n>>>17)+i|0,s+=(i^(n|~r))+t[1]-0x7a7ba22f|0,s=(s<<21|s>>>11)+n|0,r+=(n^(s|~i))+t[8]+0x6fa87e4f|0,r=(r<<6|r>>>26)+s|0,i+=(s^(r|~n))+t[15]-0x1d31920|0,i=(i<<10|i>>>22)+r|0,n+=(r^(i|~s))+t[6]-0x5cfebcec|0,n=(n<<15|n>>>17)+i|0,s+=(i^(n|~r))+t[13]+0x4e0811a1|0,s=(s<<21|s>>>11)+n|0,r+=(n^(s|~i))+t[4]-0x8ac817e|0,r=(r<<6|r>>>26)+s|0,i+=(s^(r|~n))+t[11]-0x42c50dcb|0,i=(i<<10|i>>>22)+r|0,n+=(r^(i|~s))+t[2]+0x2ad7d2bb|0,n=(n<<15|n>>>17)+i|0,s+=(i^(n|~r))+t[9]-0x14792c6f|0,s=(s<<21|s>>>11)+n|0,e[0]=r+e[0]|0,e[1]=s+e[1]|0,e[2]=n+e[2]|0,e[3]=i+e[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(e.stateIdentity),this}appendStr(t){let r=this._buffer8,s=this._buffer32,n=this._bufferLength,i,o;for(o=0;o<t.length;o+=1){if((i=t.charCodeAt(o))<128)r[n++]=i;else if(i<2048)r[n++]=(i>>>6)+192,r[n++]=63&i|128;else if(i<55296||i>56319)r[n++]=(i>>>12)+224,r[n++]=i>>>6&63|128,r[n++]=63&i|128;else{if((i=(i-55296)*1024+(t.charCodeAt(++o)-56320)+65536)>1114111)throw Error("Unicode standard supports code points up to U+10FFFF");r[n++]=(i>>>18)+240,r[n++]=i>>>12&63|128,r[n++]=i>>>6&63|128,r[n++]=63&i|128}n>=64&&(this._dataLength+=64,e._md5cycle(this._state,s),n-=64,s[0]=s[16])}return this._bufferLength=n,this}appendAsciiStr(t){let r=this._buffer8,s=this._buffer32,n=this._bufferLength,i,o=0;for(;;){for(i=Math.min(t.length-o,64-n);i--;)r[n++]=t.charCodeAt(o++);if(n<64)break;this._dataLength+=64,e._md5cycle(this._state,s),n=0}return this._bufferLength=n,this}appendByteArray(t){let r=this._buffer8,s=this._buffer32,n=this._bufferLength,i,o=0;for(;;){for(i=Math.min(t.length-o,64-n);i--;)r[n++]=t[o++];if(n<64)break;this._dataLength+=64,e._md5cycle(this._state,s),n=0}return this._bufferLength=n,this}getState(){let e=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,r=e.state,s=this._state,n;for(this._dataLength=e.length,this._bufferLength=e.buflen,s[0]=r[0],s[1]=r[1],s[2]=r[2],s[3]=r[3],n=0;n<t.length;n+=1)this._buffer8[n]=t.charCodeAt(n)}end(t=!1){let r=this._bufferLength,s=this._buffer8,n=this._buffer32,i=(r>>2)+1;this._dataLength+=r;let o=8*this._dataLength;if(s[r]=128,s[r+1]=s[r+2]=s[r+3]=0,n.set(e.buffer32Identity.subarray(i),i),r>55&&(e._md5cycle(this._state,n),n.set(e.buffer32Identity)),o<=0xffffffff)n[14]=o;else{let e=o.toString(16).match(/(.*?)(.{0,8})$/);if(null===e)return;let t=parseInt(e[2],16),r=parseInt(e[1],16)||0;n[14]=t,n[15]=r}return e._md5cycle(this._state,n),t?this._state:e._hex(this._state)}},"Md5"),E(Z,"stateIdentity",new Int32Array([0x67452301,-0x10325477,-0x67452302,0x10325476])),E(Z,"buffer32Identity",new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),E(Z,"hexChars","0123456789abcdef"),E(Z,"hexOut",[]),E(Z,"onePassHasher",new Z),X=Z}),er={};function es(e){return crypto.getRandomValues(a.alloc(e))}function en(e){if("sha256"===e)return{update:m(function(e){return{digest:m(function(){return a.from(J(e))},"digest")}},"update")};if("md5"===e)return{update:m(function(e){return{digest:m(function(){return"string"==typeof e?X.hashStr(e):X.hashByteArray(e)},"digest")}},"update")};throw Error(`Hash type '${e}' not supported`)}function ei(e,t){if("sha256"!==e)throw Error(`\
Only sha256 is supported (requested: '${e}')`);return{update:m(function(e){return{digest:m(function(){"string"==typeof t&&(t=new TextEncoder().encode(t)),"string"==typeof e&&(e=new TextEncoder().encode(e));let r=t.length;if(r>64)t=J(t);else if(r<64){let e=new Uint8Array(64);e.set(t),t=e}let s=new Uint8Array(64),n=new Uint8Array(64);for(let e=0;e<64;e++)s[e]=54^t[e],n[e]=92^t[e];let i=new Uint8Array(e.length+64);i.set(s,0),i.set(e,64);let o=new Uint8Array(96);return o.set(n,0),o.set(J(i),64),a.from(J(o))},"digest")}},"update")}}b(er,{createHash:()=>en,createHmac:()=>ei,randomBytes:()=>es});var eo=g(()=>{T(),ee(),et(),m(es,"randomBytes"),m(en,"createHash"),m(ei,"createHmac")}),ea=_((e,t)=>{T(),t.exports={host:"localhost",user:"win32"===l.platform?l.env.USERNAME:l.env.USER,database:void 0,password:null,connectionString:void 0,port:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,client_encoding:"",ssl:!1,application_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,statement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,connect_timeout:0,keepalives:1,keepalives_idle:0};var r=z(),s=r.getTypeParser(20,"text"),n=r.getTypeParser(1016,"text");t.exports.__defineSetter__("parseInt8",function(e){r.setTypeParser(20,"text",e?r.getTypeParser(23,"text"):s),r.setTypeParser(1016,"text",e?r.getTypeParser(1007,"text"):n)})}),el=_((e,t)=>{T();var r=(eo(),S(er)),s=ea();function n(e){return'"'+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}function i(e){for(var t="{",r=0;r<e.length;r++)r>0&&(t+=","),null===e[r]||typeof e[r]>"u"?t+="NULL":Array.isArray(e[r])?t+=i(e[r]):e[r]instanceof a?t+="\\\\x"+e[r].toString("hex"):t+=n(o(e[r]));return t+"}"}m(n,"escapeElement"),m(i,"arrayString");var o=m(function(e,t){if(null==e)return null;if(e instanceof a)return e;if(ArrayBuffer.isView(e)){var r=a.from(e.buffer,e.byteOffset,e.byteLength);return r.length===e.byteLength?r:r.slice(e.byteOffset,e.byteOffset+e.byteLength)}return e instanceof Date?s.parseInputDatesAsUTC?u(e):h(e):Array.isArray(e)?i(e):"object"==typeof e?l(e,t):e.toString()},"prepareValue");function l(e,t){if(e&&"function"==typeof e.toPostgres){if(-1!==(t=t||[]).indexOf(e))throw Error('circular reference detected while preparing "'+e+'" for query');return t.push(e),o(e.toPostgres(o),t)}return JSON.stringify(e)}function c(e,t){for(e=""+e;e.length<t;)e="0"+e;return e}function h(e){var t=-e.getTimezoneOffset(),r=e.getFullYear(),s=r<1;s&&(r=Math.abs(r)+1);var n=c(r,4)+"-"+c(e.getMonth()+1,2)+"-"+c(e.getDate(),2)+"T"+c(e.getHours(),2)+":"+c(e.getMinutes(),2)+":"+c(e.getSeconds(),2)+"."+c(e.getMilliseconds(),3);return t<0?(n+="-",t*=-1):n+="+",n+=c(Math.floor(t/60),2)+":"+c(t%60,2),s&&(n+=" BC"),n}function u(e){var t=e.getUTCFullYear(),r=t<1;r&&(t=Math.abs(t)+1);var s=c(t,4)+"-"+c(e.getUTCMonth()+1,2)+"-"+c(e.getUTCDate(),2)+"T"+c(e.getUTCHours(),2)+":"+c(e.getUTCMinutes(),2)+":"+c(e.getUTCSeconds(),2)+"."+c(e.getUTCMilliseconds(),3);return s+="+00:00",r&&(s+=" BC"),s}function f(e,t,r){return e="string"==typeof e?{text:e}:e,t&&("function"==typeof t?e.callback=t:e.values=t),r&&(e.callback=r),e}m(l,"prepareObject"),m(c,"pad"),m(h,"dateToString"),m(u,"dateToStringUTC"),m(f,"normalizeQueryConfig");var d=m(function(e){return r.createHash("md5").update(e,"utf-8").digest("hex")},"md5"),p=m(function(e,t,r){var s=d(t+e);return"md5"+d(a.concat([a.from(s),r]))},"postgresMd5PasswordHash");t.exports={prepareValue:m(function(e){return o(e)},"prepareValueWrapper"),normalizeQueryConfig:f,postgresMd5PasswordHash:p,md5:d}}),ec={};b(ec,{default:()=>eh});var eh,eu=g(()=>{T(),eh={}}),ef=_((e,t)=>{T();var r=(eo(),S(er));function s(e){if(-1===e.indexOf("SCRAM-SHA-256"))throw Error("SASL: Only mechanism SCRAM-SHA-256 is currently supported");let t=r.randomBytes(18).toString("base64");return{mechanism:"SCRAM-SHA-256",clientNonce:t,response:"n,,n=*,r="+t,message:"SASLInitialResponse"}}function n(e,t,r){if("SASLInitialResponse"!==e.message)throw Error("SASL: Last message was not SASLInitialResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string");if("string"!=typeof r)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string");let s=h(r);if(s.nonce.startsWith(e.clientNonce)){if(s.nonce.length===e.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");var n=y(t,a.from(s.salt,"base64"),s.iteration),i=p(n,"Client Key"),o=d(i),l="n=*,r="+e.clientNonce,c="r="+s.nonce+",s="+s.salt+",i="+s.iteration,u="c=biws,r="+s.nonce,m=l+","+c+","+u,g=f(i,p(o,m)).toString("base64"),_=p(n,"Server Key"),b=p(_,m);e.message="SASLResponse",e.serverSignature=b.toString("base64"),e.response=u+",p="+g}function i(e,t){if("SASLResponse"!==e.message)throw Error("SASL: Last message was not SASLResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string");let{serverSignature:r}=u(t);if(r!==e.serverSignature)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does not match")}function o(e){if("string"!=typeof e)throw TypeError("SASL: text must be a string");return e.split("").map((t,r)=>e.charCodeAt(r)).every(e=>e>=33&&e<=43||e>=45&&e<=126)}function l(e){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(e)}function c(e){if("string"!=typeof e)throw TypeError("SASL: attribute pairs text must be a string");return new Map(e.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]}))}function h(e){let t=c(e),r=t.get("r");if(r){if(!o(r))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce must only contain printable characters")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing");let s=t.get("s");if(s){if(!l(s))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing");let n=t.get("i");if(n){if(!/^[1-9][0-9]*$/.test(n))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration count")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing");return{nonce:r,salt:s,iteration:parseInt(n,10)}}function u(e){let t=c(e).get("v");if(t){if(!l(t))throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64")}else throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing");return{serverSignature:t}}function f(e,t){if(!a.isBuffer(e))throw TypeError("first argument must be a Buffer");if(!a.isBuffer(t))throw TypeError("second argument must be a Buffer");if(e.length!==t.length)throw Error("Buffer lengths must match");if(0===e.length)throw Error("Buffers cannot be empty");return a.from(e.map((r,s)=>e[s]^t[s]))}function d(e){return r.createHash("sha256").update(e).digest()}function p(e,t){return r.createHmac("sha256",e).update(t).digest()}function y(e,t,r){for(var s=p(e,a.concat([t,a.from([0,0,0,1])])),n=s,i=0;i<r-1;i++)n=f(n,s=p(e,s));return n}m(s,"startSession"),m(n,"continueSession"),m(i,"finalizeSession"),m(o,"isPrintableChars"),m(l,"isBase64"),m(c,"parseAttributePairs"),m(h,"parseServerFirstMessage"),m(u,"parseServerFinalMessage"),m(f,"xorBuffers"),m(d,"sha256"),m(p,"hmacSha256"),m(y,"Hi"),t.exports={startSession:s,continueSession:n,finalizeSession:i}}),ed={};function ep(...e){return e.join("/")}b(ed,{join:()=>ep});var ey=g(()=>{T(),m(ep,"join")}),em={};function eg(e,t){t(Error("No filesystem"))}b(em,{stat:()=>eg});var e_=g(()=>{T(),m(eg,"stat")}),eb={};b(eb,{default:()=>ev});var ev,ew=g(()=>{T(),ev={}}),eS={};b(eS,{StringDecoder:()=>ex});var eE,ex,eA=g(()=>{T(),m(eE=class{constructor(e){E(this,"td"),this.td=new TextDecoder(e)}write(e){return this.td.decode(e,{stream:!0})}end(e){return this.td.decode(e)}},"StringDecoder"),ex=eE}),eR=_((e,t)=>{T();var{Transform:r}=(ew(),S(eb)),{StringDecoder:s}=(eA(),S(eS)),n=Symbol("last"),i=Symbol("decoder");function o(e,t,r){let s;if(this.overflow){if(1===(s=this[i].write(e).split(this.matcher)).length)return r();s.shift(),this.overflow=!1}else this[n]+=this[i].write(e),s=this[n].split(this.matcher);this[n]=s.pop();for(let e=0;e<s.length;e++)try{l(this,this.mapper(s[e]))}catch(e){return r(e)}if(this.overflow=this[n].length>this.maxLength,this.overflow&&!this.skipOverflow)return void r(Error("maximum buffer reached"));r()}function a(e){if(this[n]+=this[i].end(),this[n])try{l(this,this.mapper(this[n]))}catch(t){return e(t)}e()}function l(e,t){void 0!==t&&e.push(t)}function c(e){return e}function h(e,t,l){switch(e=e||/\r?\n/,t=t||c,l=l||{},arguments.length){case 1:"function"==typeof e?(t=e,e=/\r?\n/):"object"!=typeof e||e instanceof RegExp||e[Symbol.split]||(l=e,e=/\r?\n/);break;case 2:"function"==typeof e?(l=t,t=e,e=/\r?\n/):"object"==typeof t&&(l=t,t=c)}(l=Object.assign({},l)).autoDestroy=!0,l.transform=o,l.flush=a,l.readableObjectMode=!0;let h=new r(l);return h[n]="",h[i]=new s("utf8"),h.matcher=e,h.mapper=t,h.maxLength=l.maxLength,h.skipOverflow=l.skipOverflow||!1,h.overflow=!1,h._destroy=function(e,t){this._writableState.errorEmitted=!1,t(e)},h}m(o,"transform"),m(a,"flush"),m(l,"push"),m(c,"noop"),m(h,"split"),t.exports=h}),eT=_((e,t)=>{T();var r=(ey(),S(ed)),s=(ew(),S(eb)).Stream,n=eR(),i=(eu(),S(ec)),o="win32"===l.platform,a=l.stderr;function c(e){return(61440&e)==32768}m(c,"isRegFile");var h=["host","port","database","user","password"],u=h.length,f=h[u-1];function d(){if(a instanceof s&&!0===a.writable){var e=Array.prototype.slice.call(arguments).concat(`
`);a.write(i.format.apply(i,e))}}m(d,"warn"),Object.defineProperty(t.exports,"isWin",{get:m(function(){return o},"get"),set:m(function(e){o=e},"set")}),t.exports.warnTo=function(e){var t=a;return a=e,t},t.exports.getFileName=function(e){var t=e||l.env;return t.PGPASSFILE||(o?r.join(t.APPDATA||"./","postgresql","pgpass.conf"):r.join(t.HOME||"./",".pgpass"))},t.exports.usePgPass=function(e,t){return!Object.prototype.hasOwnProperty.call(l.env,"PGPASSWORD")&&(!!o||(t=t||"<unkn>",c(e.mode)?!(63&e.mode)||(d('WARNING: password file "%s" has group or world access; permissions should be u=rw (0600) or less',t),!1):(d('WARNING: password file "%s" is not a plain file',t),!1)))};var p=t.exports.match=function(e,t){return h.slice(0,-1).reduce(function(r,s,n){return 1==n&&Number(e[s]||5432)===Number(t[s])?r&&!0:r&&("*"===t[s]||t[s]===e[s])},!0)};t.exports.getPassword=function(e,t,r){var s,i=t.pipe(n());function o(t){var r=y(t);r&&g(r)&&p(e,r)&&(s=r[f],i.end())}m(o,"onLine");var a=m(function(){t.destroy(),r(s)},"onEnd"),l=m(function(e){t.destroy(),d("WARNING: error on reading file: %s",e),r(void 0)},"onErr");t.on("error",l),i.on("data",o).on("end",a).on("error",l)};var y=t.exports.parseLine=function(e){if(e.length<11||e.match(/^\s+#/))return null;for(var t="",r="",s=0,n=0,i={},o=m(function(t,r,s){var n=e.substring(r,s);Object.hasOwnProperty.call(l.env,"PGPASS_NO_DEESCAPE")||(n=n.replace(/\\([:\\])/g,"$1")),i[h[t]]=n},"addToObj"),a=0;a<e.length-1;a+=1){if(t=e.charAt(a+1),r=e.charAt(a),s==u-1){o(s,n);break}a>=0&&":"==t&&"\\"!==r&&(o(s,n,a+1),n=a+2,s+=1)}return i=Object.keys(i).length===u?i:null},g=t.exports.isValidEntry=function(e){for(var t={0:function(e){return e.length>0},1:function(e){return"*"===e||isFinite(e=Number(e))&&e>0&&e<0x20000000000000&&Math.floor(e)===e},2:function(e){return e.length>0},3:function(e){return e.length>0},4:function(e){return e.length>0}},r=0;r<h.length;r+=1)if(!(0,t[r])(e[h[r]]||""))return!1;return!0}}),eC=_((e,t)=>{T(),ey(),S(ed);var r=(e_(),S(em)),s=eT();t.exports=function(e,t){var n=s.getFileName();r.stat(n,function(i,o){if(i||!s.usePgPass(o,n))return t(void 0);var a=r.createReadStream(n);s.getPassword(e,a,t)})},t.exports.warnTo=s.warnTo}),eI={};b(eI,{default:()=>ek});var ek,eL=g(()=>{T(),ek={}}),eP=_((e,t)=>{T();var r=(U(),S(N)),s=(e_(),S(em));function n(e){if("/"===e.charAt(0)){var t=e.split(" ");return{host:t[0],database:t[1]}}var n=r.parse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(e)?encodeURI(e).replace(/\%25(\d\d)/g,"%$1"):e,!0),t=n.query;for(var i in t)Array.isArray(t[i])&&(t[i]=t[i][t[i].length-1]);var o=(n.auth||":").split(":");if(t.user=o[0],t.password=o.splice(1).join(":"),t.port=n.port,"socket:"==n.protocol)return t.host=decodeURI(n.pathname),t.database=n.query.db,t.client_encoding=n.query.encoding,t;t.host||(t.host=n.hostname);var a=n.pathname;if(!t.host&&a&&/^%2f/i.test(a)){var l=a.split("/");t.host=decodeURIComponent(l[0]),a=l.splice(1).join("/")}switch(a&&"/"===a.charAt(0)&&(a=a.slice(1)||null),t.database=a&&decodeURI(a),("true"===t.ssl||"1"===t.ssl)&&(t.ssl=!0),"0"===t.ssl&&(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&(t.ssl.cert=s.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=s.readFileSync(t.sslkey).toString()),t.sslrootcert&&(t.ssl.ca=s.readFileSync(t.sslrootcert).toString()),t.sslmode){case"disable":t.ssl=!1;break;case"prefer":case"require":case"verify-ca":case"verify-full":break;case"no-verify":t.ssl.rejectUnauthorized=!1}return t}m(n,"parse"),t.exports=n,n.parse=n}),eO=_((e,t)=>{T();var r=(eL(),S(eI)),s=ea(),n=eP().parse,i=m(function(e,t,r){return void 0===r?r=l.env["PG"+e.toUpperCase()]:!1===r||(r=l.env[r]),t[e]||r||s[e]},"val"),o=m(function(){switch(l.env.PGSSLMODE){case"disable":return!1;case"prefer":case"require":case"verify-ca":case"verify-full":return!0;case"no-verify":return{rejectUnauthorized:!1}}return s.ssl},"readSSLConfigFromEnvironment"),a=m(function(e){return"'"+(""+e).replace(/\\/g,"\\\\").replace(/'/g,"\\'")+"'"},"quoteParamValue"),c=m(function(e,t,r){var s=t[r];null!=s&&e.push(r+"="+a(s))},"add"),h=class{constructor(e){(e="string"==typeof e?n(e):e||{}).connectionString&&(e=Object.assign({},e,n(e.connectionString))),this.user=i("user",e),this.database=i("database",e),void 0===this.database&&(this.database=this.user),this.port=parseInt(i("port",e),10),this.host=i("host",e),Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:i("password",e)}),this.binary=i("binary",e),this.options=i("options",e),this.ssl=typeof e.ssl>"u"?o():e.ssl,"string"==typeof this.ssl&&"true"===this.ssl&&(this.ssl=!0),"no-verify"===this.ssl&&(this.ssl={rejectUnauthorized:!1}),this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this.client_encoding=i("client_encoding",e),this.replication=i("replication",e),this.isDomainSocket=!(this.host||"").indexOf("/"),this.application_name=i("application_name",e,"PGAPPNAME"),this.fallback_application_name=i("fallback_application_name",e,!1),this.statement_timeout=i("statement_timeout",e,!1),this.lock_timeout=i("lock_timeout",e,!1),this.idle_in_transaction_session_timeout=i("idle_in_transaction_session_timeout",e,!1),this.query_timeout=i("query_timeout",e,!1),void 0===e.connectionTimeoutMillis?this.connect_timeout=l.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.floor(e.connectionTimeoutMillis/1e3),!1===e.keepAlive?this.keepalives=0:!0===e.keepAlive&&(this.keepalives=1),"number"==typeof e.keepAliveInitialDelayMillis&&(this.keepalives_idle=Math.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){var t=[];c(t,this,"user"),c(t,this,"password"),c(t,this,"port"),c(t,this,"application_name"),c(t,this,"fallback_application_name"),c(t,this,"connect_timeout"),c(t,this,"options");var s="object"==typeof this.ssl?this.ssl:this.ssl?{sslmode:this.ssl}:{};if(c(t,s,"sslmode"),c(t,s,"sslca"),c(t,s,"sslkey"),c(t,s,"sslcert"),c(t,s,"sslrootcert"),this.database&&t.push("dbname="+a(this.database)),this.replication&&t.push("replication="+a(this.replication)),this.host&&t.push("host="+a(this.host)),this.isDomainSocket)return e(null,t.join(" "));this.client_encoding&&t.push("client_encoding="+a(this.client_encoding)),r.lookup(this.host,function(r,s){return r?e(r,null):(t.push("hostaddr="+a(s)),e(null,t.join(" ")))})}};m(h,"ConnectionParameters"),t.exports=h}),eB=_((e,t)=>{T();var r=z(),s=/^([A-Za-z]+)(?: (\d+))?(?: (\d+))?/,n=class{constructor(e,t){this.command=null,this.rowCount=null,this.oid=null,this.rows=[],this.fields=[],this._parsers=void 0,this._types=t,this.RowCtor=null,this.rowAsArray="array"===e,this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){var t;(t=e.text?s.exec(e.text):s.exec(e.command))&&(this.command=t[1],t[3]?(this.oid=parseInt(t[2],10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(t[2],10)))}_parseRowAsArray(e){for(var t=Array(e.length),r=0,s=e.length;r<s;r++){var n=e[r];null!==n?t[r]=this._parsers[r](n):t[r]=null}return t}parseRow(e){for(var t={},r=0,s=e.length;r<s;r++){var n=e[r],i=this.fields[r].name;null!==n?t[i]=this._parsers[r](n):t[i]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.fields.length&&(this._parsers=Array(e.length));for(var t=0;t<e.length;t++){var s=e[t];this._types?this._parsers[t]=this._types.getTypeParser(s.dataTypeID,s.format||"text"):this._parsers[t]=r.getTypeParser(s.dataTypeID,s.format||"text")}}};m(n,"Result"),t.exports=n}),eM=_((e,t)=>{T();var{EventEmitter:r}=C(),s=eB(),n=el(),i=class extends r{constructor(e,t,r){super(),e=n.normalizeQueryConfig(e,t,r),this.text=e.text,this.values=e.values,this.rows=e.rows,this.types=e.types,this.name=e.name,this.binary=e.binary,this.portal=e.portal||"",this.callback=e.callback,this._rowMode=e.rowMode,l.domain&&e.callback&&(this.callback=l.domain.bind(e.callback)),this._result=new s(this._rowMode,this.types),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=!1,this._promise=null}requiresPreparation(){return!!this.name||!!this.rows||!!this.text&&!!this.values&&this.values.length>0}_checkForMultirow(){this._result.command&&(Array.isArray(this._results)||(this._results=[this._result]),this._result=new s(this._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){this._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.callback||!this.listeners("row").length}handleDataRow(e){let t;if(!this._canceledDueToError){try{t=this._result.parseRow(e.fields)}catch(e){this._canceledDueToError=e;return}this.emit("row",t,this._result),this._accumulateRows&&this._result.addRow(t)}}handleCommandComplete(e,t){this._checkForMultirow(),this._result.addCommandComplete(e),this.rows&&t.sync()}handleEmptyQuery(e){this.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&(e=this._canceledDueToError,this._canceledDueToError=!1),this.callback)return this.callback(e);this.emit("error",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(this._canceledDueToError,e);if(this.callback)try{this.callback(null,this._results)}catch(e){l.nextTick(()=>{throw e})}this.emit("end",this._results)}submit(e){if("string"!=typeof this.text&&"string"!=typeof this.name)return Error("A query must have either text or a name. Supplying neither is unsupported.");let t=e.parsedStatements[this.name];return this.text&&t&&this.text!==t?Error(`Prepared statements must be unique - '${this.name}\
' was used for a different statement`):this.values&&!Array.isArray(this.values)?Error("Query values must be an array"):(this.requiresPreparation()?this.prepare(e):e.query(this.text),null)}hasBeenParsed(e){return this.name&&e.parsedStatements[this.name]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){e.execute({portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=!0,this.hasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});try{e.bind({portal:this.portal,statement:this.name,values:this.values,binary:this.binary,valueMapper:n.prepareValue})}catch(t){this.handleError(t,e);return}e.describe({type:"P",name:this.portal||""}),this._getRows(e,this.rows)}handleCopyInResponse(e){e.sendCopyFail("No source stream defined")}handleCopyData(e,t){}};m(i,"Query"),t.exports=i}),eN=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.NoticeMessage=e.DataRowMessage=e.CommandCompleteMessage=e.ReadyForQueryMessage=e.NotificationResponseMessage=e.BackendKeyDataMessage=e.AuthenticationMD5Password=e.ParameterStatusMessage=e.ParameterDescriptionMessage=e.RowDescriptionMessage=e.Field=e.CopyResponse=e.CopyDataMessage=e.DatabaseError=e.copyDone=e.emptyQuery=e.replicationStart=e.portalSuspended=e.noData=e.closeComplete=e.bindComplete=e.parseComplete=void 0,e.parseComplete={name:"parseComplete",length:5},e.bindComplete={name:"bindComplete",length:5},e.closeComplete={name:"closeComplete",length:5},e.noData={name:"noData",length:5},e.portalSuspended={name:"portalSuspended",length:5},e.replicationStart={name:"replicationStart",length:4},e.emptyQuery={name:"emptyQuery",length:4},e.copyDone={name:"copyDone",length:4};var t=class extends Error{constructor(e,t,r){super(e),this.length=t,this.name=r}};m(t,"DatabaseError"),e.DatabaseError=t;var r=class{constructor(e,t){this.length=e,this.chunk=t,this.name="copyData"}};m(r,"CopyDataMessage"),e.CopyDataMessage=r;var s=class{constructor(e,t,r,s){this.length=e,this.name=t,this.binary=r,this.columnTypes=Array(s)}};m(s,"CopyResponse"),e.CopyResponse=s;var n=class{constructor(e,t,r,s,n,i,o){this.name=e,this.tableID=t,this.columnID=r,this.dataTypeID=s,this.dataTypeSize=n,this.dataTypeModifier=i,this.format=o}};m(n,"Field"),e.Field=n;var i=class{constructor(e,t){this.length=e,this.fieldCount=t,this.name="rowDescription",this.fields=Array(this.fieldCount)}};m(i,"RowDescriptionMessage"),e.RowDescriptionMessage=i;var o=class{constructor(e,t){this.length=e,this.parameterCount=t,this.name="parameterDescription",this.dataTypeIDs=Array(this.parameterCount)}};m(o,"ParameterDescriptionMessage"),e.ParameterDescriptionMessage=o;var a=class{constructor(e,t,r){this.length=e,this.parameterName=t,this.parameterValue=r,this.name="parameterStatus"}};m(a,"ParameterStatusMessage"),e.ParameterStatusMessage=a;var l=class{constructor(e,t){this.length=e,this.salt=t,this.name="authenticationMD5Password"}};m(l,"AuthenticationMD5Password"),e.AuthenticationMD5Password=l;var c=class{constructor(e,t,r){this.length=e,this.processID=t,this.secretKey=r,this.name="backendKeyData"}};m(c,"BackendKeyDataMessage"),e.BackendKeyDataMessage=c;var h=class{constructor(e,t,r,s){this.length=e,this.processId=t,this.channel=r,this.payload=s,this.name="notification"}};m(h,"NotificationResponseMessage"),e.NotificationResponseMessage=h;var u=class{constructor(e,t){this.length=e,this.status=t,this.name="readyForQuery"}};m(u,"ReadyForQueryMessage"),e.ReadyForQueryMessage=u;var f=class{constructor(e,t){this.length=e,this.text=t,this.name="commandComplete"}};m(f,"CommandCompleteMessage"),e.CommandCompleteMessage=f;var d=class{constructor(e,t){this.length=e,this.fields=t,this.name="dataRow",this.fieldCount=t.length}};m(d,"DataRowMessage"),e.DataRowMessage=d;var p=class{constructor(e,t){this.length=e,this.message=t,this.name="notice"}};m(p,"NoticeMessage"),e.NoticeMessage=p}),eD=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.Writer=void 0;var t=class{constructor(e=256){this.size=e,this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(e)}ensure(e){if(this.buffer.length-this.offset<e){var t=this.buffer,r=t.length+(t.length>>1)+e;this.buffer=a.allocUnsafe(r),t.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=e>>>24&255,this.buffer[this.offset++]=e>>>16&255,this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){return this.ensure(2),this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){if(e){var t=a.byteLength(e);this.ensure(t+1),this.buffer.write(e,this.offset,"utf-8"),this.offset+=t}else this.ensure(1);return this.buffer[this.offset++]=0,this}addString(e=""){var t=a.byteLength(e);return this.ensure(t),this.buffer.write(e,this.offset),this.offset+=t,this}add(e){return this.ensure(e.length),e.copy(this.buffer,this.offset),this.offset+=e.length,this}join(e){if(e){this.buffer[this.headerPosition]=e;let t=this.offset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+1)}return this.buffer.slice(5*!e,this.offset)}flush(e){var t=this.join(e);return this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(this.size),t}};m(t,"Writer"),e.Writer=t}),eU=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.serialize=void 0;var t=eD(),r=new t.Writer,s=m(e=>{for(let t of(r.addInt16(3).addInt16(0),Object.keys(e)))r.addCString(t).addCString(e[t]);r.addCString("client_encoding").addCString("UTF8");var s=r.addCString("").flush(),n=s.length+4;return new t.Writer().addInt32(n).add(s).flush()},"startup"),n=m(()=>{let e=a.allocUnsafe(8);return e.writeInt32BE(8,0),e.writeInt32BE(0x4d2162f,4),e},"requestSsl"),i=m(e=>r.addCString(e).flush(112),"password"),o=m(function(e,t){return r.addCString(e).addInt32(a.byteLength(t)).addString(t),r.flush(112)},"sendSASLInitialResponseMessage"),l=m(function(e){return r.addString(e).flush(112)},"sendSCRAMClientFinalMessage"),c=m(e=>r.addCString(e).flush(81),"query"),h=[],u=m(e=>{let t=e.name||"";t.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",t,t.length),console.error("This can cause conflicts and silent errors executing queries"));let s=e.types||h;for(var n=s.length,i=r.addCString(t).addCString(e.text).addInt16(n),o=0;o<n;o++)i.addInt32(s[o]);return r.flush(80)},"parse"),f=new t.Writer,d=m(function(e,t){for(let s=0;s<e.length;s++){let n=t?t(e[s],s):e[s];null==n?(r.addInt16(0),f.addInt32(-1)):n instanceof a?(r.addInt16(1),f.addInt32(n.length),f.add(n)):(r.addInt16(0),f.addInt32(a.byteLength(n)),f.addString(n))}},"writeValues"),p=m((e={})=>{let t=e.portal||"",s=e.statement||"",n=e.binary||!1,i=e.values||h,o=i.length;return r.addCString(t).addCString(s),r.addInt16(o),d(i,e.valueMapper),r.addInt16(o),r.add(f.flush()),r.addInt16(+!!n),r.flush(66)},"bind"),y=a.from([69,0,0,0,9,0,0,0,0,0]),g=m(e=>{if(!e||!e.portal&&!e.rows)return y;let t=e.portal||"",r=e.rows||0,s=a.byteLength(t),n=4+s+1+4,i=a.allocUnsafe(1+n);return i[0]=69,i.writeInt32BE(n,1),i.write(t,5,"utf-8"),i[s+5]=0,i.writeUInt32BE(r,i.length-4),i},"execute"),_=m((e,t)=>{let r=a.allocUnsafe(16);return r.writeInt32BE(16,0),r.writeInt16BE(1234,4),r.writeInt16BE(5678,6),r.writeInt32BE(e,8),r.writeInt32BE(t,12),r},"cancel"),b=m((e,t)=>{let r=4+a.byteLength(t)+1,s=a.allocUnsafe(1+r);return s[0]=e,s.writeInt32BE(r,1),s.write(t,5,"utf-8"),s[r]=0,s},"cstringMessage"),v=r.addCString("P").flush(68),w=r.addCString("S").flush(68),S=m(e=>e.name?b(68,`${e.type}${e.name||""}`):"P"===e.type?v:w,"describe"),E=m(e=>b(67,`${e.type}${e.name||""}`),"close"),x=m(e=>r.add(e).flush(100),"copyData"),A=m(e=>b(102,e),"copyFail"),R=m(e=>a.from([e,0,0,0,4]),"codeOnlyBuffer"),C=R(72),I=R(83),k=R(88),L=R(99);e.serialize={startup:s,password:i,requestSsl:n,sendSASLInitialResponseMessage:o,sendSCRAMClientFinalMessage:l,query:c,parse:u,bind:p,execute:g,describe:S,close:E,flush:m(()=>C,"flush"),sync:m(()=>I,"sync"),end:m(()=>k,"end"),copyData:x,copyDone:m(()=>L,"copyDone"),copyFail:A,cancel:_}}),eF=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.BufferReader=void 0;var t=a.allocUnsafe(0),r=class{constructor(e=0){this.offset=e,this.buffer=t,this.encoding="utf-8"}setBuffer(e,t){this.offset=e,this.buffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.offset+=2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.buffer.readInt32BE(this.offset);return this.offset+=4,e}uint32(){let e=this.buffer.readUInt32BE(this.offset);return this.offset+=4,e}string(e){let t=this.buffer.toString(this.encoding,this.offset,this.offset+e);return this.offset+=e,t}cstring(){let e=this.offset,t=e;for(;0!==this.buffer[t++];);return this.offset=t,this.buffer.toString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}};m(r,"BufferReader"),e.BufferReader=r}),ej=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.Parser=void 0;var t=eN(),r=eF(),s=a.allocUnsafe(0),n=class{constructor(e){if(this.buffer=s,this.bufferLength=0,this.bufferOffset=0,this.reader=new r.BufferReader,e?.mode==="binary")throw Error("Binary mode not supported yet");this.mode=e?.mode||"text"}parse(e,t){this.mergeBuffer(e);let r=this.bufferOffset+this.bufferLength,n=this.bufferOffset;for(;n+5<=r;){let e=this.buffer[n],s=this.buffer.readUInt32BE(n+1),i=1+s;if(i+n<=r)t(this.handlePacket(n+5,e,s,this.buffer)),n+=i;else break}n===r?(this.buffer=s,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=r-n,this.bufferOffset=n)}mergeBuffer(e){if(this.bufferLength>0){let t=this.bufferLength+e.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){let e;if(t<=this.buffer.byteLength&&this.bufferOffset>=this.bufferLength)e=this.buffer;else{let r=2*this.buffer.byteLength;for(;t>=r;)r*=2;e=a.allocUnsafe(r)}this.buffer.copy(e,0,this.bufferOffset,this.bufferOffset+this.bufferLength),this.buffer=e,this.bufferOffset=0}e.copy(this.buffer,this.bufferOffset+this.bufferLength),this.bufferLength=t}else this.buffer=e,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,r,s,n){switch(r){case 50:return t.bindComplete;case 49:return t.parseComplete;case 51:return t.closeComplete;case 110:return t.noData;case 115:return t.portalSuspended;case 99:return t.copyDone;case 87:return t.replicationStart;case 73:return t.emptyQuery;case 68:return this.parseDataRowMessage(e,s,n);case 67:return this.parseCommandCompleteMessage(e,s,n);case 90:return this.parseReadyForQueryMessage(e,s,n);case 65:return this.parseNotificationMessage(e,s,n);case 82:return this.parseAuthenticationResponse(e,s,n);case 83:return this.parseParameterStatusMessage(e,s,n);case 75:return this.parseBackendKeyData(e,s,n);case 69:return this.parseErrorMessage(e,s,n,"error");case 78:return this.parseErrorMessage(e,s,n,"notice");case 84:return this.parseRowDescriptionMessage(e,s,n);case 116:return this.parseParameterDescriptionMessage(e,s,n);case 71:return this.parseCopyInMessage(e,s,n);case 72:return this.parseCopyOutMessage(e,s,n);case 100:return this.parseCopyData(e,s,n);default:return new t.DatabaseError("received invalid response: "+r.toString(16),s,"error")}}parseReadyForQueryMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.string(1);return new t.ReadyForQueryMessage(r,n)}parseCommandCompleteMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.cstring();return new t.CommandCompleteMessage(r,n)}parseCopyData(e,r,s){let n=s.slice(e,e+(r-4));return new t.CopyDataMessage(r,n)}parseCopyInMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyInResponse")}parseCopyOutMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyOutResponse")}parseCopyMessage(e,r,s,n){this.reader.setBuffer(e,s);let i=0!==this.reader.byte(),o=this.reader.int16(),a=new t.CopyResponse(r,n,i,o);for(let e=0;e<o;e++)a.columnTypes[e]=this.reader.int16();return a}parseNotificationMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int32(),i=this.reader.cstring(),o=this.reader.cstring();return new t.NotificationResponseMessage(r,n,i,o)}parseRowDescriptionMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int16(),i=new t.RowDescriptionMessage(r,n);for(let e=0;e<n;e++)i.fields[e]=this.parseField();return i}parseField(){let e=this.reader.cstring(),r=this.reader.uint32(),s=this.reader.int16(),n=this.reader.uint32(),i=this.reader.int16(),o=this.reader.int32(),a=0===this.reader.int16()?"text":"binary";return new t.Field(e,r,s,n,i,o,a)}parseParameterDescriptionMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int16(),i=new t.ParameterDescriptionMessage(r,n);for(let e=0;e<n;e++)i.dataTypeIDs[e]=this.reader.int32();return i}parseDataRowMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int16(),i=Array(n);for(let e=0;e<n;e++){let t=this.reader.int32();i[e]=-1===t?null:this.reader.string(t)}return new t.DataRowMessage(r,i)}parseParameterStatusMessage(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.cstring(),i=this.reader.cstring();return new t.ParameterStatusMessage(r,n,i)}parseBackendKeyData(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int32(),i=this.reader.int32();return new t.BackendKeyDataMessage(r,n,i)}parseAuthenticationResponse(e,r,s){this.reader.setBuffer(e,s);let n=this.reader.int32(),i={name:"authenticationOk",length:r};switch(n){case 0:break;case 3:8===i.length&&(i.name="authenticationCleartextPassword");break;case 5:if(12===i.length){i.name="authenticationMD5Password";let e=this.reader.bytes(4);return new t.AuthenticationMD5Password(r,e)}break;case 10:let o;i.name="authenticationSASL",i.mechanisms=[];do(o=this.reader.cstring())&&i.mechanisms.push(o);while(o);break;case 11:i.name="authenticationSASLContinue",i.data=this.reader.string(r-8);break;case 12:i.name="authenticationSASLFinal",i.data=this.reader.string(r-8);break;default:throw Error("Unknown authenticationOk message type "+n)}return i}parseErrorMessage(e,r,s,n){this.reader.setBuffer(e,s);let i={},o=this.reader.string(1);for(;"\0"!==o;)i[o]=this.reader.cstring(),o=this.reader.string(1);let a=i.M,l="notice"===n?new t.NoticeMessage(r,a):new t.DatabaseError(a,r,n);return l.severity=i.S,l.code=i.C,l.detail=i.D,l.hint=i.H,l.position=i.P,l.internalPosition=i.p,l.internalQuery=i.q,l.where=i.W,l.schema=i.s,l.table=i.t,l.column=i.c,l.dataType=i.d,l.constraint=i.n,l.file=i.F,l.line=i.L,l.routine=i.R,l}};m(n,"Parser"),e.Parser=n}),eq=_(e=>{T(),Object.defineProperty(e,"__esModule",{value:!0}),e.DatabaseError=e.serialize=e.parse=void 0;var t=eN();Object.defineProperty(e,"DatabaseError",{enumerable:!0,get:m(function(){return t.DatabaseError},"get")});var r=eU();Object.defineProperty(e,"serialize",{enumerable:!0,get:m(function(){return r.serialize},"get")});var s=ej();function n(e,t){let r=new s.Parser;return e.on("data",e=>r.parse(e,t)),new Promise(t=>e.on("end",()=>t()))}m(n,"parse"),e.parse=n}),eQ={};function eW({socket:e,servername:t}){return e.startTls(t),e}b(eQ,{connect:()=>eW});var e$=g(()=>{T(),m(eW,"connect")}),eG=_((e,t)=>{T();var r=(M(),S(I)),s=C().EventEmitter,{parse:n,serialize:i}=eq(),o=i.flush(),a=i.sync(),l=i.end(),c=class extends s{constructor(e){super(),e=e||{},this.stream=e.stream||new r.Socket,this._keepAlive=e.keepAlive,this._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,this.lastBuffer=!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=!1;var t=this;this.on("newListener",function(e){"message"===e&&(t._emitMessage=!0)})}connect(e,t){var s=this;this._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,t),this.stream.once("connect",function(){s._keepAlive&&s.stream.setKeepAlive(!0,s._keepAliveInitialDelayMillis),s.emit("connect")});let n=m(function(e){s._ending&&("ECONNRESET"===e.code||"EPIPE"===e.code)||s.emit("error",e)},"reportStreamError");if(this.stream.on("error",n),this.stream.on("close",function(){s.emit("end")}),!this.ssl)return this.attachListeners(this.stream);this.stream.once("data",function(e){switch(e.toString("utf8")){case"S":break;case"N":return s.stream.end(),s.emit("error",Error("The server does not support SSL connections"));default:return s.stream.end(),s.emit("error",Error("There was an error establishing an SSL connection"))}var i=(e$(),S(eQ));let o={socket:s.stream};!0!==s.ssl&&(Object.assign(o,s.ssl),"key"in s.ssl&&(o.key=s.ssl.key)),0===r.isIP(t)&&(o.servername=t);try{s.stream=i.connect(o)}catch(e){return s.emit("error",e)}s.attachListeners(s.stream),s.stream.on("error",n),s.emit("sslconnect")})}attachListeners(e){e.on("end",()=>{this.emit("end")}),n(e,e=>{var t="error"===e.name?"errorMessage":e.name;this._emitMessage&&this.emit("message",e),this.emit(t,e)})}requestSsl(){this.stream.write(i.requestSsl())}startup(e){this.stream.write(i.startup(e))}cancel(e,t){this._send(i.cancel(e,t))}password(e){this._send(i.password(e))}sendSASLInitialResponseMessage(e,t){this._send(i.sendSASLInitialResponseMessage(e,t))}sendSCRAMClientFinalMessage(e){this._send(i.sendSCRAMClientFinalMessage(e))}_send(e){return!!this.stream.writable&&this.stream.write(e)}query(e){this._send(i.query(e))}parse(e){this._send(i.parse(e))}bind(e){this._send(i.bind(e))}execute(e){this._send(i.execute(e))}flush(){this.stream.writable&&this.stream.write(o)}sync(){this._ending=!0,this._send(o),this._send(a)}ref(){this.stream.ref()}unref(){this.stream.unref()}end(){return(this._ending=!0,this._connecting&&this.stream.writable)?this.stream.write(l,()=>{this.stream.end()}):void this.stream.end()}close(e){this._send(i.close(e))}describe(e){this._send(i.describe(e))}sendCopyFromChunk(e){this._send(i.copyData(e))}endCopyFrom(){this._send(i.copyDone())}sendCopyFail(e){this._send(i.copyFail(e))}};m(c,"Connection"),t.exports=c}),eV=_((e,t)=>{T();var r=C().EventEmitter,s=(eu(),S(ec),el()),n=ef(),o=eC(),a=K(),c=eO(),h=eM(),u=ea(),f=eG(),d=class extends r{constructor(e){super(),this.connectionParameters=new c(e),this.user=this.connectionParameters.user,this.database=this.connectionParameters.database,this.port=this.connectionParameters.port,this.host=this.connectionParameters.host,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),this.replication=this.connectionParameters.replication;var t=e||{};this._Promise=t.Promise||i.Promise,this._types=new a(t.types),this._ending=!1,this._connecting=!1,this._connected=!1,this._connectionError=!1,this._queryable=!0,this.connection=t.connection||new f({stream:t.stream,ssl:this.connectionParameters.ssl,keepAlive:t.keepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.connectionParameters.client_encoding||"utf8"}),this.queryQueue=[],this.binary=t.binary||u.binary,this.processID=null,this.secretKey=null,this.ssl=this.connectionParameters.ssl||!1,this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this._connectionTimeoutMillis=t.connectionTimeoutMillis||0}_errorAllQueries(e){let t=m(t=>{l.nextTick(()=>{t.handleError(e,this.connection)})},"enqueueError");this.activeQuery&&(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.length=0}_connect(e){var t=this,r=this.connection;if(this._connectionCallback=e,this._connecting||this._connected){let t=Error("Client has already been connected. You cannot reuse a client.");l.nextTick(()=>{e(t)});return}this._connecting=!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&(this.connectionTimeoutHandle=setTimeout(()=>{r._ending=!0,r.stream.destroy(Error("timeout expired"))},this._connectionTimeoutMillis)),this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){t.ssl?r.requestSsl():r.startup(t.getStartupConf())}),r.on("sslconnect",function(){r.startup(t.getStartupConf())}),this._attachListeners(r),r.once("end",()=>{let e=this._ending?Error("Connection terminated"):Error("Connection terminated unexpectedly");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(e),this._ending||(this._connecting&&!this._connectionError?this._connectionCallback?this._connectionCallback(e):this._handleErrorEvent(e):this._connectionError||this._handleErrorEvent(e)),l.nextTick(()=>{this.emit("end")})})}connect(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})}_attachListeners(e){e.on("authenticationCleartextPassword",this._handleAuthCleartextPassword.bind(this)),e.on("authenticationMD5Password",this._handleAuthMD5Password.bind(this)),e.on("authenticationSASL",this._handleAuthSASL.bind(this)),e.on("authenticationSASLContinue",this._handleAuthSASLContinue.bind(this)),e.on("authenticationSASLFinal",this._handleAuthSASLFinal.bind(this)),e.on("backendKeyData",this._handleBackendKeyData.bind(this)),e.on("error",this._handleErrorEvent.bind(this)),e.on("errorMessage",this._handleErrorMessage.bind(this)),e.on("readyForQuery",this._handleReadyForQuery.bind(this)),e.on("notice",this._handleNotice.bind(this)),e.on("rowDescription",this._handleRowDescription.bind(this)),e.on("dataRow",this._handleDataRow.bind(this)),e.on("portalSuspended",this._handlePortalSuspended.bind(this)),e.on("emptyQuery",this._handleEmptyQuery.bind(this)),e.on("commandComplete",this._handleCommandComplete.bind(this)),e.on("parseComplete",this._handleParseComplete.bind(this)),e.on("copyInResponse",this._handleCopyInResponse.bind(this)),e.on("copyData",this._handleCopyData.bind(this)),e.on("notification",this._handleNotification.bind(this))}_checkPgPass(e){let t=this.connection;"function"==typeof this.password?this._Promise.resolve().then(()=>this.password()).then(r=>{if(void 0!==r){if("string"!=typeof r)return void t.emit("error",TypeError("Password must be a string"));this.connectionParameters.password=this.password=r}else this.connectionParameters.password=this.password=null;e()}).catch(e=>{t.emit("error",e)}):null!==this.password?e():o(this.connectionParameters,t=>{void 0!==t&&(this.connectionParameters.password=this.password=t),e()})}_handleAuthCleartextPassword(e){this._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){this._checkPgPass(()=>{let t=s.postgresMd5PasswordHash(this.user,this.password,e.salt);this.connection.password(t)})}_handleAuthSASL(e){this._checkPgPass(()=>{this.saslSession=n.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(this.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){n.continueSession(this.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}_handleAuthSASLFinal(e){n.finalizeSession(this.saslSession,e.data),this.saslSession=null}_handleBackendKeyData(e){this.processID=e.processID,this.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this._connecting=!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&(this._connectionCallback(null,this),this._connectionCallback=null),this.emit("connect"));let{activeQuery:t}=this;this.activeQuery=null,this.readyForQuery=!0,t&&t.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){if(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback)return this._connectionCallback(e);this.emit("error",e)}}_handleErrorEvent(e){if(this._connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,this._errorAllQueries(e),this.emit("error",e)}_handleErrorMessage(e){if(this._connecting)return this._handleErrorWhileConnecting(e);let t=this.activeQuery;if(!t)return void this._handleErrorEvent(e);this.activeQuery=null,t.handleError(e,this.connection)}_handleRowDescription(e){this.activeQuery.handleRowDescription(e)}_handleDataRow(e){this.activeQuery.handleDataRow(e)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.connection)}_handleEmptyQuery(e){this.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){this.activeQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.activeQuery.name&&(this.connection.parsedStatements[this.activeQuery.name]=this.activeQuery.text)}_handleCopyInResponse(e){this.activeQuery.handleCopyInResponse(this.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(e,this.connection)}_handleNotification(e){this.emit("notification",e)}_handleNotice(e){this.emit("notice",e)}getStartupConf(){var e=this.connectionParameters,t={user:e.user,database:e.database},r=e.application_name||e.fallback_application_name;return r&&(t.application_name=r),e.replication&&(t.replication=""+e.replication),e.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,10))),e.lock_timeout&&(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&(t.idle_in_transaction_session_timeout=String(parseInt(e.idle_in_transaction_session_timeout,10))),e.options&&(t.options=e.options),t}cancel(e,t){if(e.activeQuery===t){var r=this.connection;this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){r.cancel(e.processID,e.secretKey)})}else -1!==e.queryQueue.indexOf(t)&&e.queryQueue.splice(e.queryQueue.indexOf(t),1)}setTypeParser(e,t,r){return this._types.setTypeParser(e,t,r)}getTypeParser(e,t){return this._types.getTypeParser(e,t)}escapeIdentifier(e){return'"'+e.replace(/"/g,'""')+'"'}escapeLiteral(e){for(var t=!1,r="'",s=0;s<e.length;s++){var n=e[s];"'"===n?r+=n+n:"\\"===n?(r+=n+n,t=!0):r+=n}return r+="'",!0===t&&(r=" E"+r),r}_pulseQueryQueue(){if(!0===this.readyForQuery)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){this.readyForQuery=!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);e&&l.nextTick(()=>{this.activeQuery.handleError(e,this.connection),this.readyForQuery=!0,this._pulseQueryQueue()})}else this.hasExecuted&&(this.activeQuery=null,this.emit("drain"))}query(e,t,r){var s,n,i,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");return"function"==typeof e.submit?(i=e.query_timeout||this.connectionParameters.query_timeout,n=s=e,"function"==typeof t&&(s.callback=s.callback||t)):(i=this.connectionParameters.query_timeout,(s=new h(e,t,r)).callback||(n=new this._Promise((e,t)=>{s.callback=(r,s)=>r?t(r):e(s)}))),i&&(a=s.callback,o=setTimeout(()=>{var e=Error("Query read timeout");l.nextTick(()=>{s.handleError(e,this.connection)}),a(e),s.callback=()=>{};var t=this.queryQueue.indexOf(s);t>-1&&this.queryQueue.splice(t,1),this._pulseQueryQueue()},i),s.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this.binary&&!s.binary&&(s.binary=!0),s._result&&!s._result._types&&(s._result._types=this._types),this._queryable?this._ending?l.nextTick(()=>{s.handleError(Error("Client was closed and is not queryable"),this.connection)}):(this.queryQueue.push(s),this._pulseQueryQueue()):l.nextTick(()=>{s.handleError(Error("Client has encountered a connection error and is not queryable"),this.connection)}),n}ref(){this.connection.ref()}unref(){this.connection.unref()}end(e){if(this._ending=!0,!this.connection._connecting)if(!e)return this._Promise.resolve();else e();if(this.activeQuery||!this._queryable?this.connection.stream.destroy():this.connection.end(),!e)return new this._Promise(e=>{this.connection.once("end",e)});this.connection.once("end",e)}};m(d,"Client"),d.Query=h,t.exports=d}),eY=_((e,t)=>{T();var r=C().EventEmitter,s=m(function(){},"NOOP"),n=m((e,t)=>{let r=e.findIndex(t);return -1===r?void 0:e.splice(r,1)[0]},"removeWhere"),a=class{constructor(e,t,r){this.client=e,this.idleListener=t,this.timeoutId=r}};m(a,"IdleItem");var c=class{constructor(e){this.callback=e}};function h(){throw Error("Release called on client which has already been released to the pool.")}function u(e,t){let r,s;return t?{callback:t,result:void 0}:{callback:m(function(e,t){e?r(e):s(t)},"cb"),result:new e(function(e,t){s=e,r=t}).catch(e=>{throw Error.captureStackTrace(e),e})}}function f(e,t){return m(function r(s){s.client=t,t.removeListener("error",r),t.on("error",()=>{e.log("additional client error after disconnection due to error",s)}),e._remove(t),e.emit("error",s,t)},"idleListener")}m(c,"PendingItem"),m(h,"throwOnDoubleRelease"),m(u,"promisify"),m(f,"makeIdleListener");var d=class extends r{constructor(e,t){super(),this.options=Object.assign({},e),null!=e&&"password"in e&&Object.defineProperty(this.options,"password",{configurable:!0,enumerable:!1,writable:!0,value:e.password}),null!=e&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.ssl,"key",{enumerable:!1}),this.options.max=this.options.max||this.options.poolSize||10,this.options.maxUses=this.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||!1,this.options.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.options.log||function(){},this.Client=this.options.Client||t||e1().Client,this.Promise=this.options.Promise||i.Promise,typeof this.options.idleTimeoutMillis>"u"&&(this.options.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this._expired=new WeakSet,this._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){return this._clients.length>=this.options.max}_pulseQueue(){if(this.log("pulse queue"),this.ended)return void this.log("pulse queue ended");if(this.ending){this.log("pulse queue on ending"),this._idle.length&&this._idle.slice().map(e=>{this._remove(e.client)}),this._clients.length||(this.ended=!0,this._endCallback());return}if(!this._pendingQueue.length)return void this.log("no queued requests");if(!this._idle.length&&this._isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this._idle.pop();clearTimeout(t.timeoutId);let r=t.client;r.ref&&r.ref();let s=t.idleListener;return this._acquireClient(r,e,s,!1)}if(!this._isFull())return this.newClient(e);throw Error("unexpected condition")}_remove(e){let t=n(this._idle,t=>t.client===e);void 0!==t&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(t=>t!==e),e.end(),this.emit("remove",e)}connect(e){if(this.ending){let t=Error("Cannot use a pool after calling end on the pool");return e?e(t):this.Promise.reject(t)}let t=u(this.Promise,e),r=t.result;if(this._isFull()||this._idle.length){if(this._idle.length&&l.nextTick(()=>this._pulseQueue()),!this.options.connectionTimeoutMillis)return this._pendingQueue.push(new c(t.callback)),r;let e=m((e,r,s)=>{clearTimeout(i),t.callback(e,r,s)},"queueCallback"),s=new c(e),i=setTimeout(()=>{n(this._pendingQueue,t=>t.callback===e),s.timedOut=!0,t.callback(Error("timeout exceeded when trying to connect"))},this.options.connectionTimeoutMillis);return i.unref&&i.unref(),this._pendingQueue.push(s),r}return this.newClient(new c(t.callback)),r}newClient(e){let t=new this.Client(this.options);this._clients.push(t);let r=f(this,t);this.log("checking client timeout");let n,i=!1;this.options.connectionTimeoutMillis&&(n=setTimeout(()=>{this.log("ending client due to timeout"),i=!0,t.connection?t.connection.stream.destroy():t.end()},this.options.connectionTimeoutMillis)),this.log("connecting new client"),t.connect(o=>{if(n&&clearTimeout(n),t.on("error",r),o)this.log("client failed to connect",o),this._clients=this._clients.filter(e=>e!==t),i&&(o=Error("Connection terminated due to connection timeout",{cause:o})),this._pulseQueue(),e.timedOut||e.callback(o,void 0,s);else{if(this.log("new client connected"),0!==this.options.maxLifetimeSeconds){let e=setTimeout(()=>{this.log("ending client due to expired lifetime"),this._expired.add(t),-1!==this._idle.findIndex(e=>e.client===t)&&this._acquireClient(t,new c((e,t,r)=>r()),r,!1)},1e3*this.options.maxLifetimeSeconds);e.unref(),t.once("end",()=>clearTimeout(e))}return this._acquireClient(t,e,r,!0)}})}_acquireClient(e,t,r,n){n&&this.emit("connect",e),this.emit("acquire",e),e.release=this._releaseOnce(e,r),e.removeListener("error",r),t.timedOut?n&&this.options.verify?this.options.verify(e,e.release):e.release():n&&this.options.verify?this.options.verify(e,r=>{if(r)return e.release(r),t.callback(r,void 0,s);t.callback(void 0,e,e.release)}):t.callback(void 0,e,e.release)}_releaseOnce(e,t){let r=!1;return s=>{r&&h(),r=!0,this._release(e,t,s)}}_release(e,t,r){let s;if(e.on("error",t),e._poolUseCount=(e._poolUseCount||0)+1,this.emit("release",r,e),r||this.ending||!e._queryable||e._ending||e._poolUseCount>=this.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log("remove expended client"),this._remove(e),this._pulseQueue();return}if(this._expired.has(e)){this.log("remove expired client"),this._expired.delete(e),this._remove(e),this._pulseQueue();return}this.options.idleTimeoutMillis&&(s=setTimeout(()=>{this.log("remove idle client"),this._remove(e)},this.options.idleTimeoutMillis),this.options.allowExitOnIdle&&s.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new a(e,t,s)),this._pulseQueue()}query(e,t,r){if("function"==typeof e){let t=u(this.Promise,e);return o(function(){return t.callback(Error("Passing a function as the first parameter to pool.query is not supported"))}),t.result}"function"==typeof t&&(r=t,t=void 0);let s=u(this.Promise,r);return r=s.callback,this.connect((s,n)=>{if(s)return r(s);let i=!1,o=m(e=>{i||(i=!0,n.release(e),r(e))},"onError");n.once("error",o),this.log("dispatching query");try{n.query(e,t,(e,t)=>{if(this.log("query dispatched"),n.removeListener("error",o),!i)return i=!0,n.release(e),e?r(e):r(void 0,t)})}catch(e){return n.release(e),r(e)}}),s.result}end(e){if(this.log("ending"),this.ending){let t=Error("Called end on pool more than once");return e?e(t):this.Promise.reject(t)}this.ending=!0;let t=u(this.Promise,e);return this._endCallback=t.callback,this._pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.length}get idleCount(){return this._idle.length}get expiredCount(){return this._clients.reduce((e,t)=>e+ +!!this._expired.has(t),0)}get totalCount(){return this._clients.length}};m(d,"Pool"),t.exports=d}),eH={};b(eH,{default:()=>ez});var ez,eK=g(()=>{T(),ez={}}),eJ=_((e,t)=>{t.exports={name:"pg",version:"8.8.0",description:"PostgreSQL client - pure javascript & libpq with the same API",keywords:["database","libpq","pg","postgre","postgres","postgresql","rdbms"],homepage:"https://github.com/brianc/node-postgres",repository:{type:"git",url:"git://github.com/brianc/node-postgres.git",directory:"packages/pg"},author:"Brian Carlson <<EMAIL>>",main:"./lib",dependencies:{"buffer-writer":"2.0.0","packet-reader":"1.0.0","pg-connection-string":"^2.5.0","pg-pool":"^3.5.2","pg-protocol":"^1.5.0","pg-types":"^2.1.0",pgpass:"1.x"},devDependencies:{async:"2.6.4",bluebird:"3.5.2",co:"4.6.0","pg-copy-streams":"0.3.0"},peerDependencies:{"pg-native":">=3.0.1"},peerDependenciesMeta:{"pg-native":{optional:!0}},scripts:{test:"make test-all"},files:["lib","SPONSORS.md"],license:"MIT",engines:{node:">= 8.0.0"},gitHead:"c99fb2c127ddf8d712500db2c7b9a5491a178655"}}),eZ=_((e,t)=>{T();var r=C().EventEmitter,s=(eu(),S(ec)),n=el(),i=t.exports=function(e,t,s){r.call(this),e=n.normalizeQueryConfig(e,t,s),this.text=e.text,this.values=e.values,this.name=e.name,this.callback=e.callback,this.state="new",this._arrayMode="array"===e.rowMode,this._emitRowEvents=!1,this.on("newListener",(function(e){"row"===e&&(this._emitRowEvents=!0)}).bind(this))};s.inherits(i,r);var a={sqlState:"code",statementPosition:"position",messagePrimary:"message",context:"where",schemaName:"schema",tableName:"table",columnName:"column",dataTypeName:"dataType",constraintName:"constraint",sourceFile:"file",sourceLine:"line",sourceFunction:"routine"};i.prototype.handleError=function(e){var t=this.native.pq.resultErrorFields();if(t)for(var r in t)e[a[r]||r]=t[r];this.callback?this.callback(e):this.emit("error",e),this.state="error"},i.prototype.then=function(e,t){return this._getPromise().then(e,t)},i.prototype.catch=function(e){return this._getPromise().catch(e)},i.prototype._getPromise=function(){return this._promise||(this._promise=new Promise((function(e,t){this._once("end",e),this._once("error",t)}).bind(this))),this._promise},i.prototype.submit=function(e){this.state="running";var t=this;this.native=e.native,e.native.arrayMode=this._arrayMode;var r=m(function(r,s,n){if(e.native.arrayMode=!1,o(function(){t.emit("_done")}),r)return t.handleError(r);t._emitRowEvents&&(n.length>1?s.forEach((e,r)=>{e.forEach(e=>{t.emit("row",e,n[r])})}):s.forEach(function(e){t.emit("row",e,n)})),t.state="end",t.emit("end",n),t.callback&&t.callback(null,n)},"after");if(l.domain&&(r=l.domain.bind(r)),this.name){this.name.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",this.name,this.name.length),console.error("This can cause conflicts and silent errors executing queries"));var s=(this.values||[]).map(n.prepareValue);if(e.namedQueries[this.name]){if(this.text&&e.namedQueries[this.name]!==this.text){let e=Error(`Prepa\
red statements must be unique - '${this.name}' was used for a different statement`);return r(e)}return e.native.execute(this.name,s,r)}return e.native.prepare(this.name,this.text,s.length,function(n){return n?r(n):(e.namedQueries[t.name]=t.text,t.native.execute(t.name,s,r))})}if(this.values){if(!Array.isArray(this.values)){let e=Error("Query values must be an array");return r(e)}var i=this.values.map(n.prepareValue);e.native.query(this.text,i,r)}else e.native.query(this.text,r)}}),eX=_((e,t)=>{T();var r=(eK(),S(eH)),s=K(),n=(eJ(),C().EventEmitter),o=(eu(),S(ec)),a=eO(),c=eZ(),h=t.exports=function(e){n.call(this),e=e||{},this._Promise=e.Promise||i.Promise,this._types=new s(e.types),this.native=new r({types:this._types}),this._queryQueue=[],this._ending=!1,this._connecting=!1,this._connected=!1,this._queryable=!0;var t=this.connectionParameters=new a(e);this.user=t.user,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:t.password}),this.database=t.database,this.host=t.host,this.port=t.port,this.namedQueries={}};h.Query=c,o.inherits(h,n),h.prototype._errorAllQueries=function(e){let t=m(t=>{l.nextTick(()=>{t.native=this.native,t.handleError(e)})},"enqueueError");this._hasActiveQuery()&&(t(this._activeQuery),this._activeQuery=null),this._queryQueue.forEach(t),this._queryQueue.length=0},h.prototype._connect=function(e){var t=this;if(this._connecting)return void l.nextTick(()=>e(Error("Client has already been connected. You cannot reuse a client.")));this._connecting=!0,this.connectionParameters.getLibpqConnectionString(function(r,s){if(r)return e(r);t.native.connect(s,function(r){if(r)return t.native.end(),e(r);t._connected=!0,t.native.on("error",function(e){t._queryable=!1,t._errorAllQueries(e),t.emit("error",e)}),t.native.on("notification",function(e){t.emit("notification",{channel:e.relname,payload:e.extra})}),t.emit("connect"),t._pulseQueryQueue(!0),e()})})},h.prototype.connect=function(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})},h.prototype.query=function(e,t,r){var s,n,i,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");if("function"==typeof e.submit)i=e.query_timeout||this.connectionParameters.query_timeout,n=s=e,"function"==typeof t&&(e.callback=t);else if(i=this.connectionParameters.query_timeout,!(s=new c(e,t,r)).callback){let e,t;n=new this._Promise((r,s)=>{e=r,t=s}),s.callback=(r,s)=>r?t(r):e(s)}return i&&(a=s.callback,o=setTimeout(()=>{var e=Error("Query read timeout");l.nextTick(()=>{s.handleError(e,this.connection)}),a(e),s.callback=()=>{};var t=this._queryQueue.indexOf(s);t>-1&&this._queryQueue.splice(t,1),this._pulseQueryQueue()},i),s.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this._queryable?this._ending?(s.native=this.native,l.nextTick(()=>{s.handleError(Error("Client was closed and is not queryable"))})):(this._queryQueue.push(s),this._pulseQueryQueue()):(s.native=this.native,l.nextTick(()=>{s.handleError(Error("Client has encountered a connection error and is not queryable"))})),n},h.prototype.end=function(e){var t,r=this;return this._ending=!0,this._connected||this.once("connect",this.end.bind(this,e)),e||(t=new this._Promise(function(t,r){e=m(e=>e?r(e):t(),"cb")})),this.native.end(function(){r._errorAllQueries(Error("Connection terminated")),l.nextTick(()=>{r.emit("end"),e&&e()})}),t},h.prototype._hasActiveQuery=function(){return this._activeQuery&&"error"!==this._activeQuery.state&&"end"!==this._activeQuery.state},h.prototype._pulseQueryQueue=function(e){if(this._connected&&!this._hasActiveQuery()){var t=this._queryQueue.shift();if(!t){e||this.emit("drain");return}this._activeQuery=t,t.submit(this);var r=this;t.once("_done",function(){r._pulseQueryQueue()})}},h.prototype.cancel=function(e){this._activeQuery===e?this.native.cancel(function(){}):-1!==this._queryQueue.indexOf(e)&&this._queryQueue.splice(this._queryQueue.indexOf(e),1)},h.prototype.ref=function(){},h.prototype.unref=function(){},h.prototype.setTypeParser=function(e,t,r){return this._types.setTypeParser(e,t,r)},h.prototype.getTypeParser=function(e,t){return this._types.getTypeParser(e,t)}}),e0=_((e,t)=>{T(),t.exports=eX()}),e1=_((e,t)=>{T();var r=eV(),s=ea(),n=eG(),i=eY(),{DatabaseError:o}=eq(),a=m(e=>{var t;return m(t=class extends i{constructor(t){super(t,e)}},"BoundPool"),t},"poolFactory"),c=m(function(e){this.defaults=s,this.Client=e,this.Query=this.Client.Query,this.Pool=a(this.Client),this._pools=[],this.Connection=n,this.types=z(),this.DatabaseError=o},"PG");"u">typeof l.env.NODE_PG_FORCE_NATIVE?t.exports=new c(e0()):(t.exports=new c(r),Object.defineProperty(t.exports,"native",{configurable:!0,enumerable:!1,get(){var e=null;try{e=new c(e0())}catch(e){if("MODULE_NOT_FOUND"!==e.code)throw e}return Object.defineProperty(t.exports,"native",{value:e}),e}}))});T(),T(),M(),U(),T();var e2=Object.defineProperty,e6=Object.defineProperties,e5=Object.getOwnPropertyDescriptors,e8=Object.getOwnPropertySymbols,e3=Object.prototype.hasOwnProperty,e4=Object.prototype.propertyIsEnumerable,e7=m((e,t,r)=>t in e?e2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,"__defNormalProp"),e9=m((e,t)=>{for(var r in t||(t={}))e3.call(t,r)&&e7(e,r,t[r]);if(e8)for(var r of e8(t))e4.call(t,r)&&e7(e,r,t[r]);return e},"__spreadValues"),te=m((e,t)=>e6(e,e5(t)),"__spreadProps"),tt=2===new Uint8Array(new Uint16Array([258]).buffer)[0],tr=new TextDecoder,ts=new TextEncoder,tn=ts.encode("0123456789abcdef"),ti=ts.encode("0123456789ABCDEF"),to=ts.encode("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").slice();function ta(e,{alphabet:t,scratchArr:r}={}){if(!s)if(s=new Uint16Array(256),n=new Uint16Array(256),tt)for(let e=0;e<256;e++)s[e]=tn[15&e]<<8|tn[e>>>4],n[e]=ti[15&e]<<8|ti[e>>>4];else for(let e=0;e<256;e++)s[e]=tn[15&e]|tn[e>>>4]<<8,n[e]=ti[15&e]|ti[e>>>4]<<8;e.byteOffset%4!=0&&(e=new Uint8Array(e));let i=e.length,o=i>>>2,a=r||new Uint16Array(i),l=new Uint32Array(e.buffer,e.byteOffset,o),c=new Uint32Array(a.buffer,a.byteOffset,i>>>1),h="upper"===t?n:s,u=0,f=0,d;if(tt)for(;u<o;)d=l[u++],c[f++]=h[d>>>8&255]<<16|h[255&d],c[f++]=h[d>>>24]<<16|h[d>>>16&255];else for(;u<o;)d=l[u++],c[f++]=h[d>>>24]<<16|h[d>>>16&255],c[f++]=h[d>>>8&255]<<16|h[255&d];for(u<<=2;u<i;)a[u]=h[e[u++]];return tr.decode(a.subarray(0,i))}function tl(e,t={}){let r="",s=e.length,n=Math.ceil(s/504e3),i=new Uint16Array(n>1?504e3:s);for(let s=0;s<n;s++){let n=504e3*s,o=n+504e3;r+=ta(e.subarray(n,o),te(e9({},t),{scratchArr:i}))}return r}function tc(e,t={}){return"upper"!==t.alphabet&&"function"==typeof e.toHex?e.toHex():tl(e,t)}to[62]=45,to[63]=95,m(ta,"_toHex"),m(tl,"_toHexChunked"),m(tc,"toHex"),T();var th=class e{constructor(e,t){this.strings=e,this.values=t}toParameterizedQuery(t={query:"",params:[]}){let{strings:r,values:s}=this;for(let n=0,i=r.length;n<i;n++)if(t.query+=r[n],n<s.length){let r=s[n];if(r instanceof tf)t.query+=r.sql;else if(r instanceof tS)if(r.queryData instanceof e)r.queryData.toParameterizedQuery(t);else{if(r.queryData.params?.length)throw Error("This query is not composable");t.query+=r.queryData.query}else{let{params:e}=t;e.push(r),t.query+="$"+e.length,(r instanceof a||ArrayBuffer.isView(r))&&(t.query+="::bytea")}}return t}};m(th,"SqlTemplate");var tu=class{constructor(e){this.sql=e}};m(tu,"UnsafeRawSql");var tf=tu,td=w(K()),tp=w(el()),ty=class e extends Error{constructor(t){super(t),E(this,"name","NeonDbError"),E(this,"severity"),E(this,"code"),E(this,"detail"),E(this,"hint"),E(this,"position"),E(this,"internalPosition"),E(this,"internalQuery"),E(this,"where"),E(this,"schema"),E(this,"table"),E(this,"column"),E(this,"dataType"),E(this,"constraint"),E(this,"file"),E(this,"line"),E(this,"routine"),E(this,"sourceError"),"captureStackTrace"in Error&&"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,e)}};m(ty,"NeonDbError");var tm="transaction() expects an array of queries, or a function returning an array of queries",tg=["severity","code","detail","hint","position","internalPosition","internalQuery","where","schema","table","column","dataType","constraint","file","line","routine"];function t_(e){return e instanceof a?"\\x"+tc(e):e}function tb(e){let{query:t,params:r}=e instanceof th?e.toParameterizedQuery():e;return{query:t,params:r.map(e=>t_((0,tp.prepareValue)(e)))}}function tv(e,{arrayMode:t,fullResults:r,fetchOptions:s,isolationLevel:n,readOnly:i,deferrable:o,authToken:a}={}){let l;if(!e)throw Error("No database connection string was provided to `neon()`. Perhaps an environment variable has not been set?");try{l=D(e)}catch{throw Error("Database connection string provided to `neon()` is not a valid URL. Connection string: "+String(e))}let{protocol:c,username:h,hostname:u,port:f,pathname:d}=l;if("postgres:"!==c&&"postgresql:"!==c||!h||!u||!d)throw Error("Database connection string format for `neon()` should be: postgresql://user:<EMAIL>/dbname?option=value");function p(e,...t){if(!(Array.isArray(e)&&Array.isArray(e.raw)&&Array.isArray(t)))throw Error('This function can now be called only as a tagged-template function: sql`SELECT ${value}`, not sql("SELECT $1", [value], options). For a conventional function call with value placeholders ($1, $2, etc.), use sql.query("SELECT $1", [value], options).');return new tS(y,new th(e,t))}async function y(l,c,h){let d,{fetchEndpoint:p,fetchFunction:y}=B,m=Array.isArray(l)?{queries:l.map(e=>tb(e))}:tb(l),g=s??{},_=t??!1,b=r??!1,v=n,w=i,S=o;void 0!==h&&(void 0!==h.fetchOptions&&(g={...g,...h.fetchOptions}),void 0!==h.arrayMode&&(_=h.arrayMode),void 0!==h.fullResults&&(b=h.fullResults),void 0!==h.isolationLevel&&(v=h.isolationLevel),void 0!==h.readOnly&&(w=h.readOnly),void 0!==h.deferrable&&(S=h.deferrable)),void 0===c||Array.isArray(c)||void 0===c.fetchOptions||(g={...g,...c.fetchOptions});let E=a;Array.isArray(c)||c?.authToken===void 0||(E=c.authToken);let x="function"==typeof p?p(u,f,{jwtAuth:void 0!==E}):p,A={"Neon-Connection-String":e,"Neon-Raw-Text-Output":"true","Neon-Array-Mode":"true"},R=await tx(E);R&&(A.Authorization=`Bearer ${R}`),Array.isArray(l)&&(void 0!==v&&(A["Neon-Batch-Isolation-Level"]=v),void 0!==w&&(A["Neon-Batch-Read-Only"]=String(w)),void 0!==S&&(A["Neon-Batch-Deferrable"]=String(S)));try{d=await (y??fetch)(x,{method:"POST",body:JSON.stringify(m),headers:A,...g})}catch(t){let e=new ty(`Error connecting to database: ${t}`);throw e.sourceError=t,e}if(d.ok){let e=await d.json();if(Array.isArray(l)){let t=e.results;if(!Array.isArray(t))throw new ty("Neon internal error: unexpected result format");return t.map((e,t)=>{let r=c[t]??{};return tE(e,{arrayMode:r.arrayMode??_,fullResults:r.fullResults??b,types:r.types})})}{let t=c??{};return tE(e,{arrayMode:t.arrayMode??_,fullResults:t.fullResults??b,types:t.types})}}{let{status:e}=d;if(400===e){let e=await d.json(),t=new ty(e.message);for(let r of tg)t[r]=e[r]??void 0;throw t}{let t=await d.text();throw new ty(`Server error (HTTP status ${e}): ${t}`)}}}return m(p,"templateFn"),p.query=(e,t,r)=>new tS(y,{query:e,params:t??[]},r),p.unsafe=e=>new tf(e),p.transaction=async(e,t)=>{if("function"==typeof e&&(e=e(p)),!Array.isArray(e))throw Error(tm);return e.forEach(e=>{if(!(e instanceof tS))throw Error(tm)}),y(e.map(e=>e.queryData),e.map(e=>e.opts??{}),t)},m(y,"execute"),p}m(t_,"encodeBuffersAsBytea"),m(tb,"prepareQuery"),m(tv,"neon");var tw=class{constructor(e,t,r){this.execute=e,this.queryData=t,this.opts=r}then(e,t){return this.execute(this.queryData,this.opts).then(e,t)}catch(e){return this.execute(this.queryData,this.opts).catch(e)}finally(e){return this.execute(this.queryData,this.opts).finally(e)}};m(tw,"NeonQueryPromise");var tS=tw;function tE(e,{arrayMode:t,fullResults:r,types:s}){let n=new td.default(s),i=e.fields.map(e=>e.name),o=e.fields.map(e=>n.getTypeParser(e.dataTypeID)),a=!0===t?e.rows.map(e=>e.map((e,t)=>null===e?null:o[t](e))):e.rows.map(e=>Object.fromEntries(e.map((e,t)=>[i[t],null===e?null:o[t](e)])));return r?(e.viaNeonFetch=!0,e.rowAsArray=t,e.rows=a,e._parsers=o,e._types=n,e):a}async function tx(e){if("string"==typeof e)return e;if("function"==typeof e)try{return await Promise.resolve(e())}catch(t){let e=new ty("Error getting auth token.");throw t instanceof Error&&(e=new ty(`Error getting auth to\
ken: ${t.message}`)),e}}m(tE,"processQueryResult"),m(tx,"getAuthToken"),T();var tA=w(e1());T();var tR=w(e1()),tT=class extends tR.Client{constructor(e){super(e),this.config=e}get neonConfig(){return this.connection.stream}connect(e){let{neonConfig:t}=this;t.forceDisablePgSSL&&(this.ssl=this.connection.ssl=!1),this.ssl&&t.useSecureWebSocket&&console.warn("SSL is enabled for both Postgres (e.g. ?sslmode=require in the connection string + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSocket = true). Double encryption will increase latency and CPU usage. It may be appropriate to disable SSL in the Postgres connection parameters or set forceDisablePgSSL = true.");let r="string"!=typeof this.config&&this.config?.host!==void 0||"string"!=typeof this.config&&this.config?.connectionString!==void 0||void 0!==l.env.PGHOST,s=l.env.USER??l.env.USERNAME;if(!r&&"localhost"===this.host&&this.user===s&&this.database===s&&null===this.password)throw Error(`No database host or connection string wa\
s set, and key parameters have default values (host: localhost, user: ${s}, db: ${s}, password: null\
). Is an environment variable missing? Alternatively, if you intended to connect with these paramete\
rs, please set the host to 'localhost' explicitly.`);let n=super.connect(e),i=t.pipelineTLS&&this.ssl,o="password"===t.pipelineConnect;if(!i&&!t.pipelineConnect)return n;let a=this.connection;if(i&&a.on("connect",()=>a.stream.emit("data","S")),o){a.removeAllListeners("authenticationCleartextPassword"),a.removeAllListeners("readyForQuery"),a.once("readyForQuery",()=>a.on("readyForQuery",this._handleReadyForQuery.bind(this)));let e=this.ssl?"sslconnect":"connect";a.on(e,()=>{this._handleAuthCleartextPassword(),this._handleReadyForQuery()})}return n}async _handleAuthSASLContinue(e){if(typeof crypto>"u"||void 0===crypto.subtle||void 0===crypto.subtle.importKey)throw Error("Cannot use SASL auth when `crypto.subtle` is not defined");let t=crypto.subtle,r=this.saslSession,s=this.password,n=e.data;if("SASLInitialResponse"!==r.message||"string"!=typeof s||"string"!=typeof n)throw Error("SASL: protocol error");let i=Object.fromEntries(n.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]})),o=i.r,l=i.s,c=i.i;if(!o||!/^[!-+--~]+$/.test(o))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing/unprintable");if(!l||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(l))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base64");if(!c||!/^[1-9][0-9]*$/.test(c))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: missing/invalid iteration count");if(!o.startsWith(r.clientNonce))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");if(o.length===r.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short");let h=parseInt(c,10),u=a.from(l,"base64"),f=new TextEncoder,d=f.encode(s),p=await t.importKey("raw",d,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),y=new Uint8Array(await t.sign("HMAC",p,a.concat([u,a.from([0,0,0,1])]))),m=y;for(var g=0;g<h-1;g++)y=new Uint8Array(await t.sign("HMAC",p,y)),m=a.from(m.map((e,t)=>m[t]^y[t]));let _=m,b=await t.importKey("raw",_,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),v=new Uint8Array(await t.sign("HMAC",b,f.encode("Client Key"))),w=await t.digest("SHA-256",v),S="n=*,r="+r.clientNonce,E="r="+o+",s="+l+",i="+h,x="c=biws,r="+o,A=S+","+E+","+x,R=await t.importKey("raw",w,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var T=new Uint8Array(await t.sign("HMAC",R,f.encode(A))),C=a.from(v.map((e,t)=>v[t]^T[t])).toString("base64");let I=await t.importKey("raw",_,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),k=await t.sign("HMAC",I,f.encode("Server Key")),L=await t.importKey("raw",k,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var P=a.from(await t.sign("HMAC",L,f.encode(A)));r.message="SASLResponse",r.serverSignature=P.toString("base64"),r.response=x+",p="+C,this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}};m(tT,"NeonClient"),M();var tC=w(eO());function tI(e,t){let r,s;return t?{callback:t,result:void 0}:{callback:m(function(e,t){e?r(e):s(t)},"cb"),result:new e(function(e,t){s=e,r=t})}}m(tI,"promisify");var tk=class extends tA.Pool{constructor(){super(...arguments),E(this,"Client",tT),E(this,"hasFetchUnsupportedListeners",!1),E(this,"addListener",this.on)}on(e,t){return"error"!==e&&(this.hasFetchUnsupportedListeners=!0),super.on(e,t)}query(e,t,r){if(!B.poolQueryViaFetch||this.hasFetchUnsupportedListeners||"function"==typeof e)return super.query(e,t,r);"function"==typeof t&&(r=t,t=void 0);let s=tI(this.Promise,r);r=s.callback;try{let s=new tC.default(this.options),n=encodeURIComponent,i=encodeURI,o=`postgresql://${n(s.user)}:${n(s.password)}@${n(s.host)}\
/${i(s.database)}`,a="string"==typeof e?e:e.text,l=t??e.values??[];tv(o,{fullResults:!0,arrayMode:"array"===e.rowMode}).query(a,l,{types:e.types??this.options?.types}).then(e=>r(void 0,e)).catch(e=>r(e))}catch(e){r(e)}return s.result}};m(tk,"NeonPool");var tL=tk;M();var tP=w(e1());tP.DatabaseError,tP.defaults;var tO=tP.types},10757:(e,t,r)=>{"use strict";let{EMPTY_BUFFER:s}=r(66385),n=Buffer[Symbol.species];function i(e,t,r,s,n){for(let i=0;i<n;i++)r[s+i]=e[i]^t[3&i]}function o(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new n(e):ArrayBuffer.isView(e)?t=new n(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return s;if(1===e.length)return e[0];let r=Buffer.allocUnsafe(t),i=0;for(let t=0;t<e.length;t++){let s=e[t];r.set(s,i),i+=s.length}return i<t?new n(r.buffer,r.byteOffset,i):r},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=r(82102);e.exports.mask=function(e,r,s,n,o){o<48?i(e,r,s,n,o):t.mask(e,r,s,n,o)},e.exports.unmask=function(e,r){e.length<32?o(e,r):t.unmask(e,r)}}catch(e){}},16726:(e,t)=>{"use strict";t.q=function(e,t){return function e(t,r,s=!1){let n="",i=!1,o=0,a=0,l=[],c="",h=function(e){let t=c;(t.length>0||e)&&("NULL"!==t||e||(t=null),null!==t&&r&&(t=r(t)),l.push(t),c="")};if("["===t[0])for(;o<t.length&&"="!==t[o++];);for(;o<t.length;){let u=!1;if("\\"===(n=t[o++])&&(n=t[o++],u=!0),"{"!==n||i){if("}"!==n||i)'"'!==n||u?","!==n||i?c+=n:h():(i&&h(!0),i=!i);else if(!--a&&(h(),s))return{entries:l,position:o}}else if(++a>1){let s=e(t.substr(o-1),r,!0);l.push(s.entries),o+=s.position-2}}if(0!==a)throw Error("array dimension not balanced");return l}(e,t)}},17166:(e,t,r)=>{"use strict";let{tokenChars:s}=r(89577);e.exports={parse:function(e){let t=new Set,r=-1,n=-1,i=0;for(;i<e.length;i++){let o=e.charCodeAt(i);if(-1===n&&1===s[o])-1===r&&(r=i);else if(0!==i&&(32===o||9===o))-1===n&&-1!==r&&(n=i);else if(44===o){if(-1===r)throw SyntaxError(`Unexpected character at index ${i}`);-1===n&&(n=i);let s=e.slice(r,n);if(t.has(s))throw SyntaxError(`The "${s}" subprotocol is duplicated`);t.add(s),r=n=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===r||-1!==n)throw SyntaxError("Unexpected end of input");let o=e.slice(r,i);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},18766:(e,t,r)=>{"use strict";r(76015);let{Duplex:s}=r(27910);function n(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let r=!0,a=new s({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,r){let s=!r&&a._readableState.objectMode?t.toString():t;a.push(s)||e.pause()}),e.once("error",function(e){a.destroyed||(r=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,s){if(e.readyState===e.CLOSED){s(t),process.nextTick(n,a);return}let i=!1;e.once("error",function(e){i=!0,s(e)}),e.once("close",function(){i||s(t),process.nextTick(n,a)}),r&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,r,s){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._write(t,r,s)});e.send(t,s)},a.on("end",i),a.on("error",o),a}},22543:(e,t,r)=>{"use strict";let{Writable:s}=r(27910),n=r(68622),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=r(66385),{concat:c,toArrayBuffer:h,unmask:u}=r(10757),{isValidStatusCode:f,isValidUTF8:d}=r(89577),p=Buffer[Symbol.species];class y extends s{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new p(t.buffer,t.byteOffset+e,t.length-e),new p(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],s=t.length-e;e>=r.length?t.set(this._buffers.shift(),s):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),s),this._buffers[0]=new p(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let r=(64&t[0])==64;if(r&&!this._extensions[n.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=r}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[n.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let s;s="nodebuffer"===this._binaryType?c(r,t):"arraybuffer"===this._binaryType?h(c(r,t)):"blob"===this._binaryType?new Blob(r):r,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!0),this._state=0,this.startLoop(e)}))}else{let s=c(r,t);if(!this._skipUTF8Validation&&!d(s))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let r=e.readUInt16BE(0);if(!f(r))return void t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let s=new p(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!d(s))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",r,s),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,s,n){this._loop=!1,this._errored=!0;let i=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(i,this.createError),i.code=n,i[a]=s,i}}e.exports=y},25208:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n}),r(18766),r(22543),r(72643);var s=r(76015);r(26123);let n=s},26123:(e,t,r)=>{"use strict";let s=r(94735),n=r(81630),{Duplex:i}=r(27910),{createHash:o}=r(55511),a=r(54649),l=r(68622),c=r(17166),h=r(76015),{GUID:u,kWebSocket:f}=r(66385),d=/^[+/0-9A-Za-z]{22}==$/;class p extends s{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:h,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=n.createServer((e,t)=>{let r=n.STATUS_CODES[426];t.writeHead(426,{"Content-Length":r.length,"Content-Type":"text/plain"}),t.end(r)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,s)=>{this.handleUpgrade(t,r,s,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(y,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(y,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{y(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,s){t.on("error",m);let n=e.headers["sec-websocket-key"],i=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void _(this,e,t,405,"Invalid HTTP method");if(void 0===i||"websocket"!==i.toLowerCase())return void _(this,e,t,400,"Invalid Upgrade header");if(void 0===n||!d.test(n))return void _(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==o&&13!==o)return void _(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(e))return void g(t,400);let h=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==h)try{u=c.parse(h)}catch(r){_(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],p={};if(this.options.perMessageDeflate&&void 0!==f){let r=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(f);e[l.extensionName]&&(r.accept(e[l.extensionName]),p[l.extensionName]=r)}catch(r){_(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(i,(i,o,a,l)=>{if(!i)return g(t,o||401,a,l);this.completeUpgrade(p,n,u,e,t,r,s)});if(!this.options.verifyClient(i))return g(t,401)}this.completeUpgrade(p,n,u,e,t,r,s)}completeUpgrade(e,t,r,s,n,i,c){if(!n.readable||!n.writable)return n.destroy();if(n[f])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return g(n,503);let h=o("sha1").update(t+u).digest("base64"),d=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${h}`],p=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,s):r.values().next().value;e&&(d.push(`Sec-WebSocket-Protocol: ${e}`),p._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,r=a.format({[l.extensionName]:[t]});d.push(`Sec-WebSocket-Extensions: ${r}`),p._extensions=e}this.emit("headers",d,s),n.write(d.concat("\r\n").join("\r\n")),n.removeListener("error",m),p.setSocket(n,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(p),p.on("close",()=>{this.clients.delete(p),this._shouldEmitClose&&!this.clients.size&&process.nextTick(y,this)})),c(p,s)}}function y(e){e._state=2,e.emit("close")}function m(){this.destroy()}function g(e,t,r,s){r=r||n.STATUS_CODES[t],s={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...s},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${n.STATUS_CODES[t]}\r
`+Object.keys(s).map(e=>`${e}: ${s[e]}`).join("\r\n")+"\r\n\r\n"+r)}function _(e,t,r,s,n){if(e.listenerCount("wsClientError")){let s=Error(n);Error.captureStackTrace(s,_),e.emit("wsClientError",s,r,t)}else g(r,s,n)}e.exports=p},38280:(e,t,r)=>{"use strict";let{kForOnEventAttribute:s,kListener:n}=r(66385),i=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),c=Symbol("kReason"),h=Symbol("kTarget"),u=Symbol("kType"),f=Symbol("kWasClean");class d{constructor(e){this[h]=null,this[u]=e}get target(){return this[h]}get type(){return this[u]}}Object.defineProperty(d.prototype,"target",{enumerable:!0}),Object.defineProperty(d.prototype,"type",{enumerable:!0});class p extends d{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[c]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[c]}get wasClean(){return this[f]}}Object.defineProperty(p.prototype,"code",{enumerable:!0}),Object.defineProperty(p.prototype,"reason",{enumerable:!0}),Object.defineProperty(p.prototype,"wasClean",{enumerable:!0});class y extends d{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(y.prototype,"error",{enumerable:!0}),Object.defineProperty(y.prototype,"message",{enumerable:!0});class m extends d{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function g(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:p,ErrorEvent:y,Event:d,EventTarget:{addEventListener(e,t,r={}){let i;for(let i of this.listeners(e))if(!r[s]&&i[n]===t&&!i[s])return;if("message"===e)i=function(e,r){let s=new m("message",{data:r?e:e.toString()});s[h]=this,g(t,this,s)};else if("close"===e)i=function(e,r){let s=new p("close",{code:e,reason:r.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});s[h]=this,g(t,this,s)};else if("error"===e)i=function(e){let r=new y("error",{error:e,message:e.message});r[h]=this,g(t,this,r)};else{if("open"!==e)return;i=function(){let e=new d("open");e[h]=this,g(t,this,e)}}i[s]=!!r[s],i[n]=t,r.once?this.once(e,i):this.on(e,i)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[n]===t&&!r[s]){this.removeListener(e,r);break}}},MessageEvent:m}},44356:e=>{"use strict";let t=Symbol("kDone"),r=Symbol("kRun");class s{constructor(e){this[t]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=s},54649:(e,t,r)=>{"use strict";let{tokenChars:s}=r(89577);function n(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}e.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,r,i=Object.create(null),o=Object.create(null),a=!1,l=!1,c=!1,h=-1,u=-1,f=-1,d=0;for(;d<e.length;d++)if(u=e.charCodeAt(d),void 0===t)if(-1===f&&1===s[u])-1===h&&(h=d);else if(0!==d&&(32===u||9===u))-1===f&&-1!==h&&(f=d);else if(59===u||44===u){if(-1===h)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d);let r=e.slice(h,f);44===u?(n(i,r,o),o=Object.create(null)):t=r,h=f=-1}else throw SyntaxError(`Unexpected character at index ${d}`);else if(void 0===r)if(-1===f&&1===s[u])-1===h&&(h=d);else if(32===u||9===u)-1===f&&-1!==h&&(f=d);else if(59===u||44===u){if(-1===h)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d),n(o,e.slice(h,f),!0),44===u&&(n(i,t,o),o=Object.create(null),t=void 0),h=f=-1}else if(61===u&&-1!==h&&-1===f)r=e.slice(h,d),h=f=-1;else throw SyntaxError(`Unexpected character at index ${d}`);else if(l){if(1!==s[u])throw SyntaxError(`Unexpected character at index ${d}`);-1===h?h=d:a||(a=!0),l=!1}else if(c)if(1===s[u])-1===h&&(h=d);else if(34===u&&-1!==h)c=!1,f=d;else if(92===u)l=!0;else throw SyntaxError(`Unexpected character at index ${d}`);else if(34===u&&61===e.charCodeAt(d-1))c=!0;else if(-1===f&&1===s[u])-1===h&&(h=d);else if(-1!==h&&(32===u||9===u))-1===f&&(f=d);else if(59===u||44===u){if(-1===h)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d);let s=e.slice(h,f);a&&(s=s.replace(/\\/g,""),a=!1),n(o,r,s),44===u&&(n(i,t,o),o=Object.create(null),t=void 0),r=void 0,h=f=-1}else throw SyntaxError(`Unexpected character at index ${d}`);if(-1===h||c||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===f&&(f=d);let p=e.slice(h,f);return void 0===t?n(i,p,o):(void 0===r?n(o,p,!0):a?n(o,r,p.replace(/\\/g,"")):n(o,r,p),n(i,t,o)),i}}},59986:(e,t,r)=>{"use strict";function s(e,t){if(e instanceof Promise)throw Error(t)}function n(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},o=e.isServer??("undefined"==typeof window||"Deno"in window),a=o?{...n,...i,...r}:{...r,...i},l=e.createFinalSchema?.(a,o)["~standard"].validate(t)??function(e,t){let r={},n=[];for(let i in e){let o=e[i]["~standard"].validate(t[i]);if(s(o,`Validation must be synchronous, but ${i} returned a Promise.`),o.issues){n.push(...o.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}r[i]=o.value}return n.length?{issues:n}:{value:r}}(a,t);s(l,"Validation must be synchronous");let c=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),h=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(l.issues)return c(l.issues);let u=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),f=e=>o||!u(e),d=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),l.value),{get(e,t){if("string"==typeof t&&!d(t))return f(t)?Reflect.get(e,t):h(t)}})}r.d(t,{w:()=>n})},66385:e=>{"use strict";let t=["nodebuffer","arraybuffer","fragments"],r="undefined"!=typeof Blob;r&&t.push("blob"),e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:r,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},68622:(e,t,r)=>{"use strict";let s,n=r(74075),i=r(10757),o=r(44356),{kStatusCode:a}=r(66385),l=Buffer[Symbol.species],c=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),u=Symbol("total-length"),f=Symbol("callback"),d=Symbol("buffers"),p=Symbol("error");class y{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,s||(s=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[f];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,t,r){s.add(s=>{this._decompress(e,t,(e,t)=>{s(),r(e,t)})})}compress(e,t,r){s.add(s=>{this._compress(e,t,(e,t)=>{s(),r(e,t)})})}_decompress(e,t,r){let s=this._isServer?"client":"server";if(!this._inflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=n.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[u]=0,this._inflate[d]=[],this._inflate.on("error",_),this._inflate.on("data",g)}this._inflate[f]=r,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush(()=>{let e=this._inflate[p];if(e){this._inflate.close(),this._inflate=null,r(e);return}let n=i.concat(this._inflate[d],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[d]=[],t&&this.params[`${s}_no_context_takeover`]&&this._inflate.reset()),r(null,n)})}_compress(e,t,r){let s=this._isServer?"server":"client";if(!this._deflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=n.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[d]=[],this._deflate.on("data",m)}this._deflate[f]=r,this._deflate.write(e),this._deflate.flush(n.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[d],this._deflate[u]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[f]=null,this._deflate[u]=0,this._deflate[d]=[],t&&this.params[`${s}_no_context_takeover`]&&this._deflate.reset(),r(null,e)})}}function m(e){this[d].push(e),this[u]+=e.length}function g(e){if(this[u]+=e.length,this[h]._maxPayload<1||this[u]<=this[h]._maxPayload)return void this[d].push(e);this[p]=RangeError("Max payload size exceeded"),this[p].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[p][a]=1009,this.removeListener("data",g),this.reset()}function _(e){if(this[h]._inflate=null,this[p])return void this[f](this[p]);e[a]=1007,this[f](e)}e.exports=y},71166:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(59986);function n(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},n=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,s.w)({...e,shared:n,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}},71518:e=>{"use strict";e.exports={mask:(e,t,r,s,n)=>{for(var i=0;i<n;i++)r[s+i]=e[i]^t[3&i]},unmask:(e,t)=>{let r=e.length;for(var s=0;s<r;s++)e[s]^=t[3&s]}}},72643:(e,t,r)=>{"use strict";let s,{Duplex:n}=r(27910),{randomFillSync:i}=r(55511),o=r(68622),{EMPTY_BUFFER:a,kWebSocket:l,NOOP:c}=r(66385),{isBlob:h,isValidStatusCode:u}=r(89577),{mask:f,toBuffer:d}=r(10757),p=Symbol("kByteLength"),y=Buffer.alloc(4),m=8192;class g{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=c,this[l]=void 0}static frame(e,t){let r,n,o=!1,a=2,l=!1;t.mask&&(r=t.maskBuffer||y,t.generateMask?t.generateMask(r):(8192===m&&(void 0===s&&(s=Buffer.alloc(8192)),i(s,0,8192),m=0),r[0]=s[m++],r[1]=s[m++],r[2]=s[m++],r[3]=s[m++]),l=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?n=(!t.mask||l)&&void 0!==t[p]?t[p]:(e=Buffer.from(e)).length:(n=e.length,o=t.mask&&t.readOnly&&!l);let c=n;n>=65536?(a+=8,c=127):n>125&&(a+=2,c=126);let h=Buffer.allocUnsafe(o?n+a:a);return(h[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(h[0]|=64),h[1]=c,126===c?h.writeUInt16BE(n,2):127===c&&(h[2]=h[3]=0,h.writeUIntBE(n,4,6)),t.mask)?(h[1]|=128,h[a-4]=r[0],h[a-3]=r[1],h[a-2]=r[2],h[a-1]=r[3],l)?[h,e]:o?(f(e,r,h,a,n),[h]):(f(e,r,e,0,n),[h,e]):[h,e]}close(e,t,r,s){let n;if(void 0===e)n=a;else if("number"==typeof e&&u(e))if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(n=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?n.write(t,2):n.set(t,2)}else(n=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let i={[p]:n.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,n,!1,i,s]):this.sendFrame(g.frame(n,i),s)}ping(e,t,r){let s,n;if("string"==typeof e?(s=Buffer.byteLength(e),n=!1):h(e)?(s=e.size,n=!1):(s=(e=d(e)).length,n=d.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:n,rsv1:!1};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,i,r]):this.getBlobData(e,!1,i,r):0!==this._state?this.enqueue([this.dispatch,e,!1,i,r]):this.sendFrame(g.frame(e,i),r)}pong(e,t,r){let s,n;if("string"==typeof e?(s=Buffer.byteLength(e),n=!1):h(e)?(s=e.size,n=!1):(s=(e=d(e)).length,n=d.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:n,rsv1:!1};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,i,r]):this.getBlobData(e,!1,i,r):0!==this._state?this.enqueue([this.dispatch,e,!1,i,r]):this.sendFrame(g.frame(e,i),r)}send(e,t,r){let s,n,i=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;"string"==typeof e?(s=Buffer.byteLength(e),n=!1):h(e)?(s=e.size,n=!1):(s=(e=d(e)).length,n=d.readOnly),this._firstFragment?(this._firstFragment=!1,l&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=s>=i._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0);let c={[p]:s,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:n,rsv1:l};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,c,r]):this.getBlobData(e,this._compress,c,r):0!==this._state?this.enqueue([this.dispatch,e,this._compress,c,r]):this.dispatch(e,this._compress,c,r)}getBlobData(e,t,r,s){this._bufferedBytes+=r[p],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(_,this,e,s);return}this._bufferedBytes-=r[p];let n=d(e);t?this.dispatch(n,t,r,s):(this._state=0,this.sendFrame(g.frame(n,r),s),this.dequeue())}).catch(e=>{process.nextTick(b,this,e,s)})}dispatch(e,t,r,s){if(!t)return void this.sendFrame(g.frame(e,r),s);let n=this._extensions[o.extensionName];this._bufferedBytes+=r[p],this._state=1,n.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed)return void _(this,Error("The socket was closed while data was being compressed"),s);this._bufferedBytes-=r[p],this._state=0,r.readOnly=!1,this.sendFrame(g.frame(t,r),s),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][p],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][p],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function _(e,t,r){"function"==typeof r&&r(t);for(let r=0;r<e._queue.length;r++){let s=e._queue[r],n=s[s.length-1];"function"==typeof n&&n(t)}}function b(e,t,r){_(e,t,r),e.onerror(t)}e.exports=g},73378:(e,t,r)=>{"use strict";r.d(t,{J:()=>ev});var s,n,i,o,a=r(9507),l=Object.defineProperty,c={};((e,t)=>{for(var r in t)l(e,r,{get:t[r],enumerable:!0})})(c,{$:()=>u,bgBlack:()=>L,bgBlue:()=>M,bgCyan:()=>D,bgGreen:()=>O,bgMagenta:()=>N,bgRed:()=>P,bgWhite:()=>U,bgYellow:()=>B,black:()=>w,blue:()=>A,bold:()=>p,cyan:()=>T,dim:()=>y,gray:()=>I,green:()=>E,grey:()=>k,hidden:()=>b,inverse:()=>_,italic:()=>m,magenta:()=>R,red:()=>S,reset:()=>d,strikethrough:()=>v,underline:()=>g,white:()=>C,yellow:()=>x});var h=!0;"undefined"!=typeof process&&({FORCE_COLOR:s,NODE_DISABLE_COLORS:n,NO_COLOR:i,TERM:o}=process.env||{},h=process.stdout&&process.stdout.isTTY);var u={enabled:!n&&null==i&&"dumb"!==o&&(null!=s&&"0"!==s||h)};function f(e,t){let r=RegExp(`\\x1b\\[${t}m`,"g"),s=`\x1b[${e}m`,n=`\x1b[${t}m`;return function(e){return u.enabled&&null!=e?s+(~(""+e).indexOf(n)?e.replace(r,n+s):e)+n:e}}var d=f(0,0),p=f(1,22),y=f(2,22),m=f(3,23),g=f(4,24),_=f(7,27),b=f(8,28),v=f(9,29),w=f(30,39),S=f(31,39),E=f(32,39),x=f(33,39),A=f(34,39),R=f(35,39),T=f(36,39),C=f(37,39),I=f(90,39),k=f(90,39),L=f(40,49),P=f(41,49),O=f(42,49),B=f(43,49),M=f(44,49),N=f(45,49),D=f(46,49),U=f(47,49),F=["green","yellow","blue","magenta","cyan","red"],j=[],q=Date.now(),Q=0,W="undefined"!=typeof process?process.env:{};globalThis.DEBUG??=W.DEBUG??"",globalThis.DEBUG_COLORS??=!W.DEBUG_COLORS||"true"===W.DEBUG_COLORS;var $={enable(e){"string"==typeof e&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(e=>e.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(t=>""!==t&&"-"!==t[0]&&e.match(RegExp(t.split("*").join(".*")+"$"))),s=t.some(t=>""!==t&&"-"===t[0]&&e.match(RegExp(t.slice(1).split("*").join(".*")+"$")));return r&&!s},log:(...e)=>{let[t,r,...s]=e;(console.warn??console.log)(`${t} ${r}`,...s)},formatters:{}},G=new Proxy(function(e){let t={color:F[Q++%F.length],enabled:$.enabled(e),namespace:e,log:$.log,extend:()=>{}};return new Proxy((...e)=>{let{enabled:r,namespace:s,color:n,log:i}=t;if(0!==e.length&&j.push([s,...e]),j.length>100&&j.shift(),$.enabled(s)||r){let t=e.map(e=>"string"==typeof e?e:function(e,t=2){let r=new Set;return JSON.stringify(e,(e,t)=>{if("object"==typeof t&&null!==t){if(r.has(t))return"[Circular *]";r.add(t)}else if("bigint"==typeof t)return t.toString();return t},t)}(e)),r=`+${Date.now()-q}ms`;q=Date.now(),globalThis.DEBUG_COLORS?i(c[n](p(s)),...t,c[n](r)):i(s,...t,r)}},{get:(e,r)=>t[r],set:(e,r,s)=>t[r]=s})},{get:(e,t)=>$[t],set:(e,t,r)=>$[t]=r});function V(e){return{ok:!0,value:e,map:t=>V(t(e)),flatMap:t=>t(e)}}function Y(e){return{ok:!1,error:e,map:()=>Y(e),flatMap:()=>Y(e)}}var H=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:z(e,t.queryRaw.bind(t)),executeRaw:z(e,t.executeRaw.bind(t)),commit:z(e,t.commit.bind(t)),rollback:z(e,t.rollback.bind(t))});function z(e,t){return async(...r)=>{try{return await t(...r)}catch(t){return Y({kind:"GenericJs",id:e.registerNewError(t)})}}}var K={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Text:7,Date:8,Time:9,DateTime:10,Json:11,Bytes:13,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,BytesArray:77,UuidArray:78},J=r(16726),{builtins:Z,getTypeParser:X}=a.gK,ee={BIT_ARRAY:1561,BOOL_ARRAY:1e3,BYTEA_ARRAY:1001,BPCHAR_ARRAY:1014,CHAR_ARRAY:1002,CIDR_ARRAY:651,DATE_ARRAY:1182,FLOAT4_ARRAY:1021,FLOAT8_ARRAY:1022,INET_ARRAY:1041,INT2_ARRAY:1005,INT4_ARRAY:1007,INT8_ARRAY:1016,JSONB_ARRAY:3807,JSON_ARRAY:199,MONEY_ARRAY:791,NUMERIC_ARRAY:1231,OID_ARRAY:1028,TEXT_ARRAY:1009,TIMESTAMP_ARRAY:1115,TIME_ARRAY:1183,UUID_ARRAY:2951,VARBIT_ARRAY:1563,VARCHAR_ARRAY:1015,XML_ARRAY:143},et=class e extends Error{constructor(t){super(),this.type=e.typeNames[t]||"Unknown",this.message=`Unsupported column type ${this.type}`}};function er(e){return t=>(0,J.q)(t,e)}function es(e){return e}function en(e){return e}function ei(e){return e}function eo(e){return e}function ea(e){return e.slice(1)}function el(e){return e}function ec(e){return Array.from(new Uint8Array(e))}et.typeNames={16:"bool",17:"bytea",18:"char",19:"name",20:"int8",21:"int2",22:"int2vector",23:"int4",24:"regproc",25:"text",26:"oid",27:"tid",28:"xid",29:"cid",30:"oidvector",32:"pg_ddl_command",71:"pg_type",75:"pg_attribute",81:"pg_proc",83:"pg_class",114:"json",142:"xml",194:"pg_node_tree",269:"table_am_handler",325:"index_am_handler",600:"point",601:"lseg",602:"path",603:"box",604:"polygon",628:"line",650:"cidr",700:"float4",701:"float8",705:"unknown",718:"circle",774:"macaddr8",790:"money",829:"macaddr",869:"inet",1033:"aclitem",1042:"bpchar",1043:"varchar",1082:"date",1083:"time",1114:"timestamp",1184:"timestamptz",1186:"interval",1266:"timetz",1560:"bit",1562:"varbit",1700:"numeric",1790:"refcursor",2202:"regprocedure",2203:"regoper",2204:"regoperator",2205:"regclass",2206:"regtype",2249:"record",2275:"cstring",2276:"any",2277:"anyarray",2278:"void",2279:"trigger",2280:"language_handler",2281:"internal",2283:"anyelement",2287:"_record",2776:"anynonarray",2950:"uuid",2970:"txid_snapshot",3115:"fdw_handler",3220:"pg_lsn",3310:"tsm_handler",3361:"pg_ndistinct",3402:"pg_dependencies",3500:"anyenum",3614:"tsvector",3615:"tsquery",3642:"gtsvector",3734:"regconfig",3769:"regdictionary",3802:"jsonb",3831:"anyrange",3838:"event_trigger",3904:"int4range",3906:"numrange",3908:"tsrange",3910:"tstzrange",3912:"daterange",3926:"int8range",4072:"jsonpath",4089:"regnamespace",4096:"regrole",4191:"regcollation",4451:"int4multirange",4532:"nummultirange",4533:"tsmultirange",4534:"tstzmultirange",4535:"datemultirange",4536:"int8multirange",4537:"anymultirange",4538:"anycompatiblemultirange",4600:"pg_brin_bloom_summary",4601:"pg_brin_minmax_multi_summary",5017:"pg_mcv_list",5038:"pg_snapshot",5069:"xid8",5077:"anycompatible",5078:"anycompatiblearray",5079:"anycompatiblenonarray",5080:"anycompatiblerange"};var eh=X(Z.BYTEA),eu=X(ee.BYTEA_ARRAY);function ef(e){return e}var ed={[Z.NUMERIC]:es,[ee.NUMERIC_ARRAY]:er(es),[Z.TIME]:eo,[ee.TIME_ARRAY]:er(eo),[Z.TIMETZ]:function(e){return e.split("+")[0]},[Z.DATE]:en,[ee.DATE_ARRAY]:er(en),[Z.TIMESTAMP]:ei,[ee.TIMESTAMP_ARRAY]:er(ei),[Z.TIMESTAMPTZ]:function(e){return e.split("+")[0]},[Z.MONEY]:ea,[ee.MONEY_ARRAY]:er(ea),[Z.JSON]:el,[Z.JSONB]:el,[Z.BYTEA]:function(e){return ec(eh(e))},[ee.BYTEA_ARRAY]:function(e){return eu(e).map(e=>e?ec(e):null)},[ee.BIT_ARRAY]:er(ef),[ee.VARBIT_ARRAY]:er(ef),[ee.XML_ARRAY]:er(function(e){return e})};function ep(e){for(let t=0;t<e.length;t++){let r=e[t];if(Array.isArray(r))for(let e=0;e<r.length;e++){let t=r[e];ArrayBuffer.isView(t)&&(r[e]=Buffer.from(t.buffer,t.byteOffset,t.byteLength))}}return e}var ey=G("prisma:driver-adapter:neon"),em=class{constructor(){this.provider="postgres",this.adapterName="@prisma/adapter-neon"}async queryRaw(e){ey("[js::query_raw] %O",e);let t=await this.performIO(e);if(!t.ok)return Y(t.error);let{fields:r,rows:s}=t.value,n=r.map(e=>e.name),i=[];try{i=r.map(e=>(function(e){switch(e){case Z.INT2:case Z.INT4:return K.Int32;case Z.INT8:return K.Int64;case Z.FLOAT4:return K.Float;case Z.FLOAT8:return K.Double;case Z.BOOL:return K.Boolean;case Z.DATE:return K.Date;case Z.TIME:case Z.TIMETZ:return K.Time;case Z.TIMESTAMP:case Z.TIMESTAMPTZ:return K.DateTime;case Z.NUMERIC:case Z.MONEY:return K.Numeric;case Z.JSON:case Z.JSONB:return K.Json;case Z.UUID:return K.Uuid;case Z.OID:return K.Int64;case Z.BPCHAR:case Z.TEXT:case Z.VARCHAR:case Z.BIT:case Z.VARBIT:case Z.INET:case Z.CIDR:case Z.XML:return K.Text;case Z.BYTEA:return K.Bytes;case ee.INT2_ARRAY:case ee.INT4_ARRAY:return K.Int32Array;case ee.FLOAT4_ARRAY:return K.FloatArray;case ee.FLOAT8_ARRAY:return K.DoubleArray;case ee.NUMERIC_ARRAY:case ee.MONEY_ARRAY:return K.NumericArray;case ee.BOOL_ARRAY:return K.BooleanArray;case ee.CHAR_ARRAY:return K.CharacterArray;case ee.BPCHAR_ARRAY:case ee.TEXT_ARRAY:case ee.VARCHAR_ARRAY:case ee.VARBIT_ARRAY:case ee.BIT_ARRAY:case ee.INET_ARRAY:case ee.CIDR_ARRAY:case ee.XML_ARRAY:return K.TextArray;case ee.DATE_ARRAY:return K.DateArray;case ee.TIME_ARRAY:return K.TimeArray;case ee.TIMESTAMP_ARRAY:return K.DateTimeArray;case ee.JSON_ARRAY:case ee.JSONB_ARRAY:return K.JsonArray;case ee.BYTEA_ARRAY:return K.BytesArray;case ee.UUID_ARRAY:return K.UuidArray;case ee.INT8_ARRAY:case ee.OID_ARRAY:return K.Int64Array;default:if(e>=1e4)return K.Text;throw new et(e)}})(e.dataTypeID))}catch(e){if(e instanceof et)return Y({kind:"UnsupportedNativeDataType",type:e.type});throw e}return V({columnNames:n,columnTypes:i,rows:s})}async executeRaw(e){return ey("[js::execute_raw] %O",e),(await this.performIO(e)).map(e=>e.rowCount??0)}},eg=class extends em{constructor(e){super(),this.client=e}async performIO(e){let{sql:t,args:r}=e;try{let e=await this.client.query({text:t,values:ep(r),rowMode:"array",types:{getTypeParser:(e,t)=>"text"===t&&ed[e]?ed[e]:a.gK.getTypeParser(e,t)}},ep(r));return V(e)}catch(e){if(ey("Error in performIO: %O",e),e&&"string"==typeof e.code&&"string"==typeof e.severity&&"string"==typeof e.message)return Y({kind:"postgres",code:e.code,severity:e.severity,message:e.message,detail:e.detail,column:e.column,hint:e.hint});throw e}}},e_=class extends eg{constructor(e,t){super(e),this.options=t}async commit(){return ey("[js::commit]"),this.client.release(),Promise.resolve(V(void 0))}async rollback(){return ey("[js::rollback]"),this.client.release(),Promise.resolve(V(void 0))}},eb=class extends eg{constructor(e){super(e),this.conn=e}async startTransaction(){let e={usePhantomQuery:!1};return ey("%s options: %O","[js::startTransaction]",e),V(new e_(this.conn,e))}},ev=class extends eg{constructor(e,t){if(!(e instanceof a.bC))throw TypeError(`PrismaNeon must be initialized with an instance of Pool:
import { Pool } from '@neondatabase/serverless'
const pool = new Pool({ connectionString: url })
const adapter = new PrismaNeon(pool)
`);super(e),this.options=t,this.isRunning=!0}getConnectionInfo(){return V({schemaName:this.options?.schema})}async transactionContext(){return V(new eb(await this.client.connect()))}async close(){return this.isRunning&&(await this.client.end(),this.isRunning=!1),V(void 0)}}},76015:(e,t,r)=>{"use strict";let s=r(94735),n=r(55591),i=r(81630),o=r(91645),a=r(34631),{randomBytes:l,createHash:c}=r(55511),{Duplex:h,Readable:u}=r(27910),{URL:f}=r(79551),d=r(68622),p=r(22543),y=r(72643),{isBlob:m}=r(89577),{BINARY_TYPES:g,EMPTY_BUFFER:_,GUID:b,kForOnEventAttribute:v,kListener:w,kStatusCode:S,kWebSocket:E,NOOP:x}=r(66385),{EventTarget:{addEventListener:A,removeEventListener:R}}=r(38280),{format:T,parse:C}=r(54649),{toBuffer:I}=r(10757),k=Symbol("kAborted"),L=[8,13],P=["CONNECTING","OPEN","CLOSING","CLOSED"],O=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class B extends s{constructor(e,t,r){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=_,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=B.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(r=t,t=[]):t=[t]),function e(t,r,s,o){let a,h,u,p,y={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:L[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=y.autoPong,!L.includes(y.protocolVersion))throw RangeError(`Unsupported protocol version: ${y.protocolVersion} (supported versions: ${L.join(", ")})`);if(r instanceof f)a=r;else try{a=new f(r)}catch(e){throw SyntaxError(`Invalid URL: ${r}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let m="wss:"===a.protocol,g="ws+unix:"===a.protocol;if("ws:"===a.protocol||m||g?g&&!a.pathname?h="The URL's pathname is empty":a.hash&&(h="The URL contains a fragment identifier"):h='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',h){let e=SyntaxError(h);if(0!==t._redirects)return void M(t,e);throw e}let _=m?443:80,v=l(16).toString("base64"),w=m?n.request:i.request,S=new Set;if(y.createConnection=y.createConnection||(m?D:N),y.defaultPort=y.defaultPort||_,y.port=a.port||_,y.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,y.headers={...y.headers,"Sec-WebSocket-Version":y.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},y.path=a.pathname+a.search,y.timeout=y.handshakeTimeout,y.perMessageDeflate&&(u=new d(!0!==y.perMessageDeflate?y.perMessageDeflate:{},!1,y.maxPayload),y.headers["Sec-WebSocket-Extensions"]=T({[d.extensionName]:u.offer()})),s.length){for(let e of s){if("string"!=typeof e||!O.test(e)||S.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");S.add(e)}y.headers["Sec-WebSocket-Protocol"]=s.join(",")}if(y.origin&&(y.protocolVersion<13?y.headers["Sec-WebSocket-Origin"]=y.origin:y.headers.Origin=y.origin),(a.username||a.password)&&(y.auth=`${a.username}:${a.password}`),g){let e=y.path.split(":");y.socketPath=e[0],y.path=e[1]}if(y.followRedirects){if(0===t._redirects){t._originalIpc=g,t._originalSecure=m,t._originalHostOrSocketPath=g?y.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,r]of Object.entries(e))o.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=g?!!t._originalIpc&&y.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete y.headers.authorization,delete y.headers.cookie,e||delete y.headers.host,y.auth=void 0)}y.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(y.auth).toString("base64")),p=t._req=w(y),t._redirects&&t.emit("redirect",t.url,p)}else p=t._req=w(y);y.timeout&&p.on("timeout",()=>{U(t,p,"Opening handshake has timed out")}),p.on("error",e=>{null===p||p[k]||(p=t._req=null,M(t,e))}),p.on("response",n=>{let i=n.headers.location,a=n.statusCode;if(i&&y.followRedirects&&a>=300&&a<400){let n;if(++t._redirects>y.maxRedirects)return void U(t,p,"Maximum redirects exceeded");p.abort();try{n=new f(i,r)}catch(e){M(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,n,s,o)}else t.emit("unexpected-response",p,n)||U(t,p,`Unexpected server response: ${n.statusCode}`)}),p.on("upgrade",(e,r,s)=>{let n;if(t.emit("upgrade",e),t.readyState!==B.CONNECTING)return;p=t._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase())return void U(t,r,"Invalid Upgrade header");let o=c("sha1").update(v+b).digest("base64");if(e.headers["sec-websocket-accept"]!==o)return void U(t,r,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?S.size?S.has(a)||(n="Server sent an invalid subprotocol"):n="Server sent a subprotocol but none was requested":S.size&&(n="Server sent no subprotocol"),n)return void U(t,r,n);a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!u)return void U(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=C(l)}catch(e){U(t,r,"Invalid Sec-WebSocket-Extensions header");return}let s=Object.keys(e);if(1!==s.length||s[0]!==d.extensionName)return void U(t,r,"Server indicated an extension that was not requested");try{u.accept(e[d.extensionName])}catch(e){U(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[d.extensionName]=u}t.setSocket(r,s,{allowSynchronousEvents:y.allowSynchronousEvents,generateMask:y.generateMask,maxPayload:y.maxPayload,skipUTF8Validation:y.skipUTF8Validation})}),y.finishRequest?y.finishRequest(p,t):p.end()}(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let s=new p({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation}),n=new y(e,this._extensions,r.generateMask);this._receiver=s,this._sender=n,this._socket=e,s[E]=this,n[E]=this,e[E]=this,s.on("conclude",j),s.on("drain",q),s.on("error",Q),s.on("message",$),s.on("ping",G),s.on("pong",V),n.onerror=H,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",K),e.on("data",J),e.on("end",Z),e.on("error",X),this._readyState=B.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=B.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[d.extensionName]&&this._extensions[d.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=B.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==B.CLOSED){if(this.readyState===B.CONNECTING)return void U(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===B.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=B.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),z(this)}}pause(){this.readyState!==B.CONNECTING&&this.readyState!==B.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.ping(e||_,t,r)}pong(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.pong(e||_,t,r)}resume(){this.readyState!==B.CONNECTING&&this.readyState!==B.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);let s={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[d.extensionName]||(s.compress=!1),this._sender.send(e||_,s,r)}terminate(){if(this.readyState!==B.CLOSED){if(this.readyState===B.CONNECTING)return void U(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=B.CLOSING,this._socket.destroy())}}}function M(e,t){e._readyState=B.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function N(e){return e.path=e.socketPath,o.connect(e)}function D(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function U(e,t,r){e._readyState=B.CLOSING;let s=Error(r);Error.captureStackTrace(s,U),t.setHeader?(t[k]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(M,e,s)):(t.destroy(s),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function F(e,t,r){if(t){let r=m(t)?t.size:I(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${P[e.readyState]})`);process.nextTick(r,t)}}function j(e,t){let r=this[E];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[E]&&(r._socket.removeListener("data",J),process.nextTick(Y,r._socket),1005===e?r.close():r.close(e,t))}function q(){let e=this[E];e.isPaused||e._socket.resume()}function Q(e){let t=this[E];void 0!==t._socket[E]&&(t._socket.removeListener("data",J),process.nextTick(Y,t._socket),t.close(e[S])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function W(){this[E].emitClose()}function $(e,t){this[E].emit("message",e,t)}function G(e){let t=this[E];t._autoPong&&t.pong(e,!this._isServer,x),t.emit("ping",e)}function V(e){this[E].emit("pong",e)}function Y(e){e.resume()}function H(e){let t=this[E];t.readyState!==B.CLOSED&&(t.readyState===B.OPEN&&(t._readyState=B.CLOSING,z(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function z(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function K(){let e,t=this[E];this.removeListener("close",K),this.removeListener("data",J),this.removeListener("end",Z),t._readyState=B.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[E]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",W),t._receiver.on("finish",W))}function J(e){this[E]._receiver.write(e)||this.pause()}function Z(){let e=this[E];e._readyState=B.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[E];this.removeListener("error",X),this.on("error",x),e&&(e._readyState=B.CLOSING,this.destroy())}Object.defineProperty(B,"CONNECTING",{enumerable:!0,value:P.indexOf("CONNECTING")}),Object.defineProperty(B.prototype,"CONNECTING",{enumerable:!0,value:P.indexOf("CONNECTING")}),Object.defineProperty(B,"OPEN",{enumerable:!0,value:P.indexOf("OPEN")}),Object.defineProperty(B.prototype,"OPEN",{enumerable:!0,value:P.indexOf("OPEN")}),Object.defineProperty(B,"CLOSING",{enumerable:!0,value:P.indexOf("CLOSING")}),Object.defineProperty(B.prototype,"CLOSING",{enumerable:!0,value:P.indexOf("CLOSING")}),Object.defineProperty(B,"CLOSED",{enumerable:!0,value:P.indexOf("CLOSED")}),Object.defineProperty(B.prototype,"CLOSED",{enumerable:!0,value:P.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(B.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(B.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[v])return t[w];return null},set(t){for(let t of this.listeners(e))if(t[v]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[v]:!0})}})}),B.prototype.addEventListener=A,B.prototype.removeEventListener=R,e.exports=B},78513:(e,t,r)=>{let s=require;"function"==typeof s.addon?e.exports=s.addon.bind(s):e.exports=r(87605)},82102:(e,t,r)=>{"use strict";try{e.exports=r(78513)(__dirname)}catch(t){e.exports=r(71518)}},87605:(e,t,r)=>{var s=r(29021),n=r(33873),i=r(21820),o=require,a=process.config&&process.config.variables||{},l=!!process.env.PREBUILDS_ONLY,c=process.versions.modules,h=process.versions&&process.versions.electron||process.env.ELECTRON_RUN_AS_NODE||"undefined"!=typeof window&&window.process&&"renderer"===window.process.type?"electron":process.versions&&process.versions.nw?"node-webkit":"node",u=process.env.npm_config_arch||i.arch(),f=process.env.npm_config_platform||i.platform(),d=process.env.LIBC||("linux"===f&&s.existsSync("/etc/alpine-release")?"musl":"glibc"),p=process.env.ARM_VERSION||("arm64"===u?"8":a.arm_version)||"",y=(process.versions.uv||"").split(".")[0];function m(e){return o(m.resolve(e))}function g(e){try{return s.readdirSync(e)}catch(e){return[]}}function _(e,t){var r=g(e).filter(t);return r[0]&&n.join(e,r[0])}function b(e){return/\.node$/.test(e)}function v(e){var t=e.split("-");if(2===t.length){var r=t[0],s=t[1].split("+");if(r&&s.length&&s.every(Boolean))return{name:e,platform:r,architectures:s}}}function w(e,t){return function(r){return null!=r&&r.platform===e&&r.architectures.includes(t)}}function S(e,t){return e.architectures.length-t.architectures.length}function E(e){var t=e.split("."),r=t.pop(),s={file:e,specificity:0};if("node"===r){for(var n=0;n<t.length;n++){var i=t[n];if("node"===i||"electron"===i||"node-webkit"===i)s.runtime=i;else if("napi"===i)s.napi=!0;else if("abi"===i.slice(0,3))s.abi=i.slice(3);else if("uv"===i.slice(0,2))s.uv=i.slice(2);else if("armv"===i.slice(0,4))s.armv=i.slice(4);else{if("glibc"!==i&&"musl"!==i)continue;s.libc=i}s.specificity++}return s}}function x(e,t){return function(r){var s;return null!=r&&(!r.runtime||r.runtime===e||!!("node"===(s=r).runtime&&s.napi))&&(!r.abi||r.abi===t||!!r.napi)&&(!r.uv||r.uv===y)&&(!r.armv||r.armv===p)&&(!r.libc||r.libc===d)&&!0}}function A(e){return function(t,r){return t.runtime!==r.runtime?t.runtime===e?-1:1:t.abi!==r.abi?t.abi?-1:1:t.specificity!==r.specificity?t.specificity>r.specificity?-1:1:0}}e.exports=m,m.resolve=m.path=function(e){e=n.resolve(e||".");try{var t=o(n.join(e,"package.json")).name.toUpperCase().replace(/-/g,"_");process.env[t+"_PREBUILD"]&&(e=process.env[t+"_PREBUILD"])}catch(e){}if(!l){var r=_(n.join(e,"build/Release"),b);if(r)return r;var s=_(n.join(e,"build/Debug"),b);if(s)return s}var i=m(e);if(i)return i;var a=m(n.dirname(process.execPath));if(a)return a;throw Error("No native build was found for "+["platform="+f,"arch="+u,"runtime="+h,"abi="+c,"uv="+y,p?"armv="+p:"","libc="+d,"node="+process.versions.node,process.versions.electron?"electron="+process.versions.electron:"","webpack=true"].filter(Boolean).join(" ")+"\n    loaded from: "+e+"\n");function m(e){var t=g(n.join(e,"prebuilds")).map(v).filter(w(f,u)).sort(S)[0];if(t){var r=n.join(e,"prebuilds",t.name),s=g(r).map(E).filter(x(h,c)).sort(A(h))[0];if(s)return n.join(r,s.file)}}},m.parseTags=E,m.matchTags=x,m.compareTags=A,m.parseTuple=v,m.matchTuple=w,m.compareTuples=S},89577:(e,t,r)=>{"use strict";let{isUtf8:s}=r(79428),{hasBlob:n}=r(66385);function i(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(e.exports={isBlob:function(e){return n&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},s)e.exports.isValidUTF8=function(e){return e.length<24?i(e):s(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=r(59570);e.exports.isValidUTF8=function(e){return e.length<32?i(e):t(e)}}catch(e){}}};