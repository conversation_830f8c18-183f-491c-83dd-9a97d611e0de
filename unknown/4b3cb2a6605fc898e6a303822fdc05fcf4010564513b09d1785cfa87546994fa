"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2068],{25451:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(45707).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},45707:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(50628);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:p,...f}=e;return(0,n.createElement)("svg",{ref:t,...d,width:o,height:o,stroke:r,strokeWidth:l?24*Number(a)/Number(o):a,className:i("lucide",u),...!c&&!s(f)&&{"aria-hidden":"true"},...f},[...p.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:s,...d}=r;return(0,n.createElement)(u,{ref:a,iconNode:t,className:i("lucide-".concat(o(l(e))),"lucide-".concat(e),s),...d})});return r.displayName=l(e),r}},50817:(e,t,r)=>{r.d(t,{UC:()=>ee,VY:()=>er,ZL:()=>Q,bL:()=>Y,bm:()=>en,hE:()=>et,hJ:()=>X});var n=r(50628),o=r(13859),a=r(98064),l=r(48733),i=r(29823),s=r(17691),d=r(79447),u=r(9665),c=r(4844),p=r(64714),f=r(64826),g=r(16279),h=r(10345),m=r(11712),v=r(89840),y=r(6024),w="Dialog",[x,C]=(0,l.A)(w),[b,D]=x(w),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:w});return(0,y.jsx)(b,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};j.displayName=w;var R="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(R,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":z(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})}).displayName=R;var N="DialogPortal",[k,A]=x(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=D(N,t);return(0,y.jsx)(k,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var I="DialogOverlay",O=n.forwardRef((e,t)=>{let r=A(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(I,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(F,{...o,ref:t})}):null});O.displayName=I;var _=(0,v.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(I,r);return(0,y.jsx)(h.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",L=n.forwardRef((e,t)=>{let r=A(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(M,{...o,ref:t}):(0,y.jsx)(W,{...o,ref:t})})});L.displayName=P;var M=n.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),W=n.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=D(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":z(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:c.titleId}),(0,y.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(G,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});T.displayName=G;var q="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(q,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});Z.displayName=q;var S="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(S,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function z(e){return e?"open":"closed"}U.displayName=S;var V="DialogTitleWarning",[$,H]=(0,l.q)(V,{contentName:P,titleName:G,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=H(V),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:r}=e,o=H("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Y=j,Q=E,X=O,ee=L,et=T,er=Z,en=U},52482:(e,t,r)=>{r.d(t,{b:()=>d});var n=r(50628),o=r(64826),a=r(6024),l="horizontal",i=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=l,...d}=e,u=(r=s,i.includes(r))?s:l;return(0,a.jsx)(o.sG.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...d,ref:t})});s.displayName="Separator";var d=s},65581:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(45707).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}}]);