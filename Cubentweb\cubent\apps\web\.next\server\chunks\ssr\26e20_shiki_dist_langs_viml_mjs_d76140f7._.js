module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/langs/viml.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>viml)
});
const lang = Object.freeze({
    "displayName": "V<PERSON>ript",
    "name": "viml",
    "patterns": [
        {
            "include": "#comment"
        },
        {
            "include": "#constant"
        },
        {
            "include": "#entity"
        },
        {
            "include": "#keyword"
        },
        {
            "include": "#punctuation"
        },
        {
            "include": "#storage"
        },
        {
            "include": "#strings"
        },
        {
            "include": "#support"
        },
        {
            "include": "#variable"
        },
        {
            "include": "#syntax"
        },
        {
            "include": "#commands"
        },
        {
            "include": "#option"
        },
        {
            "include": "#map"
        }
    ],
    "repository": {
        "commands": {
            "patterns": [
                {
                    "match": "\\bcom(\\s|!)",
                    "name": "storage.other.command.viml"
                },
                {
                    "match": "\\bau(\\s|!)",
                    "name": "storage.other.command.viml"
                },
                {
                    "match": "-bang",
                    "name": "storage.other.command.bang.viml"
                },
                {
                    "match": "-nargs=[*+0-9]+",
                    "name": "storage.other.command.args.viml"
                },
                {
                    "match": "-complete=\\S+",
                    "name": "storage.other.command.completion.viml"
                },
                {
                    "begin": "(aug(roup)?)",
                    "end": "(augroup\\sEND|$)",
                    "name": "support.function.augroup.viml"
                }
            ]
        },
        "comment": {
            "patterns": [
                {
                    "begin": '((\\s+)?""")',
                    "end": '^(?!")',
                    "name": "comment.block.documentation.viml"
                },
                {
                    "match": '^"\\svim:.*',
                    "name": "comment.block.modeline.viml"
                },
                {
                    "begin": '(\\s+"\\s+)(?!")',
                    "end": "$",
                    "name": "comment.line.viml",
                    "patterns": [
                        {
                            "match": "\\{\\{\\{\\d?$",
                            "name": "comment.line.foldmarker.viml"
                        },
                        {
                            "match": "\\}\\}\\}\\d?",
                            "name": "comment.line.foldmarker.viml"
                        }
                    ]
                },
                {
                    "begin": '^(\\s+)?"',
                    "end": "$",
                    "name": "comment.line.viml",
                    "patterns": [
                        {
                            "match": "\\{\\{\\{\\d?$",
                            "name": "comment.line.foldmarker.viml"
                        },
                        {
                            "match": "\\}\\}\\}\\d?",
                            "name": "comment.line.foldmarker.viml"
                        }
                    ]
                }
            ]
        },
        "constant": {
            "patterns": [
                {
                    "match": "\\b(true|false)\\b",
                    "name": "constant.language.boolean.viml"
                },
                {
                    "match": "\\b(\\d+)\\b",
                    "name": "constant.numeric.viml"
                }
            ]
        },
        "entity": {
            "patterns": [
                {
                    "match": "(([absg]:)?[a-zA-Z0-9_#.]{2,})\\b(?=\\()",
                    "name": "entity.name.function.viml"
                }
            ]
        },
        "keyword": {
            "patterns": [
                {
                    "match": "\\b(if|while|for|return|au(g|group)|else(if|)?|do|in)\\b",
                    "name": "keyword.control.viml"
                },
                {
                    "match": "\\b(end|endif|endfor|endwhile)\\s|$",
                    "name": "keyword.control.viml"
                },
                {
                    "match": "\\b(break|continue|try|catch|endtry|finally|finish|throw|range)\\b",
                    "name": "keyword.control.viml"
                },
                {
                    "match": "\\b(fun|func|function|endfunction|endfunc)\\b",
                    "name": "keyword.function.viml"
                },
                {
                    "match": "\\b(normal|silent)\\b",
                    "name": "keyword.other.viml"
                },
                {
                    "include": "#operators"
                }
            ]
        },
        "map": {
            "patterns": [
                {
                    "begin": "(<)",
                    "beginCaptures": {
                        "1": {
                            "name": "punctuation.definition.map.viml"
                        }
                    },
                    "end": "(>|\\s)",
                    "endCaptures": {
                        "1": {
                            "name": "punctuation.definition.map.viml"
                        }
                    },
                    "patterns": [
                        {
                            "match": "(?<=:\\s)(.+)",
                            "name": "constant.character.map.rhs.viml"
                        },
                        {
                            "match": "(?i:(bang|buffer|expr|nop|plug|sid|silent))",
                            "name": "constant.character.map.special.viml"
                        },
                        {
                            "match": "(?i:([adcms]-\\w))",
                            "name": "constant.character.map.key.viml"
                        },
                        {
                            "match": "(?i:(F\\d+))",
                            "name": "constant.character.map.key.fn.viml"
                        },
                        {
                            "match": "(?i:(bs|bar|cr|del|down|esc|left|right|space|tab|up|leader))",
                            "name": "constant.character.map.viml"
                        }
                    ]
                },
                {
                    "match": "(\\b([cinostvx]?(nore)?map)\\b)",
                    "name": "storage.type.map.viml"
                }
            ]
        },
        "operators": {
            "patterns": [
                {
                    "match": "([#+?!=~\\\\])",
                    "name": "keyword.operator.viml"
                },
                {
                    "match": " ([:\\-.]|[&|]{2})( |$)",
                    "name": "keyword.operator.viml"
                },
                {
                    "match": "([.]{3})",
                    "name": "keyword.operator.viml"
                },
                {
                    "match": "( [<>] )",
                    "name": "keyword.operator.viml"
                },
                {
                    "match": "(>=)",
                    "name": "keyword.operator.viml"
                }
            ]
        },
        "option": {
            "patterns": [
                {
                    "match": "&?\\b(al|aleph|anti|antialias|arab|arabic|arshape|arabicshape|ari|allowrevins|akm|altkeymap|ambw|ambiwidth|acd|autochdir|ai|autoindent|ar|autoread|aw|autowrite|awa|autowriteall|bg|background|bs|backspace|bk|backup|bkc|backupcopy|bdir|backupdir|bex|backupext|bsk|backupskip|bdlay|balloondelay|beval|ballooneval|bevalterm|balloonevalterm|bexpr|balloonexpr|bo|belloff|bin|binary|bomb|brk|breakat|bri|breakindent|briopt|breakindentopt|bsdir|browsedir|bh|bufhidden|bl|buflisted|bt|buftype|cmp|casemap|cd|cdpath|cedit|ccv|charconvert|cin|cindent|cink|cinkeys|cino|cinoptions|cinw|cinwords|cb|clipboard|ch|cmdheight|cwh|cmdwinheight|cc|colorcolumn|co|columns|com|comments|cms|commentstring|cp|compatible|cpt|complete|cocu|concealcursor|cole|conceallevel|cfu|completefunc|cot|completeopt|cf|confirm|ci|copyindent|cpo|cpoptions|cm|cryptmethod|cspc|cscopepathcomp|csprg|cscopeprg|csqf|cscopequickfix|csre|cscoperelative|cst|cscopetag|csto|cscopetagorder|csverb|cscopeverbose|crb|cursorbind|cuc|cursorcolumn|cul|cursorline|debug|def|define|deco|delcombine|dict|dictionary|diff|dex|diffexpr|dip|diffopt|dg|digraph|dir|directory|dy|display|ead|eadirection|ed|edcompatible|emo|emoji|enc|encoding|eol|endofline|ea|equalalways|ep|equalprg|eb|errorbells|ef|errorfile|efm|errorformat|ek|esckeys|ei|eventignore|et|expandtab|ex|exrc|fenc|fileencoding|fencs|fileencodings|ff|fileformat|ffs|fileformats|fic|fileignorecase|ft|filetype|fcs|fillchars|fixeol|fixendofline|fk|fkmap|fcl|foldclose|fdc|foldcolumn|fen|foldenable|fde|foldexpr|fdi|foldignore|fdl|foldlevel|fdls|foldlevelstart|fmr|foldmarker|fdm|foldmethod|fml|foldminlines|fdn|foldnestmax|fdo|foldopen|fdt|foldtext|fex|formatexpr|fo|formatoptions|flp|formatlistpat|fp|formatprg|fs|fsync|gd|gdefault|gfm|grepformat|gp|grepprg|gcr|guicursor|gfn|guifont|gfs|guifontset|gfw|guifontwide|ghr|guiheadroom|go|guioptions|guipty|gtl|guitablabel|gtt|guitabtooltip|hf|helpfile|hh|helpheight|hlg|helplang|hid|hidden|hl|highlight|hi|history|hk|hkmap|hkp|hkmapp|hls|hlsearch|icon|iconstring|ic|ignorecase|imaf|imactivatefunc|imak|imactivatekey|imc|imcmdline|imd|imdisable|imi|iminsert|ims|imsearch|imsf|imstatusfunc|imst|imstyle|inc|include|inex|includeexpr|is|incsearch|inde|indentexpr|indk|indentkeys|inf|infercase|im|insertmode|isf|isfname|isi|isident|isk|iskeyword|isp|isprint|js|joinspaces|key|kmp|keymap|km|keymodel|kp|keywordprg|lmap|langmap|lm|langmenu|lnr|langnoremap|lrm|langremap|ls|laststatus|lz|lazyredraw|lbr|linebreak|lines|lsp|linespace|lisp|lw|lispwords|list|lcs|listchars|lpl|loadplugins|luadll|macatsui|magic|mef|makeef|menc|makeencoding|mp|makeprg|mps|matchpairs|mat|matchtime|mco|maxcombine|mfd|maxfuncdepth|mmd|maxmapdepth|mm|maxmem|mmp|maxmempattern|mmt|maxmemtot|mis|menuitems|msm|mkspellmem|ml|modeline|mls|modelines|ma|modifiable|mod|modified|more|mouse|mousef|mousefocus|mh|mousehide|mousem|mousemodel|mouses|mouseshape|mouset|mousetime|mzschemedll|mzschemegcdll|mzq|mzquantum|nf|nrformats|nu|number|nuw|numberwidth|ofu|omnifunc|odev|opendevice|opfunc|operatorfunc|pp|packpath|para|paragraphs|paste|pt|pastetoggle|pex|patchexpr|pm|patchmode|pa|path|perldll|pi|preserveindent|pvh|previewheight|pvw|previewwindow|pdev|printdevice|penc|printencoding|pexpr|printexpr|pfn|printfont|pheader|printheader|pmbcs|printmbcharset|pmbfn|printmbfont|popt|printoptions|prompt|ph|pumheight|pythonthreedll|pythondll|pyx|pyxversion|qe|quoteescape|ro|readonly|rdt|redrawtime|re|regexpengine|rnu|relativenumber|remap|rop|renderoptions|report|rs|restorescreen|ri|revins|rl|rightleft|rlc|rightleftcmd|rubydll|ru|ruler|ruf|rulerformat|rtp|runtimepath|scr|scroll|scb|scrollbind|sj|scrolljump|so|scrolloff|sbo|scrollopt|sect|sections|secure|sel|selection|slm|selectmode|ssop|sessionoptions|sh|shell|shcf|shellcmdflag|sp|shellpipe|shq|shellquote|srr|shellredir|ssl|shellslash|stmp|shelltemp|st|shelltype|sxq|shellxquote|sxe|shellxescape|sr|shiftround|sw|shiftwidth|shm|shortmess|sn|shortname|sbr|showbreak|sc|showcmd|sft|showfulltag|sm|showmatch|smd|showmode|stal|showtabline|ss|sidescroll|siso|sidescrolloff|scl|signcolumn|scs|smartcase|si|smartindent|sta|smarttab|sts|softtabstop|spell|spc|spellcapcheck|spf|spellfile|spl|spelllang|sps|spellsuggest|sb|splitbelow|spr|splitright|sol|startofline|stl|statusline|su|suffixes|sua|suffixesadd|swf|swapfile|sws|swapsync|swb|switchbuf|smc|synmaxcol|syn|syntax|tal|tabline|tpm|tabpagemax|ts|tabstop|tbs|tagbsearch|tc|tagcase|tl|taglength|tr|tagrelative|tag|tags|tgst|tagstack|tcldll|term|tbidi|termbidi|tenc|termencoding|tgc|termguicolors|tk|termkey|tms|termsize|terse|ta|textauto|tx|textmode|tw|textwidth|tsr|thesaurus|top|tildeop|to|timeout|tm|timeoutlen|title|titlelen|titleold|titlestring|tb|toolbar|tbis|toolbariconsize|ttimeout|ttm|ttimeoutlen|tbi|ttybuiltin|tf|ttyfast|ttym|ttymouse|tsl|ttyscroll|tty|ttytype|udir|undodir|udf|undofile|ul|undolevels|ur|undoreload|uc|updatecount|ut|updatetime|vbs|verbose|vfile|verbosefile|vdir|viewdir|vop|viewoptions|vi|viminfo|vif|viminfofile|ve|virtualedit|vb|visualbell|warn|wiv|weirdinvert|ww|whichwrap|wc|wildchar|wcm|wildcharm|wig|wildignore|wic|wildignorecase|wmnu|wildmenu|wim|wildmode|wop|wildoptions|wak|winaltkeys|wi|window|wh|winheight|wfh|winfixheight|wfw|winfixwidth|wmh|winminheight|wmw|winminwidth|winptydll|wiw|winwidth|wrap|wm|wrapmargin|ws|wrapscan|write|wa|writeany|wb|writebackup|wd|writedelay)\\b",
                    "name": "support.type.option.viml"
                },
                {
                    "match": "&?\\b(aleph|allowrevins|altkeymap|ambiwidth|autochdir|arabic|arabicshape|autoindent|autoread|autowrite|autowriteall|background|backspace|backup|backupcopy|backupdir|backupext|backupskip|balloondelay|ballooneval|balloonexpr|belloff|binary|bomb|breakat|breakindent|breakindentopt|browsedir|bufhidden|buflisted|buftype|casemap|cdpath|cedit|charconvert|cindent|cinkeys|cinoptions|cinwords|clipboard|cmdheight|cmdwinheight|colorcolumn|columns|comments|commentstring|complete|completefunc|completeopt|concealcursor|conceallevel|confirm|copyindent|cpoptions|cscopepathcomp|cscopeprg|cscopequickfix|cscoperelative|cscopetag|cscopetagorder|cscopeverbose|cursorbind|cursorcolumn|cursorline|debug|define|delcombine|dictionary|diff|diffexpr|diffopt|digraph|directory|display|eadirection|encoding|endofline|equalalways|equalprg|errorbells|errorfile|errorformat|eventignore|expandtab|exrc|fileencoding|fileencodings|fileformat|fileformats|fileignorecase|filetype|fillchars|fixendofline|fkmap|foldclose|foldcolumn|foldenable|foldexpr|foldignore|foldlevel|foldlevelstart|foldmarker|foldmethod|foldminlines|foldnestmax|foldopen|foldtext|formatexpr|formatlistpat|formatoptions|formatprg|fsync|gdefault|grepformat|grepprg|guicursor|guifont|guifontset|guifontwide|guioptions|guitablabel|guitabtooltip|helpfile|helpheight|helplang|hidden|hlsearch|history|hkmap|hkmapp|icon|iconstring|ignorecase|imcmdline|imdisable|iminsert|imsearch|include|includeexpr|incsearch|indentexpr|indentkeys|infercase|insertmode|isfname|isident|iskeyword|isprint|joinspaces|keymap|keymodel|keywordprg|langmap|langmenu|langremap|laststatus|lazyredraw|linebreak|lines|linespace|lisp|lispwords|list|listchars|loadplugins|magic|makeef|makeprg|matchpairs|matchtime|maxcombine|maxfuncdepth|maxmapdepth|maxmem|maxmempattern|maxmemtot|menuitems|mkspellmem|modeline|modelines|modifiable|modified|more|mouse|mousefocus|mousehide|mousemodel|mouseshape|mousetime|nrformats|number|numberwidth|omnifunc|opendevice|operatorfunc|packpath|paragraphs|paste|pastetoggle|patchexpr|patchmode|path|perldll|preserveindent|previewheight|previewwindow|printdevice|printencoding|printexpr|printfont|printheader|printmbcharset|printmbfont|printoptions|prompt|pumheight|pythondll|pythonthreedll|quoteescape|readonly|redrawtime|regexpengine|relativenumber|remap|report|revins|rightleft|rightleftcmd|rubydll|ruler|rulerformat|runtimepath|scroll|scrollbind|scrolljump|scrolloff|scrollopt|sections|secure|selection|selectmode|sessionoptions|shada|shell|shellcmdflag|shellpipe|shellquote|shellredir|shellslash|shelltemp|shellxescape|shellxquote|shiftround|shiftwidth|shortmess|showbreak|showcmd|showfulltag|showmatch|showmode|showtabline|sidescroll|sidescrolloff|signcolumn|smartcase|smartindent|smarttab|softtabstop|spell|spellcapcheck|spellfile|spelllang|spellsuggest|splitbelow|splitright|startofline|statusline|suffixes|suffixesadd|swapfile|switchbuf|synmaxcol|syntax|tabline|tabpagemax|tabstop|tagbsearch|tagcase|taglength|tagrelative|tags|tagstack|term|termbidi|terse|textwidth|thesaurus|tildeop|timeout|timeoutlen|title|titlelen|titleold|titlestring|ttimeout|ttimeoutlen|ttytype|undodir|undofile|undolevels|undoreload|updatecount|updatetime|verbose|verbosefile|viewdir|viewoptions|virtualedit|visualbell|warn|whichwrap|wildchar|wildcharm|wildignore|wildignorecase|wildmenu|wildmode|wildoptions|winaltkeys|window|winheight|winfixheight|winfixwidth|winminheight|winminwidth|winwidth|wrap|wrapmargin|wrapscan|write|writeany|writebackup|writedelay)\\b",
                    "name": "support.type.option.viml"
                },
                {
                    "match": "&?\\b(al|ari|akm|ambw|acd|arab|arshape|ai|ar|aw|awa|bg|bs|bk|bkc|bdir|bex|bsk|bdlay|beval|bexpr|bo|bin|bomb|brk|bri|briopt|bsdir|bh|bl|bt|cmp|cd|cedit|ccv|cin|cink|cino|cinw|cb|ch|cwh|cc|co|com|cms|cpt|cfu|cot|cocu|cole|cf|ci|cpo|cspc|csprg|csqf|csre|cst|csto|cpo|crb|cuc|cul|debug|def|deco|dict|diff|dex|dip|dg|dir|dy|ead|enc|eol|ea|ep|eb|ef|efm|ei|et|ex|fenc|fencs|ff|ffs|fic|ft|fcs|fixeol|fk|fcl|fdc|fen|fde|fdi|fdl|fdls|fmr|fdm|fml|fdn|fdo|fdt|fex|flp|fo|fp|fs|gd|gfm|gp|gcr|gfn|gfs|gfw|go|gtl|gtt|hf|hh|hlg|hid|hls|hi|hk|hkp|icon|iconstring|ic|imc|imd|imi|ims|inc|inex|is|inde|indk|inf|im|isf|isi|isk|isp|js|kmp|km|kp|lmap|lm|lrm|ls|lz|lbr|lines|lsp|lisp|lw|list|lcs|lpl|magic|mef|mp|mps|mat|mco|mfd|mmd|mm|mmp|mmt|mis|msm|ml|mls|ma|mod|more|mouse|mousef|mh|mousem|mouses|mouset|nf|nu|nuw|ofu|odev|opfunc|pp|para|paste|pt|pex|pm|pa|perldll|pi|pvh|pvw|pdev|penc|pexpr|pfn|pheader|pmbcs|pmbfn|popt|prompt|ph|pythondll|pythonthreedlll|qe|ro|rdt|re|rnu|remap|report|ri|rl|rlc|rubydll|ru|ruf|rtp|scr|scb|sj|so|sbo|sect|secure|sel|slm|ssop|sd|sh|shcf|sp|shq|srr|ssl|stmp|sxe|sxq|sr|sw|shm|sbr|sc|sft|sm|smd|stal|ss|siso|scl|scs|si|sta|sts|spell|spc|spf|spl|sps|sb|spr|sol|stl|su|sua|swf|swb|smc|syn|tal|tpm|ts|tbs|tc|tl|tr|tag|tgst|term|tbidi|terse|tw|tsr|top|to|tm|title|titlelen|titleold|titlestring|ttimeout|ttm|tty|udir|udf|ul|ur|uc|ut|vbs|vfile|vdir|vop|ve|vb|warn|ww|wc|wcm|wig|wic|wmnu|wim|wop|wak|wi|wh|wfh|wfw|wmh|wmw|wiw|wrap|wm|ws|write|wa|wb|wd)\\b",
                    "name": "support.type.option.shortname.viml"
                },
                {
                    "match": "\\b(noanti|noantialias|noarab|noarabic|noarshape|noarabicshape|noari|noallowrevins|noakm|noaltkeymap|noacd|noautochdir|noai|noautoindent|noar|noautoread|noaw|noautowrite|noawa|noautowriteall|nobk|nobackup|nobeval|noballooneval|nobevalterm|noballoonevalterm|nobin|nobinary|nobomb|nobri|nobreakindent|nobl|nobuflisted|nocin|nocindent|nocp|nocompatible|nocf|noconfirm|noci|nocopyindent|nocsre|nocscoperelative|nocst|nocscopetag|nocsverb|nocscopeverbose|nocrb|nocursorbind|nocuc|nocursorcolumn|nocul|nocursorline|nodeco|nodelcombine|nodiff|nodg|nodigraph|noed|noedcompatible|noemo|noemoji|noeol|noendofline|noea|noequalalways|noeb|noerrorbells|noek|noesckeys|noet|noexpandtab|noex|noexrc|nofic|nofileignorecase|nofixeol|nofixendofline|nofk|nofkmap|nofen|nofoldenable|nofs|nofsync|nogd|nogdefault|noguipty|nohid|nohidden|nohk|nohkmap|nohkp|nohkmapp|nohls|nohlsearch|noicon|noic|noignorecase|noimc|noimcmdline|noimd|noimdisable|nois|noincsearch|noinf|noinfercase|noim|noinsertmode|nojs|nojoinspaces|nolnr|nolangnoremap|nolrm|nolangremap|nolz|nolazyredraw|nolbr|nolinebreak|nolisp|nolist|nolpl|noloadplugins|nomacatsui|nomagic|noml|nomodeline|noma|nomodifiable|nomod|nomodified|nomore|nomousef|nomousefocus|nomh|nomousehide|nonu|nonumber|noodev|noopendevice|nopaste|nopi|nopreserveindent|nopvw|nopreviewwindow|noprompt|noro|noreadonly|nornu|norelativenumber|nors|norestorescreen|nori|norevins|norl|norightleft|noru|noruler|noscb|noscrollbind|nosecure|nossl|noshellslash|nostmp|noshelltemp|nosr|noshiftround|nosn|noshortname|nosc|noshowcmd|nosft|noshowfulltag|nosm|noshowmatch|nosmd|noshowmode|noscs|nosmartcase|nosi|nosmartindent|nosta|nosmarttab|nospell|nosb|nosplitbelow|nospr|nosplitright|nosol|nostartofline|noswf|noswapfile|notbs|notagbsearch|notr|notagrelative|notgst|notagstack|notbidi|notermbidi|notgc|notermguicolors|noterse|nota|notextauto|notx|notextmode|notop|notildeop|noto|notimeout|notitle|nottimeout|notbi|nottybuiltin|notf|nottyfast|noudf|noundofile|novb|novisualbell|nowarn|nowiv|noweirdinvert|nowic|nowildignorecase|nowmnu|nowildmenu|nowfh|nowinfixheight|nowfw|nowinfixwidth|nowrapscan|nowrap|nows|nowrite|nowa|nowriteany|nowb|nowritebackup)\\b",
                    "name": "support.type.option.off.viml"
                }
            ]
        },
        "punctuation": {
            "patterns": [
                {
                    "match": "([()])",
                    "name": "punctuation.parens.viml"
                },
                {
                    "match": "([,])",
                    "name": "punctuation.comma.viml"
                }
            ]
        },
        "storage": {
            "patterns": [
                {
                    "match": "\\b(call|let|unlet)\\b",
                    "name": "storage.viml"
                },
                {
                    "match": "\\b(abort|autocmd)\\b",
                    "name": "storage.viml"
                },
                {
                    "match": "\\b(set(l|local)?)\\b",
                    "name": "storage.viml"
                },
                {
                    "match": "\\b(com(mand)?)\\b",
                    "name": "storage.viml"
                },
                {
                    "match": "\\b(color(scheme)?)\\b",
                    "name": "storage.viml"
                },
                {
                    "match": "\\b(Plug|Plugin)\\b",
                    "name": "storage.plugin.viml"
                }
            ]
        },
        "strings": {
            "patterns": [
                {
                    "begin": '"',
                    "end": '("|$)',
                    "name": "string.quoted.double.viml",
                    "patterns": []
                },
                {
                    "begin": "'",
                    "end": "('|$)",
                    "name": "string.quoted.single.viml",
                    "patterns": []
                },
                {
                    "match": "/(\\\\\\\\|\\\\/|[^\\n/])*/",
                    "name": "string.regexp.viml"
                }
            ]
        },
        "support": {
            "patterns": [
                {
                    "match": "(add|call|delete|empty|extend|get|has|isdirectory|join|printf)(?=\\()",
                    "name": "support.function.viml"
                },
                {
                    "match": "\\b(echo(m|hl)?|exe(cute)?|redir|redraw|sleep|so(urce)?|wincmd|setf)\\b",
                    "name": "support.function.viml"
                },
                {
                    "match": "(v:(beval_col|beval_bufnr|beval_lnum|beval_text|beval_winnr|char|charconvert_from|charconvert_to|cmdarg|cmdbang|count|count1|ctype|dying|errmsg|exception|fcs_reason|fcs_choice|fname_in|fname_out|fname_new|fname_diff|folddashes|foldlevel|foldend|foldstart|insertmode|key|lang|lc_time|lnum|mouse_win|mouse_lnum|mouse_col|oldfiles|operator|prevcount|profiling|progname|register|scrollstart|servername|searchforward|shell_error|statusmsg|swapname|swapchoice|swapcommand|termresponse|this_session|throwpoint|val|version|warningmsg|windowid))",
                    "name": "support.type.builtin.vim-variable.viml"
                },
                {
                    "match": "(&(cpo|isk|omnifunc|paste|previewwindow|rtp|tags|term|wrap))",
                    "name": "support.type.builtin.viml"
                },
                {
                    "match": "(&(shell(cmdflag|redir)?))",
                    "name": "support.type.builtin.viml"
                },
                {
                    "match": "<args>",
                    "name": "support.variable.args.viml"
                },
                {
                    "match": "\\b(None|ErrorMsg|WarningMsg)\\b",
                    "name": "support.type.syntax.viml"
                },
                {
                    "match": "\\b(BufNewFile|BufReadPre|BufRead|BufReadPost|BufReadCmd|FileReadPre|FileReadPost|FileReadCmd|FilterReadPre|FilterReadPost|StdinReadPre|StdinReadPost|BufWrite|BufWritePre|BufWritePost|BufWriteCmd|FileWritePre|FileWritePost|FileWriteCmd|FileAppendPre|FileAppendPost|FileAppendCmd|FilterWritePre|FilterWritePost|BufAdd|BufCreate|BufDelete|BufWipeout|BufFilePre|BufFilePost|BufEnter|BufLeave|BufWinEnter|BufWinLeave|BufUnload|BufHidden|BufNew|SwapExists|TermOpen|TermClose|FileType|Syntax|OptionSet|VimEnter|GUIEnter|GUIFailed|TermResponse|QuitPre|VimLeavePre|VimLeave|DirChanged|FileChangedShell|FileChangedShellPost|FileChangedRO|ShellCmdPost|ShellFilterPost|CmdUndefined|FuncUndefined|SpellFileMissing|SourcePre|SourceCmd|VimResized|FocusGained|FocusLost|CursorHold|CursorHoldI|CursorMoved|CursorMovedI|WinNew|WinEnter|WinLeave|TabEnter|TabLeave|TabNew|TabNewEntered|TabClosed|CmdlineEnter|CmdlineLeave|CmdwinEnter|CmdwinLeave|InsertEnter|InsertChange|InsertLeave|InsertCharPre|TextYankPost|TextChanged|TextChangedI|ColorScheme|RemoteReply|QuickFixCmdPre|QuickFixCmdPost|SessionLoadPost|MenuPopup|CompleteDone|User)\\b",
                    "name": "support.type.event.viml"
                },
                {
                    "match": "\\b(Comment|Constant|String|Character|Number|Boolean|Float|Identifier|Function|Statement|Conditional|Repeat|Label|Operator|Keyword|Exception|PreProc|Include|Define|Macro|PreCondit|Type|StorageClass|Structure|Typedef|Special|SpecialChar|Tag|Delimiter|SpecialComment|Debug|Underlined|Ignore|Error|Todo)\\b",
                    "name": "support.type.syntax-group.viml"
                }
            ]
        },
        "syntax": {
            "patterns": [
                {
                    "match": "syn(tax)? case (ignore|match)",
                    "name": "keyword.control.syntax.viml"
                },
                {
                    "match": "syn(tax)? (clear|enable|include|off|on|manual|sync)",
                    "name": "keyword.control.syntax.viml"
                },
                {
                    "match": "\\b(contained|display|excludenl|fold|keepend|oneline|skipnl|skipwhite|transparent)\\b",
                    "name": "keyword.other.syntax.viml"
                },
                {
                    "match": "\\b(add|containedin|contains|matchgroup|nextgroup)=",
                    "name": "keyword.other.syntax.viml"
                },
                {
                    "captures": {
                        "1": {
                            "name": "keyword.other.syntax-range.viml"
                        },
                        "3": {
                            "name": "string.regexp.viml"
                        }
                    },
                    "match": "((start|skip|end)=)(\\+\\S+\\+\\s)?"
                },
                {
                    "captures": {
                        "0": {
                            "name": "support.type.syntax.viml"
                        },
                        "1": {
                            "name": "storage.syntax.viml"
                        },
                        "3": {
                            "name": "variable.other.syntax-scope.viml"
                        },
                        "4": {
                            "name": "storage.modifier.syntax.viml"
                        }
                    },
                    "match": "(syn|syntax)\\s+(cluster|keyword|match|region)(\\s+\\w+\\s+)(contained)?",
                    "patterns": []
                },
                {
                    "captures": {
                        "1": {
                            "name": "storage.highlight.viml"
                        },
                        "2": {
                            "name": "storage.modifier.syntax.viml"
                        },
                        "3": {
                            "name": "support.function.highlight.viml"
                        },
                        "4": {
                            "name": "variable.other.viml"
                        },
                        "5": {
                            "name": "variable.other.viml"
                        }
                    },
                    "match": "(hi|highlight)(?:\\s+)(def|default)(?:\\s+)(link)(?:\\s+)(\\w+)(?:\\s+)(\\w+)",
                    "patterns": []
                }
            ]
        },
        "variable": {
            "patterns": [
                {
                    "match": "https?://\\S+",
                    "name": "variable.other.link.viml"
                },
                {
                    "match": "(?<=\\()([a-zA-Z]+)(?=\\))",
                    "name": "variable.parameter.viml"
                },
                {
                    "match": "\\b([absgl]:[a-zA-Z0-9_.#]+)\\b(?!\\()",
                    "name": "variable.other.viml"
                }
            ]
        }
    },
    "scopeName": "source.viml",
    "aliases": [
        "vim",
        "vimscript"
    ]
});
var viml = [
    lang
];
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_langs_viml_mjs_d76140f7._.js.map