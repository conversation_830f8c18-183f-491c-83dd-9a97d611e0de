{"iconDefinitions": {"git": {"iconPath": "./../icons/git.svg"}, "yaml": {"iconPath": "./../icons/yaml.svg"}, "xml": {"iconPath": "./../icons/xml.svg"}, "matlab": {"iconPath": "./../icons/matlab.svg"}, "settings": {"iconPath": "./../icons/settings.svg"}, "shaderlab": {"iconPath": "./../icons/shaderlab.svg"}, "diff": {"iconPath": "./../icons/diff.svg"}, "json": {"iconPath": "./../icons/json.svg"}, "blink": {"iconPath": "./../icons/blink.svg"}, "java": {"iconPath": "./../icons/java.svg"}, "razor": {"iconPath": "./../icons/razor.svg"}, "python": {"iconPath": "./../icons/python.svg"}, "mojo": {"iconPath": "./../icons/mojo.svg"}, "javascript": {"iconPath": "./../icons/javascript.svg"}, "typescript": {"iconPath": "./../icons/typescript.svg"}, "scala": {"iconPath": "./../icons/scala.svg"}, "handlebars": {"iconPath": "./../icons/handlebars.svg"}, "perl": {"iconPath": "./../icons/perl.svg"}, "haxe": {"iconPath": "./../icons/haxe.svg"}, "puppet": {"iconPath": "./../icons/puppet.svg"}, "elixir": {"iconPath": "./../icons/elixir.svg"}, "livescript": {"iconPath": "./../icons/livescript.svg"}, "erlang": {"iconPath": "./../icons/erlang.svg"}, "twig": {"iconPath": "./../icons/twig.svg"}, "julia": {"iconPath": "./../icons/julia.svg"}, "elm": {"iconPath": "./../icons/elm.svg"}, "purescript": {"iconPath": "./../icons/purescript.svg"}, "stylus": {"iconPath": "./../icons/stylus.svg"}, "nunjucks": {"iconPath": "./../icons/nunjucks.svg"}, "pug": {"iconPath": "./../icons/pug.svg"}, "robot": {"iconPath": "./../icons/robot.svg"}, "sass": {"iconPath": "./../icons/sass.svg"}, "less": {"iconPath": "./../icons/less.svg"}, "css": {"iconPath": "./../icons/css.svg"}, "visualstudio": {"iconPath": "./../icons/visualstudio.svg"}, "angular": {"iconPath": "./../icons/angular.svg"}, "graphql": {"iconPath": "./../icons/graphql.svg"}, "solidity": {"iconPath": "./../icons/solidity.svg"}, "autoit": {"iconPath": "./../icons/autoit.svg"}, "haml": {"iconPath": "./../icons/haml.svg"}, "yang": {"iconPath": "./../icons/yang.svg"}, "terraform": {"iconPath": "./../icons/terraform.svg"}, "applescript": {"iconPath": "./../icons/applescript.svg"}, "cake": {"iconPath": "./../icons/cake.svg"}, "cucumber": {"iconPath": "./../icons/cucumber.svg"}, "nim": {"iconPath": "./../icons/nim.svg"}, "apiblueprint": {"iconPath": "./../icons/apiblueprint.svg"}, "riot": {"iconPath": "./../icons/riot.svg"}, "postcss": {"iconPath": "./../icons/postcss.svg"}, "coldfusion": {"iconPath": "./../icons/coldfusion.svg"}, "haskell": {"iconPath": "./../icons/haskell.svg"}, "dhall": {"iconPath": "./../icons/dhall.svg"}, "cabal": {"iconPath": "./../icons/cabal.svg"}, "nix": {"iconPath": "./../icons/nix.svg"}, "ruby": {"iconPath": "./../icons/ruby.svg"}, "slim": {"iconPath": "./../icons/slim.svg"}, "php": {"iconPath": "./../icons/php.svg"}, "php_elephant": {"iconPath": "./../icons/php_elephant.svg"}, "php_elephant_pink": {"iconPath": "./../icons/php_elephant_pink.svg"}, "hack": {"iconPath": "./../icons/hack.svg"}, "react": {"iconPath": "./../icons/react.svg"}, "mjml": {"iconPath": "./../icons/mjml.svg"}, "processing": {"iconPath": "./../icons/processing.svg"}, "hcl": {"iconPath": "./../icons/hcl.svg"}, "go": {"iconPath": "./../icons/go.svg"}, "go_gopher": {"iconPath": "./../icons/go_gopher.svg"}, "nodejs_alt": {"iconPath": "./../icons/nodejs_alt.svg"}, "django": {"iconPath": "./../icons/django.svg"}, "html": {"iconPath": "./../icons/html.svg"}, "godot": {"iconPath": "./../icons/godot.svg"}, "godot-assets": {"iconPath": "./../icons/godot-assets.svg"}, "vim": {"iconPath": "./../icons/vim.svg"}, "silverstripe": {"iconPath": "./../icons/silverstripe.svg"}, "prolog": {"iconPath": "./../icons/prolog.svg"}, "pawn": {"iconPath": "./../icons/pawn.svg"}, "reason": {"iconPath": "./../icons/reason.svg"}, "sml": {"iconPath": "./../icons/sml.svg"}, "tex": {"iconPath": "./../icons/tex.svg"}, "salesforce": {"iconPath": "./../icons/salesforce.svg"}, "sas": {"iconPath": "./../icons/sas.svg"}, "docker": {"iconPath": "./../icons/docker.svg"}, "table": {"iconPath": "./../icons/table.svg"}, "csharp": {"iconPath": "./../icons/csharp.svg"}, "console": {"iconPath": "./../icons/console.svg"}, "c": {"iconPath": "./../icons/c.svg"}, "cpp": {"iconPath": "./../icons/cpp.svg"}, "objective-c": {"iconPath": "./../icons/objective-c.svg"}, "objective-cpp": {"iconPath": "./../icons/objective-cpp.svg"}, "coffee": {"iconPath": "./../icons/coffee.svg"}, "fsharp": {"iconPath": "./../icons/fsharp.svg"}, "editorconfig": {"iconPath": "./../icons/editorconfig.svg"}, "clojure": {"iconPath": "./../icons/clojure.svg"}, "groovy": {"iconPath": "./../icons/groovy.svg"}, "markdown": {"iconPath": "./../icons/markdown.svg"}, "jinja": {"iconPath": "./../icons/jinja.svg"}, "proto": {"iconPath": "./../icons/proto.svg"}, "python-misc": {"iconPath": "./../icons/python-misc.svg"}, "vue": {"iconPath": "./../icons/vue.svg"}, "lua": {"iconPath": "./../icons/lua.svg"}, "lib": {"iconPath": "./../icons/lib.svg"}, "log": {"iconPath": "./../icons/log.svg"}, "jupyter": {"iconPath": "./../icons/jupyter.svg"}, "document": {"iconPath": "./../icons/document.svg"}, "pdf": {"iconPath": "./../icons/pdf.svg"}, "powershell": {"iconPath": "./../icons/powershell.svg"}, "r": {"iconPath": "./../icons/r.svg"}, "rust": {"iconPath": "./../icons/rust.svg"}, "database": {"iconPath": "./../icons/database.svg"}, "kusto": {"iconPath": "./../icons/kusto.svg"}, "lock": {"iconPath": "./../icons/lock.svg"}, "svg": {"iconPath": "./../icons/svg.svg"}, "swift": {"iconPath": "./../icons/swift.svg"}, "react_ts": {"iconPath": "./../icons/react_ts.svg"}, "search": {"iconPath": "./../icons/search.svg"}, "minecraft": {"iconPath": "./../icons/minecraft.svg"}, "rescript": {"iconPath": "./../icons/rescript.svg"}, "otne": {"iconPath": "./../icons/otne.svg"}, "twine": {"iconPath": "./../icons/twine.svg"}, "grain": {"iconPath": "./../icons/grain.svg"}, "lolcode": {"iconPath": "./../icons/lolcode.svg"}, "idris": {"iconPath": "./../icons/idris.svg"}, "chess": {"iconPath": "./../icons/chess.svg"}, "gemini": {"iconPath": "./../icons/gemini.svg"}, "vlang": {"iconPath": "./../icons/vlang.svg"}, "wolframlanguage": {"iconPath": "./../icons/wolframlanguage.svg"}, "shader": {"iconPath": "./../icons/shader.svg"}, "tree": {"iconPath": "./../icons/tree.svg"}, "svelte": {"iconPath": "./../icons/svelte.svg"}, "dart": {"iconPath": "./../icons/dart.svg"}, "cadence": {"iconPath": "./../icons/cadence.svg"}, "stylable": {"iconPath": "./../icons/stylable.svg"}, "hjson": {"iconPath": "./../icons/hjson.svg"}, "huff": {"iconPath": "./../icons/huff.svg"}, "concourse": {"iconPath": "./../icons/concourse.svg"}, "blink_light": {"iconPath": "./../icons/blink_light.svg"}, "jinja_light": {"iconPath": "./../icons/jinja_light.svg"}, "playwright": {"iconPath": "./../icons/playwright.svg"}, "sublime": {"iconPath": "./../icons/sublime.svg"}, "image": {"iconPath": "./../icons/image.svg"}, "routing": {"iconPath": "./../icons/routing.svg"}, "typescript-def": {"iconPath": "./../icons/typescript-def.svg"}, "markojs": {"iconPath": "./../icons/markojs.svg"}, "astro": {"iconPath": "./../icons/astro.svg"}, "vscode": {"iconPath": "./../icons/vscode.svg"}, "qsharp": {"iconPath": "./../icons/qsharp.svg"}, "zip": {"iconPath": "./../icons/zip.svg"}, "vala": {"iconPath": "./../icons/vala.svg"}, "zig": {"iconPath": "./../icons/zig.svg"}, "exe": {"iconPath": "./../icons/exe.svg"}, "hex": {"iconPath": "./../icons/hex.svg"}, "jar": {"iconPath": "./../icons/jar.svg"}, "javaclass": {"iconPath": "./../icons/javaclass.svg"}, "h": {"iconPath": "./../icons/h.svg"}, "hpp": {"iconPath": "./../icons/hpp.svg"}, "rc": {"iconPath": "./../icons/rc.svg"}, "go-mod": {"iconPath": "./../icons/go-mod.svg"}, "url": {"iconPath": "./../icons/url.svg"}, "gradle": {"iconPath": "./../icons/gradle.svg"}, "word": {"iconPath": "./../icons/word.svg"}, "certificate": {"iconPath": "./../icons/certificate.svg"}, "key": {"iconPath": "./../icons/key.svg"}, "font": {"iconPath": "./../icons/font.svg"}, "dll": {"iconPath": "./../icons/dll.svg"}, "gemfile": {"iconPath": "./../icons/gemfile.svg"}, "rubocop": {"iconPath": "./../icons/rubocop.svg"}, "rubocop_light": {"iconPath": "./../icons/rubocop_light.svg"}, "rspec": {"iconPath": "./../icons/rspec.svg"}, "arduino": {"iconPath": "./../icons/arduino.svg"}, "powerpoint": {"iconPath": "./../icons/powerpoint.svg"}, "video": {"iconPath": "./../icons/video.svg"}, "virtual": {"iconPath": "./../icons/virtual.svg"}, "vedic": {"iconPath": "./../icons/vedic.svg"}, "email": {"iconPath": "./../icons/email.svg"}, "audio": {"iconPath": "./../icons/audio.svg"}, "raml": {"iconPath": "./../icons/raml.svg"}, "xaml": {"iconPath": "./../icons/xaml.svg"}, "kotlin": {"iconPath": "./../icons/kotlin.svg"}, "dart_generated": {"iconPath": "./../icons/dart_generated.svg"}, "actionscript": {"iconPath": "./../icons/actionscript.svg"}, "mxml": {"iconPath": "./../icons/mxml.svg"}, "autohotkey": {"iconPath": "./../icons/autohotkey.svg"}, "flash": {"iconPath": "./../icons/flash.svg"}, "swc": {"iconPath": "./../icons/swc.svg"}, "cmake": {"iconPath": "./../icons/cmake.svg"}, "assembly": {"iconPath": "./../icons/assembly.svg"}, "semgrep": {"iconPath": "./../icons/semgrep.svg"}, "vue-config": {"iconPath": "./../icons/vue-config.svg"}, "nuxt": {"iconPath": "./../icons/nuxt.svg"}, "ocaml": {"iconPath": "./../icons/ocaml.svg"}, "odin": {"iconPath": "./../icons/odin.svg"}, "javascript-map": {"iconPath": "./../icons/javascript-map.svg"}, "css-map": {"iconPath": "./../icons/css-map.svg"}, "test-ts": {"iconPath": "./../icons/test-ts.svg"}, "test-jsx": {"iconPath": "./../icons/test-jsx.svg"}, "test-js": {"iconPath": "./../icons/test-js.svg"}, "angular-component": {"iconPath": "./../icons/angular-component.svg"}, "angular-guard": {"iconPath": "./../icons/angular-guard.svg"}, "angular-service": {"iconPath": "./../icons/angular-service.svg"}, "angular-pipe": {"iconPath": "./../icons/angular-pipe.svg"}, "angular-directive": {"iconPath": "./../icons/angular-directive.svg"}, "angular-resolver": {"iconPath": "./../icons/angular-resolver.svg"}, "smarty": {"iconPath": "./../icons/smarty.svg"}, "bucklescript": {"iconPath": "./../icons/bucklescript.svg"}, "merlin": {"iconPath": "./../icons/merlin.svg"}, "verilog": {"iconPath": "./../icons/verilog.svg"}, "mathematica": {"iconPath": "./../icons/mathematica.svg"}, "vercel": {"iconPath": "./../icons/vercel.svg"}, "vercel_light": {"iconPath": "./../icons/vercel_light.svg"}, "verdaccio": {"iconPath": "./../icons/verdaccio.svg"}, "payload": {"iconPath": "./../icons/payload.svg"}, "payload_light": {"iconPath": "./../icons/payload_light.svg"}, "next": {"iconPath": "./../icons/next.svg"}, "next_light": {"iconPath": "./../icons/next_light.svg"}, "remix": {"iconPath": "./../icons/remix.svg"}, "remix_light": {"iconPath": "./../icons/remix_light.svg"}, "laravel": {"iconPath": "./../icons/laravel.svg"}, "vfl": {"iconPath": "./../icons/vfl.svg"}, "kl": {"iconPath": "./../icons/kl.svg"}, "posthtml": {"iconPath": "./../icons/posthtml.svg"}, "todo": {"iconPath": "./../icons/todo.svg"}, "http": {"iconPath": "./../icons/http.svg"}, "restql": {"iconPath": "./../icons/restql.svg"}, "kivy": {"iconPath": "./../icons/kivy.svg"}, "graphcool": {"iconPath": "./../icons/graphcool.svg"}, "sbt": {"iconPath": "./../icons/sbt.svg"}, "webpack": {"iconPath": "./../icons/webpack.svg"}, "ionic": {"iconPath": "./../icons/ionic.svg"}, "gulp": {"iconPath": "./../icons/gulp.svg"}, "nodejs": {"iconPath": "./../icons/nodejs.svg"}, "npm": {"iconPath": "./../icons/npm.svg"}, "yarn": {"iconPath": "./../icons/yarn.svg"}, "android": {"iconPath": "./../icons/android.svg"}, "tune": {"iconPath": "./../icons/tune.svg"}, "turborepo": {"iconPath": "./../icons/turborepo.svg"}, "turborepo_light": {"iconPath": "./../icons/turborepo_light.svg"}, "babel": {"iconPath": "./../icons/babel.svg"}, "blitz": {"iconPath": "./../icons/blitz.svg"}, "contributing": {"iconPath": "./../icons/contributing.svg"}, "readme": {"iconPath": "./../icons/readme.svg"}, "changelog": {"iconPath": "./../icons/changelog.svg"}, "architecture": {"iconPath": "./../icons/architecture.svg"}, "credits": {"iconPath": "./../icons/credits.svg"}, "authors": {"iconPath": "./../icons/authors.svg"}, "flow": {"iconPath": "./../icons/flow.svg"}, "favicon": {"iconPath": "./../icons/favicon.svg"}, "karma": {"iconPath": "./../icons/karma.svg"}, "bithound": {"iconPath": "./../icons/bithound.svg"}, "svgo": {"iconPath": "./../icons/svgo.svg"}, "appveyor": {"iconPath": "./../icons/appveyor.svg"}, "travis": {"iconPath": "./../icons/travis.svg"}, "codecov": {"iconPath": "./../icons/codecov.svg"}, "sonarcloud": {"iconPath": "./../icons/sonarcloud.svg"}, "protractor": {"iconPath": "./../icons/protractor.svg"}, "fusebox": {"iconPath": "./../icons/fusebox.svg"}, "heroku": {"iconPath": "./../icons/heroku.svg"}, "gitlab": {"iconPath": "./../icons/gitlab.svg"}, "bower": {"iconPath": "./../icons/bower.svg"}, "eslint": {"iconPath": "./../icons/eslint.svg"}, "conduct": {"iconPath": "./../icons/conduct.svg"}, "watchman": {"iconPath": "./../icons/watchman.svg"}, "aurelia": {"iconPath": "./../icons/aurelia.svg"}, "auto": {"iconPath": "./../icons/auto.svg"}, "auto_light": {"iconPath": "./../icons/auto_light.svg"}, "mocha": {"iconPath": "./../icons/mocha.svg"}, "jenkins": {"iconPath": "./../icons/jenkins.svg"}, "firebase": {"iconPath": "./../icons/firebase.svg"}, "figma": {"iconPath": "./../icons/figma.svg"}, "rollup": {"iconPath": "./../icons/rollup.svg"}, "huff_light": {"iconPath": "./../icons/huff_light.svg"}, "hardhat": {"iconPath": "./../icons/hardhat.svg"}, "stylelint": {"iconPath": "./../icons/stylelint.svg"}, "stylelint_light": {"iconPath": "./../icons/stylelint_light.svg"}, "code-climate": {"iconPath": "./../icons/code-climate.svg"}, "code-climate_light": {"iconPath": "./../icons/code-climate_light.svg"}, "prettier": {"iconPath": "./../icons/prettier.svg"}, "renovate": {"iconPath": "./../icons/renovate.svg"}, "apollo": {"iconPath": "./../icons/apollo.svg"}, "nodemon": {"iconPath": "./../icons/nodemon.svg"}, "webhint": {"iconPath": "./../icons/webhint.svg"}, "browserlist": {"iconPath": "./../icons/browserlist.svg"}, "browserlist_light": {"iconPath": "./../icons/browserlist_light.svg"}, "crystal": {"iconPath": "./../icons/crystal.svg"}, "crystal_light": {"iconPath": "./../icons/crystal_light.svg"}, "snyk": {"iconPath": "./../icons/snyk.svg"}, "drone": {"iconPath": "./../icons/drone.svg"}, "drone_light": {"iconPath": "./../icons/drone_light.svg"}, "cuda": {"iconPath": "./../icons/cuda.svg"}, "dotjs": {"iconPath": "./../icons/dotjs.svg"}, "ejs": {"iconPath": "./../icons/ejs.svg"}, "sequelize": {"iconPath": "./../icons/sequelize.svg"}, "gatsby": {"iconPath": "./../icons/gatsby.svg"}, "wakatime": {"iconPath": "./../icons/wakatime.svg"}, "wakatime_light": {"iconPath": "./../icons/wakatime_light.svg"}, "circleci": {"iconPath": "./../icons/circleci.svg"}, "circleci_light": {"iconPath": "./../icons/circleci_light.svg"}, "cloudfoundry": {"iconPath": "./../icons/cloudfoundry.svg"}, "grunt": {"iconPath": "./../icons/grunt.svg"}, "jest": {"iconPath": "./../icons/jest.svg"}, "storybook": {"iconPath": "./../icons/storybook.svg"}, "wepy": {"iconPath": "./../icons/wepy.svg"}, "fastlane": {"iconPath": "./../icons/fastlane.svg"}, "hcl_light": {"iconPath": "./../icons/hcl_light.svg"}, "helm": {"iconPath": "./../icons/helm.svg"}, "san": {"iconPath": "./../icons/san.svg"}, "quokka": {"iconPath": "./../icons/quokka.svg"}, "wallaby": {"iconPath": "./../icons/wallaby.svg"}, "stencil": {"iconPath": "./../icons/stencil.svg"}, "red": {"iconPath": "./../icons/red.svg"}, "makefile": {"iconPath": "./../icons/makefile.svg"}, "foxpro": {"iconPath": "./../icons/foxpro.svg"}, "i18n": {"iconPath": "./../icons/i18n.svg"}, "webassembly": {"iconPath": "./../icons/webassembly.svg"}, "semantic-release": {"iconPath": "./../icons/semantic-release.svg"}, "semantic-release_light": {"iconPath": "./../icons/semantic-release_light.svg"}, "bitbucket": {"iconPath": "./../icons/bitbucket.svg"}, "d": {"iconPath": "./../icons/d.svg"}, "mdx": {"iconPath": "./../icons/mdx.svg"}, "mdsvex": {"iconPath": "./../icons/mdsvex.svg"}, "ballerina": {"iconPath": "./../icons/ballerina.svg"}, "racket": {"iconPath": "./../icons/racket.svg"}, "bazel": {"iconPath": "./../icons/bazel.svg"}, "mint": {"iconPath": "./../icons/mint.svg"}, "velocity": {"iconPath": "./../icons/velocity.svg"}, "azure-pipelines": {"iconPath": "./../icons/azure-pipelines.svg"}, "azure": {"iconPath": "./../icons/azure.svg"}, "vagrant": {"iconPath": "./../icons/vagrant.svg"}, "prisma": {"iconPath": "./../icons/prisma.svg"}, "abc": {"iconPath": "./../icons/abc.svg"}, "asciidoc": {"iconPath": "./../icons/asciidoc.svg"}, "istanbul": {"iconPath": "./../icons/istanbul.svg"}, "edge": {"iconPath": "./../icons/edge.svg"}, "scheme": {"iconPath": "./../icons/scheme.svg"}, "lisp": {"iconPath": "./../icons/lisp.svg"}, "tailwindcss": {"iconPath": "./../icons/tailwindcss.svg"}, "3d": {"iconPath": "./../icons/3d.svg"}, "buildkite": {"iconPath": "./../icons/buildkite.svg"}, "netlify": {"iconPath": "./../icons/netlify.svg"}, "netlify_light": {"iconPath": "./../icons/netlify_light.svg"}, "nest": {"iconPath": "./../icons/nest.svg"}, "moon": {"iconPath": "./../icons/moon.svg"}, "moonscript": {"iconPath": "./../icons/moonscript.svg"}, "percy": {"iconPath": "./../icons/percy.svg"}, "gitpod": {"iconPath": "./../icons/gitpod.svg"}, "advpl_prw": {"iconPath": "./../icons/advpl_prw.svg"}, "advpl_ptm": {"iconPath": "./../icons/advpl_ptm.svg"}, "advpl_tlpp": {"iconPath": "./../icons/advpl_tlpp.svg"}, "advpl_include": {"iconPath": "./../icons/advpl_include.svg"}, "codeowners": {"iconPath": "./../icons/codeowners.svg"}, "gcp": {"iconPath": "./../icons/gcp.svg"}, "disc": {"iconPath": "./../icons/disc.svg"}, "fortran": {"iconPath": "./../icons/fortran.svg"}, "tcl": {"iconPath": "./../icons/tcl.svg"}, "liquid": {"iconPath": "./../icons/liquid.svg"}, "husky": {"iconPath": "./../icons/husky.svg"}, "coconut": {"iconPath": "./../icons/coconut.svg"}, "tilt": {"iconPath": "./../icons/tilt.svg"}, "capacitor": {"iconPath": "./../icons/capacitor.svg"}, "sketch": {"iconPath": "./../icons/sketch.svg"}, "adonis": {"iconPath": "./../icons/adonis.svg"}, "forth": {"iconPath": "./../icons/forth.svg"}, "uml": {"iconPath": "./../icons/uml.svg"}, "uml_light": {"iconPath": "./../icons/uml_light.svg"}, "meson": {"iconPath": "./../icons/meson.svg"}, "commitlint": {"iconPath": "./../icons/commitlint.svg"}, "buck": {"iconPath": "./../icons/buck.svg"}, "nx": {"iconPath": "./../icons/nx.svg"}, "opam": {"iconPath": "./../icons/opam.svg"}, "dune": {"iconPath": "./../icons/dune.svg"}, "imba": {"iconPath": "./../icons/imba.svg"}, "drawio": {"iconPath": "./../icons/drawio.svg"}, "pascal": {"iconPath": "./../icons/pascal.svg"}, "roadmap": {"iconPath": "./../icons/roadmap.svg"}, "nuget": {"iconPath": "./../icons/nuget.svg"}, "command": {"iconPath": "./../icons/command.svg"}, "stryker": {"iconPath": "./../icons/stryker.svg"}, "denizenscript": {"iconPath": "./../icons/denizenscript.svg"}, "modernizr": {"iconPath": "./../icons/modernizr.svg"}, "slug": {"iconPath": "./../icons/slug.svg"}, "stitches": {"iconPath": "./../icons/stitches.svg"}, "stitches_light": {"iconPath": "./../icons/stitches_light.svg"}, "nginx": {"iconPath": "./../icons/nginx.svg"}, "replit": {"iconPath": "./../icons/replit.svg"}, "rescript-interface": {"iconPath": "./../icons/rescript-interface.svg"}, "snowpack": {"iconPath": "./../icons/snowpack.svg"}, "snowpack_light": {"iconPath": "./../icons/snowpack_light.svg"}, "brainfuck": {"iconPath": "./../icons/brainfuck.svg"}, "bicep": {"iconPath": "./../icons/bicep.svg"}, "cobol": {"iconPath": "./../icons/cobol.svg"}, "quasar": {"iconPath": "./../icons/quasar.svg"}, "dependabot": {"iconPath": "./../icons/dependabot.svg"}, "pipeline": {"iconPath": "./../icons/pipeline.svg"}, "vite": {"iconPath": "./../icons/vite.svg"}, "vitest": {"iconPath": "./../icons/vitest.svg"}, "opa": {"iconPath": "./../icons/opa.svg"}, "lerna": {"iconPath": "./../icons/lerna.svg"}, "windicss": {"iconPath": "./../icons/windicss.svg"}, "textlint": {"iconPath": "./../icons/textlint.svg"}, "lilypond": {"iconPath": "./../icons/lilypond.svg"}, "chess_light": {"iconPath": "./../icons/chess_light.svg"}, "sentry": {"iconPath": "./../icons/sentry.svg"}, "phpunit": {"iconPath": "./../icons/phpunit.svg"}, "php-cs-fixer": {"iconPath": "./../icons/php-cs-fixer.svg"}, "robots": {"iconPath": "./../icons/robots.svg"}, "tsconfig": {"iconPath": "./../icons/tsconfig.svg"}, "tauri": {"iconPath": "./../icons/tauri.svg"}, "jsconfig": {"iconPath": "./../icons/jsconfig.svg"}, "maven": {"iconPath": "./../icons/maven.svg"}, "ada": {"iconPath": "./../icons/ada.svg"}, "serverless": {"iconPath": "./../icons/serverless.svg"}, "supabase": {"iconPath": "./../icons/supabase.svg"}, "ember": {"iconPath": "./../icons/ember.svg"}, "horusec": {"iconPath": "./../icons/horusec.svg"}, "poetry": {"iconPath": "./../icons/poetry.svg"}, "pdm": {"iconPath": "./../icons/pdm.svg"}, "coala": {"iconPath": "./../icons/coala.svg"}, "parcel": {"iconPath": "./../icons/parcel.svg"}, "dinophp": {"iconPath": "./../icons/dinophp.svg"}, "teal": {"iconPath": "./../icons/teal.svg"}, "template": {"iconPath": "./../icons/template.svg"}, "astyle": {"iconPath": "./../icons/astyle.svg"}, "lighthouse": {"iconPath": "./../icons/lighthouse.svg"}, "svgr": {"iconPath": "./../icons/svgr.svg"}, "rome": {"iconPath": "./../icons/rome.svg"}, "cypress": {"iconPath": "./../icons/cypress.svg"}, "siyuan": {"iconPath": "./../icons/siyuan.svg"}, "ndst": {"iconPath": "./../icons/ndst.svg"}, "plop": {"iconPath": "./../icons/plop.svg"}, "tobi": {"iconPath": "./../icons/tobi.svg"}, "tobimake": {"iconPath": "./../icons/tobimake.svg"}, "gleam": {"iconPath": "./../icons/gleam.svg"}, "pnpm": {"iconPath": "./../icons/pnpm.svg"}, "pnpm_light": {"iconPath": "./../icons/pnpm_light.svg"}, "gridsome": {"iconPath": "./../icons/gridsome.svg"}, "steadybit": {"iconPath": "./../icons/steadybit.svg"}, "capnp": {"iconPath": "./../icons/capnp.svg"}, "caddy": {"iconPath": "./../icons/caddy.svg"}, "openapi": {"iconPath": "./../icons/openapi.svg"}, "openapi_light": {"iconPath": "./../icons/openapi_light.svg"}, "swagger": {"iconPath": "./../icons/swagger.svg"}, "bun": {"iconPath": "./../icons/bun.svg"}, "bun_light": {"iconPath": "./../icons/bun_light.svg"}, "antlr": {"iconPath": "./../icons/antlr.svg"}, "pinejs": {"iconPath": "./../icons/pinejs.svg"}, "nano-staged": {"iconPath": "./../icons/nano-staged.svg"}, "nano-staged_light": {"iconPath": "./../icons/nano-staged_light.svg"}, "knip": {"iconPath": "./../icons/knip.svg"}, "taskfile": {"iconPath": "./../icons/taskfile.svg"}, "craco": {"iconPath": "./../icons/craco.svg"}, "gamemaker": {"iconPath": "./../icons/gamemaker.svg"}, "tldraw": {"iconPath": "./../icons/tldraw.svg"}, "tldraw_light": {"iconPath": "./../icons/tldraw_light.svg"}, "mercurial": {"iconPath": "./../icons/mercurial.svg"}, "deno": {"iconPath": "./../icons/deno.svg"}, "deno_light": {"iconPath": "./../icons/deno_light.svg"}, "plastic": {"iconPath": "./../icons/plastic.svg"}, "typst": {"iconPath": "./../icons/typst.svg"}, "unocss": {"iconPath": "./../icons/unocss.svg"}, "ifanr-cloud": {"iconPath": "./../icons/ifanr-cloud.svg"}, "mermaid": {"iconPath": "./../icons/mermaid.svg"}, "syncpack": {"iconPath": "./../icons/syncpack.svg"}, "werf": {"iconPath": "./../icons/werf.svg"}, "roblox": {"iconPath": "./../icons/roblox.svg"}, "panda": {"iconPath": "./../icons/panda.svg"}, "biome": {"iconPath": "./../icons/biome.svg"}, "esbuild": {"iconPath": "./../icons/esbuild.svg"}, "spwn": {"iconPath": "./../icons/spwn.svg"}, "templ": {"iconPath": "./../icons/templ.svg"}, "chrome": {"iconPath": "./../icons/chrome.svg"}, "stan": {"iconPath": "./../icons/stan.svg"}, "abap": {"iconPath": "./../icons/abap.svg"}, "lottie": {"iconPath": "./../icons/lottie.svg"}, "puppeteer": {"iconPath": "./../icons/puppeteer.svg"}, "apps-script": {"iconPath": "./../icons/apps-script.svg"}, "pkl": {"iconPath": "./../icons/pkl.svg"}, "kubernetes": {"iconPath": "./../icons/kubernetes.svg"}, "file": {"iconPath": "./../icons/file.svg"}, "folder-robot": {"iconPath": "./../icons/folder-robot.svg"}, "folder-robot-open": {"iconPath": "./../icons/folder-robot-open.svg"}, "folder-src": {"iconPath": "./../icons/folder-src.svg"}, "folder-src-open": {"iconPath": "./../icons/folder-src-open.svg"}, "folder-dist": {"iconPath": "./../icons/folder-dist.svg"}, "folder-dist-open": {"iconPath": "./../icons/folder-dist-open.svg"}, "folder-css": {"iconPath": "./../icons/folder-css.svg"}, "folder-css-open": {"iconPath": "./../icons/folder-css-open.svg"}, "folder-sass": {"iconPath": "./../icons/folder-sass.svg"}, "folder-sass-open": {"iconPath": "./../icons/folder-sass-open.svg"}, "folder-television": {"iconPath": "./../icons/folder-television.svg"}, "folder-television-open": {"iconPath": "./../icons/folder-television-open.svg"}, "folder-desktop": {"iconPath": "./../icons/folder-desktop.svg"}, "folder-desktop-open": {"iconPath": "./../icons/folder-desktop-open.svg"}, "folder-console": {"iconPath": "./../icons/folder-console.svg"}, "folder-console-open": {"iconPath": "./../icons/folder-console-open.svg"}, "folder-images": {"iconPath": "./../icons/folder-images.svg"}, "folder-images-open": {"iconPath": "./../icons/folder-images-open.svg"}, "folder-scripts": {"iconPath": "./../icons/folder-scripts.svg"}, "folder-scripts-open": {"iconPath": "./../icons/folder-scripts-open.svg"}, "folder-node": {"iconPath": "./../icons/folder-node.svg"}, "folder-node-open": {"iconPath": "./../icons/folder-node-open.svg"}, "folder-javascript": {"iconPath": "./../icons/folder-javascript.svg"}, "folder-javascript-open": {"iconPath": "./../icons/folder-javascript-open.svg"}, "folder-json": {"iconPath": "./../icons/folder-json.svg"}, "folder-json-open": {"iconPath": "./../icons/folder-json-open.svg"}, "folder-font": {"iconPath": "./../icons/folder-font.svg"}, "folder-font-open": {"iconPath": "./../icons/folder-font-open.svg"}, "folder-bower": {"iconPath": "./../icons/folder-bower.svg"}, "folder-bower-open": {"iconPath": "./../icons/folder-bower-open.svg"}, "folder-test": {"iconPath": "./../icons/folder-test.svg"}, "folder-test-open": {"iconPath": "./../icons/folder-test-open.svg"}, "folder-jinja": {"iconPath": "./../icons/folder-jinja.svg"}, "folder-jinja-open": {"iconPath": "./../icons/folder-jinja-open.svg"}, "folder-jinja_light": {"iconPath": "./../icons/folder-jinja_light.svg"}, "folder-jinja-open_light": {"iconPath": "./../icons/folder-jinja-open_light.svg"}, "folder-markdown": {"iconPath": "./../icons/folder-markdown.svg"}, "folder-markdown-open": {"iconPath": "./../icons/folder-markdown-open.svg"}, "folder-pdm": {"iconPath": "./../icons/folder-pdm.svg"}, "folder-pdm-open": {"iconPath": "./../icons/folder-pdm-open.svg"}, "folder-php": {"iconPath": "./../icons/folder-php.svg"}, "folder-php-open": {"iconPath": "./../icons/folder-php-open.svg"}, "folder-phpmailer": {"iconPath": "./../icons/folder-phpmailer.svg"}, "folder-phpmailer-open": {"iconPath": "./../icons/folder-phpmailer-open.svg"}, "folder-sublime": {"iconPath": "./../icons/folder-sublime.svg"}, "folder-sublime-open": {"iconPath": "./../icons/folder-sublime-open.svg"}, "folder-docs": {"iconPath": "./../icons/folder-docs.svg"}, "folder-docs-open": {"iconPath": "./../icons/folder-docs-open.svg"}, "folder-git": {"iconPath": "./../icons/folder-git.svg"}, "folder-git-open": {"iconPath": "./../icons/folder-git-open.svg"}, "folder-github": {"iconPath": "./../icons/folder-github.svg"}, "folder-github-open": {"iconPath": "./../icons/folder-github-open.svg"}, "folder-gitlab": {"iconPath": "./../icons/folder-gitlab.svg"}, "folder-gitlab-open": {"iconPath": "./../icons/folder-gitlab-open.svg"}, "folder-vscode": {"iconPath": "./../icons/folder-vscode.svg"}, "folder-vscode-open": {"iconPath": "./../icons/folder-vscode-open.svg"}, "folder-views": {"iconPath": "./../icons/folder-views.svg"}, "folder-views-open": {"iconPath": "./../icons/folder-views-open.svg"}, "folder-vue": {"iconPath": "./../icons/folder-vue.svg"}, "folder-vue-open": {"iconPath": "./../icons/folder-vue-open.svg"}, "folder-vuepress": {"iconPath": "./../icons/folder-vuepress.svg"}, "folder-vuepress-open": {"iconPath": "./../icons/folder-vuepress-open.svg"}, "folder-expo": {"iconPath": "./../icons/folder-expo.svg"}, "folder-expo-open": {"iconPath": "./../icons/folder-expo-open.svg"}, "folder-config": {"iconPath": "./../icons/folder-config.svg"}, "folder-config-open": {"iconPath": "./../icons/folder-config-open.svg"}, "folder-i18n": {"iconPath": "./../icons/folder-i18n.svg"}, "folder-i18n-open": {"iconPath": "./../icons/folder-i18n-open.svg"}, "folder-components": {"iconPath": "./../icons/folder-components.svg"}, "folder-components-open": {"iconPath": "./../icons/folder-components-open.svg"}, "folder-verdaccio": {"iconPath": "./../icons/folder-verdaccio.svg"}, "folder-verdaccio-open": {"iconPath": "./../icons/folder-verdaccio-open.svg"}, "folder-aurelia": {"iconPath": "./../icons/folder-aurelia.svg"}, "folder-aurelia-open": {"iconPath": "./../icons/folder-aurelia-open.svg"}, "folder-resource": {"iconPath": "./../icons/folder-resource.svg"}, "folder-resource-open": {"iconPath": "./../icons/folder-resource-open.svg"}, "folder-lib": {"iconPath": "./../icons/folder-lib.svg"}, "folder-lib-open": {"iconPath": "./../icons/folder-lib-open.svg"}, "folder-theme": {"iconPath": "./../icons/folder-theme.svg"}, "folder-theme-open": {"iconPath": "./../icons/folder-theme-open.svg"}, "folder-webpack": {"iconPath": "./../icons/folder-webpack.svg"}, "folder-webpack-open": {"iconPath": "./../icons/folder-webpack-open.svg"}, "folder-global": {"iconPath": "./../icons/folder-global.svg"}, "folder-global-open": {"iconPath": "./../icons/folder-global-open.svg"}, "folder-public": {"iconPath": "./../icons/folder-public.svg"}, "folder-public-open": {"iconPath": "./../icons/folder-public-open.svg"}, "folder-include": {"iconPath": "./../icons/folder-include.svg"}, "folder-include-open": {"iconPath": "./../icons/folder-include-open.svg"}, "folder-docker": {"iconPath": "./../icons/folder-docker.svg"}, "folder-docker-open": {"iconPath": "./../icons/folder-docker-open.svg"}, "folder-database": {"iconPath": "./../icons/folder-database.svg"}, "folder-database-open": {"iconPath": "./../icons/folder-database-open.svg"}, "folder-log": {"iconPath": "./../icons/folder-log.svg"}, "folder-log-open": {"iconPath": "./../icons/folder-log-open.svg"}, "folder-target": {"iconPath": "./../icons/folder-target.svg"}, "folder-target-open": {"iconPath": "./../icons/folder-target-open.svg"}, "folder-temp": {"iconPath": "./../icons/folder-temp.svg"}, "folder-temp-open": {"iconPath": "./../icons/folder-temp-open.svg"}, "folder-aws": {"iconPath": "./../icons/folder-aws.svg"}, "folder-aws-open": {"iconPath": "./../icons/folder-aws-open.svg"}, "folder-audio": {"iconPath": "./../icons/folder-audio.svg"}, "folder-audio-open": {"iconPath": "./../icons/folder-audio-open.svg"}, "folder-video": {"iconPath": "./../icons/folder-video.svg"}, "folder-video-open": {"iconPath": "./../icons/folder-video-open.svg"}, "folder-kubernetes": {"iconPath": "./../icons/folder-kubernetes.svg"}, "folder-kubernetes-open": {"iconPath": "./../icons/folder-kubernetes-open.svg"}, "folder-import": {"iconPath": "./../icons/folder-import.svg"}, "folder-import-open": {"iconPath": "./../icons/folder-import-open.svg"}, "folder-export": {"iconPath": "./../icons/folder-export.svg"}, "folder-export-open": {"iconPath": "./../icons/folder-export-open.svg"}, "folder-wakatime": {"iconPath": "./../icons/folder-wakatime.svg"}, "folder-wakatime-open": {"iconPath": "./../icons/folder-wakatime-open.svg"}, "folder-circleci": {"iconPath": "./../icons/folder-circleci.svg"}, "folder-circleci-open": {"iconPath": "./../icons/folder-circleci-open.svg"}, "folder-wordpress": {"iconPath": "./../icons/folder-wordpress.svg"}, "folder-wordpress-open": {"iconPath": "./../icons/folder-wordpress-open.svg"}, "folder-gradle": {"iconPath": "./../icons/folder-gradle.svg"}, "folder-gradle-open": {"iconPath": "./../icons/folder-gradle-open.svg"}, "folder-coverage": {"iconPath": "./../icons/folder-coverage.svg"}, "folder-coverage-open": {"iconPath": "./../icons/folder-coverage-open.svg"}, "folder-class": {"iconPath": "./../icons/folder-class.svg"}, "folder-class-open": {"iconPath": "./../icons/folder-class-open.svg"}, "folder-other": {"iconPath": "./../icons/folder-other.svg"}, "folder-other-open": {"iconPath": "./../icons/folder-other-open.svg"}, "folder-lua": {"iconPath": "./../icons/folder-lua.svg"}, "folder-lua-open": {"iconPath": "./../icons/folder-lua-open.svg"}, "folder-typescript": {"iconPath": "./../icons/folder-typescript.svg"}, "folder-typescript-open": {"iconPath": "./../icons/folder-typescript-open.svg"}, "folder-graphql": {"iconPath": "./../icons/folder-graphql.svg"}, "folder-graphql-open": {"iconPath": "./../icons/folder-graphql-open.svg"}, "folder-routes": {"iconPath": "./../icons/folder-routes.svg"}, "folder-routes-open": {"iconPath": "./../icons/folder-routes-open.svg"}, "folder-ci": {"iconPath": "./../icons/folder-ci.svg"}, "folder-ci-open": {"iconPath": "./../icons/folder-ci-open.svg"}, "folder-benchmark": {"iconPath": "./../icons/folder-benchmark.svg"}, "folder-benchmark-open": {"iconPath": "./../icons/folder-benchmark-open.svg"}, "folder-messages": {"iconPath": "./../icons/folder-messages.svg"}, "folder-messages-open": {"iconPath": "./../icons/folder-messages-open.svg"}, "folder-less": {"iconPath": "./../icons/folder-less.svg"}, "folder-less-open": {"iconPath": "./../icons/folder-less-open.svg"}, "folder-gulp": {"iconPath": "./../icons/folder-gulp.svg"}, "folder-gulp-open": {"iconPath": "./../icons/folder-gulp-open.svg"}, "folder-python": {"iconPath": "./../icons/folder-python.svg"}, "folder-python-open": {"iconPath": "./../icons/folder-python-open.svg"}, "folder-mojo": {"iconPath": "./../icons/folder-mojo.svg"}, "folder-mojo-open": {"iconPath": "./../icons/folder-mojo-open.svg"}, "folder-moon": {"iconPath": "./../icons/folder-moon.svg"}, "folder-moon-open": {"iconPath": "./../icons/folder-moon-open.svg"}, "folder-debug": {"iconPath": "./../icons/folder-debug.svg"}, "folder-debug-open": {"iconPath": "./../icons/folder-debug-open.svg"}, "folder-fastlane": {"iconPath": "./../icons/folder-fastlane.svg"}, "folder-fastlane-open": {"iconPath": "./../icons/folder-fastlane-open.svg"}, "folder-plugin": {"iconPath": "./../icons/folder-plugin.svg"}, "folder-plugin-open": {"iconPath": "./../icons/folder-plugin-open.svg"}, "folder-middleware": {"iconPath": "./../icons/folder-middleware.svg"}, "folder-middleware-open": {"iconPath": "./../icons/folder-middleware-open.svg"}, "folder-controller": {"iconPath": "./../icons/folder-controller.svg"}, "folder-controller-open": {"iconPath": "./../icons/folder-controller-open.svg"}, "folder-ansible": {"iconPath": "./../icons/folder-ansible.svg"}, "folder-ansible-open": {"iconPath": "./../icons/folder-ansible-open.svg"}, "folder-server": {"iconPath": "./../icons/folder-server.svg"}, "folder-server-open": {"iconPath": "./../icons/folder-server-open.svg"}, "folder-client": {"iconPath": "./../icons/folder-client.svg"}, "folder-client-open": {"iconPath": "./../icons/folder-client-open.svg"}, "folder-tasks": {"iconPath": "./../icons/folder-tasks.svg"}, "folder-tasks-open": {"iconPath": "./../icons/folder-tasks-open.svg"}, "folder-android": {"iconPath": "./../icons/folder-android.svg"}, "folder-android-open": {"iconPath": "./../icons/folder-android-open.svg"}, "folder-ios": {"iconPath": "./../icons/folder-ios.svg"}, "folder-ios-open": {"iconPath": "./../icons/folder-ios-open.svg"}, "folder-upload": {"iconPath": "./../icons/folder-upload.svg"}, "folder-upload-open": {"iconPath": "./../icons/folder-upload-open.svg"}, "folder-download": {"iconPath": "./../icons/folder-download.svg"}, "folder-download-open": {"iconPath": "./../icons/folder-download-open.svg"}, "folder-tools": {"iconPath": "./../icons/folder-tools.svg"}, "folder-tools-open": {"iconPath": "./../icons/folder-tools-open.svg"}, "folder-helper": {"iconPath": "./../icons/folder-helper.svg"}, "folder-helper-open": {"iconPath": "./../icons/folder-helper-open.svg"}, "folder-serverless": {"iconPath": "./../icons/folder-serverless.svg"}, "folder-serverless-open": {"iconPath": "./../icons/folder-serverless-open.svg"}, "folder-api": {"iconPath": "./../icons/folder-api.svg"}, "folder-api-open": {"iconPath": "./../icons/folder-api-open.svg"}, "folder-app": {"iconPath": "./../icons/folder-app.svg"}, "folder-app-open": {"iconPath": "./../icons/folder-app-open.svg"}, "folder-apollo": {"iconPath": "./../icons/folder-apollo.svg"}, "folder-apollo-open": {"iconPath": "./../icons/folder-apollo-open.svg"}, "folder-archive": {"iconPath": "./../icons/folder-archive.svg"}, "folder-archive-open": {"iconPath": "./../icons/folder-archive-open.svg"}, "folder-batch": {"iconPath": "./../icons/folder-batch.svg"}, "folder-batch-open": {"iconPath": "./../icons/folder-batch-open.svg"}, "folder-buildkite": {"iconPath": "./../icons/folder-buildkite.svg"}, "folder-buildkite-open": {"iconPath": "./../icons/folder-buildkite-open.svg"}, "folder-cluster": {"iconPath": "./../icons/folder-cluster.svg"}, "folder-cluster-open": {"iconPath": "./../icons/folder-cluster-open.svg"}, "folder-command": {"iconPath": "./../icons/folder-command.svg"}, "folder-command-open": {"iconPath": "./../icons/folder-command-open.svg"}, "folder-constant": {"iconPath": "./../icons/folder-constant.svg"}, "folder-constant-open": {"iconPath": "./../icons/folder-constant-open.svg"}, "folder-container": {"iconPath": "./../icons/folder-container.svg"}, "folder-container-open": {"iconPath": "./../icons/folder-container-open.svg"}, "folder-content": {"iconPath": "./../icons/folder-content.svg"}, "folder-content-open": {"iconPath": "./../icons/folder-content-open.svg"}, "folder-context": {"iconPath": "./../icons/folder-context.svg"}, "folder-context-open": {"iconPath": "./../icons/folder-context-open.svg"}, "folder-core": {"iconPath": "./../icons/folder-core.svg"}, "folder-core-open": {"iconPath": "./../icons/folder-core-open.svg"}, "folder-delta": {"iconPath": "./../icons/folder-delta.svg"}, "folder-delta-open": {"iconPath": "./../icons/folder-delta-open.svg"}, "folder-dump": {"iconPath": "./../icons/folder-dump.svg"}, "folder-dump-open": {"iconPath": "./../icons/folder-dump-open.svg"}, "folder-examples": {"iconPath": "./../icons/folder-examples.svg"}, "folder-examples-open": {"iconPath": "./../icons/folder-examples-open.svg"}, "folder-environment": {"iconPath": "./../icons/folder-environment.svg"}, "folder-environment-open": {"iconPath": "./../icons/folder-environment-open.svg"}, "folder-functions": {"iconPath": "./../icons/folder-functions.svg"}, "folder-functions-open": {"iconPath": "./../icons/folder-functions-open.svg"}, "folder-generator": {"iconPath": "./../icons/folder-generator.svg"}, "folder-generator-open": {"iconPath": "./../icons/folder-generator-open.svg"}, "folder-hook": {"iconPath": "./../icons/folder-hook.svg"}, "folder-hook-open": {"iconPath": "./../icons/folder-hook-open.svg"}, "folder-job": {"iconPath": "./../icons/folder-job.svg"}, "folder-job-open": {"iconPath": "./../icons/folder-job-open.svg"}, "folder-keys": {"iconPath": "./../icons/folder-keys.svg"}, "folder-keys-open": {"iconPath": "./../icons/folder-keys-open.svg"}, "folder-layout": {"iconPath": "./../icons/folder-layout.svg"}, "folder-layout-open": {"iconPath": "./../icons/folder-layout-open.svg"}, "folder-mail": {"iconPath": "./../icons/folder-mail.svg"}, "folder-mail-open": {"iconPath": "./../icons/folder-mail-open.svg"}, "folder-mappings": {"iconPath": "./../icons/folder-mappings.svg"}, "folder-mappings-open": {"iconPath": "./../icons/folder-mappings-open.svg"}, "folder-meta": {"iconPath": "./../icons/folder-meta.svg"}, "folder-meta-open": {"iconPath": "./../icons/folder-meta-open.svg"}, "folder-changesets": {"iconPath": "./../icons/folder-changesets.svg"}, "folder-changesets-open": {"iconPath": "./../icons/folder-changesets-open.svg"}, "folder-packages": {"iconPath": "./../icons/folder-packages.svg"}, "folder-packages-open": {"iconPath": "./../icons/folder-packages-open.svg"}, "folder-shared": {"iconPath": "./../icons/folder-shared.svg"}, "folder-shared-open": {"iconPath": "./../icons/folder-shared-open.svg"}, "folder-shader": {"iconPath": "./../icons/folder-shader.svg"}, "folder-shader-open": {"iconPath": "./../icons/folder-shader-open.svg"}, "folder-stack": {"iconPath": "./../icons/folder-stack.svg"}, "folder-stack-open": {"iconPath": "./../icons/folder-stack-open.svg"}, "folder-template": {"iconPath": "./../icons/folder-template.svg"}, "folder-template-open": {"iconPath": "./../icons/folder-template-open.svg"}, "folder-utils": {"iconPath": "./../icons/folder-utils.svg"}, "folder-utils-open": {"iconPath": "./../icons/folder-utils-open.svg"}, "folder-supabase": {"iconPath": "./../icons/folder-supabase.svg"}, "folder-supabase-open": {"iconPath": "./../icons/folder-supabase-open.svg"}, "folder-private": {"iconPath": "./../icons/folder-private.svg"}, "folder-private-open": {"iconPath": "./../icons/folder-private-open.svg"}, "folder-linux": {"iconPath": "./../icons/folder-linux.svg"}, "folder-linux-open": {"iconPath": "./../icons/folder-linux-open.svg"}, "folder-windows": {"iconPath": "./../icons/folder-windows.svg"}, "folder-windows-open": {"iconPath": "./../icons/folder-windows-open.svg"}, "folder-macos": {"iconPath": "./../icons/folder-macos.svg"}, "folder-macos-open": {"iconPath": "./../icons/folder-macos-open.svg"}, "folder-error": {"iconPath": "./../icons/folder-error.svg"}, "folder-error-open": {"iconPath": "./../icons/folder-error-open.svg"}, "folder-event": {"iconPath": "./../icons/folder-event.svg"}, "folder-event-open": {"iconPath": "./../icons/folder-event-open.svg"}, "folder-secure": {"iconPath": "./../icons/folder-secure.svg"}, "folder-secure-open": {"iconPath": "./../icons/folder-secure-open.svg"}, "folder-custom": {"iconPath": "./../icons/folder-custom.svg"}, "folder-custom-open": {"iconPath": "./../icons/folder-custom-open.svg"}, "folder-mock": {"iconPath": "./../icons/folder-mock.svg"}, "folder-mock-open": {"iconPath": "./../icons/folder-mock-open.svg"}, "folder-syntax": {"iconPath": "./../icons/folder-syntax.svg"}, "folder-syntax-open": {"iconPath": "./../icons/folder-syntax-open.svg"}, "folder-vm": {"iconPath": "./../icons/folder-vm.svg"}, "folder-vm-open": {"iconPath": "./../icons/folder-vm-open.svg"}, "folder-stylus": {"iconPath": "./../icons/folder-stylus.svg"}, "folder-stylus-open": {"iconPath": "./../icons/folder-stylus-open.svg"}, "folder-flow": {"iconPath": "./../icons/folder-flow.svg"}, "folder-flow-open": {"iconPath": "./../icons/folder-flow-open.svg"}, "folder-rules": {"iconPath": "./../icons/folder-rules.svg"}, "folder-rules-open": {"iconPath": "./../icons/folder-rules-open.svg"}, "folder-review": {"iconPath": "./../icons/folder-review.svg"}, "folder-review-open": {"iconPath": "./../icons/folder-review-open.svg"}, "folder-animation": {"iconPath": "./../icons/folder-animation.svg"}, "folder-animation-open": {"iconPath": "./../icons/folder-animation-open.svg"}, "folder-guard": {"iconPath": "./../icons/folder-guard.svg"}, "folder-guard-open": {"iconPath": "./../icons/folder-guard-open.svg"}, "folder-prisma": {"iconPath": "./../icons/folder-prisma.svg"}, "folder-prisma-open": {"iconPath": "./../icons/folder-prisma-open.svg"}, "folder-pipe": {"iconPath": "./../icons/folder-pipe.svg"}, "folder-pipe-open": {"iconPath": "./../icons/folder-pipe-open.svg"}, "folder-svg": {"iconPath": "./../icons/folder-svg.svg"}, "folder-svg-open": {"iconPath": "./../icons/folder-svg-open.svg"}, "folder-terraform": {"iconPath": "./../icons/folder-terraform.svg"}, "folder-terraform-open": {"iconPath": "./../icons/folder-terraform-open.svg"}, "folder-mobile": {"iconPath": "./../icons/folder-mobile.svg"}, "folder-mobile-open": {"iconPath": "./../icons/folder-mobile-open.svg"}, "folder-stencil": {"iconPath": "./../icons/folder-stencil.svg"}, "folder-stencil-open": {"iconPath": "./../icons/folder-stencil-open.svg"}, "folder-firebase": {"iconPath": "./../icons/folder-firebase.svg"}, "folder-firebase-open": {"iconPath": "./../icons/folder-firebase-open.svg"}, "folder-svelte": {"iconPath": "./../icons/folder-svelte.svg"}, "folder-svelte-open": {"iconPath": "./../icons/folder-svelte-open.svg"}, "folder-update": {"iconPath": "./../icons/folder-update.svg"}, "folder-update-open": {"iconPath": "./../icons/folder-update-open.svg"}, "folder-intellij": {"iconPath": "./../icons/folder-intellij.svg"}, "folder-intellij-open": {"iconPath": "./../icons/folder-intellij-open.svg"}, "folder-intellij_light": {"iconPath": "./../icons/folder-intellij_light.svg"}, "folder-intellij-open_light": {"iconPath": "./../icons/folder-intellij-open_light.svg"}, "folder-azure-pipelines": {"iconPath": "./../icons/folder-azure-pipelines.svg"}, "folder-azure-pipelines-open": {"iconPath": "./../icons/folder-azure-pipelines-open.svg"}, "folder-mjml": {"iconPath": "./../icons/folder-mjml.svg"}, "folder-mjml-open": {"iconPath": "./../icons/folder-mjml-open.svg"}, "folder-admin": {"iconPath": "./../icons/folder-admin.svg"}, "folder-admin-open": {"iconPath": "./../icons/folder-admin-open.svg"}, "folder-scala": {"iconPath": "./../icons/folder-scala.svg"}, "folder-scala-open": {"iconPath": "./../icons/folder-scala-open.svg"}, "folder-connection": {"iconPath": "./../icons/folder-connection.svg"}, "folder-connection-open": {"iconPath": "./../icons/folder-connection-open.svg"}, "folder-quasar": {"iconPath": "./../icons/folder-quasar.svg"}, "folder-quasar-open": {"iconPath": "./../icons/folder-quasar-open.svg"}, "folder-next": {"iconPath": "./../icons/folder-next.svg"}, "folder-next-open": {"iconPath": "./../icons/folder-next-open.svg"}, "folder-cobol": {"iconPath": "./../icons/folder-cobol.svg"}, "folder-cobol-open": {"iconPath": "./../icons/folder-cobol-open.svg"}, "folder-yarn": {"iconPath": "./../icons/folder-yarn.svg"}, "folder-yarn-open": {"iconPath": "./../icons/folder-yarn-open.svg"}, "folder-husky": {"iconPath": "./../icons/folder-husky.svg"}, "folder-husky-open": {"iconPath": "./../icons/folder-husky-open.svg"}, "folder-storybook": {"iconPath": "./../icons/folder-storybook.svg"}, "folder-storybook-open": {"iconPath": "./../icons/folder-storybook-open.svg"}, "folder-base": {"iconPath": "./../icons/folder-base.svg"}, "folder-base-open": {"iconPath": "./../icons/folder-base-open.svg"}, "folder-cart": {"iconPath": "./../icons/folder-cart.svg"}, "folder-cart-open": {"iconPath": "./../icons/folder-cart-open.svg"}, "folder-home": {"iconPath": "./../icons/folder-home.svg"}, "folder-home-open": {"iconPath": "./../icons/folder-home-open.svg"}, "folder-project": {"iconPath": "./../icons/folder-project.svg"}, "folder-project-open": {"iconPath": "./../icons/folder-project-open.svg"}, "folder-interface": {"iconPath": "./../icons/folder-interface.svg"}, "folder-interface-open": {"iconPath": "./../icons/folder-interface-open.svg"}, "folder-netlify": {"iconPath": "./../icons/folder-netlify.svg"}, "folder-netlify-open": {"iconPath": "./../icons/folder-netlify-open.svg"}, "folder-enum": {"iconPath": "./../icons/folder-enum.svg"}, "folder-enum-open": {"iconPath": "./../icons/folder-enum-open.svg"}, "folder-contract": {"iconPath": "./../icons/folder-contract.svg"}, "folder-contract-open": {"iconPath": "./../icons/folder-contract-open.svg"}, "folder-queue": {"iconPath": "./../icons/folder-queue.svg"}, "folder-queue-open": {"iconPath": "./../icons/folder-queue-open.svg"}, "folder-vercel": {"iconPath": "./../icons/folder-vercel.svg"}, "folder-vercel-open": {"iconPath": "./../icons/folder-vercel-open.svg"}, "folder-cypress": {"iconPath": "./../icons/folder-cypress.svg"}, "folder-cypress-open": {"iconPath": "./../icons/folder-cypress-open.svg"}, "folder-decorators": {"iconPath": "./../icons/folder-decorators.svg"}, "folder-decorators-open": {"iconPath": "./../icons/folder-decorators-open.svg"}, "folder-java": {"iconPath": "./../icons/folder-java.svg"}, "folder-java-open": {"iconPath": "./../icons/folder-java-open.svg"}, "folder-resolver": {"iconPath": "./../icons/folder-resolver.svg"}, "folder-resolver-open": {"iconPath": "./../icons/folder-resolver-open.svg"}, "folder-angular": {"iconPath": "./../icons/folder-angular.svg"}, "folder-angular-open": {"iconPath": "./../icons/folder-angular-open.svg"}, "folder-unity": {"iconPath": "./../icons/folder-unity.svg"}, "folder-unity-open": {"iconPath": "./../icons/folder-unity-open.svg"}, "folder-pdf": {"iconPath": "./../icons/folder-pdf.svg"}, "folder-pdf-open": {"iconPath": "./../icons/folder-pdf-open.svg"}, "folder-proto": {"iconPath": "./../icons/folder-proto.svg"}, "folder-proto-open": {"iconPath": "./../icons/folder-proto-open.svg"}, "folder-plastic": {"iconPath": "./../icons/folder-plastic.svg"}, "folder-plastic-open": {"iconPath": "./../icons/folder-plastic-open.svg"}, "folder-gamemaker": {"iconPath": "./../icons/folder-gamemaker.svg"}, "folder-gamemaker-open": {"iconPath": "./../icons/folder-gamemaker-open.svg"}, "folder-mercurial": {"iconPath": "./../icons/folder-mercurial.svg"}, "folder-mercurial-open": {"iconPath": "./../icons/folder-mercurial-open.svg"}, "folder-godot": {"iconPath": "./../icons/folder-godot.svg"}, "folder-godot-open": {"iconPath": "./../icons/folder-godot-open.svg"}, "folder-lottie": {"iconPath": "./../icons/folder-lottie.svg"}, "folder-lottie-open": {"iconPath": "./../icons/folder-lottie-open.svg"}, "folder-taskfile": {"iconPath": "./../icons/folder-taskfile.svg"}, "folder-taskfile-open": {"iconPath": "./../icons/folder-taskfile-open.svg"}, "folder-cloudflare": {"iconPath": "./../icons/folder-cloudflare.svg"}, "folder-cloudflare-open": {"iconPath": "./../icons/folder-cloudflare-open.svg"}, "folder-seeders": {"iconPath": "./../icons/folder-seeders.svg"}, "folder-seeders-open": {"iconPath": "./../icons/folder-seeders-open.svg"}, "folder": {"iconPath": "./../icons/folder.svg"}, "folder-open": {"iconPath": "./../icons/folder-open.svg"}, "folder-root": {"iconPath": "./../icons/folder-root.svg"}, "folder-root-open": {"iconPath": "./../icons/folder-root-open.svg"}}, "folderNames": {"bot": "folder-robot", ".bot": "folder-robot", "_bot": "folder-robot", "__bot__": "folder-robot", "robot": "folder-robot", ".robot": "folder-robot", "_robot": "folder-robot", "__robot__": "folder-robot", "src": "folder-src", ".src": "folder-src", "_src": "folder-src", "__src__": "folder-src", "srcs": "folder-src", ".srcs": "folder-src", "_srcs": "folder-src", "__srcs__": "folder-src", "source": "folder-src", ".source": "folder-src", "_source": "folder-src", "__source__": "folder-src", "sources": "folder-src", ".sources": "folder-src", "_sources": "folder-src", "__sources__": "folder-src", "code": "folder-src", ".code": "folder-src", "_code": "folder-src", "__code__": "folder-src", "dist": "folder-dist", ".dist": "folder-dist", "_dist": "folder-dist", "__dist__": "folder-dist", "out": "folder-dist", ".out": "folder-dist", "_out": "folder-dist", "__out__": "folder-dist", "build": "folder-dist", ".build": "folder-dist", "_build": "folder-dist", "__build__": "folder-dist", "release": "folder-dist", ".release": "folder-dist", "_release": "folder-dist", "__release__": "folder-dist", "bin": "folder-dist", ".bin": "folder-dist", "_bin": "folder-dist", "__bin__": "folder-dist", "css": "folder-css", ".css": "folder-css", "_css": "folder-css", "__css__": "folder-css", "stylesheet": "folder-css", ".stylesheet": "folder-css", "_stylesheet": "folder-css", "__stylesheet__": "folder-css", "stylesheets": "folder-css", ".stylesheets": "folder-css", "_stylesheets": "folder-css", "__stylesheets__": "folder-css", "style": "folder-css", ".style": "folder-css", "_style": "folder-css", "__style__": "folder-css", "styles": "folder-css", ".styles": "folder-css", "_styles": "folder-css", "__styles__": "folder-css", "sass": "folder-sass", ".sass": "folder-sass", "_sass": "folder-sass", "__sass__": "folder-sass", "scss": "folder-sass", ".scss": "folder-sass", "_scss": "folder-sass", "__scss__": "folder-sass", "tv": "folder-television", ".tv": "folder-television", "_tv": "folder-television", "__tv__": "folder-television", "television": "folder-television", ".television": "folder-television", "_television": "folder-television", "__television__": "folder-television", "desktop": "folder-desktop", ".desktop": "folder-desktop", "_desktop": "folder-desktop", "__desktop__": "folder-desktop", "console": "folder-console", ".console": "folder-console", "_console": "folder-console", "__console__": "folder-console", "images": "folder-images", ".images": "folder-images", "_images": "folder-images", "__images__": "folder-images", "image": "folder-images", ".image": "folder-images", "_image": "folder-images", "__image__": "folder-images", "imgs": "folder-images", ".imgs": "folder-images", "_imgs": "folder-images", "__imgs__": "folder-images", "img": "folder-images", ".img": "folder-images", "_img": "folder-images", "__img__": "folder-images", "icons": "folder-images", ".icons": "folder-images", "_icons": "folder-images", "__icons__": "folder-images", "icon": "folder-images", ".icon": "folder-images", "_icon": "folder-images", "__icon__": "folder-images", "icos": "folder-images", ".icos": "folder-images", "_icos": "folder-images", "__icos__": "folder-images", "ico": "folder-images", ".ico": "folder-images", "_ico": "folder-images", "__ico__": "folder-images", "figures": "folder-images", ".figures": "folder-images", "_figures": "folder-images", "__figures__": "folder-images", "figure": "folder-images", ".figure": "folder-images", "_figure": "folder-images", "__figure__": "folder-images", "figs": "folder-images", ".figs": "folder-images", "_figs": "folder-images", "__figs__": "folder-images", "fig": "folder-images", ".fig": "folder-images", "_fig": "folder-images", "__fig__": "folder-images", "screenshot": "folder-images", ".screenshot": "folder-images", "_screenshot": "folder-images", "__screenshot__": "folder-images", "screenshots": "folder-images", ".screenshots": "folder-images", "_screenshots": "folder-images", "__screenshots__": "folder-images", "screengrab": "folder-images", ".screengrab": "folder-images", "_screengrab": "folder-images", "__screengrab__": "folder-images", "screengrabs": "folder-images", ".screengrabs": "folder-images", "_screengrabs": "folder-images", "__screengrabs__": "folder-images", "pic": "folder-images", ".pic": "folder-images", "_pic": "folder-images", "__pic__": "folder-images", "pics": "folder-images", ".pics": "folder-images", "_pics": "folder-images", "__pics__": "folder-images", "picture": "folder-images", ".picture": "folder-images", "_picture": "folder-images", "__picture__": "folder-images", "pictures": "folder-images", ".pictures": "folder-images", "_pictures": "folder-images", "__pictures__": "folder-images", "photo": "folder-images", ".photo": "folder-images", "_photo": "folder-images", "__photo__": "folder-images", "photos": "folder-images", ".photos": "folder-images", "_photos": "folder-images", "__photos__": "folder-images", "photograph": "folder-images", ".photograph": "folder-images", "_photograph": "folder-images", "__photograph__": "folder-images", "photographs": "folder-images", ".photographs": "folder-images", "_photographs": "folder-images", "__photographs__": "folder-images", "script": "folder-scripts", ".script": "folder-scripts", "_script": "folder-scripts", "__script__": "folder-scripts", "scripts": "folder-scripts", ".scripts": "folder-scripts", "_scripts": "folder-scripts", "__scripts__": "folder-scripts", "scripting": "folder-scripts", ".scripting": "folder-scripts", "_scripting": "folder-scripts", "__scripting__": "folder-scripts", "node_modules": "folder-node", ".node_modules": "folder-node", "_node_modules": "folder-node", "__node_modules__": "folder-node", "js": "folder-javascript", ".js": "folder-javascript", "_js": "folder-javascript", "__js__": "folder-javascript", "javascript": "folder-javascript", ".javascript": "folder-javascript", "_javascript": "folder-javascript", "__javascript__": "folder-javascript", "javascripts": "folder-javascript", ".javascripts": "folder-javascript", "_javascripts": "folder-javascript", "__javascripts__": "folder-javascript", "json": "folder-json", ".json": "folder-json", "_json": "folder-json", "__json__": "folder-json", "jsons": "folder-json", ".jsons": "folder-json", "_jsons": "folder-json", "__jsons__": "folder-json", "font": "folder-font", ".font": "folder-font", "_font": "folder-font", "__font__": "folder-font", "fonts": "folder-font", ".fonts": "folder-font", "_fonts": "folder-font", "__fonts__": "folder-font", "bower_components": "folder-bower", ".bower_components": "folder-bower", "_bower_components": "folder-bower", "__bower_components__": "folder-bower", "test": "folder-test", ".test": "folder-test", "_test": "folder-test", "__test__": "folder-test", "tests": "folder-test", ".tests": "folder-test", "_tests": "folder-test", "__tests__": "folder-test", "testing": "folder-test", ".testing": "folder-test", "_testing": "folder-test", "__testing__": "folder-test", "snapshots": "folder-test", ".snapshots": "folder-test", "_snapshots": "folder-test", "__snapshots__": "folder-test", "spec": "folder-test", ".spec": "folder-test", "_spec": "folder-test", "__spec__": "folder-test", "specs": "folder-test", ".specs": "folder-test", "_specs": "folder-test", "__specs__": "folder-test", "jinja": "folder-jinja", ".jinja": "folder-jinja", "_jinja": "folder-jinja", "__jinja__": "folder-jinja", "jinja2": "folder-jinja", ".jinja2": "folder-jinja", "_jinja2": "folder-jinja", "__jinja2__": "folder-jinja", "j2": "folder-jinja", ".j2": "folder-jinja", "_j2": "folder-jinja", "__j2__": "folder-jinja", "markdown": "folder-markdown", ".markdown": "folder-markdown", "_markdown": "folder-markdown", "__markdown__": "folder-markdown", "md": "folder-markdown", ".md": "folder-markdown", "_md": "folder-markdown", "__md__": "folder-markdown", "pdm-plugins": "folder-pdm", ".pdm-plugins": "folder-pdm", "_pdm-plugins": "folder-pdm", "__pdm-plugins__": "folder-pdm", "pdm-build": "folder-pdm", ".pdm-build": "folder-pdm", "_pdm-build": "folder-pdm", "__pdm-build__": "folder-pdm", "php": "folder-php", ".php": "folder-php", "_php": "folder-php", "__php__": "folder-php", "phpmailer": "folder-phpmailer", ".phpmailer": "folder-phpmailer", "_phpmailer": "folder-phpmailer", "__phpmailer__": "folder-phpmailer", "sublime": "folder-sublime", ".sublime": "folder-sublime", "_sublime": "folder-sublime", "__sublime__": "folder-sublime", "doc": "folder-docs", ".doc": "folder-docs", "_doc": "folder-docs", "__doc__": "folder-docs", "docs": "folder-docs", ".docs": "folder-docs", "_docs": "folder-docs", "__docs__": "folder-docs", "document": "folder-docs", ".document": "folder-docs", "_document": "folder-docs", "__document__": "folder-docs", "documents": "folder-docs", ".documents": "folder-docs", "_documents": "folder-docs", "__documents__": "folder-docs", "documentation": "folder-docs", ".documentation": "folder-docs", "_documentation": "folder-docs", "__documentation__": "folder-docs", "post": "folder-docs", ".post": "folder-docs", "_post": "folder-docs", "__post__": "folder-docs", "posts": "folder-docs", ".posts": "folder-docs", "_posts": "folder-docs", "__posts__": "folder-docs", "article": "folder-docs", ".article": "folder-docs", "_article": "folder-docs", "__article__": "folder-docs", "articles": "folder-docs", ".articles": "folder-docs", "_articles": "folder-docs", "__articles__": "folder-docs", "git": "folder-git", ".git": "folder-git", "_git": "folder-git", "__git__": "folder-git", "patches": "folder-git", ".patches": "folder-git", "_patches": "folder-git", "__patches__": "folder-git", "githooks": "folder-git", ".githooks": "folder-git", "_githooks": "folder-git", "__githooks__": "folder-git", "submodules": "folder-git", ".submodules": "folder-git", "_submodules": "folder-git", "__submodules__": "folder-git", "github": "folder-github", ".github": "folder-github", "_github": "folder-github", "__github__": "folder-github", "gitlab": "folder-gitlab", ".gitlab": "folder-gitlab", "_gitlab": "folder-gitlab", "__gitlab__": "folder-gitlab", "vscode": "folder-vscode", ".vscode": "folder-vscode", "_vscode": "folder-vscode", "__vscode__": "folder-vscode", "vscode-test": "folder-vscode", ".vscode-test": "folder-vscode", "_vscode-test": "folder-vscode", "__vscode-test__": "folder-vscode", "view": "folder-views", ".view": "folder-views", "_view": "folder-views", "__view__": "folder-views", "views": "folder-views", ".views": "folder-views", "_views": "folder-views", "__views__": "folder-views", "screen": "folder-views", ".screen": "folder-views", "_screen": "folder-views", "__screen__": "folder-views", "screens": "folder-views", ".screens": "folder-views", "_screens": "folder-views", "__screens__": "folder-views", "page": "folder-views", ".page": "folder-views", "_page": "folder-views", "__page__": "folder-views", "pages": "folder-views", ".pages": "folder-views", "_pages": "folder-views", "__pages__": "folder-views", "public_html": "folder-views", ".public_html": "folder-views", "_public_html": "folder-views", "__public_html__": "folder-views", "html": "folder-views", ".html": "folder-views", "_html": "folder-views", "__html__": "folder-views", "vue": "folder-vue", ".vue": "folder-vue", "_vue": "folder-vue", "__vue__": "folder-vue", "vuepress": "folder-vuepress", ".vuepress": "folder-vuepress", "_vuepress": "folder-vuepress", "__vuepress__": "folder-vuepress", "expo": "folder-expo", ".expo": "folder-expo", "_expo": "folder-expo", "__expo__": "folder-expo", "expo-shared": "folder-expo", ".expo-shared": "folder-expo", "_expo-shared": "folder-expo", "__expo-shared__": "folder-expo", "cfg": "folder-config", ".cfg": "folder-config", "_cfg": "folder-config", "__cfg__": "folder-config", "cfgs": "folder-config", ".cfgs": "folder-config", "_cfgs": "folder-config", "__cfgs__": "folder-config", "conf": "folder-config", ".conf": "folder-config", "_conf": "folder-config", "__conf__": "folder-config", "confs": "folder-config", ".confs": "folder-config", "_confs": "folder-config", "__confs__": "folder-config", "config": "folder-config", ".config": "folder-config", "_config": "folder-config", "__config__": "folder-config", "configs": "folder-config", ".configs": "folder-config", "_configs": "folder-config", "__configs__": "folder-config", "configuration": "folder-config", ".configuration": "folder-config", "_configuration": "folder-config", "__configuration__": "folder-config", "configurations": "folder-config", ".configurations": "folder-config", "_configurations": "folder-config", "__configurations__": "folder-config", "setting": "folder-config", ".setting": "folder-config", "_setting": "folder-config", "__setting__": "folder-config", "settings": "folder-config", ".settings": "folder-config", "_settings": "folder-config", "__settings__": "folder-config", "META-INF": "folder-config", ".META-INF": "folder-config", "_META-INF": "folder-config", "__META-INF__": "folder-config", "option": "folder-config", ".option": "folder-config", "_option": "folder-config", "__option__": "folder-config", "options": "folder-config", ".options": "folder-config", "_options": "folder-config", "__options__": "folder-config", "i18n": "folder-i18n", ".i18n": "folder-i18n", "_i18n": "folder-i18n", "__i18n__": "folder-i18n", "internationalization": "folder-i18n", ".internationalization": "folder-i18n", "_internationalization": "folder-i18n", "__internationalization__": "folder-i18n", "lang": "folder-i18n", ".lang": "folder-i18n", "_lang": "folder-i18n", "__lang__": "folder-i18n", "langs": "folder-i18n", ".langs": "folder-i18n", "_langs": "folder-i18n", "__langs__": "folder-i18n", "language": "folder-i18n", ".language": "folder-i18n", "_language": "folder-i18n", "__language__": "folder-i18n", "languages": "folder-i18n", ".languages": "folder-i18n", "_languages": "folder-i18n", "__languages__": "folder-i18n", "locale": "folder-i18n", ".locale": "folder-i18n", "_locale": "folder-i18n", "__locale__": "folder-i18n", "locales": "folder-i18n", ".locales": "folder-i18n", "_locales": "folder-i18n", "__locales__": "folder-i18n", "l10n": "folder-i18n", ".l10n": "folder-i18n", "_l10n": "folder-i18n", "__l10n__": "folder-i18n", "localization": "folder-i18n", ".localization": "folder-i18n", "_localization": "folder-i18n", "__localization__": "folder-i18n", "translation": "folder-i18n", ".translation": "folder-i18n", "_translation": "folder-i18n", "__translation__": "folder-i18n", "translate": "folder-i18n", ".translate": "folder-i18n", "_translate": "folder-i18n", "__translate__": "folder-i18n", "translations": "folder-i18n", ".translations": "folder-i18n", "_translations": "folder-i18n", "__translations__": "folder-i18n", "tx": "folder-i18n", ".tx": "folder-i18n", "_tx": "folder-i18n", "__tx__": "folder-i18n", "components": "folder-components", ".components": "folder-components", "_components": "folder-components", "__components__": "folder-components", "widget": "folder-components", ".widget": "folder-components", "_widget": "folder-components", "__widget__": "folder-components", "widgets": "folder-components", ".widgets": "folder-components", "_widgets": "folder-components", "__widgets__": "folder-components", "fragments": "folder-components", ".fragments": "folder-components", "_fragments": "folder-components", "__fragments__": "folder-components", "verdaccio": "folder-ve<PERSON><PERSON><PERSON>", ".verdaccio": "folder-ve<PERSON><PERSON><PERSON>", "_verdaccio": "folder-ve<PERSON><PERSON><PERSON>", "__verdaccio__": "folder-ve<PERSON><PERSON><PERSON>", "aurelia_project": "folder-aurelia", ".aurelia_project": "folder-aurelia", "_aurelia_project": "folder-aurelia", "__aurelia_project__": "folder-aurelia", "resource": "folder-resource", ".resource": "folder-resource", "_resource": "folder-resource", "__resource__": "folder-resource", "resources": "folder-resource", ".resources": "folder-resource", "_resources": "folder-resource", "__resources__": "folder-resource", "res": "folder-resource", ".res": "folder-resource", "_res": "folder-resource", "__res__": "folder-resource", "asset": "folder-resource", ".asset": "folder-resource", "_asset": "folder-resource", "__asset__": "folder-resource", "assets": "folder-resource", ".assets": "folder-resource", "_assets": "folder-resource", "__assets__": "folder-resource", "static": "folder-resource", ".static": "folder-resource", "_static": "folder-resource", "__static__": "folder-resource", "report": "folder-resource", ".report": "folder-resource", "_report": "folder-resource", "__report__": "folder-resource", "reports": "folder-resource", ".reports": "folder-resource", "_reports": "folder-resource", "__reports__": "folder-resource", "lib": "folder-lib", ".lib": "folder-lib", "_lib": "folder-lib", "__lib__": "folder-lib", "libs": "folder-lib", ".libs": "folder-lib", "_libs": "folder-lib", "__libs__": "folder-lib", "library": "folder-lib", ".library": "folder-lib", "_library": "folder-lib", "__library__": "folder-lib", "libraries": "folder-lib", ".libraries": "folder-lib", "_libraries": "folder-lib", "__libraries__": "folder-lib", "vendor": "folder-lib", ".vendor": "folder-lib", "_vendor": "folder-lib", "__vendor__": "folder-lib", "vendors": "folder-lib", ".vendors": "folder-lib", "_vendors": "folder-lib", "__vendors__": "folder-lib", "third-party": "folder-lib", ".third-party": "folder-lib", "_third-party": "folder-lib", "__third-party__": "folder-lib", "themes": "folder-theme", ".themes": "folder-theme", "_themes": "folder-theme", "__themes__": "folder-theme", "theme": "folder-theme", ".theme": "folder-theme", "_theme": "folder-theme", "__theme__": "folder-theme", "color": "folder-theme", ".color": "folder-theme", "_color": "folder-theme", "__color__": "folder-theme", "colors": "folder-theme", ".colors": "folder-theme", "_colors": "folder-theme", "__colors__": "folder-theme", "design": "folder-theme", ".design": "folder-theme", "_design": "folder-theme", "__design__": "folder-theme", "designs": "folder-theme", ".designs": "folder-theme", "_designs": "folder-theme", "__designs__": "folder-theme", "webpack": "folder-webpack", ".webpack": "folder-webpack", "_webpack": "folder-webpack", "__webpack__": "folder-webpack", "global": "folder-global", ".global": "folder-global", "_global": "folder-global", "__global__": "folder-global", "public": "folder-public", ".public": "folder-public", "_public": "folder-public", "__public__": "folder-public", "www": "folder-public", ".www": "folder-public", "_www": "folder-public", "__www__": "folder-public", "wwwroot": "folder-public", ".wwwroot": "folder-public", "_wwwroot": "folder-public", "__wwwroot__": "folder-public", "web": "folder-public", ".web": "folder-public", "_web": "folder-public", "__web__": "folder-public", "website": "folder-public", ".website": "folder-public", "_website": "folder-public", "__website__": "folder-public", "site": "folder-public", ".site": "folder-public", "_site": "folder-public", "__site__": "folder-public", "browser": "folder-public", ".browser": "folder-public", "_browser": "folder-public", "__browser__": "folder-public", "browsers": "folder-public", ".browsers": "folder-public", "_browsers": "folder-public", "__browsers__": "folder-public", "inc": "folder-include", ".inc": "folder-include", "_inc": "folder-include", "__inc__": "folder-include", "include": "folder-include", ".include": "folder-include", "_include": "folder-include", "__include__": "folder-include", "includes": "folder-include", ".includes": "folder-include", "_includes": "folder-include", "__includes__": "folder-include", "partial": "folder-include", ".partial": "folder-include", "_partial": "folder-include", "__partial__": "folder-include", "partials": "folder-include", ".partials": "folder-include", "_partials": "folder-include", "__partials__": "folder-include", "docker": "folder-docker", ".docker": "folder-docker", "_docker": "folder-docker", "__docker__": "folder-docker", "dockerfiles": "folder-docker", ".dockerfiles": "folder-docker", "_dockerfiles": "folder-docker", "__dockerfiles__": "folder-docker", "db": "folder-database", ".db": "folder-database", "_db": "folder-database", "__db__": "folder-database", "data": "folder-database", ".data": "folder-database", "_data": "folder-database", "__data__": "folder-database", "database": "folder-database", ".database": "folder-database", "_database": "folder-database", "__database__": "folder-database", "databases": "folder-database", ".databases": "folder-database", "_databases": "folder-database", "__databases__": "folder-database", "sql": "folder-database", ".sql": "folder-database", "_sql": "folder-database", "__sql__": "folder-database", "log": "folder-log", ".log": "folder-log", "_log": "folder-log", "__log__": "folder-log", "logs": "folder-log", ".logs": "folder-log", "_logs": "folder-log", "__logs__": "folder-log", "logging": "folder-log", ".logging": "folder-log", "_logging": "folder-log", "__logging__": "folder-log", "target": "folder-target", ".target": "folder-target", "_target": "folder-target", "__target__": "folder-target", "temp": "folder-temp", ".temp": "folder-temp", "_temp": "folder-temp", "__temp__": "folder-temp", "tmp": "folder-temp", ".tmp": "folder-temp", "_tmp": "folder-temp", "__tmp__": "folder-temp", "cached": "folder-temp", ".cached": "folder-temp", "_cached": "folder-temp", "__cached__": "folder-temp", "cache": "folder-temp", ".cache": "folder-temp", "_cache": "folder-temp", "__cache__": "folder-temp", "aws": "folder-aws", ".aws": "folder-aws", "_aws": "folder-aws", "__aws__": "folder-aws", "aud": "folder-audio", ".aud": "folder-audio", "_aud": "folder-audio", "__aud__": "folder-audio", "auds": "folder-audio", ".auds": "folder-audio", "_auds": "folder-audio", "__auds__": "folder-audio", "audio": "folder-audio", ".audio": "folder-audio", "_audio": "folder-audio", "__audio__": "folder-audio", "audios": "folder-audio", ".audios": "folder-audio", "_audios": "folder-audio", "__audios__": "folder-audio", "music": "folder-audio", ".music": "folder-audio", "_music": "folder-audio", "__music__": "folder-audio", "sound": "folder-audio", ".sound": "folder-audio", "_sound": "folder-audio", "__sound__": "folder-audio", "sounds": "folder-audio", ".sounds": "folder-audio", "_sounds": "folder-audio", "__sounds__": "folder-audio", "vid": "folder-video", ".vid": "folder-video", "_vid": "folder-video", "__vid__": "folder-video", "vids": "folder-video", ".vids": "folder-video", "_vids": "folder-video", "__vids__": "folder-video", "video": "folder-video", ".video": "folder-video", "_video": "folder-video", "__video__": "folder-video", "videos": "folder-video", ".videos": "folder-video", "_videos": "folder-video", "__videos__": "folder-video", "movie": "folder-video", ".movie": "folder-video", "_movie": "folder-video", "__movie__": "folder-video", "movies": "folder-video", ".movies": "folder-video", "_movies": "folder-video", "__movies__": "folder-video", "kubernetes": "folder-kubernetes", ".kubernetes": "folder-kubernetes", "_kubernetes": "folder-kubernetes", "__kubernetes__": "folder-kubernetes", "k8s": "folder-kubernetes", ".k8s": "folder-kubernetes", "_k8s": "folder-kubernetes", "__k8s__": "folder-kubernetes", "import": "folder-import", ".import": "folder-import", "_import": "folder-import", "__import__": "folder-import", "imports": "folder-import", ".imports": "folder-import", "_imports": "folder-import", "__imports__": "folder-import", "imported": "folder-import", ".imported": "folder-import", "_imported": "folder-import", "__imported__": "folder-import", "export": "folder-export", ".export": "folder-export", "_export": "folder-export", "__export__": "folder-export", "exports": "folder-export", ".exports": "folder-export", "_exports": "folder-export", "__exports__": "folder-export", "exported": "folder-export", ".exported": "folder-export", "_exported": "folder-export", "__exported__": "folder-export", "wakatime": "folder-wakatime", ".wakatime": "folder-wakatime", "_wakatime": "folder-wakatime", "__wakatime__": "folder-wakatime", "circleci": "folder-circleci", ".circleci": "folder-circleci", "_circleci": "folder-circleci", "__circleci__": "folder-circleci", "wordpress-org": "folder-wordpress", ".wordpress-org": "folder-wordpress", "_wordpress-org": "folder-wordpress", "__wordpress-org__": "folder-wordpress", "wp-content": "folder-wordpress", ".wp-content": "folder-wordpress", "_wp-content": "folder-wordpress", "__wp-content__": "folder-wordpress", "gradle": "folder-gradle", ".gradle": "folder-gradle", "_gradle": "folder-gradle", "__gradle__": "folder-gradle", "coverage": "folder-coverage", ".coverage": "folder-coverage", "_coverage": "folder-coverage", "__coverage__": "folder-coverage", "nyc-output": "folder-coverage", ".nyc-output": "folder-coverage", "_nyc-output": "folder-coverage", "__nyc-output__": "folder-coverage", "nyc_output": "folder-coverage", ".nyc_output": "folder-coverage", "_nyc_output": "folder-coverage", "__nyc_output__": "folder-coverage", "e2e": "folder-coverage", ".e2e": "folder-coverage", "_e2e": "folder-coverage", "__e2e__": "folder-coverage", "it": "folder-coverage", ".it": "folder-coverage", "_it": "folder-coverage", "__it__": "folder-coverage", "integration-test": "folder-coverage", ".integration-test": "folder-coverage", "_integration-test": "folder-coverage", "__integration-test__": "folder-coverage", "integration-tests": "folder-coverage", ".integration-tests": "folder-coverage", "_integration-tests": "folder-coverage", "__integration-tests__": "folder-coverage", "class": "folder-class", ".class": "folder-class", "_class": "folder-class", "__class__": "folder-class", "classes": "folder-class", ".classes": "folder-class", "_classes": "folder-class", "__classes__": "folder-class", "model": "folder-class", ".model": "folder-class", "_model": "folder-class", "__model__": "folder-class", "models": "folder-class", ".models": "folder-class", "_models": "folder-class", "__models__": "folder-class", "schemas": "folder-class", ".schemas": "folder-class", "_schemas": "folder-class", "__schemas__": "folder-class", "schema": "folder-class", ".schema": "folder-class", "_schema": "folder-class", "__schema__": "folder-class", "other": "folder-other", ".other": "folder-other", "_other": "folder-other", "__other__": "folder-other", "others": "folder-other", ".others": "folder-other", "_others": "folder-other", "__others__": "folder-other", "misc": "folder-other", ".misc": "folder-other", "_misc": "folder-other", "__misc__": "folder-other", "miscellaneous": "folder-other", ".miscellaneous": "folder-other", "_miscellaneous": "folder-other", "__miscellaneous__": "folder-other", "extra": "folder-other", ".extra": "folder-other", "_extra": "folder-other", "__extra__": "folder-other", "extras": "folder-other", ".extras": "folder-other", "_extras": "folder-other", "__extras__": "folder-other", "etc": "folder-other", ".etc": "folder-other", "_etc": "folder-other", "__etc__": "folder-other", "lua": "folder-lua", ".lua": "folder-lua", "_lua": "folder-lua", "__lua__": "folder-lua", "typescript": "folder-typescript", ".typescript": "folder-typescript", "_typescript": "folder-typescript", "__typescript__": "folder-typescript", "ts": "folder-typescript", ".ts": "folder-typescript", "_ts": "folder-typescript", "__ts__": "folder-typescript", "typings": "folder-typescript", ".typings": "folder-typescript", "_typings": "folder-typescript", "__typings__": "folder-typescript", "@types": "folder-typescript", ".@types": "folder-typescript", "_@types": "folder-typescript", "__@types__": "folder-typescript", "types": "folder-typescript", ".types": "folder-typescript", "_types": "folder-typescript", "__types__": "folder-typescript", "graphql": "folder-graphql", ".graphql": "folder-graphql", "_graphql": "folder-graphql", "__graphql__": "folder-graphql", "gql": "folder-graphql", ".gql": "folder-graphql", "_gql": "folder-graphql", "__gql__": "folder-graphql", "routes": "folder-routes", ".routes": "folder-routes", "_routes": "folder-routes", "__routes__": "folder-routes", "router": "folder-routes", ".router": "folder-routes", "_router": "folder-routes", "__router__": "folder-routes", "routers": "folder-routes", ".routers": "folder-routes", "_routers": "folder-routes", "__routers__": "folder-routes", "ci": "folder-ci", ".ci": "folder-ci", "_ci": "folder-ci", "__ci__": "folder-ci", "benchmark": "folder-benchmark", ".benchmark": "folder-benchmark", "_benchmark": "folder-benchmark", "__benchmark__": "folder-benchmark", "benchmarks": "folder-benchmark", ".benchmarks": "folder-benchmark", "_benchmarks": "folder-benchmark", "__benchmarks__": "folder-benchmark", "performance": "folder-benchmark", ".performance": "folder-benchmark", "_performance": "folder-benchmark", "__performance__": "folder-benchmark", "measure": "folder-benchmark", ".measure": "folder-benchmark", "_measure": "folder-benchmark", "__measure__": "folder-benchmark", "measures": "folder-benchmark", ".measures": "folder-benchmark", "_measures": "folder-benchmark", "__measures__": "folder-benchmark", "measurement": "folder-benchmark", ".measurement": "folder-benchmark", "_measurement": "folder-benchmark", "__measurement__": "folder-benchmark", "messages": "folder-messages", ".messages": "folder-messages", "_messages": "folder-messages", "__messages__": "folder-messages", "messaging": "folder-messages", ".messaging": "folder-messages", "_messaging": "folder-messages", "__messaging__": "folder-messages", "forum": "folder-messages", ".forum": "folder-messages", "_forum": "folder-messages", "__forum__": "folder-messages", "chat": "folder-messages", ".chat": "folder-messages", "_chat": "folder-messages", "__chat__": "folder-messages", "chats": "folder-messages", ".chats": "folder-messages", "_chats": "folder-messages", "__chats__": "folder-messages", "conversation": "folder-messages", ".conversation": "folder-messages", "_conversation": "folder-messages", "__conversation__": "folder-messages", "conversations": "folder-messages", ".conversations": "folder-messages", "_conversations": "folder-messages", "__conversations__": "folder-messages", "less": "folder-less", ".less": "folder-less", "_less": "folder-less", "__less__": "folder-less", "gulp": "folder-gulp", ".gulp": "folder-gulp", "_gulp": "folder-gulp", "__gulp__": "folder-gulp", "gulp-tasks": "folder-gulp", ".gulp-tasks": "folder-gulp", "_gulp-tasks": "folder-gulp", "__gulp-tasks__": "folder-gulp", "gulpfile.js": "folder-gulp", ".gulpfile.js": "folder-gulp", "_gulpfile.js": "folder-gulp", "__gulpfile.js__": "folder-gulp", "gulpfile.mjs": "folder-gulp", ".gulpfile.mjs": "folder-gulp", "_gulpfile.mjs": "folder-gulp", "__gulpfile.mjs__": "folder-gulp", "gulpfile.ts": "folder-gulp", ".gulpfile.ts": "folder-gulp", "_gulpfile.ts": "folder-gulp", "__gulpfile.ts__": "folder-gulp", "gulpfile.babel.js": "folder-gulp", ".gulpfile.babel.js": "folder-gulp", "_gulpfile.babel.js": "folder-gulp", "__gulpfile.babel.js__": "folder-gulp", "python": "folder-python", ".python": "folder-python", "_python": "folder-python", "__python__": "folder-python", "pycache": "folder-python", ".pycache": "folder-python", "_pycache": "folder-python", "__pycache__": "folder-python", "pytest_cache": "folder-python", ".pytest_cache": "folder-python", "_pytest_cache": "folder-python", "__pytest_cache__": "folder-python", "mojo": "folder-mojo", ".mojo": "folder-mojo", "_mojo": "folder-mojo", "__mojo__": "folder-mojo", "moon": "folder-moon", ".moon": "folder-moon", "_moon": "folder-moon", "__moon__": "folder-moon", "debug": "folder-debug", ".debug": "folder-debug", "_debug": "folder-debug", "__debug__": "folder-debug", "debugging": "folder-debug", ".debugging": "folder-debug", "_debugging": "folder-debug", "__debugging__": "folder-debug", "fastlane": "folder-fastlane", ".fastlane": "folder-fastlane", "_fastlane": "folder-fastlane", "__fastlane__": "folder-fastlane", "plugin": "folder-plugin", ".plugin": "folder-plugin", "_plugin": "folder-plugin", "__plugin__": "folder-plugin", "plugins": "folder-plugin", ".plugins": "folder-plugin", "_plugins": "folder-plugin", "__plugins__": "folder-plugin", "mod": "folder-plugin", ".mod": "folder-plugin", "_mod": "folder-plugin", "__mod__": "folder-plugin", "mods": "folder-plugin", ".mods": "folder-plugin", "_mods": "folder-plugin", "__mods__": "folder-plugin", "modding": "folder-plugin", ".modding": "folder-plugin", "_modding": "folder-plugin", "__modding__": "folder-plugin", "extension": "folder-plugin", ".extension": "folder-plugin", "_extension": "folder-plugin", "__extension__": "folder-plugin", "extensions": "folder-plugin", ".extensions": "folder-plugin", "_extensions": "folder-plugin", "__extensions__": "folder-plugin", "addon": "folder-plugin", ".addon": "folder-plugin", "_addon": "folder-plugin", "__addon__": "folder-plugin", "addons": "folder-plugin", ".addons": "folder-plugin", "_addons": "folder-plugin", "__addons__": "folder-plugin", "module": "folder-plugin", ".module": "folder-plugin", "_module": "folder-plugin", "__module__": "folder-plugin", "modules": "folder-plugin", ".modules": "folder-plugin", "_modules": "folder-plugin", "__modules__": "folder-plugin", "middleware": "folder-middleware", ".middleware": "folder-middleware", "_middleware": "folder-middleware", "__middleware__": "folder-middleware", "middlewares": "folder-middleware", ".middlewares": "folder-middleware", "_middlewares": "folder-middleware", "__middlewares__": "folder-middleware", "controller": "folder-controller", ".controller": "folder-controller", "_controller": "folder-controller", "__controller__": "folder-controller", "controllers": "folder-controller", ".controllers": "folder-controller", "_controllers": "folder-controller", "__controllers__": "folder-controller", "service": "folder-controller", ".service": "folder-controller", "_service": "folder-controller", "__service__": "folder-controller", "services": "folder-controller", ".services": "folder-controller", "_services": "folder-controller", "__services__": "folder-controller", "provider": "folder-controller", ".provider": "folder-controller", "_provider": "folder-controller", "__provider__": "folder-controller", "providers": "folder-controller", ".providers": "folder-controller", "_providers": "folder-controller", "__providers__": "folder-controller", "handler": "folder-controller", ".handler": "folder-controller", "_handler": "folder-controller", "__handler__": "folder-controller", "handlers": "folder-controller", ".handlers": "folder-controller", "_handlers": "folder-controller", "__handlers__": "folder-controller", "ansible": "folder-ansible", ".ansible": "folder-ansible", "_ansible": "folder-ansible", "__ansible__": "folder-ansible", "server": "folder-server", ".server": "folder-server", "_server": "folder-server", "__server__": "folder-server", "servers": "folder-server", ".servers": "folder-server", "_servers": "folder-server", "__servers__": "folder-server", "backend": "folder-server", ".backend": "folder-server", "_backend": "folder-server", "__backend__": "folder-server", "backends": "folder-server", ".backends": "folder-server", "_backends": "folder-server", "__backends__": "folder-server", "client": "folder-client", ".client": "folder-client", "_client": "folder-client", "__client__": "folder-client", "clients": "folder-client", ".clients": "folder-client", "_clients": "folder-client", "__clients__": "folder-client", "frontend": "folder-client", ".frontend": "folder-client", "_frontend": "folder-client", "__frontend__": "folder-client", "frontends": "folder-client", ".frontends": "folder-client", "_frontends": "folder-client", "__frontends__": "folder-client", "pwa": "folder-client", ".pwa": "folder-client", "_pwa": "folder-client", "__pwa__": "folder-client", "tasks": "folder-tasks", ".tasks": "folder-tasks", "_tasks": "folder-tasks", "__tasks__": "folder-tasks", "tickets": "folder-tasks", ".tickets": "folder-tasks", "_tickets": "folder-tasks", "__tickets__": "folder-tasks", "android": "folder-android", ".android": "folder-android", "_android": "folder-android", "__android__": "folder-android", "ios": "folder-ios", ".ios": "folder-ios", "_ios": "folder-ios", "__ios__": "folder-ios", "uploads": "folder-upload", ".uploads": "folder-upload", "_uploads": "folder-upload", "__uploads__": "folder-upload", "upload": "folder-upload", ".upload": "folder-upload", "_upload": "folder-upload", "__upload__": "folder-upload", "downloads": "folder-download", ".downloads": "folder-download", "_downloads": "folder-download", "__downloads__": "folder-download", "download": "folder-download", ".download": "folder-download", "_download": "folder-download", "__download__": "folder-download", "tools": "folder-tools", ".tools": "folder-tools", "_tools": "folder-tools", "__tools__": "folder-tools", "toolkit": "folder-tools", ".toolkit": "folder-tools", "_toolkit": "folder-tools", "__toolkit__": "folder-tools", "toolkits": "folder-tools", ".toolkits": "folder-tools", "_toolkits": "folder-tools", "__toolkits__": "folder-tools", "toolbox": "folder-tools", ".toolbox": "folder-tools", "_toolbox": "folder-tools", "__toolbox__": "folder-tools", "toolboxes": "folder-tools", ".toolboxes": "folder-tools", "_toolboxes": "folder-tools", "__toolboxes__": "folder-tools", "tooling": "folder-tools", ".tooling": "folder-tools", "_tooling": "folder-tools", "__tooling__": "folder-tools", "helpers": "folder-helper", ".helpers": "folder-helper", "_helpers": "folder-helper", "__helpers__": "folder-helper", "helper": "folder-helper", ".helper": "folder-helper", "_helper": "folder-helper", "__helper__": "folder-helper", "serverless": "folder-serverless", ".serverless": "folder-serverless", "_serverless": "folder-serverless", "__serverless__": "folder-serverless", "api": "folder-api", ".api": "folder-api", "_api": "folder-api", "__api__": "folder-api", "apis": "folder-api", ".apis": "folder-api", "_apis": "folder-api", "__apis__": "folder-api", "restapi": "folder-api", ".restapi": "folder-api", "_restapi": "folder-api", "__restapi__": "folder-api", "app": "folder-app", ".app": "folder-app", "_app": "folder-app", "__app__": "folder-app", "apps": "folder-app", ".apps": "folder-app", "_apps": "folder-app", "__apps__": "folder-app", "apollo": "folder-a<PERSON>lo", ".apollo": "folder-a<PERSON>lo", "_apollo": "folder-a<PERSON>lo", "__apollo__": "folder-a<PERSON>lo", "apollo-client": "folder-a<PERSON>lo", ".apollo-client": "folder-a<PERSON>lo", "_apollo-client": "folder-a<PERSON>lo", "__apollo-client__": "folder-a<PERSON>lo", "apollo-cache": "folder-a<PERSON>lo", ".apollo-cache": "folder-a<PERSON>lo", "_apollo-cache": "folder-a<PERSON>lo", "__apollo-cache__": "folder-a<PERSON>lo", "apollo-config": "folder-a<PERSON>lo", ".apollo-config": "folder-a<PERSON>lo", "_apollo-config": "folder-a<PERSON>lo", "__apollo-config__": "folder-a<PERSON>lo", "arc": "folder-archive", ".arc": "folder-archive", "_arc": "folder-archive", "__arc__": "folder-archive", "arcs": "folder-archive", ".arcs": "folder-archive", "_arcs": "folder-archive", "__arcs__": "folder-archive", "archive": "folder-archive", ".archive": "folder-archive", "_archive": "folder-archive", "__archive__": "folder-archive", "archives": "folder-archive", ".archives": "folder-archive", "_archives": "folder-archive", "__archives__": "folder-archive", "archival": "folder-archive", ".archival": "folder-archive", "_archival": "folder-archive", "__archival__": "folder-archive", "bkp": "folder-archive", ".bkp": "folder-archive", "_bkp": "folder-archive", "__bkp__": "folder-archive", "bkps": "folder-archive", ".bkps": "folder-archive", "_bkps": "folder-archive", "__bkps__": "folder-archive", "bak": "folder-archive", ".bak": "folder-archive", "_bak": "folder-archive", "__bak__": "folder-archive", "baks": "folder-archive", ".baks": "folder-archive", "_baks": "folder-archive", "__baks__": "folder-archive", "backup": "folder-archive", ".backup": "folder-archive", "_backup": "folder-archive", "__backup__": "folder-archive", "backups": "folder-archive", ".backups": "folder-archive", "_backups": "folder-archive", "__backups__": "folder-archive", "back-up": "folder-archive", ".back-up": "folder-archive", "_back-up": "folder-archive", "__back-up__": "folder-archive", "back-ups": "folder-archive", ".back-ups": "folder-archive", "_back-ups": "folder-archive", "__back-ups__": "folder-archive", "history": "folder-archive", ".history": "folder-archive", "_history": "folder-archive", "__history__": "folder-archive", "histories": "folder-archive", ".histories": "folder-archive", "_histories": "folder-archive", "__histories__": "folder-archive", "batch": "folder-batch", ".batch": "folder-batch", "_batch": "folder-batch", "__batch__": "folder-batch", "batchs": "folder-batch", ".batchs": "folder-batch", "_batchs": "folder-batch", "__batchs__": "folder-batch", "batches": "folder-batch", ".batches": "folder-batch", "_batches": "folder-batch", "__batches__": "folder-batch", "buildkite": "folder-buildkite", ".buildkite": "folder-buildkite", "_buildkite": "folder-buildkite", "__buildkite__": "folder-buildkite", "cluster": "folder-cluster", ".cluster": "folder-cluster", "_cluster": "folder-cluster", "__cluster__": "folder-cluster", "clusters": "folder-cluster", ".clusters": "folder-cluster", "_clusters": "folder-cluster", "__clusters__": "folder-cluster", "command": "folder-command", ".command": "folder-command", "_command": "folder-command", "__command__": "folder-command", "commands": "folder-command", ".commands": "folder-command", "_commands": "folder-command", "__commands__": "folder-command", "cmd": "folder-command", ".cmd": "folder-command", "_cmd": "folder-command", "__cmd__": "folder-command", "cli": "folder-command", ".cli": "folder-command", "_cli": "folder-command", "__cli__": "folder-command", "clis": "folder-command", ".clis": "folder-command", "_clis": "folder-command", "__clis__": "folder-command", "constant": "folder-constant", ".constant": "folder-constant", "_constant": "folder-constant", "__constant__": "folder-constant", "constants": "folder-constant", ".constants": "folder-constant", "_constants": "folder-constant", "__constants__": "folder-constant", "container": "folder-container", ".container": "folder-container", "_container": "folder-container", "__container__": "folder-container", "containers": "folder-container", ".containers": "folder-container", "_containers": "folder-container", "__containers__": "folder-container", "devcontainer": "folder-container", ".devcontainer": "folder-container", "_devcontainer": "folder-container", "__devcontainer__": "folder-container", "content": "folder-content", ".content": "folder-content", "_content": "folder-content", "__content__": "folder-content", "contents": "folder-content", ".contents": "folder-content", "_contents": "folder-content", "__contents__": "folder-content", "context": "folder-context", ".context": "folder-context", "_context": "folder-context", "__context__": "folder-context", "contexts": "folder-context", ".contexts": "folder-context", "_contexts": "folder-context", "__contexts__": "folder-context", "core": "folder-core", ".core": "folder-core", "_core": "folder-core", "__core__": "folder-core", "delta": "folder-delta", ".delta": "folder-delta", "_delta": "folder-delta", "__delta__": "folder-delta", "deltas": "folder-delta", ".deltas": "folder-delta", "_deltas": "folder-delta", "__deltas__": "folder-delta", "changes": "folder-delta", ".changes": "folder-delta", "_changes": "folder-delta", "__changes__": "folder-delta", "dump": "folder-dump", ".dump": "folder-dump", "_dump": "folder-dump", "__dump__": "folder-dump", "dumps": "folder-dump", ".dumps": "folder-dump", "_dumps": "folder-dump", "__dumps__": "folder-dump", "demo": "folder-examples", ".demo": "folder-examples", "_demo": "folder-examples", "__demo__": "folder-examples", "demos": "folder-examples", ".demos": "folder-examples", "_demos": "folder-examples", "__demos__": "folder-examples", "example": "folder-examples", ".example": "folder-examples", "_example": "folder-examples", "__example__": "folder-examples", "examples": "folder-examples", ".examples": "folder-examples", "_examples": "folder-examples", "__examples__": "folder-examples", "sample": "folder-examples", ".sample": "folder-examples", "_sample": "folder-examples", "__sample__": "folder-examples", "samples": "folder-examples", ".samples": "folder-examples", "_samples": "folder-examples", "__samples__": "folder-examples", "sample-data": "folder-examples", ".sample-data": "folder-examples", "_sample-data": "folder-examples", "__sample-data__": "folder-examples", "env": "folder-environment", ".env": "folder-environment", "_env": "folder-environment", "__env__": "folder-environment", "envs": "folder-environment", ".envs": "folder-environment", "_envs": "folder-environment", "__envs__": "folder-environment", "environment": "folder-environment", ".environment": "folder-environment", "_environment": "folder-environment", "__environment__": "folder-environment", "environments": "folder-environment", ".environments": "folder-environment", "_environments": "folder-environment", "__environments__": "folder-environment", "venv": "folder-environment", ".venv": "folder-environment", "_venv": "folder-environment", "__venv__": "folder-environment", "func": "folder-functions", ".func": "folder-functions", "_func": "folder-functions", "__func__": "folder-functions", "funcs": "folder-functions", ".funcs": "folder-functions", "_funcs": "folder-functions", "__funcs__": "folder-functions", "function": "folder-functions", ".function": "folder-functions", "_function": "folder-functions", "__function__": "folder-functions", "functions": "folder-functions", ".functions": "folder-functions", "_functions": "folder-functions", "__functions__": "folder-functions", "lambda": "folder-functions", ".lambda": "folder-functions", "_lambda": "folder-functions", "__lambda__": "folder-functions", "lambdas": "folder-functions", ".lambdas": "folder-functions", "_lambdas": "folder-functions", "__lambdas__": "folder-functions", "logic": "folder-functions", ".logic": "folder-functions", "_logic": "folder-functions", "__logic__": "folder-functions", "math": "folder-functions", ".math": "folder-functions", "_math": "folder-functions", "__math__": "folder-functions", "maths": "folder-functions", ".maths": "folder-functions", "_maths": "folder-functions", "__maths__": "folder-functions", "calc": "folder-functions", ".calc": "folder-functions", "_calc": "folder-functions", "__calc__": "folder-functions", "calcs": "folder-functions", ".calcs": "folder-functions", "_calcs": "folder-functions", "__calcs__": "folder-functions", "calculation": "folder-functions", ".calculation": "folder-functions", "_calculation": "folder-functions", "__calculation__": "folder-functions", "calculations": "folder-functions", ".calculations": "folder-functions", "_calculations": "folder-functions", "__calculations__": "folder-functions", "generator": "folder-generator", ".generator": "folder-generator", "_generator": "folder-generator", "__generator__": "folder-generator", "generators": "folder-generator", ".generators": "folder-generator", "_generators": "folder-generator", "__generators__": "folder-generator", "generated": "folder-generator", ".generated": "folder-generator", "_generated": "folder-generator", "__generated__": "folder-generator", "cfn-gen": "folder-generator", ".cfn-gen": "folder-generator", "_cfn-gen": "folder-generator", "__cfn-gen__": "folder-generator", "gen": "folder-generator", ".gen": "folder-generator", "_gen": "folder-generator", "__gen__": "folder-generator", "gens": "folder-generator", ".gens": "folder-generator", "_gens": "folder-generator", "__gens__": "folder-generator", "auto": "folder-generator", ".auto": "folder-generator", "_auto": "folder-generator", "__auto__": "folder-generator", "hook": "folder-hook", ".hook": "folder-hook", "_hook": "folder-hook", "__hook__": "folder-hook", "hooks": "folder-hook", ".hooks": "folder-hook", "_hooks": "folder-hook", "__hooks__": "folder-hook", "trigger": "folder-hook", ".trigger": "folder-hook", "_trigger": "folder-hook", "__trigger__": "folder-hook", "triggers": "folder-hook", ".triggers": "folder-hook", "_triggers": "folder-hook", "__triggers__": "folder-hook", "job": "folder-job", ".job": "folder-job", "_job": "folder-job", "__job__": "folder-job", "jobs": "folder-job", ".jobs": "folder-job", "_jobs": "folder-job", "__jobs__": "folder-job", "key": "folder-keys", ".key": "folder-keys", "_key": "folder-keys", "__key__": "folder-keys", "keys": "folder-keys", ".keys": "folder-keys", "_keys": "folder-keys", "__keys__": "folder-keys", "token": "folder-keys", ".token": "folder-keys", "_token": "folder-keys", "__token__": "folder-keys", "tokens": "folder-keys", ".tokens": "folder-keys", "_tokens": "folder-keys", "__tokens__": "folder-keys", "jwt": "folder-keys", ".jwt": "folder-keys", "_jwt": "folder-keys", "__jwt__": "folder-keys", "secret": "folder-keys", ".secret": "folder-keys", "_secret": "folder-keys", "__secret__": "folder-keys", "secrets": "folder-keys", ".secrets": "folder-keys", "_secrets": "folder-keys", "__secrets__": "folder-keys", "layout": "folder-layout", ".layout": "folder-layout", "_layout": "folder-layout", "__layout__": "folder-layout", "layouts": "folder-layout", ".layouts": "folder-layout", "_layouts": "folder-layout", "__layouts__": "folder-layout", "mail": "folder-mail", ".mail": "folder-mail", "_mail": "folder-mail", "__mail__": "folder-mail", "mails": "folder-mail", ".mails": "folder-mail", "_mails": "folder-mail", "__mails__": "folder-mail", "email": "folder-mail", ".email": "folder-mail", "_email": "folder-mail", "__email__": "folder-mail", "emails": "folder-mail", ".emails": "folder-mail", "_emails": "folder-mail", "__emails__": "folder-mail", "smtp": "folder-mail", ".smtp": "folder-mail", "_smtp": "folder-mail", "__smtp__": "folder-mail", "mailers": "folder-mail", ".mailers": "folder-mail", "_mailers": "folder-mail", "__mailers__": "folder-mail", "mappings": "folder-mappings", ".mappings": "folder-mappings", "_mappings": "folder-mappings", "__mappings__": "folder-mappings", "mapping": "folder-mappings", ".mapping": "folder-mappings", "_mapping": "folder-mappings", "__mapping__": "folder-mappings", "meta": "folder-meta", ".meta": "folder-meta", "_meta": "folder-meta", "__meta__": "folder-meta", "changesets": "folder-changesets", ".changesets": "folder-changesets", "_changesets": "folder-changesets", "__changesets__": "folder-changesets", "changeset": "folder-changesets", ".changeset": "folder-changesets", "_changeset": "folder-changesets", "__changeset__": "folder-changesets", "package": "folder-packages", ".package": "folder-packages", "_package": "folder-packages", "__package__": "folder-packages", "packages": "folder-packages", ".packages": "folder-packages", "_packages": "folder-packages", "__packages__": "folder-packages", "pkg": "folder-packages", ".pkg": "folder-packages", "_pkg": "folder-packages", "__pkg__": "folder-packages", "pkgs": "folder-packages", ".pkgs": "folder-packages", "_pkgs": "folder-packages", "__pkgs__": "folder-packages", "shared": "folder-shared", ".shared": "folder-shared", "_shared": "folder-shared", "__shared__": "folder-shared", "common": "folder-shared", ".common": "folder-shared", "_common": "folder-shared", "__common__": "folder-shared", "glsl": "folder-shader", ".glsl": "folder-shader", "_glsl": "folder-shader", "__glsl__": "folder-shader", "hlsl": "folder-shader", ".hlsl": "folder-shader", "_hlsl": "folder-shader", "__hlsl__": "folder-shader", "shader": "folder-shader", ".shader": "folder-shader", "_shader": "folder-shader", "__shader__": "folder-shader", "shaders": "folder-shader", ".shaders": "folder-shader", "_shaders": "folder-shader", "__shaders__": "folder-shader", "stack": "folder-stack", ".stack": "folder-stack", "_stack": "folder-stack", "__stack__": "folder-stack", "stacks": "folder-stack", ".stacks": "folder-stack", "_stacks": "folder-stack", "__stacks__": "folder-stack", "template": "folder-template", ".template": "folder-template", "_template": "folder-template", "__template__": "folder-template", "templates": "folder-template", ".templates": "folder-template", "_templates": "folder-template", "__templates__": "folder-template", "util": "folder-utils", ".util": "folder-utils", "_util": "folder-utils", "__util__": "folder-utils", "utils": "folder-utils", ".utils": "folder-utils", "_utils": "folder-utils", "__utils__": "folder-utils", "utility": "folder-utils", ".utility": "folder-utils", "_utility": "folder-utils", "__utility__": "folder-utils", "utilities": "folder-utils", ".utilities": "folder-utils", "_utilities": "folder-utils", "__utilities__": "folder-utils", "supabase": "folder-supabase", ".supabase": "folder-supabase", "_supabase": "folder-supabase", "__supabase__": "folder-supabase", "private": "folder-private", ".private": "folder-private", "_private": "folder-private", "__private__": "folder-private", "linux": "folder-linux", ".linux": "folder-linux", "_linux": "folder-linux", "__linux__": "folder-linux", "linuxbsd": "folder-linux", ".linuxbsd": "folder-linux", "_linuxbsd": "folder-linux", "__linuxbsd__": "folder-linux", "unix": "folder-linux", ".unix": "folder-linux", "_unix": "folder-linux", "__unix__": "folder-linux", "windows": "folder-windows", ".windows": "folder-windows", "_windows": "folder-windows", "__windows__": "folder-windows", "win": "folder-windows", ".win": "folder-windows", "_win": "folder-windows", "__win__": "folder-windows", "macos": "folder-macos", ".macos": "folder-macos", "_macos": "folder-macos", "__macos__": "folder-macos", "mac": "folder-macos", ".mac": "folder-macos", "_mac": "folder-macos", "__mac__": "folder-macos", "DS_Store": "folder-macos", ".DS_Store": "folder-macos", "_DS_Store": "folder-macos", "__DS_Store__": "folder-macos", "error": "folder-error", ".error": "folder-error", "_error": "folder-error", "__error__": "folder-error", "errors": "folder-error", ".errors": "folder-error", "_errors": "folder-error", "__errors__": "folder-error", "err": "folder-error", ".err": "folder-error", "_err": "folder-error", "__err__": "folder-error", "errs": "folder-error", ".errs": "folder-error", "_errs": "folder-error", "__errs__": "folder-error", "crash": "folder-error", ".crash": "folder-error", "_crash": "folder-error", "__crash__": "folder-error", "crashes": "folder-error", ".crashes": "folder-error", "_crashes": "folder-error", "__crashes__": "folder-error", "event": "folder-event", ".event": "folder-event", "_event": "folder-event", "__event__": "folder-event", "events": "folder-event", ".events": "folder-event", "_events": "folder-event", "__events__": "folder-event", "auth": "folder-secure", ".auth": "folder-secure", "_auth": "folder-secure", "__auth__": "folder-secure", "authentication": "folder-secure", ".authentication": "folder-secure", "_authentication": "folder-secure", "__authentication__": "folder-secure", "secure": "folder-secure", ".secure": "folder-secure", "_secure": "folder-secure", "__secure__": "folder-secure", "security": "folder-secure", ".security": "folder-secure", "_security": "folder-secure", "__security__": "folder-secure", "cert": "folder-secure", ".cert": "folder-secure", "_cert": "folder-secure", "__cert__": "folder-secure", "certs": "folder-secure", ".certs": "folder-secure", "_certs": "folder-secure", "__certs__": "folder-secure", "certificate": "folder-secure", ".certificate": "folder-secure", "_certificate": "folder-secure", "__certificate__": "folder-secure", "certificates": "folder-secure", ".certificates": "folder-secure", "_certificates": "folder-secure", "__certificates__": "folder-secure", "ssl": "folder-secure", ".ssl": "folder-secure", "_ssl": "folder-secure", "__ssl__": "folder-secure", "custom": "folder-custom", ".custom": "folder-custom", "_custom": "folder-custom", "__custom__": "folder-custom", "customs": "folder-custom", ".customs": "folder-custom", "_customs": "folder-custom", "__customs__": "folder-custom", "draft": "folder-mock", ".draft": "folder-mock", "_draft": "folder-mock", "__draft__": "folder-mock", "drafts": "folder-mock", ".drafts": "folder-mock", "_drafts": "folder-mock", "__drafts__": "folder-mock", "mock": "folder-mock", ".mock": "folder-mock", "_mock": "folder-mock", "__mock__": "folder-mock", "mocks": "folder-mock", ".mocks": "folder-mock", "_mocks": "folder-mock", "__mocks__": "folder-mock", "fixture": "folder-mock", ".fixture": "folder-mock", "_fixture": "folder-mock", "__fixture__": "folder-mock", "fixtures": "folder-mock", ".fixtures": "folder-mock", "_fixtures": "folder-mock", "__fixtures__": "folder-mock", "concept": "folder-mock", ".concept": "folder-mock", "_concept": "folder-mock", "__concept__": "folder-mock", "concepts": "folder-mock", ".concepts": "folder-mock", "_concepts": "folder-mock", "__concepts__": "folder-mock", "sketch": "folder-mock", ".sketch": "folder-mock", "_sketch": "folder-mock", "__sketch__": "folder-mock", "sketches": "folder-mock", ".sketches": "folder-mock", "_sketches": "folder-mock", "__sketches__": "folder-mock", "syntax": "folder-syntax", ".syntax": "folder-syntax", "_syntax": "folder-syntax", "__syntax__": "folder-syntax", "syntaxes": "folder-syntax", ".syntaxes": "folder-syntax", "_syntaxes": "folder-syntax", "__syntaxes__": "folder-syntax", "spellcheck": "folder-syntax", ".spellcheck": "folder-syntax", "_spellcheck": "folder-syntax", "__spellcheck__": "folder-syntax", "vm": "folder-vm", ".vm": "folder-vm", "_vm": "folder-vm", "__vm__": "folder-vm", "vms": "folder-vm", ".vms": "folder-vm", "_vms": "folder-vm", "__vms__": "folder-vm", "stylus": "folder-stylus", ".stylus": "folder-stylus", "_stylus": "folder-stylus", "__stylus__": "folder-stylus", "flow-typed": "folder-flow", ".flow-typed": "folder-flow", "_flow-typed": "folder-flow", "__flow-typed__": "folder-flow", "rule": "folder-rules", ".rule": "folder-rules", "_rule": "folder-rules", "__rule__": "folder-rules", "rules": "folder-rules", ".rules": "folder-rules", "_rules": "folder-rules", "__rules__": "folder-rules", "validation": "folder-rules", ".validation": "folder-rules", "_validation": "folder-rules", "__validation__": "folder-rules", "validations": "folder-rules", ".validations": "folder-rules", "_validations": "folder-rules", "__validations__": "folder-rules", "validator": "folder-rules", ".validator": "folder-rules", "_validator": "folder-rules", "__validator__": "folder-rules", "validators": "folder-rules", ".validators": "folder-rules", "_validators": "folder-rules", "__validators__": "folder-rules", "review": "folder-review", ".review": "folder-review", "_review": "folder-review", "__review__": "folder-review", "reviews": "folder-review", ".reviews": "folder-review", "_reviews": "folder-review", "__reviews__": "folder-review", "revisal": "folder-review", ".revisal": "folder-review", "_revisal": "folder-review", "__revisal__": "folder-review", "revisals": "folder-review", ".revisals": "folder-review", "_revisals": "folder-review", "__revisals__": "folder-review", "reviewed": "folder-review", ".reviewed": "folder-review", "_reviewed": "folder-review", "__reviewed__": "folder-review", "anim": "folder-animation", ".anim": "folder-animation", "_anim": "folder-animation", "__anim__": "folder-animation", "anims": "folder-animation", ".anims": "folder-animation", "_anims": "folder-animation", "__anims__": "folder-animation", "animation": "folder-animation", ".animation": "folder-animation", "_animation": "folder-animation", "__animation__": "folder-animation", "animations": "folder-animation", ".animations": "folder-animation", "_animations": "folder-animation", "__animations__": "folder-animation", "animated": "folder-animation", ".animated": "folder-animation", "_animated": "folder-animation", "__animated__": "folder-animation", "guard": "folder-guard", ".guard": "folder-guard", "_guard": "folder-guard", "__guard__": "folder-guard", "guards": "folder-guard", ".guards": "folder-guard", "_guards": "folder-guard", "__guards__": "folder-guard", "prisma": "folder-prisma", ".prisma": "folder-prisma", "_prisma": "folder-prisma", "__prisma__": "folder-prisma", "pipe": "folder-pipe", ".pipe": "folder-pipe", "_pipe": "folder-pipe", "__pipe__": "folder-pipe", "pipes": "folder-pipe", ".pipes": "folder-pipe", "_pipes": "folder-pipe", "__pipes__": "folder-pipe", "svg": "folder-svg", ".svg": "folder-svg", "_svg": "folder-svg", "__svg__": "folder-svg", "svgs": "folder-svg", ".svgs": "folder-svg", "_svgs": "folder-svg", "__svgs__": "folder-svg", "terraform": "folder-terraform", ".terraform": "folder-terraform", "_terraform": "folder-terraform", "__terraform__": "folder-terraform", "mobile": "folder-mobile", ".mobile": "folder-mobile", "_mobile": "folder-mobile", "__mobile__": "folder-mobile", "mobiles": "folder-mobile", ".mobiles": "folder-mobile", "_mobiles": "folder-mobile", "__mobiles__": "folder-mobile", "portable": "folder-mobile", ".portable": "folder-mobile", "_portable": "folder-mobile", "__portable__": "folder-mobile", "portability": "folder-mobile", ".portability": "folder-mobile", "_portability": "folder-mobile", "__portability__": "folder-mobile", "stencil": "folder-stencil", ".stencil": "folder-stencil", "_stencil": "folder-stencil", "__stencil__": "folder-stencil", "firebase": "folder-firebase", ".firebase": "folder-firebase", "_firebase": "folder-firebase", "__firebase__": "folder-firebase", "svelte": "folder-svelte", ".svelte": "folder-svelte", "_svelte": "folder-svelte", "__svelte__": "folder-svelte", "svelte-kit": "folder-svelte", ".svelte-kit": "folder-svelte", "_svelte-kit": "folder-svelte", "__svelte-kit__": "folder-svelte", "update": "folder-update", ".update": "folder-update", "_update": "folder-update", "__update__": "folder-update", "updates": "folder-update", ".updates": "folder-update", "_updates": "folder-update", "__updates__": "folder-update", "upgrade": "folder-update", ".upgrade": "folder-update", "_upgrade": "folder-update", "__upgrade__": "folder-update", "upgrades": "folder-update", ".upgrades": "folder-update", "_upgrades": "folder-update", "__upgrades__": "folder-update", "idea": "folder-intellij", ".idea": "folder-intellij", "_idea": "folder-intellij", "__idea__": "folder-intellij", "azure-pipelines": "folder-azure-pipelines", ".azure-pipelines": "folder-azure-pipelines", "_azure-pipelines": "folder-azure-pipelines", "__azure-pipelines__": "folder-azure-pipelines", "azure-pipelines-ci": "folder-azure-pipelines", ".azure-pipelines-ci": "folder-azure-pipelines", "_azure-pipelines-ci": "folder-azure-pipelines", "__azure-pipelines-ci__": "folder-azure-pipelines", "mjml": "folder-mjml", ".mjml": "folder-mjml", "_mjml": "folder-mjml", "__mjml__": "folder-mjml", "admin": "folder-admin", ".admin": "folder-admin", "_admin": "folder-admin", "__admin__": "folder-admin", "admins": "folder-admin", ".admins": "folder-admin", "_admins": "folder-admin", "__admins__": "folder-admin", "manager": "folder-admin", ".manager": "folder-admin", "_manager": "folder-admin", "__manager__": "folder-admin", "managers": "folder-admin", ".managers": "folder-admin", "_managers": "folder-admin", "__managers__": "folder-admin", "moderator": "folder-admin", ".moderator": "folder-admin", "_moderator": "folder-admin", "__moderator__": "folder-admin", "moderators": "folder-admin", ".moderators": "folder-admin", "_moderators": "folder-admin", "__moderators__": "folder-admin", "scala": "folder-scala", ".scala": "folder-scala", "_scala": "folder-scala", "__scala__": "folder-scala", "connection": "folder-connection", ".connection": "folder-connection", "_connection": "folder-connection", "__connection__": "folder-connection", "connections": "folder-connection", ".connections": "folder-connection", "_connections": "folder-connection", "__connections__": "folder-connection", "integration": "folder-connection", ".integration": "folder-connection", "_integration": "folder-connection", "__integration__": "folder-connection", "integrations": "folder-connection", ".integrations": "folder-connection", "_integrations": "folder-connection", "__integrations__": "folder-connection", "quasar": "folder-quasar", ".quasar": "folder-quasar", "_quasar": "folder-quasar", "__quasar__": "folder-quasar", "next": "folder-next", ".next": "folder-next", "_next": "folder-next", "__next__": "folder-next", "cobol": "folder-cobol", ".cobol": "folder-cobol", "_cobol": "folder-cobol", "__cobol__": "folder-cobol", "yarn": "folder-yarn", ".yarn": "folder-yarn", "_yarn": "folder-yarn", "__yarn__": "folder-yarn", "husky": "folder-husky", ".husky": "folder-husky", "_husky": "folder-husky", "__husky__": "folder-husky", "storybook": "folder-storybook", ".storybook": "folder-storybook", "_storybook": "folder-storybook", "__storybook__": "folder-storybook", "stories": "folder-storybook", ".stories": "folder-storybook", "_stories": "folder-storybook", "__stories__": "folder-storybook", "base": "folder-base", ".base": "folder-base", "_base": "folder-base", "__base__": "folder-base", "bases": "folder-base", ".bases": "folder-base", "_bases": "folder-base", "__bases__": "folder-base", "cart": "folder-cart", ".cart": "folder-cart", "_cart": "folder-cart", "__cart__": "folder-cart", "shopping-cart": "folder-cart", ".shopping-cart": "folder-cart", "_shopping-cart": "folder-cart", "__shopping-cart__": "folder-cart", "shopping": "folder-cart", ".shopping": "folder-cart", "_shopping": "folder-cart", "__shopping__": "folder-cart", "shop": "folder-cart", ".shop": "folder-cart", "_shop": "folder-cart", "__shop__": "folder-cart", "home": "folder-home", ".home": "folder-home", "_home": "folder-home", "__home__": "folder-home", "start": "folder-home", ".start": "folder-home", "_start": "folder-home", "__start__": "folder-home", "project": "folder-project", ".project": "folder-project", "_project": "folder-project", "__project__": "folder-project", "projects": "folder-project", ".projects": "folder-project", "_projects": "folder-project", "__projects__": "folder-project", "interface": "folder-interface", ".interface": "folder-interface", "_interface": "folder-interface", "__interface__": "folder-interface", "interfaces": "folder-interface", ".interfaces": "folder-interface", "_interfaces": "folder-interface", "__interfaces__": "folder-interface", "netlify": "folder-netlify", ".netlify": "folder-netlify", "_netlify": "folder-netlify", "__netlify__": "folder-netlify", "enum": "folder-enum", ".enum": "folder-enum", "_enum": "folder-enum", "__enum__": "folder-enum", "enums": "folder-enum", ".enums": "folder-enum", "_enums": "folder-enum", "__enums__": "folder-enum", "pact": "folder-contract", ".pact": "folder-contract", "_pact": "folder-contract", "__pact__": "folder-contract", "pacts": "folder-contract", ".pacts": "folder-contract", "_pacts": "folder-contract", "__pacts__": "folder-contract", "contract": "folder-contract", ".contract": "folder-contract", "_contract": "folder-contract", "__contract__": "folder-contract", "contracts": "folder-contract", ".contracts": "folder-contract", "_contracts": "folder-contract", "__contracts__": "folder-contract", "contract-testing": "folder-contract", ".contract-testing": "folder-contract", "_contract-testing": "folder-contract", "__contract-testing__": "folder-contract", "contract-test": "folder-contract", ".contract-test": "folder-contract", "_contract-test": "folder-contract", "__contract-test__": "folder-contract", "contract-tests": "folder-contract", ".contract-tests": "folder-contract", "_contract-tests": "folder-contract", "__contract-tests__": "folder-contract", "queue": "folder-queue", ".queue": "folder-queue", "_queue": "folder-queue", "__queue__": "folder-queue", "queues": "folder-queue", ".queues": "folder-queue", "_queues": "folder-queue", "__queues__": "folder-queue", "bull": "folder-queue", ".bull": "folder-queue", "_bull": "folder-queue", "__bull__": "folder-queue", "mq": "folder-queue", ".mq": "folder-queue", "_mq": "folder-queue", "__mq__": "folder-queue", "vercel": "folder-vercel", ".vercel": "folder-vercel", "_vercel": "folder-vercel", "__vercel__": "folder-vercel", "now": "folder-vercel", ".now": "folder-vercel", "_now": "folder-vercel", "__now__": "folder-vercel", "cypress": "folder-cypress", ".cypress": "folder-cypress", "_cypress": "folder-cypress", "__cypress__": "folder-cypress", "decorator": "folder-decorators", ".decorator": "folder-decorators", "_decorator": "folder-decorators", "__decorator__": "folder-decorators", "decorators": "folder-decorators", ".decorators": "folder-decorators", "_decorators": "folder-decorators", "__decorators__": "folder-decorators", "java": "folder-java", ".java": "folder-java", "_java": "folder-java", "__java__": "folder-java", "resolver": "folder-resolver", ".resolver": "folder-resolver", "_resolver": "folder-resolver", "__resolver__": "folder-resolver", "resolvers": "folder-resolver", ".resolvers": "folder-resolver", "_resolvers": "folder-resolver", "__resolvers__": "folder-resolver", "angular": "folder-angular", ".angular": "folder-angular", "_angular": "folder-angular", "__angular__": "folder-angular", "unity": "folder-unity", ".unity": "folder-unity", "_unity": "folder-unity", "__unity__": "folder-unity", "pdf": "folder-pdf", ".pdf": "folder-pdf", "_pdf": "folder-pdf", "__pdf__": "folder-pdf", "pdfs": "folder-pdf", ".pdfs": "folder-pdf", "_pdfs": "folder-pdf", "__pdfs__": "folder-pdf", "protobuf": "folder-proto", ".protobuf": "folder-proto", "_protobuf": "folder-proto", "__protobuf__": "folder-proto", "protobufs": "folder-proto", ".protobufs": "folder-proto", "_protobufs": "folder-proto", "__protobufs__": "folder-proto", "proto": "folder-proto", ".proto": "folder-proto", "_proto": "folder-proto", "protos": "folder-proto", ".protos": "folder-proto", "_protos": "folder-proto", "__protos__": "folder-proto", "plastic": "folder-plastic", ".plastic": "folder-plastic", "_plastic": "folder-plastic", "__plastic__": "folder-plastic", "gamemaker": "folder-gamemaker", ".gamemaker": "folder-gamemaker", "_gamemaker": "folder-gamemaker", "__gamemaker__": "folder-gamemaker", "gamemaker2": "folder-gamemaker", ".gamemaker2": "folder-gamemaker", "_gamemaker2": "folder-gamemaker", "__gamemaker2__": "folder-gamemaker", "hg": "folder-mercurial", ".hg": "folder-mercurial", "_hg": "folder-mercurial", "__hg__": "folder-mercurial", "hghooks": "folder-mercurial", ".hghooks": "folder-mercurial", "_hghooks": "folder-mercurial", "__hghooks__": "folder-mercurial", "hgext": "folder-mercurial", ".hgext": "folder-mercurial", "_hgext": "folder-mercurial", "__hgext__": "folder-mercurial", "godot": "folder-godot", ".godot": "folder-godot", "_godot": "folder-godot", "__godot__": "folder-godot", "godot-cpp": "folder-godot", ".godot-cpp": "folder-godot", "_godot-cpp": "folder-godot", "__godot-cpp__": "folder-godot", "lottie": "folder-lottie", ".lottie": "folder-lottie", "_lottie": "folder-lottie", "__lottie__": "folder-lottie", "lotties": "folder-lottie", ".lotties": "folder-lottie", "_lotties": "folder-lottie", "__lotties__": "folder-lottie", "lottiefiles": "folder-lottie", ".lottiefiles": "folder-lottie", "_lottiefiles": "folder-lottie", "__lottiefiles__": "folder-lottie", "taskfile": "folder-taskfile", ".taskfile": "folder-taskfile", "_taskfile": "folder-taskfile", "__taskfile__": "folder-taskfile", "taskfiles": "folder-taskfile", ".taskfiles": "folder-taskfile", "_taskfiles": "folder-taskfile", "__taskfiles__": "folder-taskfile", "cloudflare": "folder-cloudflare", ".cloudflare": "folder-cloudflare", "_cloudflare": "folder-cloudflare", "__cloudflare__": "folder-cloudflare", "seeds": "folder-seeders", ".seeds": "folder-seeders", "_seeds": "folder-seeders", "__seeds__": "folder-seeders", "seeders": "folder-seeders", ".seeders": "folder-seeders", "_seeders": "folder-seeders", "__seeders__": "folder-seeders", "seed": "folder-seeders", ".seed": "folder-seeders", "_seed": "folder-seeders", "__seed__": "folder-seeders", "seeding": "folder-seeders", ".seeding": "folder-seeders", "_seeding": "folder-seeders", "__seeding__": "folder-seeders"}, "folderNamesExpanded": {"bot": "folder-robot-open", ".bot": "folder-robot-open", "_bot": "folder-robot-open", "__bot__": "folder-robot-open", "robot": "folder-robot-open", ".robot": "folder-robot-open", "_robot": "folder-robot-open", "__robot__": "folder-robot-open", "src": "folder-src-open", ".src": "folder-src-open", "_src": "folder-src-open", "__src__": "folder-src-open", "srcs": "folder-src-open", ".srcs": "folder-src-open", "_srcs": "folder-src-open", "__srcs__": "folder-src-open", "source": "folder-src-open", ".source": "folder-src-open", "_source": "folder-src-open", "__source__": "folder-src-open", "sources": "folder-src-open", ".sources": "folder-src-open", "_sources": "folder-src-open", "__sources__": "folder-src-open", "code": "folder-src-open", ".code": "folder-src-open", "_code": "folder-src-open", "__code__": "folder-src-open", "dist": "folder-dist-open", ".dist": "folder-dist-open", "_dist": "folder-dist-open", "__dist__": "folder-dist-open", "out": "folder-dist-open", ".out": "folder-dist-open", "_out": "folder-dist-open", "__out__": "folder-dist-open", "build": "folder-dist-open", ".build": "folder-dist-open", "_build": "folder-dist-open", "__build__": "folder-dist-open", "release": "folder-dist-open", ".release": "folder-dist-open", "_release": "folder-dist-open", "__release__": "folder-dist-open", "bin": "folder-dist-open", ".bin": "folder-dist-open", "_bin": "folder-dist-open", "__bin__": "folder-dist-open", "css": "folder-css-open", ".css": "folder-css-open", "_css": "folder-css-open", "__css__": "folder-css-open", "stylesheet": "folder-css-open", ".stylesheet": "folder-css-open", "_stylesheet": "folder-css-open", "__stylesheet__": "folder-css-open", "stylesheets": "folder-css-open", ".stylesheets": "folder-css-open", "_stylesheets": "folder-css-open", "__stylesheets__": "folder-css-open", "style": "folder-css-open", ".style": "folder-css-open", "_style": "folder-css-open", "__style__": "folder-css-open", "styles": "folder-css-open", ".styles": "folder-css-open", "_styles": "folder-css-open", "__styles__": "folder-css-open", "sass": "folder-sass-open", ".sass": "folder-sass-open", "_sass": "folder-sass-open", "__sass__": "folder-sass-open", "scss": "folder-sass-open", ".scss": "folder-sass-open", "_scss": "folder-sass-open", "__scss__": "folder-sass-open", "tv": "folder-television-open", ".tv": "folder-television-open", "_tv": "folder-television-open", "__tv__": "folder-television-open", "television": "folder-television-open", ".television": "folder-television-open", "_television": "folder-television-open", "__television__": "folder-television-open", "desktop": "folder-desktop-open", ".desktop": "folder-desktop-open", "_desktop": "folder-desktop-open", "__desktop__": "folder-desktop-open", "console": "folder-console-open", ".console": "folder-console-open", "_console": "folder-console-open", "__console__": "folder-console-open", "images": "folder-images-open", ".images": "folder-images-open", "_images": "folder-images-open", "__images__": "folder-images-open", "image": "folder-images-open", ".image": "folder-images-open", "_image": "folder-images-open", "__image__": "folder-images-open", "imgs": "folder-images-open", ".imgs": "folder-images-open", "_imgs": "folder-images-open", "__imgs__": "folder-images-open", "img": "folder-images-open", ".img": "folder-images-open", "_img": "folder-images-open", "__img__": "folder-images-open", "icons": "folder-images-open", ".icons": "folder-images-open", "_icons": "folder-images-open", "__icons__": "folder-images-open", "icon": "folder-images-open", ".icon": "folder-images-open", "_icon": "folder-images-open", "__icon__": "folder-images-open", "icos": "folder-images-open", ".icos": "folder-images-open", "_icos": "folder-images-open", "__icos__": "folder-images-open", "ico": "folder-images-open", ".ico": "folder-images-open", "_ico": "folder-images-open", "__ico__": "folder-images-open", "figures": "folder-images-open", ".figures": "folder-images-open", "_figures": "folder-images-open", "__figures__": "folder-images-open", "figure": "folder-images-open", ".figure": "folder-images-open", "_figure": "folder-images-open", "__figure__": "folder-images-open", "figs": "folder-images-open", ".figs": "folder-images-open", "_figs": "folder-images-open", "__figs__": "folder-images-open", "fig": "folder-images-open", ".fig": "folder-images-open", "_fig": "folder-images-open", "__fig__": "folder-images-open", "screenshot": "folder-images-open", ".screenshot": "folder-images-open", "_screenshot": "folder-images-open", "__screenshot__": "folder-images-open", "screenshots": "folder-images-open", ".screenshots": "folder-images-open", "_screenshots": "folder-images-open", "__screenshots__": "folder-images-open", "screengrab": "folder-images-open", ".screengrab": "folder-images-open", "_screengrab": "folder-images-open", "__screengrab__": "folder-images-open", "screengrabs": "folder-images-open", ".screengrabs": "folder-images-open", "_screengrabs": "folder-images-open", "__screengrabs__": "folder-images-open", "pic": "folder-images-open", ".pic": "folder-images-open", "_pic": "folder-images-open", "__pic__": "folder-images-open", "pics": "folder-images-open", ".pics": "folder-images-open", "_pics": "folder-images-open", "__pics__": "folder-images-open", "picture": "folder-images-open", ".picture": "folder-images-open", "_picture": "folder-images-open", "__picture__": "folder-images-open", "pictures": "folder-images-open", ".pictures": "folder-images-open", "_pictures": "folder-images-open", "__pictures__": "folder-images-open", "photo": "folder-images-open", ".photo": "folder-images-open", "_photo": "folder-images-open", "__photo__": "folder-images-open", "photos": "folder-images-open", ".photos": "folder-images-open", "_photos": "folder-images-open", "__photos__": "folder-images-open", "photograph": "folder-images-open", ".photograph": "folder-images-open", "_photograph": "folder-images-open", "__photograph__": "folder-images-open", "photographs": "folder-images-open", ".photographs": "folder-images-open", "_photographs": "folder-images-open", "__photographs__": "folder-images-open", "script": "folder-scripts-open", ".script": "folder-scripts-open", "_script": "folder-scripts-open", "__script__": "folder-scripts-open", "scripts": "folder-scripts-open", ".scripts": "folder-scripts-open", "_scripts": "folder-scripts-open", "__scripts__": "folder-scripts-open", "scripting": "folder-scripts-open", ".scripting": "folder-scripts-open", "_scripting": "folder-scripts-open", "__scripting__": "folder-scripts-open", "node_modules": "folder-node-open", ".node_modules": "folder-node-open", "_node_modules": "folder-node-open", "__node_modules__": "folder-node-open", "js": "folder-javascript-open", ".js": "folder-javascript-open", "_js": "folder-javascript-open", "__js__": "folder-javascript-open", "javascript": "folder-javascript-open", ".javascript": "folder-javascript-open", "_javascript": "folder-javascript-open", "__javascript__": "folder-javascript-open", "javascripts": "folder-javascript-open", ".javascripts": "folder-javascript-open", "_javascripts": "folder-javascript-open", "__javascripts__": "folder-javascript-open", "json": "folder-json-open", ".json": "folder-json-open", "_json": "folder-json-open", "__json__": "folder-json-open", "jsons": "folder-json-open", ".jsons": "folder-json-open", "_jsons": "folder-json-open", "__jsons__": "folder-json-open", "font": "folder-font-open", ".font": "folder-font-open", "_font": "folder-font-open", "__font__": "folder-font-open", "fonts": "folder-font-open", ".fonts": "folder-font-open", "_fonts": "folder-font-open", "__fonts__": "folder-font-open", "bower_components": "folder-bower-open", ".bower_components": "folder-bower-open", "_bower_components": "folder-bower-open", "__bower_components__": "folder-bower-open", "test": "folder-test-open", ".test": "folder-test-open", "_test": "folder-test-open", "__test__": "folder-test-open", "tests": "folder-test-open", ".tests": "folder-test-open", "_tests": "folder-test-open", "__tests__": "folder-test-open", "testing": "folder-test-open", ".testing": "folder-test-open", "_testing": "folder-test-open", "__testing__": "folder-test-open", "snapshots": "folder-test-open", ".snapshots": "folder-test-open", "_snapshots": "folder-test-open", "__snapshots__": "folder-test-open", "spec": "folder-test-open", ".spec": "folder-test-open", "_spec": "folder-test-open", "__spec__": "folder-test-open", "specs": "folder-test-open", ".specs": "folder-test-open", "_specs": "folder-test-open", "__specs__": "folder-test-open", "jinja": "folder-jinja-open", ".jinja": "folder-jinja-open", "_jinja": "folder-jinja-open", "__jinja__": "folder-jinja-open", "jinja2": "folder-jinja-open", ".jinja2": "folder-jinja-open", "_jinja2": "folder-jinja-open", "__jinja2__": "folder-jinja-open", "j2": "folder-jinja-open", ".j2": "folder-jinja-open", "_j2": "folder-jinja-open", "__j2__": "folder-jinja-open", "markdown": "folder-markdown-open", ".markdown": "folder-markdown-open", "_markdown": "folder-markdown-open", "__markdown__": "folder-markdown-open", "md": "folder-markdown-open", ".md": "folder-markdown-open", "_md": "folder-markdown-open", "__md__": "folder-markdown-open", "pdm-plugins": "folder-pdm-open", ".pdm-plugins": "folder-pdm-open", "_pdm-plugins": "folder-pdm-open", "__pdm-plugins__": "folder-pdm-open", "pdm-build": "folder-pdm-open", ".pdm-build": "folder-pdm-open", "_pdm-build": "folder-pdm-open", "__pdm-build__": "folder-pdm-open", "php": "folder-php-open", ".php": "folder-php-open", "_php": "folder-php-open", "__php__": "folder-php-open", "phpmailer": "folder-phpmailer-open", ".phpmailer": "folder-phpmailer-open", "_phpmailer": "folder-phpmailer-open", "__phpmailer__": "folder-phpmailer-open", "sublime": "folder-sublime-open", ".sublime": "folder-sublime-open", "_sublime": "folder-sublime-open", "__sublime__": "folder-sublime-open", "doc": "folder-docs-open", ".doc": "folder-docs-open", "_doc": "folder-docs-open", "__doc__": "folder-docs-open", "docs": "folder-docs-open", ".docs": "folder-docs-open", "_docs": "folder-docs-open", "__docs__": "folder-docs-open", "document": "folder-docs-open", ".document": "folder-docs-open", "_document": "folder-docs-open", "__document__": "folder-docs-open", "documents": "folder-docs-open", ".documents": "folder-docs-open", "_documents": "folder-docs-open", "__documents__": "folder-docs-open", "documentation": "folder-docs-open", ".documentation": "folder-docs-open", "_documentation": "folder-docs-open", "__documentation__": "folder-docs-open", "post": "folder-docs-open", ".post": "folder-docs-open", "_post": "folder-docs-open", "__post__": "folder-docs-open", "posts": "folder-docs-open", ".posts": "folder-docs-open", "_posts": "folder-docs-open", "__posts__": "folder-docs-open", "article": "folder-docs-open", ".article": "folder-docs-open", "_article": "folder-docs-open", "__article__": "folder-docs-open", "articles": "folder-docs-open", ".articles": "folder-docs-open", "_articles": "folder-docs-open", "__articles__": "folder-docs-open", "git": "folder-git-open", ".git": "folder-git-open", "_git": "folder-git-open", "__git__": "folder-git-open", "patches": "folder-git-open", ".patches": "folder-git-open", "_patches": "folder-git-open", "__patches__": "folder-git-open", "githooks": "folder-git-open", ".githooks": "folder-git-open", "_githooks": "folder-git-open", "__githooks__": "folder-git-open", "submodules": "folder-git-open", ".submodules": "folder-git-open", "_submodules": "folder-git-open", "__submodules__": "folder-git-open", "github": "folder-github-open", ".github": "folder-github-open", "_github": "folder-github-open", "__github__": "folder-github-open", "gitlab": "folder-gitlab-open", ".gitlab": "folder-gitlab-open", "_gitlab": "folder-gitlab-open", "__gitlab__": "folder-gitlab-open", "vscode": "folder-vscode-open", ".vscode": "folder-vscode-open", "_vscode": "folder-vscode-open", "__vscode__": "folder-vscode-open", "vscode-test": "folder-vscode-open", ".vscode-test": "folder-vscode-open", "_vscode-test": "folder-vscode-open", "__vscode-test__": "folder-vscode-open", "view": "folder-views-open", ".view": "folder-views-open", "_view": "folder-views-open", "__view__": "folder-views-open", "views": "folder-views-open", ".views": "folder-views-open", "_views": "folder-views-open", "__views__": "folder-views-open", "screen": "folder-views-open", ".screen": "folder-views-open", "_screen": "folder-views-open", "__screen__": "folder-views-open", "screens": "folder-views-open", ".screens": "folder-views-open", "_screens": "folder-views-open", "__screens__": "folder-views-open", "page": "folder-views-open", ".page": "folder-views-open", "_page": "folder-views-open", "__page__": "folder-views-open", "pages": "folder-views-open", ".pages": "folder-views-open", "_pages": "folder-views-open", "__pages__": "folder-views-open", "public_html": "folder-views-open", ".public_html": "folder-views-open", "_public_html": "folder-views-open", "__public_html__": "folder-views-open", "html": "folder-views-open", ".html": "folder-views-open", "_html": "folder-views-open", "__html__": "folder-views-open", "vue": "folder-vue-open", ".vue": "folder-vue-open", "_vue": "folder-vue-open", "__vue__": "folder-vue-open", "vuepress": "folder-vuepress-open", ".vuepress": "folder-vuepress-open", "_vuepress": "folder-vuepress-open", "__vuepress__": "folder-vuepress-open", "expo": "folder-expo-open", ".expo": "folder-expo-open", "_expo": "folder-expo-open", "__expo__": "folder-expo-open", "expo-shared": "folder-expo-open", ".expo-shared": "folder-expo-open", "_expo-shared": "folder-expo-open", "__expo-shared__": "folder-expo-open", "cfg": "folder-config-open", ".cfg": "folder-config-open", "_cfg": "folder-config-open", "__cfg__": "folder-config-open", "cfgs": "folder-config-open", ".cfgs": "folder-config-open", "_cfgs": "folder-config-open", "__cfgs__": "folder-config-open", "conf": "folder-config-open", ".conf": "folder-config-open", "_conf": "folder-config-open", "__conf__": "folder-config-open", "confs": "folder-config-open", ".confs": "folder-config-open", "_confs": "folder-config-open", "__confs__": "folder-config-open", "config": "folder-config-open", ".config": "folder-config-open", "_config": "folder-config-open", "__config__": "folder-config-open", "configs": "folder-config-open", ".configs": "folder-config-open", "_configs": "folder-config-open", "__configs__": "folder-config-open", "configuration": "folder-config-open", ".configuration": "folder-config-open", "_configuration": "folder-config-open", "__configuration__": "folder-config-open", "configurations": "folder-config-open", ".configurations": "folder-config-open", "_configurations": "folder-config-open", "__configurations__": "folder-config-open", "setting": "folder-config-open", ".setting": "folder-config-open", "_setting": "folder-config-open", "__setting__": "folder-config-open", "settings": "folder-config-open", ".settings": "folder-config-open", "_settings": "folder-config-open", "__settings__": "folder-config-open", "META-INF": "folder-config-open", ".META-INF": "folder-config-open", "_META-INF": "folder-config-open", "__META-INF__": "folder-config-open", "option": "folder-config-open", ".option": "folder-config-open", "_option": "folder-config-open", "__option__": "folder-config-open", "options": "folder-config-open", ".options": "folder-config-open", "_options": "folder-config-open", "__options__": "folder-config-open", "i18n": "folder-i18n-open", ".i18n": "folder-i18n-open", "_i18n": "folder-i18n-open", "__i18n__": "folder-i18n-open", "internationalization": "folder-i18n-open", ".internationalization": "folder-i18n-open", "_internationalization": "folder-i18n-open", "__internationalization__": "folder-i18n-open", "lang": "folder-i18n-open", ".lang": "folder-i18n-open", "_lang": "folder-i18n-open", "__lang__": "folder-i18n-open", "langs": "folder-i18n-open", ".langs": "folder-i18n-open", "_langs": "folder-i18n-open", "__langs__": "folder-i18n-open", "language": "folder-i18n-open", ".language": "folder-i18n-open", "_language": "folder-i18n-open", "__language__": "folder-i18n-open", "languages": "folder-i18n-open", ".languages": "folder-i18n-open", "_languages": "folder-i18n-open", "__languages__": "folder-i18n-open", "locale": "folder-i18n-open", ".locale": "folder-i18n-open", "_locale": "folder-i18n-open", "__locale__": "folder-i18n-open", "locales": "folder-i18n-open", ".locales": "folder-i18n-open", "_locales": "folder-i18n-open", "__locales__": "folder-i18n-open", "l10n": "folder-i18n-open", ".l10n": "folder-i18n-open", "_l10n": "folder-i18n-open", "__l10n__": "folder-i18n-open", "localization": "folder-i18n-open", ".localization": "folder-i18n-open", "_localization": "folder-i18n-open", "__localization__": "folder-i18n-open", "translation": "folder-i18n-open", ".translation": "folder-i18n-open", "_translation": "folder-i18n-open", "__translation__": "folder-i18n-open", "translate": "folder-i18n-open", ".translate": "folder-i18n-open", "_translate": "folder-i18n-open", "__translate__": "folder-i18n-open", "translations": "folder-i18n-open", ".translations": "folder-i18n-open", "_translations": "folder-i18n-open", "__translations__": "folder-i18n-open", "tx": "folder-i18n-open", ".tx": "folder-i18n-open", "_tx": "folder-i18n-open", "__tx__": "folder-i18n-open", "components": "folder-components-open", ".components": "folder-components-open", "_components": "folder-components-open", "__components__": "folder-components-open", "widget": "folder-components-open", ".widget": "folder-components-open", "_widget": "folder-components-open", "__widget__": "folder-components-open", "widgets": "folder-components-open", ".widgets": "folder-components-open", "_widgets": "folder-components-open", "__widgets__": "folder-components-open", "fragments": "folder-components-open", ".fragments": "folder-components-open", "_fragments": "folder-components-open", "__fragments__": "folder-components-open", "verdaccio": "folder-verdaccio-open", ".verdaccio": "folder-verdaccio-open", "_verdaccio": "folder-verdaccio-open", "__verdaccio__": "folder-verdaccio-open", "aurelia_project": "folder-aurelia-open", ".aurelia_project": "folder-aurelia-open", "_aurelia_project": "folder-aurelia-open", "__aurelia_project__": "folder-aurelia-open", "resource": "folder-resource-open", ".resource": "folder-resource-open", "_resource": "folder-resource-open", "__resource__": "folder-resource-open", "resources": "folder-resource-open", ".resources": "folder-resource-open", "_resources": "folder-resource-open", "__resources__": "folder-resource-open", "res": "folder-resource-open", ".res": "folder-resource-open", "_res": "folder-resource-open", "__res__": "folder-resource-open", "asset": "folder-resource-open", ".asset": "folder-resource-open", "_asset": "folder-resource-open", "__asset__": "folder-resource-open", "assets": "folder-resource-open", ".assets": "folder-resource-open", "_assets": "folder-resource-open", "__assets__": "folder-resource-open", "static": "folder-resource-open", ".static": "folder-resource-open", "_static": "folder-resource-open", "__static__": "folder-resource-open", "report": "folder-resource-open", ".report": "folder-resource-open", "_report": "folder-resource-open", "__report__": "folder-resource-open", "reports": "folder-resource-open", ".reports": "folder-resource-open", "_reports": "folder-resource-open", "__reports__": "folder-resource-open", "lib": "folder-lib-open", ".lib": "folder-lib-open", "_lib": "folder-lib-open", "__lib__": "folder-lib-open", "libs": "folder-lib-open", ".libs": "folder-lib-open", "_libs": "folder-lib-open", "__libs__": "folder-lib-open", "library": "folder-lib-open", ".library": "folder-lib-open", "_library": "folder-lib-open", "__library__": "folder-lib-open", "libraries": "folder-lib-open", ".libraries": "folder-lib-open", "_libraries": "folder-lib-open", "__libraries__": "folder-lib-open", "vendor": "folder-lib-open", ".vendor": "folder-lib-open", "_vendor": "folder-lib-open", "__vendor__": "folder-lib-open", "vendors": "folder-lib-open", ".vendors": "folder-lib-open", "_vendors": "folder-lib-open", "__vendors__": "folder-lib-open", "third-party": "folder-lib-open", ".third-party": "folder-lib-open", "_third-party": "folder-lib-open", "__third-party__": "folder-lib-open", "themes": "folder-theme-open", ".themes": "folder-theme-open", "_themes": "folder-theme-open", "__themes__": "folder-theme-open", "theme": "folder-theme-open", ".theme": "folder-theme-open", "_theme": "folder-theme-open", "__theme__": "folder-theme-open", "color": "folder-theme-open", ".color": "folder-theme-open", "_color": "folder-theme-open", "__color__": "folder-theme-open", "colors": "folder-theme-open", ".colors": "folder-theme-open", "_colors": "folder-theme-open", "__colors__": "folder-theme-open", "design": "folder-theme-open", ".design": "folder-theme-open", "_design": "folder-theme-open", "__design__": "folder-theme-open", "designs": "folder-theme-open", ".designs": "folder-theme-open", "_designs": "folder-theme-open", "__designs__": "folder-theme-open", "webpack": "folder-webpack-open", ".webpack": "folder-webpack-open", "_webpack": "folder-webpack-open", "__webpack__": "folder-webpack-open", "global": "folder-global-open", ".global": "folder-global-open", "_global": "folder-global-open", "__global__": "folder-global-open", "public": "folder-public-open", ".public": "folder-public-open", "_public": "folder-public-open", "__public__": "folder-public-open", "www": "folder-public-open", ".www": "folder-public-open", "_www": "folder-public-open", "__www__": "folder-public-open", "wwwroot": "folder-public-open", ".wwwroot": "folder-public-open", "_wwwroot": "folder-public-open", "__wwwroot__": "folder-public-open", "web": "folder-public-open", ".web": "folder-public-open", "_web": "folder-public-open", "__web__": "folder-public-open", "website": "folder-public-open", ".website": "folder-public-open", "_website": "folder-public-open", "__website__": "folder-public-open", "site": "folder-public-open", ".site": "folder-public-open", "_site": "folder-public-open", "__site__": "folder-public-open", "browser": "folder-public-open", ".browser": "folder-public-open", "_browser": "folder-public-open", "__browser__": "folder-public-open", "browsers": "folder-public-open", ".browsers": "folder-public-open", "_browsers": "folder-public-open", "__browsers__": "folder-public-open", "inc": "folder-include-open", ".inc": "folder-include-open", "_inc": "folder-include-open", "__inc__": "folder-include-open", "include": "folder-include-open", ".include": "folder-include-open", "_include": "folder-include-open", "__include__": "folder-include-open", "includes": "folder-include-open", ".includes": "folder-include-open", "_includes": "folder-include-open", "__includes__": "folder-include-open", "partial": "folder-include-open", ".partial": "folder-include-open", "_partial": "folder-include-open", "__partial__": "folder-include-open", "partials": "folder-include-open", ".partials": "folder-include-open", "_partials": "folder-include-open", "__partials__": "folder-include-open", "docker": "folder-docker-open", ".docker": "folder-docker-open", "_docker": "folder-docker-open", "__docker__": "folder-docker-open", "dockerfiles": "folder-docker-open", ".dockerfiles": "folder-docker-open", "_dockerfiles": "folder-docker-open", "__dockerfiles__": "folder-docker-open", "db": "folder-database-open", ".db": "folder-database-open", "_db": "folder-database-open", "__db__": "folder-database-open", "data": "folder-database-open", ".data": "folder-database-open", "_data": "folder-database-open", "__data__": "folder-database-open", "database": "folder-database-open", ".database": "folder-database-open", "_database": "folder-database-open", "__database__": "folder-database-open", "databases": "folder-database-open", ".databases": "folder-database-open", "_databases": "folder-database-open", "__databases__": "folder-database-open", "sql": "folder-database-open", ".sql": "folder-database-open", "_sql": "folder-database-open", "__sql__": "folder-database-open", "log": "folder-log-open", ".log": "folder-log-open", "_log": "folder-log-open", "__log__": "folder-log-open", "logs": "folder-log-open", ".logs": "folder-log-open", "_logs": "folder-log-open", "__logs__": "folder-log-open", "logging": "folder-log-open", ".logging": "folder-log-open", "_logging": "folder-log-open", "__logging__": "folder-log-open", "target": "folder-target-open", ".target": "folder-target-open", "_target": "folder-target-open", "__target__": "folder-target-open", "temp": "folder-temp-open", ".temp": "folder-temp-open", "_temp": "folder-temp-open", "__temp__": "folder-temp-open", "tmp": "folder-temp-open", ".tmp": "folder-temp-open", "_tmp": "folder-temp-open", "__tmp__": "folder-temp-open", "cached": "folder-temp-open", ".cached": "folder-temp-open", "_cached": "folder-temp-open", "__cached__": "folder-temp-open", "cache": "folder-temp-open", ".cache": "folder-temp-open", "_cache": "folder-temp-open", "__cache__": "folder-temp-open", "aws": "folder-aws-open", ".aws": "folder-aws-open", "_aws": "folder-aws-open", "__aws__": "folder-aws-open", "aud": "folder-audio-open", ".aud": "folder-audio-open", "_aud": "folder-audio-open", "__aud__": "folder-audio-open", "auds": "folder-audio-open", ".auds": "folder-audio-open", "_auds": "folder-audio-open", "__auds__": "folder-audio-open", "audio": "folder-audio-open", ".audio": "folder-audio-open", "_audio": "folder-audio-open", "__audio__": "folder-audio-open", "audios": "folder-audio-open", ".audios": "folder-audio-open", "_audios": "folder-audio-open", "__audios__": "folder-audio-open", "music": "folder-audio-open", ".music": "folder-audio-open", "_music": "folder-audio-open", "__music__": "folder-audio-open", "sound": "folder-audio-open", ".sound": "folder-audio-open", "_sound": "folder-audio-open", "__sound__": "folder-audio-open", "sounds": "folder-audio-open", ".sounds": "folder-audio-open", "_sounds": "folder-audio-open", "__sounds__": "folder-audio-open", "vid": "folder-video-open", ".vid": "folder-video-open", "_vid": "folder-video-open", "__vid__": "folder-video-open", "vids": "folder-video-open", ".vids": "folder-video-open", "_vids": "folder-video-open", "__vids__": "folder-video-open", "video": "folder-video-open", ".video": "folder-video-open", "_video": "folder-video-open", "__video__": "folder-video-open", "videos": "folder-video-open", ".videos": "folder-video-open", "_videos": "folder-video-open", "__videos__": "folder-video-open", "movie": "folder-video-open", ".movie": "folder-video-open", "_movie": "folder-video-open", "__movie__": "folder-video-open", "movies": "folder-video-open", ".movies": "folder-video-open", "_movies": "folder-video-open", "__movies__": "folder-video-open", "kubernetes": "folder-kubernetes-open", ".kubernetes": "folder-kubernetes-open", "_kubernetes": "folder-kubernetes-open", "__kubernetes__": "folder-kubernetes-open", "k8s": "folder-kubernetes-open", ".k8s": "folder-kubernetes-open", "_k8s": "folder-kubernetes-open", "__k8s__": "folder-kubernetes-open", "import": "folder-import-open", ".import": "folder-import-open", "_import": "folder-import-open", "__import__": "folder-import-open", "imports": "folder-import-open", ".imports": "folder-import-open", "_imports": "folder-import-open", "__imports__": "folder-import-open", "imported": "folder-import-open", ".imported": "folder-import-open", "_imported": "folder-import-open", "__imported__": "folder-import-open", "export": "folder-export-open", ".export": "folder-export-open", "_export": "folder-export-open", "__export__": "folder-export-open", "exports": "folder-export-open", ".exports": "folder-export-open", "_exports": "folder-export-open", "__exports__": "folder-export-open", "exported": "folder-export-open", ".exported": "folder-export-open", "_exported": "folder-export-open", "__exported__": "folder-export-open", "wakatime": "folder-wakatime-open", ".wakatime": "folder-wakatime-open", "_wakatime": "folder-wakatime-open", "__wakatime__": "folder-wakatime-open", "circleci": "folder-circleci-open", ".circleci": "folder-circleci-open", "_circleci": "folder-circleci-open", "__circleci__": "folder-circleci-open", "wordpress-org": "folder-wordpress-open", ".wordpress-org": "folder-wordpress-open", "_wordpress-org": "folder-wordpress-open", "__wordpress-org__": "folder-wordpress-open", "wp-content": "folder-wordpress-open", ".wp-content": "folder-wordpress-open", "_wp-content": "folder-wordpress-open", "__wp-content__": "folder-wordpress-open", "gradle": "folder-gradle-open", ".gradle": "folder-gradle-open", "_gradle": "folder-gradle-open", "__gradle__": "folder-gradle-open", "coverage": "folder-coverage-open", ".coverage": "folder-coverage-open", "_coverage": "folder-coverage-open", "__coverage__": "folder-coverage-open", "nyc-output": "folder-coverage-open", ".nyc-output": "folder-coverage-open", "_nyc-output": "folder-coverage-open", "__nyc-output__": "folder-coverage-open", "nyc_output": "folder-coverage-open", ".nyc_output": "folder-coverage-open", "_nyc_output": "folder-coverage-open", "__nyc_output__": "folder-coverage-open", "e2e": "folder-coverage-open", ".e2e": "folder-coverage-open", "_e2e": "folder-coverage-open", "__e2e__": "folder-coverage-open", "it": "folder-coverage-open", ".it": "folder-coverage-open", "_it": "folder-coverage-open", "__it__": "folder-coverage-open", "integration-test": "folder-coverage-open", ".integration-test": "folder-coverage-open", "_integration-test": "folder-coverage-open", "__integration-test__": "folder-coverage-open", "integration-tests": "folder-coverage-open", ".integration-tests": "folder-coverage-open", "_integration-tests": "folder-coverage-open", "__integration-tests__": "folder-coverage-open", "class": "folder-class-open", ".class": "folder-class-open", "_class": "folder-class-open", "__class__": "folder-class-open", "classes": "folder-class-open", ".classes": "folder-class-open", "_classes": "folder-class-open", "__classes__": "folder-class-open", "model": "folder-class-open", ".model": "folder-class-open", "_model": "folder-class-open", "__model__": "folder-class-open", "models": "folder-class-open", ".models": "folder-class-open", "_models": "folder-class-open", "__models__": "folder-class-open", "schemas": "folder-class-open", ".schemas": "folder-class-open", "_schemas": "folder-class-open", "__schemas__": "folder-class-open", "schema": "folder-class-open", ".schema": "folder-class-open", "_schema": "folder-class-open", "__schema__": "folder-class-open", "other": "folder-other-open", ".other": "folder-other-open", "_other": "folder-other-open", "__other__": "folder-other-open", "others": "folder-other-open", ".others": "folder-other-open", "_others": "folder-other-open", "__others__": "folder-other-open", "misc": "folder-other-open", ".misc": "folder-other-open", "_misc": "folder-other-open", "__misc__": "folder-other-open", "miscellaneous": "folder-other-open", ".miscellaneous": "folder-other-open", "_miscellaneous": "folder-other-open", "__miscellaneous__": "folder-other-open", "extra": "folder-other-open", ".extra": "folder-other-open", "_extra": "folder-other-open", "__extra__": "folder-other-open", "extras": "folder-other-open", ".extras": "folder-other-open", "_extras": "folder-other-open", "__extras__": "folder-other-open", "etc": "folder-other-open", ".etc": "folder-other-open", "_etc": "folder-other-open", "__etc__": "folder-other-open", "lua": "folder-lua-open", ".lua": "folder-lua-open", "_lua": "folder-lua-open", "__lua__": "folder-lua-open", "typescript": "folder-typescript-open", ".typescript": "folder-typescript-open", "_typescript": "folder-typescript-open", "__typescript__": "folder-typescript-open", "ts": "folder-typescript-open", ".ts": "folder-typescript-open", "_ts": "folder-typescript-open", "__ts__": "folder-typescript-open", "typings": "folder-typescript-open", ".typings": "folder-typescript-open", "_typings": "folder-typescript-open", "__typings__": "folder-typescript-open", "@types": "folder-typescript-open", ".@types": "folder-typescript-open", "_@types": "folder-typescript-open", "__@types__": "folder-typescript-open", "types": "folder-typescript-open", ".types": "folder-typescript-open", "_types": "folder-typescript-open", "__types__": "folder-typescript-open", "graphql": "folder-graphql-open", ".graphql": "folder-graphql-open", "_graphql": "folder-graphql-open", "__graphql__": "folder-graphql-open", "gql": "folder-graphql-open", ".gql": "folder-graphql-open", "_gql": "folder-graphql-open", "__gql__": "folder-graphql-open", "routes": "folder-routes-open", ".routes": "folder-routes-open", "_routes": "folder-routes-open", "__routes__": "folder-routes-open", "router": "folder-routes-open", ".router": "folder-routes-open", "_router": "folder-routes-open", "__router__": "folder-routes-open", "routers": "folder-routes-open", ".routers": "folder-routes-open", "_routers": "folder-routes-open", "__routers__": "folder-routes-open", "ci": "folder-ci-open", ".ci": "folder-ci-open", "_ci": "folder-ci-open", "__ci__": "folder-ci-open", "benchmark": "folder-benchmark-open", ".benchmark": "folder-benchmark-open", "_benchmark": "folder-benchmark-open", "__benchmark__": "folder-benchmark-open", "benchmarks": "folder-benchmark-open", ".benchmarks": "folder-benchmark-open", "_benchmarks": "folder-benchmark-open", "__benchmarks__": "folder-benchmark-open", "performance": "folder-benchmark-open", ".performance": "folder-benchmark-open", "_performance": "folder-benchmark-open", "__performance__": "folder-benchmark-open", "measure": "folder-benchmark-open", ".measure": "folder-benchmark-open", "_measure": "folder-benchmark-open", "__measure__": "folder-benchmark-open", "measures": "folder-benchmark-open", ".measures": "folder-benchmark-open", "_measures": "folder-benchmark-open", "__measures__": "folder-benchmark-open", "measurement": "folder-benchmark-open", ".measurement": "folder-benchmark-open", "_measurement": "folder-benchmark-open", "__measurement__": "folder-benchmark-open", "messages": "folder-messages-open", ".messages": "folder-messages-open", "_messages": "folder-messages-open", "__messages__": "folder-messages-open", "messaging": "folder-messages-open", ".messaging": "folder-messages-open", "_messaging": "folder-messages-open", "__messaging__": "folder-messages-open", "forum": "folder-messages-open", ".forum": "folder-messages-open", "_forum": "folder-messages-open", "__forum__": "folder-messages-open", "chat": "folder-messages-open", ".chat": "folder-messages-open", "_chat": "folder-messages-open", "__chat__": "folder-messages-open", "chats": "folder-messages-open", ".chats": "folder-messages-open", "_chats": "folder-messages-open", "__chats__": "folder-messages-open", "conversation": "folder-messages-open", ".conversation": "folder-messages-open", "_conversation": "folder-messages-open", "__conversation__": "folder-messages-open", "conversations": "folder-messages-open", ".conversations": "folder-messages-open", "_conversations": "folder-messages-open", "__conversations__": "folder-messages-open", "less": "folder-less-open", ".less": "folder-less-open", "_less": "folder-less-open", "__less__": "folder-less-open", "gulp": "folder-gulp-open", ".gulp": "folder-gulp-open", "_gulp": "folder-gulp-open", "__gulp__": "folder-gulp-open", "gulp-tasks": "folder-gulp-open", ".gulp-tasks": "folder-gulp-open", "_gulp-tasks": "folder-gulp-open", "__gulp-tasks__": "folder-gulp-open", "gulpfile.js": "folder-gulp-open", ".gulpfile.js": "folder-gulp-open", "_gulpfile.js": "folder-gulp-open", "__gulpfile.js__": "folder-gulp-open", "gulpfile.mjs": "folder-gulp-open", ".gulpfile.mjs": "folder-gulp-open", "_gulpfile.mjs": "folder-gulp-open", "__gulpfile.mjs__": "folder-gulp-open", "gulpfile.ts": "folder-gulp-open", ".gulpfile.ts": "folder-gulp-open", "_gulpfile.ts": "folder-gulp-open", "__gulpfile.ts__": "folder-gulp-open", "gulpfile.babel.js": "folder-gulp-open", ".gulpfile.babel.js": "folder-gulp-open", "_gulpfile.babel.js": "folder-gulp-open", "__gulpfile.babel.js__": "folder-gulp-open", "python": "folder-python-open", ".python": "folder-python-open", "_python": "folder-python-open", "__python__": "folder-python-open", "pycache": "folder-python-open", ".pycache": "folder-python-open", "_pycache": "folder-python-open", "__pycache__": "folder-python-open", "pytest_cache": "folder-python-open", ".pytest_cache": "folder-python-open", "_pytest_cache": "folder-python-open", "__pytest_cache__": "folder-python-open", "mojo": "folder-mojo-open", ".mojo": "folder-mojo-open", "_mojo": "folder-mojo-open", "__mojo__": "folder-mojo-open", "moon": "folder-moon-open", ".moon": "folder-moon-open", "_moon": "folder-moon-open", "__moon__": "folder-moon-open", "debug": "folder-debug-open", ".debug": "folder-debug-open", "_debug": "folder-debug-open", "__debug__": "folder-debug-open", "debugging": "folder-debug-open", ".debugging": "folder-debug-open", "_debugging": "folder-debug-open", "__debugging__": "folder-debug-open", "fastlane": "folder-fastlane-open", ".fastlane": "folder-fastlane-open", "_fastlane": "folder-fastlane-open", "__fastlane__": "folder-fastlane-open", "plugin": "folder-plugin-open", ".plugin": "folder-plugin-open", "_plugin": "folder-plugin-open", "__plugin__": "folder-plugin-open", "plugins": "folder-plugin-open", ".plugins": "folder-plugin-open", "_plugins": "folder-plugin-open", "__plugins__": "folder-plugin-open", "mod": "folder-plugin-open", ".mod": "folder-plugin-open", "_mod": "folder-plugin-open", "__mod__": "folder-plugin-open", "mods": "folder-plugin-open", ".mods": "folder-plugin-open", "_mods": "folder-plugin-open", "__mods__": "folder-plugin-open", "modding": "folder-plugin-open", ".modding": "folder-plugin-open", "_modding": "folder-plugin-open", "__modding__": "folder-plugin-open", "extension": "folder-plugin-open", ".extension": "folder-plugin-open", "_extension": "folder-plugin-open", "__extension__": "folder-plugin-open", "extensions": "folder-plugin-open", ".extensions": "folder-plugin-open", "_extensions": "folder-plugin-open", "__extensions__": "folder-plugin-open", "addon": "folder-plugin-open", ".addon": "folder-plugin-open", "_addon": "folder-plugin-open", "__addon__": "folder-plugin-open", "addons": "folder-plugin-open", ".addons": "folder-plugin-open", "_addons": "folder-plugin-open", "__addons__": "folder-plugin-open", "module": "folder-plugin-open", ".module": "folder-plugin-open", "_module": "folder-plugin-open", "__module__": "folder-plugin-open", "modules": "folder-plugin-open", ".modules": "folder-plugin-open", "_modules": "folder-plugin-open", "__modules__": "folder-plugin-open", "middleware": "folder-middleware-open", ".middleware": "folder-middleware-open", "_middleware": "folder-middleware-open", "__middleware__": "folder-middleware-open", "middlewares": "folder-middleware-open", ".middlewares": "folder-middleware-open", "_middlewares": "folder-middleware-open", "__middlewares__": "folder-middleware-open", "controller": "folder-controller-open", ".controller": "folder-controller-open", "_controller": "folder-controller-open", "__controller__": "folder-controller-open", "controllers": "folder-controller-open", ".controllers": "folder-controller-open", "_controllers": "folder-controller-open", "__controllers__": "folder-controller-open", "service": "folder-controller-open", ".service": "folder-controller-open", "_service": "folder-controller-open", "__service__": "folder-controller-open", "services": "folder-controller-open", ".services": "folder-controller-open", "_services": "folder-controller-open", "__services__": "folder-controller-open", "provider": "folder-controller-open", ".provider": "folder-controller-open", "_provider": "folder-controller-open", "__provider__": "folder-controller-open", "providers": "folder-controller-open", ".providers": "folder-controller-open", "_providers": "folder-controller-open", "__providers__": "folder-controller-open", "handler": "folder-controller-open", ".handler": "folder-controller-open", "_handler": "folder-controller-open", "__handler__": "folder-controller-open", "handlers": "folder-controller-open", ".handlers": "folder-controller-open", "_handlers": "folder-controller-open", "__handlers__": "folder-controller-open", "ansible": "folder-ansible-open", ".ansible": "folder-ansible-open", "_ansible": "folder-ansible-open", "__ansible__": "folder-ansible-open", "server": "folder-server-open", ".server": "folder-server-open", "_server": "folder-server-open", "__server__": "folder-server-open", "servers": "folder-server-open", ".servers": "folder-server-open", "_servers": "folder-server-open", "__servers__": "folder-server-open", "backend": "folder-server-open", ".backend": "folder-server-open", "_backend": "folder-server-open", "__backend__": "folder-server-open", "backends": "folder-server-open", ".backends": "folder-server-open", "_backends": "folder-server-open", "__backends__": "folder-server-open", "client": "folder-client-open", ".client": "folder-client-open", "_client": "folder-client-open", "__client__": "folder-client-open", "clients": "folder-client-open", ".clients": "folder-client-open", "_clients": "folder-client-open", "__clients__": "folder-client-open", "frontend": "folder-client-open", ".frontend": "folder-client-open", "_frontend": "folder-client-open", "__frontend__": "folder-client-open", "frontends": "folder-client-open", ".frontends": "folder-client-open", "_frontends": "folder-client-open", "__frontends__": "folder-client-open", "pwa": "folder-client-open", ".pwa": "folder-client-open", "_pwa": "folder-client-open", "__pwa__": "folder-client-open", "tasks": "folder-tasks-open", ".tasks": "folder-tasks-open", "_tasks": "folder-tasks-open", "__tasks__": "folder-tasks-open", "tickets": "folder-tasks-open", ".tickets": "folder-tasks-open", "_tickets": "folder-tasks-open", "__tickets__": "folder-tasks-open", "android": "folder-android-open", ".android": "folder-android-open", "_android": "folder-android-open", "__android__": "folder-android-open", "ios": "folder-ios-open", ".ios": "folder-ios-open", "_ios": "folder-ios-open", "__ios__": "folder-ios-open", "uploads": "folder-upload-open", ".uploads": "folder-upload-open", "_uploads": "folder-upload-open", "__uploads__": "folder-upload-open", "upload": "folder-upload-open", ".upload": "folder-upload-open", "_upload": "folder-upload-open", "__upload__": "folder-upload-open", "downloads": "folder-download-open", ".downloads": "folder-download-open", "_downloads": "folder-download-open", "__downloads__": "folder-download-open", "download": "folder-download-open", ".download": "folder-download-open", "_download": "folder-download-open", "__download__": "folder-download-open", "tools": "folder-tools-open", ".tools": "folder-tools-open", "_tools": "folder-tools-open", "__tools__": "folder-tools-open", "toolkit": "folder-tools-open", ".toolkit": "folder-tools-open", "_toolkit": "folder-tools-open", "__toolkit__": "folder-tools-open", "toolkits": "folder-tools-open", ".toolkits": "folder-tools-open", "_toolkits": "folder-tools-open", "__toolkits__": "folder-tools-open", "toolbox": "folder-tools-open", ".toolbox": "folder-tools-open", "_toolbox": "folder-tools-open", "__toolbox__": "folder-tools-open", "toolboxes": "folder-tools-open", ".toolboxes": "folder-tools-open", "_toolboxes": "folder-tools-open", "__toolboxes__": "folder-tools-open", "tooling": "folder-tools-open", ".tooling": "folder-tools-open", "_tooling": "folder-tools-open", "__tooling__": "folder-tools-open", "helpers": "folder-helper-open", ".helpers": "folder-helper-open", "_helpers": "folder-helper-open", "__helpers__": "folder-helper-open", "helper": "folder-helper-open", ".helper": "folder-helper-open", "_helper": "folder-helper-open", "__helper__": "folder-helper-open", "serverless": "folder-serverless-open", ".serverless": "folder-serverless-open", "_serverless": "folder-serverless-open", "__serverless__": "folder-serverless-open", "api": "folder-api-open", ".api": "folder-api-open", "_api": "folder-api-open", "__api__": "folder-api-open", "apis": "folder-api-open", ".apis": "folder-api-open", "_apis": "folder-api-open", "__apis__": "folder-api-open", "restapi": "folder-api-open", ".restapi": "folder-api-open", "_restapi": "folder-api-open", "__restapi__": "folder-api-open", "app": "folder-app-open", ".app": "folder-app-open", "_app": "folder-app-open", "__app__": "folder-app-open", "apps": "folder-app-open", ".apps": "folder-app-open", "_apps": "folder-app-open", "__apps__": "folder-app-open", "apollo": "folder-apollo-open", ".apollo": "folder-apollo-open", "_apollo": "folder-apollo-open", "__apollo__": "folder-apollo-open", "apollo-client": "folder-apollo-open", ".apollo-client": "folder-apollo-open", "_apollo-client": "folder-apollo-open", "__apollo-client__": "folder-apollo-open", "apollo-cache": "folder-apollo-open", ".apollo-cache": "folder-apollo-open", "_apollo-cache": "folder-apollo-open", "__apollo-cache__": "folder-apollo-open", "apollo-config": "folder-apollo-open", ".apollo-config": "folder-apollo-open", "_apollo-config": "folder-apollo-open", "__apollo-config__": "folder-apollo-open", "arc": "folder-archive-open", ".arc": "folder-archive-open", "_arc": "folder-archive-open", "__arc__": "folder-archive-open", "arcs": "folder-archive-open", ".arcs": "folder-archive-open", "_arcs": "folder-archive-open", "__arcs__": "folder-archive-open", "archive": "folder-archive-open", ".archive": "folder-archive-open", "_archive": "folder-archive-open", "__archive__": "folder-archive-open", "archives": "folder-archive-open", ".archives": "folder-archive-open", "_archives": "folder-archive-open", "__archives__": "folder-archive-open", "archival": "folder-archive-open", ".archival": "folder-archive-open", "_archival": "folder-archive-open", "__archival__": "folder-archive-open", "bkp": "folder-archive-open", ".bkp": "folder-archive-open", "_bkp": "folder-archive-open", "__bkp__": "folder-archive-open", "bkps": "folder-archive-open", ".bkps": "folder-archive-open", "_bkps": "folder-archive-open", "__bkps__": "folder-archive-open", "bak": "folder-archive-open", ".bak": "folder-archive-open", "_bak": "folder-archive-open", "__bak__": "folder-archive-open", "baks": "folder-archive-open", ".baks": "folder-archive-open", "_baks": "folder-archive-open", "__baks__": "folder-archive-open", "backup": "folder-archive-open", ".backup": "folder-archive-open", "_backup": "folder-archive-open", "__backup__": "folder-archive-open", "backups": "folder-archive-open", ".backups": "folder-archive-open", "_backups": "folder-archive-open", "__backups__": "folder-archive-open", "back-up": "folder-archive-open", ".back-up": "folder-archive-open", "_back-up": "folder-archive-open", "__back-up__": "folder-archive-open", "back-ups": "folder-archive-open", ".back-ups": "folder-archive-open", "_back-ups": "folder-archive-open", "__back-ups__": "folder-archive-open", "history": "folder-archive-open", ".history": "folder-archive-open", "_history": "folder-archive-open", "__history__": "folder-archive-open", "histories": "folder-archive-open", ".histories": "folder-archive-open", "_histories": "folder-archive-open", "__histories__": "folder-archive-open", "batch": "folder-batch-open", ".batch": "folder-batch-open", "_batch": "folder-batch-open", "__batch__": "folder-batch-open", "batchs": "folder-batch-open", ".batchs": "folder-batch-open", "_batchs": "folder-batch-open", "__batchs__": "folder-batch-open", "batches": "folder-batch-open", ".batches": "folder-batch-open", "_batches": "folder-batch-open", "__batches__": "folder-batch-open", "buildkite": "folder-buildkite-open", ".buildkite": "folder-buildkite-open", "_buildkite": "folder-buildkite-open", "__buildkite__": "folder-buildkite-open", "cluster": "folder-cluster-open", ".cluster": "folder-cluster-open", "_cluster": "folder-cluster-open", "__cluster__": "folder-cluster-open", "clusters": "folder-cluster-open", ".clusters": "folder-cluster-open", "_clusters": "folder-cluster-open", "__clusters__": "folder-cluster-open", "command": "folder-command-open", ".command": "folder-command-open", "_command": "folder-command-open", "__command__": "folder-command-open", "commands": "folder-command-open", ".commands": "folder-command-open", "_commands": "folder-command-open", "__commands__": "folder-command-open", "cmd": "folder-command-open", ".cmd": "folder-command-open", "_cmd": "folder-command-open", "__cmd__": "folder-command-open", "cli": "folder-command-open", ".cli": "folder-command-open", "_cli": "folder-command-open", "__cli__": "folder-command-open", "clis": "folder-command-open", ".clis": "folder-command-open", "_clis": "folder-command-open", "__clis__": "folder-command-open", "constant": "folder-constant-open", ".constant": "folder-constant-open", "_constant": "folder-constant-open", "__constant__": "folder-constant-open", "constants": "folder-constant-open", ".constants": "folder-constant-open", "_constants": "folder-constant-open", "__constants__": "folder-constant-open", "container": "folder-container-open", ".container": "folder-container-open", "_container": "folder-container-open", "__container__": "folder-container-open", "containers": "folder-container-open", ".containers": "folder-container-open", "_containers": "folder-container-open", "__containers__": "folder-container-open", "devcontainer": "folder-container-open", ".devcontainer": "folder-container-open", "_devcontainer": "folder-container-open", "__devcontainer__": "folder-container-open", "content": "folder-content-open", ".content": "folder-content-open", "_content": "folder-content-open", "__content__": "folder-content-open", "contents": "folder-content-open", ".contents": "folder-content-open", "_contents": "folder-content-open", "__contents__": "folder-content-open", "context": "folder-context-open", ".context": "folder-context-open", "_context": "folder-context-open", "__context__": "folder-context-open", "contexts": "folder-context-open", ".contexts": "folder-context-open", "_contexts": "folder-context-open", "__contexts__": "folder-context-open", "core": "folder-core-open", ".core": "folder-core-open", "_core": "folder-core-open", "__core__": "folder-core-open", "delta": "folder-delta-open", ".delta": "folder-delta-open", "_delta": "folder-delta-open", "__delta__": "folder-delta-open", "deltas": "folder-delta-open", ".deltas": "folder-delta-open", "_deltas": "folder-delta-open", "__deltas__": "folder-delta-open", "changes": "folder-delta-open", ".changes": "folder-delta-open", "_changes": "folder-delta-open", "__changes__": "folder-delta-open", "dump": "folder-dump-open", ".dump": "folder-dump-open", "_dump": "folder-dump-open", "__dump__": "folder-dump-open", "dumps": "folder-dump-open", ".dumps": "folder-dump-open", "_dumps": "folder-dump-open", "__dumps__": "folder-dump-open", "demo": "folder-examples-open", ".demo": "folder-examples-open", "_demo": "folder-examples-open", "__demo__": "folder-examples-open", "demos": "folder-examples-open", ".demos": "folder-examples-open", "_demos": "folder-examples-open", "__demos__": "folder-examples-open", "example": "folder-examples-open", ".example": "folder-examples-open", "_example": "folder-examples-open", "__example__": "folder-examples-open", "examples": "folder-examples-open", ".examples": "folder-examples-open", "_examples": "folder-examples-open", "__examples__": "folder-examples-open", "sample": "folder-examples-open", ".sample": "folder-examples-open", "_sample": "folder-examples-open", "__sample__": "folder-examples-open", "samples": "folder-examples-open", ".samples": "folder-examples-open", "_samples": "folder-examples-open", "__samples__": "folder-examples-open", "sample-data": "folder-examples-open", ".sample-data": "folder-examples-open", "_sample-data": "folder-examples-open", "__sample-data__": "folder-examples-open", "env": "folder-environment-open", ".env": "folder-environment-open", "_env": "folder-environment-open", "__env__": "folder-environment-open", "envs": "folder-environment-open", ".envs": "folder-environment-open", "_envs": "folder-environment-open", "__envs__": "folder-environment-open", "environment": "folder-environment-open", ".environment": "folder-environment-open", "_environment": "folder-environment-open", "__environment__": "folder-environment-open", "environments": "folder-environment-open", ".environments": "folder-environment-open", "_environments": "folder-environment-open", "__environments__": "folder-environment-open", "venv": "folder-environment-open", ".venv": "folder-environment-open", "_venv": "folder-environment-open", "__venv__": "folder-environment-open", "func": "folder-functions-open", ".func": "folder-functions-open", "_func": "folder-functions-open", "__func__": "folder-functions-open", "funcs": "folder-functions-open", ".funcs": "folder-functions-open", "_funcs": "folder-functions-open", "__funcs__": "folder-functions-open", "function": "folder-functions-open", ".function": "folder-functions-open", "_function": "folder-functions-open", "__function__": "folder-functions-open", "functions": "folder-functions-open", ".functions": "folder-functions-open", "_functions": "folder-functions-open", "__functions__": "folder-functions-open", "lambda": "folder-functions-open", ".lambda": "folder-functions-open", "_lambda": "folder-functions-open", "__lambda__": "folder-functions-open", "lambdas": "folder-functions-open", ".lambdas": "folder-functions-open", "_lambdas": "folder-functions-open", "__lambdas__": "folder-functions-open", "logic": "folder-functions-open", ".logic": "folder-functions-open", "_logic": "folder-functions-open", "__logic__": "folder-functions-open", "math": "folder-functions-open", ".math": "folder-functions-open", "_math": "folder-functions-open", "__math__": "folder-functions-open", "maths": "folder-functions-open", ".maths": "folder-functions-open", "_maths": "folder-functions-open", "__maths__": "folder-functions-open", "calc": "folder-functions-open", ".calc": "folder-functions-open", "_calc": "folder-functions-open", "__calc__": "folder-functions-open", "calcs": "folder-functions-open", ".calcs": "folder-functions-open", "_calcs": "folder-functions-open", "__calcs__": "folder-functions-open", "calculation": "folder-functions-open", ".calculation": "folder-functions-open", "_calculation": "folder-functions-open", "__calculation__": "folder-functions-open", "calculations": "folder-functions-open", ".calculations": "folder-functions-open", "_calculations": "folder-functions-open", "__calculations__": "folder-functions-open", "generator": "folder-generator-open", ".generator": "folder-generator-open", "_generator": "folder-generator-open", "__generator__": "folder-generator-open", "generators": "folder-generator-open", ".generators": "folder-generator-open", "_generators": "folder-generator-open", "__generators__": "folder-generator-open", "generated": "folder-generator-open", ".generated": "folder-generator-open", "_generated": "folder-generator-open", "__generated__": "folder-generator-open", "cfn-gen": "folder-generator-open", ".cfn-gen": "folder-generator-open", "_cfn-gen": "folder-generator-open", "__cfn-gen__": "folder-generator-open", "gen": "folder-generator-open", ".gen": "folder-generator-open", "_gen": "folder-generator-open", "__gen__": "folder-generator-open", "gens": "folder-generator-open", ".gens": "folder-generator-open", "_gens": "folder-generator-open", "__gens__": "folder-generator-open", "auto": "folder-generator-open", ".auto": "folder-generator-open", "_auto": "folder-generator-open", "__auto__": "folder-generator-open", "hook": "folder-hook-open", ".hook": "folder-hook-open", "_hook": "folder-hook-open", "__hook__": "folder-hook-open", "hooks": "folder-hook-open", ".hooks": "folder-hook-open", "_hooks": "folder-hook-open", "__hooks__": "folder-hook-open", "trigger": "folder-hook-open", ".trigger": "folder-hook-open", "_trigger": "folder-hook-open", "__trigger__": "folder-hook-open", "triggers": "folder-hook-open", ".triggers": "folder-hook-open", "_triggers": "folder-hook-open", "__triggers__": "folder-hook-open", "job": "folder-job-open", ".job": "folder-job-open", "_job": "folder-job-open", "__job__": "folder-job-open", "jobs": "folder-job-open", ".jobs": "folder-job-open", "_jobs": "folder-job-open", "__jobs__": "folder-job-open", "key": "folder-keys-open", ".key": "folder-keys-open", "_key": "folder-keys-open", "__key__": "folder-keys-open", "keys": "folder-keys-open", ".keys": "folder-keys-open", "_keys": "folder-keys-open", "__keys__": "folder-keys-open", "token": "folder-keys-open", ".token": "folder-keys-open", "_token": "folder-keys-open", "__token__": "folder-keys-open", "tokens": "folder-keys-open", ".tokens": "folder-keys-open", "_tokens": "folder-keys-open", "__tokens__": "folder-keys-open", "jwt": "folder-keys-open", ".jwt": "folder-keys-open", "_jwt": "folder-keys-open", "__jwt__": "folder-keys-open", "secret": "folder-keys-open", ".secret": "folder-keys-open", "_secret": "folder-keys-open", "__secret__": "folder-keys-open", "secrets": "folder-keys-open", ".secrets": "folder-keys-open", "_secrets": "folder-keys-open", "__secrets__": "folder-keys-open", "layout": "folder-layout-open", ".layout": "folder-layout-open", "_layout": "folder-layout-open", "__layout__": "folder-layout-open", "layouts": "folder-layout-open", ".layouts": "folder-layout-open", "_layouts": "folder-layout-open", "__layouts__": "folder-layout-open", "mail": "folder-mail-open", ".mail": "folder-mail-open", "_mail": "folder-mail-open", "__mail__": "folder-mail-open", "mails": "folder-mail-open", ".mails": "folder-mail-open", "_mails": "folder-mail-open", "__mails__": "folder-mail-open", "email": "folder-mail-open", ".email": "folder-mail-open", "_email": "folder-mail-open", "__email__": "folder-mail-open", "emails": "folder-mail-open", ".emails": "folder-mail-open", "_emails": "folder-mail-open", "__emails__": "folder-mail-open", "smtp": "folder-mail-open", ".smtp": "folder-mail-open", "_smtp": "folder-mail-open", "__smtp__": "folder-mail-open", "mailers": "folder-mail-open", ".mailers": "folder-mail-open", "_mailers": "folder-mail-open", "__mailers__": "folder-mail-open", "mappings": "folder-mappings-open", ".mappings": "folder-mappings-open", "_mappings": "folder-mappings-open", "__mappings__": "folder-mappings-open", "mapping": "folder-mappings-open", ".mapping": "folder-mappings-open", "_mapping": "folder-mappings-open", "__mapping__": "folder-mappings-open", "meta": "folder-meta-open", ".meta": "folder-meta-open", "_meta": "folder-meta-open", "__meta__": "folder-meta-open", "changesets": "folder-changesets-open", ".changesets": "folder-changesets-open", "_changesets": "folder-changesets-open", "__changesets__": "folder-changesets-open", "changeset": "folder-changesets-open", ".changeset": "folder-changesets-open", "_changeset": "folder-changesets-open", "__changeset__": "folder-changesets-open", "package": "folder-packages-open", ".package": "folder-packages-open", "_package": "folder-packages-open", "__package__": "folder-packages-open", "packages": "folder-packages-open", ".packages": "folder-packages-open", "_packages": "folder-packages-open", "__packages__": "folder-packages-open", "pkg": "folder-packages-open", ".pkg": "folder-packages-open", "_pkg": "folder-packages-open", "__pkg__": "folder-packages-open", "pkgs": "folder-packages-open", ".pkgs": "folder-packages-open", "_pkgs": "folder-packages-open", "__pkgs__": "folder-packages-open", "shared": "folder-shared-open", ".shared": "folder-shared-open", "_shared": "folder-shared-open", "__shared__": "folder-shared-open", "common": "folder-shared-open", ".common": "folder-shared-open", "_common": "folder-shared-open", "__common__": "folder-shared-open", "glsl": "folder-shader-open", ".glsl": "folder-shader-open", "_glsl": "folder-shader-open", "__glsl__": "folder-shader-open", "hlsl": "folder-shader-open", ".hlsl": "folder-shader-open", "_hlsl": "folder-shader-open", "__hlsl__": "folder-shader-open", "shader": "folder-shader-open", ".shader": "folder-shader-open", "_shader": "folder-shader-open", "__shader__": "folder-shader-open", "shaders": "folder-shader-open", ".shaders": "folder-shader-open", "_shaders": "folder-shader-open", "__shaders__": "folder-shader-open", "stack": "folder-stack-open", ".stack": "folder-stack-open", "_stack": "folder-stack-open", "__stack__": "folder-stack-open", "stacks": "folder-stack-open", ".stacks": "folder-stack-open", "_stacks": "folder-stack-open", "__stacks__": "folder-stack-open", "template": "folder-template-open", ".template": "folder-template-open", "_template": "folder-template-open", "__template__": "folder-template-open", "templates": "folder-template-open", ".templates": "folder-template-open", "_templates": "folder-template-open", "__templates__": "folder-template-open", "util": "folder-utils-open", ".util": "folder-utils-open", "_util": "folder-utils-open", "__util__": "folder-utils-open", "utils": "folder-utils-open", ".utils": "folder-utils-open", "_utils": "folder-utils-open", "__utils__": "folder-utils-open", "utility": "folder-utils-open", ".utility": "folder-utils-open", "_utility": "folder-utils-open", "__utility__": "folder-utils-open", "utilities": "folder-utils-open", ".utilities": "folder-utils-open", "_utilities": "folder-utils-open", "__utilities__": "folder-utils-open", "supabase": "folder-supabase-open", ".supabase": "folder-supabase-open", "_supabase": "folder-supabase-open", "__supabase__": "folder-supabase-open", "private": "folder-private-open", ".private": "folder-private-open", "_private": "folder-private-open", "__private__": "folder-private-open", "linux": "folder-linux-open", ".linux": "folder-linux-open", "_linux": "folder-linux-open", "__linux__": "folder-linux-open", "linuxbsd": "folder-linux-open", ".linuxbsd": "folder-linux-open", "_linuxbsd": "folder-linux-open", "__linuxbsd__": "folder-linux-open", "unix": "folder-linux-open", ".unix": "folder-linux-open", "_unix": "folder-linux-open", "__unix__": "folder-linux-open", "windows": "folder-windows-open", ".windows": "folder-windows-open", "_windows": "folder-windows-open", "__windows__": "folder-windows-open", "win": "folder-windows-open", ".win": "folder-windows-open", "_win": "folder-windows-open", "__win__": "folder-windows-open", "macos": "folder-macos-open", ".macos": "folder-macos-open", "_macos": "folder-macos-open", "__macos__": "folder-macos-open", "mac": "folder-macos-open", ".mac": "folder-macos-open", "_mac": "folder-macos-open", "__mac__": "folder-macos-open", "DS_Store": "folder-macos-open", ".DS_Store": "folder-macos-open", "_DS_Store": "folder-macos-open", "__DS_Store__": "folder-macos-open", "error": "folder-error-open", ".error": "folder-error-open", "_error": "folder-error-open", "__error__": "folder-error-open", "errors": "folder-error-open", ".errors": "folder-error-open", "_errors": "folder-error-open", "__errors__": "folder-error-open", "err": "folder-error-open", ".err": "folder-error-open", "_err": "folder-error-open", "__err__": "folder-error-open", "errs": "folder-error-open", ".errs": "folder-error-open", "_errs": "folder-error-open", "__errs__": "folder-error-open", "crash": "folder-error-open", ".crash": "folder-error-open", "_crash": "folder-error-open", "__crash__": "folder-error-open", "crashes": "folder-error-open", ".crashes": "folder-error-open", "_crashes": "folder-error-open", "__crashes__": "folder-error-open", "event": "folder-event-open", ".event": "folder-event-open", "_event": "folder-event-open", "__event__": "folder-event-open", "events": "folder-event-open", ".events": "folder-event-open", "_events": "folder-event-open", "__events__": "folder-event-open", "auth": "folder-secure-open", ".auth": "folder-secure-open", "_auth": "folder-secure-open", "__auth__": "folder-secure-open", "authentication": "folder-secure-open", ".authentication": "folder-secure-open", "_authentication": "folder-secure-open", "__authentication__": "folder-secure-open", "secure": "folder-secure-open", ".secure": "folder-secure-open", "_secure": "folder-secure-open", "__secure__": "folder-secure-open", "security": "folder-secure-open", ".security": "folder-secure-open", "_security": "folder-secure-open", "__security__": "folder-secure-open", "cert": "folder-secure-open", ".cert": "folder-secure-open", "_cert": "folder-secure-open", "__cert__": "folder-secure-open", "certs": "folder-secure-open", ".certs": "folder-secure-open", "_certs": "folder-secure-open", "__certs__": "folder-secure-open", "certificate": "folder-secure-open", ".certificate": "folder-secure-open", "_certificate": "folder-secure-open", "__certificate__": "folder-secure-open", "certificates": "folder-secure-open", ".certificates": "folder-secure-open", "_certificates": "folder-secure-open", "__certificates__": "folder-secure-open", "ssl": "folder-secure-open", ".ssl": "folder-secure-open", "_ssl": "folder-secure-open", "__ssl__": "folder-secure-open", "custom": "folder-custom-open", ".custom": "folder-custom-open", "_custom": "folder-custom-open", "__custom__": "folder-custom-open", "customs": "folder-custom-open", ".customs": "folder-custom-open", "_customs": "folder-custom-open", "__customs__": "folder-custom-open", "draft": "folder-mock-open", ".draft": "folder-mock-open", "_draft": "folder-mock-open", "__draft__": "folder-mock-open", "drafts": "folder-mock-open", ".drafts": "folder-mock-open", "_drafts": "folder-mock-open", "__drafts__": "folder-mock-open", "mock": "folder-mock-open", ".mock": "folder-mock-open", "_mock": "folder-mock-open", "__mock__": "folder-mock-open", "mocks": "folder-mock-open", ".mocks": "folder-mock-open", "_mocks": "folder-mock-open", "__mocks__": "folder-mock-open", "fixture": "folder-mock-open", ".fixture": "folder-mock-open", "_fixture": "folder-mock-open", "__fixture__": "folder-mock-open", "fixtures": "folder-mock-open", ".fixtures": "folder-mock-open", "_fixtures": "folder-mock-open", "__fixtures__": "folder-mock-open", "concept": "folder-mock-open", ".concept": "folder-mock-open", "_concept": "folder-mock-open", "__concept__": "folder-mock-open", "concepts": "folder-mock-open", ".concepts": "folder-mock-open", "_concepts": "folder-mock-open", "__concepts__": "folder-mock-open", "sketch": "folder-mock-open", ".sketch": "folder-mock-open", "_sketch": "folder-mock-open", "__sketch__": "folder-mock-open", "sketches": "folder-mock-open", ".sketches": "folder-mock-open", "_sketches": "folder-mock-open", "__sketches__": "folder-mock-open", "syntax": "folder-syntax-open", ".syntax": "folder-syntax-open", "_syntax": "folder-syntax-open", "__syntax__": "folder-syntax-open", "syntaxes": "folder-syntax-open", ".syntaxes": "folder-syntax-open", "_syntaxes": "folder-syntax-open", "__syntaxes__": "folder-syntax-open", "spellcheck": "folder-syntax-open", ".spellcheck": "folder-syntax-open", "_spellcheck": "folder-syntax-open", "__spellcheck__": "folder-syntax-open", "vm": "folder-vm-open", ".vm": "folder-vm-open", "_vm": "folder-vm-open", "__vm__": "folder-vm-open", "vms": "folder-vm-open", ".vms": "folder-vm-open", "_vms": "folder-vm-open", "__vms__": "folder-vm-open", "stylus": "folder-stylus-open", ".stylus": "folder-stylus-open", "_stylus": "folder-stylus-open", "__stylus__": "folder-stylus-open", "flow-typed": "folder-flow-open", ".flow-typed": "folder-flow-open", "_flow-typed": "folder-flow-open", "__flow-typed__": "folder-flow-open", "rule": "folder-rules-open", ".rule": "folder-rules-open", "_rule": "folder-rules-open", "__rule__": "folder-rules-open", "rules": "folder-rules-open", ".rules": "folder-rules-open", "_rules": "folder-rules-open", "__rules__": "folder-rules-open", "validation": "folder-rules-open", ".validation": "folder-rules-open", "_validation": "folder-rules-open", "__validation__": "folder-rules-open", "validations": "folder-rules-open", ".validations": "folder-rules-open", "_validations": "folder-rules-open", "__validations__": "folder-rules-open", "validator": "folder-rules-open", ".validator": "folder-rules-open", "_validator": "folder-rules-open", "__validator__": "folder-rules-open", "validators": "folder-rules-open", ".validators": "folder-rules-open", "_validators": "folder-rules-open", "__validators__": "folder-rules-open", "review": "folder-review-open", ".review": "folder-review-open", "_review": "folder-review-open", "__review__": "folder-review-open", "reviews": "folder-review-open", ".reviews": "folder-review-open", "_reviews": "folder-review-open", "__reviews__": "folder-review-open", "revisal": "folder-review-open", ".revisal": "folder-review-open", "_revisal": "folder-review-open", "__revisal__": "folder-review-open", "revisals": "folder-review-open", ".revisals": "folder-review-open", "_revisals": "folder-review-open", "__revisals__": "folder-review-open", "reviewed": "folder-review-open", ".reviewed": "folder-review-open", "_reviewed": "folder-review-open", "__reviewed__": "folder-review-open", "anim": "folder-animation-open", ".anim": "folder-animation-open", "_anim": "folder-animation-open", "__anim__": "folder-animation-open", "anims": "folder-animation-open", ".anims": "folder-animation-open", "_anims": "folder-animation-open", "__anims__": "folder-animation-open", "animation": "folder-animation-open", ".animation": "folder-animation-open", "_animation": "folder-animation-open", "__animation__": "folder-animation-open", "animations": "folder-animation-open", ".animations": "folder-animation-open", "_animations": "folder-animation-open", "__animations__": "folder-animation-open", "animated": "folder-animation-open", ".animated": "folder-animation-open", "_animated": "folder-animation-open", "__animated__": "folder-animation-open", "guard": "folder-guard-open", ".guard": "folder-guard-open", "_guard": "folder-guard-open", "__guard__": "folder-guard-open", "guards": "folder-guard-open", ".guards": "folder-guard-open", "_guards": "folder-guard-open", "__guards__": "folder-guard-open", "prisma": "folder-prisma-open", ".prisma": "folder-prisma-open", "_prisma": "folder-prisma-open", "__prisma__": "folder-prisma-open", "pipe": "folder-pipe-open", ".pipe": "folder-pipe-open", "_pipe": "folder-pipe-open", "__pipe__": "folder-pipe-open", "pipes": "folder-pipe-open", ".pipes": "folder-pipe-open", "_pipes": "folder-pipe-open", "__pipes__": "folder-pipe-open", "svg": "folder-svg-open", ".svg": "folder-svg-open", "_svg": "folder-svg-open", "__svg__": "folder-svg-open", "svgs": "folder-svg-open", ".svgs": "folder-svg-open", "_svgs": "folder-svg-open", "__svgs__": "folder-svg-open", "terraform": "folder-terraform-open", ".terraform": "folder-terraform-open", "_terraform": "folder-terraform-open", "__terraform__": "folder-terraform-open", "mobile": "folder-mobile-open", ".mobile": "folder-mobile-open", "_mobile": "folder-mobile-open", "__mobile__": "folder-mobile-open", "mobiles": "folder-mobile-open", ".mobiles": "folder-mobile-open", "_mobiles": "folder-mobile-open", "__mobiles__": "folder-mobile-open", "portable": "folder-mobile-open", ".portable": "folder-mobile-open", "_portable": "folder-mobile-open", "__portable__": "folder-mobile-open", "portability": "folder-mobile-open", ".portability": "folder-mobile-open", "_portability": "folder-mobile-open", "__portability__": "folder-mobile-open", "stencil": "folder-stencil-open", ".stencil": "folder-stencil-open", "_stencil": "folder-stencil-open", "__stencil__": "folder-stencil-open", "firebase": "folder-firebase-open", ".firebase": "folder-firebase-open", "_firebase": "folder-firebase-open", "__firebase__": "folder-firebase-open", "svelte": "folder-svelte-open", ".svelte": "folder-svelte-open", "_svelte": "folder-svelte-open", "__svelte__": "folder-svelte-open", "svelte-kit": "folder-svelte-open", ".svelte-kit": "folder-svelte-open", "_svelte-kit": "folder-svelte-open", "__svelte-kit__": "folder-svelte-open", "update": "folder-update-open", ".update": "folder-update-open", "_update": "folder-update-open", "__update__": "folder-update-open", "updates": "folder-update-open", ".updates": "folder-update-open", "_updates": "folder-update-open", "__updates__": "folder-update-open", "upgrade": "folder-update-open", ".upgrade": "folder-update-open", "_upgrade": "folder-update-open", "__upgrade__": "folder-update-open", "upgrades": "folder-update-open", ".upgrades": "folder-update-open", "_upgrades": "folder-update-open", "__upgrades__": "folder-update-open", "idea": "folder-intellij-open", ".idea": "folder-intellij-open", "_idea": "folder-intellij-open", "__idea__": "folder-intellij-open", "azure-pipelines": "folder-azure-pipelines-open", ".azure-pipelines": "folder-azure-pipelines-open", "_azure-pipelines": "folder-azure-pipelines-open", "__azure-pipelines__": "folder-azure-pipelines-open", "azure-pipelines-ci": "folder-azure-pipelines-open", ".azure-pipelines-ci": "folder-azure-pipelines-open", "_azure-pipelines-ci": "folder-azure-pipelines-open", "__azure-pipelines-ci__": "folder-azure-pipelines-open", "mjml": "folder-mjml-open", ".mjml": "folder-mjml-open", "_mjml": "folder-mjml-open", "__mjml__": "folder-mjml-open", "admin": "folder-admin-open", ".admin": "folder-admin-open", "_admin": "folder-admin-open", "__admin__": "folder-admin-open", "admins": "folder-admin-open", ".admins": "folder-admin-open", "_admins": "folder-admin-open", "__admins__": "folder-admin-open", "manager": "folder-admin-open", ".manager": "folder-admin-open", "_manager": "folder-admin-open", "__manager__": "folder-admin-open", "managers": "folder-admin-open", ".managers": "folder-admin-open", "_managers": "folder-admin-open", "__managers__": "folder-admin-open", "moderator": "folder-admin-open", ".moderator": "folder-admin-open", "_moderator": "folder-admin-open", "__moderator__": "folder-admin-open", "moderators": "folder-admin-open", ".moderators": "folder-admin-open", "_moderators": "folder-admin-open", "__moderators__": "folder-admin-open", "scala": "folder-scala-open", ".scala": "folder-scala-open", "_scala": "folder-scala-open", "__scala__": "folder-scala-open", "connection": "folder-connection-open", ".connection": "folder-connection-open", "_connection": "folder-connection-open", "__connection__": "folder-connection-open", "connections": "folder-connection-open", ".connections": "folder-connection-open", "_connections": "folder-connection-open", "__connections__": "folder-connection-open", "integration": "folder-connection-open", ".integration": "folder-connection-open", "_integration": "folder-connection-open", "__integration__": "folder-connection-open", "integrations": "folder-connection-open", ".integrations": "folder-connection-open", "_integrations": "folder-connection-open", "__integrations__": "folder-connection-open", "quasar": "folder-quasar-open", ".quasar": "folder-quasar-open", "_quasar": "folder-quasar-open", "__quasar__": "folder-quasar-open", "next": "folder-next-open", ".next": "folder-next-open", "_next": "folder-next-open", "__next__": "folder-next-open", "cobol": "folder-cobol-open", ".cobol": "folder-cobol-open", "_cobol": "folder-cobol-open", "__cobol__": "folder-cobol-open", "yarn": "folder-yarn-open", ".yarn": "folder-yarn-open", "_yarn": "folder-yarn-open", "__yarn__": "folder-yarn-open", "husky": "folder-husky-open", ".husky": "folder-husky-open", "_husky": "folder-husky-open", "__husky__": "folder-husky-open", "storybook": "folder-storybook-open", ".storybook": "folder-storybook-open", "_storybook": "folder-storybook-open", "__storybook__": "folder-storybook-open", "stories": "folder-storybook-open", ".stories": "folder-storybook-open", "_stories": "folder-storybook-open", "__stories__": "folder-storybook-open", "base": "folder-base-open", ".base": "folder-base-open", "_base": "folder-base-open", "__base__": "folder-base-open", "bases": "folder-base-open", ".bases": "folder-base-open", "_bases": "folder-base-open", "__bases__": "folder-base-open", "cart": "folder-cart-open", ".cart": "folder-cart-open", "_cart": "folder-cart-open", "__cart__": "folder-cart-open", "shopping-cart": "folder-cart-open", ".shopping-cart": "folder-cart-open", "_shopping-cart": "folder-cart-open", "__shopping-cart__": "folder-cart-open", "shopping": "folder-cart-open", ".shopping": "folder-cart-open", "_shopping": "folder-cart-open", "__shopping__": "folder-cart-open", "shop": "folder-cart-open", ".shop": "folder-cart-open", "_shop": "folder-cart-open", "__shop__": "folder-cart-open", "home": "folder-home-open", ".home": "folder-home-open", "_home": "folder-home-open", "__home__": "folder-home-open", "start": "folder-home-open", ".start": "folder-home-open", "_start": "folder-home-open", "__start__": "folder-home-open", "project": "folder-project-open", ".project": "folder-project-open", "_project": "folder-project-open", "__project__": "folder-project-open", "projects": "folder-project-open", ".projects": "folder-project-open", "_projects": "folder-project-open", "__projects__": "folder-project-open", "interface": "folder-interface-open", ".interface": "folder-interface-open", "_interface": "folder-interface-open", "__interface__": "folder-interface-open", "interfaces": "folder-interface-open", ".interfaces": "folder-interface-open", "_interfaces": "folder-interface-open", "__interfaces__": "folder-interface-open", "netlify": "folder-netlify-open", ".netlify": "folder-netlify-open", "_netlify": "folder-netlify-open", "__netlify__": "folder-netlify-open", "enum": "folder-enum-open", ".enum": "folder-enum-open", "_enum": "folder-enum-open", "__enum__": "folder-enum-open", "enums": "folder-enum-open", ".enums": "folder-enum-open", "_enums": "folder-enum-open", "__enums__": "folder-enum-open", "pact": "folder-contract-open", ".pact": "folder-contract-open", "_pact": "folder-contract-open", "__pact__": "folder-contract-open", "pacts": "folder-contract-open", ".pacts": "folder-contract-open", "_pacts": "folder-contract-open", "__pacts__": "folder-contract-open", "contract": "folder-contract-open", ".contract": "folder-contract-open", "_contract": "folder-contract-open", "__contract__": "folder-contract-open", "contracts": "folder-contract-open", ".contracts": "folder-contract-open", "_contracts": "folder-contract-open", "__contracts__": "folder-contract-open", "contract-testing": "folder-contract-open", ".contract-testing": "folder-contract-open", "_contract-testing": "folder-contract-open", "__contract-testing__": "folder-contract-open", "contract-test": "folder-contract-open", ".contract-test": "folder-contract-open", "_contract-test": "folder-contract-open", "__contract-test__": "folder-contract-open", "contract-tests": "folder-contract-open", ".contract-tests": "folder-contract-open", "_contract-tests": "folder-contract-open", "__contract-tests__": "folder-contract-open", "queue": "folder-queue-open", ".queue": "folder-queue-open", "_queue": "folder-queue-open", "__queue__": "folder-queue-open", "queues": "folder-queue-open", ".queues": "folder-queue-open", "_queues": "folder-queue-open", "__queues__": "folder-queue-open", "bull": "folder-queue-open", ".bull": "folder-queue-open", "_bull": "folder-queue-open", "__bull__": "folder-queue-open", "mq": "folder-queue-open", ".mq": "folder-queue-open", "_mq": "folder-queue-open", "__mq__": "folder-queue-open", "vercel": "folder-vercel-open", ".vercel": "folder-vercel-open", "_vercel": "folder-vercel-open", "__vercel__": "folder-vercel-open", "now": "folder-vercel-open", ".now": "folder-vercel-open", "_now": "folder-vercel-open", "__now__": "folder-vercel-open", "cypress": "folder-cypress-open", ".cypress": "folder-cypress-open", "_cypress": "folder-cypress-open", "__cypress__": "folder-cypress-open", "decorator": "folder-decorators-open", ".decorator": "folder-decorators-open", "_decorator": "folder-decorators-open", "__decorator__": "folder-decorators-open", "decorators": "folder-decorators-open", ".decorators": "folder-decorators-open", "_decorators": "folder-decorators-open", "__decorators__": "folder-decorators-open", "java": "folder-java-open", ".java": "folder-java-open", "_java": "folder-java-open", "__java__": "folder-java-open", "resolver": "folder-resolver-open", ".resolver": "folder-resolver-open", "_resolver": "folder-resolver-open", "__resolver__": "folder-resolver-open", "resolvers": "folder-resolver-open", ".resolvers": "folder-resolver-open", "_resolvers": "folder-resolver-open", "__resolvers__": "folder-resolver-open", "angular": "folder-angular-open", ".angular": "folder-angular-open", "_angular": "folder-angular-open", "__angular__": "folder-angular-open", "unity": "folder-unity-open", ".unity": "folder-unity-open", "_unity": "folder-unity-open", "__unity__": "folder-unity-open", "pdf": "folder-pdf-open", ".pdf": "folder-pdf-open", "_pdf": "folder-pdf-open", "__pdf__": "folder-pdf-open", "pdfs": "folder-pdf-open", ".pdfs": "folder-pdf-open", "_pdfs": "folder-pdf-open", "__pdfs__": "folder-pdf-open", "protobuf": "folder-proto-open", ".protobuf": "folder-proto-open", "_protobuf": "folder-proto-open", "__protobuf__": "folder-proto-open", "protobufs": "folder-proto-open", ".protobufs": "folder-proto-open", "_protobufs": "folder-proto-open", "__protobufs__": "folder-proto-open", "proto": "folder-proto-open", ".proto": "folder-proto-open", "_proto": "folder-proto-open", "protos": "folder-proto-open", ".protos": "folder-proto-open", "_protos": "folder-proto-open", "__protos__": "folder-proto-open", "plastic": "folder-plastic-open", ".plastic": "folder-plastic-open", "_plastic": "folder-plastic-open", "__plastic__": "folder-plastic-open", "gamemaker": "folder-gamemaker-open", ".gamemaker": "folder-gamemaker-open", "_gamemaker": "folder-gamemaker-open", "__gamemaker__": "folder-gamemaker-open", "gamemaker2": "folder-gamemaker-open", ".gamemaker2": "folder-gamemaker-open", "_gamemaker2": "folder-gamemaker-open", "__gamemaker2__": "folder-gamemaker-open", "hg": "folder-mercurial-open", ".hg": "folder-mercurial-open", "_hg": "folder-mercurial-open", "__hg__": "folder-mercurial-open", "hghooks": "folder-mercurial-open", ".hghooks": "folder-mercurial-open", "_hghooks": "folder-mercurial-open", "__hghooks__": "folder-mercurial-open", "hgext": "folder-mercurial-open", ".hgext": "folder-mercurial-open", "_hgext": "folder-mercurial-open", "__hgext__": "folder-mercurial-open", "godot": "folder-godot-open", ".godot": "folder-godot-open", "_godot": "folder-godot-open", "__godot__": "folder-godot-open", "godot-cpp": "folder-godot-open", ".godot-cpp": "folder-godot-open", "_godot-cpp": "folder-godot-open", "__godot-cpp__": "folder-godot-open", "lottie": "folder-lottie-open", ".lottie": "folder-lottie-open", "_lottie": "folder-lottie-open", "__lottie__": "folder-lottie-open", "lotties": "folder-lottie-open", ".lotties": "folder-lottie-open", "_lotties": "folder-lottie-open", "__lotties__": "folder-lottie-open", "lottiefiles": "folder-lottie-open", ".lottiefiles": "folder-lottie-open", "_lottiefiles": "folder-lottie-open", "__lottiefiles__": "folder-lottie-open", "taskfile": "folder-taskfile-open", ".taskfile": "folder-taskfile-open", "_taskfile": "folder-taskfile-open", "__taskfile__": "folder-taskfile-open", "taskfiles": "folder-taskfile-open", ".taskfiles": "folder-taskfile-open", "_taskfiles": "folder-taskfile-open", "__taskfiles__": "folder-taskfile-open", "cloudflare": "folder-cloudflare-open", ".cloudflare": "folder-cloudflare-open", "_cloudflare": "folder-cloudflare-open", "__cloudflare__": "folder-cloudflare-open", "seeds": "folder-seeders-open", ".seeds": "folder-seeders-open", "_seeds": "folder-seeders-open", "__seeds__": "folder-seeders-open", "seeders": "folder-seeders-open", ".seeders": "folder-seeders-open", "_seeders": "folder-seeders-open", "__seeders__": "folder-seeders-open", "seed": "folder-seeders-open", ".seed": "folder-seeders-open", "_seed": "folder-seeders-open", "__seed__": "folder-seeders-open", "seeding": "folder-seeders-open", ".seeding": "folder-seeders-open", "_seeding": "folder-seeders-open", "__seeding__": "folder-seeders-open"}, "fileExtensions": {"htm": "html", "xhtml": "html", "html_vm": "html", "asp": "html", "jade": "pug", "pug": "pug", "md": "markdown", "markdown": "markdown", "rst": "markdown", "blink": "blink", "css": "css", "scss": "sass", "sass": "sass", "less": "less", "json": "json", "jsonc": "json", "tsbuildinfo": "json", "json5": "json", "jsonl": "json", "ndjson": "json", "hjson": "h<PERSON><PERSON>", "jinja": "jinja", "jinja2": "jinja", "j2": "jinja", "jinja-html": "jinja", "proto": "proto", "sublime-project": "sublime", "sublime-workspace": "sublime", "tw": "twine", "twee": "twine", "yml.dist": "yaml", "yaml.dist": "yaml", "YAML-tmLanguage": "yaml", "xml": "xml", "plist": "xml", "xsd": "xml", "dtd": "xml", "xsl": "xml", "xslt": "xml", "resx": "xml", "iml": "xml", "xquery": "xml", "tmLanguage": "xml", "manifest": "xml", "project": "xml", "xml.dist": "xml", "xml.dist.sample": "xml", "dmn": "xml", "jrxml": "xml", "png": "image", "jpeg": "image", "jpg": "image", "gif": "image", "ico": "image", "tif": "image", "tiff": "image", "psd": "image", "psb": "image", "ami": "image", "apx": "image", "avif": "image", "bmp": "image", "bpg": "image", "brk": "image", "cur": "image", "dds": "image", "dng": "image", "exr": "image", "fpx": "image", "gbr": "image", "img": "image", "jbig2": "image", "jb2": "image", "jng": "image", "jxr": "image", "pgf": "image", "pic": "image", "raw": "image", "webp": "image", "eps": "image", "afphoto": "image", "ase": "image", "aseprite": "image", "clip": "image", "cpt": "image", "heif": "image", "heic": "image", "kra": "image", "mdp": "image", "ora": "image", "pdn": "image", "reb": "image", "sai": "image", "tga": "image", "xcf": "image", "jfif": "image", "ppm": "image", "pbm": "image", "pgm": "image", "pnm": "image", "icns": "image", "esx": "javascript", "mjs": "javascript", "jsx": "react", "tsx": "react_ts", "routing.ts": "routing", "routing.tsx": "routing", "routing.js": "routing", "routing.jsx": "routing", "routes.ts": "routing", "routes.tsx": "routing", "routes.js": "routing", "routes.jsx": "routing", "ini": "settings", "dlc": "settings", "config": "settings", "conf": "settings", "properties": "settings", "prop": "settings", "settings": "settings", "option": "settings", "props": "settings", "toml": "settings", "prefs": "settings", "sln.dotsettings": "settings", "sln.dotsettings.user": "settings", "cfg": "settings", "d.ts": "typescript-def", "d.cts": "typescript-def", "d.mts": "typescript-def", "marko": "<PERSON><PERSON><PERSON><PERSON>", "astro": "astro", "pdf": "pdf", "xlsx": "table", "xlsm": "table", "xls": "table", "csv": "table", "tsv": "table", "psv": "table", "ods": "table", "vscodeignore": "vscode", "vsixmanifest": "vscode", "vsix": "vscode", "code-workplace": "vscode", "code-workspace": "vscode", "code-profile": "vscode", "code-snippets": "vscode", "csproj": "visualstudio", "ruleset": "visualstudio", "sln": "visualstudio", "slnx": "visualstudio", "suo": "visualstudio", "vb": "visualstudio", "vbs": "visualstudio", "vcxitems": "visualstudio", "vcxitems.filters": "visualstudio", "vcxproj": "visualstudio", "vcxproj.filters": "visualstudio", "pdb": "database", "sql": "database", "pks": "database", "pkb": "database", "accdb": "database", "mdb": "database", "sqlite": "database", "sqlite3": "database", "pgsql": "database", "postgres": "database", "plpgsql": "database", "psql": "database", "db": "database", "db3": "database", "dblite": "database", "dblite3": "database", "debugsymbols": "database", "kql": "kusto", "cs": "csharp", "csx": "csharp", "csharp": "csharp", "qs": "qsharp", "zip": "zip", "tar": "zip", "gz": "zip", "xz": "zip", "lzma": "zip", "lz4": "zip", "br": "zip", "bz2": "zip", "bzip2": "zip", "gzip": "zip", "brotli": "zip", "7z": "zip", "rar": "zip", "tz": "zip", "txz": "zip", "tgz": "zip", "zst": "zip", "vala": "vala", "zig": "zig", "zon": "zig", "exe": "exe", "msi": "exe", "dat": "hex", "bin": "hex", "hex": "hex", "java": "java", "jsp": "java", "jar": "jar", "class": "javaclass", "c": "c", "i": "c", "mi": "c", "h": "h", "cc": "cpp", "cpp": "cpp", "cxx": "cpp", "c++": "cpp", "cp": "cpp", "mii": "cpp", "ii": "cpp", "hh": "hpp", "hpp": "hpp", "hxx": "hpp", "h++": "hpp", "hp": "hpp", "tcc": "hpp", "inl": "hpp", "rc": "rc", "go": "go", "py": "python", "pyc": "python-misc", "whl": "python-misc", "url": "url", "sh": "console", "ksh": "console", "csh": "console", "tcsh": "console", "zsh": "console", "bash": "console", "bat": "console", "cmd": "console", "awk": "console", "fish": "console", "exp": "console", "nu": "console", "ps1": "powershell", "psm1": "powershell", "psd1": "powershell", "ps1xml": "powershell", "psc1": "powershell", "pssc": "powershell", "gradle": "gradle", "doc": "word", "docx": "word", "rtf": "word", "odt": "word", "cer": "certificate", "cert": "certificate", "crt": "certificate", "pub": "key", "key": "key", "pem": "key", "asc": "key", "gpg": "key", "passwd": "key", "woff": "font", "woff2": "font", "ttf": "font", "eot": "font", "suit": "font", "otf": "font", "bmap": "font", "fnt": "font", "odttf": "font", "ttc": "font", "font": "font", "fonts": "font", "sui": "font", "ntf": "font", "mrf": "font", "lib": "lib", "bib": "lib", "a": "lib", "dll": "dll", "ilk": "dll", "so": "dll", "rb": "ruby", "erb": "ruby", "rbs": "ruby", "fs": "fsharp", "fsx": "fsharp", "fsi": "fsharp", "fsproj": "fsharp", "swift": "swift", "ino": "a<PERSON><PERSON><PERSON>", "dockerignore": "docker", "dockerfile": "docker", "docker-compose.yml": "docker", "docker-compose.yaml": "docker", "containerignore": "docker", "containerfile": "docker", "compose.yaml": "docker", "compose.yml": "docker", "tex": "tex", "sty": "tex", "dtx": "tex", "ltx": "tex", "pptx": "powerpoint", "ppt": "powerpoint", "pptm": "powerpoint", "potx": "powerpoint", "potm": "powerpoint", "ppsx": "powerpoint", "ppsm": "powerpoint", "pps": "powerpoint", "ppam": "powerpoint", "ppa": "powerpoint", "odp": "powerpoint", "webm": "video", "mkv": "video", "flv": "video", "vob": "video", "ogv": "video", "ogg": "video", "gifv": "video", "avi": "video", "mov": "video", "qt": "video", "wmv": "video", "yuv": "video", "rm": "video", "rmvb": "video", "mp4": "video", "m4v": "video", "mpg": "video", "mp2": "video", "mpeg": "video", "mpe": "video", "mpv": "video", "m2v": "video", "vdi": "virtual", "vbox": "virtual", "vbox-prev": "virtual", "ved": "vedic", "veda": "vedic", "vedic": "vedic", "ics": "email", "mp3": "audio", "flac": "audio", "m4a": "audio", "wma": "audio", "aiff": "audio", "wav": "audio", "coffee": "coffee", "cson": "coffee", "iced": "coffee", "txt": "document", "graphql": "graphql", "gql": "graphql", "rs": "rust", "ron": "rust", "raml": "raml", "xaml": "xaml", "hs": "haskell", "kt": "kotlin", "kts": "kotlin", "otne": "otne", "patch": "git", "lua": "lua", "clj": "clojure", "cljs": "clojure", "cljc": "clojure", "groovy": "groovy", "r": "r", "rmd": "r", "dart": "dart", "freezed.dart": "dart_generated", "g.dart": "dart_generated", "as": "actionscript", "mxml": "mxml", "ahk": "autohotkey", "swf": "flash", "swc": "swc", "cmake": "cmake", "asm": "assembly", "a51": "assembly", "inc": "assembly", "nasm": "assembly", "s": "assembly", "ms": "assembly", "agc": "assembly", "ags": "assembly", "aea": "assembly", "argus": "assembly", "mitigus": "assembly", "binsource": "assembly", "vue": "vue", "ml": "ocaml", "mli": "ocaml", "cmx": "ocaml", "odin": "odin", "js.map": "javascript-map", "mjs.map": "javascript-map", "cjs.map": "javascript-map", "css.map": "css-map", "lock": "lock", "hbs": "handlebars", "mustache": "handlebars", "pm": "perl", "raku": "perl", "hx": "haxe", "spec.ts": "test-ts", "spec.cts": "test-ts", "spec.mts": "test-ts", "cy.ts": "test-ts", "e2e-spec.ts": "test-ts", "e2e-spec.cts": "test-ts", "e2e-spec.mts": "test-ts", "test.ts": "test-ts", "test.cts": "test-ts", "test.mts": "test-ts", "ts.snap": "test-ts", "spec-d.ts": "test-ts", "test-d.ts": "test-ts", "spec.tsx": "test-jsx", "test.tsx": "test-jsx", "tsx.snap": "test-jsx", "spec.jsx": "test-jsx", "test.jsx": "test-jsx", "jsx.snap": "test-jsx", "cy.jsx": "test-jsx", "cy.tsx": "test-jsx", "spec-d.tsx": "test-jsx", "test-d.tsx": "test-jsx", "spec.js": "test-js", "spec.cjs": "test-js", "spec.mjs": "test-js", "e2e-spec.js": "test-js", "e2e-spec.cjs": "test-js", "e2e-spec.mjs": "test-js", "test.js": "test-js", "test.cjs": "test-js", "test.mjs": "test-js", "js.snap": "test-js", "cy.js": "test-js", "module.ts": "angular", "module.js": "angular", "ng-template": "angular", "component.ts": "angular-component", "component.js": "angular-component", "guard.ts": "angular-guard", "guard.js": "angular-guard", "service.ts": "angular-service", "service.js": "angular-service", "pipe.ts": "angular-pipe", "pipe.js": "angular-pipe", "filter.js": "angular-pipe", "directive.ts": "angular-directive", "directive.js": "angular-directive", "resolver.ts": "angular-resolver", "resolver.js": "angular-resolver", "pp": "puppet", "ex": "elixir", "exs": "elixir", "eex": "elixir", "leex": "elixir", "heex": "elixir", "ls": "livescript", "erl": "erlang", "twig": "twig", "jl": "julia", "elm": "elm", "pure": "purescript", "purs": "purescript", "tpl": "smarty", "styl": "stylus", "re": "reason", "rei": "reason", "cmj": "bucklescript", "merlin": "merlin", "vhd": "verilog", "sv": "verilog", "svh": "verilog", "nb": "mathematica", "wl": "wolframlanguage", "wls": "wolframlanguage", "njk": "nunjucks", "nunjucks": "nunjucks", "robot": "robot", "sol": "solidity", "au3": "autoit", "haml": "haml", "yang": "yang", "mjml": "mjml", "tf": "terraform", "tf.json": "terraform", "tfvars": "terraform", "tfstate": "terraform", "tfbackend": "terraform", "blade.php": "laravel", "inky.php": "laravel", "applescript": "applescript", "ipa": "applescript", "cake": "cake", "feature": "cucumber", "features": "cucumber", "nim": "nim", "nimble": "nim", "apib": "apiblueprint", "apiblueprint": "apiblueprint", "riot": "riot", "tag": "riot", "vfl": "vfl", "kl": "kl", "pcss": "postcss", "sss": "postcss", "todo": "todo", "cfml": "coldfusion", "cfc": "coldfusion", "lucee": "coldfusion", "cfm": "coldfusion", "cabal": "cabal", "nix": "nix", "slim": "slim", "http": "http", "rest": "http", "rql": "restql", "restql": "restql", "kv": "kivy", "graphcool": "graphcool", "sbt": "sbt", "apk": "android", "smali": "android", "dex": "android", "env": "tune", "gitlab-ci.yml": "gitlab", "jenkinsfile": "jenkins", "jenkins": "jenkins", "fig": "figma", "huff": "huff", "cr": "crystal", "ecr": "crystal", "drone.yml": "drone", "cu": "cuda", "cuh": "cuda", "log": "log", "def": "dotjs", "dot": "dotjs", "jst": "dotjs", "ejs": "ejs", ".wakatime-project": "wakatime", "pde": "processing", "stories.js": "storybook", "stories.jsx": "storybook", "stories.mdx": "storybook", "story.js": "storybook", "story.jsx": "storybook", "stories.ts": "storybook", "stories.tsx": "storybook", "story.ts": "storybook", "story.tsx": "storybook", "stories.svelte": "storybook", "story.mdx": "storybook", "wpy": "wepy", "hcl": "hcl", "san": "san", "quokka.js": "quokka", "quokka.ts": "quokka", "quokka.jsx": "quokka", "quokka.tsx": "quokka", "djt": "django", "red": "red", "mk": "makefile", "fxp": "foxpro", "prg": "foxpro", "pot": "i18n", "po": "i18n", "mo": "i18n", "lang": "i18n", "wat": "webassembly", "wasm": "webassembly", "ipynb": "jup<PERSON><PERSON>", "d": "d", "mdx": "mdx", "svx": "mdsvex", "bal": "ballerina", "balx": "ballerina", "rkt": "racket", "bzl": "bazel", "bazel": "bazel", "mint": "mint", "vm": "velocity", "fhtml": "velocity", "vtl": "velocity", "gd": "godot", "godot": "godot-assets", "tres": "godot-assets", "tscn": "godot-assets", "gdns": "godot-assets", "gdnlib": "godot-assets", "gdshader": "godot-assets", "gdshaderinc": "godot-assets", "gdextension": "godot-assets", "azure-pipelines.yml": "azure-pipelines", "azure-pipelines.yaml": "azure-pipelines", "azure-pipelines-main.yml": "azure-pipelines", "azure-pipelines-main.yaml": "azure-pipelines", "azcli": "azure", "vagrantfile": "vagrant", "prisma": "prisma", "cshtml": "razor", "vbhtml": "razor", "abc": "abc", "ad": "asciidoc", "adoc": "asciidoc", "asciidoc": "asciidoc", "edge": "edge", "ss": "scheme", "scm": "scheme", "lisp": "lisp", "lsp": "lisp", "cl": "lisp", "fast": "lisp", "stl": "3d", "stp": "3d", "obj": "3d", "o": "3d", "ac": "3d", "blend": "3d", "dxf": "3d", "fbx": "3d", "mesh": "3d", "mqo": "3d", "pmd": "3d", "pmx": "3d", "skp": "3d", "vac": "3d", "vdp": "3d", "vox": "3d", "svg": "svg", "svelte": "svelte", "vimrc": "vim", "gvimrc": "vim", "exrc": "vim", "vim": "vim", "viminfo": "vim", "moon": "moonscript", "prw": "advpl_prw", "prx": "advpl_prw", "ptm": "advpl_ptm", "tlpp": "advpl_tlpp", "ch": "advpl_include", "iso": "disc", "vmdk": "disc", "hdd": "disc", "qcow": "disc", "qcow2": "disc", "qed": "disc", "dmg": "disc", "f": "fortran", "f77": "fortran", "f90": "fortran", "f95": "fortran", "f03": "fortran", "f08": "fortran", "tcl": "tcl", "liquid": "liquid", "p": "prolog", "pro": "prolog", "pl": "prolog", "coco": "coconut", "sketch": "sketch", "pwn": "pawn", "amx": "pawn", "4th": "forth", "fth": "forth", "frt": "forth", "iuml": "uml", "pu": "uml", "puml": "uml", "plantuml": "uml", "wsd": "uml", "wrap": "meson", "dhall": "dhall", "dhallb": "dhall", "sml": "sml", "mlton": "sml", "mlb": "sml", "sig": "sml", "fun": "sml", "cm": "sml", "lex": "sml", "use": "sml", "grm": "sml", "opam": "opam", "imba": "imba", "drawio": "drawio", "dio": "drawio", "pas": "pascal", "unity": "shaderlab", "sas": "sas", "sas7bdat": "sas", "sashdat": "sas", "astore": "sas", "ast": "sas", "sast": "sas", "nupkg": "nuget", "command": "command", "dsc": "denizenscript", "code-search": "search", "nginx": "nginx", "nginxconf": "nginx", "nginxconfig": "nginx", "mcfunction": "minecraft", "mcmeta": "minecraft", "mcr": "minecraft", "mca": "minecraft", "mcgame": "minecraft", "mclevel": "minecraft", "mcworld": "minecraft", "mine": "minecraft", "mus": "minecraft", "mcstructure": "minecraft", "mcpack": "minecraft", "mcaddon": "minecraft", "mctemplate": "minecraft", "mcproject": "minecraft", "res": "rescript", "resi": "rescript-interface", "b": "brainfuck", "bf": "brainfuck", "bicep": "bicep", "cob": "cobol", "cbl": "cobol", "gr": "grain", "lol": "lolcode", "idr": "idris", "ibc": "idris", "pipeline": "pipeline", "rego": "opa", "windi": "windicss", "scala": "scala", "sc": "scala", "ly": "lilypond", "v": "vlang", "pgn": "chess", "fen": "chess", "gmi": "gemini", "gemini": "gemini", "tsconfig.json": "tsconfig", "tauri": "tauri", "jsconfig.json": "jsconfig", "ada": "ada", "adb": "ada", "ads": "ada", "ali": "ada", "horusec-config.json": "<PERSON><PERSON><PERSON>", "pdm.lock": "pdm", "pdm.toml": "pdm", "coarc": "coala", "coafile": "coala", "bubble": "dinophp", "html.bubble": "dinophp", "php.bubble": "dinophp", "tl": "teal", "template": "template", "glsl": "shader", "vert": "shader", "tesc": "shader", "tese": "shader", "geom": "shader", "frag": "shader", "comp": "shader", "vert.glsl": "shader", "tesc.glsl": "shader", "tese.glsl": "shader", "geom.glsl": "shader", "frag.glsl": "shader", "comp.glsl": "shader", "vertex.glsl": "shader", "geometry.glsl": "shader", "fragment.glsl": "shader", "compute.glsl": "shader", "ts.glsl": "shader", "gs.glsl": "shader", "vs.glsl": "shader", "fs.glsl": "shader", "shader": "shader", "vertexshader": "shader", "fragmentshader": "shader", "geometryshader": "shader", "computeshader": "shader", "hlsl": "shader", "pixel.hlsl": "shader", "geometry.hlsl": "shader", "compute.hlsl": "shader", "tessellation.hlsl": "shader", "px.hlsl": "shader", "geom.hlsl": "shader", "comp.hlsl": "shader", "tess.hlsl": "shader", "wgsl": "shader", "sy": "siyuan", "ndst.yml": "ndst", "ndst.yaml": "ndst", "ndst.json": "ndst", "tobi": "tobi", "gleam": "gleam", "steadybit.yml": "steadybit", "steadybit.yaml": "steadybit", "capnp": "capnp", "tree": "tree", "cdc": "cadence", "openapi.json": "openapi", "openapi.yml": "openapi", "openapi.yaml": "openapi", "swagger.json": "swagger", "swagger.yml": "swagger", "swagger.yaml": "swagger", "g4": "antlr", "st.css": "stylable", "pine": "<PERSON><PERSON><PERSON>", "taskfile.yml": "taskfile", "taskfile.yaml": "taskfile", "gml": "gamemaker", "yy": "gamemaker", "yyp": "gamemaker", "yyz": "gamemaker", "tldr": "tldraw", "typ": "typst", "mmd": "mermaid", "mermaid": "mermaid", "mojo": "mojo", "🔥": "mojo", "rbxl": "roblo<PERSON>", "rbxlx": "roblo<PERSON>", "rbxm": "roblo<PERSON>", "rbxmx": "roblo<PERSON>", "spwn": "spwn", "templ": "templ", "crx": "chrome", "stan": "stan", "abap": "abap", "acds": "abap", "asddls": "abap", "lottie": "lottie", "gs": "apps-script", "pkl": "pkl"}, "fileNames": {".pug-lintrc": "pug", ".pug-lintrc.js": "pug", ".pug-lintrc.json": "pug", ".jscsrc": "json", ".jshintrc": "json", "composer.lock": "json", ".jsbeautifyrc": "json", ".esformatter": "json", "cdp.pid": "json", ".lintstagedrc": "json", ".whitesource": "json", "playwright.config.js": "playwright", "playwright.config.mjs": "playwright", "playwright.config.ts": "playwright", "playwright.config.base.js": "playwright", "playwright.config.base.mjs": "playwright", "playwright.config.base.ts": "playwright", "playwright-ct.config.js": "playwright", "playwright-ct.config.mjs": "playwright", "playwright-ct.config.ts": "playwright", ".htaccess": "xml", "router.js": "routing", "router.jsx": "routing", "router.ts": "routing", "router.tsx": "routing", "routes.js": "routing", "routes.jsx": "routing", "routes.ts": "routing", "routes.tsx": "routing", ".jshintignore": "settings", ".buildignore": "settings", ".mrconfig": "settings", ".yardopts": "settings", "manifest.mf": "settings", ".clang-format": "settings", ".clang-tidy": "settings", "astro.config.js": "astro", "astro.config.mjs": "astro", "astro.config.cjs": "astro", "astro.config.ts": "astro", "astro.config.cts": "astro", "astro.config.mts": "astro", "go.mod": "go-mod", "go.sum": "go-mod", "go.work": "go-mod", "go.work.sum": "go-mod", "requirements.txt": "python-misc", "pipfile": "python-misc", ".python-version": "python-misc", "manifest.in": "python-misc", "pylintrc": "python-misc", ".pylintrc": "python-misc", "pyproject.toml": "python-misc", "commit-msg": "console", "pre-commit": "console", "pre-push": "console", "post-merge": "console", "gradle.properties": "gradle", "gradlew": "gradle", "gradle-wrapper.properties": "gradle", "copying": "certificate", "copying.md": "certificate", "copying.rst": "certificate", "copying.txt": "certificate", "copyright": "certificate", "copyright.md": "certificate", "copyright.rst": "certificate", "copyright.txt": "certificate", "license": "certificate", "license-agpl": "certificate", "license-apache": "certificate", "license-bsd": "certificate", "license-mit": "certificate", "license-gpl": "certificate", "license-lgpl": "certificate", "license.md": "certificate", "license.rst": "certificate", "license.txt": "certificate", "licence": "certificate", "licence-agpl": "certificate", "licence-apache": "certificate", "licence-bsd": "certificate", "licence-mit": "certificate", "licence-gpl": "certificate", "licence-lgpl": "certificate", "licence.md": "certificate", "licence.rst": "certificate", "licence.txt": "certificate", ".htpasswd": "key", ".ruby-version": "ruby", "gemfile": "gemfile", ".rubocop.yml": "rubocop", ".rubocop-todo.yml": "rubocop", ".rubocop_todo.yml": "rubocop", ".rspec": "rspec", "dockerfile": "docker", "dockerfile.prod": "docker", "dockerfile.production": "docker", "dockerfile.alpha": "docker", "dockerfile.beta": "docker", "dockerfile.stage": "docker", "dockerfile.staging": "docker", "dockerfile.dev": "docker", "dockerfile.development": "docker", "dockerfile.local": "docker", "dockerfile.test": "docker", "dockerfile.testing": "docker", "dockerfile.ci": "docker", "dockerfile.web": "docker", "dockerfile.worker": "docker", "docker-compose.yml": "docker", "docker-compose.override.yml": "docker", "docker-compose.prod.yml": "docker", "docker-compose.production.yml": "docker", "docker-compose.alpha.yml": "docker", "docker-compose.beta.yml": "docker", "docker-compose.stage.yml": "docker", "docker-compose.staging.yml": "docker", "docker-compose.dev.yml": "docker", "docker-compose.development.yml": "docker", "docker-compose.local.yml": "docker", "docker-compose.test.yml": "docker", "docker-compose.testing.yml": "docker", "docker-compose.ci.yml": "docker", "docker-compose.web.yml": "docker", "docker-compose.worker.yml": "docker", "docker-compose.yaml": "docker", "docker-compose.override.yaml": "docker", "docker-compose.prod.yaml": "docker", "docker-compose.production.yaml": "docker", "docker-compose.alpha.yaml": "docker", "docker-compose.beta.yaml": "docker", "docker-compose.stage.yaml": "docker", "docker-compose.staging.yaml": "docker", "docker-compose.dev.yaml": "docker", "docker-compose.development.yaml": "docker", "docker-compose.local.yaml": "docker", "docker-compose.test.yaml": "docker", "docker-compose.testing.yaml": "docker", "docker-compose.ci.yaml": "docker", "docker-compose.web.yaml": "docker", "docker-compose.worker.yaml": "docker", "containerfile": "docker", "containerfile.prod": "docker", "containerfile.production": "docker", "containerfile.alpha": "docker", "containerfile.beta": "docker", "containerfile.stage": "docker", "containerfile.staging": "docker", "containerfile.dev": "docker", "containerfile.development": "docker", "containerfile.local": "docker", "containerfile.test": "docker", "containerfile.testing": "docker", "containerfile.ci": "docker", "containerfile.web": "docker", "containerfile.worker": "docker", "compose.yaml": "docker", "compose.override.yaml": "docker", "compose.prod.yaml": "docker", "compose.production.yaml": "docker", "compose.alpha.yaml": "docker", "compose.beta.yaml": "docker", "compose.stage.yaml": "docker", "compose.staging.yaml": "docker", "compose.dev.yaml": "docker", "compose.development.yaml": "docker", "compose.local.yaml": "docker", "compose.test.yaml": "docker", "compose.testing.yaml": "docker", "compose.ci.yaml": "docker", "compose.web.yaml": "docker", "compose.worker.yaml": "docker", "compose.yml": "docker", "compose.override.yml": "docker", "compose.prod.yml": "docker", "compose.production.yml": "docker", "compose.alpha.yml": "docker", "compose.beta.yml": "docker", "compose.stage.yml": "docker", "compose.staging.yml": "docker", "compose.dev.yml": "docker", "compose.development.yml": "docker", "compose.local.yml": "docker", "compose.test.yml": "docker", "compose.testing.yml": "docker", "compose.ci.yml": "docker", "compose.web.yml": "docker", "compose.worker.yml": "docker", ".mailmap": "email", ".graphqlrc": "graphql", ".graphqlrc.json": "graphql", ".graphqlrc.jsonc": "graphql", ".graphqlrc.json5": "graphql", ".graphqlrc.yaml": "graphql", ".graphqlrc.yml": "graphql", ".graphqlrc.toml": "graphql", ".graphqlrc.js": "graphql", ".graphqlrc.mjs": "graphql", ".graphqlrc.cjs": "graphql", ".graphqlrc.ts": "graphql", ".graphqlrc.mts": "graphql", ".graphqlrc.cts": "graphql", ".config/graphqlrc": "graphql", ".config/graphqlrc.json": "graphql", ".config/graphqlrc.jsonc": "graphql", ".config/graphqlrc.json5": "graphql", ".config/graphqlrc.yaml": "graphql", ".config/graphqlrc.yml": "graphql", ".config/graphqlrc.toml": "graphql", ".config/graphqlrc.js": "graphql", ".config/graphqlrc.mjs": "graphql", ".config/graphqlrc.cjs": "graphql", ".config/graphqlrc.ts": "graphql", ".config/graphqlrc.mts": "graphql", ".config/graphqlrc.cts": "graphql", "graphql.config.json": "graphql", "graphql.config.jsonc": "graphql", "graphql.config.json5": "graphql", "graphql.config.yaml": "graphql", "graphql.config.yml": "graphql", "graphql.config.toml": "graphql", "graphql.config.js": "graphql", "graphql.config.mjs": "graphql", "graphql.config.cjs": "graphql", "graphql.config.ts": "graphql", "graphql.config.mts": "graphql", "graphql.config.cts": "graphql", ".graphqlconfig": "graphql", ".git": "git", ".gitignore": "git", ".gitmessage": "git", ".gitignore-global": "git", ".gitignore_global": "git", ".gitattributes": "git", ".gitattributes-global": "git", ".gitattributes_global": "git", ".gitconfig": "git", ".gitmodules": "git", ".gitkeep": "git", ".keep": "git", ".gitpreserve": "git", ".gitinclude": "git", ".git-blame-ignore": "git", ".git-blame-ignore-revs": "git", ".git-for-windows-updater": "git", "git-history": "git", ".luacheckrc": "lua", ".Rhistory": "r", ".pubignore": "dart", "cmakelists.txt": "cmake", "cmakecache.txt": "cmake", "semgrep.yml": "semgrep", ".semgrepignore": "semgrep", "vue.config.js": "vue-config", "vue.config.ts": "vue-config", "vetur.config.js": "vue-config", "vetur.config.ts": "vue-config", "volar.config.js": "vue-config", "nuxt.config.js": "nuxt", "nuxt.config.ts": "nuxt", ".nuxtignore": "nuxt", "security.md": "lock", "security.txt": "lock", "security": "lock", "angular-cli.json": "angular", ".angular-cli.json": "angular", "angular.json": "angular", "ng-package.json": "angular", ".mjmlconfig": "mjml", "vercel.json": "vercel", ".vercelignore": "vercel", "now.json": "vercel", ".nowignore": "vercel", "verdaccio.yml": "verda<PERSON><PERSON>", "payload.config.js": "payload", "payload.config.mjs": "payload", "payload.config.ts": "payload", "payload.config.mts": "payload", "next.config.js": "next", "next.config.mjs": "next", "next.config.ts": "next", "next.config.mts": "next", "remix.config.js": "remix", "remix.config.ts": "remix", "artisan": "laravel", ".vfl": "vfl", ".kl": "kl", ".postcssrc": "postcss", ".postcssrc.json": "postcss", ".postcssrc.jsonc": "postcss", ".postcssrc.json5": "postcss", ".postcssrc.yaml": "postcss", ".postcssrc.yml": "postcss", ".postcssrc.toml": "postcss", ".postcssrc.js": "postcss", ".postcssrc.mjs": "postcss", ".postcssrc.cjs": "postcss", ".postcssrc.ts": "postcss", ".postcssrc.mts": "postcss", ".postcssrc.cts": "postcss", ".config/postcssrc": "postcss", ".config/postcssrc.json": "postcss", ".config/postcssrc.jsonc": "postcss", ".config/postcssrc.json5": "postcss", ".config/postcssrc.yaml": "postcss", ".config/postcssrc.yml": "postcss", ".config/postcssrc.toml": "postcss", ".config/postcssrc.js": "postcss", ".config/postcssrc.mjs": "postcss", ".config/postcssrc.cjs": "postcss", ".config/postcssrc.ts": "postcss", ".config/postcssrc.mts": "postcss", ".config/postcssrc.cts": "postcss", "postcss.config.json": "postcss", "postcss.config.jsonc": "postcss", "postcss.config.json5": "postcss", "postcss.config.yaml": "postcss", "postcss.config.yml": "postcss", "postcss.config.toml": "postcss", "postcss.config.js": "postcss", "postcss.config.mjs": "postcss", "postcss.config.cjs": "postcss", "postcss.config.ts": "postcss", "postcss.config.mts": "postcss", "postcss.config.cts": "postcss", ".posthtmlrc": "posthtml", ".posthtmlrc.json": "posthtml", ".posthtmlrc.jsonc": "posthtml", ".posthtmlrc.json5": "posthtml", ".posthtmlrc.yaml": "posthtml", ".posthtmlrc.yml": "posthtml", ".posthtmlrc.toml": "posthtml", ".posthtmlrc.js": "posthtml", ".posthtmlrc.mjs": "posthtml", ".posthtmlrc.cjs": "posthtml", ".posthtmlrc.ts": "posthtml", ".posthtmlrc.mts": "posthtml", ".posthtmlrc.cts": "posthtml", ".config/posthtmlrc": "posthtml", ".config/posthtmlrc.json": "posthtml", ".config/posthtmlrc.jsonc": "posthtml", ".config/posthtmlrc.json5": "posthtml", ".config/posthtmlrc.yaml": "posthtml", ".config/posthtmlrc.yml": "posthtml", ".config/posthtmlrc.toml": "posthtml", ".config/posthtmlrc.js": "posthtml", ".config/posthtmlrc.mjs": "posthtml", ".config/posthtmlrc.cjs": "posthtml", ".config/posthtmlrc.ts": "posthtml", ".config/posthtmlrc.mts": "posthtml", ".config/posthtmlrc.cts": "posthtml", "posthtml.config.json": "posthtml", "posthtml.config.jsonc": "posthtml", "posthtml.config.json5": "posthtml", "posthtml.config.yaml": "posthtml", "posthtml.config.yml": "posthtml", "posthtml.config.toml": "posthtml", "posthtml.config.js": "posthtml", "posthtml.config.mjs": "posthtml", "posthtml.config.cjs": "posthtml", "posthtml.config.ts": "posthtml", "posthtml.config.mts": "posthtml", "posthtml.config.cts": "posthtml", "todo.md": "todo", "todos.md": "todo", "cabal.project": "cabal", "cabal.project.freeze": "cabal", "cabal.project.local": "cabal", "CNAME": "http", "project.graphcool": "graphcool", "webpack.base.js": "webpack", "webpack.base.mjs": "webpack", "webpack.base.cjs": "webpack", "webpack.base.ts": "webpack", "webpack.base.mts": "webpack", "webpack.base.cts": "webpack", "webpack.client.js": "webpack", "webpack.client.mjs": "webpack", "webpack.client.cjs": "webpack", "webpack.client.ts": "webpack", "webpack.client.mts": "webpack", "webpack.client.cts": "webpack", "webpack.common.js": "webpack", "webpack.common.mjs": "webpack", "webpack.common.cjs": "webpack", "webpack.common.ts": "webpack", "webpack.common.mts": "webpack", "webpack.common.cts": "webpack", "webpack.config.babel.js": "webpack", "webpack.config.babel.mjs": "webpack", "webpack.config.babel.cjs": "webpack", "webpack.config.babel.ts": "webpack", "webpack.config.babel.mts": "webpack", "webpack.config.babel.cts": "webpack", "webpack.config.base.babel.js": "webpack", "webpack.config.base.babel.mjs": "webpack", "webpack.config.base.babel.cjs": "webpack", "webpack.config.base.babel.ts": "webpack", "webpack.config.base.babel.mts": "webpack", "webpack.config.base.babel.cts": "webpack", "webpack.config.base.js": "webpack", "webpack.config.base.mjs": "webpack", "webpack.config.base.cjs": "webpack", "webpack.config.base.ts": "webpack", "webpack.config.base.mts": "webpack", "webpack.config.base.cts": "webpack", "webpack.config.client.js": "webpack", "webpack.config.client.mjs": "webpack", "webpack.config.client.cjs": "webpack", "webpack.config.client.ts": "webpack", "webpack.config.client.mts": "webpack", "webpack.config.client.cts": "webpack", "webpack.config.common.babel.js": "webpack", "webpack.config.common.babel.mjs": "webpack", "webpack.config.common.babel.cjs": "webpack", "webpack.config.common.babel.ts": "webpack", "webpack.config.common.babel.mts": "webpack", "webpack.config.common.babel.cts": "webpack", "webpack.config.common.js": "webpack", "webpack.config.common.mjs": "webpack", "webpack.config.common.cjs": "webpack", "webpack.config.common.ts": "webpack", "webpack.config.common.mts": "webpack", "webpack.config.common.cts": "webpack", "webpack.config.dev.babel.js": "webpack", "webpack.config.dev.babel.mjs": "webpack", "webpack.config.dev.babel.cjs": "webpack", "webpack.config.dev.babel.ts": "webpack", "webpack.config.dev.babel.mts": "webpack", "webpack.config.dev.babel.cts": "webpack", "webpack.config.dev.js": "webpack", "webpack.config.dev.mjs": "webpack", "webpack.config.dev.cjs": "webpack", "webpack.config.dev.ts": "webpack", "webpack.config.dev.mts": "webpack", "webpack.config.dev.cts": "webpack", "webpack.config.main.js": "webpack", "webpack.config.main.mjs": "webpack", "webpack.config.main.cjs": "webpack", "webpack.config.main.ts": "webpack", "webpack.config.main.mts": "webpack", "webpack.config.main.cts": "webpack", "webpack.config.prod.babel.js": "webpack", "webpack.config.prod.babel.mjs": "webpack", "webpack.config.prod.babel.cjs": "webpack", "webpack.config.prod.babel.ts": "webpack", "webpack.config.prod.babel.mts": "webpack", "webpack.config.prod.babel.cts": "webpack", "webpack.config.prod.js": "webpack", "webpack.config.prod.mjs": "webpack", "webpack.config.prod.cjs": "webpack", "webpack.config.prod.ts": "webpack", "webpack.config.prod.mts": "webpack", "webpack.config.prod.cts": "webpack", "webpack.config.production.babel.js": "webpack", "webpack.config.production.babel.mjs": "webpack", "webpack.config.production.babel.cjs": "webpack", "webpack.config.production.babel.ts": "webpack", "webpack.config.production.babel.mts": "webpack", "webpack.config.production.babel.cts": "webpack", "webpack.config.production.js": "webpack", "webpack.config.production.mjs": "webpack", "webpack.config.production.cjs": "webpack", "webpack.config.production.ts": "webpack", "webpack.config.production.mts": "webpack", "webpack.config.production.cts": "webpack", "webpack.config.renderer.js": "webpack", "webpack.config.renderer.mjs": "webpack", "webpack.config.renderer.cjs": "webpack", "webpack.config.renderer.ts": "webpack", "webpack.config.renderer.mts": "webpack", "webpack.config.renderer.cts": "webpack", "webpack.config.server.js": "webpack", "webpack.config.server.mjs": "webpack", "webpack.config.server.cjs": "webpack", "webpack.config.server.ts": "webpack", "webpack.config.server.mts": "webpack", "webpack.config.server.cts": "webpack", "webpack.config.staging.babel.js": "webpack", "webpack.config.staging.babel.mjs": "webpack", "webpack.config.staging.babel.cjs": "webpack", "webpack.config.staging.babel.ts": "webpack", "webpack.config.staging.babel.mts": "webpack", "webpack.config.staging.babel.cts": "webpack", "webpack.config.staging.js": "webpack", "webpack.config.staging.mjs": "webpack", "webpack.config.staging.cjs": "webpack", "webpack.config.staging.ts": "webpack", "webpack.config.staging.mts": "webpack", "webpack.config.staging.cts": "webpack", "webpack.config.test.js": "webpack", "webpack.config.test.mjs": "webpack", "webpack.config.test.cjs": "webpack", "webpack.config.test.ts": "webpack", "webpack.config.test.mts": "webpack", "webpack.config.test.cts": "webpack", "webpack.config.vendor.production.js": "webpack", "webpack.config.vendor.production.mjs": "webpack", "webpack.config.vendor.production.cjs": "webpack", "webpack.config.vendor.production.ts": "webpack", "webpack.config.vendor.production.mts": "webpack", "webpack.config.vendor.production.cts": "webpack", "webpack.config.vendor.js": "webpack", "webpack.config.vendor.mjs": "webpack", "webpack.config.vendor.cjs": "webpack", "webpack.config.vendor.ts": "webpack", "webpack.config.vendor.mts": "webpack", "webpack.config.vendor.cts": "webpack", "webpack.config.js": "webpack", "webpack.config.mjs": "webpack", "webpack.config.cjs": "webpack", "webpack.config.ts": "webpack", "webpack.config.mts": "webpack", "webpack.config.cts": "webpack", "webpack.dev.js": "webpack", "webpack.dev.mjs": "webpack", "webpack.dev.cjs": "webpack", "webpack.dev.ts": "webpack", "webpack.dev.mts": "webpack", "webpack.dev.cts": "webpack", "webpack.development.js": "webpack", "webpack.development.mjs": "webpack", "webpack.development.cjs": "webpack", "webpack.development.ts": "webpack", "webpack.development.mts": "webpack", "webpack.development.cts": "webpack", "webpack.dist.js": "webpack", "webpack.dist.mjs": "webpack", "webpack.dist.cjs": "webpack", "webpack.dist.ts": "webpack", "webpack.dist.mts": "webpack", "webpack.dist.cts": "webpack", "webpack.mix.js": "webpack", "webpack.mix.mjs": "webpack", "webpack.mix.cjs": "webpack", "webpack.mix.ts": "webpack", "webpack.mix.mts": "webpack", "webpack.mix.cts": "webpack", "webpack.prod.config.js": "webpack", "webpack.prod.config.mjs": "webpack", "webpack.prod.config.cjs": "webpack", "webpack.prod.config.ts": "webpack", "webpack.prod.config.mts": "webpack", "webpack.prod.config.cts": "webpack", "webpack.prod.js": "webpack", "webpack.prod.mjs": "webpack", "webpack.prod.cjs": "webpack", "webpack.prod.ts": "webpack", "webpack.prod.mts": "webpack", "webpack.prod.cts": "webpack", "webpack.production.js": "webpack", "webpack.production.mjs": "webpack", "webpack.production.cjs": "webpack", "webpack.production.ts": "webpack", "webpack.production.mts": "webpack", "webpack.production.cts": "webpack", "webpack.server.js": "webpack", "webpack.server.mjs": "webpack", "webpack.server.cjs": "webpack", "webpack.server.ts": "webpack", "webpack.server.mts": "webpack", "webpack.server.cts": "webpack", "webpack.test.js": "webpack", "webpack.test.mjs": "webpack", "webpack.test.cjs": "webpack", "webpack.test.ts": "webpack", "webpack.test.mts": "webpack", "webpack.test.cts": "webpack", "webpack.js": "webpack", "webpack.mjs": "webpack", "webpack.cjs": "webpack", "webpack.ts": "webpack", "webpack.mts": "webpack", "webpack.cts": "webpack", "webpackfile.js": "webpack", "webpackfile.mjs": "webpack", "webpackfile.cjs": "webpack", "webpackfile.ts": "webpack", "webpackfile.mts": "webpack", "webpackfile.cts": "webpack", "webpack.config.coffee": "webpack", "ionic.config.json": "ionic", ".io-config.json": "ionic", "gulpfile.js": "gulp", "gulpfile.mjs": "gulp", "gulpfile.ts": "gulp", "gulpfile.cts": "gulp", "gulpfile.mts": "gulp", "gulpfile.babel.js": "gulp", "package.json": "nodejs", "package-lock.json": "nodejs", ".nvmrc": "nodejs", ".esmrc": "nodejs", ".node-version": "nodejs", ".npmignore": "npm", ".npmrc": "npm", ".yarnrc": "yarn", "yarn.lock": "yarn", ".yarnclean": "yarn", ".yarn-integrity": "yarn", "yarn-error.log": "yarn", ".yarnrc.yml": "yarn", ".yarnrc.yaml": "yarn", "androidmanifest.xml": "android", ".env.defaults": "tune", ".env.example": "tune", ".env.sample": "tune", ".env.template": "tune", ".env.schema": "tune", ".env.local": "tune", ".env.dev": "tune", ".env.development": "tune", ".env.alpha": "tune", ".env.e2e": "tune", ".env.qa": "tune", ".env.dist": "tune", ".env.prod": "tune", ".env.production": "tune", ".env.stage": "tune", ".env.staging": "tune", ".env.preview": "tune", ".env.test": "tune", ".env.testing": "tune", ".env.development.local": "tune", ".env.qa.local": "tune", ".env.production.local": "tune", ".env.staging.local": "tune", ".env.test.local": "tune", ".env.uat": "tune", "turbo.json": "turborepo", ".babelrc": "babel", ".babelrc.json": "babel", ".babelrc.jsonc": "babel", ".babelrc.json5": "babel", ".babelrc.yaml": "babel", ".babelrc.yml": "babel", ".babelrc.toml": "babel", ".babelrc.js": "babel", ".babelrc.mjs": "babel", ".babelrc.cjs": "babel", ".babelrc.ts": "babel", ".babelrc.mts": "babel", ".babelrc.cts": "babel", ".config/babelrc": "babel", ".config/babelrc.json": "babel", ".config/babelrc.jsonc": "babel", ".config/babelrc.json5": "babel", ".config/babelrc.yaml": "babel", ".config/babelrc.yml": "babel", ".config/babelrc.toml": "babel", ".config/babelrc.js": "babel", ".config/babelrc.mjs": "babel", ".config/babelrc.cjs": "babel", ".config/babelrc.ts": "babel", ".config/babelrc.mts": "babel", ".config/babelrc.cts": "babel", "babel.config.json": "babel", "babel.config.jsonc": "babel", "babel.config.json5": "babel", "babel.config.yaml": "babel", "babel.config.yml": "babel", "babel.config.toml": "babel", "babel.config.js": "babel", "babel.config.mjs": "babel", "babel.config.cjs": "babel", "babel.config.ts": "babel", "babel.config.mts": "babel", "babel.config.cts": "babel", ".babel-plugin-macrosrc": "babel", ".babel-plugin-macrosrc.json": "babel", ".babel-plugin-macrosrc.jsonc": "babel", ".babel-plugin-macrosrc.json5": "babel", ".babel-plugin-macrosrc.yaml": "babel", ".babel-plugin-macrosrc.yml": "babel", ".babel-plugin-macrosrc.toml": "babel", ".babel-plugin-macrosrc.js": "babel", ".babel-plugin-macrosrc.mjs": "babel", ".babel-plugin-macrosrc.cjs": "babel", ".babel-plugin-macrosrc.ts": "babel", ".babel-plugin-macrosrc.mts": "babel", ".babel-plugin-macrosrc.cts": "babel", ".config/babel-plugin-macrosrc": "babel", ".config/babel-plugin-macrosrc.json": "babel", ".config/babel-plugin-macrosrc.jsonc": "babel", ".config/babel-plugin-macrosrc.json5": "babel", ".config/babel-plugin-macrosrc.yaml": "babel", ".config/babel-plugin-macrosrc.yml": "babel", ".config/babel-plugin-macrosrc.toml": "babel", ".config/babel-plugin-macrosrc.js": "babel", ".config/babel-plugin-macrosrc.mjs": "babel", ".config/babel-plugin-macrosrc.cjs": "babel", ".config/babel-plugin-macrosrc.ts": "babel", ".config/babel-plugin-macrosrc.mts": "babel", ".config/babel-plugin-macrosrc.cts": "babel", "babel-plugin-macros.config.json": "babel", "babel-plugin-macros.config.jsonc": "babel", "babel-plugin-macros.config.json5": "babel", "babel-plugin-macros.config.yaml": "babel", "babel-plugin-macros.config.yml": "babel", "babel-plugin-macros.config.toml": "babel", "babel-plugin-macros.config.js": "babel", "babel-plugin-macros.config.mjs": "babel", "babel-plugin-macros.config.cjs": "babel", "babel-plugin-macros.config.ts": "babel", "babel-plugin-macros.config.mts": "babel", "babel-plugin-macros.config.cts": "babel", "babel-transform.js": "babel", "blitz.config.js": "blitz", "blitz.config.ts": "blitz", ".blitz.config.compiled.js": "blitz", "contributing.md": "contributing", "contributing.rst": "contributing", "contributing.txt": "contributing", "contributing": "contributing", "readme.md": "readme", "readme.rst": "readme", "readme.txt": "readme", "readme": "readme", "changelog": "changelog", "changelog.md": "changelog", "changelog.rst": "changelog", "changelog.txt": "changelog", "changes": "changelog", "changes.md": "changelog", "changes.rst": "changelog", "changes.txt": "changelog", "architecture.md": "architecture", "architecture.rst": "architecture", "architecture.txt": "architecture", "architecture": "architecture", "credits.md": "credits", "credits.rst": "credits", "credits.txt": "credits", "credits": "credits", "authors.md": "authors", "authors.rst": "authors", "authors.txt": "authors", "authors": "authors", "contributors.md": "authors", "contributors.rst": "authors", "contributors.txt": "authors", "contributors": "authors", ".flowconfig": "flow", "favicon.ico": "favicon", "karma.conf.js": "karma", "karma.conf.ts": "karma", "karma.conf.coffee": "karma", "karma.config.js": "karma", "karma.config.ts": "karma", "karma-main.js": "karma", "karma-main.ts": "karma", ".bithoundrc": "bithound", "svgo.config.js": "svgo", "svgo.config.cjs": "svgo", "svgo.config.mjs": "svgo", ".appveyor.yml": "appveyor", "appveyor.yml": "appveyor", ".travis.yml": "travis", ".codecov.yml": "codecov", "codecov.yml": "codecov", "sonar-project.properties": "sonarcloud", ".sonarcloud.properties": "sonarcloud", "sonarcloud.yaml": "sonarcloud", "protractor.conf.js": "protractor", "protractor.conf.ts": "protractor", "protractor.conf.coffee": "protractor", "protractor.config.js": "protractor", "protractor.config.ts": "protractor", "fuse.js": "fusebox", "procfile": "<PERSON><PERSON>", "procfile.windows": "<PERSON><PERSON>", ".editorconfig": "editorconfig", ".bowerrc": "bower", "bower.json": "bower", ".eslintrc": "eslint", ".eslintrc.json": "eslint", ".eslintrc.jsonc": "eslint", ".eslintrc.json5": "eslint", ".eslintrc.yaml": "eslint", ".eslintrc.yml": "eslint", ".eslintrc.toml": "eslint", ".eslintrc.js": "eslint", ".eslintrc.mjs": "eslint", ".eslintrc.cjs": "eslint", ".eslintrc.ts": "eslint", ".eslintrc.mts": "eslint", ".eslintrc.cts": "eslint", ".config/eslintrc": "eslint", ".config/eslintrc.json": "eslint", ".config/eslintrc.jsonc": "eslint", ".config/eslintrc.json5": "eslint", ".config/eslintrc.yaml": "eslint", ".config/eslintrc.yml": "eslint", ".config/eslintrc.toml": "eslint", ".config/eslintrc.js": "eslint", ".config/eslintrc.mjs": "eslint", ".config/eslintrc.cjs": "eslint", ".config/eslintrc.ts": "eslint", ".config/eslintrc.mts": "eslint", ".config/eslintrc.cts": "eslint", "eslint.config.json": "eslint", "eslint.config.jsonc": "eslint", "eslint.config.json5": "eslint", "eslint.config.yaml": "eslint", "eslint.config.yml": "eslint", "eslint.config.toml": "eslint", "eslint.config.js": "eslint", "eslint.config.mjs": "eslint", "eslint.config.cjs": "eslint", "eslint.config.ts": "eslint", "eslint.config.mts": "eslint", "eslint.config.cts": "eslint", ".eslintrc-md.js": "eslint", ".eslintrc-jsdoc.js": "eslint", ".eslintignore": "eslint", ".eslintcache": "eslint", ".eslintrc.base.json": "eslint", "code_of_conduct.md": "conduct", "code_of_conduct.txt": "conduct", "code_of_conduct": "conduct", ".watchmanconfig": "watchman", "aurelia.json": "aurelia", ".autorc": "auto", "auto.config.js": "auto", "auto.config.ts": "auto", "auto-config.json": "auto", "auto-config.yaml": "auto", "auto-config.yml": "auto", "auto-config.ts": "auto", "auto-config.js": "auto", "mocha.opts": "mocha", ".mocharc.yml": "mocha", ".mocharc.yaml": "mocha", ".mocharc.js": "mocha", ".mocharc.json": "mocha", ".mocharc.jsonc": "mocha", "jenkinsfile": "jenkins", "firebase.json": "firebase", ".firebaserc": "firebase", "firestore.rules": "firebase", "firestore.indexes.json": "firebase", "rollup.config.js": "rollup", "rollup.config.mjs": "rollup", "rollup.config.ts": "rollup", "rollup-config.js": "rollup", "rollup-config.mjs": "rollup", "rollup-config.ts": "rollup", "rollup.config.common.js": "rollup", "rollup.config.common.mjs": "rollup", "rollup.config.common.ts": "rollup", "rollup.config.base.js": "rollup", "rollup.config.base.mjs": "rollup", "rollup.config.base.ts": "rollup", "rollup.config.prod.js": "rollup", "rollup.config.prod.mjs": "rollup", "rollup.config.prod.ts": "rollup", "rollup.config.dev.js": "rollup", "rollup.config.dev.mjs": "rollup", "rollup.config.dev.ts": "rollup", "rollup.config.prod.vendor.js": "rollup", "rollup.config.prod.vendor.mjs": "rollup", "rollup.config.prod.vendor.ts": "rollup", ".hhconfig": "hack", "hardhat.config.js": "hardhat", "hardhat.config.ts": "hardhat", ".stylelintrc": "stylelint", ".stylelintrc.json": "stylelint", ".stylelintrc.jsonc": "stylelint", ".stylelintrc.json5": "stylelint", ".stylelintrc.yaml": "stylelint", ".stylelintrc.yml": "stylelint", ".stylelintrc.toml": "stylelint", ".stylelintrc.js": "stylelint", ".stylelintrc.mjs": "stylelint", ".stylelintrc.cjs": "stylelint", ".stylelintrc.ts": "stylelint", ".stylelintrc.mts": "stylelint", ".stylelintrc.cts": "stylelint", ".config/stylelintrc": "stylelint", ".config/stylelintrc.json": "stylelint", ".config/stylelintrc.jsonc": "stylelint", ".config/stylelintrc.json5": "stylelint", ".config/stylelintrc.yaml": "stylelint", ".config/stylelintrc.yml": "stylelint", ".config/stylelintrc.toml": "stylelint", ".config/stylelintrc.js": "stylelint", ".config/stylelintrc.mjs": "stylelint", ".config/stylelintrc.cjs": "stylelint", ".config/stylelintrc.ts": "stylelint", ".config/stylelintrc.mts": "stylelint", ".config/stylelintrc.cts": "stylelint", "stylelint.config.json": "stylelint", "stylelint.config.jsonc": "stylelint", "stylelint.config.json5": "stylelint", "stylelint.config.yaml": "stylelint", "stylelint.config.yml": "stylelint", "stylelint.config.toml": "stylelint", "stylelint.config.js": "stylelint", "stylelint.config.mjs": "stylelint", "stylelint.config.cjs": "stylelint", "stylelint.config.ts": "stylelint", "stylelint.config.mts": "stylelint", "stylelint.config.cts": "stylelint", ".stylelintignore": "stylelint", ".stylelintcache": "stylelint", ".codeclimate.yml": "code-climate", ".prettierrc": "prettier", ".prettierrc.json": "prettier", ".prettierrc.jsonc": "prettier", ".prettierrc.json5": "prettier", ".prettierrc.yaml": "prettier", ".prettierrc.yml": "prettier", ".prettierrc.toml": "prettier", ".prettierrc.js": "prettier", ".prettierrc.mjs": "prettier", ".prettierrc.cjs": "prettier", ".prettierrc.ts": "prettier", ".prettierrc.mts": "prettier", ".prettierrc.cts": "prettier", ".config/prettierrc": "prettier", ".config/prettierrc.json": "prettier", ".config/prettierrc.jsonc": "prettier", ".config/prettierrc.json5": "prettier", ".config/prettierrc.yaml": "prettier", ".config/prettierrc.yml": "prettier", ".config/prettierrc.toml": "prettier", ".config/prettierrc.js": "prettier", ".config/prettierrc.mjs": "prettier", ".config/prettierrc.cjs": "prettier", ".config/prettierrc.ts": "prettier", ".config/prettierrc.mts": "prettier", ".config/prettierrc.cts": "prettier", "prettier.config.json": "prettier", "prettier.config.jsonc": "prettier", "prettier.config.json5": "prettier", "prettier.config.yaml": "prettier", "prettier.config.yml": "prettier", "prettier.config.toml": "prettier", "prettier.config.js": "prettier", "prettier.config.mjs": "prettier", "prettier.config.cjs": "prettier", "prettier.config.ts": "prettier", "prettier.config.mts": "prettier", "prettier.config.cts": "prettier", ".prettierignore": "prettier", ".renovaterc": "renovate", ".renovaterc.json": "renovate", "renovate-config.json": "renovate", "renovate.json": "renovate", "renovate.json5": "renovate", "apollo.config.js": "apollo", "nodemon.json": "nodemon", "nodemon-debug.json": "nodemon", ".hintrc": "web<PERSON>t", "browserslist": "browserlist", ".browserslistrc": "browserlist", ".snyk": "snyk", ".drone.yml": "drone", ".sequelizerc": "sequelize", "gatsby-config.js": "gatsby", "gatsby-config.mjs": "gatsby", "gatsby-config.ts": "gatsby", "gatsby-node.js": "gatsby", "gatsby-node.mjs": "gatsby", "gatsby-node.ts": "gatsby", "gatsby-browser.js": "gatsby", "gatsby-browser.tsx": "gatsby", "gatsby-ssr.js": "gatsby", "gatsby-ssr.tsx": "gatsby", ".wakatime-project": "wakatime", "circle.yml": "<PERSON><PERSON>", ".cfignore": "cloudfoundry", "gruntfile.js": "grunt", "gruntfile.ts": "grunt", "gruntfile.cjs": "grunt", "gruntfile.cts": "grunt", "gruntfile.coffee": "grunt", "gruntfile.babel.js": "grunt", "gruntfile.babel.ts": "grunt", "gruntfile.babel.coffee": "grunt", "jest.config.js": "jest", "jest.config.cjs": "jest", "jest.config.mjs": "jest", "jest.config.ts": "jest", "jest.config.cts": "jest", "jest.config.mts": "jest", "jest.config.json": "jest", "jest.e2e.config.js": "jest", "jest.e2e.config.cjs": "jest", "jest.e2e.config.mjs": "jest", "jest.e2e.config.ts": "jest", "jest.e2e.config.cts": "jest", "jest.e2e.config.mts": "jest", "jest.e2e.config.json": "jest", "jest.e2e.json": "jest", "jest-unit.config.js": "jest", "jest-e2e.config.js": "jest", "jest-e2e.config.cjs": "jest", "jest-e2e.config.mjs": "jest", "jest-e2e.config.ts": "jest", "jest-e2e.config.cts": "jest", "jest-e2e.config.mts": "jest", "jest-e2e.config.json": "jest", "jest-e2e.json": "jest", "jest-github-actions-reporter.js": "jest", "jest.setup.js": "jest", "jest.setup.ts": "jest", "jest.json": "jest", ".jestrc": "jest", ".jestrc.js": "jest", ".jestrc.json": "jest", "jest.teardown.js": "jest", "jest-preset.json": "jest", "jest-preset.js": "jest", "jest-preset.cjs": "jest", "jest-preset.mjs": "jest", "jest.preset.js": "jest", "jest.preset.mjs": "jest", "jest.preset.cjs": "jest", "jest.preset.json": "jest", "fastfile": "fastlane", "appfile": "fastlane", ".helmignore": "helm", "wallaby.js": "wallaby", "wallaby.conf.js": "wallaby", "stencil.config.js": "stencil", "stencil.config.ts": "stencil", "makefile": "makefile", "gnumakefile": "makefile", "kbuild": "makefile", ".releaserc": "semantic-release", ".releaserc.json": "semantic-release", ".releaserc.jsonc": "semantic-release", ".releaserc.json5": "semantic-release", ".releaserc.yaml": "semantic-release", ".releaserc.yml": "semantic-release", ".releaserc.toml": "semantic-release", ".releaserc.js": "semantic-release", ".releaserc.mjs": "semantic-release", ".releaserc.cjs": "semantic-release", ".releaserc.ts": "semantic-release", ".releaserc.mts": "semantic-release", ".releaserc.cts": "semantic-release", ".config/releaserc": "semantic-release", ".config/releaserc.json": "semantic-release", ".config/releaserc.jsonc": "semantic-release", ".config/releaserc.json5": "semantic-release", ".config/releaserc.yaml": "semantic-release", ".config/releaserc.yml": "semantic-release", ".config/releaserc.toml": "semantic-release", ".config/releaserc.js": "semantic-release", ".config/releaserc.mjs": "semantic-release", ".config/releaserc.cjs": "semantic-release", ".config/releaserc.ts": "semantic-release", ".config/releaserc.mts": "semantic-release", ".config/releaserc.cts": "semantic-release", "release.config.json": "semantic-release", "release.config.jsonc": "semantic-release", "release.config.json5": "semantic-release", "release.config.yaml": "semantic-release", "release.config.yml": "semantic-release", "release.config.toml": "semantic-release", "release.config.js": "semantic-release", "release.config.mjs": "semantic-release", "release.config.cjs": "semantic-release", "release.config.ts": "semantic-release", "release.config.mts": "semantic-release", "release.config.cts": "semantic-release", "bitbucket-pipelines.yaml": "bitbucket", "bitbucket-pipelines.yml": "bitbucket", ".bazelignore": "bazel", ".bazelrc": "bazel", ".bazelversion": "bazel", ".gdignore": "godot-assets", "._sc_": "godot-assets", "_sc_": "godot-assets", "azure-pipelines.yml": "azure-pipelines", "azure-pipelines.yaml": "azure-pipelines", "azure-pipelines-main.yml": "azure-pipelines", "azure-pipelines-main.yaml": "azure-pipelines", "vagrantfile": "vagrant", "prisma.yml": "prisma", ".nycrc": "istanbul", ".nycrc.json": "istanbul", ".nycrc.yaml": "istanbul", ".nycrc.yml": "istanbul", "nyc.config.js": "istanbul", ".istanbul.yml": "istanbul", "tailwind.js": "tailwindcss", "tailwind.ts": "tailwindcss", "tailwind.config.js": "tailwindcss", "tailwind.config.cjs": "tailwindcss", "tailwind.config.mjs": "tailwindcss", "tailwind.config.ts": "tailwindcss", "tailwind.config.cts": "tailwindcss", "tailwind.config.mts": "tailwindcss", "buildkite.yml": "buildkite", "buildkite.yaml": "buildkite", "netlify.json": "netlify", "netlify.yml": "netlify", "netlify.yaml": "netlify", "netlify.toml": "netlify", "svelte.config.js": "svelte", "svelte.config.cjs": "svelte", "nest-cli.json": "nest", ".nest-cli.json": "nest", "nestconfig.json": "nest", ".nestconfig.json": "nest", "moon.yml": "moon", ".percy.yml": "percy", ".gitpod.yml": "gitpod", "codeowners": "codeowners", "OWNERS": "codeowners", ".gcloudignore": "gcp", ".huskyrc": "husky", ".huskyrc.json": "husky", ".huskyrc.jsonc": "husky", ".huskyrc.json5": "husky", ".huskyrc.yaml": "husky", ".huskyrc.yml": "husky", ".huskyrc.toml": "husky", ".huskyrc.js": "husky", ".huskyrc.mjs": "husky", ".huskyrc.cjs": "husky", ".huskyrc.ts": "husky", ".huskyrc.mts": "husky", ".huskyrc.cts": "husky", ".config/huskyrc": "husky", ".config/huskyrc.json": "husky", ".config/huskyrc.jsonc": "husky", ".config/huskyrc.json5": "husky", ".config/huskyrc.yaml": "husky", ".config/huskyrc.yml": "husky", ".config/huskyrc.toml": "husky", ".config/huskyrc.js": "husky", ".config/huskyrc.mjs": "husky", ".config/huskyrc.cjs": "husky", ".config/huskyrc.ts": "husky", ".config/huskyrc.mts": "husky", ".config/huskyrc.cts": "husky", "husky.config.json": "husky", "husky.config.jsonc": "husky", "husky.config.json5": "husky", "husky.config.yaml": "husky", "husky.config.yml": "husky", "husky.config.toml": "husky", "husky.config.js": "husky", "husky.config.mjs": "husky", "husky.config.cjs": "husky", "husky.config.ts": "husky", "husky.config.mts": "husky", "husky.config.cts": "husky", "tiltfile": "tilt", "capacitor.config.json": "capacitor", "capacitor.config.ts": "capacitor", ".adonisrc.json": "adonis", "ace": "adonis", "meson.build": "meson", "meson_options.txt": "meson", ".commitlintrc": "commitlint", ".commitlintrc.json": "commitlint", ".commitlintrc.jsonc": "commitlint", ".commitlintrc.json5": "commitlint", ".commitlintrc.yaml": "commitlint", ".commitlintrc.yml": "commitlint", ".commitlintrc.toml": "commitlint", ".commitlintrc.js": "commitlint", ".commitlintrc.mjs": "commitlint", ".commitlintrc.cjs": "commitlint", ".commitlintrc.ts": "commitlint", ".commitlintrc.mts": "commitlint", ".commitlintrc.cts": "commitlint", ".config/commitlintrc": "commitlint", ".config/commitlintrc.json": "commitlint", ".config/commitlintrc.jsonc": "commitlint", ".config/commitlintrc.json5": "commitlint", ".config/commitlintrc.yaml": "commitlint", ".config/commitlintrc.yml": "commitlint", ".config/commitlintrc.toml": "commitlint", ".config/commitlintrc.js": "commitlint", ".config/commitlintrc.mjs": "commitlint", ".config/commitlintrc.cjs": "commitlint", ".config/commitlintrc.ts": "commitlint", ".config/commitlintrc.mts": "commitlint", ".config/commitlintrc.cts": "commitlint", "commitlint.config.json": "commitlint", "commitlint.config.jsonc": "commitlint", "commitlint.config.json5": "commitlint", "commitlint.config.yaml": "commitlint", "commitlint.config.yml": "commitlint", "commitlint.config.toml": "commitlint", "commitlint.config.js": "commitlint", "commitlint.config.mjs": "commitlint", "commitlint.config.cjs": "commitlint", "commitlint.config.ts": "commitlint", "commitlint.config.mts": "commitlint", "commitlint.config.cts": "commitlint", ".commitlint.yaml": "commitlint", ".commitlint.yml": "commitlint", ".buckconfig": "buck", "nx.json": "nx", ".nxignore": "nx", "dune": "dune", "dune-project": "dune", "dune-workspace": "dune", "dune-workspace.dev": "dune", "roadmap.md": "roadmap", "roadmap.txt": "roadmap", "timeline.md": "roadmap", "timeline.txt": "roadmap", "milestones.md": "roadmap", "milestones.txt": "roadmap", "nuget.config": "nuget", ".nuspec": "nuget", "nuget.exe": "nuget", "stryker.conf.json": "stryker", "stryker.conf.js": "stryker", "stryker.conf.cjs": "stryker", "stryker.conf.mjs": "stryker", ".stryker.conf.json": "stryker", ".stryker.conf.js": "stryker", ".stryker.conf.cjs": "stryker", ".stryker.conf.mjs": "stryker", ".modernizrrc": "modernizr", ".modernizrrc.js": "modernizr", ".modernizrrc.json": "modernizr", ".slugignore": "slug", "stitches.config.js": "stitches", "stitches.config.ts": "stitches", "nginx.conf": "nginx", ".mcattributes": "minecraft", ".mcdefinitions": "minecraft", ".mcignore": "minecraft", ".replit": "replit", "snowpack.config.js": "snowpack", "snowpack.config.cjs": "snowpack", "snowpack.config.mjs": "snowpack", "snowpack.config.ts": "snowpack", "snowpack.config.cts": "snowpack", "snowpack.config.mts": "snowpack", "snowpack.deps.json": "snowpack", "snowpack.config.json": "snowpack", "quasar.conf.js": "quasar", "quasar.config.js": "quasar", "dependabot.yml": "dependabot", "dependabot.yaml": "dependabot", "vite.config.js": "vite", "vite.config.mjs": "vite", "vite.config.cjs": "vite", "vite.config.ts": "vite", "vite.config.cts": "vite", "vite.config.mts": "vite", "vitest.config.ts": "vitest", "vitest.config.mts": "vitest", "vitest.config.cts": "vitest", "vitest.config.js": "vitest", "vitest.config.mjs": "vitest", "vitest.config.cjs": "vitest", "vitest.workspace.ts": "vitest", "vitest.workspace.mts": "vitest", "vitest.workspace.cts": "vitest", "vitest.workspace.js": "vitest", "vitest.workspace.mjs": "vitest", "vitest.workspace.cjs": "vitest", "lerna.json": "lerna", "windi.config.js": "windicss", "windi.config.cjs": "windicss", "windi.config.ts": "windicss", "windi.config.cts": "windicss", "windi.config.json": "windicss", ".textlintrc": "textlint", ".textlintrc.js": "textlint", ".textlintrc.json": "textlint", ".textlintrc.yml": "textlint", ".textlintrc.yaml": "textlint", "vpkg.json": "vlang", "v.mod": "vlang", ".sentryclirc": "sentry", ".phpunit.result.cache": "phpunit", ".phpunit-watcher.yml": "phpunit", "phpunit.xml": "phpunit", "phpunit.xml.dist": "phpunit", "phpunit-watcher.yml": "phpunit", "phpunit-watcher.yml.dist": "phpunit", ".php_cs": "php-cs-fixer", ".php_cs.dist": "php-cs-fixer", ".php_cs.php": "php-cs-fixer", ".php_cs.dist.php": "php-cs-fixer", ".php-cs-fixer.php": "php-cs-fixer", ".php-cs-fixer.dist.php": "php-cs-fixer", "robots.txt": "robots", "tsconfig.json": "tsconfig", "tsconfig.app.json": "tsconfig", "tsconfig.editor.json": "tsconfig", "tsconfig.spec.json": "tsconfig", "tsconfig.base.json": "tsconfig", "tsconfig.build.json": "tsconfig", "tsconfig.eslint.json": "tsconfig", "tsconfig.lib.json": "tsconfig", "tsconfig.lib.prod.json": "tsconfig", "tsconfig.node.json": "tsconfig", "tsconfig.test.json": "tsconfig", "tsconfig.e2e.json": "tsconfig", "tsconfig.web.json": "tsconfig", "tsconfig.webworker.json": "tsconfig", "tsconfig.worker.json": "tsconfig", "tsconfig.config.json": "tsconfig", "tsconfig.vitest.json": "tsconfig", "tsconfig.cjs.json": "tsconfig", "tsconfig.esm.json": "tsconfig", "tsconfig.mjs.json": "tsconfig", "tsconfig.doc.json": "tsconfig", "tsconfig.paths.json": "tsconfig", "tsconfig.main.json": "tsconfig", "tsconfig.renderer.json": "tsconfig", "tsconfig.server.json": "tsconfig", "tauri.conf.json": "tauri", "tauri.config.json": "tauri", "tauri.linux.conf.json": "tauri", "tauri.windows.conf.json": "tauri", "tauri.macos.conf.json": "tauri", ".taurignore": "tauri", "jsconfig.json": "jsconfig", "maven.config": "maven", "jvm.config": "maven", "pom.xml": "maven", "serverless.yml": "serverless", "serverless.yaml": "serverless", "serverless.json": "serverless", "serverless.js": "serverless", "serverless.ts": "serverless", "supabase.js": "supabase", "supabase.py": "supabase", ".ember-cli": "ember", ".ember-cli.js": "ember", "ember-cli-builds.js": "ember", "horusec-config.json": "<PERSON><PERSON><PERSON>", "poetry.lock": "poetry", "pdm.lock": "pdm", "pdm.toml": "pdm", ".pdm-python": "pdm", ".parcelrc": "parcel", ".astylerc": "astyle", ".lighthouserc.js": "lighthouse", "lighthouserc.js": "lighthouse", ".lighthouserc.cjs": "lighthouse", "lighthouserc.cjs": "lighthouse", ".lighthouserc.json": "lighthouse", "lighthouserc.json": "lighthouse", ".lighthouserc.yml": "lighthouse", "lighthouserc.yml": "lighthouse", ".lighthouserc.yaml": "lighthouse", "lighthouserc.yaml": "lighthouse", ".svgrrc": "svgr", ".svgrrc.json": "svgr", ".svgrrc.jsonc": "svgr", ".svgrrc.json5": "svgr", ".svgrrc.yaml": "svgr", ".svgrrc.yml": "svgr", ".svgrrc.toml": "svgr", ".svgrrc.js": "svgr", ".svgrrc.mjs": "svgr", ".svgrrc.cjs": "svgr", ".svgrrc.ts": "svgr", ".svgrrc.mts": "svgr", ".svgrrc.cts": "svgr", ".config/svgrrc": "svgr", ".config/svgrrc.json": "svgr", ".config/svgrrc.jsonc": "svgr", ".config/svgrrc.json5": "svgr", ".config/svgrrc.yaml": "svgr", ".config/svgrrc.yml": "svgr", ".config/svgrrc.toml": "svgr", ".config/svgrrc.js": "svgr", ".config/svgrrc.mjs": "svgr", ".config/svgrrc.cjs": "svgr", ".config/svgrrc.ts": "svgr", ".config/svgrrc.mts": "svgr", ".config/svgrrc.cts": "svgr", "svgr.config.json": "svgr", "svgr.config.jsonc": "svgr", "svgr.config.json5": "svgr", "svgr.config.yaml": "svgr", "svgr.config.yml": "svgr", "svgr.config.toml": "svgr", "svgr.config.js": "svgr", "svgr.config.mjs": "svgr", "svgr.config.cjs": "svgr", "svgr.config.ts": "svgr", "svgr.config.mts": "svgr", "svgr.config.cts": "svgr", "rome.json": "rome", "cypress.json": "cypress", "cypress.env.json": "cypress", "cypress.config.ts": "cypress", "cypress.config.js": "cypress", "cypress.config.cjs": "cypress", "cypress.config.mjs": "cypress", "plopfile.js": "plop", "plopfile.cjs": "plop", "plopfile.mjs": "plop", "plopfile.ts": "plop", ".tobimake": "tobimake", "gleam.toml": "gleam", "pnpm-lock.yaml": "pnpm", "pnpm-workspace.yaml": "pnpm", ".pnpmfile.cjs": "pnpm", "gridsome.config.js": "gridsome", "gridsome.server.js": "gridsome", ".steadybit.yml": "steadybit", "steadybit.yml": "steadybit", ".steadybit.yaml": "steadybit", "steadybit.yaml": "steadybit", "Caddyfile": "caddy", "openapi.json": "openapi", "openapi.yml": "openapi", "openapi.yaml": "openapi", "swagger.json": "swagger", "swagger.yml": "swagger", "swagger.yaml": "swagger", "bun.lockb": "bun", "bunfig.toml": "bun", ".nano-staged.js": "nano-staged", "nano-staged.js": "nano-staged", ".nano-staged.cjs": "nano-staged", "nano-staged.cjs": "nano-staged", ".nano-staged.mjs": "nano-staged", "nano-staged.mjs": "nano-staged", ".nano-staged.json": "nano-staged", "nano-staged.json": "nano-staged", ".nanostagedrc": "nano-staged", "knip.json": "knip", "knip.jsonc": "knip", ".knip.json": "knip", ".knip.jsonc": "knip", "knip.ts": "knip", "knip.js": "knip", "knip.config.ts": "knip", "knip.config.js": "knip", "taskfile.yml": "taskfile", "taskfile.yaml": "taskfile", "taskfile.dist.yml": "taskfile", "taskfile.dist.yaml": "taskfile", ".cracorc": "craco", ".cracorc.json": "craco", ".cracorc.jsonc": "craco", ".cracorc.json5": "craco", ".cracorc.yaml": "craco", ".cracorc.yml": "craco", ".cracorc.toml": "craco", ".cracorc.js": "craco", ".cracorc.mjs": "craco", ".cracorc.cjs": "craco", ".cracorc.ts": "craco", ".cracorc.mts": "craco", ".cracorc.cts": "craco", ".config/cracorc": "craco", ".config/cracorc.json": "craco", ".config/cracorc.jsonc": "craco", ".config/cracorc.json5": "craco", ".config/cracorc.yaml": "craco", ".config/cracorc.yml": "craco", ".config/cracorc.toml": "craco", ".config/cracorc.js": "craco", ".config/cracorc.mjs": "craco", ".config/cracorc.cjs": "craco", ".config/cracorc.ts": "craco", ".config/cracorc.mts": "craco", ".config/cracorc.cts": "craco", "craco.config.json": "craco", "craco.config.jsonc": "craco", "craco.config.json5": "craco", "craco.config.yaml": "craco", "craco.config.yml": "craco", "craco.config.toml": "craco", "craco.config.js": "craco", "craco.config.mjs": "craco", "craco.config.cjs": "craco", "craco.config.ts": "craco", "craco.config.mts": "craco", "craco.config.cts": "craco", ".hg": "mercurial", ".hgignore": "mercurial", ".hgflow": "mercurial", ".hgrc": "mercurial", "hgrc": "mercurial", "mercurial.ini": "mercurial", "deno.json": "deno", "deno.jsonc": "deno", "deno.lock": "deno", "plastic.branchexplorer": "plastic", "plastic.selector": "plastic", "plastic.wktree": "plastic", "plastic.workspace": "plastic", "plastic.workspaces": "plastic", "uno.config.js": "unocss", "uno.config.mjs": "unocss", "uno.config.ts": "unocss", "uno.config.mts": "unocss", "unocss.config.js": "unocss", "unocss.config.mjs": "unocss", "unocss.config.ts": "unocss", "unocss.config.mts": "unocss", ".mincloudrc": "ifanr-cloud", "concourse.yml": "concourse", ".syncpackrc": "syncpack", ".syncpackrc.json": "syncpack", ".syncpackrc.jsonc": "syncpack", ".syncpackrc.json5": "syncpack", ".syncpackrc.yaml": "syncpack", ".syncpackrc.yml": "syncpack", ".syncpackrc.toml": "syncpack", ".syncpackrc.js": "syncpack", ".syncpackrc.mjs": "syncpack", ".syncpackrc.cjs": "syncpack", ".syncpackrc.ts": "syncpack", ".syncpackrc.mts": "syncpack", ".syncpackrc.cts": "syncpack", ".config/syncpackrc": "syncpack", ".config/syncpackrc.json": "syncpack", ".config/syncpackrc.jsonc": "syncpack", ".config/syncpackrc.json5": "syncpack", ".config/syncpackrc.yaml": "syncpack", ".config/syncpackrc.yml": "syncpack", ".config/syncpackrc.toml": "syncpack", ".config/syncpackrc.js": "syncpack", ".config/syncpackrc.mjs": "syncpack", ".config/syncpackrc.cjs": "syncpack", ".config/syncpackrc.ts": "syncpack", ".config/syncpackrc.mts": "syncpack", ".config/syncpackrc.cts": "syncpack", "syncpack.config.json": "syncpack", "syncpack.config.jsonc": "syncpack", "syncpack.config.json5": "syncpack", "syncpack.config.yaml": "syncpack", "syncpack.config.yml": "syncpack", "syncpack.config.toml": "syncpack", "syncpack.config.js": "syncpack", "syncpack.config.mjs": "syncpack", "syncpack.config.cjs": "syncpack", "syncpack.config.ts": "syncpack", "syncpack.config.mts": "syncpack", "syncpack.config.cts": "syncpack", "werf.yaml": "werf", "werf.yml": "werf", "werf-giterminism.yaml": "werf", "werf-giterminism.yml": "werf", "panda.config.ts": "panda", "panda.config.js": "panda", "panda.config.mjs": "panda", "panda.config.mts": "panda", "panda.config.cjs": "panda", "biome.json": "biome", "esbuild.js": "esbuild", "esbuild.ts": "esbuild", "esbuild.cjs": "esbuild", "esbuild.mjs": "esbuild", "esbuild.config.js": "esbuild", "esbuild.config.ts": "esbuild", "esbuild.config.cjs": "esbuild", "esbuild.config.mjs": "esbuild", ".puppeteerrc": "puppeteer", ".puppeteerrc.json": "puppeteer", ".puppeteerrc.jsonc": "puppeteer", ".puppeteerrc.json5": "puppeteer", ".puppeteerrc.yaml": "puppeteer", ".puppeteerrc.yml": "puppeteer", ".puppeteerrc.toml": "puppeteer", ".puppeteerrc.js": "puppeteer", ".puppeteerrc.mjs": "puppeteer", ".puppeteerrc.cjs": "puppeteer", ".puppeteerrc.ts": "puppeteer", ".puppeteerrc.mts": "puppeteer", ".puppeteerrc.cts": "puppeteer", ".config/puppeteerrc": "puppeteer", ".config/puppeteerrc.json": "puppeteer", ".config/puppeteerrc.jsonc": "puppeteer", ".config/puppeteerrc.json5": "puppeteer", ".config/puppeteerrc.yaml": "puppeteer", ".config/puppeteerrc.yml": "puppeteer", ".config/puppeteerrc.toml": "puppeteer", ".config/puppeteerrc.js": "puppeteer", ".config/puppeteerrc.mjs": "puppeteer", ".config/puppeteerrc.cjs": "puppeteer", ".config/puppeteerrc.ts": "puppeteer", ".config/puppeteerrc.mts": "puppeteer", ".config/puppeteerrc.cts": "puppeteer", "puppeteer.config.json": "puppeteer", "puppeteer.config.jsonc": "puppeteer", "puppeteer.config.json5": "puppeteer", "puppeteer.config.yaml": "puppeteer", "puppeteer.config.yml": "puppeteer", "puppeteer.config.toml": "puppeteer", "puppeteer.config.js": "puppeteer", "puppeteer.config.mjs": "puppeteer", "puppeteer.config.cjs": "puppeteer", "puppeteer.config.ts": "puppeteer", "puppeteer.config.mts": "puppeteer", "puppeteer.config.cts": "puppeteer", "PklProject": "pkl", "PklProject.deps.json": "pkl", "k8s.yml": "kubernetes", "k8s.yaml": "kubernetes", "kubernetes.yml": "kubernetes", "kubernetes.yaml": "kubernetes", ".k8s.yml": "kubernetes", ".k8s.yaml": "kubernetes"}, "languageIds": {"git": "git", "git-commit": "git", "git-rebase": "git", "ignore": "git", "yaml": "yaml", "github-actions-workflow": "yaml", "spring-boot-properties-yaml": "yaml", "ansible": "yaml", "ansible-jinja": "yaml", "xml": "xml", "xquery": "xml", "xsl": "xml", "matlab": "matlab", "makefile": "settings", "toml": "settings", "ini": "settings", "properties": "settings", "spring-boot-properties": "settings", "shaderlab": "shaderlab", "diff": "diff", "json": "json", "jsonc": "json", "json5": "json", "blink": "blink", "java": "java", "razor": "razor", "aspnetcorerazor": "razor", "python": "python", "mojo": "mojo", "javascript": "javascript", "typescript": "typescript", "scala": "scala", "handlebars": "handlebars", "perl": "perl", "perl6": "perl", "haxe": "haxe", "hxml": "haxe", "puppet": "puppet", "elixir": "elixir", "livescript": "livescript", "erlang": "erlang", "twig": "twig", "julia": "julia", "elm": "elm", "purescript": "purescript", "stylus": "stylus", "nunjucks": "nunjucks", "pug": "pug", "robotframework": "robot", "sass": "sass", "scss": "sass", "less": "less", "css": "css", "testOutput": "visualstudio", "vb": "visualstudio", "ng-template": "angular", "graphql": "graphql", "solidity": "solidity", "autoit": "autoit", "haml": "haml", "yang": "yang", "terraform": "terraform", "applescript": "applescript", "cake": "cake", "cucumber": "cucumber", "nim": "nim", "nimble": "nim", "apiblueprint": "apiblueprint", "riot": "riot", "postcss": "postcss", "lang-cfml": "coldfusion", "haskell": "haskell", "dhall": "dhall", "cabal": "cabal", "nix": "nix", "ruby": "ruby", "slim": "slim", "php": "php", "hack": "hack", "javascriptreact": "react", "mjml": "mjml", "processing": "processing", "hcl": "hcl", "go": "go", "django-html": "django", "django-txt": "django", "html": "html", "gdscript": "godot", "gdresource": "godot-assets", "gdshader": "godot-assets", "viml": "vim", "prolog": "prolog", "pawn": "pawn", "reason": "reason", "reason_lisp": "reason", "sml": "sml", "tex": "tex", "doctex": "tex", "latex": "tex", "latex-expl3": "tex", "apex": "salesforce", "sas": "sas", "dockerfile": "docker", "dockercompose": "docker", "csv": "table", "tsv": "table", "psv": "table", "csharp": "csharp", "bat": "console", "awk": "console", "shellscript": "console", "c": "c", "cpp": "cpp", "objective-c": "objective-c", "objective-cpp": "objective-cpp", "coffeescript": "coffee", "fsharp": "fsharp", "editorconfig": "editorconfig", "clojure": "clojure", "groovy": "groovy", "markdown": "markdown", "jinja": "jinja", "proto": "proto", "pip-requirements": "python-misc", "vue": "vue", "vue-postcss": "vue", "vue-html": "vue", "lua": "lua", "bibtex": "lib", "bibtex-style": "lib", "log": "log", "jupyter": "jup<PERSON><PERSON>", "plaintext": "document", "pdf": "pdf", "powershell": "powershell", "jade": "pug", "r": "r", "rsweave": "r", "rust": "rust", "sql": "database", "kql": "kusto", "ssh_config": "lock", "svg": "svg", "swift": "swift", "typescriptreact": "react_ts", "search-result": "search", "mcfunction": "minecraft", "rescript": "rescript", "otne": "otne", "twee3": "twine", "twee3-harlowe-3": "twine", "twee3-chapbook-1": "twine", "twee3-sugarcube-2": "twine", "grain": "grain", "lolcode": "lolcode", "idris": "idris", "pgn": "chess", "gemini": "gemini", "text-gemini": "gemini", "v": "vlang", "wolfram": "wolframlanguage", "hlsl": "shader", "glsl": "shader", "wgsl": "shader", "tree": "tree", "svelte": "svelte", "dart": "dart", "cadence": "cadence", "stylable": "stylable", "hjson": "h<PERSON><PERSON>", "huff": "huff", "concourse-pipeline-yaml": "concourse", "concourse-task-yaml": "concourse"}, "light": {"fileExtensions": {"blink": "blink_light", "jinja": "jinja_light", "jinja2": "jinja_light", "j2": "jinja_light", "jinja-html": "jinja_light", "huff": "huff_light", "cr": "crystal_light", "ecr": "crystal_light", "drone.yml": "drone_light", ".wakatime-project": "wakatime_light", "hcl": "hcl_light", "iuml": "uml_light", "pu": "uml_light", "puml": "uml_light", "plantuml": "uml_light", "wsd": "uml_light", "pgn": "chess_light", "fen": "chess_light", "openapi.json": "openapi_light", "openapi.yml": "openapi_light", "openapi.yaml": "openapi_light", "tldr": "tldraw_light"}, "fileNames": {".rubocop.yml": "rubocop_light", ".rubocop-todo.yml": "rubocop_light", ".rubocop_todo.yml": "rubocop_light", "vercel.json": "vercel_light", ".vercelignore": "vercel_light", "now.json": "vercel_light", ".nowignore": "vercel_light", "payload.config.js": "payload_light", "payload.config.mjs": "payload_light", "payload.config.ts": "payload_light", "payload.config.mts": "payload_light", "next.config.js": "next_light", "next.config.mjs": "next_light", "next.config.ts": "next_light", "next.config.mts": "next_light", "remix.config.js": "remix_light", "remix.config.ts": "remix_light", "turbo.json": "turborepo_light", ".autorc": "auto_light", "auto.config.js": "auto_light", "auto.config.ts": "auto_light", "auto-config.json": "auto_light", "auto-config.yaml": "auto_light", "auto-config.yml": "auto_light", "auto-config.ts": "auto_light", "auto-config.js": "auto_light", ".stylelintrc": "stylelint_light", ".stylelintrc.json": "stylelint_light", ".stylelintrc.jsonc": "stylelint_light", ".stylelintrc.json5": "stylelint_light", ".stylelintrc.yaml": "stylelint_light", ".stylelintrc.yml": "stylelint_light", ".stylelintrc.toml": "stylelint_light", ".stylelintrc.js": "stylelint_light", ".stylelintrc.mjs": "stylelint_light", ".stylelintrc.cjs": "stylelint_light", ".stylelintrc.ts": "stylelint_light", ".stylelintrc.mts": "stylelint_light", ".stylelintrc.cts": "stylelint_light", ".config/stylelintrc": "stylelint_light", ".config/stylelintrc.json": "stylelint_light", ".config/stylelintrc.jsonc": "stylelint_light", ".config/stylelintrc.json5": "stylelint_light", ".config/stylelintrc.yaml": "stylelint_light", ".config/stylelintrc.yml": "stylelint_light", ".config/stylelintrc.toml": "stylelint_light", ".config/stylelintrc.js": "stylelint_light", ".config/stylelintrc.mjs": "stylelint_light", ".config/stylelintrc.cjs": "stylelint_light", ".config/stylelintrc.ts": "stylelint_light", ".config/stylelintrc.mts": "stylelint_light", ".config/stylelintrc.cts": "stylelint_light", "stylelint.config.json": "stylelint_light", "stylelint.config.jsonc": "stylelint_light", "stylelint.config.json5": "stylelint_light", "stylelint.config.yaml": "stylelint_light", "stylelint.config.yml": "stylelint_light", "stylelint.config.toml": "stylelint_light", "stylelint.config.js": "stylelint_light", "stylelint.config.mjs": "stylelint_light", "stylelint.config.cjs": "stylelint_light", "stylelint.config.ts": "stylelint_light", "stylelint.config.mts": "stylelint_light", "stylelint.config.cts": "stylelint_light", ".stylelintignore": "stylelint_light", ".stylelintcache": "stylelint_light", ".codeclimate.yml": "code-climate_light", "browserslist": "browserlist_light", ".browserslistrc": "browserlist_light", ".drone.yml": "drone_light", ".wakatime-project": "wakatime_light", "circle.yml": "circleci_light", ".releaserc": "semantic-release_light", ".releaserc.json": "semantic-release_light", ".releaserc.jsonc": "semantic-release_light", ".releaserc.json5": "semantic-release_light", ".releaserc.yaml": "semantic-release_light", ".releaserc.yml": "semantic-release_light", ".releaserc.toml": "semantic-release_light", ".releaserc.js": "semantic-release_light", ".releaserc.mjs": "semantic-release_light", ".releaserc.cjs": "semantic-release_light", ".releaserc.ts": "semantic-release_light", ".releaserc.mts": "semantic-release_light", ".releaserc.cts": "semantic-release_light", ".config/releaserc": "semantic-release_light", ".config/releaserc.json": "semantic-release_light", ".config/releaserc.jsonc": "semantic-release_light", ".config/releaserc.json5": "semantic-release_light", ".config/releaserc.yaml": "semantic-release_light", ".config/releaserc.yml": "semantic-release_light", ".config/releaserc.toml": "semantic-release_light", ".config/releaserc.js": "semantic-release_light", ".config/releaserc.mjs": "semantic-release_light", ".config/releaserc.cjs": "semantic-release_light", ".config/releaserc.ts": "semantic-release_light", ".config/releaserc.mts": "semantic-release_light", ".config/releaserc.cts": "semantic-release_light", "release.config.json": "semantic-release_light", "release.config.jsonc": "semantic-release_light", "release.config.json5": "semantic-release_light", "release.config.yaml": "semantic-release_light", "release.config.yml": "semantic-release_light", "release.config.toml": "semantic-release_light", "release.config.js": "semantic-release_light", "release.config.mjs": "semantic-release_light", "release.config.cjs": "semantic-release_light", "release.config.ts": "semantic-release_light", "release.config.mts": "semantic-release_light", "release.config.cts": "semantic-release_light", "netlify.json": "netlify_light", "netlify.yml": "netlify_light", "netlify.yaml": "netlify_light", "netlify.toml": "netlify_light", "stitches.config.js": "stitches_light", "stitches.config.ts": "stitches_light", "snowpack.config.js": "snowpack_light", "snowpack.config.cjs": "snowpack_light", "snowpack.config.mjs": "snowpack_light", "snowpack.config.ts": "snowpack_light", "snowpack.config.cts": "snowpack_light", "snowpack.config.mts": "snowpack_light", "snowpack.deps.json": "snowpack_light", "snowpack.config.json": "snowpack_light", "pnpm-lock.yaml": "pnpm_light", "pnpm-workspace.yaml": "pnpm_light", ".pnpmfile.cjs": "pnpm_light", "openapi.json": "openapi_light", "openapi.yml": "openapi_light", "openapi.yaml": "openapi_light", "bun.lockb": "bun_light", "bunfig.toml": "bun_light", ".nano-staged.js": "nano-staged_light", "nano-staged.js": "nano-staged_light", ".nano-staged.cjs": "nano-staged_light", "nano-staged.cjs": "nano-staged_light", ".nano-staged.mjs": "nano-staged_light", "nano-staged.mjs": "nano-staged_light", ".nano-staged.json": "nano-staged_light", "nano-staged.json": "nano-staged_light", ".nanostagedrc": "nano-staged_light", "deno.json": "deno_light", "deno.jsonc": "deno_light", "deno.lock": "deno_light"}, "folderNames": {"jinja": "folder-jinja_light", ".jinja": "folder-jinja_light", "_jinja": "folder-jinja_light", "__jinja__": "folder-jinja_light", "jinja2": "folder-jinja_light", ".jinja2": "folder-jinja_light", "_jinja2": "folder-jinja_light", "__jinja2__": "folder-jinja_light", "j2": "folder-jinja_light", ".j2": "folder-jinja_light", "_j2": "folder-jinja_light", "__j2__": "folder-jinja_light", "idea": "folder-intellij_light", ".idea": "folder-intellij_light", "_idea": "folder-intellij_light", "__idea__": "folder-intellij_light"}, "folderNamesExpanded": {"jinja": "folder-jinja-open_light", ".jinja": "folder-jinja-open_light", "_jinja": "folder-jinja-open_light", "__jinja__": "folder-jinja-open_light", "jinja2": "folder-jinja-open_light", ".jinja2": "folder-jinja-open_light", "_jinja2": "folder-jinja-open_light", "__jinja2__": "folder-jinja-open_light", "j2": "folder-jinja-open_light", ".j2": "folder-jinja-open_light", "_j2": "folder-jinja-open_light", "__j2__": "folder-jinja-open_light", "idea": "folder-intellij-open_light", ".idea": "folder-intellij-open_light", "_idea": "folder-intellij-open_light", "__idea__": "folder-intellij-open_light"}}, "highContrast": {"fileExtensions": {}, "fileNames": {}}, "options": {"folders": {"theme": "specific", "color": "#90a4ae", "associations": {}}, "activeIconPack": "angular", "hidesExplorerArrows": false, "opacity": 1, "saturation": 1, "files": {"color": "#90a4ae", "associations": {}}, "languages": {"associations": {}}}, "file": "file", "hidesExplorerArrows": false, "folder": "folder", "folderExpanded": "folder-open", "rootFolder": "folder-root", "rootFolderExpanded": "folder-root-open"}